---
description:
globs:
alwaysApply: true
---
# 项目背景

本项目是一个 react 管理后台项目，技术栈使用的是@umijs/max@4.0.33

# 编码标准

- 使用函数式组件和 Hooks
- 使用 tailwindcss 进行样式编写
- 定义 typeScript 类型使用 type 而不是 interface
- 处理 tailwind 条件样式的时候使用 @/lib/utils 的 cn 函数
- 使用 pnpm 作为包管理器安装依赖
- 帮我修改代码的时候不要删除我的 log
- 创建新函数的时候使用 const 形式创建，而不是使用 function
- 使用第三块库的时候无需提示我要安装，直接编写相关代码
- 使用 ProTable 获取接口数据使用形式参考 request={(params, sorter) => {
  return proTableRequestAdapterParamsAndData(params, sorter, ConversationDetailData);
  }}只需要 params 的接口使用proTableRequestAdapter(params, sorter, filter, ConversationDetailData);
- Agent 模式下直接帮我修改代码文件无需询问我
- 图表相关的内容使用 ReactECharts
- 使用useRequest解构出的 data 无需再 .data 直接使用

# 首选的库

- 使用 antd@5.21.0
- 使用 @ant-design/pro-components@2.8.0
- 使用 tailwindcss@3
- 使用 ahooks@3.8.1
- 使用 @umijs/max@4.0.33
- 使用 echarts-for-react@3.0.2
