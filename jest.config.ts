import { Config, configUmiAlias, createConfig } from '@umijs/max/test';

export default async () => {
  try {
    return (await configUmiAlias({
      ...createConfig({
        target: 'browser',
        jsTransformer: 'esbuild',
        jsTransformerOpts: { jsx: 'automatic' },
      }),

      collectCoverageFrom: ['src/utils/**/*.{ts,js}', '!src/utils/**/*.d.ts'],
      moduleNameMapper: {
        '\\.(jpg|jpeg|png|gif|webp|svg)$': '<rootDir>/__mocks__/fileMock.js',
        '\\.(css|less|scss|sass)$': '<rootDir>/__mocks__/styleMock.js',
        '^darkreader$': '<rootDir>/__mocks__/darkreader.js',
      },
      // if you require some es-module npm package, please uncomment below line and insert your package name
      // transformIgnorePatterns: ['node_modules/(?!.*(lodash-es|your-es-pkg-name)/)']
    })) as Config.InitialOptions;
  } catch (e) {
    console.log(e);
    throw e;
  }
};
