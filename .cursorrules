# 项目背景
本项目是一个 react 管理后台项目，技术栈使用的是@umijs/max@4.0.33

# 编码标准
- 使用函数式组件和 Hooks
- 使用 tailwindcss 进行样式编写
- 定义 typeScript 类型使用 type 而不是 interface
- 处理 tailwind 条件样式的时候使用 @/lib/utils 的 cn 函数
- 使用 ProTable 的时候添加上这三个配置 search={{ ...proTableSearchConfig }} options={{ ...proTableOptionsConfig }} pagination={{ ...proTablePaginationConfig }} 与 tableClassName="custom-table"
- 使用 useRequest 的时候 import { useRequest } from '@umijs/max';\
- 生成代码后不需要主动处理 lint 相关错误

# 首选的库
- 使用 antd@5.21.0
- 使用 @ant-design/pro-components@2.8.0
- 使用 tailwindcss@3
- 使用 ahooks@3.8.1
- 使用 @umijs/max@4.0.33