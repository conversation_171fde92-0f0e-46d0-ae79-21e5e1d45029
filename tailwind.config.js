module.exports = {
  content: ['./src/pages/**/*.tsx', './src/components/**/*.tsx', './src/layouts/**/*.tsx'],
  darkMode: ['selector', 'class'],
  corePlugins: {
    preflight: false, // https://github.com/umijs/umi/issues/9565
  },
  theme: {
    extend: {
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
      },
      colors: {
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))',
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))',
        },
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))',
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))',
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))',
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))',
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))',
        },
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        chart: {
          1: 'hsl(var(--chart-1))',
          2: 'hsl(var(--chart-2))',
          3: 'hsl(var(--chart-3))',
          4: 'hsl(var(--chart-4))',
          5: 'hsl(var(--chart-5))',
        },
        'new-media-gray': {
          50: 'var(--new-media-gray-50)',
          100: 'var(--new-media-gray-100)',
          200: 'var(--new-media-gray-200)',
          300: 'var(--new-media-gray-300)',
          400: 'var(--new-media-gray-400)',
          500: 'var(--new-media-gray-500)',
          600: 'var(--new-media-gray-600)',
          700: 'var(--new-media-gray-700)',
          800: 'var(--new-media-gray-800)',
        },
        'new-media-blue': {
          50: 'var(--new-media-blue-50)',
          100: 'var(--new-media-blue-100)',
          200: 'var(--new-media-blue-200)',
          300: 'var(--new-media-blue-300)',
          400: 'var(--new-media-blue-400)',
          500: 'var(--new-media-blue-500)',
          600: 'var(--new-media-blue-600)',
          700: 'var(--new-media-blue-700)',
          800: 'var(--new-media-blue-800)',
          900: 'var(--new-media-blue-900)',
          950: 'var(--new-media-blue-950)',
        },
      },
      keyframes: {
        'accordion-down': {
          from: {
            height: '0',
          },
          to: {
            height: 'var(--radix-accordion-content-height)',
          },
        },
        'accordion-up': {
          from: {
            height: 'var(--radix-accordion-content-height)',
          },
          to: {
            height: '0',
          },
        },
      },
      animation: {
        'accordion-down': 'accordion-down 0.2s ease-out',
        'accordion-up': 'accordion-up 0.2s ease-out',
      },
    },
    fontFamily: {
      pingfang: ['PingFang SC', 'sans-serif'],
    },
  },
  experimental: { optimizeUniversalDefaults: true },
  plugins: [require('tailwindcss-animate')],
};
