import { GetAllProject } from '@/services/project';
import { useRequest } from '@umijs/max';
import { Card, Typography } from 'antd';
import { IndustryType } from '@/utils/const';

const { Title, Text } = Typography;

type ProjectItem = {
  id: string;
  name: string;
  description?: string;
  iconUrl: string;
  projectKey: string;
  industryType: IndustryType;
};

// 按行业分组项目
const groupProjectsByIndustry = (projects: ProjectItem[]) => {
  const grouped = projects.reduce(
    (acc, project) => {
      const industry = project.industryType === IndustryType.CAR ? '汽车行业' : '电商行业';
      if (!acc[industry]) {
        acc[industry] = [];
      }
      acc[industry].push(project);
      return acc;
    },
    {} as Record<string, ProjectItem[]>,
  );

  return grouped;
};

const ProjectList: React.FC = () => {
  const { data: projectList } = useRequest(() => GetAllProject(), {
    cacheKey: 'GetAllProject',
    cacheTime: 10 * 60 * 1000, // 10 分钟
    refreshDeps: [],
    onSuccess: (projectData) => {
      if (projectData) {
        localStorage.setItem('projectList', JSON.stringify(projectData));
      }
    },
  });

  if (!projectList) {
    return null;
  }

  const groupedProjects = groupProjectsByIndustry(projectList);

  return (
    <div className="min-h-screen">
      {Object.entries(groupedProjects).map(([industry, projects]) => (
        <div key={industry} className="mb-2">
          <Title level={4} className="mb-4 text-gray-700">
            {industry}
          </Title>
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5">
            {projects.map((item) => (
              <Card
                key={item.id}
                hoverable={true}
                className="cursor-pointer transition-all duration-200 hover:shadow-lg"
                onClick={() =>
                  window.open(
                    `/project/${item.projectKey}/${item.industryType}/overview/data-dashboard`,
                  )
                }
                styles={{ body: { padding: '16px' } }}
              >
                <div className="flex items-center space-x-3">
                  <div className="flex-shrink-0">
                    <img
                      src={item.iconUrl}
                      alt={item.name}
                      className="h-10 w-10 rounded-lg object-cover"
                    />
                  </div>
                  <div className="min-w-0 flex-1">
                    <Title level={5} className="mb-1 truncate text-gray-900">
                      {item.name}
                    </Title>
                    <Text type="secondary" className="block truncate text-sm">
                      {item.description || '-'}
                    </Text>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>
      ))}
    </div>
  );
};

export default ProjectList;
