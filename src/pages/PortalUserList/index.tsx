import {
  <PERSON>reatePortalUser,
  DeletePortalUser,
  QueryPortalUsersForPagination,
  UpdatePortalUser,
} from '@/services/auth';
import { GetRoleSelect } from '@/services/system';
import { PortalUser } from '@/services/typings';
import { proTableRequestAdapter } from '@/utils';
import {
  ActionType,
  ModalForm,
  PageContainer,
  ProColumns,
  ProFormInstance,
  ProFormSelect,
  ProFormText,
  ProTable,
} from '@ant-design/pro-components';
import { Button, message, Popconfirm, Space } from 'antd';
import { useRef } from 'react';

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 18 },
};

const PortalUserList: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const formRef = useRef<ProFormInstance>();

  const columns: Array<ProColumns<PortalUser>> = [
    {
      title: 'ID',
      dataIndex: 'id',
      hideInSearch: true,
      hideInTable: true,
    },
    {
      title: '姓名',
      dataIndex: 'name',
      hideInSearch: false,
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      hideInSearch: true,
      width: '250px',
    },
    {
      title: '系统角色',
      dataIndex: 'roleCodes',
      valueType: 'select',
      fieldProps: {
        mode: 'multiple',
      },
      request: async () => {
        const res = await GetRoleSelect();
        const transfer = res.data.map((item: any) => ({
          label: item.name,
          value: item.value,
        }));
        return transfer;
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      hideInSearch: true,
      hideInTable: false,
      render: (text, record, _, action) => {
        return (
          <Space>
            <Popconfirm
              title={'是否确认删除'}
              onConfirm={async () => {
                const res = await DeletePortalUser([record.id]);
                if (res.code == 0) {
                  message.success('删除成功');
                } else {
                  message.error('删除失败');
                }
                await action?.reload();
              }}
              key={'delete'}
            >
              <a>删除</a>
            </Popconfirm>
            <ModalForm
              trigger={<a>编辑用户</a>}
              layout={'horizontal'}
              title="编辑用户"
              width="500px"
              modalProps={{
                destroyOnHidden: true,
              }}
              {...formItemLayout}
              initialValues={record}
              onFinish={async (values) => {
                const res = await UpdatePortalUser(values);
                if (res.code == 0) {
                  message.success('修改成功');
                } else {
                  return false;
                }
                await action?.reload();
                return true;
              }}
            >
              <ProFormText width="md" name="id" hidden={true} />
              <ProFormText
                width="md"
                name="name"
                label="用户姓名"
                placeholder="请输入"
                rules={[{ required: true, message: '请输入用户姓名' }]}
              />
              {record.email && (
                <ProFormText
                  width="md"
                  name="email"
                  label="用户邮箱"
                  placeholder="请输入"
                  rules={[{ required: true, message: '请输入用户邮箱' }]}
                />
              )}
              <ProFormSelect
                name="roleCodes"
                label="系统角色"
                request={async () => {
                  const res = await GetRoleSelect();
                  return res.data;
                }}
                fieldProps={{
                  fieldNames: { label: 'name', value: 'value' },
                  mode: 'multiple',
                }}
                placeholder="请选择"
                rules={[{ required: true, message: '请选择系统角色' }]}
              />
            </ModalForm>
          </Space>
        );
      },
    },
  ];

  return (
    <PageContainer
      header={{
        style: { background: '#ffffff', margin: '10px 40px', borderRadius: '6px' },
      }}
      extra={[
        <ModalForm
          trigger={<Button type="primary">新建用户</Button>}
          layout={'horizontal'}
          title="新建用户"
          width="500px"
          modalProps={{
            destroyOnHidden: true,
          }}
          key="createUser"
          {...formItemLayout}
          onFinish={async (values) => {
            const newValue = {
              type: 0, // 0邮箱自建，1飞书扫码
              ...values,
            };
            const res = await CreatePortalUser(newValue);
            if (res.code === 0) {
              message.success('修改成功');
              await actionRef.current?.reload();
              return true;
            }
          }}
        >
          <ProFormText
            width="md"
            name="name"
            label="用户姓名"
            placeholder="请输入"
            rules={[{ required: true, message: '请输入用户姓名' }]}
          />
          <ProFormText
            width="md"
            name="email"
            label="用户邮箱"
            placeholder="请输入"
            rules={[{ required: true, message: '请输入用户邮箱' }]}
          />
          <ProFormSelect
            name="roleCodes"
            label="系统角色"
            request={async () => {
              const res = await GetRoleSelect();
              return res.data;
            }}
            fieldProps={{
              fieldNames: { label: 'name', value: 'value' },
              mode: 'multiple',
            }}
            placeholder="请选择"
            rules={[{ required: true, message: '请选择系统角色' }]}
          />
        </ModalForm>,
      ]}
    >
      <ProTable<PortalUser>
        formRef={formRef}
        actionRef={actionRef}
        scroll={{ x: 'max-content' }}
        columns={columns}
        rowKey="id"
        toolBarRender={false}
        bordered
        size="small"
        dateFormatter="string"
        pagination={{ defaultPageSize: 15 }}
        search={{
          filterType: 'query',
          defaultCollapsed: false,
        }}
        request={async (params, sorter, filter) => {
          return proTableRequestAdapter(params, sorter, filter, QueryPortalUsersForPagination);
        }}
      />
    </PageContainer>
  );
};

export default PortalUserList;
