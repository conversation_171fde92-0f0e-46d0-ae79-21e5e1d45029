import { spiderSyncUserTags, spiderSyncUserTagsReloader } from '@/models/store';
import { AccountPostItem, QueryAccountPostsForPagination } from '@/services/account';
import { getLastQueryTagIds, LSQueryTagIdsKey } from '@/services/constants';
import { proTableRequestAdapterParamsAndData } from '@/utils';
import { AccountPlatformEnum, AccountPlatformKeysMap } from '@/utils/platform';
import { CheckCircleFilled } from '@ant-design/icons';
import {
  ActionType,
  PageContainer,
  ProColumns,
  ProFormInstance,
  ProTable,
} from '@ant-design/pro-components';
import { NavLink } from '@umijs/max';
import { Button, message, Popover, Row, Space, Typography } from 'antd';
import copy from 'copy-to-clipboard';
import dayjs from 'dayjs';
import { useAtomValue, useSetAtom } from 'jotai';
import { useEffect, useRef } from 'react';
import SpiderSyncUserTagSelect from '../TagList/Components/spiderSyncUserTagSelect';
import PostListCard from './components/post-list-card';

const LiveList: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const formRef = useRef<ProFormInstance>();

  const tagGroups = useAtomValue(spiderSyncUserTags);
  const reloadSpiderSyncUserTags = useSetAtom(spiderSyncUserTagsReloader);

  useEffect(() => {
    reloadSpiderSyncUserTags();
  }, []);

  const columns: Array<ProColumns<AccountPostItem>> = [
    {
      title: '账号ID',
      dataIndex: 'accountId',
      hideInSearch: false,
      hideInTable: true,
    },
    {
      title: '账号名称',
      dataIndex: 'nickname',
      hideInSearch: false,
      hideInTable: true,
    },
    {
      title: '账号',
      dataIndex: 'nickname',
      hideInSearch: true,
      hideInTable: false,
      render(dom, record) {
        const entity = record.userInfo;
        if (!entity) return null;

        return (
          <Space>
            <NavLink
              to={`/monitor/account/user-list/detail/${AccountPlatformKeysMap[entity.platform]}/${
                entity.accountId
              }`}
            >
              <Row align="middle">
                <img
                  src={entity.avatar}
                  alt={entity.nickname}
                  width={24}
                  height={24}
                  style={{ marginRight: 8 }}
                />

                <span className="block text-sm">{entity.nickname}</span>
                {entity.blueVipFlag === 1 && (
                  <Popover content={entity.blueVipReason} trigger="hover">
                    <CheckCircleFilled style={{ marginLeft: 4 }} />
                  </Popover>
                )}
              </Row>
            </NavLink>
            <Button
              type="default"
              size="small"
              onClick={() => {
                if (entity.accountId) {
                  copy(entity.accountId);
                  message.success('已复制到剪切板');
                }
              }}
            >
              ID
            </Button>
          </Space>
        );
      },
    },
    {
      title: '账号标签',
      dataIndex: 'tagIds',
      hideInSearch: false,
      hideInTable: true,
      valueType: 'text',
      renderFormItem: () => {
        return <SpiderSyncUserTagSelect data={tagGroups} />;
      },
      initialValue: getLastQueryTagIds(),
    },
    {
      title: '账号平台',
      dataIndex: 'platform',
      hideInSearch: false,
      hideInTable: false,
      valueEnum: AccountPlatformEnum,
    },
    {
      title: '标题',
      dataIndex: 'title',
      hideInSearch: true,
      hideInTable: false,
      width: 240,
      render(dom, entity) {
        let title: string | undefined = entity.title;
        let description: string | undefined = entity.description;
        if (!description && title) {
          description = title;
          title = undefined;
        }
        return (
          <div style={{ width: 240, overflowX: 'hidden' }}>
            {title && (
              <Typography.Paragraph
                style={{
                  margin: 0,
                  width: 240,
                  fontWeight: 'bold',
                }}
                ellipsis={{ rows: 1, expandable: true, symbol: '更多' }}
              >
                {title}
              </Typography.Paragraph>
            )}
            {description && (
              <Typography.Paragraph
                style={{ margin: 0, width: 240 }}
                ellipsis={{ rows: 4, expandable: true, symbol: '更多' }}
              >
                {description}
              </Typography.Paragraph>
            )}
          </div>
        );
      },
    },
    {
      title: '作品',
      dataIndex: 'videoUrl',
      hideInSearch: true,
      hideInTable: false,
      render(_, entity) {
        return <PostListCard entity={entity} />;
      },
    },
    {
      title: '质检状态',
      dataIndex: 'qualityFlag',
      hideInSearch: false,
      hideInTable: false,
      valueEnum: {
        1: {
          text: '已触发',
          color: 'green',
        },
        0: {
          text: '未触发',
          color: 'gray',
        },
      },
    },
    {
      title: '发布时间',
      dataIndex: 'post_create_time',
      valueType: 'dateTimeRange',
      hideInTable: true,
      initialValue: [dayjs().subtract(7, 'days'), dayjs()],
      search: {
        transform: (value) => {
          return {
            startTime: value[0],
            endTime: value[1],
          };
        },
      },
    },
    {
      title: '作品 ID',
      dataIndex: 'postId',
      hideInTable: true,
    },
    {
      title: '点赞数',
      dataIndex: 'diggCount',
      hideInSearch: true,
      hideInTable: false,
      valueType: (entity) => (entity.diggCount !== null ? 'digit' : 'text'),
      renderText: (_, entity) =>
        entity.diggCount !== null ? entity.diggCount : entity.diggCountStr,
      sorter: true,
    },
    {
      title: '评论数',
      dataIndex: 'commentCount',
      hideInSearch: true,
      hideInTable: false,
      valueType: (entity) => (entity.commentCount !== null ? 'digit' : 'text'),
      renderText: (_, entity) =>
        entity.commentCount !== null ? entity.commentCount : entity.commentCountStr,
      sorter: true,
    },
    {
      title: '收藏数',
      dataIndex: 'collectCount',
      hideInSearch: true,
      hideInTable: false,
      valueType: (entity) => (entity.collectCount !== null ? 'digit' : 'text'),
      renderText: (_, entity) =>
        entity.collectCount !== null ? entity.collectCount : entity.collectCountStr,
      sorter: true,
    },
    {
      title: '分享数',
      dataIndex: 'shareCount',
      hideInSearch: true,
      hideInTable: false,
      valueType: (entity) => (entity.shareCount !== null ? 'digit' : 'text'),
      renderText: (_, entity) =>
        entity.shareCount !== null ? entity.shareCount : entity.shareCountStr,
      sorter: true,
    },
    {
      title: '发布时间',
      dataIndex: 'post_create_time',
      hideInSearch: true,
      hideInTable: false,
      renderText(_, record) {
        if (!record.postCreateTime) {
          return '-';
        }

        return dayjs(record.postCreateTime).format('YYYY-MM-DD HH:mm');
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      hideInSearch: true,
      hideInTable: false,
      render(_, record) {
        return (
          <Space>
            {record.platform === 1 && (
              <a
                onClick={() => {
                  if (record.shareUrl) {
                    window.open(record.shareUrl);
                  } else {
                    window.open(
                      `https://www.douyin.com/user/${record.accountId}?modal_id=${record.postId}`,
                    );
                  }
                }}
              >
                抖音作品页面
              </a>
            )}
            {record.platform === 6 && (
              <a
                onClick={() => {
                  window.open(`https://www.xiaohongshu.com/explore/${record.postId}`);
                }}
              >
                小红书作品页面
              </a>
            )}
            {record.qualityFlag ? (
              <a
                onClick={() => {
                  window.open(
                    `${window.location.origin}/post/detail/${record.postId}/${record.platform}/${record.projectIndustryType}`,
                  );
                }}
              >
                作品详情监控
              </a>
            ) : null}
          </Space>
        );
      },
    },
  ];

  return (
    <PageContainer extra={[]}>
      <ProTable<AccountPostItem>
        formRef={formRef}
        actionRef={actionRef}
        scroll={{ x: 'max-content' }}
        columns={columns}
        rowKey="id"
        toolBarRender={false}
        bordered
        size="small"
        dateFormatter="string"
        pagination={{ defaultPageSize: 15 }}
        search={{ filterType: 'query', defaultCollapsed: false }}
        request={(params, sorter) => {
          localStorage.setItem(LSQueryTagIdsKey, JSON.stringify({ tagIds: params.tagIds || [] }));
          return proTableRequestAdapterParamsAndData(
            params,
            sorter,
            QueryAccountPostsForPagination,
          );
        }}
        rowSelection={{
          type: 'checkbox',
        }}
        tableAlertRender={({ selectedRowKeys, onCleanSelected }) => {
          return (
            <Space size={24}>
              <span>
                已选 {selectedRowKeys.length} 项
                <a style={{ marginInlineStart: 8 }} onClick={onCleanSelected}>
                  取消选择
                </a>
              </span>
            </Space>
          );
        }}
        tableAlertOptionRender={({ selectedRows }) => {
          return (
            <Space size={16}>
              <a
                onClick={() => {
                  if (selectedRows.length) {
                    copy(
                      selectedRows
                        .filter((i) => i.videoUrl)
                        .map((i) => i.videoUrl)
                        .join('\n'),
                    );
                    message.success('已复制到剪切板');
                  }
                }}
              >
                批量复制链接
              </a>
            </Space>
          );
        }}
      />
    </PageContainer>
  );
};

export default LiveList;
