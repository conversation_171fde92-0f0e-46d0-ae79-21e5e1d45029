import XGVideoCard from '@/components/xgVideoCard';
import { AccountPostItem } from '@/services/account';
import { LinkOutlined, SoundOutlined } from '@ant-design/icons';
import { Button, Image, message, Space, Tooltip } from 'antd';
import copy from 'copy-to-clipboard';

interface PropsType {
  entity: AccountPostItem;
}

const PostListCard: React.FC<PropsType> = ({ entity }: PropsType) => {
  if (entity.postType === 3 && entity.pictureUrls && entity.coverUrl) {
    // 图片视频类型
    const videoUrl = entity.videoTransferUrl || entity.videoUrl;
    return (
      <Space>
        <div style={{ width: '100px', height: '132px', overflow: 'hidden' }}>
          <Image.PreviewGroup
          // items={entity.pictureTransferUrls.length ? entity.pictureTransferUrls : entity.pictureUrls}
          >
            {entity.pictureTransferUrls?.length ? (
              entity.pictureTransferUrls.map((item, index) => {
                return (
                  <Image
                    key={index}
                    src={
                      entity.coverTransferUrl && entity.coverTransferUrl.length
                        ? entity.coverTransferUrl
                        : item
                    }
                    width={100}
                    height={132}
                    style={{ objectFit: 'contain', position: 'absolute', left: '0', top: '0' }}
                  />
                );
              })
            ) : entity.pictureUrls.length ? (
              entity.pictureUrls.map((item, index) => {
                return (
                  <Image
                    key={index}
                    src={
                      entity.coverTransferUrl && entity.coverTransferUrl.length
                        ? entity.coverTransferUrl
                        : item
                    }
                    width={100}
                    height={132}
                    style={{ objectFit: 'contain', position: 'absolute', left: '0', top: '0' }}
                  />
                );
              })
            ) : (
              <Image
                src={entity.coverUrl}
                width={100}
                height={132}
                style={{ objectFit: 'contain', position: 'absolute', left: '0', top: '0' }}
              />
            )}
          </Image.PreviewGroup>
        </div>
        {entity.pictureUrls.length > 1 && <span>等{entity.pictureUrls.length}张图片</span>}
        {videoUrl && (
          <Button
            icon={<SoundOutlined />}
            size="small"
            onClick={() => {
              window.open(videoUrl);
            }}
          >
            bgm
          </Button>
        )}
      </Space>
    );
  }

  if (entity.postType === 2 && entity.pictureUrls && entity.coverUrl) {
    // 图片类型
    return (
      <Space>
        <div style={{ width: '100px', height: '132px', overflow: 'hidden' }}>
          <Image.PreviewGroup
          // items={entity.pictureTransferUrls.length ? entity.pictureTransferUrls : entity.pictureUrls}
          >
            {entity.pictureTransferUrls?.length ? (
              entity.pictureTransferUrls.map((item, index) => {
                return (
                  <Image
                    key={index}
                    src={
                      entity.coverTransferUrl && entity.coverTransferUrl.length
                        ? entity.coverTransferUrl
                        : item
                    }
                    width={100}
                    height={132}
                    style={{ objectFit: 'contain', position: 'absolute', left: '0', top: '0' }}
                  />
                );
              })
            ) : entity.pictureUrls.length ? (
              entity.pictureUrls.map((item, index) => {
                return (
                  <Image
                    key={index}
                    src={
                      entity.coverTransferUrl && entity.coverTransferUrl.length
                        ? entity.coverTransferUrl
                        : item
                    }
                    width={100}
                    height={132}
                    style={{ objectFit: 'contain', position: 'absolute', left: '0', top: '0' }}
                  />
                );
              })
            ) : (
              <Image
                src={entity.coverUrl}
                width={100}
                height={132}
                style={{ objectFit: 'contain', position: 'absolute', left: '0', top: '0' }}
              />
            )}
          </Image.PreviewGroup>
        </div>
        {entity.pictureUrls.length > 1 && <span>等{entity.pictureUrls.length}张图片</span>}
      </Space>
    );
  }

  // 视频类型
  const videoUrl = entity.videoTransferUrl || entity.videoUrl;
  if (videoUrl) {
    return (
      <Space>
        <XGVideoCard
          playableVideoUrl={entity.videoTransferUrl}
          videoUrl={entity.videoUrl}
          width={100}
          height={132}
          coverUrl={
            entity.coverTransferUrl && entity.coverTransferUrl.length
              ? entity.coverTransferUrl
              : entity.coverUrl
          }
          videoWidth={entity.videoWidth}
          videoHeight={entity.videoHeight}
        />
        <Tooltip title="复制视频链接">
          <Button
            icon={<LinkOutlined />}
            size="small"
            onClick={() => {
              copy(videoUrl);
              message.success('已复制到剪切板');
            }}
          >
            链接
          </Button>
        </Tooltip>
      </Space>
    );
  }

  if (entity.shareUrl && entity.coverUrl) {
    return (
      <Space>
        <Image
          onClick={() => {
            window.open(entity.shareUrl);
          }}
          src={entity.coverUrl}
          width={100}
          height={132}
          referrerPolicy="no-referrer"
          style={{ objectFit: 'cover', borderRadius: '4px' }}
          preview={false}
        />
        <Button
          icon={<LinkOutlined />}
          size="small"
          onClick={() => {
            window.open(entity.shareUrl);
          }}
        >
          点击查看
        </Button>
      </Space>
    );
  }

  if (entity.shareUrl) {
    return (
      <Space>
        <Button
          icon={<LinkOutlined />}
          size="small"
          onClick={() => {
            window.open(entity.shareUrl);
          }}
        >
          点击查看
        </Button>
      </Space>
    );
  }

  return null;
};

export default PostListCard;
