.infoCard {
  width: 100%;
  min-height: 197px;
  color: rgba(255, 255, 255, 0.8);
  background: #1a1d23;
  border-radius: 6px;

  .userInfo {
    display: flex;
    flex-direction: row;
    align-items: center;

    .name {
      flex: 1;
      align-items: center;
      margin-top: 10px;
      margin-left: 30px;
      font-style: normal;

      div:nth-of-type(1) {
        font-weight: 500;
        font-size: 16px;
      }

      div:nth-of-type(2) {
        font-weight: 400;
        font-size: 14px;
      }
    }
  }

  .detail {
    margin-top: 20px;
    overflow: hidden;
    font-weight: 400;
    font-size: 12px;
    font-style: normal;

    div {
      margin-block: 5px;
    }

    .title {
      max-width: 330px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      height: 21px;
    }

    .liveTime {
      min-height: 21px;
      max-height: 38px;
    }

    .tag {
      display: inline-block;
      height: 21px;
      padding: 1px 10px;
      background-color: #2f333c;
      border-radius: 5px;
      margin-inline-end: 5px;

      img {
        margin-right: 3px;
      }
    }
  }
}
