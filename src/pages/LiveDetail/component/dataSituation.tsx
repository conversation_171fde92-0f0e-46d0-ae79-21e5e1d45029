import LineChartTrend from '@/components/chart/lineChartTrend';
import StatistCardDark from '@/components/statistCardDark';
import WordCloudComponent from '@/components/wordCloud';
import { QualityTargetType } from '@/services/constants';
import {
  ExportLiveEcommerceHighLight,
  GetLiveTrend,
  GetLiveWordCloud,
  LiveQualityEcommerceItem,
  LiveQualityItem,
  TimeLineChartData,
  WordCloudData,
} from '@/services/quality';
import { formatNum } from '@/utils/common';
import {
  CustomDateRadioButtonDark,
  CustomDateRadioGroup,
  HideScrollBarDiv,
  ProCardDark,
} from '@/utils/commonStyle';
import { ProCard } from '@ant-design/pro-components';
import { useParams } from '@umijs/max';
import { useAsyncEffect } from 'ahooks';
import { Button, Flex, message, Radio, RadioChangeEvent } from 'antd';
import { useState } from 'react';
import { styled } from 'styled-components';
import HighLightShow from './highLightShow';
import { IndustryType } from '@/utils/const';
import { usePollingExport } from '@/hooks/usePollingExport';
import dayjs from 'dayjs';

const options = [
  { label: '在线人数', value: 'online' },
  { label: '弹幕数', value: 'dm' },
  { label: '点赞数', value: 'digg' },
  { label: '成交金额', value: 'totalPayOrderCountGmvIncludeCoupon' },
  { label: '成交单数', value: 'totalPayOrderCount' },
  { label: '消耗金额', value: 'statCost' },
];

const dataformOptions = [
  { label: '总量', value: 'total' },
  { label: '增量', value: 'increment' },
];

type DataSituationProps = {
  liveData?: LiveQualityItem | LiveQualityEcommerceItem;
  industryType?: string;
};

const CustomRadioGroup = styled(Radio.Group)`
  .ant-radio-button-wrapper {
    color: #8f9093;
    background-color: #1b1d22;
    border-color: #3d4350;

    &::before {
      background-color: #3d4350;
    }

    &.ant-radio-button-wrapper-checked {
      background-color: #3870ff;
    }

    &.ant-radio-button-wrapper-checked:hover {
      background-color: #3870ff;
    }
  }
`;

const DataSituation = (props: DataSituationProps) => {
  const { liveData, industryType } = props;
  const { roomId, platform } = useParams();
  const [type, setType] = useState('online');
  const [dataform, setDataForm] = useState<'total' | 'increment'>('total');
  const [radioValue, setRadioValue] = useState<number | null>(1);
  const [lineChartData, setLineChartData] = useState<TimeLineChartData[]>([]);
  const [showLineChartData, setShowLineChartData] = useState<TimeLineChartData[]>([]);
  const [wordCloudData, setWordCloudData] = useState<WordCloudData[]>([]);
  const ecommerceType = ['totalPayOrderCountGmvIncludeCoupon', 'totalPayOrderCount', 'statCost'];
  const { pollingExport } = usePollingExport(`高光片段导出-${dayjs().format('YYYYMMDD')}`);

  const onChangeType = ({ target: { value } }: RadioChangeEvent) => {
    setType(value);
    if (ecommerceType.includes(value)) {
      setRadioValue(10);
    }
  };

  const onChangeFormType = ({ target: { value } }: RadioChangeEvent) => {
    setDataForm(value);
  };

  const onChange = (e: RadioChangeEvent) => {
    const interval = e.target.value;
    const newLineChartData = lineChartData.filter((item, index) => index % interval === 0);
    setRadioValue(e.target.value);
    setShowLineChartData(newLineChartData);
  };

  useAsyncEffect(async () => {
    const res = await GetLiveTrend({ roomId, type, platform });
    const { data } = res;
    setLineChartData(data || []);
    setShowLineChartData(data || []);
  }, [type]);

  useAsyncEffect(async () => {
    const res = await GetLiveWordCloud({ roomId, platform });
    const wordData = res.data;
    setWordCloudData(wordData || []);
  }, []);

  const showCardInfo: Record<string, React.ReactNode> = {
    [IndustryType.CAR]: (
      <ProCard ghost style={{ backgroundColor: '#1b1d22' }}>
        <ProCardDark title="人气数据" ghost bodyStyle={{ display: 'flex' }} colSpan="50%">
          <StatistCardDark title="累计观看人数" value={formatNum(liveData?.viewCount)} />
          <StatistCardDark title="人气峰值" value={formatNum(liveData?.popularityPeakNum)} />
        </ProCardDark>
        <ProCardDark title="互动数据" ghost bodyStyle={{ display: 'flex' }} colSpan="50%">
          <StatistCardDark title="总弹幕条数" value={formatNum(liveData?.dmCount)} />
          <StatistCardDark title="总点赞次数" value={formatNum(liveData?.diggCount)} />
        </ProCardDark>
      </ProCard>
    ),
    [IndustryType.E_COMMERCE]: (
      <ProCard ghost style={{ backgroundColor: '#1b1d22' }}>
        <ProCardDark title="人气数据" ghost bodyStyle={{ display: 'flex' }} colSpan="30%">
          <StatistCardDark title="累计观看人数" value={formatNum(liveData?.viewCount)} />
          <StatistCardDark title="人气峰值" value={formatNum(liveData?.popularityPeakNum)} />
        </ProCardDark>
        <ProCardDark title="互动数据" ghost bodyStyle={{ display: 'flex' }} colSpan="30%">
          <StatistCardDark title="总弹幕条数" value={formatNum(liveData?.dmCount)} />
          <StatistCardDark title="总点赞次数" value={formatNum(liveData?.diggCount)} />
        </ProCardDark>
        <ProCardDark
          title="成交数据"
          ghost
          bodyStyle={{ display: 'flex' }}
          colSpan="30%"
          tooltip="下播后,第二天10点更新数据"
        >
          <StatistCardDark
            title="成交金额"
            value={formatNum((liveData as LiveQualityEcommerceItem)?.totalPayOrderGmvIncludeCoupon)}
          />
          <StatistCardDark
            title="成交单数"
            value={formatNum((liveData as LiveQualityEcommerceItem)?.totalPayOrderCount)}
          />
          <StatistCardDark
            title="消耗金额"
            value={formatNum((liveData as LiveQualityEcommerceItem)?.statCost)}
          />
        </ProCardDark>
      </ProCard>
    ),
  };

  const renderTimeInterval = () => {
    if (ecommerceType.includes(type)) {
      return (
        <CustomDateRadioGroup value={radioValue} buttonStyle="solid" onChange={onChange}>
          <CustomDateRadioButtonDark value={10}>10分钟间隔</CustomDateRadioButtonDark>
        </CustomDateRadioGroup>
      );
    } else {
      return (
        <CustomDateRadioGroup value={radioValue} buttonStyle="solid" onChange={onChange}>
          <CustomDateRadioButtonDark value={1}>1分钟间隔</CustomDateRadioButtonDark>
          <CustomDateRadioButtonDark value={5}>5分钟间隔</CustomDateRadioButtonDark>
        </CustomDateRadioGroup>
      );
    }
  };

  const handleExportHightLight = async () => {
    const res = await ExportLiveEcommerceHighLight({ roomId, platform });
    if (res.data) {
      pollingExport({ taskId: res.data });
    } else {
      message.error('导出失败,请重试');
    }
  };

  return (
    <HideScrollBarDiv style={{ height: 'calc(100vh - 66px - 56px - 40px)' }}>
      <div
        style={{
          width: '100%',
          height: 'max-content',
          alignItems: 'stretch',
          display: 'flex',
          flexDirection: 'column',
          paddingBottom: '100px',
        }}
      >
        {industryType && showCardInfo[industryType]}
        <ProCardDark style={{ marginTop: '20px' }} title="本场数据" ghost>
          <CustomRadioGroup
            options={options}
            onChange={onChangeType}
            value={type}
            optionType="button"
            buttonStyle="solid"
            style={{ marginBottom: 16 }}
          />

          <CustomRadioGroup
            options={dataformOptions}
            onChange={onChangeFormType}
            value={dataform}
            optionType="button"
            buttonStyle="solid"
            style={{ float: 'right', marginRight: 33 }}
          />

          <Flex align="flex-start" style={{ marginBottom: 16 }}>
            {renderTimeInterval()}
          </Flex>
          <LineChartTrend
            lineChartData={showLineChartData}
            showXAxis={'HH:mm'}
            dataform={dataform}
            target={QualityTargetType.LIVE}
          />
        </ProCardDark>

        <WordCloudComponent wordCloudData={wordCloudData} target={QualityTargetType.LIVE} />

        <ProCardDark
          style={{ marginTop: '20px', minHeight: '350px' }}
          title="高光片段"
          ghost
          extra={
            Number(industryType) === IndustryType.E_COMMERCE ? (
              <Button type="link" className="text-base" onClick={handleExportHightLight}>
                下载高光
              </Button>
            ) : null
          }
        >
          <HighLightShow />
        </ProCardDark>
      </div>
    </HideScrollBarDiv>
  );
};

export default DataSituation;
