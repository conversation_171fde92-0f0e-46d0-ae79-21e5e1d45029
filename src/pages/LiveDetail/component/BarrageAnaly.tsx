import { useState } from 'react';
import { HideScrollBarDiv, ProCardDark } from '@/utils/commonStyle';
import WordCloudComponent from '@/components/wordCloud';
import BarrageOverviewCharts from '@/pages/ProjectHome/BarrageAnalysis/components/barrageOverviewCharts';
import BarrageList from '@/pages/ProjectHome/BarrageAnalysis/components/barrageList';
import { GetLiveWordCloud, WordCloudData } from '@/services/quality';
import { QualityTargetType } from '@/services/constants';
import { useAsyncEffect } from 'ahooks';
import { useParams } from '@umijs/max';

interface BarrageAnalyProps {
  projectId?: string;
}

const BarrageAnaly = (props: BarrageAnalyProps) => {
  const { projectId } = props;
  const { roomId, platform } = useParams();

  const [wordCloudData, setWordCloudData] = useState<WordCloudData[]>([]);
  const [barrageTotal, setBarrageTotal] = useState<number | null>(null);

  useAsyncEffect(async () => {
    const res = await GetLiveWordCloud({ roomId, platform });
    const wordData = res.data;
    if (wordData) {
      setWordCloudData(wordData);
    }
  }, []);

  return (
    <HideScrollBarDiv style={{ height: 'calc(100vh - 66px - 60px - 56px - 30px)' }}>
      <ProCardDark ghost style={{ padding: '0 24px' }} direction="column">
        <ProCardDark
          ghost
          style={{ padding: 0 }}
          title="数据总量"
          extra={
            barrageTotal !== null && (
              <span>
                弹幕总数：<span style={{ color: '#1e5eff' }}>{barrageTotal}</span>
              </span>
            )
          }
        >
          <BarrageOverviewCharts
            darkMode={true}
            liveRoomId={roomId}
            projectId={projectId}
            onBarrageTotalChange={setBarrageTotal}
          />
        </ProCardDark>

        <ProCardDark ghost title="弹幕分析" style={{ padding: 0, marginTop: 30 }}>
          <BarrageList darkMode={true} liveRoomId={roomId} projectId={projectId} />
        </ProCardDark>
      </ProCardDark>

      <WordCloudComponent wordCloudData={wordCloudData} target={QualityTargetType.LIVE} />
    </HideScrollBarDiv>
  );
};

export default BarrageAnaly;
