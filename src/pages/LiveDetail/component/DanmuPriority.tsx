import { DanmuTimeline } from '@/components/antd/timeline';
import { DanmuItem } from '@/components/commentShow';

import { useLiveDanmu } from '@/hooks/request/use-danmu';
import { useLiveFrame } from '@/hooks/request/use-live-frame';
import { useLiveSubtitle } from '@/hooks/request/use-live-subtitle';
import { QualityTargetType } from '@/services/constants';
import { LiveQualityItem, LiveScreenItem } from '@/services/quality';
import { HideScrollBarDiv } from '@/utils/commonStyle';
import { useSize } from 'ahooks';
import { Empty, Popover, Space, Spin } from 'antd';
import dayjs from 'dayjs';
import duration from 'dayjs/plugin/duration';
import { useAtom } from 'jotai';
import { chunk, debounce, isEmpty } from 'lodash-es';
import React, { useEffect, useRef, useState } from 'react';
import { styled, keyframes } from 'styled-components';
import Player from 'xgplayer';
import { DanmuSpan } from './PicturePriority';
import { selectedLiveFrameAtom, selectedLiveSubtitleAtom } from './liveAtom';
import { VideoSubtitleItem } from './types';

dayjs.extend(duration);

export const SbuTitleContainer = styled.span<{
  $showHit: boolean;
  $subtitleIsSelected: boolean;
  $exInfo: boolean;
}>`
  text-decoration: ${(props) =>
    props.$exInfo && props.$showHit
      ? 'underline wavy red'
      : props.$subtitleIsSelected
        ? 'underline solid'
        : 'none'};

  cursor: ${(props) => (props.$showHit ? 'pointer' : 'default')};

  :hover {
    text-decoration: underline;
    text-decoration-style: ${(props) => (!props.$showHit ? 'dotted' : 'solid')};
  }
  .highlight {
    color: black;
    background: yellow;
  }
`;

export function durationToTime(duration: number) {
  return dayjs.duration(duration, 'seconds').format('HH:mm:ss');
}

type FrameInfo = {
  width: number;
  height: number;
  frameNum: number;
};

type FrameAnalyProps = {
  liveData?: LiveQualityItem;
  videoPlayerRef: React.MutableRefObject<Player | null>;
  showHit: boolean;
  targetId?: string;
  platform?: string;
  targetType: QualityTargetType;
  isLive?: boolean;
  isInQuality?: boolean;
  showContent: string[];
  tabKey?: string;
};

// 定义闪烁动画
export const flashAnimation = keyframes`
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
`;

export const SubTitleSpan = styled.span`
  [data-tooltip] {
    position: relative;
  }

  [data-tooltip]:hover::after {
    position: absolute;
    top: -200%;
    right: 0;
    left: 0;
    width: 50px;
    margin-top: 5px;
    padding: 2px 5px;
    color: white;
    white-space: nowrap;
    text-align: center;
    word-wrap: break-word;
    background-color: #424242;
    border-radius: 5px;
    box-shadow:
      0 6px 16px 0 rgba(0, 0, 0, 0.08),
      0 3px 6px -4px rgba(0, 0, 0, 0.12),
      0 9px 28px 8px rgba(0, 0, 0, 0.05);
    transform: translateX(-25%);
    content: attr(data-tooltip);
  }

  &.flash-animation {
    background-color: #6c8fdd;
    animation: ${flashAnimation} 2s 2;
  }
`;

export const ImgByFlash = styled.img`
  width: 100%;
  height: 100%;
  object-fit: cover;

  &.flash-animation {
    background-color: #6c8fdd;
    animation: ${flashAnimation} 2s 2;
  }
`;

export const FrameBlendMode = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  /* mix-blend-mode: screen; */
  .ex-info {
    position: absolute;
    bottom: 0;
    width: 100%;
    overflow: hidden;
    color: #ffffff;
    font-size: 10px;
    white-space: nowrap;
    text-align: center;
    background: #272c36;
  }
`;

export const frameInfo: FrameInfo = {
  width: 100,
  height: 180,
  frameNum: 10,
};
interface IRenderData {
  range: { startTime: number; endTime: number };
  frameData: LiveScreenItem[];
  danmuList: DanmuItem[];
  subtitles: VideoSubtitleItem[];
  ex: boolean;
}
export const DanmuPriority = (props: FrameAnalyProps) => {
  const { videoPlayerRef, showContent, showHit, targetId, platform, liveData, tabKey } = props;

  const { data: liveScreen } = useLiveFrame({
    roomId: targetId,
    platform,
  });

  const { data: subtitles, loading: subtitleLoading } = useLiveSubtitle({
    roomId: targetId,
    platform,
  });
  const { data: danmuList } = useLiveDanmu({
    roomId: targetId,
    platform,
  });
  const [selectedLiveFrame, setSelectedLiveFrame] = useAtom(selectedLiveFrameAtom);
  const [selectSubtitles, setSelectSubtitles] = useAtom(selectedLiveSubtitleAtom);

  const rightSiderRef = useRef<HTMLDivElement | null>(null);
  const size = useSize(rightSiderRef);
  const [liveFrameData, setLiveFrameData] = useState<IRenderData[]>([]);

  const subtitleFilter = useRef<string[]>([]);
  const oneLineFrameNumRef = useRef<number>(0);

  const [hoverRange, setHoverRange] = useState<{ startTime: number; endTime: number } | null>(null);

  const updateLiveFrameData = debounce(() => {
    const width = size?.width;
    if (!width || isEmpty(liveScreen)) return;
    subtitleFilter.current = [];
    oneLineFrameNumRef.current = Math.floor(width / (frameInfo.width / 2)) - 1;
    const framePicArr = chunk(liveScreen, oneLineFrameNumRef.current);

    const renderFrameData = framePicArr.map((frame, idx) => {
      let ex = false;
      const startSeconds = Number(idx * 10) * oneLineFrameNumRef.current;
      const endSeconds = (Number(idx * 10) + 10) * oneLineFrameNumRef.current;
      const itemDanmuList = danmuList?.filter(
        (danmu) => Number(danmu.startTime) >= startSeconds && Number(danmu.startTime) <= endSeconds,
      );
      itemDanmuList?.forEach((item) => {
        if (item.suspectFlag) {
          ex = true;
        }
      });
      const result = itemDanmuList?.reduce(
        (
          acc: DanmuItem[],
          curr: DanmuItem & {
            count?: number;
          },
        ) => {
          const existingItem = acc.find((item) => item.content === curr.content);
          if (existingItem) {
            //@ts-ignore
            existingItem.count++;
          } else {
            acc.push({ ...curr, count: 1 });
          }
          return acc;
        },
        [],
      );
      // TODO 字幕和弹幕的排序
      result?.sort((a, b) => {
        // 先按 count 进行降序排序
        // @ts-ignore
        if (a.count > b.count) {
          return -1;
          // @ts-ignore
        } else if (a.count < b.count) {
          return 1;
        } else {
          // 如果 count 相等，再按 time 进行升序排序
          return dayjs(a.time).diff(dayjs(b.time));
        }
      });
      const itemSubtitles = subtitles?.filter((subtitle) => {
        const startTime = Number(subtitle.startTime);

        const haveBeenSelected = subtitleFilter.current.some((item) => item === subtitle.id);
        if (startTime >= startSeconds && startTime < endSeconds && !haveBeenSelected) {
          subtitleFilter.current = subtitleFilter.current.concat(subtitle.id as string);
          return true;
        } else {
          return false;
        }
      });
      return {
        range: {
          startTime: startSeconds,
          endTime: endSeconds,
        },
        frameData: frame,
        danmuList: result,
        subtitles: itemSubtitles,
        ex,
      };
    });

    const newRenderFrameData = mergeData(renderFrameData);
    // @ts-ignore
    setLiveFrameData(newRenderFrameData);
  }, 300);

  useEffect(() => {
    updateLiveFrameData();
  }, [size, frameInfo, liveScreen, subtitles, danmuList]);

  const liveFrameClick = (item: LiveScreenItem, startTime: number, index: number) => {
    if (showHit) {
      const isSelect = selectedLiveFrame.some((selectedItem) => selectedItem.picUrl === item.url);
      if (isSelect) {
        setSelectedLiveFrame(
          selectedLiveFrame.filter((selectedItem) => selectedItem.picUrl !== item.url),
        );
      } else {
        setSelectedLiveFrame([
          ...selectedLiveFrame,
          {
            startTime: startTime,
            endTime: startTime + 10,
            picUrl: item.url,
            index,
          },
        ]);
      }
    } else {
      if (videoPlayerRef.current) {
        videoPlayerRef.current.currentTime = startTime;
        if (videoPlayerRef.current.paused) {
          videoPlayerRef.current.play();
        }
      }
    }
  };

  function handleSubtitleHover(subtitleItem: VideoSubtitleItem) {
    setHoverRange({
      startTime: subtitleItem.startTime,
      endTime: subtitleItem.endTime,
    });
  }

  function handleSubtitleHoverLeave() {
    setHoverRange(null);
  }
  function handleSubtitleClick(subtitleItem: VideoSubtitleItem) {
    if (!showHit) return;
    const isSelect = selectSubtitles.some((selectedItem) => selectedItem.id === subtitleItem.id);
    if (isSelect) {
      setSelectSubtitles(
        selectSubtitles.filter((selectedItem) => selectedItem.id !== subtitleItem.id),
      );
    } else {
      setSelectSubtitles([
        {
          ...subtitleItem,
        },
      ]);
    }
  }
  const suspectFlagList = danmuList?.filter((danmu) => danmu.suspectFlag === 1);
  if (isEmpty(suspectFlagList)) {
    return <Empty description="直播间没有违规弹幕" />;
  }

  return (
    <div
      style={{
        width: '100%',
        height: 'max-content',
      }}
    >
      <HideScrollBarDiv
        id="scrollableDiv"
        ref={rightSiderRef}
        style={{
          display: 'flex',
          width: '100%',
          flexDirection: 'row',
          flexWrap: 'wrap',
          height: 'calc(100vh - 56px - 300px)',
        }}
      >
        <div
          style={{
            display: 'flex',
            width: '100%',
            flexDirection: 'row',
            flexWrap: 'wrap',
            paddingTop: '10px',
            paddingLeft: '2px',
          }}
        >
          {liveFrameData && (
            <DanmuTimeline
              items={liveFrameData?.map((oneLineLiveFrame, idx) => {
                return {
                  ...(!(Boolean(oneLineLiveFrame?.ex) || liveFrameData[idx + 1]?.ex) && {
                    dot: (
                      <svg
                        style={{ zIndex: 1000 }}
                        width="14"
                        height="14"
                        viewBox="0 0 14 14"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M13.125 6.625C13.125 3.24226 10.3827 0.5 7 0.5C3.61726 0.5 0.875 3.24226 0.875 6.625C0.875 10.0077 3.61726 12.75 7 12.75C10.3827 12.75 13.125 10.0077 13.125 6.625Z"
                          fill="#1A1D23"
                          stroke="#3D4350"
                        />
                        <path
                          d="M4.81912 5.95898L4.60699 5.74685C4.48983 5.62969 4.48983 5.43974 4.60699 5.32258L6.78787 3.1417C6.90503 3.02454 7.09498 3.02454 7.21213 3.1417L9.39302 5.32258C9.51017 5.43974 9.51017 5.62969 9.39302 5.74685L9.18086 5.95896C9.06371 6.07611 8.87378 6.07612 8.75662 5.95898L7 4.20272L5.24338 5.95898C5.12622 6.07612 4.93627 6.07613 4.81912 5.95898ZM4.81912 7.24795L4.60699 7.46008C4.48983 7.57724 4.48983 7.76719 4.60699 7.88435L6.78787 10.0652C6.90503 10.1824 7.09498 10.1824 7.21213 10.0652L9.39302 7.88435C9.51017 7.76719 9.51017 7.57724 9.39302 7.46008L9.18086 7.24797C9.06371 7.13082 8.87378 7.13081 8.75662 7.24795L7 9.00422L5.24338 7.24795C5.12622 7.13081 4.93627 7.1308 4.81912 7.24795Z"
                          fill="#3770FF"
                        />
                      </svg>
                    ),
                  }),

                  children: (
                    <div key={`timeLineItem-${idx}`}>
                      {Boolean(oneLineLiveFrame?.ex) || liveFrameData[idx + 1]?.ex ? (
                        <Space>
                          <span>
                            {dayjs(liveData?.liveStartTime)
                              .add(Number(idx * 10) * oneLineFrameNumRef.current, 'second')
                              .format('HH:mm:ss')}
                          </span>
                          {liveData?.liveStatus !== 0 && (
                            <span style={{ color: '#8c8e91' }}>
                              （{durationToTime(Number(idx * 10) * oneLineFrameNumRef.current)}）
                            </span>
                          )}
                        </Space>
                      ) : (
                        <div>
                          已折叠
                          <span>
                            {dayjs(liveData?.liveStartTime)
                              .add(oneLineLiveFrame.range.startTime, 'second')
                              .format('HH:mm:ss')}
                          </span>
                          -
                          <span>
                            {dayjs(liveData?.liveStartTime)
                              .add(oneLineLiveFrame.range.endTime, 'second')
                              .format('HH:mm:ss')}{' '}
                            的内容
                          </span>
                        </div>
                      )}

                      {(Boolean(oneLineLiveFrame?.ex) || liveFrameData[idx + 1]?.ex) && (
                        <>
                          <div>
                            {showContent.includes('frame') &&
                              oneLineLiveFrame.frameData?.map((frame, index) => {
                                const currentFrameSeconds =
                                  // @ts-ignore
                                  liveScreen?.findIndex((live) => live.url === frame.url) * 10;
                                const frameStartTime = currentFrameSeconds;

                                const isSelected = selectedLiveFrame.some(
                                  (item) => item.picUrl === frame.url,
                                );
                                const isSelectedBySubtitle = selectSubtitles.some((subtitle) => {
                                  return (
                                    Number(frameStartTime) >= Number(subtitle.startTime) &&
                                    Number(frameStartTime) <= Number(subtitle.endTime)
                                  );
                                });
                                const width = frameInfo.width / 2;
                                const height = frameInfo.height / 2;
                                const videoWidth = 1080;
                                const maxWidth = 400 - 40 - 20;
                                const videoHeight = (1920 * maxWidth) / videoWidth;

                                const isHoveredBySubtitle =
                                  hoverRange?.startTime &&
                                  hoverRange?.endTime &&
                                  currentFrameSeconds >= hoverRange.startTime &&
                                  currentFrameSeconds <= hoverRange.endTime;

                                return (
                                  <React.Fragment key={frame.url}>
                                    <Popover
                                      key={frame.url}
                                      placement="left"
                                      mouseLeaveDelay={0}
                                      destroyTooltipOnHide
                                      fresh
                                      content={
                                        showHit ? (
                                          <div>
                                            <img
                                              src={frame.url}
                                              style={{
                                                width: maxWidth,
                                                height: videoHeight,
                                              }}
                                            />
                                            <div>
                                              {dayjs(liveData?.liveStartTime)
                                                .add(currentFrameSeconds, 'second')
                                                .format('HH:mm:ss')}
                                              {liveData?.liveStatus !== 0 && (
                                                <>（{durationToTime(currentFrameSeconds)}）</>
                                              )}
                                            </div>
                                          </div>
                                        ) : null
                                      }
                                      title={null}
                                    >
                                      <div
                                        style={{
                                          width,
                                          height,
                                          display: 'inline-block',
                                          position: 'relative',
                                          cursor: showHit ? 'pointer' : 'default',
                                        }}
                                        onClick={() =>
                                          liveFrameClick(frame, currentFrameSeconds, index)
                                        }
                                      >
                                        <ImgByFlash
                                          src={`${frame.url}?imageMogr2/thumbnail/!${frameInfo.width}x${frameInfo.height}r`}
                                          loading="lazy"
                                          id={`frame-${tabKey}-${frameStartTime}`}
                                        />
                                        {showHit && (isSelected || isSelectedBySubtitle) && (
                                          <FrameBlendMode
                                            style={{
                                              boxShadow:
                                                'inset 0 0 19.3px 1px rgba(55, 112, 255, 0.6)',
                                            }}
                                          />
                                        )}
                                        {isHoveredBySubtitle && (
                                          <FrameBlendMode
                                            style={{
                                              boxShadow:
                                                'inset 0 0 19.3px 1px rgba(55, 112, 255, 0.6)',
                                            }}
                                          />
                                        )}
                                        {showHit && !isEmpty(frame.exInfo) && (
                                          <FrameBlendMode
                                            style={{
                                              background: 'rgba(179,40,21, 0.3)',
                                              border: '1px solid #B32815',
                                              bottom: 0,
                                            }}
                                          >
                                            <span className="ex-info">
                                              {frame.exInfo.monitoringWordName}
                                            </span>
                                          </FrameBlendMode>
                                        )}
                                      </div>
                                    </Popover>
                                  </React.Fragment>
                                );
                              })}
                          </div>
                          {showContent.includes('subtitle') && (
                            <div>
                              <Spin
                                tip="口播字幕加载中..."
                                spinning={isEmpty(oneLineLiveFrame.subtitles) && subtitleLoading}
                              >
                                {isEmpty(oneLineLiveFrame.subtitles) && subtitleLoading ? (
                                  <div
                                    style={{
                                      width: '100%',
                                      height: '100px',
                                      display: 'flex',
                                      justifyContent: 'center',
                                      alignItems: 'center',
                                    }}
                                  />
                                ) : (
                                  <>
                                    {oneLineLiveFrame.subtitles?.map((subtitleItem) => {
                                      const subtitleIsSelected = selectSubtitles.some(
                                        (subtitle) => {
                                          return subtitle.id === subtitleItem.id;
                                        },
                                      );
                                      return (
                                        <SbuTitleContainer
                                          $showHit={showHit || subtitleIsSelected}
                                          $subtitleIsSelected={subtitleIsSelected}
                                          $exInfo={!isEmpty(subtitleItem.exInfo)}
                                          key={subtitleItem.id}
                                          onMouseEnter={() => {
                                            handleSubtitleHover(subtitleItem);
                                          }}
                                          onMouseLeave={() => {
                                            handleSubtitleHoverLeave();
                                          }}
                                          onClick={() => {
                                            handleSubtitleClick(subtitleItem);
                                          }}
                                        >
                                          <SubTitleSpan
                                            dangerouslySetInnerHTML={{
                                              __html:
                                                subtitleItem.content || subtitleItem.subtitle || '',
                                            }}
                                            key={subtitleItem.id}
                                            id={`subtitle-${tabKey}-${subtitleItem.startTime}`}
                                          />
                                        </SbuTitleContainer>
                                      );
                                    })}
                                  </>
                                )}
                              </Spin>
                            </div>
                          )}
                          <div>
                            <Space
                              size={[8, 8]}
                              wrap
                              style={{
                                marginTop: '5px',
                              }}
                            >
                              {oneLineLiveFrame.danmuList?.map((danmuItem) => {
                                return (
                                  <DanmuSpan key={danmuItem.id}>
                                    <span
                                      dangerouslySetInnerHTML={{
                                        __html: danmuItem.content,
                                      }}
                                    />
                                    {danmuItem.count && danmuItem.count > 1 && (
                                      <span className="count">X{danmuItem.count}</span>
                                    )}
                                  </DanmuSpan>
                                );
                              })}
                            </Space>
                          </div>
                        </>
                      )}
                    </div>
                  ),
                };
              })}
            />
          )}
        </div>
      </HideScrollBarDiv>
    </div>
  );
};
function mergeData(data: any[]) {
  const mergedData: never[] = [];
  let currentGroup: { range: { endTime: any } } | null = null;

  data.forEach((item, index) => {
    if (item.ex === false && data[index + 1]?.ex === false) {
      if (!currentGroup) {
        // 如果当前分组为空，则将当前数据作为新分组的起始
        currentGroup = { ...item };
      } else {
        // 否则，将当前数据合并到当前分组中
        currentGroup.range.endTime = item.range.endTime;
      }
    } else {
      if (currentGroup) {
        // 如果当前分组存在，则将其添加到结果数组中，并重置当前分组
        // @ts-ignore
        mergedData.push(currentGroup);
        currentGroup = null;
      }
      // 添加当前数据到结果数组中
      // @ts-ignore
      mergedData.push(item);
    }
  });

  // 如果最后一个分组还未添加到结果数组中，则添加它
  if (currentGroup) {
    mergedData.push(currentGroup);
  }

  return mergedData;
}
