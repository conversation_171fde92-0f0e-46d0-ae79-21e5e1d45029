import { SubmitQualityCard } from '@/components/submitQualityCard';
import { PicturePriority } from '@/pages/LiveDetail/component/PicturePriority';
import { ButtonTabs } from '@/pages/ProjectHome/style';
import { QualityTargetType } from '@/services/constants';
import {
  LiveQualityEcommerceItem,
  LiveQualityItem,
  QualityMonitorWordList,
} from '@/services/quality';
import { HideScrollBarDiv, ProCardDarkGhost, ShowRightSiderBtn } from '@/utils/commonStyle';
import { DoubleLeftOutlined, DoubleRightOutlined } from '@ant-design/icons';
import { useParams, useQuery } from '@umijs/max';
import { Checkbox, Flex, Space, Switch, Tabs, TabsProps } from 'antd';
import { useAtom } from 'jotai';
import { useEffect, useMemo, useRef, useState } from 'react';
import { styled } from 'styled-components';
import Player from 'xgplayer';
import CarPrice from './carPrice';
import { DanmuPriority } from './DanmuPriority';
import {
  danmuPriorityShowContentAtom,
  framePriorityShowContentAtom,
  liveTabKeyAtom,
  rightSiderActiveKeyAtom,
  selectedLiveFrameAtom,
  selectedLiveSubtitleAtom,
  showLiveHitStateAtom,
  subtitlesPriorityShowContentAtom,
  toggleLiveRightSiderShowAtom,
} from './liveAtom';
import QualityDetail from './qualityDetail';
import { SubtitlePriority } from './SubtitlePriority';

type VideoAnalyProps = {
  videoPlayerRef: React.MutableRefObject<Player | null>;
  targetId?: string;
  projectId?: string;
  platform?: string;
  monitoringWordType: QualityTargetType;
  isLive?: boolean;
  isInQuality?: boolean;
  liveData?: LiveQualityItem | LiveQualityEcommerceItem;
};

const TabsNoMarginBottom = styled(Tabs)`
  .ant-tabs-nav {
    margin-bottom: 0;
  }
`;

const TabContainer = styled.div`
  padding: 0px 20px 10px;
`;

const VideoAnaly = (props: VideoAnalyProps) => {
  const {
    videoPlayerRef,
    targetId,
    projectId,
    platform,
    monitoringWordType,
    isLive,
    isInQuality,
    liveData,
  } = props;
  const [showHitState, setShowHitState] = useAtom(showLiveHitStateAtom);
  const [toggleRightSiderShow, setToggleRightSiderShow] = useAtom(toggleLiveRightSiderShowAtom);
  const [rightSiderWidth, setRightSiderWidth] = useState<number | undefined>(undefined);
  const rightSiderRef = useRef<HTMLDivElement>(null);
  const [btnTop, setBtnTop] = useState<number>(80);
  const [isDragging, setIsDragging] = useState(false);
  const startYRef = useRef(0);
  // 是否移动了按钮
  const [isMove, setIsMove] = useState(false);
  const { roomId } = useParams();
  const [rightSiderActiveKey, setRightSiderActiveKey] = useAtom(rightSiderActiveKeyAtom);
  const [selectedLiveFrame, setSelectedLiveFrame] = useAtom(selectedLiveFrameAtom);
  const [selectedLiveSubtitle, setSelectedLiveSubtitle] = useAtom(selectedLiveSubtitleAtom);
  const [framePriorityShowContent, setFramePriorityShowContent] = useAtom(
    framePriorityShowContentAtom,
  );
  const [subtitlesPriorityShowContent, setSubtitlesPriorityShowContent] = useAtom(
    subtitlesPriorityShowContentAtom,
  );
  const [danmuPriorityShowContent, setDanmuPriorityShowContent] = useAtom(
    danmuPriorityShowContentAtom,
  );
  const [tabKey, setTabKey] = useAtom(liveTabKeyAtom);

  // 1-文字标识 2-行为标识 3-弹幕标识
  const { data: subTitleMonitorData } = useQuery({
    queryKey: ['qualityMonitorWordList', projectId, 1],
    queryFn: () => QualityMonitorWordList({ projectId, sceneType: 1 }),
    enabled: !!projectId,
  });

  const { data: frameMonitorData } = useQuery({
    queryKey: ['qualityMonitorWordList', projectId, 2],
    queryFn: () => QualityMonitorWordList({ projectId, sceneType: 2 }),
    enabled: !!projectId,
  });

  const tabItems = [
    {
      label: '画面优先',
      key: 'framePriority',
      children: (
        <TabContainer>
          <PicturePriority
            videoPlayerRef={videoPlayerRef}
            showHit={showHitState}
            targetId={targetId}
            platform={platform}
            targetType={monitoringWordType}
            isLive={isLive}
            liveData={liveData}
            isInQuality={isInQuality}
            showContent={framePriorityShowContent}
            tabKey="framePriority"
          />
        </TabContainer>
      ),
    },
    {
      label: '字幕优先',
      key: 'subtitlesPriority',
      children: (
        <TabContainer>
          <SubtitlePriority
            videoPlayerRef={videoPlayerRef}
            showHit={showHitState}
            targetId={targetId}
            platform={platform}
            targetType={monitoringWordType}
            isLive={isLive}
            liveData={liveData}
            isInQuality={isInQuality}
            showContent={subtitlesPriorityShowContent}
            tabKey="subtitlesPriority"
          />
        </TabContainer>
      ),
    },
    {
      label: '弹幕优先',
      key: 'danmuPriority',
      children: (
        <TabContainer>
          <DanmuPriority
            videoPlayerRef={videoPlayerRef}
            showHit={showHitState}
            targetId={targetId}
            platform={platform}
            targetType={monitoringWordType}
            isLive={isLive}
            liveData={liveData}
            isInQuality={isInQuality}
            showContent={danmuPriorityShowContent}
            tabKey="danmuPriority"
          />
        </TabContainer>
      ),
    },
  ];

  const commonItem = {
    key: 'confirmQuality',
    label: '违规内容',
    children: (
      <QualityDetail
        tabKey={tabKey}
        targetId={roomId}
        targetType={QualityTargetType.LIVE}
        liveStartTime={liveData?.liveStartTime}
        projectId={projectId}
      />
    ),
  };

  const rightSiderItems: TabsProps['items'] =
    projectId === '22' || projectId === '60'
      ? [
          commonItem,
          {
            key: 'price',
            label: '车型报价',
            children: <CarPrice projectId={projectId} />,
          },
        ]
      : [commonItem];

  useEffect(() => {
    const transitionDom = rightSiderRef.current;
    transitionDom?.addEventListener('transitionend', () => {
      setRightSiderWidth(transitionDom.offsetWidth);
    });
  }, [toggleRightSiderShow]);

  const handleMouseDown = (e: any) => {
    setIsDragging(true);
    startYRef.current = e.clientY - btnTop; // 记录鼠标相对于元素顶部的偏移量
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const handleMouseMove = (e: any) => {
    if (isDragging) {
      const newTop = e.clientY - startYRef.current;
      setBtnTop(newTop);
      setIsMove(true);
    }
  };

  return (
    <Flex gap={10} style={{ height: 'calc(100vh - 66px - 56px - 40px - 50px)' }}>
      <HideScrollBarDiv style={{ height: '100%' }}>
        <div
          style={{
            width: '100%',
            alignItems: 'stretch',
            display: 'flex',
            flexDirection: 'column',
            position: 'relative',
            height: '100%',
          }}
        >
          <ProCardDarkGhost
            ghost
            style={{
              height: '100%',
            }}
          >
            <ButtonTabs
              type="card"
              tabBarExtraContent={{
                left: (
                  <LeftTabBarExtraContent
                    activeKey={tabKey}
                    value={
                      tabKey === 'framePriority'
                        ? framePriorityShowContent
                        : tabKey === 'subtitlesPriority'
                          ? subtitlesPriorityShowContent
                          : danmuPriorityShowContent
                    }
                    onChange={(checkedValues) => {
                      // 取消对应的勾选框把对应的上报卡片也消掉
                      if (tabKey === 'framePriority') {
                        setFramePriorityShowContent(checkedValues);
                        if (!checkedValues.includes('subtitle')) {
                          setSelectedLiveSubtitle([]);
                        }
                      } else if (tabKey === 'subtitlesPriority') {
                        setSubtitlesPriorityShowContent(checkedValues);
                        if (!checkedValues.includes('frame')) {
                          setSelectedLiveFrame([]);
                        }
                      } else if (tabKey === 'danmuPriority') {
                        setDanmuPriorityShowContent(checkedValues);
                      }
                    }}
                  />
                ),
                right: (
                  <Switch
                    id="showHitState"
                    checkedChildren="手动标记"
                    unCheckedChildren="手动标记"
                    checked={showHitState}
                    onChange={(checked) => {
                      setSelectedLiveFrame([]);
                      setSelectedLiveSubtitle([]);
                      setShowHitState(checked);
                    }}
                  />
                ),
              }}
              items={tabItems}
              activeKey={tabKey}
              onChange={(activeKey: string) => {
                setTabKey(activeKey);
                // 切换tab把之前的都清空
                setSelectedLiveFrame([]);
                setSelectedLiveSubtitle([]);
              }}
            />
          </ProCardDarkGhost>

          {showHitState && selectedLiveFrame.length > 0 && (
            <SubmitQualityCard
              triggerType={'frame'}
              data={frameMonitorData?.data}
              style={{
                position: 'absolute',
                bottom: '5%',
                left: '50%',
                transform: 'translateX(-50%)',
              }}
              targetType={monitoringWordType}
              platform={platform}
              targetId={targetId}
              selectItems={selectedLiveFrame}
              setSelectItems={setSelectedLiveFrame}
              projectId={projectId}
            />
          )}
          {showHitState && selectedLiveFrame.length === 0 && selectedLiveSubtitle.length > 0 && (
            <SubmitQualityCard
              triggerType={'subtitle'}
              data={subTitleMonitorData?.data}
              targetType={monitoringWordType}
              platform={platform}
              targetId={targetId}
              selectItems={selectedLiveSubtitle}
              setSelectItems={setSelectedLiveSubtitle}
              projectId={projectId}
              style={{
                position: 'absolute',
                bottom: '5%',
                left: '50%',
                transform: 'translateX(-50%)',
              }}
            />
          )}
        </div>
      </HideScrollBarDiv>
      {/* 通过width 0 来隐藏 方便展示收缩展开的动画 */}
      <HideScrollBarDiv
        style={{
          backgroundColor: '#1b1d22',
          width: toggleRightSiderShow ? '400px' : '0px',
          height: '100%',
          transition: 'width 0.3s ease',
        }}
        ref={rightSiderRef}
      >
        <TabsNoMarginBottom
          activeKey={rightSiderActiveKey}
          onChange={(key) => {
            setRightSiderActiveKey(key);
          }}
          items={rightSiderItems}
          tabBarStyle={{
            position: 'sticky',
            top: 0,
            zIndex: 999,
          }}
          tabBarExtraContent={
            <span
              style={{ fontSize: '14px', color: '#fff' }}
              onClick={() => setToggleRightSiderShow(false)}
            >
              收起 <DoubleRightOutlined />
            </span>
          }
        />
      </HideScrollBarDiv>
      {rightSiderWidth === 0 && (
        <ShowRightSiderBtn
          $top={btnTop}
          onMouseDown={handleMouseDown}
          onMouseUp={handleMouseUp}
          onMouseMove={handleMouseMove}
          onClick={() => {
            if (!isMove) {
              setToggleRightSiderShow(true);
            } else {
              setIsMove(false);
            }
          }}
        >
          <div>
            <DoubleLeftOutlined />
            展开
          </div>
        </ShowRightSiderBtn>
      )}
    </Flex>
  );
};

export default VideoAnaly;

interface LeftTabBarExtraContentProps {
  activeKey: string;
  value: string[];
  onChange: (checkedValues: string[]) => void;
}

function LeftTabBarExtraContent(props: LeftTabBarExtraContentProps) {
  const { activeKey, value: showContent, onChange } = props;
  const options = useMemo(() => {
    if (activeKey === 'framePriority') {
      return [
        { label: '字幕', value: 'subtitle' },
        { label: '弹幕', value: 'danmu' },
      ];
    }
    if (activeKey === 'subtitlesPriority') {
      return [
        { label: '画面', value: 'frame' },
        { label: '弹幕', value: 'danmu' },
      ];
    }
    if (activeKey === 'danmuPriority') {
      return [
        { label: '画面', value: 'frame' },
        { label: '字幕', value: 'subtitle' },
      ];
    }
    return [];
  }, [activeKey]);
  return (
    <Space id="showContentCheckbox">
      <label>显示内容</label>
      <Checkbox.Group value={showContent} onChange={onChange} options={options} />
    </Space>
  );
}
