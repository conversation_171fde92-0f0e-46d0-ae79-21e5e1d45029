import { Alert } from 'antd';
import { CloseOutline } from 'antd-mobile-icons';
import { useState } from 'react';

export function HighlightAlert() {
  const [alertShow, setAlertShow] = useState(true);

  return (
    <Alert
      style={{ margin: '10px 0', visibility: alertShow ? 'visible' : 'hidden' }}
      action={
        <CloseOutline
          onClick={() => {
            setAlertShow(false);
          }}
        />
      }
      message={
        <span>
          字幕车型为<span style={{ color: '#45B1FF' }}>蓝色</span>；字幕价格为
          <span style={{ color: '#B32815' }}>红色</span>；字幕内容（包括标题）命中疑似词语为
          <span style={{ color: '#F0B114' }}>黄色</span>
          ；弹幕命中疑似词的内容为<span style={{ color: '#DA6918' }}>橙色</span>
        </span>
      }
      type="info"
      showIcon
    />
  );
}
