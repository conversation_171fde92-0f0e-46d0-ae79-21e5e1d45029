import FallBackImg from '@/assets/fallback-img.png';
import { useDeleteExInfoMutation } from '@/hooks/request/use-quality-ex';
import { QualityTargetType } from '@/services/constants';
import { GetAnchorImageViolationList, GetQualityExList, QualityItem } from '@/services/quality';
import {
  AimOutlined,
  DeleteOutlined,
  RotateLeftOutlined,
  RotateRightOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
} from '@ant-design/icons';
import { useParams, useQuery, useQueryClient } from '@umijs/max';
import { EventEmitter } from 'ahooks/lib/useEventEmitter';
import {
  App,
  Badge,
  ConfigProvider,
  Empty,
  Flex,
  Image,
  ImageProps,
  Popconfirm,
  Space,
  theme,
} from 'antd';
import dayjs from 'dayjs';
import { groupBy } from 'lodash-es';
import styled from 'styled-components';
import { durationToTime } from './PicturePriority';
import type { GetProp } from 'antd';

type ImagePreviewType = GetProp<ImageProps, 'preview'>;

const ImageToolBar = styled.div`
  background: rgba(255, 255, 255, 0.2);
  -webkit-backdrop-filter: blur(8px);
  backdrop-filter: blur(8px);
  box-shadow: inset 0 0 6px rgba(255, 255, 255, 0.2);
  width: 100%;
  position: absolute;
  bottom: 0;
  border-radius: 0px 0px 5px 5px;
  padding: 3px 5px;
`;

const FlexContainer = styled(Flex)`
  width: 30%;
  height: 150px;
  justify-content: center;
  align-items: center;
`;

const QualityImage = styled(Image)`
  height: 120px !important;
  width: 90px !important;
  object-fit: cover;
  border-radius: 5px !important;
`;

type QualityDetailProps = {
  targetId?: string;
  targetType: QualityTargetType;
  liveStartTime?: string;
  postEvent$?: EventEmitter<void>;
  tabKey?: string; // 需要知道当前在哪个Tab下
  projectId?: string;
};

const sortExDetailByTime = (exDetail: QualityItem[], liveStartTime?: string) => {
  return exDetail.sort((a: any, b: any) => {
    // 开播时间加时长得出绝对时间再排序
    const timeA = dayjs(liveStartTime).add(a.startTime, 'second');
    const timeB = dayjs(liveStartTime).add(b.startTime, 'second');

    // 比较时间
    if (timeA.isBefore(timeB)) {
      return -1;
    } else if (timeA.isAfter(timeB)) {
      return 1;
    } else {
      return 0;
    }
  });
};

const sortExDetailByStartTime = (exDetail: QualityItem[]) => {
  return exDetail.sort((a: any, b: any) => a.startTime - b.startTime);
};

const QualityDetail = (props: QualityDetailProps) => {
  const { message } = App.useApp();
  const { platform } = useParams();
  const { targetId, targetType, liveStartTime, postEvent$, tabKey, projectId } = props;
  const queryClient = useQueryClient();

  const { data: qualityExList } = useQuery({
    queryFn: () => GetQualityExList({ targetId, targetType, platform }),
    queryKey: ['qualityDetail', targetId, targetType, platform],
  });

  // 主播形象违规
  const { data: anchorImageViolationList } = useQuery({
    queryFn: () => GetAnchorImageViolationList({ roomId: targetId, projectId, platform }),
    queryKey: ['anchorImageViolationList', targetId, projectId, platform],
    enabled: targetType === QualityTargetType.LIVE && !!projectId,
  });

  const showAnchorImageViolation = anchorImageViolationList?.data;
  const groupByNameQualityExData = groupBy(qualityExList?.data, 'monitoringWordName');
  const deleteExInfoMutation = useDeleteExInfoMutation(targetType);
  const showExData = Object.entries(groupByNameQualityExData).map(([title, exDetail]) => ({
    title,
    exDetail:
      targetType === QualityTargetType.LIVE
        ? sortExDetailByTime(exDetail, liveStartTime)
        : sortExDetailByStartTime(exDetail),
  }));

  const handleScrollAim = (item: Pick<QualityItem, 'qualityRuleType' | 'startTime' | 'mark'>) => {
    // 切换tab还会存留上一个tab内容的dom 所以根据tabkey区分
    const frameDom = document.getElementById(`frame-${tabKey}-${item.startTime}`);
    const subtitleDom = document.getElementById(`subtitle-${tabKey}-${item.startTime}`);
    if (frameDom && item.qualityRuleType === 4) {
      frameDom.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
      });
      // 在滚动到位后添加闪烁效果
      frameDom.classList.add('flash-animation');
      setTimeout(() => {
        frameDom.classList.remove('flash-animation');
      }, 3500);
    } else if (subtitleDom && item.qualityRuleType === 2) {
      subtitleDom.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
      });
      // 在滚动到位后添加闪烁效果
      subtitleDom.classList.add('flash-animation');
      setTimeout(() => {
        subtitleDom.classList.remove('flash-animation');
      }, 3500);

      // 如果存在 mark 属性，则匹配并添加高亮样式
      if (item.mark) {
        const markText = item.mark.trim(); // 假设 mark 是一个字符串
        const originalInnerHTML = subtitleDom.innerHTML;
        const originalContentText = subtitleDom.textContent || '';
        const regex = new RegExp(markText, 'gi'); // 使用全局不区分大小写的正则表达式
        const highlightedHTML = originalContentText.replace(
          regex,
          `<span class="mark-highlight">${markText}</span>`,
        );
        subtitleDom.innerHTML = highlightedHTML;

        setTimeout(() => {
          subtitleDom.innerHTML = originalInnerHTML; // 还原原始 innerHTML
        }, 3500);
      }
    } else if (item.qualityRuleType === 1) {
      message.error(`标题存在违规内容 ${item.mark}`);
    } else if (item.qualityRuleType === 3) {
      message.error('封面违规');
    } else {
      message.error('当前定位内容未展示');
    }
  };

  const imagePreviewConfig: ImagePreviewType = {
    toolbarRender: (
      _,
      { transform: { scale }, actions: { onRotateLeft, onRotateRight, onZoomOut, onZoomIn } },
    ) => (
      <Space size={12} className="toolbar-wrapper">
        <RotateLeftOutlined onClick={onRotateLeft} />
        <RotateRightOutlined onClick={onRotateRight} />
        <ZoomOutOutlined disabled={scale === 1} onClick={onZoomOut} />
        <ZoomInOutlined disabled={scale === 50} onClick={onZoomIn} />
      </Space>
    ),
    rootClassName: 'quality-imgepreview',
  };

  return (
    <>
      {showExData.length === 0 && showAnchorImageViolation?.length === 0 ? (
        <ConfigProvider theme={{ algorithm: theme.darkAlgorithm }}>
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description={<span>暂无数据</span>}
            style={{ paddingTop: '50px' }}
          />
        </ConfigProvider>
      ) : (
        <>
          {showExData.map((item, index) => {
            return (
              <div
                style={{ paddingInline: '10px', marginBlockEnd: '10px' }}
                key={`quality-type-${index}`}
              >
                <div style={{ color: '#fff', paddingBlock: '10px' }}>{item.title}</div>
                <Flex justify="flex-start" wrap="wrap" gap={10}>
                  {item.exDetail.map((item, index) => (
                    <FlexContainer vertical key={`quality-img-${index}`}>
                      <div style={{ position: 'relative', borderRadius: '5px' }}>
                        {item.picUrl && item.picUrl?.length > 1 ? (
                          <Image.PreviewGroup items={item.picUrl} preview={imagePreviewConfig}>
                            <Badge.Ribbon text={`X${item.picUrl.length}`} color="blue">
                              <QualityImage src={item.picUrl[0]} />
                            </Badge.Ribbon>
                          </Image.PreviewGroup>
                        ) : (
                          <QualityImage
                            src={item.picUrl?.[0]}
                            fallback={FallBackImg}
                            rootClassName="quality-imgepreview"
                            preview={{
                              toolbarRender: (
                                _,
                                {
                                  transform: { scale },
                                  actions: { onRotateLeft, onRotateRight, onZoomOut, onZoomIn },
                                },
                              ) => (
                                <Space size={12} className="toolbar-wrapper">
                                  <RotateLeftOutlined onClick={onRotateLeft} />
                                  <RotateRightOutlined onClick={onRotateRight} />
                                  <ZoomOutOutlined disabled={scale === 1} onClick={onZoomOut} />
                                  <ZoomInOutlined disabled={scale === 50} onClick={onZoomIn} />
                                </Space>
                              ),
                            }}
                          />
                        )}
                        <ImageToolBar>
                          <Flex style={{ float: 'right', color: '#fff' }} gap={5}>
                            <AimOutlined onClick={() => handleScrollAim(item)} />
                            <Popconfirm
                              title={'是否确认删除'}
                              onConfirm={async () => {
                                deleteExInfoMutation.mutate(item);
                                if (targetType === QualityTargetType.POST) {
                                  postEvent$?.emit();
                                  queryClient.invalidateQueries({
                                    queryKey: [`qualityDetail`],
                                  });
                                }
                              }}
                              key={'delete'}
                            >
                              <DeleteOutlined />
                            </Popconfirm>
                          </Flex>
                        </ImageToolBar>
                      </div>

                      <div style={{ float: 'left', width: '100%', overflow: 'hidden' }}>
                        {targetType === QualityTargetType.LIVE ? (
                          <>
                            {dayjs(liveStartTime).add(item.startTime, 'second').format('HH:mm:ss')}
                          </>
                        ) : (
                          <>{durationToTime(item.startTime || 0)}</>
                        )}
                      </div>
                    </FlexContainer>
                  ))}
                </Flex>
              </div>
            );
          })}
          {/* 主播形象违规的内容 */}
          {showAnchorImageViolation?.map((item, index) => {
            return (
              <div
                style={{ paddingInline: '10px', marginBlockEnd: '10px' }}
                key={`quality-anchor-image-group-${index}`}
              >
                <div style={{ color: '#fff', paddingBlock: '10px' }}>{item.name}</div>
                <FlexContainer vertical key={`quality-anchor-image-${index}`}>
                  <div style={{ position: 'relative', borderRadius: '5px' }}>
                    <QualityImage
                      src={item.picUrl}
                      fallback={FallBackImg}
                      rootClassName="quality-imgepreview"
                      preview={imagePreviewConfig}
                    />
                    <ImageToolBar>
                      <Flex style={{ float: 'right', color: '#fff' }} gap={5}>
                        <AimOutlined
                          onClick={() => handleScrollAim({ ...item, qualityRuleType: 4 })}
                        />
                      </Flex>
                    </ImageToolBar>
                  </div>
                  <div style={{ float: 'left', width: '100%', overflow: 'hidden' }}>
                    {dayjs(liveStartTime).add(item.startTime, 'second').format('HH:mm:ss')}
                  </div>
                </FlexContainer>
              </div>
            );
          })}
          {/* 折扣报价违规 被要求删掉了 */}
          {/* {showQualityDiscount?.map((item, index) => {
            return (
              <div
                style={{ paddingInline: '10px', marginBlockEnd: '10px' }}
                key={`quality-discount-group-${index}`}
              >
                <div style={{ color: '#fff', paddingBlock: '10px' }}>折扣误导</div>
                <FlexContainer vertical key={`quality-discount-image-${index}`}>
                  <div style={{ position: 'relative', borderRadius: '5px' }}>
                    <QualityImage
                      src={item.picUrl}
                      fallback={FallBackImg}
                      rootClassName="quality-imgepreview"
                      preview={imagePreviewConfig}
                    />
                    <ImageToolBar>
                      <Flex style={{ float: 'right', color: '#fff' }} gap={5}>
                        <AimOutlined
                          onClick={() =>
                            handleScrollAim({
                              mark: item.mark,
                              qualityRuleType: item.type,
                              startTime: item.startTime,
                            })
                          }
                        />
                      </Flex>
                    </ImageToolBar>
                  </div>
                  <div style={{ float: 'left', width: '100%', overflow: 'hidden' }}>
                    {dayjs(liveStartTime).add(item.startTime, 'second').format('HH:mm:ss')}
                  </div>
                </FlexContainer>
              </div>
            );
          })} */}
        </>
      )}
    </>
  );
};

export default QualityDetail;
