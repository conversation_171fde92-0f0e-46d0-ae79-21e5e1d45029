import Logo from '@/assets/logo.png';
import { SvgIcon } from '@/components/SvgIcon';
import {
  GetLiveEcommerceHighLight,
  GetLiveHighLight,
  QualityLiveEcommerceHighLight,
  QualityLiveHighLight,
} from '@/services/quality';
import { IndustryType } from '@/utils/const';
import { useParams } from '@umijs/max';
import { useAsyncEffect } from 'ahooks';
import { Button, ConfigProvider, Empty, Flex, Image, Popover, theme, Typography } from 'antd';
import dayjs from 'dayjs';
import { useState } from 'react';
import { DanmuSpan } from './PicturePriority';
import { replaceDouyinEmoji } from '@/utils/douyinEmoji';

const HighLightShow = () => {
  const { roomId, platform, industryType } = useParams();
  const [highLightData, setHighLightData] = useState<QualityLiveHighLight[]>([]);
  const [highLightEcommerceData, setHighLightEcommerceData] =
    useState<QualityLiveEcommerceHighLight>();
  const [expanded, setExpanded] = useState(false);
  const [imagesExpanded, setImagesExpanded] = useState(false);

  useAsyncEffect(async () => {
    if (industryType) {
      if (Number(industryType) === IndustryType.CAR) {
        const res = await GetLiveHighLight({ roomId, platform });
        const data = res.data;
        setHighLightData(data || []);
      } else {
        const res = await GetLiveEcommerceHighLight({ roomId, platform });
        const data = res.data;
        if (data) {
          data.dmList = data.dmList?.map((item) => replaceDouyinEmoji(item)) || [];
          setHighLightEcommerceData(data);
        }
      }
    }
  }, [industryType]);

  const isDataClean = highLightData.length > 0 && highLightData[0].cleanFlag === 1;

  if (
    (Number(industryType) === IndustryType.CAR && highLightData.length === 0) ||
    !highLightEcommerceData
  ) {
    return (
      <ConfigProvider theme={{ algorithm: theme.darkAlgorithm }}>
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description={<span>暂无数据</span>}
          className="pt-12"
        />
      </ConfigProvider>
    );
  }

  if (Number(industryType) === IndustryType.CAR && isDataClean) {
    return (
      <ConfigProvider theme={{ algorithm: theme.darkAlgorithm }}>
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description={<span>视频超过存储时长，已清理</span>}
          className="pt-12"
        />
      </ConfigProvider>
    );
  }

  return (
    <div className="no-scrollbar flex h-auto min-h-[350px] flex-col gap-2.5 overflow-y-auto">
      {Number(industryType) === IndustryType.CAR ? (
        <div>
          {highLightData.map((item) => (
            <div
              className="relative mb-3 flex flex-row rounded p-2.5 text-white/80"
              key={item.videoUrl}
            >
              <Flex
                justify="center"
                align="center"
                className="relative cursor-pointer px-2.5 py-0.5"
                onClick={() => window.open(`${item.videoUrl}`)}
              >
                <Image
                  width={129}
                  height={193}
                  preview={false}
                  className="rounded object-cover"
                  alt="直播封面"
                  src={item.cover || Logo}
                />
                <div className="absolute">
                  <SvgIcon icon="local:outline/play" />
                </div>
              </Flex>
              <div className="ml-2.5 flex-1 space-y-1">
                <div>
                  <span className="text-white/50">高光时间:&nbsp;</span>
                  {dayjs(item.startTime.split(' ')[0]).format('MM-DD')} &nbsp;
                  {item.startTime.split(' ')[1]}-{item.endTime.split(' ')[1]}
                </div>
                <div>
                  <span className="text-white/50">在线人数: </span>
                  {item.onlineNum}
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="relative mb-3 flex flex-row items-start rounded-md bg-[#1A1D23] text-white/80">
          <div className="flex content-start justify-start">
            <Flex
              justify="center"
              align="center"
              className="cursor-pointer py-0.5 pr-2.5"
              onClick={() => window.open(`${highLightEcommerceData.highlightVideoUrl}`)}
            >
              <Image
                width={129}
                height={193}
                preview={false}
                className="rounded object-cover"
                alt="高光片段截图"
                src={highLightEcommerceData.highlightScreenshots?.[0] || Logo}
              />
              <div className="absolute">
                <SvgIcon icon="local:outline/play" />
              </div>
            </Flex>
          </div>
          <div className="ml-2.5 flex-1 space-y-2">
            <div>
              <span className="text-white/50">高光时间:&nbsp;</span>
              {highLightEcommerceData.peakTime
                ? dayjs(highLightEcommerceData.peakTime).format('MM-DD HH:mm:ss')
                : '--'}
            </div>
            <div>
              <span className="text-white/50">成交金额:&nbsp;</span>
              {highLightEcommerceData.peakGmv ? `${highLightEcommerceData.peakGmv}` : '--'}
            </div>
            <div className="flex w-full">
              <span className="w-16 flex-shrink-0 self-start text-white/50">高光画面:&nbsp;</span>
              <div className="flex flex-1 flex-col">
                <div className="flex flex-wrap">
                  {highLightEcommerceData.highlightScreenshots
                    ?.slice(0, imagesExpanded ? undefined : 10)
                    .map((imgUrl, index) => (
                      <Popover
                        key={`highlight-screenshot-${index}`}
                        destroyOnHidden
                        placement="left"
                        content={
                          <img
                            src={imgUrl}
                            width={300}
                            className="object-cover"
                            alt={`highlight-screenshot-${index}`}
                          />
                        }
                      >
                        <img
                          src={imgUrl}
                          width={72}
                          className="mb-2 mr-2 object-cover"
                          alt={`highlight-screenshot-${index}`}
                          loading="lazy"
                        />
                      </Popover>
                    ))}
                </div>
                {highLightEcommerceData.highlightScreenshots &&
                  highLightEcommerceData.highlightScreenshots.length > 4 && (
                    <Button
                      type="link"
                      size="small"
                      className="self-start p-0 text-white/60 hover:text-white/80"
                      onClick={() => setImagesExpanded(!imagesExpanded)}
                    >
                      {imagesExpanded
                        ? '收起'
                        : `展开更多 (${highLightEcommerceData.highlightScreenshots.length - 4})`}
                    </Button>
                  )}
              </div>
            </div>
            {highLightEcommerceData.highlightSubtitle && (
              <div className="flex text-sm text-white/80">
                <div className="w-16 flex-shrink-0 self-start text-white/50">高光文案:&nbsp;</div>
                <div className="flex-1">
                  <Typography.Paragraph
                    className="!mb-0"
                    style={{
                      color: 'rgba(255, 255, 255, 0.8)',
                      width: '100%',
                    }}
                    ellipsis={{
                      rows: 2,
                      expandable: 'collapsible',
                      expanded,
                      onExpand: (_, info) => setExpanded(info.expanded),
                    }}
                  >
                    {highLightEcommerceData.highlightSubtitle}
                  </Typography.Paragraph>
                </div>
              </div>
            )}
            <div className="flex w-full">
              <span className="w-16 flex-shrink-0 self-start text-white/50">高光弹幕:&nbsp;</span>
              <div className="flex flex-1 flex-wrap gap-1">
                {highLightEcommerceData.dmList?.map((item, index) => (
                  <DanmuSpan key={`${item}-${index}`}>
                    <span
                      dangerouslySetInnerHTML={{
                        __html: item,
                      }}
                    />
                  </DanmuSpan>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default HighLightShow;
