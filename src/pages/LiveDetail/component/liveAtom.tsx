import { findLocalStorageValue } from '@/utils/quality';
import { atom } from 'jotai';
import { VideoSubtitleItem } from './types';

export type HitExTime = {
  startTime: number;
  endTime: number;
  targetUrl?: string;
  picUrl?: string;
  index?: number;
  groupId?: string;
  monitoringWordName?: string;
};

export const selectedLiveFrameAtom = atom<HitExTime[]>([]);
export const selectedLiveSubtitleAtom = atom<VideoSubtitleItem[]>([]);
// 根据localStorage存储的信息恢复页面
export const rightSiderActiveKeyAtom = atom(
  findLocalStorageValue<string>('liveDetailRecords', 'rightSiderActiveKey') || 'confirmQuality',
);
export const liveTabKeyAtom = atom(
  findLocalStorageValue<string>('liveDetailRecords', 'tabKey') || 'framePriority',
);
export const showLiveHitStateAtom = atom(
  findLocalStorageValue<boolean>('liveDetailRecords', 'showHitState') || false,
);
export const toggleLiveRightSiderShowAtom = atom(
  findLocalStorageValue<boolean>('liveDetailRecords', 'toggleRightSiderShow') ?? true,
);

export const framePriorityShowContentAtom = atom<string[]>(
  findLocalStorageValue<string[]>('liveDetailRecords', 'framePriorityShowContent') || [
    'subtitle',
    'danmu',
  ],
);

export const subtitlesPriorityShowContentAtom = atom<string[]>(
  findLocalStorageValue<string[]>('liveDetailRecords', 'subtitlesPriorityShowContent') || [
    'frame',
    'danmu',
  ],
);

export const danmuPriorityShowContentAtom = atom<string[]>(
  findLocalStorageValue<string[]>('liveDetailRecords', 'danmuPriorityShowContent') || [
    'frame',
    'subtitle',
  ],
);
