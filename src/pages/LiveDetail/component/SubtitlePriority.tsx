import { Timeline } from '@/components/antd/timeline';
import { DanmuItem } from '@/components/commentShow';

import { useLiveDanmu } from '@/hooks/request/use-danmu';
import { useLiveFrame } from '@/hooks/request/use-live-frame';
import { useLiveSubtitle } from '@/hooks/request/use-live-subtitle';
import { QualityTargetType } from '@/services/constants';
import { LiveQualityItem, LiveScreenItem } from '@/services/quality';
import { HideScrollBarDiv } from '@/utils/commonStyle';

import { Empty, Input, Popover, Space, Spin } from 'antd';
import dayjs from 'dayjs';
import duration from 'dayjs/plugin/duration';
import { useAtom } from 'jotai';
import { debounce, isEmpty } from 'lodash-es';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import Player from 'xgplayer';
import {
  DanmuSpan,
  FrameBlendMode,
  ImgByFlash,
  SbuTitleContainer,
  SubTitleSpan,
  frameInfo,
} from './PicturePriority';
import { DownOutlined, UpOutlined } from '@ant-design/icons';
import { VideoSubtitleItem } from './types';
import { selectedLiveFrameAtom, selectedLiveSubtitleAtom } from './liveAtom';

dayjs.extend(duration);

function durationToTime(duration: number) {
  return dayjs.duration(duration, 'seconds').format('HH:mm:ss');
}

type FrameAnalyProps = {
  liveData?: LiveQualityItem;
  videoPlayerRef: React.MutableRefObject<Player | null>;
  showHit: boolean;
  targetId?: string;
  platform?: string;
  targetType: QualityTargetType;
  isLive?: boolean;
  isInQuality?: boolean;
  showContent: string[];
  tabKey?: string;
};

export const SubtitlePriority = (props: FrameAnalyProps) => {
  const { videoPlayerRef, showContent, showHit, targetId, platform, liveData, tabKey } = props;
  const [searchValue, setSearchValue] = useState('');

  const [selectedLiveFrame, setSelectedLiveFrame] = useAtom(selectedLiveFrameAtom);
  const [selectSubtitles, setSelectSubtitles] = useAtom(selectedLiveSubtitleAtom);

  const rightSiderRef = useRef<HTMLDivElement | null>(null);
  const frameFilter = useRef<string[]>([]);
  const [_, setCurrentHighlightIndex] = useState(0);

  const [originLiveFrameData, setOriginLiveFrameData] = useState<
    {
      range: { startTime: number; endTime: number };
      frameData: LiveScreenItem[];
      danmuList: DanmuItem[];
      subtitles: VideoSubtitleItem[];
    }[]
  >([]);
  const [hoverRange, setHoverRange] = useState<{ startTime: number; endTime: number } | null>(null);
  const [highlightLiveFrameData, setHighlightLiveFrameData] = useState<
    {
      range: { startTime: number; endTime: number };
      frameData: LiveScreenItem[];
      danmuList: DanmuItem[];
      subtitles: VideoSubtitleItem[];
    }[]
  >([]);

  const liveFrameData = useMemo(() => {
    if (!isEmpty(searchValue)) {
      return highlightLiveFrameData;
    } else {
      return originLiveFrameData;
    }
  }, [searchValue, highlightLiveFrameData, originLiveFrameData]);

  const { data: liveScreen } = useLiveFrame({
    roomId: targetId,
    platform,
  });

  const { data: subtitles } = useLiveSubtitle({
    roomId: targetId,
    platform,
  });

  const { data: danmuList } = useLiveDanmu({
    roomId: targetId,
    platform,
  });

  const updateLiveFrameData = debounce(() => {
    frameFilter.current = [];

    const renderSubtitlesData = subtitles?.map((subtitle) => {
      const itemDanmuList = danmuList?.filter(
        (danmu) =>
          Number(danmu.startTime) >= Number(subtitle.startTime) &&
          Number(danmu.startTime) <= Number(subtitle.endTime),
      );
      const result = itemDanmuList?.reduce(
        (
          acc: DanmuItem[],
          curr: DanmuItem & {
            count?: number;
          },
        ) => {
          const existingItem = acc.find((item) => item.content === curr.content);
          if (existingItem) {
            //@ts-ignore
            existingItem.count++;
          } else {
            acc.push({ ...curr, count: 1 });
          }
          return acc;
        },
        [],
      );
      const frames = liveScreen?.filter((frame, index) => {
        const haveBeenSelected = frameFilter.current.some((item) => item === frame.url);
        const frameStartTime = liveScreen.findIndex((item) => item.url === frame.url) * 10;
        frame.index = index;
        if (
          Number(frameStartTime) >= Number(subtitle.startTime) &&
          Number(frameStartTime) < Number(subtitle.endTime) &&
          !haveBeenSelected
        ) {
          frameFilter.current = frameFilter.current.concat(frame.url as string);
          return true;
        } else {
          return false;
        }
      });

      return {
        range: {
          startTime: subtitle.startTime,
          endTime: subtitle.endTime,
        },
        danmuList: result,
        frameData: frames,
        subtitles: [subtitle],
      };
    });

    // @ts-ignore
    setOriginLiveFrameData(renderSubtitlesData);
  }, 300);

  useEffect(() => {
    updateLiveFrameData();
  }, [liveScreen, subtitles, danmuList]);

  const liveFrameClick = (item: LiveScreenItem, startTime: number, index?: number) => {
    if (showHit) {
      const isSelect = selectedLiveFrame.some((selectedItem) => selectedItem.picUrl === item.url);
      if (isSelect) {
        setSelectedLiveFrame(
          selectedLiveFrame.filter((selectedItem) => selectedItem.picUrl !== item.url),
        );
      } else {
        setSelectedLiveFrame([
          ...selectedLiveFrame,
          {
            startTime: startTime,
            endTime: startTime + 10,
            picUrl: item.url,
            index,
          },
        ]);
      }
    } else {
      if (videoPlayerRef.current) {
        videoPlayerRef.current.currentTime = startTime;
        if (videoPlayerRef.current.paused) {
          videoPlayerRef.current.play();
        }
      }
    }
  };

  function handleSubtitleHover(subtitleItem: VideoSubtitleItem) {
    setHoverRange({
      startTime: subtitleItem.startTime,
      endTime: subtitleItem.endTime,
    });
  }

  function handleSubtitleHoverLeave() {
    setHoverRange(null);
  }
  function handleSubtitleClick(subtitleItem: VideoSubtitleItem) {
    if (!showHit) return;
    const isSelect = selectSubtitles.some((selectedItem) => selectedItem.id === subtitleItem.id);
    if (isSelect) {
      setSelectSubtitles(
        selectSubtitles.filter((selectedItem) => selectedItem.id !== subtitleItem.id),
      );
    } else {
      setSelectSubtitles([
        {
          ...subtitleItem,
        },
      ]);
    }
  }
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const _searchValue = e.target.value;
    setSearchValue(_searchValue);
    if (!_searchValue) {
      return;
    }

    const newLiveFrameData = originLiveFrameData?.map((oneLineLiveFrame) => {
      const newOneLineLiveFrame = oneLineLiveFrame.subtitles?.map((subtitleItem) => {
        if (subtitleItem && subtitleItem.subtitle) {
          const searchRegex = new RegExp(_searchValue, 'gi');
          let content = subtitleItem.subtitle;
          content = content.replace(searchRegex, '<span class="highlight">$&</span>');

          return {
            ...subtitleItem,
            subtitle: content,
          };
        }
        return {
          ...subtitleItem,
        };
      });
      return {
        ...oneLineLiveFrame,
        subtitles: newOneLineLiveFrame,
      };
    });
    setHighlightLiveFrameData([...newLiveFrameData]);
  };

  if (isEmpty(subtitles)) {
    return <Empty description="直播间没有字幕" />;
  }

  const scrollToHighlightedText = (direction: 'next' | 'previous') => {
    const highlightedElements = document.querySelectorAll('.highlight');
    if (highlightedElements.length > 0) {
      setCurrentHighlightIndex((prevIndex) => {
        const newIndex =
          direction === 'next'
            ? (prevIndex + 1) % highlightedElements.length
            : (prevIndex - 1 + highlightedElements.length) % highlightedElements.length;
        highlightedElements[newIndex].scrollIntoView({
          behavior: 'smooth',
          block: 'center',
        });
        return newIndex;
      });
    }
  };

  return (
    <div
      style={{
        width: '100%',
        height: 'max-content',
      }}
    >
      <Input.Search
        placeholder="输入关键词过滤"
        onChange={handleSearch}
        value={searchValue}
        allowClear
        style={{ flex: 1, maxWidth: '95%' }}
        suffix={
          searchValue && (
            <Space>
              <DownOutlined onClick={() => scrollToHighlightedText('next')} />
              <UpOutlined onClick={() => scrollToHighlightedText('previous')} />
            </Space>
          )
        }
      />
      <HideScrollBarDiv
        id="scrollableDiv"
        ref={rightSiderRef}
        style={{
          width: '100%',

          height: 'calc(100vh - 56px - 300px)',
          justifyContent: 'center',
          alignItems: 'center',
        }}
      >
        <div
          style={{
            paddingTop: '10px',
          }}
        >
          {!isEmpty(liveFrameData) ? (
            <Timeline
              items={liveFrameData?.map((oneLineLiveFrame, idx) => {
                return {
                  children: (
                    <div key={`timeLineItem-${idx}`}>
                      <Space>
                        <span>
                          {dayjs(liveData?.liveStartTime)
                            .add(oneLineLiveFrame.subtitles[0].startTime, 'second')
                            .format('HH:mm:ss')}
                        </span>
                        {liveData?.liveStatus !== 0 && (
                          <span style={{ color: '#8c8e91' }}>
                            （{durationToTime(oneLineLiveFrame.subtitles[0].startTime)}）
                          </span>
                        )}
                      </Space>
                      <div>
                        {oneLineLiveFrame.subtitles.map((subtitleItem) => {
                          const subtitleIsSelected = selectSubtitles.some((subtitle) => {
                            return subtitle.id === subtitleItem.id;
                          });
                          return (
                            <SbuTitleContainer
                              $showHit={showHit || subtitleIsSelected}
                              $subtitleIsSelected={subtitleIsSelected}
                              $exInfo={!isEmpty(subtitleItem.exInfo)}
                              key={subtitleItem.id}
                              onMouseEnter={() => {
                                handleSubtitleHover(subtitleItem);
                              }}
                              onMouseLeave={() => {
                                handleSubtitleHoverLeave();
                              }}
                              onClick={() => {
                                handleSubtitleClick(subtitleItem);
                              }}
                            >
                              <SubTitleSpan
                                dangerouslySetInnerHTML={{
                                  __html: subtitleItem.subtitle || '',
                                }}
                                key={subtitleItem.id}
                                id={`subtitle-${tabKey}-${subtitleItem.startTime}`}
                              />
                            </SbuTitleContainer>
                          );
                        })}
                      </div>
                      <div>
                        {showContent.includes('frame') &&
                          oneLineLiveFrame?.frameData?.map((frame) => {
                            const currentFrameSeconds =
                              // @ts-ignore
                              liveScreen?.findIndex((live) => live.url === frame.url) * 10;
                            const frameStartTime = currentFrameSeconds;

                            const isSelected = selectedLiveFrame.some(
                              (item) => item.picUrl === frame.url,
                            );
                            const isSelectedBySubtitle = selectSubtitles.some((subtitle) => {
                              return (
                                Number(frameStartTime) >= Number(subtitle.startTime) &&
                                Number(frameStartTime) <= Number(subtitle.endTime)
                              );
                            });
                            const width = frameInfo.width / 2;
                            const height = frameInfo.height / 2;
                            const videoWidth = 1080;
                            const maxWidth = 400 - 40 - 20;
                            const videoHeight = (1920 * maxWidth) / videoWidth;

                            const isHoveredBySubtitle =
                              hoverRange?.startTime &&
                              hoverRange?.endTime &&
                              currentFrameSeconds >= hoverRange.startTime &&
                              currentFrameSeconds < hoverRange.endTime;
                            return (
                              <React.Fragment key={frame.url}>
                                <Popover
                                  placement="left"
                                  content={
                                    !showHit ? null : (
                                      <div>
                                        <img
                                          src={frame.url}
                                          style={{
                                            width: maxWidth,
                                            height: videoHeight,
                                          }}
                                        />
                                        <div>
                                          {dayjs(liveData?.liveStartTime)
                                            .add(currentFrameSeconds, 'second')
                                            .format('HH:mm:ss')}
                                          {liveData?.liveStatus !== 0 && (
                                            <>（{durationToTime(currentFrameSeconds)}）</>
                                          )}
                                        </div>
                                      </div>
                                    )
                                  }
                                  title={null}
                                >
                                  <div
                                    style={{
                                      width,
                                      height,
                                      display: 'inline-block',
                                      position: 'relative',
                                      cursor: showHit ? 'pointer' : 'default',
                                    }}
                                    onClick={() =>
                                      liveFrameClick(frame, currentFrameSeconds, frame.index)
                                    }
                                  >
                                    <ImgByFlash
                                      src={`${frame.url}?imageMogr2/thumbnail/!${frameInfo.width}x${frameInfo.height}r`}
                                      loading="lazy"
                                      id={`frame-${tabKey}-${frameStartTime}`}
                                    />
                                    {showHit && (isSelected || isSelectedBySubtitle) && (
                                      <FrameBlendMode
                                        style={{
                                          boxShadow: 'inset 0 0 19.3px 1px rgba(55, 112, 255, 0.6)',
                                        }}
                                      />
                                    )}
                                    {isHoveredBySubtitle && (
                                      <FrameBlendMode
                                        style={{
                                          boxShadow: 'inset 0 0 19.3px 1px rgba(55, 112, 255, 0.6)',
                                        }}
                                      />
                                    )}
                                    {showHit && !isEmpty(frame.exInfo) && (
                                      <FrameBlendMode
                                        style={{
                                          background: 'rgba(179,40,21, 0.3)',
                                          border: '1px solid #B32815',
                                          bottom: 0,
                                        }}
                                      >
                                        <span className="ex-info">
                                          {frame.exInfo.monitoringWordName}
                                        </span>
                                      </FrameBlendMode>
                                    )}
                                  </div>
                                </Popover>
                              </React.Fragment>
                            );
                          })}
                      </div>

                      <div>
                        {showContent.includes('danmu') && (
                          <Space
                            size={[8, 8]}
                            wrap
                            style={{
                              marginTop: '5px',
                            }}
                          >
                            {oneLineLiveFrame.danmuList?.map((danmuItem) => {
                              return (
                                <DanmuSpan key={danmuItem.id}>
                                  <span
                                    dangerouslySetInnerHTML={{
                                      __html: danmuItem.content,
                                    }}
                                  />
                                  {danmuItem.count && danmuItem.count > 1 && (
                                    <span className="count">X{danmuItem.count}</span>
                                  )}
                                </DanmuSpan>
                              );
                            })}
                          </Space>
                        )}
                      </div>
                    </div>
                  ),
                };
              })}
            />
          ) : (
            <Spin tip="数据加载中...">
              <div
                style={{
                  width: '100%',
                  height: '100px',
                  textAlign: 'center',
                }}
              />
            </Spin>
          )}
        </div>
      </HideScrollBarDiv>
    </div>
  );
};
