import LiveDuring from '@/assets/svg/live-during.svg';
import LiveEnd from '@/assets/svg/live-end.svg';
import QualityDuring from '@/assets/svg/quality-during.svg';
import QualityEnd from '@/assets/svg/quality-end.svg';
import { LiveQualityEcommerceItem, LiveQualityItem } from '@/services/quality';
import { formatNum } from '@/utils/common';
import { formatSecond } from '@/utils/time';
import { UserOutlined } from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';
import { Avatar } from 'antd';
import styles from './infocard.module.less';

type InfoCardProps = {
  liveData?: LiveQualityItem | LiveQualityEcommerceItem;
};

const InfoCard = (props: InfoCardProps) => {
  const { liveData } = props;
  const bizTypeValue = liveData?.teamFieldList?.find((item) => item.bizType === 1);

  return (
    <ProCard bordered className={styles.infoCard}>
      <div className={styles.userInfo}>
        <Avatar
          size={64}
          icon={<UserOutlined />}
          src={liveData?.avatar}
          className={styles.avatar}
        />
        <div className={styles.name}>
          <div>{liveData?.nickname}</div>
          <div>粉丝总数: {formatNum(liveData?.followerCount)}</div>
        </div>
      </div>
      <div className={styles.detail}>
        {bizTypeValue && (
          <div className={styles.title}>
            {bizTypeValue.name}: {bizTypeValue.value}
          </div>
        )}
        <div className={styles.title}>
          直播标题:{' '}
          <span
            dangerouslySetInnerHTML={{
              __html: liveData?.roomTitle || '',
            }}
          />
        </div>
        <div className={styles.liveTime}>
          直播时间: &nbsp;
          {liveData?.liveStatus === 0 ? (
            `${liveData?.liveStartTime}-`
          ) : (
            <>
              <span>({formatSecond(liveData?.duration)})</span>
              <br />
              <span>
                {liveData?.liveStartTime} - {liveData?.liveEndTime}
              </span>
            </>
          )}
        </div>
        <div>
          质检状态: &nbsp;
          <span className={styles.tag}>
            {liveData?.qualityStatus === 0 ? (
              <span>
                <img src={QualityDuring} />
                质检中
              </span>
            ) : (
              <span>
                <img src={QualityEnd} />
                已结束
              </span>
            )}
          </span>
        </div>
        <div>
          直播状态: &nbsp;
          <span className={styles.tag}>
            {liveData?.liveStatus === 0 ? (
              <span>
                <img src={LiveDuring} />
                直播中
              </span>
            ) : (
              <span>
                <img src={LiveEnd} />
                已结束
              </span>
            )}
          </span>
        </div>
      </div>
    </ProCard>
  );
};

export default InfoCard;
