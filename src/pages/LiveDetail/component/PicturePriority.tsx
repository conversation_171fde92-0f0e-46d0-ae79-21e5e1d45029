import { Timeline } from '@/components/antd/timeline';
import { DanmuItem } from '@/components/commentShow';

import { useLiveDanmu } from '@/hooks/request/use-danmu';
import { useLiveFrame } from '@/hooks/request/use-live-frame';
import { useLiveSubtitle } from '@/hooks/request/use-live-subtitle';
import { QualityTargetType } from '@/services/constants';
import { LiveQualityItem, LiveScreenItem } from '@/services/quality';
import { HideScrollBarDiv } from '@/utils/commonStyle';
import { useSize } from 'ahooks';
import { Popover, Space, Spin } from 'antd';
import dayjs from 'dayjs';
import duration from 'dayjs/plugin/duration';
import { useAtom } from 'jotai';
import { chunk, debounce, isEmpty } from 'lodash-es';
import React, { useEffect, useRef, useState } from 'react';
import styled, { keyframes } from 'styled-components';
import Player from 'xgplayer';
import { selectedLive<PERSON>rame<PERSON>tom, selectedLiveSubtitleAtom } from './liveAtom';
import { VideoSubtitleItem } from './types';

dayjs.extend(duration);

export const SbuTitleContainer = styled.span<{
  $showHit: boolean;
  $subtitleIsSelected: boolean;
  $exInfo: boolean;
}>`
  text-decoration: ${(props) =>
    props.$exInfo && props.$showHit
      ? 'underline wavy red'
      : props.$subtitleIsSelected
        ? 'underline solid'
        : 'none'};

  cursor: ${(props) => (props.$showHit ? 'pointer' : 'default')};

  :hover {
    text-decoration: underline;
    text-decoration-style: ${(props) => (!props.$showHit ? 'dotted' : 'solid')};
  }
  .highlight {
    color: black;
    background: yellow;
  }
`;

export const DanmuSpan = styled.div`
  display: 'inline-block';
  color: #c7c8ca;
  background-color: #272c36;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;

  .count {
    margin-left: 5px;
    padding: 2px 5px;
    color: #ffffff;
    background-color: #263a6d;
    border-radius: 50px;
  }
`;
export function durationToTime(duration: number) {
  return dayjs.duration(duration, 'seconds').format('HH:mm:ss');
}

type FrameInfo = {
  width: number;
  height: number;
  frameNum: number;
};

type FrameAnalyProps = {
  liveData?: LiveQualityItem;
  videoPlayerRef: React.MutableRefObject<Player | null>;
  showHit: boolean;
  targetId?: string;
  platform?: string;
  targetType: QualityTargetType;
  isLive?: boolean;
  isInQuality?: boolean;
  showContent: string[];
  tabKey?: string;
};

// 定义闪烁动画
const flashAnimation = keyframes`
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
`;

export const SubTitleSpan = styled.span`
  [data-tooltip] {
    position: relative;
  }

  [data-tooltip]:hover::after {
    position: absolute;
    top: -200%;
    right: 0;
    left: 0;
    width: 50px;
    margin-top: 5px;
    padding: 2px 5px;
    color: white;
    white-space: nowrap;
    text-align: center;
    word-wrap: break-word;
    background-color: #424242;
    border-radius: 5px;
    box-shadow:
      0 6px 16px 0 rgba(0, 0, 0, 0.08),
      0 3px 6px -4px rgba(0, 0, 0, 0.12),
      0 9px 28px 8px rgba(0, 0, 0, 0.05);
    transform: translateX(-25%);
    content: attr(data-tooltip);
  }

  &.flash-animation {
    background-color: #6c8fdd;
    animation: ${flashAnimation} 2s 2;
  }

  &.flash-animation .mark-highlight {
    color: black;
    background: yellow;
  }
`;

export const ImgByFlash = styled.img`
  width: 100%;
  height: 100%;
  object-fit: cover;

  &.flash-animation {
    background-color: #6c8fdd;
    animation: ${flashAnimation} 2s 2;
  }
`;

export const FrameBlendMode = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  /* mix-blend-mode: screen; */
  .ex-info {
    position: absolute;
    bottom: 0;
    width: 100%;
    overflow: hidden;
    color: #ffffff;
    font-size: 10px;
    white-space: nowrap;
    text-align: center;
    background: #272c36;
  }
`;

export const frameInfo: FrameInfo = {
  width: 100,
  height: 180,
  frameNum: 10,
};

export const PicturePriority = (props: FrameAnalyProps) => {
  const { videoPlayerRef, showContent, showHit, targetId, platform, liveData, tabKey } = props;

  const { data: liveScreen } = useLiveFrame({
    roomId: targetId,
    platform,
  });

  const { data: subtitles, loading: subtitleLoading } = useLiveSubtitle({
    roomId: targetId,
    platform,
  });
  const { data: danmuList } = useLiveDanmu({
    roomId: targetId,
    platform,
  });
  const [selectedLiveFrame, setSelectedLiveFrame] = useAtom(selectedLiveFrameAtom);
  const [selectSubtitles, setSelectSubtitles] = useAtom(selectedLiveSubtitleAtom);

  const rightSiderRef = useRef<HTMLDivElement | null>(null);
  const size = useSize(rightSiderRef);
  const [liveFrameData, setLiveFrameData] = useState<
    {
      range: { startTime: number; endTime: number };
      frameData: LiveScreenItem[];
      danmuList: DanmuItem[];
      subtitles: VideoSubtitleItem[];
    }[]
  >([]);

  const subtitleFilter = useRef<string[]>([]);
  const oneLineFrameNumRef = useRef<number>(0);

  const [hoverRange, setHoverRange] = useState<{ startTime: number; endTime: number } | null>(null);

  const updateLiveFrameData = debounce(() => {
    const width = size?.width;
    if (!width || isEmpty(liveScreen)) return;
    subtitleFilter.current = [];
    oneLineFrameNumRef.current = Math.floor(width / (frameInfo.width / 2)) - 1;
    liveScreen?.forEach((item, index) => {
      item.index = index;
    });
    const framePicArr = chunk(liveScreen, oneLineFrameNumRef.current);

    const renderFrameData = framePicArr.map((frame, idx) => {
      const startSeconds = Number(idx * 10) * oneLineFrameNumRef.current;
      const endSeconds = (Number(idx * 10) + 10) * oneLineFrameNumRef.current;
      const itemDanmuList = danmuList?.filter(
        (danmu) => Number(danmu.startTime) >= startSeconds && Number(danmu.startTime) <= endSeconds,
      );
      const result = itemDanmuList?.reduce(
        (
          acc: DanmuItem[],
          curr: DanmuItem & {
            count?: number;
          },
        ) => {
          const existingItem = acc.find((item) => item.content === curr.content);
          if (existingItem) {
            //@ts-ignore
            existingItem.count++;
          } else {
            acc.push({ ...curr, count: 1 });
          }
          return acc;
        },
        [],
      );
      // TODO 字幕和弹幕的排序
      result?.sort((a, b) => {
        // 先按 count 进行降序排序
        // @ts-ignore
        if (a.count > b.count) {
          return -1;
          // @ts-ignore
        } else if (a.count < b.count) {
          return 1;
        } else {
          // 如果 count 相等，再按 time 进行升序排序
          return dayjs(a.time).diff(dayjs(b.time));
        }
      });
      const itemSubtitles = subtitles?.filter((subtitle) => {
        const startTime = Number(subtitle.startTime);

        const haveBeenSelected = subtitleFilter.current.some((item) => item === subtitle.id);
        if (startTime >= startSeconds && startTime < endSeconds && !haveBeenSelected) {
          subtitleFilter.current = subtitleFilter.current.concat(subtitle.id as string);
          return true;
        } else {
          return false;
        }
      });
      return {
        range: {
          startTime: startSeconds,
          endTime: endSeconds,
        },
        frameData: frame,
        danmuList: result,
        subtitles: itemSubtitles,
      };
    });
    // @ts-ignore
    setLiveFrameData(renderFrameData);
  }, 300);

  useEffect(() => {
    updateLiveFrameData();
  }, [size, frameInfo, liveScreen, subtitles, danmuList]);

  const liveFrameClick = (item: LiveScreenItem, startTime: number, index?: number) => {
    if (showHit) {
      const isSelect = selectedLiveFrame.some((selectedItem) => selectedItem.picUrl === item.url);
      if (isSelect) {
        setSelectedLiveFrame(
          selectedLiveFrame.filter((selectedItem) => selectedItem.picUrl !== item.url),
        );
      } else {
        setSelectedLiveFrame([
          ...selectedLiveFrame,
          {
            startTime: startTime,
            endTime: startTime + 10,
            picUrl: item.url,
            index,
          },
        ]);
      }
    } else {
      if (videoPlayerRef.current) {
        videoPlayerRef.current.currentTime = startTime;
        if (videoPlayerRef.current.paused) {
          videoPlayerRef.current.play();
        }
      }
    }
  };

  function handleSubtitleHover(subtitleItem: VideoSubtitleItem) {
    setHoverRange({
      startTime: subtitleItem.startTime,
      endTime: subtitleItem.endTime,
    });
  }

  function handleSubtitleHoverLeave() {
    setHoverRange(null);
  }
  function handleSubtitleClick(subtitleItem: VideoSubtitleItem) {
    if (!showHit) return;
    const isSelect = selectSubtitles.some((selectedItem) => selectedItem.id === subtitleItem.id);
    if (isSelect) {
      setSelectSubtitles(
        selectSubtitles.filter((selectedItem) => selectedItem.id !== subtitleItem.id),
      );
    } else {
      setSelectSubtitles([
        {
          ...subtitleItem,
        },
      ]);
    }
  }

  return (
    <div
      style={{
        width: '100%',
        height: 'max-content',
      }}
    >
      <HideScrollBarDiv
        id="scrollableDiv"
        ref={rightSiderRef}
        style={{
          display: 'flex',
          width: '100%',
          flexDirection: 'row',
          flexWrap: 'wrap',
          height: 'calc(100vh - 56px - 300px)',
        }}
      >
        <div
          style={{
            display: 'flex',
            width: '100%',
            flexDirection: 'row',
            flexWrap: 'wrap',
            paddingTop: '10px',
          }}
        >
          {liveFrameData && (
            <Timeline
              items={liveFrameData?.map((oneLineLiveFrame, idx) => {
                return {
                  children: (
                    <div key={`timeLineItem-${idx}`}>
                      <Space>
                        <span>
                          {dayjs(liveData?.liveStartTime)
                            .add(Number(idx * 10) * oneLineFrameNumRef.current, 'second')
                            .format('HH:mm:ss')}
                        </span>
                        {liveData?.liveStatus !== 0 && (
                          <span style={{ color: '#8c8e91' }}>
                            （{durationToTime(Number(idx * 10) * oneLineFrameNumRef.current)}）
                          </span>
                        )}
                      </Space>
                      <div>
                        {oneLineLiveFrame?.frameData?.map((frame) => {
                          const currentFrameSeconds =
                            (liveScreen?.findIndex((live) => live.url === frame.url) ?? 0) * 10;
                          const frameStartTime = currentFrameSeconds;
                          // const frameEndTime = currentFrameSeconds + 10;

                          const isSelected = selectedLiveFrame.some(
                            (item) => item.picUrl === frame.url,
                          );
                          const isSelectedBySubtitle = selectSubtitles.some((subtitle) => {
                            return (
                              Number(frameStartTime) >= Number(subtitle.startTime) &&
                              Number(frameStartTime) <= Number(subtitle.endTime)
                              // &&
                              // Number(frameEndTime) > Number(subtitle.startTime) &&
                              // Number(frameEndTime) <= Number(subtitle.endTime)
                            );
                          });
                          const width = frameInfo.width / 2;
                          const height = frameInfo.height / 2;
                          const videoWidth = 1080;
                          const maxWidth = 400 - 40 - 20;
                          const videoHeight = (1920 * maxWidth) / videoWidth;

                          const isHoveredBySubtitle =
                            hoverRange?.startTime &&
                            hoverRange?.endTime &&
                            currentFrameSeconds >= hoverRange.startTime &&
                            currentFrameSeconds <= hoverRange.endTime;

                          return (
                            <React.Fragment key={frame.url}>
                              <Popover
                                key={frame.url}
                                placement="left"
                                mouseLeaveDelay={0}
                                destroyOnHidden
                                fresh
                                content={
                                  showHit ? (
                                    <div>
                                      <img
                                        src={frame.url}
                                        style={{
                                          width: maxWidth,
                                          height: videoHeight,
                                        }}
                                      />
                                      <div>
                                        {dayjs(liveData?.liveStartTime)
                                          .add(currentFrameSeconds, 'second')
                                          .format('HH:mm:ss')}
                                        {liveData?.liveStatus !== 0 && (
                                          <>（{durationToTime(currentFrameSeconds)}）</>
                                        )}
                                      </div>
                                    </div>
                                  ) : null
                                }
                                title={null}
                              >
                                <div
                                  style={{
                                    width,
                                    height,
                                    display: 'inline-block',
                                    position: 'relative',
                                    cursor: showHit ? 'pointer' : 'default',
                                  }}
                                  onClick={() =>
                                    liveFrameClick(frame, currentFrameSeconds, frame.index)
                                  }
                                >
                                  <ImgByFlash
                                    src={`${frame.url}?imageMogr2/thumbnail/!${frameInfo.width}x${frameInfo.height}r`}
                                    loading="lazy"
                                    id={`frame-${tabKey}-${frameStartTime}`}
                                  />
                                  {showHit && (isSelected || isSelectedBySubtitle) && (
                                    <FrameBlendMode
                                      style={{
                                        boxShadow: 'inset 0 0 19.3px 1px rgba(55, 112, 255, 0.6)',
                                      }}
                                    />
                                  )}
                                  {isHoveredBySubtitle && (
                                    <FrameBlendMode
                                      style={{
                                        boxShadow: 'inset 0 0 19.3px 1px rgba(55, 112, 255, 0.6)',
                                      }}
                                    />
                                  )}
                                  {showHit && !isEmpty(frame.exInfo) && (
                                    <FrameBlendMode
                                      style={{
                                        background: 'rgba(179,40,21, 0.3)',
                                        border: '1px solid #B32815',
                                        bottom: 0,
                                      }}
                                    >
                                      <span className="ex-info">
                                        {frame.exInfo.monitoringWordName}
                                      </span>
                                    </FrameBlendMode>
                                  )}
                                </div>
                              </Popover>
                            </React.Fragment>
                          );
                        })}
                      </div>
                      {showContent.includes('subtitle') && (
                        <div>
                          <Spin
                            tip="口播字幕加载中..."
                            spinning={isEmpty(oneLineLiveFrame.subtitles) && subtitleLoading}
                          >
                            {isEmpty(oneLineLiveFrame.subtitles) && subtitleLoading ? (
                              <div
                                style={{
                                  width: '100%',
                                  height: '100px',
                                  display: 'flex',
                                  justifyContent: 'center',
                                  alignItems: 'center',
                                }}
                              />
                            ) : (
                              <>
                                {oneLineLiveFrame.subtitles?.map((subtitleItem) => {
                                  const subtitleIsSelected = selectSubtitles.some((subtitle) => {
                                    return subtitle.id === subtitleItem.id;
                                  });
                                  return (
                                    <SbuTitleContainer
                                      $showHit={showHit || subtitleIsSelected}
                                      $subtitleIsSelected={subtitleIsSelected}
                                      $exInfo={!isEmpty(subtitleItem.exInfo)}
                                      key={subtitleItem.id}
                                      onMouseEnter={() => {
                                        handleSubtitleHover(subtitleItem);
                                      }}
                                      onMouseLeave={() => {
                                        handleSubtitleHoverLeave();
                                      }}
                                      onClick={() => {
                                        handleSubtitleClick(subtitleItem);
                                      }}
                                    >
                                      <SubTitleSpan
                                        dangerouslySetInnerHTML={{
                                          __html:
                                            subtitleItem.content || subtitleItem.subtitle || '',
                                        }}
                                        key={subtitleItem.id}
                                        id={`subtitle-${tabKey}-${subtitleItem.startTime}`}
                                      />
                                    </SbuTitleContainer>
                                  );
                                })}
                              </>
                            )}
                          </Spin>
                        </div>
                      )}
                      <div>
                        {showContent.includes('danmu') && (
                          <Space
                            size={[8, 8]}
                            wrap
                            style={{
                              marginTop: '5px',
                            }}
                          >
                            {oneLineLiveFrame.danmuList?.map((danmuItem) => {
                              return (
                                <DanmuSpan key={danmuItem.id}>
                                  <span
                                    dangerouslySetInnerHTML={{
                                      __html: danmuItem.content,
                                    }}
                                  />
                                  {danmuItem.count && danmuItem.count > 1 && (
                                    <span className="count">X{danmuItem.count}</span>
                                  )}
                                </DanmuSpan>
                              );
                            })}
                          </Space>
                        )}
                      </div>
                    </div>
                  ),
                };
              })}
            />
          )}
        </div>
      </HideScrollBarDiv>
    </div>
  );
};
