import { LiveQualityEcommerceItem, LiveQualityItem } from '@/services/quality';
import { ProCard } from '@ant-design/pro-components';
import { ConfigProvider, Empty, Spin, theme } from 'antd';
import { lazy, Suspense, useCallback, useMemo } from 'react';
import Player from 'xgplayer';

const XGPlayer = lazy(() => import('@/components/xgplayer'));

type VideoCardProps = {
  liveData: LiveQualityItem | LiveQualityEcommerceItem;
  videoPlayerRef: React.MutableRefObject<Player | null>;
};

const VideoCard = (props: VideoCardProps) => {
  const { liveData, videoPlayerRef } = props;

  const onVideoReady = useCallback((player: Player) => {
    videoPlayerRef.current = player;

    const cfg = localStorage.getItem('video-card-preview-volume');
    if (cfg) {
      try {
        const { volume, muted } = JSON.parse(cfg);
        player.volume = volume;
        player.muted = muted;
      } catch (err) {
        console.log(err);
      }
    }
  }, []);

  const playerOptions = useMemo(() => {
    const videoWidth = 1080;
    const videoHeight = 1920;
    const maxWidth = 400 - 40 - 20;
    const height = (videoHeight * maxWidth) / videoWidth;
    return {
      width: maxWidth,
      height: height,
      loop: true,
      controls: true,
      autoplay: false,
      playbackRate: [0.5, 1, 1.5, 2],
      // 直播隐藏倍速  非直播隐藏css的全屏控件（因为超过一小时的视频 时间展示控件和倍速按钮 因为放不下而被自动隐藏）
      ignores: liveData.liveStatus === 0 ? ['playbackrate'] : ['cssfullscreen'],
    };
  }, [liveData]);

  const replyUrls = useMemo(() => {
    if (liveData.replayUrls) {
      return liveData.replayUrls[0];
    } else {
      return '';
    }
  }, [liveData]);

  const videoType = () => {
    if (liveData.liveStatus === 0) {
      return 'flv';
    } else if (liveData.isPullUrl === 0) {
      return 'mp4';
    } else {
      return 'm3u8';
    }
  };

  const videoUrl = () => {
    if (liveData.liveStatus === 0) {
      return liveData.livePullUrl;
    } else if (liveData.isPullUrl === 0) {
      return liveData.videoUrl;
    } else {
      return replyUrls;
    }
  };

  const currentVideoType = videoType();
  const currentVideoUrl = videoUrl();

  return (
    <ProCard
      bordered
      style={{
        borderRadius: '6px',
        marginTop: '16px',
        backgroundColor: '#1b1d22',
        padding: '10px',
        marginInline: 'auto',
      }}
      bodyStyle={{ overflow: 'hidden' }}
      ghost
    >
      {liveData.cleanFlag ? (
        <ConfigProvider theme={{ algorithm: theme.darkAlgorithm }}>
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description={<span>视频超过存储时长，已清理</span>}
            style={{ paddingTop: '50px' }}
          />
        </ConfigProvider>
      ) : (
        <Suspense fallback={<Spin />}>
          <XGPlayer
            url={currentVideoUrl}
            isLive={liveData.liveStatus === 0}
            type={currentVideoType}
            playerOptions={playerOptions}
            onPlayerReady={onVideoReady}
          />
        </Suspense>
      )}
    </ProCard>
  );
};

export default VideoCard;
