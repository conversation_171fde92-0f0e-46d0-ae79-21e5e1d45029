import { QueryCarQuotation } from '@/services/price';
import { formatNum } from '@/utils/common';
import { useParams } from '@umijs/max';
import { useRequest } from 'ahooks';
import { ConfigProvider, Empty, Flex, Spin, theme } from 'antd';

type CarPriceProps = {
  projectId?: string;
};

const CarPrice = (props: CarPriceProps) => {
  const { projectId } = props;
  const { roomId } = useParams();
  // 只有大众的项目有所以直接写死, 后面其他项目也需要的话应该去掉projectId这个参数
  const { data, loading } = useRequest(() =>
    QueryCarQuotation({ projectId: projectId || '22', roomId }),
  );

  return (
    <>
      {loading ? (
        <Spin
          style={{
            width: '100%',
            height: '300px',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        />
      ) : (
        <>
          {data?.data?.length === 0 ? (
            <ConfigProvider theme={{ algorithm: theme.darkAlgorithm }}>
              <Empty
                image={Empty.PRESENTED_IMAGE_SIMPLE}
                description={<span>暂无数据</span>}
                style={{ paddingTop: '50px' }}
              />
            </ConfigProvider>
          ) : (
            <>
              {data?.data?.map((item, index) => (
                <div style={{ paddingInline: '10px' }} key={`car-price-${index}`}>
                  <Flex align="center" style={{ padding: '10px', gap: '10px' }}>
                    <div className="flex flex-col items-center gap-2">
                      <img
                        src={item.icon}
                        alt="car"
                        style={{
                          width: '50px',
                          height: '50px',
                          objectFit: 'contain',
                        }}
                      />
                      <div>{item.carTypeCn}</div>
                    </div>
                    <Flex vertical gap={10}>
                      <div style={{ fontSize: '14px', color: '#fff' }}>{item.carVersion}</div>
                      <div style={{ fontSize: '12px', color: '#8C8E91' }}>
                        直播间报价：{formatNum(item.minQuotation)}
                      </div>
                    </Flex>
                  </Flex>
                  <hr style={{ backgroundColor: '#3D4350', border: 'none', height: '1px' }} />
                </div>
              ))}
            </>
          )}
        </>
      )}
    </>
  );
};

export default CarPrice;
