import { LiveStreamSummaryContainer } from '@/pages/ProjectHome/LiveAnalysis/components/LiveAnalysisList';
import { RadioGroup } from '@/pages/ProjectHome/LiveAnalysis/components/RadioGroup';
import { getStatisticContentDetail } from '@/services/ai-live/statistic';
import { ProCard } from '@ant-design/pro-components';
import { useParams, useQuery } from '@umijs/max';
import { Flex } from 'antd';
import { useEffect, useMemo, useState } from 'react';

function useGetStatisticContentDetail({ roomId, platform }: { roomId: string; platform: string }) {
  const { data } = useQuery({
    queryKey: ['statisticContentDetail', roomId, platform],
    queryFn: () => getStatisticContentDetail({ roomId, platform }),
    enabled: !!roomId && !!platform,
  });
  return { data: data?.data };
}

export function ContentAnaly() {
  const { roomId, platform } = useParams();

  const { data } = useGetStatisticContentDetail({ roomId: roomId || '', platform: platform || '' });
  const [selectedRadioGroupValue, setSelectedRadioGroupValue] = useState('');

  const carVersion = useMemo(() => {
    return [...new Set(data?.map((item) => item.carVersion))].map((version) => ({
      label: version,
      value: version,
    }));
  }, [data]);
  console.log('carVersion: ', carVersion);

  const handleChange = (value: string) => {
    setSelectedRadioGroupValue(value);
  };

  useEffect(() => {
    setSelectedRadioGroupValue(carVersion?.[0]?.label || '');
  }, [carVersion]);
  const contentData = useMemo(() => {
    return data?.filter((item) => item.carVersion === selectedRadioGroupValue);
  }, [data, selectedRadioGroupValue]);

  return (
    <ProCard>
      <div className="flex items-center">
        <div>车型类型：</div>
        <RadioGroup
          options={carVersion || []}
          onChange={handleChange}
          value={selectedRadioGroupValue}
          showValue={false}
        />
      </div>
      <Flex vertical gap={20} style={{ padding: '10px' }}>
        {contentData?.map((item) => (
          <LiveStreamSummaryContainer
            style={{
              backgroundColor: '#272C36',
              borderRadius: '4px',
            }}
            key={item.id}
          >
            <div
              className="content-box"
              style={{
                backgroundColor: '#272C36',
                borderRadius: '4px',
                marginTop: '0',
                color: '#0e1015',
              }}
            >
              <div
                className="title"
                style={{
                  color: '#FFFFFF',
                }}
              >
                {item.contentType}
              </div>
              <div
                className="summary"
                style={{
                  color: 'rgba(255, 255, 255, 0.5)',
                }}
              >
                内容总结： {item.contentSummary}
              </div>
            </div>
          </LiveStreamSummaryContainer>
        ))}
      </Flex>
    </ProCard>
  );
}
