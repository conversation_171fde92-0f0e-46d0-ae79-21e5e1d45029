import { DanmuItem } from '@/components/commentShow';
import CommonHeader from '@/components/header';
import { useLiveDetail } from '@/hooks/request/use-live-detail';
import useBeforeUnloadRecords from '@/hooks/useBeforeUnloadRecord';
import VideoAnaly from '@/pages/LiveDetail/component/videoAnaly';
import { QualityTargetType } from '@/services/constants';
import { CustomHeaderTabsDark, ScrollSider } from '@/utils/commonStyle';
import { findLocalStorageValue } from '@/utils/quality';
import { useParams } from '@umijs/max';
import { useTitle } from 'ahooks';
import { App, Breadcrumb, ConfigProvider, Flex, TabsProps, theme } from 'antd';
import { atom, useAtomValue } from 'jotai';
import { useMemo, useRef, useState } from 'react';
import Player from 'xgplayer';
import BarrageAnaly from './component/BarrageAnaly';
import DataSituation from './component/dataSituation';
import { HighlightAlert } from './component/HighlightAlert';
import InfoCard from './component/infoCard';
import VideoCard from './component/videoCard';

import useProjectFeature from '@/hooks/fg/useProjectFeature';
import { FunctionCode } from '@/services/system';
import { ContentAnaly } from './component/ContentAnaly';
import {
  danmuPriorityShowContentAtom,
  framePriorityShowContentAtom,
  liveTabKeyAtom,
  rightSiderActiveKeyAtom,
  showLiveHitStateAtom,
  subtitlesPriorityShowContentAtom,
  toggleLiveRightSiderShowAtom,
} from './component/liveAtom';
import { VideoSubtitleItem } from './component/types';

export const subtitlesAtom = atom<VideoSubtitleItem[]>([]);
export const danmuListAtom = atom<DanmuItem[]>([]);

const LiveDetail = () => {
  useTitle('质检详情');
  const { roomId, platform, industryType } = useParams();

  const videoPlayerRef = useRef<Player | null>(null);
  const { data: liveDetailData } = useLiveDetail({ roomId, platform, industryType });

  const [headerActiveKey, setHeaderActiveKey] = useState<string>(
    findLocalStorageValue<string>('liveDetailRecords', 'headerActiveKey') || 'videoAnaly',
  );
  const rightSiderActiveKey = useAtomValue(rightSiderActiveKeyAtom);
  const tabKey = useAtomValue(liveTabKeyAtom);
  const showHitState = useAtomValue(showLiveHitStateAtom);
  const toggleRightSiderShow = useAtomValue(toggleLiveRightSiderShowAtom);
  const framePriorityShowContent = useAtomValue(framePriorityShowContentAtom);
  const subtitlesPriorityShowContent = useAtomValue(subtitlesPriorityShowContentAtom);
  const danmuPriorityShowContent = useAtomValue(danmuPriorityShowContentAtom);

  useBeforeUnloadRecords('liveDetailRecords', {
    rightSiderActiveKey,
    headerActiveKey,
    tabKey,
    showHitState,
    toggleRightSiderShow,
    framePriorityShowContent,
    subtitlesPriorityShowContent,
    danmuPriorityShowContent,
  });

  const { featureEnableDict } = useProjectFeature({ projectId: liveDetailData?.projectId });

  const items = useMemo(() => {
    const items: TabsProps['items'] = [
      {
        label: '数据情况',
        key: 'dataSituation',
        children: <DataSituation liveData={liveDetailData} industryType={industryType} />,
      },
      {
        label: '视频分析',
        key: 'videoAnaly',
        children: (
          <VideoAnaly
            videoPlayerRef={videoPlayerRef}
            targetId={roomId}
            projectId={liveDetailData?.projectId}
            platform={platform}
            liveData={liveDetailData}
            monitoringWordType={QualityTargetType.LIVE}
            isLive={liveDetailData?.liveStatus === 0 ? true : false}
            isInQuality={liveDetailData?.qualityStatus === 0 ? true : false}
          />
        ),
      },
    ];

    const switchFeatures: TabsProps['items'] = [
      {
        label: '弹幕分析',
        key: String(FunctionCode.BarrageAnalysis),
        children: <BarrageAnaly projectId={liveDetailData?.projectId} />,
      },
      {
        label: '内容分析',
        key: String(FunctionCode.LiveAnalysis),
        children: <ContentAnaly />,
      },
    ];

    switchFeatures.forEach((item) => {
      if (featureEnableDict?.[Number(item.key) as unknown as FunctionCode]) {
        items.push(item);
      }
    });
    return items;
  }, [featureEnableDict, liveDetailData, platform, roomId]);

  return (
    <ConfigProvider theme={{ algorithm: theme.darkAlgorithm }}>
      <App
        style={{
          height: '100vh',
        }}
      >
        <Flex vertical style={{ height: '100%' }}>
          <CommonHeader />
          <div
            style={{
              background: '#06070b',
              flex: 1,
              overflow: 'hidden',
              display: 'flex',
              flexDirection: 'row',
              overflowX: 'auto',
            }}
          >
            <ScrollSider>
              <Breadcrumb
                style={{
                  padding: '18px 20px',
                  position: 'sticky',
                  top: 0,
                  zIndex: 1000,
                  background: '#06070A',
                }}
                items={[
                  {
                    title: '质量管理',
                  },
                  {
                    title: '直播质检',
                  },
                  {
                    title: '直播监控详情',
                  },
                ]}
              />
              <div
                style={{
                  width: '100%',
                  height: 'max-content',
                  paddingInline: '20px',
                  alignItems: 'center',
                  display: 'flex',
                  flexDirection: 'column',
                }}
              >
                <InfoCard liveData={liveDetailData} />
                {liveDetailData && (
                  <VideoCard liveData={liveDetailData} videoPlayerRef={videoPlayerRef} />
                )}
              </div>
            </ScrollSider>
            <div
              style={{
                paddingRight: '20px',
                height: '100%',
                flex: 1,
                overflow: 'hidden',
                minWidth: '700px',
              }}
            >
              {/* 只有一起播项目显示  TODO 接口来 先hardcode*/}
              {liveDetailData?.projectId === '22' || liveDetailData?.projectId === '60' ? (
                <HighlightAlert />
              ) : (
                <div style={{ height: '60px' }} />
              )}
              <CustomHeaderTabsDark
                activeKey={headerActiveKey}
                items={items}
                onChange={setHeaderActiveKey}
              />
            </div>
          </div>
        </Flex>
      </App>
    </ConfigProvider>
  );
};

export default LiveDetail;
