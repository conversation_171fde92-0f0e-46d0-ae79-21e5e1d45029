import useProjectId from '@/hooks/useProjectId';
import { DateType } from '@/services/business';
import { getTimeByTypeBusiness } from '@/utils/time';
import { PageContainer, ProCard } from '@ant-design/pro-components';
import { useState } from 'react';
import BarrageAnalysisDetailFilters from '../BarrageAnalysis/components/barrageAnalysisDetailFilters';
import { DataTypeList } from './components/dataTypeList';
import { TrendChart } from './components/trendChart';
import { useLocation, useNavigate, useParams } from '@umijs/max';
import { paramsFilter } from '@/utils/url';
import { useAtom } from 'jotai';
import { selectingDealsAtom } from '../atom';
import { useRequest } from 'ahooks';
import { getDataTypes } from '@/services/project-work-analysis';
import { Spin } from 'antd';
import { withAuth } from '@/hoc/withAuth';
import { FunctionCode } from '@/services/system';
import BreadCrumbSimple from '@/components/ui/breadcrumbSimple';

const ProjectWorkAnalysis = () => {
  const { industryType } = useParams();
  const projectId = useProjectId();
  const navigate = useNavigate();
  const location = useLocation();

  const [radioValue, setRadioValue] = useState<DateType>(DateType.Yesterday);
  const [timeRange, setTimeRange] = useState<string[]>(getTimeByTypeBusiness(DateType.Yesterday));
  const [treeValue, setTreeValue] = useAtom(selectingDealsAtom);
  /** 当前选择的车型/视频类型 */
  const [selectingTypeId, setSelectingTypeId] = useState<string | undefined>(undefined);
  /** 需要统计的数据类型 0车型数据 1视频类型数据 */
  const [typeGroup, setTypeGroup] = useState<0 | 1>(0);

  const { data: dataTypeListRes, loading } = useRequest(
    () => {
      if (!projectId) {
        return Promise.reject();
      }
      return getDataTypes({
        startTime: timeRange[0],
        endTime: timeRange[1],
        type: typeGroup,
        projectId,
        teamCodeList: treeValue,
        industryType: industryType ? Number(industryType) : undefined,
      });
    },
    {
      refreshDeps: [typeGroup, timeRange, projectId, treeValue],
      onSuccess: (res) => {
        setSelectingTypeId(res?.data?.[0]?.name);
      },
    },
  );

  return (
    <PageContainer title={<BreadCrumbSimple breadcrumbs={['智能分析', '作品分析']} />}>
      <Spin spinning={loading}>
        <ProCard colSpan={{ md: 24, lg: 18 }}>
          <BarrageAnalysisDetailFilters
            dateOptions={[
              [DateType.Yesterday, '近一天'],
              [DateType.LastWeek, '近一周'],
              [DateType.LastMonth, '近一个月'],
            ]}
            radioValue={radioValue}
            setRadioValue={setRadioValue}
            rangeTime={timeRange}
            setRangeTime={setTimeRange}
            treeValue={treeValue}
            setTreeValue={setTreeValue as any}
          />

          <DataTypeList
            typeGroup={typeGroup}
            setTypeGroup={setTypeGroup}
            selectingTypeId={selectingTypeId}
            setSelectingTypeId={setSelectingTypeId}
            dataTypes={dataTypeListRes?.data || []}
          />
        </ProCard>
      </Spin>

      <ProCard style={{ marginTop: 20 }}>
        <TrendChart
          treeValue={treeValue}
          onChartDoubleClick={(query) => {
            const { pathname } = location;
            let dateStart = timeRange[0];
            let dateEnd = timeRange[1];
            let videoType, carInfo;
            if (query.date) {
              dateStart = query.date;
              dateEnd = query.date;
            }

            if (typeGroup === 0) {
              // 如果查看的是车型数据 则typeId为视频类型
              carInfo = selectingTypeId;
              videoType = query.typeId;
            } else {
              // 如果查看的是视频类型数据 则typeId为车型
              videoType = selectingTypeId;
              carInfo = query.typeId;
            }

            const targetPathname = `${pathname
              .split('/')
              .slice(0, 4)
              .join('/')}/platform/product-setting`;

            navigate(
              paramsFilter(targetPathname, {
                dateStart,
                dateEnd,
                videoType,
                carInfo,
              }),
            );
          }}
          selectingTypeId={selectingTypeId}
          typeGroup={typeGroup}
          timeRange={timeRange}
        />
      </ProCard>
    </PageContainer>
  );
};

export default withAuth(ProjectWorkAnalysis, FunctionCode.ProjectWorksAnalysis);
