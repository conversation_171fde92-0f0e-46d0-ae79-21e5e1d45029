import EChartsReact, { EChartsOption } from 'echarts-for-react';
import styles from '../style.module.less';
import { formatNum } from '@/utils/common';
import { Empty, Flex, Spin } from 'antd';
import { DashBoard } from './dashBoard';
import { useRequest } from 'ahooks';
import { getDataTypeInfo } from '@/services/project-work-analysis';
import useProjectId from '@/hooks/useProjectId';
import { useMemo } from 'react';

import IconComment from '@/assets/wa_comment.png';
import IconInteract from '@/assets/wa_interact.png';
import IconLike from '@/assets/wa_like.png';
import IconShare from '@/assets/wa_share.png';
import IconStar from '@/assets/wa_star.png';
import { useParams } from '@umijs/max';
interface MarkPointItem {
  value: number;
  xAxis: string | undefined;
  yAxis: number;
}
export const TrendChart = ({
  selectingTypeId,
  typeGroup,
  onChartDoubleClick,
  timeRange,
  treeValue,
}: {
  timeRange: string[];
  selectingTypeId?: string;
  typeGroup: number;
  treeValue?: string[];
  onChartDoubleClick: (query: Record<string, string>) => void;
  // onChart2Jump: (typeId: string) => void;
}) => {
  const { industryTypeParams } = useParams();
  const industryType = industryTypeParams ? Number(industryTypeParams) : undefined;

  const projectId = useProjectId();
  const { data: analysisInfoRes, loading } = useRequest(
    () => {
      if (projectId && selectingTypeId) {
        return getDataTypeInfo({
          projectId,
          startTime: timeRange[0],
          endTime: timeRange[1],
          type: typeGroup,
          name: selectingTypeId,
          teamCodeList: treeValue || [],
          industryType,
        });
      }
      return Promise.resolve(null);
    },
    { refreshDeps: [selectingTypeId, timeRange, projectId, typeGroup, treeValue] },
  );

  const simpleData = useMemo(() => {
    return [
      {
        name: '互动量',
        value1: analysisInfoRes?.data?.totalInteraction ?? '-',
        value2: analysisInfoRes?.data?.medianInteraction ?? '-',
        value3: analysisInfoRes?.data?.medianBrandInteraction ?? '-',
        icon: IconInteract,
      },
      {
        name: '点赞量',
        value1: analysisInfoRes?.data?.totalDigg ?? '-',
        value2: analysisInfoRes?.data?.medianDigg ?? '-',
        value3: analysisInfoRes?.data?.medianBrandDigg ?? '-',
        icon: IconLike,
      },
      {
        name: '评论量',
        value1: analysisInfoRes?.data?.totalComment ?? '-',
        value2: analysisInfoRes?.data?.medianComment ?? '-',
        value3: analysisInfoRes?.data?.medianBrandComment ?? '-',
        icon: IconComment,
      },
      {
        name: '收藏量',
        value1: analysisInfoRes?.data?.totalCollect ?? '-',
        value2: analysisInfoRes?.data?.medianComment ?? '-',
        value3: analysisInfoRes?.data?.medianBrandCollect ?? '-',
        icon: IconStar,
      },
      {
        name: '分享量',
        value1: analysisInfoRes?.data?.totalShare ?? '-',
        value2: analysisInfoRes?.data?.medianShare ?? '-',
        value3: analysisInfoRes?.data?.medianBrandShare ?? '-',
        icon: IconShare,
      },
    ];
  }, [analysisInfoRes?.data]);

  const option1: EChartsOption = {
    grid: {
      left: '0%',
      right: '0%',
      containLabel: true,
    },
    legend: {
      show: true,
      orient: 'horizontal',
      itemGap: 15,
      icon: 'circle',
      textStyle: {
        color: 'gray',
      },
    },
    tooltip: {
      show: true,
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
      },
      formatter: function (params: any) {
        let tooltipText = `<div style="text-align: left;">${params[0].axisValue}`;
        params.forEach((param: any) => {
          tooltipText += `
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <div>${param.marker} ${param.seriesName}</div>
            &nbsp;&nbsp;<div style="color: #666; font-weight: 900; font-size: 14px;">${formatNum(
              param.value,
            )}</div>
          </div>
        `;
        });
        tooltipText += '<span style="color: #1E5EFF;">tips: 双击跳转作品详情</span></div>';
        return tooltipText;
      },
    },
    xAxis: {
      type: 'category',
      data: analysisInfoRes?.data?.dailySums?.map((item) => item.date) || [],
      axisLabel: {
        interval: (analysisInfoRes?.data?.dailySums?.length || 0) > 7 ? 7 : 0,
      },
    },
    dataZoom: [
      // 有滚动条 平移
      {
        type: 'slider',
        show: (analysisInfoRes?.data?.dailySums?.length || 0) > 30,
        realtime: true,
        startValue: 0,
        endValue: 30,
        height: 4,
        handleSize: 30, // 两边的按钮大小
        showDetail: false, // 拖拽时是否展示滚动条两侧的文字
        top: '90%',
        zoomLock: true, // 是否只平移不缩放
        moveOnMouseMove: true, //鼠标移动能触发数据窗口平移
        zoomOnMouseWheel: true, //鼠标移动能触发数据窗口缩放
      },
      {
        type: 'inside', // 支持内部鼠标滚动平移
        show: (analysisInfoRes?.data?.dailySums?.length || 0) > 30,
        startValue: 0,
        endValue: 30,
        zoomLock: true, // 是否只平移不缩放
        zoomOnMouseWheel: false, // 关闭滚轮缩放
        moveOnMouseWheel: true, // 开启滚轮平移
        moveOnMouseMove: true, // 鼠标移动能触发数据窗口平移
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: '作品',
        position: 'left',
        alignTicks: true,
        axisLine: {
          show: false,
          lineStyle: {
            // color: 'blue',
          },
        },
        axisLabel: {
          show: true,
          formatter: '{value} ',
        },
      },
      {
        type: 'value',
        name: '互动',
        position: 'right',
        alignTicks: true,
        axisLabel: {
          show: true,
          formatter: '{value} ',
        },
      },
    ],
    series: [
      {
        name: '作品量',
        data: analysisInfoRes?.data?.dailySums?.map((item) => item.worksCount) || [],
        type: 'bar',
        barGap: 0,
        color: '#5EE0D8',
        yAxisIndex: 0,

        markPoint: {
          data: [
            { type: 'max', name: '最大值' },
            analysisInfoRes?.data?.dailySums?.reduce((acc, cur) => {
              // 找比0大的最小值
              if (cur?.worksCount && cur.worksCount < (acc?.value || Number.MAX_SAFE_INTEGER)) {
                return {
                  value: cur?.worksCount,
                  xAxis: cur?.date,
                  yAxis: cur?.worksCount,
                };
              }
              return acc;
            }, {} as MarkPointItem),
          ],
        },
      },
      {
        name: '互动量',
        data: analysisInfoRes?.data?.dailySums?.map((item) => item.interactionCount) || [],
        type: 'bar',
        color: '#73A0FA',
        yAxisIndex: 1,
        markPoint: {
          data: [
            { type: 'max', name: '最大值' },
            analysisInfoRes?.data?.dailySums?.reduce((acc, cur) => {
              // 找比0大的最小值
              if (
                cur?.interactionCount &&
                cur.interactionCount < (acc?.value || Number.MAX_SAFE_INTEGER)
              ) {
                return {
                  value: cur?.interactionCount,
                  xAxis: cur?.date,
                  yAxis: cur?.interactionCount,
                };
              }
              return acc;
            }, {} as MarkPointItem),
          ],
        },
      },
    ],
  };

  const option2: EChartsOption = {
    grid: {
      left: '0%',
      right: '0%',
      containLabel: true,
    },
    legend: {
      show: true,
      orient: 'horizontal',
      itemGap: 15,
      icon: 'circle',
      textStyle: {
        color: 'gray',
      },
    },
    tooltip: {
      show: true,
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
      },
      formatter: function (params: any) {
        console.log(params);

        let tooltipText = `<div style="text-align: left;">${params[0].axisValue}`;
        params.forEach((param: any) => {
          tooltipText += `
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <div>${param.marker} ${param.seriesName}</div>
            &nbsp;&nbsp;<div style="color: #666; font-weight: 900; font-size: 14px;">${formatNum(
              param.value,
            )}</div>
          </div>
        `;
        });
        tooltipText += '<span style="color: #1E5EFF;">tips: 双击跳转作品详情</span></div>';
        return tooltipText;
      },
    },
    xAxis: {
      type: 'category',
      data: analysisInfoRes?.data?.distributionInfos?.map((item) =>
        item.name === '-' ? '其他' : item.name,
      ),
    },
    yAxis: [
      {
        type: 'value',
        name: '作品',
        position: 'left',
        alignTicks: true,
        axisLine: {
          show: false,
          lineStyle: {
            // color: 'blue',
          },
        },
        axisLabel: {
          show: true,
          formatter: '{value} ',
        },
      },
      {
        type: 'value',
        name: '互动',
        position: 'right',
        alignTicks: true,
        axisLabel: {
          show: true,
          formatter: '{value} ',
        },
      },
    ],
    series: [
      {
        name: '作品量',
        data: analysisInfoRes?.data?.distributionInfos?.map((item) => item.worksCount) || [],
        type: 'bar',
        barGap: 0,
        color: '#5EE0D8',
        yAxisIndex: 0,
        markPoint: {
          data:
            analysisInfoRes?.data?.distributionInfos?.map((item) => {
              if (item.worksCount === 0) {
                return {};
              } else {
                return {
                  value: item?.worksCount,
                  xAxis: item?.name,
                  yAxis: item?.worksCount,
                };
              }
            }) || [],
        },
      },
      {
        name: '互动量',
        data: analysisInfoRes?.data?.distributionInfos?.map((item) => item.interactionCount) || [],
        type: 'bar',
        color: '#73A0FA',
        yAxisIndex: 1,
        markPoint: {
          data:
            analysisInfoRes?.data?.distributionInfos?.map((item) => {
              if (item.interactionCount === 0) {
                return {};
              } else {
                return {
                  value: item?.interactionCount,
                  xAxis: item?.name,
                  yAxis: item?.interactionCount,
                };
              }
            }) || [],
        },
      },
    ],
  };

  return (
    <Spin spinning={loading}>
      <DashBoard
        data={simpleData}
        selectingTypeId={selectingTypeId}
        total={analysisInfoRes?.data?.totalWorks}
      />

      {analysisInfoRes?.data ? (
        <Flex vertical>
          {/* x轴：日期 y轴：当前所选数据类型的互动量/作品量 */}
          <div className={styles.title}>
            {selectingTypeId === '-' ? '其他' : selectingTypeId} 时间趋势图表
          </div>
          <div className={styles.graphWrapper}>
            <EChartsReact
              option={option1}
              showLoading={false}
              notMerge={true}
              style={{ width: '100%', height: '100%' }}
              onEvents={{
                dblclick: (params: any) => {
                  onChartDoubleClick({ date: params.name });
                },
              }}
            />
          </div>

          {/* x轴：数据类型。typeGroup为车型时展示视频类型 typeGroup为视频类型时展示车型类型 y轴：各数据类型的互动量/作品量 */}
          <div className={styles.title}>
            {selectingTypeId === '-' ? '其他' : selectingTypeId}
            {typeGroup === 0 ? ` 视频类型` : ` 车型`}
            分布图表
          </div>
          <div className={styles.graphWrapper}>
            <EChartsReact
              option={option2}
              showLoading={false}
              notMerge={true}
              style={{ width: '100%', height: '100%' }}
              onEvents={{
                dblclick: (params: any) => {
                  onChartDoubleClick({ typeId: params.name });
                },
              }}
            />
          </div>
        </Flex>
      ) : (
        <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
      )}
    </Spin>
  );
};
