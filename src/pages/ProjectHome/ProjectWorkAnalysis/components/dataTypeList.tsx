import { Col, Empty, Flex, Radio, Row } from 'antd';

import styles from '../style.module.less';
import { ProjectAccountPostAnalysisVO } from '@/services/project-work-analysis';
import { useParams } from '@umijs/max';
import { IndustryType } from '@/utils/const';

export const DataTypeList = ({
  dataTypes,
  typeGroup,
  setTypeGroup,
  selectingTypeId,
  setSelectingTypeId,
}: {
  dataTypes: ProjectAccountPostAnalysisVO[];
  typeGroup: 0 | 1;
  setTypeGroup: React.Dispatch<React.SetStateAction<0 | 1>>;
  selectingTypeId?: string;
  setSelectingTypeId: React.Dispatch<React.SetStateAction<string | undefined>>;
}) => {
  const { industryType: industryTypeParams } = useParams();
  const industryType = Number(industryTypeParams);

  return (
    <>
      <Flex justify="space-between" align="center" style={{ margin: '15px 0' }}>
        <span className={styles.title}>
          {typeGroup === 0 ? (industryType === IndustryType.CAR ? '车型' : '商品') : '视频类型'}
          分布
        </span>
        <Radio.Group
          value={typeGroup}
          className={styles.typeSwitcher}
          onChange={(e) => {
            setTypeGroup(e.target.value);
            setSelectingTypeId(undefined);
          }}
          style={{ whiteSpace: 'nowrap' }}
        >
          <Radio.Button value={0} key={0}>
            {industryType === IndustryType.CAR ? '车型对比' : '商品对比'}
          </Radio.Button>
          <Radio.Button value={1} key={1}>
            视频类型对比
          </Radio.Button>
        </Radio.Group>
      </Flex>

      {dataTypes?.length === 0 && (
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description={<span>暂无{typeGroup === 0 ? '车型' : '视频类型'}数据</span>}
        />
      )}

      <Row gutter={[16, 16]}>
        {dataTypes?.map((item) => {
          const key = item.name;
          return (
            <Col
              key={key}
              xs={{ flex: '50%' }}
              sm={{ flex: '50%' }}
              md={{ flex: '50%' }}
              lg={{ flex: '25%' }}
              xl={{ flex: '20%' }}
              onClick={() => setSelectingTypeId(key)}
            >
              <div
                className={[styles.dataTypeItem, selectingTypeId === key && styles.selected].join(
                  ' ',
                )}
              >
                <Flex>{item.name === '-' ? '其他' : item.name}</Flex>
                <Flex justify="space-between">
                  <span>{item.videoRatio?.toFixed(2)}%</span>
                  <span>{item.interRatio?.toFixed(2)}%</span>
                </Flex>
                <Flex justify="space-between" className={styles.dataTypeNum}>
                  <span>作品量 {item.videoCount}</span>
                  <span>互动量 {item.interCount}</span>
                </Flex>
              </div>
            </Col>
          );
        })}
      </Row>
    </>
  );
};
