import styles from '../style.module.less';
import { Row, Col, Flex } from 'antd';

export const DashBoard = ({
  data,
  total,
  selectingTypeId,
}: {
  data: {
    name: string;
    value1: number | string;
    value2: number | string;
    value3: number | string;
    icon: string;
  }[];
  selectingTypeId?: string;
  total?: number;
}) => {
  return (
    <>
      <span className={styles.title}>
        {selectingTypeId === '-' ? '其他' : selectingTypeId} 视频素材概览
      </span>
      <br />
      <span className={styles.tips}>
        所选时间周期内 本视频类型共记 {total ?? '-'} 个视频 以下为整体分析情况
      </span>
      <Row gutter={[16, 16]}>
        {data?.map((item, index) => {
          const key = `col-${index}`;
          return (
            <Col
              key={key}
              xs={{ flex: '50%' }}
              sm={{ flex: '50%' }}
              md={{ flex: '50%' }}
              lg={{ flex: '25%' }}
              xl={{ flex: '20%' }}
            >
              <Flex
                key={item.name}
                className={styles.dataItem}
                justify="space-between"
                align="self-start"
                vertical
              >
                <Flex justify="space-between" align="center" style={{ width: '100%' }}>
                  <Flex style={{ fontSize: 14 }} align="center">
                    <img src={item.icon} style={{ width: 20, height: 20, marginRight: 4 }} alt="" />
                    总{item.name}
                  </Flex>
                  <span style={{ fontSize: 12 }}>
                    <span>品牌中位数&nbsp;</span>
                    <span style={{ fontWeight: 500, fontSize: 16, textAlign: 'right' }}>
                      {item.value3}
                    </span>
                  </span>
                </Flex>
                <Flex justify="space-between" align="flex-end" style={{ width: '100%' }}>
                  <span style={{ fontWeight: 500, fontSize: 18, textAlign: 'left' }}>
                    {item.value1}
                  </span>
                  <span style={{ fontSize: 12 }}>
                    <span>{selectingTypeId === '-' ? '其他' : selectingTypeId}中位数&nbsp;</span>
                    <span style={{ fontWeight: 500, fontSize: 16, textAlign: 'right' }}>
                      {item.value2}
                    </span>
                  </span>
                </Flex>
                {/* <Row style={{ width: '100%', height: '100%' }} justify="space-between">
                  <Col span={12} className={styles.dataItemCol}>
                    <span>
                      <img
                        src={item.icon}
                        style={{ width: 20, height: 20, marginRight: 4 }}
                        alt=""
                      />
                      总{item.name}
                    </span>
                    <span style={{ fontSize: 18, transform: 'translateY(4px)' }}>
                      {item.value1}
                    </span>
                  </Col>

                  <Col span={12} className={styles.dataItemCol}>
                    <div style={{ width: '100%' }}>
                      <span className={styles.dataItemLabel}>品牌中位数&nbsp;</span>
                      <span className={styles.dataItemValue}>{item.value3}</span>
                    </div>
                    <div style={{ width: '100%' }}>
                      <span className={styles.dataItemLabel}>
                        {selectingTypeId === '-' ? '其他' : selectingTypeId}中位数&nbsp;
                      </span>
                      <span className={styles.dataItemValue}>{item.value2}</span>
                    </div>
                  </Col>
                </Row> */}
              </Flex>
            </Col>
          );
        })}
      </Row>
    </>
  );
};
