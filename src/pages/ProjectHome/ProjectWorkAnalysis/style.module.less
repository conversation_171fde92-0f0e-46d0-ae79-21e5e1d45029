.title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 6px;
}
.tips {
  font-size: 14px;
  color: #95979c;
}
.dataTypeItem {
  height: 93px;
  background-color: #1e5eff05;
  border: 1px solid transparent;
  border-radius: 6px;
  padding: 12px;
  font-size: 14px;
  color: #0e1015;
  font-family: PingFang SC;
  font-weight: 500;
  box-sizing: border-box;
  border: 1.5px solid transparent;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  &.selected {
    border-color: #1e5eff;
    background-color: #edf3ff;
    color: #1e5eff !important;
    span {
      color: #1e5eff !important;
    }
    .dataTypeNum {
      color: #1e5eff;
    }
  }
  &.selected::after {
    width: 20px;
    height: 20px;
    display: block;
    content: '';
    position: absolute;
    background: url(@/assets/wa_video-type-icon.png) no-repeat;
    background-size: 100% auto;
    top: 0;
    right: 0;
  }
  .dataTypeNum {
    color: #64666b;
    font-size: 12px;
  }
}

.dataItem {
  height: 85px;
  background-color: #f3f5fb;
  border-radius: 4px;
  margin: 16px 0 24px 0;
  display: flex;
  padding: 16px 12px;
  box-sizing: border-box;
  .dataItemCol {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
  .dataItemLabel {
    display: inline-block;
    width: 70%;
    height: 100%;
    // margin-right: 8px;
    text-align: right;
    font-size: 12px;
  }
  .dataItemValue {
    display: inline-block;
    font-size: 14px;
    font-weight: 500;
    text-align: right;
    width: 30%;
  }
}

.graphWrapper {
  height: 350px;
  width: 100%;
  margin-bottom: 10px;
}

.typeSwitcher {
  :global(.ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled)) {
    background: #edf3ff !important;
  }
}
