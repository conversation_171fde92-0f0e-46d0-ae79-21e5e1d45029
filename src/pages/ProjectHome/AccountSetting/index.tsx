import { PageContainer } from '@ant-design/pro-components';
import { Button, Col, Flex, Space } from 'antd';
import useProjectId from '@/hooks/useProjectId';
import BreadCrumbSimple from '@/components/ui/breadcrumbSimple';
import TimeFilter from '@/components/ui/timeFilter';
import { getTimeByType } from '@/utils/time';
import { useEffect, useState } from 'react';
import { AnimatePresence, motion } from 'framer-motion';
import { LeftOutlined, RightOutlined } from '@ant-design/icons';
import DataCard from '@/components/dataCard';
import { GetDataCardTrend } from '@/services/data-card';
import { useRequest } from '@umijs/max';
import { useAtomValue } from 'jotai';
import { selectPlatformAtom } from '../atom';
import BasicData from './component/basicData';
import { HideScrollBarRow } from '@/utils/commonStyle';
import PlatformSwitch from '@/components/platformSwitch';
import { useHorizontalDragScroll } from '@/hooks/useHorizontalDragScroll';

const AccountSetting = () => {
  const projectId = useProjectId();
  const [rangeTime, setRangeTime] = useState<(string | undefined)[]>(getTimeByType('day'));
  const [showLeftBtn, setShowLeftBtn] = useState(false);
  const scrollContainerRef = useHorizontalDragScroll<HTMLDivElement>();
  const [hasHorizontalScroll, setHasHorizontalScroll] = useState(false);
  const platform = useAtomValue(selectPlatformAtom);

  const requestFn = (type: number) =>
    GetDataCardTrend({
      projectId,
      platform,
      startDate: rangeTime[0],
      endDate: rangeTime[1],
      type,
    });

  const { data: accountNumData, loading: accountNumDataLoading } = useRequest(
    () => requestFn(101),
    {
      refreshDeps: [rangeTime, platform],
    },
  );

  const { data: accountFollowCountData, loading: accountFollowCountDataLoading } = useRequest(
    () => requestFn(102),
    {
      refreshDeps: [rangeTime, platform],
    },
  );

  const { data: accountDiggCountData, loading: accountDiggCountDataLoading } = useRequest(
    () => requestFn(103),
    {
      refreshDeps: [rangeTime, platform],
    },
  );

  const { data: accountPostCountData, loading: accountPostCountDataLoading } = useRequest(
    () => requestFn(104),
    {
      refreshDeps: [rangeTime, platform],
    },
  );

  const handleScroll = () => {
    if (scrollContainerRef.current) {
      const { scrollLeft } = scrollContainerRef.current;
      setShowLeftBtn(scrollLeft >= 30);
    }
  };

  useEffect(() => {
    const container = scrollContainerRef.current;
    if (container) {
      container.addEventListener('scroll', handleScroll);
    }

    return () => {
      // 清理滚动监听
      if (container) {
        container.removeEventListener('scroll', handleScroll);
      }
    };
  }, []);

  // 检查容器是否有横向滚动条
  const checkScrollBar = () => {
    if (scrollContainerRef.current) {
      const { scrollWidth, clientWidth } = scrollContainerRef.current;
      // 如果 scrollWidth > clientWidth，表示有横向滚动条
      setHasHorizontalScroll(scrollWidth > clientWidth);
    }
  };

  useEffect(() => {
    // 初次检查
    checkScrollBar();

    // 添加 resize 事件监听
    window.addEventListener('resize', checkScrollBar);

    // 清理事件监听
    return () => {
      window.removeEventListener('resize', checkScrollBar);
    };
  }, []);

  const scroll = (width: number) => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: width, behavior: 'smooth' });
    }
  };

  return (
    <PageContainer title={<BreadCrumbSimple breadcrumbs={['平台管理', '账号管理']} />}>
      <div className="rounded-lg bg-white px-4 py-2">
        <Space>
          <TimeFilter value={rangeTime} onChange={(value) => setRangeTime(value)} />
          <PlatformSwitch />
        </Space>
      </div>
      <Flex justify="center" align="center">
        <AnimatePresence>
          {showLeftBtn && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.5 }}
              style={{
                zIndex: '1000',
              }}
            >
              <Button shape="circle" icon={<LeftOutlined />} onClick={() => scroll(-300)} />
            </motion.div>
          )}
        </AnimatePresence>
        <HideScrollBarRow
          gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}
          ref={scrollContainerRef}
          className="py-4"
          wrap={false}
        >
          <Col xs={8} sm={8} md={7} lg={7} xl={8} xxl={5} span={5}>
            <DataCard title="账号总数" data={accountNumData} loading={accountNumDataLoading} />
          </Col>
          <Col xs={8} sm={8} md={7} lg={7} xl={8} xxl={5} span={5}>
            <DataCard
              title="粉丝总数"
              data={accountFollowCountData}
              loading={accountFollowCountDataLoading}
            />
          </Col>
          <Col xs={8} sm={8} md={7} lg={7} xl={8} xxl={5} span={5}>
            <DataCard
              title="获赞总数"
              data={accountDiggCountData}
              loading={accountDiggCountDataLoading}
            />
          </Col>
          <Col xs={8} sm={8} md={7} lg={7} xl={8} xxl={5} span={5}>
            <DataCard
              title="作品总数"
              data={accountPostCountData}
              loading={accountPostCountDataLoading}
            />
          </Col>
        </HideScrollBarRow>
        {hasHorizontalScroll && (
          <Button
            shape="circle"
            icon={<RightOutlined />}
            style={{
              zIndex: '1000',
            }}
            onClick={() => scroll(300)}
          />
        )}
      </Flex>
      <BasicData projectId={projectId} rangeTime={rangeTime} />
    </PageContainer>
  );
};

export default AccountSetting;
