import { UpdateObjectTags } from '@/services/tag';
import { CommonTagGroup } from '@/services/typings';
import { ActionType } from '@ant-design/pro-components';
import { useAccess } from '@umijs/max';
import { Empty, Input, message, Modal, Tag } from 'antd';
import { PropsWithChildren, useState } from 'react';

type Tags = {
  id: number;
  name: string;
  groupId: number;
  pos: number;
};

type DeleteTagModalProps = {
  userId: number;
  tagGroups: CommonTagGroup[];
  actionRef: React.MutableRefObject<ActionType | undefined>;
  selectedTags: Tags[];
};

const UpdateTagModal = (props: PropsWithChildren<DeleteTagModalProps>) => {
  const { userId, tagGroups, actionRef, selectedTags } = props;
  const selectedIds = selectedTags.map((tag) => tag.id);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [searchKey, setSearchKey] = useState('');
  const [selectedTagIds, setSelectedTagIds] = useState<number[]>(selectedIds);
  const access = useAccess();
  const isAdmin = access.isAdmin;

  const tagGroupFilter = searchKey
    ? tagGroups.filter((tagGroup) => tagGroup.tags.some((tag) => tag.name.includes(searchKey)))
    : tagGroups;

  const showModal = () => {
    isAdmin && setIsModalOpen(true);
  };

  const handleOk = async () => {
    setIsModalOpen(false);
    await UpdateObjectTags({
      type: 3,
      objectId: String(userId),
      tagIds: selectedTagIds,
    });
    message.success('修改标签成功');
    actionRef.current?.reload();
  };

  const handleCancel = () => {
    setSelectedTagIds(selectedIds);
    setIsModalOpen(false);
  };

  const handleChange = (tag: number, checked: boolean) => {
    const nextSelectedTags = checked
      ? [...selectedTagIds, tag]
      : selectedTagIds.filter((t) => t !== tag);

    setSelectedTagIds(nextSelectedTags);
  };

  return (
    <>
      <div onClick={showModal}>{props.children || <span>编辑标签</span>}</div>
      <Modal
        title={'编辑标签'}
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        destroyOnHidden
      >
        <Input
          placeholder="输入关键词检索标签和标签组"
          onChange={(e) => setSearchKey(e.target.value)}
        />

        <div style={{ marginTop: 10, maxHeight: 220, overflow: 'auto' }}>
          {tagGroupFilter.length ? (
            tagGroupFilter.map((group) => {
              return (
                <div key={group.id} style={{ marginBottom: 15 }}>
                  <span>{group.name}</span>
                  <div className="mt-1">
                    {group.tags.map((tag) => (
                      <Tag.CheckableTag
                        key={tag.id}
                        style={{ border: '1px solid #DBDBDB' }}
                        className="py-[2px]"
                        checked={selectedTagIds.includes(tag.id)}
                        onChange={(checked) => handleChange(tag.id, checked)}
                      >
                        {tag.name}
                      </Tag.CheckableTag>
                    ))}
                  </div>
                </div>
              );
            })
          ) : (
            <Empty />
          )}
        </div>
      </Modal>
    </>
  );
};

export default UpdateTagModal;
