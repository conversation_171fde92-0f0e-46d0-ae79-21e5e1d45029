import BlueVip from '@/assets/blue-vip.png';
import DefaultAvatar from '@/assets/default-avatar.png';
import DataFilter from '@/components/dataFilter';
import DynamicTree from '@/components/dynamicTree';
import ExportButton from '@/components/exportButton';
import TagsShow from '@/components/tags/tagsShow';
import useLiveAfkFG from '@/hooks/fg/useLiveAfkFG';
import useGetTeamFieldList from '@/hooks/useFields';
import { usePollingExport } from '@/hooks/usePollingExport';
import {
  AccountBasicItem,
  AccountSettingPage,
  AccountUserChangeTeam,
  AccountUserDelete,
  AccountUserUpdate,
  ExportAccount,
} from '@/services/setting';
import { GetTagGroups, TagsType } from '@/services/tag';
import { CommonTagGroup } from '@/services/typings';
import { proTableRequestAdapter } from '@/utils';
import { formatNum, initDataFilter, STICKY_OFFSETHEADER, transferParams } from '@/utils/common';
import { renderCustomColumns } from '@/components/renderCustomColumns';
import { formatSecond } from '@/utils/time';
import { FilterFilled, PlusOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns, ProFormInstance } from '@ant-design/pro-components';
import {
  ModalForm,
  ProCard,
  ProFormSelect,
  ProFormText,
  ProTable,
} from '@ant-design/pro-components';
import { NavLink, useAccess, useParams } from '@umijs/max';
import { useAsyncEffect, useGetState, useSetState } from 'ahooks';
import { Avatar, Button, message, Popconfirm, Select, Space, Tooltip } from 'antd';
import copy from 'copy-to-clipboard';
import { omit } from 'lodash-es';
import { useEffect, useRef, useState } from 'react';
import { TagSelectModal } from '../../PotentialUser/tagModal';
import UpdateTagModal from './updateTagModal';
import { selectPlatformAtom } from '../../atom';
import { useAtomValue } from 'jotai';
import {
  proTableOptionsConfig,
  proTablePaginationConfig,
  proTableSearchConfig,
} from '@/utils/proTableConfig';
import AggregateQueryInput from '@/components/aggregateQueryInput';
import { FilterDropdownProps } from 'antd/es/table/interface';
import FilterDropdownRadio from '@/components/filterDropdownRadio';
import { Copy } from 'lucide-react';
import { getSumColumnsWidth } from '@/utils/table';
import { BlueVipFlag } from '@/services/common';
import { AccountPlatformKeysMap, PlatForm } from '@/utils/platform';

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 18 },
};

type BasicDataProps = {
  projectId?: string;
  rangeTime: (string | undefined)[];
};

const BasicData = ({ projectId, rangeTime }: BasicDataProps) => {
  const access = useAccess();
  const { projectKey, industryType = '1' } = useParams();
  const isAdmin = access.isAdmin;
  const actionRef = useRef<ActionType>();
  const platform = useAtomValue(selectPlatformAtom);
  const [blueVipFlag, setBlueVipFlag, getBlueVipFlag] = useGetState<BlueVipFlag | undefined>();
  const [accountStatus, setAccountStatus, getAccountStatus] = useGetState<number | undefined>();
  const afkState = useLiveAfkFG(projectId);
  const optionsRef = useRef<{ value: string; label: string }[]>([
    { value: 'nickname', label: '账号名称' },
  ]);

  useEffect(() => {
    optionsRef.current = [
      { value: 'nickname', label: '账号名称' },
      ...(afkState
        ? [
            { value: 'liveAfkDuration', label: '空播时长' },
            { value: 'liveAfkRate', label: '空播率' },
          ]
        : []),
    ];
  }, [afkState]);

  const defaultColumns: ProColumns<AccountBasicItem>[] = [
    {
      dataIndex: 'id',
      hideInTable: true,
      hideInSearch: true,
    },
    {
      dataIndex: 'uid',
      hideInTable: true,
      hideInSearch: true,
    },
    {
      dataIndex: 'teamCode',
      hideInTable: true,
      hideInSearch: true,
    },
    {
      title: '聚合查询',
      dataIndex: 'aggregateQuery',
      hideInTable: true,
      formItemProps: {
        label: null,
      },
      search: {
        transform: (value: string[]) => {
          if (value.length > 0) {
            if (value[0] === 'liveAfkDuration') {
              return {
                [value[0]]: value[1] ? Number(value[1]) * 60 : undefined,
              };
            } else if (value[0] === 'liveAfkRate') {
              return {
                [value[0]]: value[1] ? Number(value[1]) / 100 : undefined,
              };
            } else {
              return {
                [value[0]]: value[1],
              };
            }
          }
        },
      },
      renderFormItem: () => {
        return <AggregateQueryInput selectOptions={optionsRef.current} />;
      },
    },
    {
      title: '动态维度筛选',
      dataIndex: 'teamCodeList',
      hideInTable: true,
      formItemProps: {
        label: null,
      },
      renderFormItem: () => {
        return <DynamicTree actionRef={actionRef} />;
      },
    },
    {
      title: '账号信息',
      dataIndex: 'nickname',
      width: 300,
      ellipsis: true,
      hideInSearch: true,
      align: 'left',
      fixed: 'left',
      filterIcon: () => (
        <FilterFilled style={{ color: getBlueVipFlag() !== undefined ? '#3B77F6' : undefined }} />
      ),
      filterDropdown: (props: FilterDropdownProps) => {
        return (
          <FilterDropdownRadio
            title="是否是蓝V"
            options={[
              { label: '是', value: BlueVipFlag.Vip },
              { label: '否', value: BlueVipFlag.NoVip },
            ]}
            filterDropdownProps={props}
            setValueChange={setBlueVipFlag}
          />
        );
      },
      render: (text, record) => {
        return (
          <Space>
            <NavLink
              to={`/project/${projectKey}/${industryType}/platform/account-detail?platformKey=${
                AccountPlatformKeysMap[record.platform]
              }&accountId=${record.accountId}`}
            >
              <Avatar
                size="small"
                src={record.avatar || DefaultAvatar}
                style={{ width: '16px', height: '16px' }}
              />
              &nbsp;
              <span>{record.nickname}</span>
            </NavLink>

            <Tooltip title="复制账号ID">
              <Copy
                size={13}
                className="mt-1 hover:cursor-pointer"
                onClick={() => {
                  if (record.showAccountId) {
                    copy(record.showAccountId);
                    message.success('账号ID已复制到剪切板');
                  }
                }}
              />
            </Tooltip>
            {record.blueVipFlag ? <img src={BlueVip} width={18} /> : null}
          </Space>
        );
      },
    },
    {
      title: '账号标签',
      dataIndex: 'tagIds',
      align: 'left',
      width: 150,
      fieldProps: {
        placeholder: '账号标签',
      },
      formItemProps: {
        label: null,
      },
      ellipsis: true,
      render(_, record) {
        const filterTags = record.tags?.filter((item) => item !== null);
        if (filterTags.length > 0) {
          return (
            <UpdateTagModal
              tagGroups={tagResRef.current || []}
              userId={record.id}
              selectedTags={filterTags}
              actionRef={actionRef}
            >
              <TagsShow tags={record.tags} />
            </UpdateTagModal>
          );
        } else {
          return (
            <TagSelectModal
              userIds={[record.id]}
              tagGroups={tagResRef.current || []}
              onKeywordGroupsChange={() => actionRef.current?.reload()}
              type={3}
            >
              <Button icon={<PlusOutlined />} size="small" />
            </TagSelectModal>
          );
        }
      },
      renderFormItem: () => {
        return (
          <Select
            mode="multiple"
            allowClear
            maxTagCount="responsive"
            options={tagResRef?.current?.map((item) => {
              return {
                label: item?.name,
                options: item?.tags.map((tag) => {
                  return {
                    label: tag?.name,
                    value: tag?.id,
                  };
                }),
              };
            })}
          />
        );
      },
    },
    {
      title: '粉丝增量',
      dataIndex: 'followerGrowthCount',
      align: 'right',
      width: 90,
      hideInSearch: true,
      render: (text, record) => formatNum(record.followerGrowthCount),
      sorter: true,
    },
    {
      title: '粉丝总数',
      dataIndex: 'followerCount',
      width: 90,
      hideInSearch: true,
      align: 'right',
      render: (text, record) => formatNum(record.followerCount),
      sorter: true,
    },
    {
      title: '作品总数',
      dataIndex: 'postCount',
      width: 90,
      hideInSearch: true,
      align: 'right',
      render: (text, record) => formatNum(record.postCount),
      sorter: true,
    },
    {
      title: '作品总播放量',
      dataIndex: 'postPlayCount',
      width: 120,
      hideInSearch: true,
      align: 'right',
      render: (text, record) => formatNum(record.postPlayCount),
      sorter: true,
    },
    {
      title: '作品新增播放量',
      dataIndex: 'postNewPlayCount',
      width: 140,
      hideInSearch: true,
      align: 'right',
      render: (text, record) => formatNum(record.postNewPlayCount),
      sorter: true,
    },
    {
      title: '作品发布数',
      dataIndex: 'postPublishCount',
      align: 'right',
      width: 120,
      hideInSearch: true,
      render: (text, record) => formatNum(record.postPublishCount),
      sorter: true,
    },
    {
      title: '作品获赞数',
      dataIndex: 'favoritedCount',
      width: 110,
      hideInSearch: true,
      align: 'right',
      render: (text, record) => formatNum(record.favoritedCount),
      sorter: true,
    },
    {
      title: '作品评论数',
      dataIndex: 'postCommentCount',
      width: 110,
      hideInSearch: true,
      align: 'right',
      render: (text, record) => formatNum(record.postCommentCount),
      sorter: true,
    },
    {
      title: '作品收藏数',
      dataIndex: 'postCollectCount',
      width: 110,
      hideInSearch: true,
      align: 'right',
      render: (text, record) => formatNum(record.postCollectCount),
    },
    {
      title: '作品分享数',
      dataIndex: 'postShareCount',
      width: 110,
      hideInSearch: true,
      align: 'right',
      render: (text, record) => formatNum(record.postShareCount),
      sorter: true,
    },
    {
      title: '直播场次',
      dataIndex: 'liveCount',
      width: 90,
      hideInSearch: true,
      align: 'right',
      render: (text, record) => formatNum(record.liveCount),
      sorter: true,
    },
    {
      title: '直播时长',
      dataIndex: 'liveDuration',
      width: 150,
      hideInSearch: true,
      align: 'right',
      render: (text, record) => formatSecond(record.liveDuration),
      sorter: true,
    },
    {
      title: '检测时长',
      dataIndex: 'liveAfkCheckDuration',
      align: 'right',
      tooltip: '用于检测空播挂播的片段时长',
      width: 150,
      hideInSearch: true,
      render: (text, record) => formatSecond(record.liveAfkCheckDuration),
    },
    {
      title: '空播时长',
      dataIndex: 'liveAfkDuration',
      align: 'right',
      tooltip: '画面无变化且讲解内容和直播无关的时长',
      width: 150,
      hideInSearch: true,
      render: (text, record) => formatSecond(record.liveAfkDuration, '未检测'),
      sorter: true,
    },
    {
      title: '空播率',
      dataIndex: 'liveAfkRate',
      align: 'right',
      tooltip: '空播时长/实际直播时长，由于实际直播时长需要次日更新，所以空播率也需要次日更新',
      width: 150,
      hideInSearch: true,
      render: (text, record) => {
        return (
          <span>
            {record.liveAfkRate
              ? (Number(record.liveAfkRate) * 100).toFixed(2) + '%'
              : record.liveAfkDuration
                ? '未判定'
                : '未检测'}
          </span>
        );
      },
    },
    {
      title: '轻微空播挂播场次',
      dataIndex: 'liveMinorAfkCount',
      align: 'right',
      tooltip: '轻微空播挂播场次指20%≤空播率＜50%的直播场次',
      width: 180,
      hideInSearch: true,
      sorter: true,
    },
    {
      title: '严重空播挂播场次',
      dataIndex: 'liveSeriousAfkCount',
      align: 'right',
      tooltip: '严重空播挂播场次指空播率≥50%的直播场次',
      width: 180,
      hideInSearch: true,
      sorter: true,
    },
    {
      title: '直播观看人次',
      dataIndex: 'liveViewTime',
      align: 'right',
      width: 120,
      hideInSearch: true,
      render: (text, record) => formatNum(record.liveViewTime),
      sorter: true,
    },
    {
      title: '直播观看人数',
      dataIndex: 'liveViewCount',
      align: 'right',
      width: 120,
      hideInSearch: true,
      render: (text, record) => formatNum(record.liveViewCount),
      sorter: true,
    },
    {
      title: '直播点赞次数',
      dataIndex: 'liveDiggCount',
      align: 'right',
      width: 120,
      hideInSearch: true,
      render: (text, record) => formatNum(record.liveDiggCount),
      sorter: true,
    },
    {
      title: '直播评论量',
      dataIndex: 'liveCommentCount',
      align: 'right',
      width: 110,
      hideInSearch: true,
      render: (text, record) => formatNum(record.liveCommentCount),
      sorter: true,
    },
    {
      title: '直播粉丝增量',
      dataIndex: 'liveFollowerGrowthCount',
      align: 'right',
      width: 120,
      hideInSearch: true,
      render: (text, record) => formatNum(record.liveFollowerGrowthCount),
      sorter: true,
    },
    {
      title: '线索总数',
      dataIndex: 'clueCount',
      align: 'right',
      width: 120,
      hideInSearch: true,
      render: (text, record) => formatNum(record.clueCount),
      sorter: true,
    },
    {
      title: '账号状态',
      dataIndex: platform === PlatForm.Douyin ? 'status' : 'hostingGrantStatus',
      width: 100,
      valueType: 'select',
      align: 'left',
      hideInSearch: true,
      filterIcon: () => (
        <FilterFilled style={{ color: getAccountStatus() !== undefined ? '#3B77F6' : undefined }} />
      ),
      filterDropdown: (props: FilterDropdownProps) => {
        return (
          <FilterDropdownRadio
            title="账号状态"
            options={[
              { label: '待授权', value: 0 },
              { label: '正常', value: 1 },
              { label: '已失效', value: 2 },
            ]}
            filterDropdownProps={props}
            setValueChange={setAccountStatus}
          />
        );
      },
      valueEnum: {
        2: {
          text: '已失效',
          color: 'red',
        },
        1: {
          text: '正常',
          color: 'green',
        },
        0: {
          text: '待授权',
          color: 'gray',
        },
      },
    },
    {
      title: '操作',
      dataIndex: 'options',
      align: 'left',
      hideInSearch: true,
      hideInTable: false,
      fixed: 'right',
      width: 120,
      render: (text, record, _, action) => {
        return (
          <Space>
            <ModalForm
              trigger={<a>编辑</a>}
              layout={'horizontal'}
              title="账号编辑"
              width="500px"
              modalProps={{
                destroyOnHidden: true,
              }}
              {...formItemLayout}
              initialValues={record}
              onFinish={async ({ teamCode }) => {
                const res = await AccountUserUpdate({
                  accountUserId: record.id,
                  projectId: projectId,
                  teamCode,
                });
                if (res.data) {
                  message.success('修改成功');
                  await action?.reload();
                } else {
                  message.error('修改失败');
                }
                return true;
              }}
            >
              <ProFormText width="md" label="平台" name="platformName" readonly />
              <ProFormText width="md" label="平台UID" name="showAccountId" readonly />
              <ProFormText width="md" label="账号昵称" name="nickname" readonly />
              <ProFormSelect
                name="teamCode"
                label="切换团队"
                request={async () => {
                  const res = await AccountUserChangeTeam({ projectId });
                  return res?.data;
                }}
                fieldProps={{
                  fieldNames: {
                    label: 'name',
                  },
                  showSearch: true,
                }}
              />
            </ModalForm>
            <Popconfirm
              title={'是否确认删除'}
              onConfirm={async () => {
                await AccountUserDelete([record.id]);
                message.success('删除成功');
                await action?.reload();
              }}
              key={'delete'}
            >
              <a>删除</a>
            </Popconfirm>
          </Space>
        );
      },
    },
  ];

  const formRef = useRef<ProFormInstance>();
  const [columns, setColumns] = useState<ProColumns<AccountBasicItem>[]>(defaultColumns);
  const firstAddColumns = useRef(false);
  const filedIdArrRef = useRef<string[]>([]);
  const [dataFilterState, setDataFilterState] = useSetState(initDataFilter);
  const [selectRow, setSelectRow] = useState<any[]>([]);
  const [tagRes, setTagRes] = useState<CommonTagGroup[]>();
  const tagResRef = useRef<CommonTagGroup[] | null | undefined>(null);
  const teamFieldListRef = useGetTeamFieldList(projectId);
  const paramsRef = useRef<any>({});
  const { pollingExport, percent, pollingLoading } = usePollingExport();
  const [isEmptyTable, setIsEmptyTable] = useState(false);

  useAsyncEffect(async () => {
    const res = await GetTagGroups(TagsType.accountTag, { projectId });
    tagResRef.current = res.data;
    setTagRes(res.data);
  }, []);

  const handleExportAccount = async () => {
    const omitPageParams = omit(paramsRef.current, ['pageSize', 'current']);
    const res = await ExportAccount({
      ...omitPageParams,
    });
    if (res.data) {
      pollingExport({ taskId: res.data });
    } else {
      message.error('导出失败,请重试');
    }
  };

  useEffect(() => {
    const afkColumns = [
      'liveAfkCheckDuration',
      'liveAfkDuration',
      'liveAfkRate',
      'liveMinorAfkCount',
      'liveSeriousAfkCount',
    ];
    // 这里条件的原因是下面ProTable的postData会修改columns列 要保证排在动态列设置后再修改一次 避免修改失效
    if (firstAddColumns.current) {
      const newColumns = columns.map((item) => {
        if (afkColumns.includes(item.dataIndex as string)) {
          return {
            ...item,
            hidden: !afkState,
            // search 会有一些自定义 transform 配置 所以需要继承下来
            search: afkState ? item.search : false,
          };
        }
        return item;
      }) as ProColumns<AccountBasicItem>[];
      setColumns(newColumns);
    }
  }, [firstAddColumns.current, afkState, platform]);

  // 初始化时，表头文字会挤压目前只想到这个方式解决，使用 Loading 状态隐藏的话初始化还是会看到挤压
  // .ant-spin-nested-loading:has(.ant-spin.ant-spin-spinning) .ant-table-thead {
  //   opacity: 0;
  // }
  useEffect(() => {
    const header = document.querySelector('.ant-table-header');
    if (header) {
      header.classList.add('fix-init-width');
      setTimeout(() => {
        header.classList.remove('fix-init-width');
      }, 1000);
    }
  }, []);

  return (
    <ProCard>
      <ProTable<AccountBasicItem>
        columns={columns}
        actionRef={actionRef}
        formRef={formRef}
        params={{
          projectId,
          startTime: rangeTime[0],
          endTime: rangeTime[1],
          postPlayCount: dataFilterState.videoFilter ? dataFilterState.postPlayCount : undefined,
          liveDuration: dataFilterState.liveFilter
            ? Number(dataFilterState.liveDuration) * 60
            : undefined,
          platform,
          blueVipFlag,
          status: platform === PlatForm.Douyin ? accountStatus : undefined,
          hostingGrantStatus: platform === PlatForm.XiaoHongShu ? accountStatus : undefined,
        }}
        onReset={() => {
          setDataFilterState(initDataFilter);
        }}
        ghost
        sticky={{ offsetHeader: STICKY_OFFSETHEADER }}
        tableClassName="custom-table"
        scroll={{ x: isEmptyTable ? getSumColumnsWidth(columns) : 'max-content' }}
        rowSelection={
          isAdmin
            ? {
                type: 'checkbox',
                alwaysShowAlert: true,
                preserveSelectedRowKeys: true,
                onChange: (_, selectedRows) => {
                  setSelectRow(selectedRows);
                },
              }
            : false
        }
        postData={(data: AccountBasicItem[]) => {
          // 判断表格数据是否为空
          data.length === 0 ? setIsEmptyTable(true) : setIsEmptyTable(false);
          if (!firstAddColumns.current && data.length > 0) {
            renderCustomColumns(teamFieldListRef.current, true).then(
              ({ customColumns, fieldIdArr }) => {
                filedIdArrRef.current = fieldIdArr;
                const newColumns = [...columns, ...customColumns] as ProColumns<AccountBasicItem>[];
                setColumns(newColumns);
              },
            );
            firstAddColumns.current = true;
          }
          // 将teamFields内的字段都抽出来
          const formatData = data.map((item) => {
            const res = { ...item };
            const teamFields = res.teamFieldList;
            if (teamFields) {
              teamFields.forEach((field) => {
                (res as any)[field.fieldId] = field.value;
              });
            }
            return res;
          });
          return formatData;
        }}
        beforeSearchSubmit={(params) => {
          // 查找前清空已选项 避免打标误操作
          actionRef.current?.clearSelected?.();
          const fieldList = transferParams(params, filedIdArrRef.current);
          if (fieldList.length > 0) {
            return { ...params, fieldList };
          }
          return params;
        }}
        request={(params, sorter, filter) => {
          paramsRef.current = params;
          return proTableRequestAdapter(params, sorter, filter, AccountSettingPage);
        }}
        search={{ ...proTableSearchConfig }}
        options={{ ...proTableOptionsConfig }}
        pagination={{ ...proTablePaginationConfig }}
        headerTitle={
          <DataFilter dataFilterState={dataFilterState} setDataFilterState={setDataFilterState} />
        }
        toolBarRender={() => [
          <ExportButton
            exportFn={() => handleExportAccount()}
            loading={pollingLoading}
            percent={percent}
            key="export"
          />,
        ]}
        columnsState={{
          persistenceKey: 'AccountSetting-Table-Columns',
          persistenceType: 'localStorage',
        }}
        rowKey="id"
        dateFormatter="string"
        tableAlertRender={({ selectedRowKeys }) => {
          if (isAdmin) {
            return (
              <Space size={24}>
                <span>已选 {selectedRowKeys.length} 项</span>
                <TagSelectModal
                  userIds={selectRow.map((item) => item.id)}
                  tagGroups={tagRes || []}
                  onKeywordGroupsChange={() => actionRef.current?.reload()}
                  type={3}
                >
                  <a>批量添加标签</a>
                </TagSelectModal>
              </Space>
            );
          } else {
            return false;
          }
        }}
      />
    </ProCard>
  );
};

export default BasicData;
