import { Modal, Form, message } from 'antd';
import { useEffect, useState } from 'react';
import ChatBg from '@/assets/chat-background.jpg';
import { EditRowValue } from './intentionTable';
import { IntentionTableConfig, PostLeadsCustomer } from '@/services/message-robot';
import ModalLeftForm from './modalLeftForm';
import ModalRightPreview from './modalRightPreview';
import { transformBackToTemplate } from '../customerService/transformDefault';

type IntentionTableModalProps = {
  open: boolean;
  onCancel: () => void;
  onOk: () => void;
  initialValues?: EditRowValue;
  projectId?: string;
};

const keyMapChinese: Record<keyof IntentionTableConfig, string> = {
  openingTalk: '开场白',
  intentionCarSeries: '意向车系',
  intentionCity: '意向城市',
  driveWillingness: '试驾意愿',
  contactWay: '联系方式',
  salutation: '称呼',
  purchaseTime: '购车时间',
  concludingRemark: '结束语',
};

export default function IntentionTableModal({
  open,
  onCancel,
  onOk,
  initialValues,
  projectId,
}: IntentionTableModalProps) {
  const [form] = Form.useForm();
  const [preview, setPreview] = useState<EditRowValue | undefined>(initialValues);
  const [formValues, setFormValues] = useState<any>(initialValues?.data);

  useEffect(() => {
    if (open && initialValues) {
      form.setFieldsValue(initialValues.data);
      setPreview(initialValues);
      setFormValues(initialValues.data);
    }
  }, [form, initialValues, open]);

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      title={`编辑「${preview?.key ? keyMapChinese[preview.key] : ''}」`}
      open={open}
      onCancel={handleCancel}
      onOk={() => form.submit()}
      width={1000}
      destroyOnHidden
      className="edit-intention-modal"
    >
      <div className="flex gap-6 border-t">
        <div className="no-scrollbar h-[700px] w-2/3 overflow-hidden overflow-y-auto border-r pr-5 pt-4">
          <Form
            form={form}
            layout="horizontal"
            labelAlign="left"
            labelCol={{ span: 4 }}
            wrapperCol={{ span: 20 }}
            initialValues={initialValues?.data}
            onFinish={async (values) => {
              const specialKeys = ['intentionCarSeries', 'intentionCity', 'purchaseTime'];

              // 如果是特殊的 key，需要对 responseContent 进行模板转换
              if (specialKeys.includes(preview?.key as string) && values.responseContent) {
                values.responseContent = transformBackToTemplate(values.responseContent);
              }
              const res = await PostLeadsCustomer({
                projectId,
                updateType: preview?.key as keyof IntentionTableConfig,
                leadsCustomerConfig: {
                  [preview?.key as keyof IntentionTableConfig]: { ...preview?.data, ...values },
                },
              });
              if (res.code === 0) {
                message.success('提交成功');
                onOk();
              } else {
                message.error('提交失败');
              }
            }}
            onValuesChange={(_, allValues) => {
              setFormValues(allValues);
            }}
          >
            <ModalLeftForm
              preview={preview}
              form={form}
              formValues={formValues}
              setFormValues={setFormValues}
            />
          </Form>
        </div>

        <div className="w-1/3 p-2 pt-4">
          <div className="mb-2 text-base font-medium">效果示例</div>
          <div className="relative my-5 h-[620px] w-full overflow-hidden rounded-lg shadow-xl">
            <img
              src={ChatBg}
              alt="chat-bg"
              className="absolute inset-0 h-full w-full overflow-hidden object-cover"
            />

            <div className="no-scrollbar absolute inset-0 bottom-10 top-14 overflow-y-auto p-4">
              <div className="mb-4 text-center text-xs text-gray-500">上午11:35</div>
              <ModalRightPreview preview={preview} formValues={formValues} form={form} />
            </div>
          </div>
        </div>
      </div>
    </Modal>
  );
}
