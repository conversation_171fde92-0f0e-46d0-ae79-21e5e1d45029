import { ProColumns, ProTable } from '@ant-design/pro-components';
import { Button, Input, message, Switch } from 'antd';
import KnowledgeTableModal from './knowledgeTableModal';
import { useEffect, useState } from 'react';
import {
  KnowledgeItem,
  KnowledgeTableCofig,
  PostActiveGuide,
  PostActiveGuideSwitch,
  RobotConfig,
} from '@/services/message-robot';
import useProjectId from '@/hooks/useProjectId';
import { cn } from '@/lib/utils';

export type KnowledgeTableProps = {
  config?: KnowledgeTableCofig;
  status?: number;
  refreshConfig: () => Promise<RobotConfig | undefined>;
};

export default function KnowledgeTable({ config, status, refreshConfig }: KnowledgeTableProps) {
  const projectId = useProjectId();
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [inputValue, setInputValue] = useState(config?.guideTitle);

  useEffect(() => {
    setInputValue(config?.guideTitle);
  }, [config?.guideTitle]);

  const columns: ProColumns<KnowledgeItem>[] = [
    {
      dataIndex: 'sort',
      hideInTable: true,
    },
    {
      title: '序号',
      width: 80,
      render: (_, record, index) => index + 1,
    },
    {
      title: '用户问题',
      dataIndex: 'question',
      width: 200,
    },
    {
      title: '回复答案',
      dataIndex: 'answer',
      width: 200,
    },
  ];

  const handleEditKnowledge = async (values: any) => {
    const res = await PostActiveGuide({
      projectId,
      status: 1,
      activeGuideConfig: {
        ...config,
        questionList: values,
      },
    });
    if (res.code === 0) {
      setEditModalOpen(false);
      message.success('提交成功');
      await refreshConfig();
    } else {
      message.error('提交失败');
    }
  };

  const onSwitchChange = async (checked: boolean) => {
    await PostActiveGuideSwitch({
      projectId,
      status: checked ? 1 : 0,
    });
    await refreshConfig();
  };

  const onChangeGuideTitle = async () => {
    const res = await PostActiveGuide({
      projectId,
      status: status || 0,
      activeGuideConfig: {
        ...config,
        guideTitle: inputValue,
      },
    });
    if (res.code === 0) {
      message.success('更新成功');
      await refreshConfig();
    } else {
      message.error('更新失败');
    }
  };

  return (
    <>
      <div className="mb-2 mt-3 text-base font-medium">主动引导</div>
      <div className="w-full rounded-md bg-new-media-blue-100 p-4">
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            <span className="text-base font-medium">用户产生行为主动引导提问</span>
            <Switch value={Boolean(status)} onChange={onSwitchChange} />
          </div>
        </div>
        <div className="mt-4 text-muted-foreground">
          用户关注后，或进入私信后5min未发问，则会自动向用户发送以下列表中的聚合推荐问题列表。1天只发送1次（以发送的时间开始计算，一天以发送的24小时后计算）
        </div>
        <div className="mt-4">
          <div className="flex items-center gap-2">
            <span className="text-nowrap">引导文案</span>
            <Input
              defaultValue={config?.guideTitle}
              className="flex-1"
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              maxLength={50}
              allowClear
              showCount
              disabled={!status}
            />
            <Button type="primary" onClick={onChangeGuideTitle} disabled={!status}>
              更新
            </Button>
            <Button onClick={() => setInputValue(config?.guideTitle || '')} disabled={!status}>
              取消
            </Button>
          </div>
        </div>
      </div>

      <ProTable<KnowledgeItem>
        columns={columns}
        dataSource={config?.questionList}
        rowKey={(record) => `${record.sort}-${record.question}-${record.answer}`}
        search={false}
        options={false}
        pagination={false}
        ghost
        tableClassName={cn('custom-table', {
          'cursor-not-allowed pointer-events-none opacity-60': !status,
        })}
        headerTitle={
          <div className="flex items-center gap-3">
            <div className="text-sm">知识列表 ({config?.questionList?.length || 0}/6)</div>
            <div className="text-sm font-normal text-muted-foreground">知识列表上限为6条</div>
          </div>
        }
        toolBarRender={() => [
          <Button
            key="edit"
            disabled={!status}
            onClick={() => {
              setEditModalOpen(true);
            }}
          >
            编辑知识
          </Button>,
        ]}
      />

      <KnowledgeTableModal
        open={editModalOpen}
        onCancel={() => setEditModalOpen(false)}
        onOk={handleEditKnowledge}
        initialValues={config?.questionList}
        guideTitle={config?.guideTitle}
      />
    </>
  );
}
