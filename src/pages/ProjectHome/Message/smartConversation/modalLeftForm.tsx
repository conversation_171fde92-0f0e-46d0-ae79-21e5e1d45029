import { Button, Checkbox, Form, FormInstance, Input } from 'antd';
import { EditRowValue } from './intentionTable';
import { DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { SortableItem } from './components/sortableItem';
import { cn } from '@/lib/utils';
import ImageUpload from '@/pages/PortalProjectList/ImageUpload';
import { uploadBase64Image } from '@/utils/oss';
import { message } from 'antd';

type RenderModalLeftFormProps = {
  preview?: EditRowValue;
  form: FormInstance;
  formValues: any;
  setFormValues: (values: any) => void;
};

type CarSeriesListProps = {
  fields: any[];
  form: FormInstance;
  onRemove: (name: number) => void;
  onAdd: (value: any) => void;
  type: 'car' | 'time';
};

const DndSortList = ({ fields, form, onRemove, onAdd, type }: CarSeriesListProps) => {
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    }),
  );

  const placeholderText = type === 'car' ? '请输入车型' : '请输入时间';
  const titleText = type === 'car' ? '车型' : '时间';
  const chineseNumbers = ['一', '二', '三', '四', '五'];

  return (
    <div className="rounded-md bg-[#F5F5F5] p-3 shadow-sm">
      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragEnd={(event) => {
          const { active, over } = event;
          if (over && active.id !== over.id) {
            const oldIndex = fields.findIndex((item) => item.key === active.id);
            const newIndex = fields.findIndex((item) => item.key === over.id);
            const currentValues = form.getFieldValue('guideCardConfig');
            const newValues = arrayMove(currentValues, oldIndex, newIndex);
            form.setFieldsValue({
              guideCardConfig: newValues,
            });
          }
        }}
      >
        <SortableContext
          items={fields.map((field) => field.key)}
          strategy={verticalListSortingStrategy}
        >
          {fields.map((field, index) => (
            <SortableItem key={field.key} id={field.key}>
              <div className="flex-none">
                {titleText}
                {chineseNumbers[index]}
              </div>
              <Form.Item
                name={[field.name, 'question']}
                noStyle
                rules={[{ required: true, message: placeholderText }]}
              >
                <Input placeholder={placeholderText} maxLength={14} showCount variant="outlined" />
              </Form.Item>
              <div
                className={cn(
                  fields.length <= 2
                    ? 'cursor-not-allowed text-gray-400'
                    : 'cursor-pointer text-gray-600 hover:text-gray-600',
                )}
                onClick={() => fields.length > 2 && onRemove(field.name)}
              >
                <DeleteOutlined />
              </div>
            </SortableItem>
          ))}
        </SortableContext>
      </DndContext>
      <Button
        block
        icon={<PlusOutlined />}
        onClick={() => onAdd({ question: '', sort: fields.length + 1 })}
        disabled={fields.length >= 5}
      >
        添加{titleText} ({fields.length}/5)
      </Button>
      <div className="mt-2 text-xs text-gray-500">至少添加两个选项</div>
    </div>
  );
};

export default function ModalLeftForm({
  preview,
  form,
  formValues,
  setFormValues,
}: RenderModalLeftFormProps) {
  const clickUseDefaultContent = (field: string, defaultContent: string) => {
    form.setFieldValue(field, defaultContent);
    setFormValues({ ...formValues, [field]: defaultContent });
    form.validateFields([field]);
  };

  const renderDefaultContent = (field: string, defaultContent: string) => {
    return (
      <div
        className="cursor-pointer text-sm text-blue-500 hover:text-blue-600"
        onClick={() => {
          clickUseDefaultContent(field, defaultContent);
        }}
      >
        使用默认话术
      </div>
    );
  };

  switch (preview?.key) {
    // 开场白
    case 'openingTalk':
      return (
        <>
          <div className="mb-5 text-base font-medium">机器人开场白</div>
          <div>
            <Form.Item
              label="开场白"
              name="content"
              rules={[{ required: true, message: '请输入开场白' }]}
              extra={renderDefaultContent('content', '您好,很高兴为您服务~')}
            >
              <Input.TextArea rows={3} maxLength={300} showCount />
            </Form.Item>
          </div>
        </>
      );
    // 意向城市
    case 'intentionCity':
      return (
        <>
          <div className="mb-5 text-base font-medium">机器人发问</div>
          <div>
            <Form.Item
              label="发问话术"
              name="askContent"
              rules={[{ required: true, message: '请输入发问话术' }]}
              extra={renderDefaultContent(
                'askContent',
                '亲在哪个城市,我们给您分配就近区域的门店销售',
              )}
            >
              <Input.TextArea rows={3} maxLength={50} showCount />
            </Form.Item>

            <div className="mb-5 text-base font-medium">机器人回应话术</div>
            <div>
              <Form.Item
                label="回应话术"
                name="responseContent"
                rules={[{ required: true, message: '请输入回应话术' }]}
                extra={renderDefaultContent('responseContent', '[用户所选城市] 对吧,好的收到!')}
              >
                <Input.TextArea maxLength={30} showCount />
              </Form.Item>
              <div className="absolute -bottom-5 left-0 mt-2 text-xs text-gray-500">
                用户回应 <span className="underline">不包含意向城市</span>
                时，通过FAQ回答问题，并发问下一问题直至结束
              </div>
            </div>
          </div>
        </>
      );
    // 意向车系
    case 'intentionCarSeries':
      return (
        <>
          <div className="mb-5 text-base font-medium">机器人发问</div>
          <div>
            <Form.Item
              label="发问话术"
              name="askContent"
              rules={[{ required: true, message: '请输入发问话术' }]}
              extra={renderDefaultContent('askContent', '亲，最近在关注咱家的哪款车？')}
            >
              <Input.TextArea rows={3} maxLength={50} showCount />
            </Form.Item>
          </div>

          <div className="mt-8">
            <div className="mb-5 text-base font-medium">附加内容</div>
            <div className="mb-2">车型选择</div>
            <Form.List name="guideCardConfig">
              {(fields, { add, remove }) => (
                <DndSortList fields={fields} form={form} onRemove={remove} onAdd={add} type="car" />
              )}
            </Form.List>
          </div>

          <div className="mt-8">
            <div className="mb-5 text-base font-medium">机器人回应话术</div>
            <div>
              <Form.Item
                label="回应话术"
                name="responseContent"
                rules={[{ required: true, message: '请输入回应话术' }]}
                extra={renderDefaultContent('responseContent', '[用户回复车系] 不错的，问的人很多')}
              >
                <Input.TextArea maxLength={50} showCount />
              </Form.Item>
            </div>
          </div>
        </>
      );
    // 试驾意愿
    case 'driveWillingness':
      return (
        <>
          <div className="mb-5 text-base font-medium">机器人发问</div>
          <div>
            <Form.Item
              label="发问话术"
              name="askContent"
              rules={[{ required: true, message: '请输入发问话术' }]}
              extra={renderDefaultContent(
                'askContent',
                '您预计什么时候到店试驾呢？（请直接点击选项）',
              )}
            >
              <Input.TextArea rows={3} maxLength={50} showCount />
            </Form.Item>
          </div>

          <div className="mt-8">
            <div className="mb-5 text-base font-medium">附加内容</div>
            <div className="mb-2">时间选择</div>
            <Form.List name="guideCardConfig">
              {(fields, { add, remove }) => (
                <DndSortList
                  fields={fields}
                  form={form}
                  onRemove={remove}
                  onAdd={add}
                  type="time"
                />
              )}
            </Form.List>
          </div>

          <div className="mt-8">
            <div className="mb-5 text-base font-medium">机器人回应话术</div>
            <div>
              <Form.Item
                label="回应话术"
                name="responseContent"
                rules={[{ required: true, message: '请输入回应话术' }]}
                extra={renderDefaultContent('responseContent', '好的，我们尽量帮您提前安排试驾车')}
              >
                <Input maxLength={30} showCount />
              </Form.Item>
            </div>
          </div>
        </>
      );
    // 获取联系方式
    case 'contactWay':
      return (
        <>
          <div className="mb-5 text-base font-medium">机器人发问</div>
          <div>
            <Form.Item
              label="发问话术"
              name="askContent"
              rules={[{ required: true, message: '请输入发问话术' }]}
              extra={renderDefaultContent(
                'askContent',
                '最近价格很优惠啊，留个联系方式吧，让销售联络您~ ',
              )}
            >
              <Input.TextArea rows={3} maxLength={50} showCount />
            </Form.Item>
          </div>

          <div className="mt-8">
            <div className="mb-5 text-base font-medium">机器人回应话术</div>
            <div>
              <Form.Item
                label="回应话术"
                name="responseContent"
                rules={[{ required: true, message: '请输入回应话术' }]}
                extra={renderDefaultContent(
                  'responseContent',
                  '收到！会尽快为您安排附近的销售人员~',
                )}
              >
                <Input maxLength={30} showCount />
              </Form.Item>
            </div>
          </div>
        </>
      );
    // 获取称呼
    case 'salutation':
      return (
        <>
          <div className="mb-5 text-base font-medium">机器人发问</div>
          <div>
            <Form.Item
              label="发问话术"
              name="askContent"
              rules={[{ required: true, message: '请输入发问话术' }]}
              extra={renderDefaultContent('askContent', '销售人员该怎么称呼您啊？')}
            >
              <Input.TextArea rows={3} maxLength={50} showCount />
            </Form.Item>
          </div>

          <div className="mb-3 text-base font-medium">卡片内容</div>
          <Form.Item name="imageUrl" label="上传图片" initialValue={formValues?.imageUrl}>
            <ImageUpload
              customRequest={async (req) => {
                const file = req.file as File;
                if (!file.name) {
                  message.error('非法参数');
                  return;
                }
                const hide = message.loading('上传中');
                try {
                  const uploadRes = await uploadBase64Image({
                    base64Data: file,
                    key: `/message_card_image/${file.name}`,
                  });
                  hide();
                  if (uploadRes.statusCode === 200) {
                    const imageUrl = `https://${uploadRes.Location}`;
                    req.onSuccess?.(imageUrl);
                    return;
                  }
                  message.error('上传图片失败');
                } catch (e) {
                  console.error(e);
                  hide();
                  message.error('上传图片失败');
                }
              }}
            />
          </Form.Item>

          <Form.Item name="title" label="卡片标题">
            <Input placeholder='请输入卡片标题名称,例如"留资卡片"' maxLength={60} showCount />
          </Form.Item>

          <Form.Item name="componentList" label="卡片标题">
            <Checkbox.Group
              options={[
                { label: '姓名', value: 1 },
                { label: '手机号', value: 2 },
                { label: '城市', value: 3 },
              ]}
            />
          </Form.Item>

          <div>
            <div className="mb-5 text-base font-medium">机器人回应话术</div>
            <div>
              <Form.Item
                label="回应话术"
                name="responseContent"
                rules={[{ required: true, message: '好的' }]}
                extra={renderDefaultContent(
                  'responseContent',
                  '收到！会尽快为您安排附近的销售人员~',
                )}
              >
                <Input maxLength={30} showCount />
              </Form.Item>
            </div>
          </div>
        </>
      );
    // 购车时间
    case 'purchaseTime':
      return (
        <>
          <div className="mb-5 text-base font-medium">机器人发问</div>
          <div>
            <Form.Item
              label="发问话术"
              name="askContent"
              rules={[{ required: true, message: '请输入发问话术' }]}
              extra={renderDefaultContent('askContent', '您是计划什么时候购车呀？')}
            >
              <Input.TextArea rows={3} maxLength={50} showCount />
            </Form.Item>
          </div>

          <div className="mt-8">
            <div className="mb-5 text-base font-medium">附加内容</div>
            <div className="mb-2">时间选择</div>
            <Form.List name="guideCardConfig">
              {(fields, { add, remove }) => (
                <DndSortList
                  fields={fields}
                  form={form}
                  onRemove={remove}
                  onAdd={add}
                  type="time"
                />
              )}
            </Form.List>
          </div>

          <div className="mt-8">
            <div className="mb-5 text-base font-medium">机器人回应话术</div>
            <div>
              <Form.Item
                label="回应话术"
                name="responseContent"
                rules={[{ required: true, message: '请输入回应话术' }]}
                extra={renderDefaultContent(
                  'responseContent',
                  '[用户所选购车时间] 的话，建议您尽早到门店试驾体验哦',
                )}
              >
                <Input maxLength={30} showCount />
              </Form.Item>
            </div>
            <div className="absolute -bottom-5 left-0 mt-2 text-xs text-gray-500">
              用户回应 <span className="underline">不包含意向购车时间</span>
              时，通过FAQ回答问题，并发问下一问题直至结束
            </div>
          </div>
        </>
      );
    // 结束语
    case 'concludingRemark':
      return (
        <>
          <div className="mb-5 text-base font-medium">机器人结束语</div>
          <div>
            <Form.Item
              label="结束语"
              name="content"
              rules={[{ required: true, message: '请输入结束语' }]}
              extra={renderDefaultContent('content', '感谢您的关注,有其他问题都可随时咨询哦~')}
            >
              <Input.TextArea rows={3} maxLength={50} showCount />
            </Form.Item>
          </div>
        </>
      );
    default:
      return null;
  }
}
