import { Form, FormInstance, Image } from 'antd';
import { EditRowValue } from './intentionTable';
import { uniqueId } from 'lodash-es';
import ChatBubble from '../component/chatBubble';

type ModalRightPreviewProps = {
  preview?: EditRowValue;
  formValues?: any;
  form: FormInstance<any>;
};

export const defaultCardImage =
  'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/%E5%9B%BE%E7%89%87%E7%B4%A0%E6%9D%90/default-card-bg.png';

export default function ModalRightPreview({ preview, formValues, form }: ModalRightPreviewProps) {
  const configList = Form.useWatch('guideCardConfig', form);

  const renderConfigList = () => {
    return configList?.map((item: any) => (
      <div key={item?.sort || uniqueId()} className="underline">
        {item?.question || ''}
      </div>
    ));
  };

  switch (preview?.key) {
    // 开场白
    case 'openingTalk':
      return (
        <>
          <ChatBubble content="您好，在吗？" />
          <ChatBubble content={formValues?.content || ''} isUser />
        </>
      );
    // 意向城市
    case 'intentionCity':
      return (
        <>
          <ChatBubble content={formValues?.askContent} isUser />
          <ChatBubble content="北京" />
          <ChatBubble
            content={formValues?.responseContent.replace('[用户所选城市]', '北京')}
            isUser
          />
        </>
      );
    // 意向车系
    case 'intentionCarSeries':
      return (
        <>
          <ChatBubble content="您好，我想咨询下车型" />
          <ChatBubble
            content={
              <>
                {formValues?.askContent || ''}
                {renderConfigList()}
              </>
            }
            isUser
          />
          <ChatBubble content="车型 B" />
          <ChatBubble
            content={formValues?.responseContent.replace('[用户回复车系]', '车型 B')}
            isUser
          />
        </>
      );
    // 试驾意愿
    case 'driveWillingness':
      return (
        <>
          <ChatBubble content="您好,我想咨询下试驾" />
          <ChatBubble
            content={
              <>
                {formValues?.askContent || ''}
                {renderConfigList()}
              </>
            }
            isUser
          />
          <ChatBubble content="好呀,下周三可以" />
          <ChatBubble content={formValues?.responseContent || ''} isUser />
        </>
      );
    // 获取联系方式
    case 'contactWay':
      return (
        <>
          <ChatBubble content="您好，我想买车" />
          <ChatBubble content={formValues?.askContent || ''} isUser />
          <ChatBubble content="1234567890" />
          <ChatBubble content={formValues?.responseContent || ''} isUser />
        </>
      );
    // 获取称呼
    case 'salutation':
      return (
        <>
          <ChatBubble content="您好，我想买车" />
          <ChatBubble content={formValues?.askContent || ''} isUser />
          <ChatBubble content="林先生" />
          <ChatBubble content={formValues?.responseContent || ''} isUser />
          <ChatBubble
            content={
              <>
                <Image
                  alt="card-image"
                  preview={false}
                  width="100%"
                  src={formValues.imageUrl || defaultCardImage}
                  fallback={defaultCardImage}
                  style={{ height: '100px', width: '100%', objectFit: 'cover' }}
                />
                {formValues?.title && (
                  <div className="my-1 text-base font-medium">{formValues.title}</div>
                )}
                <div className="space-y-1">
                  {formValues?.componentList?.includes(1) && (
                    <div className="flex items-center gap-2 rounded-md border bg-white p-2">
                      <span>林先生/女士</span>
                    </div>
                  )}
                  {formValues?.componentList?.includes(2) && (
                    <div className="flex items-center gap-2 rounded-md border bg-white p-2">
                      <span>123456789</span>
                    </div>
                  )}
                  {formValues?.componentList?.includes(3) && (
                    <div className="flex items-center gap-2 rounded-md border bg-white p-2">
                      <span>如：广东省广州市天河区</span>
                    </div>
                  )}
                </div>
              </>
            }
          />
        </>
      );
    // 购车时间
    case 'purchaseTime':
      return (
        <>
          <ChatBubble content="您好，我想买车" />
          <ChatBubble
            content={
              <>
                {formValues?.askContent || ''}
                {renderConfigList()}
              </>
            }
            isUser
          />
          <ChatBubble content="最近 3 个月内" />
          <ChatBubble
            content={formValues?.responseContent.replace('[用户所选购车时间]', '最近 3 个月内')}
            isUser
          />
        </>
      );
    // 结束语
    case 'concludingRemark':
      return (
        <>
          <ChatBubble content="好的, 我了解了" />
          <ChatBubble content={formValues?.content || ''} isUser />
        </>
      );
    default:
      return null;
  }
}
