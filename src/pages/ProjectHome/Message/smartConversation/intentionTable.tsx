import { ProTable } from '@ant-design/pro-components';
import { Switch, Image } from 'antd';
import type { ProColumns } from '@ant-design/pro-components';
import { cn } from '@/lib/utils';
import { useState } from 'react';
import IntentionTableModal from './intentionTableModal';
import {
  IntentionTableConfig,
  PostLeadsCustomer,
  PostLeadsCustomerSwitch,
  RobotConfig,
  Salutation,
  SubConfig,
} from '@/services/message-robot';
import useProjectId from '@/hooks/useProjectId';
import { defaultCardImage } from './modalRightPreview';

type IntentionTableProps = {
  config?: IntentionTableConfig;
  status?: number;
  refreshConfig: () => Promise<RobotConfig | undefined>;
};

type TableDataType = {
  type: string;
  askContent: string;
  responseContent: string;
  status: number;
  key: keyof IntentionTableConfig;
  raw: IntentionTableConfig[keyof IntentionTableConfig];
  guideCardConfig?: SubConfig[];
};

export type EditRowValue = {
  data: IntentionTableConfig[keyof IntentionTableConfig];
  key: keyof IntentionTableConfig;
};

export default function IntentionTable({ config, status, refreshConfig }: IntentionTableProps) {
  const projectId = useProjectId();
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [editingRecord, setEditingRecord] = useState<EditRowValue>();

  const onSwitchRowChange = async (record: TableDataType) => {
    await PostLeadsCustomer({
      projectId,
      updateType: record.key,
      leadsCustomerConfig: { [record.key]: { ...record.raw, status: record.status ? 0 : 1 } },
    });
    await refreshConfig();
  };

  const columns: ProColumns<TableDataType>[] = [
    {
      title: '话术类型',
      dataIndex: 'type',
      width: 120,
    },
    {
      title: '机器人发问话术',
      dataIndex: 'askContent',
      width: 200,
      render: (_, record) => {
        const salutationRowData = record.raw as Salutation;
        return (
          <>
            <div className="flex items-center gap-2">
              <div className="flex h-6 w-6 items-center justify-center rounded bg-blue-100 text-blue-500">
                问
              </div>
              <div>{record.askContent}</div>
            </div>
            {/* 特殊处理 */}
            {record.key === 'salutation' && (
              <div className="mt-2 flex items-start gap-2">
                <div className="flex h-6 w-6 items-center justify-center rounded bg-[#EAF5F0] text-[#0FA964]">
                  附
                </div>
                <div className="bg-[#F3F3F4] p-2">
                  <Image
                    alt="card-image"
                    preview={false}
                    width="100%"
                    src={salutationRowData.imageUrl || defaultCardImage}
                    fallback={defaultCardImage}
                    style={{ height: '100px', width: '100%', objectFit: 'cover' }}
                  />
                  {salutationRowData.title && (
                    <div className="my-1 text-base font-medium">{salutationRowData.title}</div>
                  )}
                  <div className="space-y-1">
                    {salutationRowData.componentList?.includes(1) && (
                      <div className="flex items-center gap-2 rounded-md border bg-white p-2">
                        <span>林先生/女士</span>
                      </div>
                    )}
                    {salutationRowData.componentList?.includes(2) && (
                      <div className="flex items-center gap-2 rounded-md border bg-white p-2">
                        <span>123456789</span>
                      </div>
                    )}
                    {salutationRowData.componentList?.includes(3) && (
                      <div className="flex items-center gap-2 rounded-md border bg-white p-2">
                        <span>如：广东省广州市天河区</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}
            {record.guideCardConfig && (
              <div className="mt-2 flex items-center gap-2">
                <div className="flex h-6 w-6 items-center justify-center rounded bg-[#EAF5F0] text-[#0FA964]">
                  附
                </div>
                <div>{record.guideCardConfig.map((item) => item?.question).join('、')}</div>
              </div>
            )}
          </>
        );
      },
    },
    {
      title: '机器人回应话术',
      dataIndex: 'responseContent',
      width: 200,
    },
    {
      title: '话术状态',
      dataIndex: 'status',
      width: 100,
      render: (_, record) => (
        <div className="flex items-center">
          <span
            className={cn(
              'mr-2 h-2 w-2 rounded-full',
              record.status ? 'bg-green-500' : 'bg-gray-300',
            )}
          />
          {record.status ? '启用中' : '已停用'}
        </div>
      ),
    },
    {
      title: '启用话术',
      width: 100,
      render: (_, record) => (
        <Switch checked={Boolean(record.status)} onChange={() => onSwitchRowChange(record)} />
      ),
    },
    {
      title: '操作',
      width: 80,
      render: (_, record) => (
        <a
          className="cursor-pointer text-blue-500 hover:text-blue-600"
          onClick={() => {
            const editConfig = {
              data: record.raw,
              key: record.key,
            } as EditRowValue;
            setEditingRecord(editConfig);
            setEditModalOpen(true);
          }}
        >
          编辑
        </a>
      ),
    },
  ];

  const dataSource = config
    ? [
        {
          type: '开场白',
          askContent: '-',
          responseContent: config.openingTalk.content,
          status: config.openingTalk.status,
          key: 'openingTalk' as keyof IntentionTableConfig,
          raw: config.openingTalk,
        },
        {
          type: '获取意向城市',
          askContent: config.intentionCity.askContent,
          responseContent: config.intentionCity.responseContent,
          status: config.intentionCity.status,
          key: 'intentionCity' as keyof IntentionTableConfig,
          raw: config.intentionCity,
        },
        {
          type: '获取意向车系',
          askContent: config.intentionCarSeries.askContent,
          responseContent: config.intentionCarSeries.responseContent,
          status: config.intentionCarSeries.status,
          guideCardConfig: config.intentionCarSeries.guideCardConfig,
          key: 'intentionCarSeries' as keyof IntentionTableConfig,
          raw: config.intentionCarSeries,
        },
        {
          type: '获取联系方式',
          askContent: config.contactWay.askContent,
          responseContent: config.contactWay.responseContent,
          status: config.contactWay.status,
          key: 'contactWay' as keyof IntentionTableConfig,
          raw: config.contactWay,
        },
        {
          type: '获取试驾意愿',
          askContent: config.driveWillingness.askContent,
          responseContent: config.driveWillingness.responseContent,
          status: config.driveWillingness.status,
          guideCardConfig: config.driveWillingness.guideCardConfig,
          key: 'driveWillingness' as keyof IntentionTableConfig,
          raw: config.driveWillingness,
        },
        {
          type: '获取称呼',
          askContent: config.salutation.askContent,
          responseContent: config.salutation.responseContent,
          status: config.salutation.status,
          key: 'salutation' as keyof IntentionTableConfig,
          raw: config.salutation,
        },
        {
          type: '获取购车时间',
          askContent: config.purchaseTime.askContent,
          responseContent: config.purchaseTime.responseContent,
          status: config.purchaseTime.status,
          guideCardConfig: config.purchaseTime.guideCardConfig,
          key: 'purchaseTime' as keyof IntentionTableConfig,
          raw: config.purchaseTime,
        },
        {
          type: '结束语',
          askContent: '-',
          responseContent: config.concludingRemark.content,
          status: config.concludingRemark.status,
          key: 'concludingRemark' as keyof IntentionTableConfig,
          raw: config.concludingRemark,
        },
      ]
    : [];

  const handleEditSubmit = async () => {
    // 这里处理更新逻辑
    setEditModalOpen(false);
    await refreshConfig();
  };

  const onSwitchChange = async (checked: boolean) => {
    await PostLeadsCustomerSwitch({
      projectId,
      status: checked ? 1 : 0,
    });
    await refreshConfig();
  };

  return (
    <>
      <div className="mb-2 mt-6 text-base font-medium">意向用户主动获取</div>
      <div className="w-full rounded-md bg-new-media-blue-100 p-4">
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            <span className="text-base font-medium">用户进入对话自动获取意向</span>
            <Switch value={Boolean(status)} onChange={onSwitchChange} />
          </div>
        </div>
        <div className="mt-4 text-muted-foreground">
          开启后，系统将根据下方意向收集列表，引导用户进行对话，并自动识别会话中意向
        </div>
      </div>
      <ProTable<TableDataType>
        columns={columns}
        dataSource={dataSource}
        search={false}
        options={false}
        pagination={false}
        ghost
        rowKey="key"
        tableClassName={cn(
          'custom-table',
          !status && 'opacity-60 pointer-events-none cursor-not-allowed',
        )}
        headerTitle={<div className="text-sm">意向列表</div>}
      />
      <IntentionTableModal
        open={editModalOpen}
        onCancel={() => setEditModalOpen(false)}
        onOk={handleEditSubmit}
        initialValues={editingRecord}
        projectId={projectId}
      />
    </>
  );
}
