import ChatBg from '@/assets/chat-background.jpg';
import defaultAvatar from '@/assets/default-avatar.png';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion';
import { KnowledgeItem } from '@/services/message-robot';
import { HolderOutlined } from '@ant-design/icons';
import {
  closestCenter,
  DndContext,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import {
  SortableContext,
  sortableKeyboardCoordinates,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { Avatar, Button, Form, Input, message, Modal, Popconfirm } from 'antd';
import { CirclePlus, Trash2 } from 'lucide-react';
import { useEffect, useState } from 'react';

type KnowledgeTableModalProps = {
  open: boolean;
  onCancel: () => void;
  onOk: (values: KnowledgeItem[]) => void;
  initialValues?: KnowledgeItem[];
  guideTitle?: string;
  hideLeadsAnswer?: boolean;
};

const SortableAccordionItem = ({
  field,
  index,
  onDelete,
  onExpand,
  hideLeadsAnswer,
}: {
  field: any;
  index: number;
  onDelete: () => void;
  onExpand: (key: string | number) => void;
  hideLeadsAnswer?: boolean;
}) => {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
    id: field.key,
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <div ref={setNodeRef} style={style}>
      <AccordionItem value={field.key.toString()} className="py-1">
        <div className="flex w-full items-center justify-between">
          <div className="flex items-center gap-1">
            <div {...attributes} {...listeners}>
              <HolderOutlined className="cursor-move" />
            </div>
            <span className="font-medium">知识项 {index + 1}</span>
          </div>
          <div className="flex items-center gap-3">
            <Popconfirm
              title="确认删除该知识项吗?"
              okText="确认"
              cancelText="取消"
              onConfirm={onDelete}
            >
              <Trash2 size={16} className="cursor-pointer" />
            </Popconfirm>
            <AccordionTrigger
              className="hover:no-underline"
              onClick={(e) => {
                e.preventDefault();
                onExpand(field.key);
              }}
            />
          </div>
        </div>
        <AccordionContent className="flex flex-col gap-1">
          <Form.Item
            label="用户问题"
            name={[field.name, 'question']}
            rules={[{ required: true, message: '请输入用户问题' }]}
            labelCol={{ span: 4 }}
            wrapperCol={{ span: 20 }}
          >
            <Input maxLength={14} showCount />
          </Form.Item>

          <Form.Item
            label="回复答案"
            name={[field.name, 'answer']}
            rules={[{ required: true, message: '请输入回复答案' }]}
            labelCol={{ span: 4 }}
            wrapperCol={{ span: 20 }}
          >
            <Input.TextArea rows={6} maxLength={300} showCount />
          </Form.Item>
          <div className="mb-2 pl-24 text-muted-foreground">
            上述设置的用户问题与回复答案将用于AI学习，当用户问相似的问题时，会根据设置的回复答案进行类似的话术回答。
          </div>

          {!hideLeadsAnswer && (
            <Form.Item
              label="留资后回答"
              name={[field.name, 'leadsAnswer']}
              labelCol={{ span: 4 }}
              wrapperCol={{ span: 20 }}
            >
              <Input.TextArea rows={6} maxLength={300} showCount />
            </Form.Item>
          )}
        </AccordionContent>
      </AccordionItem>
    </div>
  );
};

const MAX_KNOWLEDGE_ITEMS = 6;

export default function KnowledgeTableModal({
  open,
  onCancel,
  onOk,
  initialValues,
  guideTitle,
  hideLeadsAnswer,
}: KnowledgeTableModalProps) {
  const [form] = Form.useForm();
  const [previewItems, setPreviewItems] = useState<KnowledgeItem[]>([]);
  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);

  useEffect(() => {
    if (open && initialValues) {
      form.setFieldsValue({ items: initialValues });
      setPreviewItems(initialValues);
    }
  }, [open, initialValues, form]);

  const handleValuesChange = (_: any, allValues: any) => {
    if (allValues.items) {
      setPreviewItems(allValues.items);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  const handleOk = async () => {
    try {
      const values = await form.validateFields({ recursive: true });
      const hasEmptyRequired = values.items?.some(
        (item: KnowledgeItem) => !item.question?.trim() || !item.answer?.trim(),
      );
      if (hasEmptyRequired) {
        throw new Error();
      }
      onOk(values.items);
      form.resetFields();
    } catch (error) {
      message.error('请检查表单填写是否完整');
    }
  };

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    }),
  );

  const handleDragEnd = (event: any, { move }: any) => {
    const { active, over } = event;

    if (active.id !== over.id) {
      const activeIndex = active.data.current.sortable.index;
      const overIndex = over.data.current.sortable.index;

      move(activeIndex, overIndex);

      const currentValues = form.getFieldValue('items');
      const updatedValues = currentValues.map((item: KnowledgeItem, index: number) => ({
        ...item,
        sort: index + 1,
      }));

      form.setFieldsValue({ items: updatedValues });

      setPreviewItems(updatedValues);
    }
  };

  return (
    <Modal
      title="编辑知识"
      open={open}
      onCancel={handleCancel}
      onOk={handleOk}
      width={1000}
      className="edit-knowledge-modal"
    >
      <div className="flex gap-6 border-t">
        <div className="no-scrollbar h-[60vh] w-2/3 overflow-hidden overflow-y-auto border-r p-5">
          <Form
            form={form}
            layout="horizontal"
            labelAlign="left"
            onValuesChange={handleValuesChange}
          >
            <Form.List name="items">
              {(fields, { add, remove, move }) => (
                <>
                  <DndContext
                    sensors={sensors}
                    collisionDetection={closestCenter}
                    onDragEnd={(event) => handleDragEnd(event, { add, move })}
                  >
                    <SortableContext
                      items={fields.map((field) => field.key)}
                      strategy={verticalListSortingStrategy}
                    >
                      <Accordion type="multiple" value={expandedKeys}>
                        {fields.map((field, index) => (
                          <SortableAccordionItem
                            key={field.key}
                            field={field}
                            index={index}
                            onDelete={() => remove(index)}
                            onExpand={(key) => {
                              const keyString = key.toString();
                              setExpandedKeys((prev) =>
                                prev.includes(keyString)
                                  ? prev.filter((k) => k !== keyString)
                                  : [...prev, keyString],
                              );
                            }}
                            hideLeadsAnswer={hideLeadsAnswer}
                          />
                        ))}
                      </Accordion>
                    </SortableContext>
                  </DndContext>
                  <Button
                    type="link"
                    onClick={() => {
                      if (fields.length >= MAX_KNOWLEDGE_ITEMS) {
                        message.warning('最多只能添加6条知识项');
                        return;
                      }
                      const newKey = fields.length.toString();
                      add({
                        sort: fields.length + 1,
                        question: '',
                        answer: '',
                        leadsAnswer: '',
                      });
                      setExpandedKeys((prev) => [...prev, newKey]);
                    }}
                    disabled={fields.length >= MAX_KNOWLEDGE_ITEMS}
                    className="mt-3 px-0"
                  >
                    <CirclePlus size={16} /> 添加知识
                    {fields.length ? ` (${fields.length}/${MAX_KNOWLEDGE_ITEMS})` : ''}
                  </Button>
                </>
              )}
            </Form.List>
          </Form>
        </div>

        <div className="w-1/3 p-2">
          <div className="mb-2 text-base font-medium">效果示例</div>
          <div className="relative my-5 h-[620px] w-full rounded-lg shadow">
            <img
              src={ChatBg}
              alt="chat-bg"
              className="absolute inset-0 h-full w-full overflow-hidden object-cover"
            />

            <div className="absolute inset-0 top-14 overflow-hidden rounded-lg p-4 shadow-xl">
              <div className="mb-4 text-center text-xs text-gray-500">上午11:35</div>

              <div className="mb-4 flex items-start gap-2">
                <Avatar size="small" src={<img src={defaultAvatar} />} alt="avatar" />
                <div className="rounded-lg bg-[#F3F3F4] p-2 text-sm shadow-sm">
                  {guideTitle || '您好，请问想要咨询什么问题呢?'}
                  {previewItems?.map((item: KnowledgeItem) => (
                    <div
                      key={`${item.sort}-${item.question}-${item.answer}`}
                      className="break-words text-primary"
                    >
                      {item.question}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Modal>
  );
}
