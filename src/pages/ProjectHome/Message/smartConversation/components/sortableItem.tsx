import React from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { MenuOutlined } from '@ant-design/icons';

type SortableItemProps = {
  id: string | number;
  children: React.ReactNode;
};

const SortableItem: React.FC<SortableItemProps> = ({ id, children }) => {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
    id,
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <div ref={setNodeRef} style={style} className="mb-2">
      <div className="flex items-center gap-2">
        <div
          className="cursor-move text-gray-400 hover:text-gray-600"
          {...attributes}
          {...listeners}
        >
          <MenuOutlined className="cursor-move text-gray-400 hover:text-gray-600" />
        </div>
        {children}
      </div>
    </div>
  );
};

export { SortableItem };
