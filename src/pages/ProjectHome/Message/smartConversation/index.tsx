import { RobotConfig } from '@/services/message-robot';
import { Button, Modal, Space } from 'antd';
import { useState } from 'react';
import ChatExperience from '../component/chatExperience';
import IntentionTable from './intentionTable';
import KnowledgeTable from './knowledgeTable';

export type SmartConversationProps = {
  robotConfig?: RobotConfig;
  refreshConfig: () => Promise<RobotConfig | undefined>;
};

export default function SmartConversation({ robotConfig, refreshConfig }: SmartConversationProps) {
  const [open, setOpen] = useState(false);

  return (
    <div className="pb-10">
      <div>
        <Space>
          <div className="text-base font-medium">被动回复智能知识库</div>
        </Space>
        <Button
          type="link"
          href="#"
          className="float-right"
          onClick={(e) => {
            e.preventDefault();
            setOpen(true);
          }}
        >
          智能销售客服体验
        </Button>
      </div>
      <KnowledgeTable
        config={robotConfig?.activeGuideConfig}
        status={robotConfig?.activeGuideStatus}
        refreshConfig={refreshConfig}
      />
      <IntentionTable
        config={robotConfig?.leadsCustomerConfig}
        status={robotConfig?.leadsCustomerStatus}
        refreshConfig={refreshConfig}
      />

      <Modal
        title="智能销售客服体验"
        open={open}
        onCancel={() => setOpen(false)}
        footer={null}
        width={800}
      >
        <ChatExperience />
      </Modal>
    </div>
  );
}
