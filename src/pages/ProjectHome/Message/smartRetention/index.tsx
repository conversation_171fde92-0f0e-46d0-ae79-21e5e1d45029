import useProjectId from '@/hooks/useProjectId';
import {
  PostRetainCustomer,
  PostRetainCustomerSwitch,
  RetainConfig,
  RobotConfig,
} from '@/services/message-robot';
import { Button, message, Switch, Timeline, Modal } from 'antd';
import {
  FooterToolbar,
  ProForm,
  ProFormCheckbox,
  ProFormDigit,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { Form } from 'antd';
import Preview from './preview';
import { PlusOutlined } from '@ant-design/icons';
import { Trash2 } from 'lucide-react';
import { cn } from '@/lib/utils';

export type SmartRetentionProps = {
  robotConfig?: RobotConfig;
  refreshConfig: () => Promise<RobotConfig | undefined>;
  activeSubKey: string;
  masterSwitchStatus: boolean;
};

// 添加时间转换的工具函数
const secondsToMinutes = (seconds: number) => Math.floor(seconds / 60);
const minutesToSeconds = (minutes: number) => minutes * 60;

const retainDefaultConfig = [
  { silenceTime: 1, content: '感兴趣的话可以先留个电话哈，安排专人尽快联系您' },
  { silenceTime: 30, content: '亲可以留个联系方式,先给您发一份详细资料慢慢开率，不会频繁打扰的话' },
  { silenceTime: 60, content: '感谢关注呀~先添加您的联系方式发下价格表哦，可以参考对比下的~' },
];

const resetRetainConfig = retainDefaultConfig.slice(0, 2);

export default function SmartRetention({
  robotConfig,
  refreshConfig,
  activeSubKey,
  masterSwitchStatus,
}: SmartRetentionProps) {
  const projectId = useProjectId();
  const [form] = Form.useForm();
  const [modal, contextHolder] = Modal.useModal();
  const retainConfigWatch = Form.useWatch('retainConfig', form) || [];

  // 转换初始值中的秒为分钟
  const initialRetainConfig = robotConfig?.retainCustomerConfig?.retainConfig?.map((config) => ({
    ...config,
    silenceTime: secondsToMinutes(config.silenceTime),
  }));

  const onSwitchChange = async (checked: boolean) => {
    await PostRetainCustomerSwitch({
      projectId,
      status: checked ? 1 : 0,
    });
    await refreshConfig();
  };

  const switchStatus = Boolean(robotConfig?.retainCustomerStatus);

  return (
    <div className="flex gap-6">
      <div className="flex-1">
        <div className="w-full rounded-md bg-new-media-blue-100 p-4">
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2">
              <span className="text-base font-medium">用户沉默超时自动挽留</span>
              <Switch value={switchStatus} onChange={onSwitchChange} />
            </div>
          </div>
          <div className="mt-4 text-muted-foreground">
            已进线的用户如沉默超过下列设定时间，将自动执行下方挽留配置，提升留资转化效果
          </div>
        </div>
        {/* Form表单 */}
        <div className="mt-6">
          <ProForm<{ retainConfig: RetainConfig[]; nightNotDisturbStatus: boolean }>
            form={form}
            disabled={!switchStatus || !masterSwitchStatus}
            submitter={
              activeSubKey === 'smart-retention'
                ? {
                    render: (_, dom) => (
                      <FooterToolbar
                        style={{
                          right: 0,
                          width: '100%',
                        }}
                      >
                        {dom}
                      </FooterToolbar>
                    ),
                  }
                : undefined
            }
            onReset={() => {
              modal.confirm({
                title: '确定重置?',
                content: '重置后，您自定义内容将丢失并重置为系统预设内容，不可恢复',
                onOk: () => {
                  form.setFieldsValue({
                    retainConfig: resetRetainConfig,
                    nightNotDisturbStatus: false,
                  });
                },
              });
              return false;
            }}
            initialValues={{
              retainConfig: initialRetainConfig,
              nightNotDisturbStatus: !!robotConfig?.retainCustomerConfig?.nightNotDisturbStatus,
            }}
            onFinish={async (values) => {
              // 提交时转换分钟为秒
              const submitRetainConfig = {
                ...values.retainConfig,
                retainConfig: values.retainConfig.map((config) => ({
                  ...config,
                  silenceTime: minutesToSeconds(config.silenceTime),
                })),
              };
              const submitData = {
                ...submitRetainConfig,
                nightNotDisturbStatus: values.nightNotDisturbStatus ? 1 : 0,
              };
              const res = await PostRetainCustomer({
                projectId,
                douyinPrivateMessageRetainCustomerConfig: submitData,
              });
              if (res.code === 0) {
                message.success('提交成功');
                await refreshConfig();
              } else {
                message.error('提交失败');
              }
            }}
            layout="horizontal"
          >
            <Form.List name="retainConfig">
              {(fields, { add, remove }) => (
                <Timeline
                  items={[
                    ...fields.map((field, index: number) => ({
                      children: (
                        <>
                          <span className="text-sm">第{index + 1}轮挽留</span>
                          <span className="text-gray ml-2 text-xs text-gray-500">
                            建议用户沉默时间{retainDefaultConfig[index]?.silenceTime}分钟
                          </span>
                          <div className="relative mt-3 max-w-[720px] space-y-4 rounded-md bg-new-media-blue-100 px-4 pb-8 pt-1">
                            <div
                              className={cn(
                                'absolute right-2 top-2 z-50 rounded p-2 transition-colors',
                                switchStatus && fields.length > 1
                                  ? 'cursor-pointer hover:bg-white/50'
                                  : 'cursor-not-allowed opacity-50',
                              )}
                            >
                              <button
                                className={cn(
                                  'text-gray-600',
                                  switchStatus && fields.length > 1
                                    ? 'hover:text-gray-900'
                                    : 'pointer-events-none text-gray-400',
                                )}
                                onClick={() => remove(field.name)}
                                disabled={fields.length <= 1 || !switchStatus}
                              >
                                <Trash2 size={16} />
                              </button>
                            </div>
                            <ProFormDigit
                              name={[field.name, 'silenceTime']}
                              label="回复类型"
                              rules={[
                                { required: true, message: '请输入用户沉默时间' },
                                ({ getFieldValue }) => ({
                                  validator(_, value) {
                                    if (index === 0) return Promise.resolve();
                                    const prevValue = getFieldValue([
                                      'retainConfig',
                                      index - 1,
                                      'silenceTime',
                                    ]);
                                    if (value && prevValue && value <= prevValue) {
                                      return Promise.reject(new Error('沉默时间需大于上一轮'));
                                    }
                                    return Promise.resolve();
                                  },
                                }),
                              ]}
                              min={1}
                              width="sm"
                              addonBefore={<div>用户沉默</div>}
                              addonAfter={<div>分钟后发送</div>}
                              fieldProps={{
                                controls: true,
                                precision: 0,
                                defaultValue: retainDefaultConfig[index]?.silenceTime,
                              }}
                            />
                            <ProFormTextArea
                              name={[field.name, 'content']}
                              label="挽留话术"
                              rules={[{ required: true, message: '请输入挽留话术' }]}
                              placeholder="请输入挽留话术"
                              fieldProps={{
                                rows: 4,
                                showCount: true,
                                maxLength: 150,
                              }}
                              extra={
                                <div
                                  className={cn(
                                    'cursor-pointer text-sm',
                                    switchStatus
                                      ? 'text-blue-500 hover:text-blue-600'
                                      : 'cursor-not-allowed text-gray-400',
                                  )}
                                  onClick={() => {
                                    if (!switchStatus) return;
                                    form.setFieldValue(
                                      ['retainConfig', field.name, 'content'],
                                      retainDefaultConfig[index]?.content,
                                    );
                                  }}
                                >
                                  使用默认话术
                                </div>
                              }
                            />
                          </div>
                        </>
                      ),
                    })),
                    {
                      children: (
                        <Button
                          block
                          icon={<PlusOutlined />}
                          onClick={() => {
                            const retainConfig = retainConfigWatch;
                            const currentIndex = retainConfig.length - 1;
                            add(retainDefaultConfig[currentIndex + 1]);
                          }}
                          disabled={!switchStatus || retainConfigWatch?.length >= 3}
                          className="max-w-[50%]"
                        >
                          添加轮次({retainConfigWatch?.length || 0}/3)
                        </Button>
                      ),
                    },
                  ]}
                />
              )}
            </Form.List>
            <ProFormCheckbox name="nightNotDisturbStatus">
              夜间免打扰: 用户0点~8点间触发的挽留，在早8点后发送
            </ProFormCheckbox>
          </ProForm>
        </div>
      </div>
      <div className="w-[350px] p-2 pt-4">
        <Preview formData={retainConfigWatch} />
      </div>
      <div>{contextHolder}</div>
    </div>
  );
}
