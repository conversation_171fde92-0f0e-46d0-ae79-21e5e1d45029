import ChatBubble from '@/pages/ProjectHome/Message/component/chatBubble';
import ChatBg from '@/assets/chat-background.jpg';
import { Card } from 'antd';
import { RetainConfig } from '@/services/message-robot';
import React from 'react';

type PreviewPanelProps = {
  formData: RetainConfig[];
};

const tipContent = <div className="underline">一键发送抖音号授权手机号</div>;

export default function Preview({ formData }: PreviewPanelProps) {
  return (
    <Card title="效果示例">
      <div className="relative my-5 h-[620px] w-full overflow-hidden rounded-lg shadow-xl">
        <img
          src={ChatBg}
          alt="chat-bg"
          className="absolute inset-0 h-full w-full overflow-hidden object-cover"
        />

        <div className="no-scrollbar absolute inset-0 bottom-14 top-14 overflow-y-auto p-4">
          <div className="mb-4 text-center text-xs text-gray-500">上午11:35</div>
          <ChatBubble content="拍一套多少钱" />

          {formData.length > 0 &&
            formData.map((item, index) => (
              <React.Fragment key={`${item?.silenceTime}-${index}`}>
                <div className="mb-4 text-center text-xs text-gray-500">
                  沉默{item?.silenceTime}分钟
                </div>
                <ChatBubble
                  content={
                    <>
                      <div>{item?.content}</div>
                      {tipContent}
                    </>
                  }
                  isUser
                />
              </React.Fragment>
            ))}
        </div>
      </div>
    </Card>
  );
}
