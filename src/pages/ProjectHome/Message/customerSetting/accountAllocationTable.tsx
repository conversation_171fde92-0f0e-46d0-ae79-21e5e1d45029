import {
  ProColumns,
  ProTable,
  useControlModel,
  WithControlPropsType,
} from '@ant-design/pro-components';
import { Button, message, Popconfirm } from 'antd';
import AddModalForm from './addModalForm';
import { DouyinImManualTeam } from '@/services/douyin-im/manual-config';
import { DouyinImManualSourceAccount } from '@/services/douyin-im/manual-source';
import { useState } from 'react';
import { checkDuplicateData } from './utils';

type AccountAllocationTableProps = {
  receptionUserList?: DouyinImManualTeam[];
};

export default function AccountAllocationTable(
  props: WithControlPropsType & AccountAllocationTableProps,
) {
  const model = useControlModel(props);
  const { value, onChange } = model;
  const [addedCount, setAddedCount] = useState(value.length || 0);

  const handleSourceAccountDelete = (id?: number | string, sysUserId?: number) => {
    if (id && sysUserId) {
      onChange(
        value.filter(
          (item: DouyinImManualSourceAccount) => !(item.id === id && item.sysUserId === sysUserId),
        ),
      );
      setAddedCount(addedCount - 1);
    }
  };

  const sourceAccountColumns: ProColumns<DouyinImManualSourceAccount>[] = [
    {
      dataIndex: 'id',
      hideInTable: true,
    },
    {
      title: '账号',
      dataIndex: 'nickname',
      width: '30%',
    },
    {
      title: '来源门店',
      dataIndex: 'teamName',
      width: '30%',
    },
    {
      title: '负责接待客服',
      dataIndex: 'sysUserNickname',
      width: '30%',
    },
    {
      title: '操作',
      dataIndex: 'operation',
      width: '10%',
      render: (_: any, record: DouyinImManualSourceAccount) => (
        <Popconfirm
          title="是否确认删除"
          onConfirm={() => handleSourceAccountDelete(record.id, record.sysUserId)}
        >
          <Button type="link" danger style={{ padding: '0px' }}>
            删除
          </Button>
        </Popconfirm>
      ),
    },
  ];

  return (
    <ProTable<DouyinImManualSourceAccount>
      ghost
      size="small"
      rowKey={(record) => `${record.id}-${record.sysUserId}`}
      columns={sourceAccountColumns}
      dataSource={value}
      className="custom-setting-table"
      style={{ width: '700px' }}
      tableClassName="custom-table"
      search={false}
      options={false}
      pagination={{
        pageSize: 10,
        hideOnSinglePage: true,
      }}
      headerTitle={<div className="text-sm text-new-media-gray-600">已添加: {addedCount}</div>}
      toolBarRender={() => [
        <AddModalForm
          key="add-modal-form"
          receptionUserList={props.receptionUserList}
          onFinish={async (values) => {
            const { duplicates, uniqueData } = checkDuplicateData(value, values);

            duplicates.forEach((item) => {
              message.warning(
                `账号 ${item.nickname} 和客服 ${item.sysUserNickname} 的组合已存在，请勿重复添加`,
              );
            });

            const newDataSource = [...value, ...uniqueData];
            onChange(newDataSource);
            setAddedCount(newDataSource.length);
            return true;
          }}
        />,
      ]}
    />
  );
}
