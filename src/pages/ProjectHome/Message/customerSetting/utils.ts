import { DouyinImManualTeam } from '@/services/douyin-im/manual-config';
import { DouyinImManualSourceAccount } from '@/services/douyin-im/manual-source';
import { nanoid } from '@ant-design/pro-components';
import { Option } from './addModalForm';
// 转换数据用于提交API
export const transformToApiData = (data: DouyinImManualSourceAccount[]) => {
  const groupedData = data.reduce((acc: Record<number, string[]>, item) => {
    if (item.sysUserId && item.accountId) {
      acc[item.sysUserId] = acc[item.sysUserId] || [];
      acc[item.sysUserId].push(item.accountId);
    }
    return acc;
  }, {});

  return Object.entries(groupedData).map(([sysUserId, accountIds]) => ({
    sysUserId: Number(sysUserId),
    accountIdList: accountIds,
  }));
};

// 构建表格展示数据
export const buildTableData = (
  accountList: string[],
  options: Option[],
  selectedSysUserList?: DouyinImManualTeam[],
): DouyinImManualSourceAccount[] => {
  const sourceAccounts: DouyinImManualSourceAccount[] = [];

  const selectedAccounts = options
    .flatMap((team) => team.children || [])
    .filter((item) => accountList.includes(item.value));

  const selectedTeams = options.filter((team) =>
    team.children?.some((child: { value: string }) => accountList.includes(child.value)),
  );

  selectedAccounts.forEach((account) => {
    selectedSysUserList?.forEach((user) => {
      const currentTeam = selectedTeams.find((team) =>
        team.children?.some((child: { value: any }) => child.value === account.value),
      );

      sourceAccounts.push({
        id: nanoid(),
        accountId: account.value,
        nickname: account.label,
        sysUserId: user.sysUserId,
        sysUserNickname: user.name,
        teamName: currentTeam?.label,
      });
    });
  });

  return sourceAccounts;
};

// 检查重复数据
export const checkDuplicateData = (
  existingData: DouyinImManualSourceAccount[],
  newData: DouyinImManualSourceAccount[],
) => {
  const duplicates: DouyinImManualSourceAccount[] = [];
  const uniqueData: DouyinImManualSourceAccount[] = [];

  newData.forEach((current) => {
    const isDuplicate = existingData.some(
      (item) => item.accountId === current.accountId && item.sysUserId === current.sysUserId,
    );
    if (isDuplicate) {
      duplicates.push(current);
    } else {
      uniqueData.push(current);
    }
  });

  return { duplicates, uniqueData };
};
