import DefaultAvatar from '@/assets/default-avatar.png';
import { useControlModel, WithControlPropsType } from '@ant-design/pro-components';
import { Avatar, InputNumber, List } from 'antd';
import { DouyinImManualTeam } from '@/services/douyin-im/manual-config';

export default function CustomerList(props: WithControlPropsType) {
  const model = useControlModel(props);
  const { value, onChange } = model;

  const handleMaxConnectionsChange = (newValue: number | null, sysUserId: number | undefined) => {
    if (newValue === null) return;
    onChange(
      value.map((item: DouyinImManualTeam) =>
        item.sysUserId === sysUserId ? { ...item, receptionLimit: newValue } : item,
      ),
    );
  };

  return (
    <List
      dataSource={value as DouyinImManualTeam[]}
      split={false}
      style={{
        width: '700px',
        maxHeight: '300px',
      }}
      size="small"
      className="customer-list"
      header={
        <div className="sticky top-0 z-10 mt-[5px] flex justify-between bg-white">
          <div className="flex items-center text-new-media-gray-600">
            已添加: <span className="text-new-media-blue-900">{value?.length}</span>
          </div>
        </div>
      }
      renderItem={(item) => (
        <List.Item key={item.sysUserId} className="hover:bg-gray-50">
          <List.Item.Meta
            avatar={<Avatar size={40} src={DefaultAvatar} />}
            title={item.name}
            description={item.email}
          />
          <div className="mr-5 flex items-center gap-2 text-gray-600">
            <span>接待上限：</span>
            <InputNumber
              style={{ width: '120px' }}
              value={item.receptionLimit}
              onChange={(value) => handleMaxConnectionsChange(value, item.sysUserId)}
              suffix="人"
              min={1}
              max={999}
              precision={0}
              step={1}
            />
          </div>
        </List.Item>
      )}
    />
  );
}
