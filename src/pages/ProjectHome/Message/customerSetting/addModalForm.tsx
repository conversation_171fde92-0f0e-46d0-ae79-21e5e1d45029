import useProjectId from '@/hooks/useProjectId';
import { DouyinImManualTeam } from '@/services/douyin-im/manual-config';
import {
  ManualSourceAccountRuleTeamList,
  ManualSourceAccountRuleTeamProjectAccountUserList,
} from '@/services/douyin-im/manual-source';
import { ModalForm, ProFormCascader, ProFormSelect } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Button, Cascader, Form } from 'antd';
import { useState, type FC } from 'react';
import { DouyinImManualSourceAccount } from '@/services/douyin-im/manual-source';
import { buildTableData } from './utils';

type AddModalFormProps = {
  onFinish?: (values: DouyinImManualSourceAccount[]) => Promise<boolean | void>;
  receptionUserList?: DouyinImManualTeam[];
};

export type Option = {
  value: string;
  label: string;
  isLeaf?: boolean;
  children?: Option[];
};

const formItemLayout = {
  labelCol: { span: 5 },
  wrapperCol: { span: 19 },
};

const AddModalForm: FC<AddModalFormProps> = ({ onFinish, receptionUserList }) => {
  const projectId = useProjectId();
  const [options, setOptions] = useState<Option[]>([]);
  const [form] = Form.useForm();

  useRequest(() => ManualSourceAccountRuleTeamList({ projectId }), {
    refreshDeps: [projectId],
    onSuccess: (data) => {
      const cascaderOptions =
        data?.map((item) => ({
          value: item.teamCode,
          label: item.teamName,
          isLeaf: false,
        })) || [];
      setOptions(cascaderOptions as Option[]);
    },
  });

  const loadData = async (selectedOptions: Option[]) => {
    const targetOption = selectedOptions[selectedOptions.length - 1];

    try {
      const res = await ManualSourceAccountRuleTeamProjectAccountUserList({
        projectId,
        teamCode: targetOption.value,
      });

      targetOption.children =
        res.data?.map((item) => ({
          value: item.accountId,
          label: item.nickname,
          isLeaf: true,
        })) || [];
      setOptions([...options]);
    } catch (error) {
      console.error('加载子节点失败:', error);
    }
  };

  const loadAndSelectChildren = async (parentOption: Option) => {
    try {
      const res = await ManualSourceAccountRuleTeamProjectAccountUserList({
        projectId,
        teamCode: parentOption.value,
      });

      const children =
        res.data?.map((item) => ({
          value: item.accountId,
          label: item.nickname,
          isLeaf: true,
        })) || [];

      const newOptions = options.map((opt) =>
        opt.value === parentOption.value ? { ...opt, children } : opt,
      );
      setOptions(newOptions);

      const childrenValues = children.map((child) => ({
        teamCode: parentOption.value,
        accountId: child.value,
        values: [parentOption.value, child.value],
      }));

      const currentValue = form.getFieldValue('accountIdList') || [];

      form.setFieldsValue({
        accountIdList: [
          ...currentValue.filter((v: string[]) => v[0] !== parentOption.value),
          ...childrenValues.map((item) => item.values),
        ],
      });

      return children;
    } catch (error) {
      console.error('加载并选择子节点失败:', error);
      return [];
    }
  };

  const handleCascaderChange = async (value: string[][], selectedOptions: Option[][]) => {
    const targetOption = selectedOptions[selectedOptions.length - 1] as Option[];

    // 表示只勾选了父节点的 checkbox
    if (targetOption.length === 1 && !targetOption[0].isLeaf) {
      await loadAndSelectChildren(targetOption[0]);
    }
  };

  return (
    <ModalForm
      form={form}
      title="添加"
      trigger={<Button type="link">添加</Button>}
      onFinish={async (values) => {
        if (!values.accountIdList?.length || !values.sysUserIdList?.length) {
          return false;
        }
        const accountList = values.accountIdList.map((item: string[]) => item[1]);
        const selectedSysUserList = receptionUserList?.filter((user) =>
          values.sysUserIdList.includes(user.sysUserId),
        );
        const sourceAccounts = buildTableData(accountList, options, selectedSysUserList);

        return onFinish?.(sourceAccounts);
      }}
      width={600}
      layout="horizontal"
      labelAlign="left"
      {...formItemLayout}
      modalProps={{
        destroyOnHidden: true,
        maskClosable: false,
      }}
    >
      <ProFormCascader
        name="accountIdList"
        label="账号"
        placeholder="选择账号"
        fieldProps={{
          loadData,
          changeOnSelect: true,
          options,
          multiple: true,
          maxTagCount: 'responsive',
          showCheckedStrategy: Cascader.SHOW_CHILD,
          showSearch: {
            matchInputWidth: false,
          },
          onChange: handleCascaderChange,
        }}
      />
      <ProFormSelect
        mode="multiple"
        name="sysUserIdList"
        label="负责接待客服"
        placeholder="选择负责接待客服"
        fieldProps={{
          options: receptionUserList?.map((item) => ({
            value: item.sysUserId,
            label: item.name,
          })),
        }}
      />
    </ModalForm>
  );
};

export default AddModalForm;
