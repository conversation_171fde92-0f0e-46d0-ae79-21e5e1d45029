import {
  FooterToolbar,
  ProForm,
  ProFormDependency,
  ProFormDigit,
  ProFormRadio,
  ProFormSwitch,
  ProFormTimePicker,
} from '@ant-design/pro-components';
import { Form, message, Space } from 'antd';
import AccountAllocationTable from './accountAllocationTable';
import CustomerList from './customerList';
import {
  DouyinImManualConfig,
  ManualConfigQuery,
  ManualConfigUpdate,
} from '@/services/douyin-im/manual-config';
import useProjectId from '@/hooks/useProjectId';
import {
  DouyinImManualSourceAccount,
  ManualSourceAccountRuleList,
  ManualSourceAccountRuleSave,
} from '@/services/douyin-im/manual-source';
import React from 'react';
import { transformToApiData } from './utils';
import { useAccess } from '@umijs/max';

const formItemLayout = {
  labelCol: { span: 2 },
  wrapperCol: { span: 22 },
};

type CustomerSettingProps = {
  isActiveKey: boolean;
};

type DouyinImManualProFormConfig = Omit<DouyinImManualConfig, 'projectId'> & {
  sourceAccountData?: DouyinImManualSourceAccount[];
};

export default function CustomerSetting(props: CustomerSettingProps) {
  const { isActiveKey } = props;
  const access = useAccess();
  const canWriteProject = access.canWriteProject;
  const projectId = useProjectId();
  const [form] = Form.useForm();
  const receptionUserList = Form.useWatch('receptionUserList', form);

  return (
    <ProForm<DouyinImManualProFormConfig>
      layout="horizontal"
      labelAlign="left"
      colon={false}
      disabled={!canWriteProject}
      form={form}
      {...formItemLayout}
      submitter={{
        render: (_, dom) => (isActiveKey ? <FooterToolbar>{dom}</FooterToolbar> : null),
      }}
      request={async () => {
        const configRes = await ManualConfigQuery({ projectId });
        let sourceAccountData: DouyinImManualSourceAccount[] = [];
        if (configRes.data?.sourceAccountAssignRuleStatus) {
          const sourceAccountRes = await ManualSourceAccountRuleList({ projectId });
          sourceAccountData = sourceAccountRes.data || [];
        }
        if (configRes.data) {
          return {
            ...configRes.data,
            sourceAccountData,
          } as DouyinImManualProFormConfig;
        }
        return {};
      }}
      onFinish={async (values) => {
        const { sourceAccountData, ...restValues } = values;
        try {
          if (
            values.sourceAccountAssignRuleStatus === 1 &&
            sourceAccountData &&
            sourceAccountData.length > 0
          ) {
            const sourceAccountRuleList = transformToApiData(sourceAccountData);
            await Promise.all([
              ManualConfigUpdate({ ...restValues, projectId }),
              ManualSourceAccountRuleSave({ projectId, sourceAccountRuleList }),
            ]);
          } else {
            await ManualConfigUpdate({ ...restValues, projectId });
          }
          message.success('配置成功');
        } catch (error) {
          message.error('配置失败');
        }
      }}
    >
      <div className="mb-4 text-base font-medium">客服团队列表</div>
      <ProForm.Item name="receptionUserList" label="客服人员">
        <CustomerList />
      </ProForm.Item>

      <div className="mb-4 text-base font-medium">服务时段</div>
      <ProFormTimePicker.RangePicker
        name="effectTime"
        label="人工服务时段"
        fieldProps={{
          format: 'HH:mm',
        }}
      />
      <div className="mb-4 text-base font-medium">接待不及时智能客服介入</div>
      <ProFormSwitch
        name="robotInterventionStatus"
        label="是否启用"
        transform={(value) => ({
          robotInterventionStatus: value ? 1 : 0,
        })}
      />

      <ProFormDependency name={['robotInterventionStatus']}>
        {({ robotInterventionStatus }) => (
          <div className={robotInterventionStatus ? 'block' : 'hidden'}>
            <Form.Item label="人工客服≥" labelCol={{ span: 2 }}>
              <Space>
                <ProFormDigit
                  name="robotInterventionTime"
                  min={1}
                  fieldProps={{
                    precision: 0,
                    suffix: '分',
                  }}
                  width={110}
                  noStyle
                  rules={[
                    {
                      type: 'number',
                      min: 1,
                      max: 10,
                      message: '请输入1-10之间的整数',
                      validateTrigger: ['onChange'],
                    },
                  ]}
                />
                <span>未回复时，为接待不及时</span>
              </Space>
            </Form.Item>
            <div className="mb-7 text-new-media-gray-500">
              此功能不包含7天内已留资的会话，且不受「智能客服总开关」控制。回复内容来源于知识库。
            </div>
          </div>
        )}
      </ProFormDependency>

      <div className="mb-4 text-base font-medium">客服分流规则</div>
      <div className="mb-4 text-new-media-gray-500">
        如分流规则与下方配置的分配规则同时命中，将按
        <span className="text-new-media-blue-900">{`按账号分配>分流方式`}</span>
        的优先级分配客服
      </div>

      <ProFormSwitch
        name="sourceAccountAssignRuleStatus"
        label="按来源账号分配"
        transform={(value) => ({
          sourceAccountAssignRuleStatus: value ? 1 : 0,
        })}
      />

      <ProFormDependency name={['sourceAccountAssignRuleStatus']}>
        {({ sourceAccountAssignRuleStatus }) => (
          <ProForm.Item
            label="账号分配规则"
            name="sourceAccountData"
            className={sourceAccountAssignRuleStatus ? 'block' : 'hidden'}
          >
            <AccountAllocationTable receptionUserList={receptionUserList} />
          </ProForm.Item>
        )}
      </ProFormDependency>

      <ProFormRadio.Group
        name="shuntRule"
        label="分流方式"
        options={[
          { label: '接待量不饱和的客服优先', value: 1 },
          // { label: '轮流分配', value: 2, disabled: true }, // 暂时隐藏
        ]}
      />
    </ProForm>
  );
}
