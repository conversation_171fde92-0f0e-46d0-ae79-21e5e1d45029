import { cn } from '@/lib/utils';
import { RobotTimeConfig } from '@/services/message-robot';
import React, { useState } from 'react';

const weekDays = ['mon', 'tue', 'wed', 'thu', 'fri', 'sat', 'sun'] as const;
type WeekDay = (typeof weekDays)[number];

const weekDayDisplay: Record<WeekDay, string> = {
  mon: '周一',
  tue: '周二',
  wed: '周三',
  thu: '周四',
  fri: '周五',
  sat: '周六',
  sun: '周日',
};

interface TimePeriodSelectorProps {
  value?: RobotTimeConfig;
  onChange?: (selectedTime: RobotTimeConfig) => void;
}

type TimeRange = {
  start: number;
  end: number;
};

type FormattedTime = {
  day: string;
  time: string;
};

const convertHoursToRanges = (hours: number[]): TimeRange[] => {
  if (!hours.length) return [];

  const ranges: TimeRange[] = [];
  let start = hours[0];
  let prev = hours[0];

  for (let i = 1; i <= hours.length; i++) {
    if (i === hours.length || hours[i] !== prev + 1) {
      ranges.push({ start, end: prev + 1 });
      if (i < hours.length) {
        start = hours[i];
        prev = hours[i];
      }
    } else {
      prev = hours[i];
    }
  }

  return ranges;
};

const formatTimeRange = (range: TimeRange): string => {
  const { start, end } = range;
  return `${String(start).padStart(2, '0')}:00~${String(end).padStart(2, '0')}:00`;
};

export const formatSelectedTimes = (selectedSlots: RobotTimeConfig): FormattedTime[] => {
  return Object.entries(selectedSlots)
    .filter(([, hours]) => hours.length > 0)
    .map(([day, hours]) => ({
      day: weekDayDisplay[day as WeekDay],
      time: convertHoursToRanges(hours).map(formatTimeRange).join('、'),
    }));
};

export const formatSelectedTimesToString = (selectedSlots: RobotTimeConfig): string => {
  return formatSelectedTimes(selectedSlots)
    .map(({ day, time }) => `${day} ${time}`)
    .join('\n');
};

export default function TimePeriodSelector({ value, onChange }: TimePeriodSelectorProps) {
  const [selectedSlots, setSelectedSlots] = useState<RobotTimeConfig>(
    value || {
      mon: [],
      tue: [],
      wed: [],
      thu: [],
      fri: [],
      sat: [],
      sun: [],
    },
  );
  const [isSelecting, setIsSelecting] = useState(false);
  const [selectionStart, setSelectionStart] = useState<{ day: WeekDay; hour: number } | null>(null);
  const [currentSelecting, setCurrentSelecting] = useState<{ day: WeekDay; hours: number[] }>({
    day: 'mon',
    hours: [],
  });
  const [isUnselecting, setIsUnselecting] = useState(false);
  const [pendingUnselect, setPendingUnselect] = useState<{ day: WeekDay; hours: number[] }>({
    day: 'mon',
    hours: [],
  });

  const handleClick = (day: WeekDay, hour: number) => {
    if (isSelecting) return;

    setSelectedSlots((prev) => {
      const currentHours = prev[day];
      const newSelectedSlots = currentHours?.includes(hour)
        ? {
            ...prev,
            [day]: currentHours?.filter((h) => h !== hour),
          }
        : {
            ...prev,
            [day]: [...(currentHours || []), hour].sort((a, b) => a - b),
          };

      // 触发onChange回调
      onChange?.(newSelectedSlots);
      return newSelectedSlots;
    });
  };

  const handleMouseDown = (day: WeekDay, hour: number) => {
    setIsSelecting(true);
    setSelectionStart({ day, hour });
    setIsUnselecting(selectedSlots[day]?.includes(hour) ?? false);
  };

  const handleMouseEnter = (day: WeekDay, hour: number) => {
    if (!isSelecting || !selectionStart || selectionStart.day !== day) return;

    const minHour = Math.min(selectionStart.hour, hour);
    const maxHour = Math.max(selectionStart.hour, hour);
    const hours = Array.from({ length: maxHour - minHour + 1 }, (_, i) => minHour + i);

    setCurrentSelecting({ day, hours });

    if (isUnselecting) {
      setPendingUnselect({ day, hours });
    } else {
      setSelectedSlots((prev) => {
        const newSelectedSlots = {
          ...prev,
          [day]: Array.from(new Set([...(prev[day] || []), ...hours])).sort((a, b) => a - b),
        };
        onChange?.(newSelectedSlots);
        return newSelectedSlots;
      });
    }
  };

  const handleMouseUp = () => {
    if (isUnselecting && pendingUnselect.hours.length > 0) {
      setSelectedSlots((prev) => {
        const newSelectedSlots = {
          ...prev,
          [pendingUnselect.day]: (prev[pendingUnselect.day] || []).filter(
            (h) => !pendingUnselect.hours.includes(h),
          ),
        };
        onChange?.(newSelectedSlots);
        return newSelectedSlots;
      });
    }

    setIsSelecting(false);
    setSelectionStart(null);
    setCurrentSelecting({ day: 'mon', hours: [] });
    setPendingUnselect({ day: 'mon', hours: [] });
    setIsUnselecting(false);
  };

  const handleClear = () => {
    const emptySlots = {
      mon: [],
      tue: [],
      wed: [],
      thu: [],
      fri: [],
      sat: [],
      sun: [],
    };
    setSelectedSlots(emptySlots);
    // 触发onChange回调
    onChange?.(emptySlots);
  };

  return (
    <div className="mx-auto my-10 w-[520px] border border-gray-200 bg-white p-5 md:w-[700px]">
      {/* 表格区域 */}
      <table className="w-full border">
        <thead className="border-b">
          <tr>
            <th rowSpan={2} className="border p-1 text-center text-xs font-normal">
              日期/时间
            </th>
            <th colSpan={12} className="border p-1 text-center text-xs font-normal">
              00:00~12:00
            </th>
            <th colSpan={12} className="border p-1 text-center text-xs font-normal">
              12:00~24:00
            </th>
          </tr>
          <tr>
            {Array.from({ length: 24 }).map((_, i) => (
              <th key={i} className="p-1 text-center text-xs font-normal">
                {i}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {weekDays.map((day) => (
            <tr key={day}>
              <td className="h-8 w-14 border-b text-center">{weekDayDisplay[day]}</td>
              {Array.from({ length: 24 }).map((_, hourIndex) => (
                <td
                  key={hourIndex}
                  className={cn(
                    'h-[30px] w-[20px] cursor-pointer border border-gray-200 transition-colors',
                    {
                      'bg-new-media-blue-950':
                        isSelecting &&
                        currentSelecting.day === day &&
                        currentSelecting.hours.includes(hourIndex),
                      'bg-new-media-blue-900 hover:bg-new-media-blue-800':
                        selectedSlots[day as WeekDay]?.includes(hourIndex) &&
                        !(
                          isSelecting &&
                          currentSelecting.day === day &&
                          currentSelecting.hours.includes(hourIndex)
                        ),
                      'bg-white hover:bg-new-media-gray-50':
                        !selectedSlots[day as WeekDay]?.includes(hourIndex),
                    },
                  )}
                  onMouseDown={() => handleMouseDown(day, hourIndex)}
                  onMouseEnter={() => handleMouseEnter(day, hourIndex)}
                  onMouseUp={handleMouseUp}
                  onClick={() => handleClick(day, hourIndex)}
                />
              ))}
            </tr>
          ))}
        </tbody>
      </table>

      <div className="my-3 mt-5 border-b border-gray-200" />

      <div className="flex items-center justify-between">
        <div className="mb-2">已选择时间:</div>
        <a onClick={handleClear} className="cursor-pointer text-blue-500 hover:text-blue-600">
          清空
        </a>
      </div>
      <div className="flex flex-col gap-2">
        {formatSelectedTimes(selectedSlots).map(({ day, time }, index) => (
          <div key={index} className="flex">
            <span className="min-w-[75px]">{day}</span>
            <span className="flex-1 break-all text-new-media-gray-600">{time}</span>
          </div>
        ))}
      </div>
    </div>
  );
}
