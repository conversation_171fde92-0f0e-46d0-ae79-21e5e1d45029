import useProjectId from '@/hooks/useProjectId';
import {
  AddKnowledge,
  DeleteKnowledge,
  DownloadTemplate,
  GetKnowledgeBasePage,
  GetProblemTypeSelectData,
  KnowledgeBaseItem,
  PostSwitchStatus,
  ProblemTypeItem,
  UpdateKnowledge,
} from '@/services/knowledge-base';
import { proTableRequestAdapter } from '@/utils';
import { proTablePaginationConfig, proTableSearchConfig } from '@/utils/proTableConfig';
import { InboxOutlined } from '@ant-design/icons';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import {
  Button,
  FormInstance,
  message,
  Modal,
  Popconfirm,
  Space,
  Switch,
  Typography,
  Upload,
} from 'antd';
import saveAs from 'file-saver';
import { Download } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import ChatExperience from '../chatExperience';
import KnowledgeForm, { KnowledgeFormValues } from './knowledgeFrom';
import QuestionTypeModal from './questionTypeModal';
import { useRequest } from '@umijs/max';

type KnowledgeRightTableProps = {
  selectedGroupId: number | null;
};

const { Paragraph } = Typography;

export default function KnowledgeRightTable({ selectedGroupId }: KnowledgeRightTableProps) {
  const projectId = useProjectId();
  const [open, setOpen] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [knowledgeModalOpen, setKnowledgeModalOpen] = useState(false);
  const [editingRecord, setEditingRecord] = useState<KnowledgeBaseItem | undefined>();
  const [formType, setFormType] = useState<'create' | 'edit'>('create');
  const actionRef = useRef<ActionType>();
  const formRef = useRef<FormInstance>();
  const [questionTypeModalOpen, setQuestionTypeModalOpen] = useState(false);
  const [loading, setLoading] = useState(false);

  const { data: problemTypeData, refresh: refreshProblemType } = useRequest(
    () => {
      if (!selectedGroupId) {
        return Promise.resolve([]);
      }
      return GetProblemTypeSelectData({
        projectId,
        knowledgeBaseGroupId: selectedGroupId,
      });
    },
    {
      refreshDeps: [projectId, selectedGroupId],
    },
  );

  const selectData = problemTypeData
    ? (problemTypeData as ProblemTypeItem[]).reduce((acc, item) => {
        // @ts-ignore
        acc[item.value] = item.name;
        return acc;
      }, {})
    : {};

  const handleEdit = (record: KnowledgeBaseItem) => {
    setFormType('edit');
    setEditingRecord(record);
    setKnowledgeModalOpen(true);
  };

  const handleCreate = () => {
    setFormType('create');
    setEditingRecord(undefined);
    setKnowledgeModalOpen(true);
  };

  const handleKnowledgeFormOk = async (values: KnowledgeFormValues) => {
    if (!selectedGroupId) {
      message.error('请先选择分组');
      return;
    }
    setKnowledgeModalOpen(false);
    if (formType === 'create') {
      setLoading(true);
      const res = await AddKnowledge({
        ...values,
        projectId,
        groupId: selectedGroupId,
      });
      if (res.code === 0) {
        message.success('新建知识成功');
      }
      setLoading(false);
    } else {
      if (editingRecord?.id) {
        setLoading(true);
        const res = await UpdateKnowledge({
          ...values,
          projectId,
          groupId: selectedGroupId,
          id: editingRecord?.id,
        });
        if (res.code === 0) {
          message.success('更新知识成功');
        }
        setLoading(false);
      }
    }
    actionRef.current?.reload();
  };

  const columns: ProColumns<KnowledgeBaseItem>[] = [
    {
      title: '问题类型',
      dataIndex: 'problemTypeName',
      width: 100,
      valueType: 'select',
      valueEnum: selectData,
      search: {
        transform: (value) => {
          return {
            problemTypeId: value,
          };
        },
      },
      fieldProps: {
        placeholder: '问题类型',
      },
      formItemProps: {
        label: null,
      },
    },
    {
      title: '用户问题',
      dataIndex: 'problemContent',
      width: 150,
      search: true,
      fieldProps: {
        placeholder: '搜索用户问题',
      },
      formItemProps: {
        label: null,
      },
    },
    {
      title: '回复内容',
      dataIndex: 'answerContent',
      width: 300,
      hideInSearch: true,
      render: (text) => {
        return (
          <Paragraph
            ellipsis={{
              rows: 5,
              expandable: 'collapsible',
            }}
          >
            {text}
          </Paragraph>
        );
      },
    },
    {
      title: '编辑人',
      dataIndex: 'operator',
      width: 100,
      hideInSearch: true,
    },
    {
      title: '编辑时间',
      dataIndex: 'createTime',
      width: 150,
      hideInSearch: true,
    },
    {
      title: '启用',
      dataIndex: 'status',
      width: 80,
      hideInSearch: true,
      render: (_, record, idx, action) => (
        <Switch
          checked={record.status === 1}
          onChange={async () => {
            await PostSwitchStatus({
              projectId,
              id: record.id,
              status: record.status === 1 ? 0 : 1,
            });
            action?.reload();
          }}
        />
      ),
    },
    {
      title: '操作',
      width: 80,
      hideInSearch: true,
      render: (_, record: KnowledgeBaseItem) => <a onClick={() => handleEdit(record)}>编辑</a>,
    },
  ];

  const handleImport = () => {
    setIsModalOpen(true);
  };

  const handleModalCancel = () => {
    setIsModalOpen(false);
  };

  const handleDownloadTemplate = async () => {
    const downloadBlob = await DownloadTemplate();
    saveAs(downloadBlob, '导入模版.xlsx');
  };

  const handlePatchDelete = async (ids: number[]) => {
    const res = await DeleteKnowledge({
      projectId,
      ids,
    });
    res.code === 0 ? message.success('删除成功') : message.error('删除失败');
    actionRef.current?.clearSelected?.();
    actionRef.current?.reload();
  };

  const handleQuestionType = () => {
    setQuestionTypeModalOpen(true);
  };

  const uploadProps = {
    name: 'file',
    maxCount: 1,
    multiple: false,
    accept: '.xlsx,.xls',
    action: `/new-media-api/douyin/private-message/knowledge-base/import?projectId=${projectId}&groupId=${selectedGroupId}`,
    headers: {
      contentType: 'multipart/form-data',
    },
    showUploadList: false,
    onChange: (info: any) => {
      const { status } = info.file;
      if (status === 'done') {
        const respond = info.file.response;
        if (respond.code !== 0) {
          message.error(`${info.file.name} 文件上传失败, 错误原因: ${respond.msg}`);
        } else {
          message.success(`${info.file.name} 文件上传成功`);
        }
        setIsModalOpen(false);
        actionRef.current?.reload();
        refreshProblemType();
      } else if (status === 'error') {
        message.error(`${info.file.name} 文件上传失败`);
      }
    },
  };

  useEffect(() => {
    actionRef.current?.clearSelected?.();
    actionRef.current?.reset?.();
    formRef.current?.resetFields();
  }, [selectedGroupId]);

  return (
    <>
      <ProTable<KnowledgeBaseItem>
        columns={columns}
        formRef={formRef}
        actionRef={actionRef}
        rowKey="id"
        tableClassName="custom-table"
        scroll={{ x: 'max-content', y: 'calc(100vh - 56px - 100px - 290px)' }}
        options={false}
        loading={loading}
        search={{ ...proTableSearchConfig }}
        pagination={{ ...proTablePaginationConfig }}
        params={{
          projectId,
          groupId: selectedGroupId ?? undefined,
        }}
        request={(params, sorter, filter) => {
          if (!selectedGroupId) {
            return Promise.resolve({
              data: [],
              success: true,
              total: 0,
            });
          }
          return proTableRequestAdapter(params, sorter, filter, GetKnowledgeBasePage);
        }}
        rowSelection={{
          type: 'checkbox',
          preserveSelectedRowKeys: true,
        }}
        cardProps={{
          bodyStyle: { paddingInline: 0, paddingTop: 0 },
        }}
        toolBarRender={() => [
          <Button
            type="link"
            key="experience"
            href="#"
            className="float-right"
            onClick={(e) => {
              e.preventDefault();
              setOpen(true);
            }}
          >
            智能销售客服体验
          </Button>,
        ]}
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        tableAlertOptionRender={({ selectedRowKeys, selectedRows, onCleanSelected }) => {
          return (
            <Space size={16}>
              <Popconfirm
                title={'是否确认删除'}
                onConfirm={() => handlePatchDelete(selectedRowKeys as number[])}
                key={'delete'}
              >
                <a>批量删除</a>
              </Popconfirm>

              <a onClick={onCleanSelected}>取消选择</a>
            </Space>
          );
        }}
        toolbar={{
          title: (
            <Space>
              <Button onClick={handleImport}>批量导入</Button>
              <Button onClick={handleQuestionType}>问题类型管理</Button>
              <Button type="primary" onClick={handleCreate}>
                新建知识
              </Button>
            </Space>
          ),
        }}
      />

      <Modal title="批量导入知识库" open={isModalOpen} onCancel={handleModalCancel} footer={null}>
        <Upload.Dragger {...uploadProps}>
          <p className="ant-upload-drag-icon">
            <InboxOutlined />
          </p>
          <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
        </Upload.Dragger>

        <Space style={{ marginBlock: '4px' }}>
          <Button type="link" onClick={handleDownloadTemplate} style={{ padding: 0 }}>
            <Download size={14} />
            下载导入模板
          </Button>
          <div className="text-[#A2A4A8]">支持 .xlsx、.xls 格式的文件</div>
        </Space>
      </Modal>

      <KnowledgeForm
        open={knowledgeModalOpen}
        onCancel={() => setKnowledgeModalOpen(false)}
        onOk={handleKnowledgeFormOk}
        initialValues={editingRecord}
        type={formType}
        key={formType}
        selectData={selectData}
      />

      <Modal
        title="智能销售客服体验"
        open={open}
        onCancel={() => setOpen(false)}
        footer={null}
        width={800}
      >
        <ChatExperience />
      </Modal>

      <QuestionTypeModal
        open={questionTypeModalOpen}
        onClose={() => setQuestionTypeModalOpen(false)}
        projectId={projectId}
        knowledgeBaseGroupId={selectedGroupId}
        problemTypeData={problemTypeData as ProblemTypeItem[]}
        refreshProblemType={refreshProblemType}
        actionRef={actionRef}
      />
    </>
  );
}
