import { KnowledgeBaseItem } from '@/services/knowledge-base';
import { ProFormSelect } from '@ant-design/pro-components';
import { Form, Input, message, Modal } from 'antd';
import { useEffect } from 'react';

type KnowledgeFormProps = {
  open: boolean;
  onCancel: () => void;
  onOk: (values: KnowledgeFormValues) => void;
  initialValues?: KnowledgeBaseItem;
  type: 'create' | 'edit';
  selectData?: Record<string, string>;
};

export type KnowledgeFormValues = {
  problemTypeId: number;
  problemContent: string;
  answerContent: string;
};

const KnowledgeForm = (props: KnowledgeFormProps) => {
  const { open, onCancel, onOk, initialValues, type, selectData } = props;
  const [form] = Form.useForm();

  useEffect(() => {
    if (open && initialValues) {
      form.setFieldsValue(initialValues);
    }
  }, [open, initialValues, form]);

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      onOk(values);
      form.resetFields();
    } catch (error) {
      message.error('请检查表单填写是否正确');
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      title={type === 'create' ? '新建知识' : '编辑知识'}
      open={open}
      onOk={handleOk}
      onCancel={handleCancel}
      destroyOnHidden
    >
      <Form<KnowledgeFormValues> form={form} layout="vertical" initialValues={initialValues}>
        <ProFormSelect
          name="problemTypeId"
          label="问题类型"
          rules={[{ required: true, message: '请选择问题类型' }]}
          valueEnum={selectData}
        />

        <Form.Item
          name="problemContent"
          label="用户问题"
          rules={[{ required: true, message: '请输入用户问题' }]}
        >
          <Input.TextArea
            placeholder="请输入用户可能发送的问题"
            maxLength={300}
            showCount
            rows={4}
          />
        </Form.Item>

        <Form.Item
          name="answerContent"
          label="回复答案"
          rules={[{ required: true, message: '请输入回复内容' }]}
        >
          <Input.TextArea
            placeholder="请输入根据上述问题，回复用户的话术"
            maxLength={300}
            showCount
            rows={4}
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default KnowledgeForm;
