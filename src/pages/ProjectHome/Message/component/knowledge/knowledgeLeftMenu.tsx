import Wiki from '@/assets/svg/wiki.svg';
import useProjectId from '@/hooks/useProjectId';
import { cn } from '@/lib/utils';
import {
  GetKnowledgeGroupList,
  KnowledgeGroupItem,
  CreateKnowledgeGroup,
  DeleteKnowledgeGroup,
  UpdateKnowledgeGroup,
} from '@/services/knowledge-base';
import { DeleteOutlined, EditOutlined } from '@ant-design/icons';
import { useRequest } from '@umijs/max';
import { Button, Input, message, Popconfirm, Tooltip } from 'antd';
import { Plus } from 'lucide-react';
import { useState } from 'react';

type KnowledgeLeftMenuProps = {
  selectedGroupId: number | null;
  onGroupSelect: (groupId: number | null) => void;
};

export default function KnowledgeLeftMenu({
  selectedGroupId,
  onGroupSelect,
}: KnowledgeLeftMenuProps) {
  const projectId = useProjectId();
  const [isAddingGroup, setIsAddingGroup] = useState(false);
  const [newGroupName, setNewGroupName] = useState('');
  const [editingGroupId, setEditingGroupId] = useState<number | null>(null);

  const { data: groupsData, refresh: refreshGroups } = useRequest(
    () => GetKnowledgeGroupList({ projectId }),
    {
      refreshDeps: [projectId],
      onSuccess: (res) => {
        if (res && res?.length > 0 && !selectedGroupId) {
          onGroupSelect(res[0].id);
        }
      },
    },
  );

  const handleAddGroup = () => {
    setIsAddingGroup(true);
  };

  const handleConfirmAddGroup = async (e?: React.MouseEvent) => {
    e?.stopPropagation();
    if (newGroupName.trim()) {
      await CreateKnowledgeGroup({
        projectId,
        groupName: newGroupName.trim(),
      });
      setIsAddingGroup(false);
      setNewGroupName('');
      refreshGroups();
    }
  };

  const handleCancelAddGroup = (e?: React.MouseEvent) => {
    e?.stopPropagation();
    setIsAddingGroup(false);
    setNewGroupName('');
  };

  const handleDeleteGroup = async (id: number) => {
    if (groupsData && groupsData.length <= 1) {
      message.warning('请至少保留一个分组');
      return;
    }
    await DeleteKnowledgeGroup({
      id,
      projectId,
    });
    onGroupSelect(null);
    refreshGroups();
  };

  const handleEditGroup = async (id: number, groupName: string) => {
    await UpdateKnowledgeGroup({
      id,
      projectId,
      groupName,
    });
    setEditingGroupId(null);
    refreshGroups();
  };

  const renderMenuItem = (group: KnowledgeGroupItem) => {
    if (editingGroupId === group.id) {
      return (
        <Input
          autoFocus
          defaultValue={group.groupName}
          onBlur={(e) => handleEditGroup(group.id, e.target.value)}
          onPressEnter={(e) => handleEditGroup(group.id, (e.target as HTMLInputElement).value)}
          onClick={(e) => e.stopPropagation()}
          placeholder="输入名称后按enter确认"
          style={{ height: '42px ' }}
        />
      );
    }

    return (
      <div
        key={group.id}
        className={cn(
          'mx-1 my-1 cursor-pointer rounded-md px-3 py-2.5 transition-colors',
          selectedGroupId === group.id
            ? 'bg-[#EAEFFD] text-primary hover:bg-[#EAEFFD]'
            : 'bg-transparent text-[#1f2329] hover:bg-[#f5f8ff]',
        )}
        onClick={() => onGroupSelect(group.id)}
      >
        <div className="flex items-center justify-between">
          <span>{group.groupName}</span>
          {selectedGroupId === group.id && (
            <div className="flex gap-3">
              <Tooltip title="编辑分组名称">
                <EditOutlined
                  onClick={(e) => {
                    e.stopPropagation();
                    setEditingGroupId(group.id);
                  }}
                />
              </Tooltip>
              <Popconfirm
                title="确定要删除吗？"
                onConfirm={() => handleDeleteGroup(group.id)}
                okText="确定"
                cancelText="取消"
              >
                <Tooltip title="删除分组">
                  <DeleteOutlined />
                </Tooltip>
              </Popconfirm>
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <>
      <div className="flex h-[40px] items-center justify-between gap-2 px-2">
        <div className="flex items-center gap-1">
          <img src={Wiki} />
          <div className="font-medium">知识库</div>
        </div>
        <Button
          size="small"
          shape="default"
          icon={<Plus size={16} strokeWidth={1.5} />}
          onClick={handleAddGroup}
          style={{ width: '20px', height: '20px' }}
        />
      </div>

      <div style={{ display: 'flex', flexDirection: 'column' }}>
        {groupsData && groupsData.map((group) => renderMenuItem(group))}
      </div>

      {isAddingGroup && (
        <div className="m-1">
          <Input
            autoFocus
            value={newGroupName}
            onChange={(e) => setNewGroupName(e.target.value)}
            onPressEnter={() => handleConfirmAddGroup()}
            onBlur={() => handleCancelAddGroup()}
            maxLength={10}
            placeholder="输入名称后按enter确认"
          />
        </div>
      )}
    </>
  );
}
