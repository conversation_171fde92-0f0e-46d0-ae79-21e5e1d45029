import { Modal, Input, Tag, message } from 'antd';
import { useState } from 'react';
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import { cn } from '@/lib/utils';
import {
  CreateProblemType,
  DeleteProblemType,
  ProblemTypeItem,
  UpdateProblemType,
} from '@/services/knowledge-base';
import { ActionType } from '@ant-design/pro-components';

type QuestionTypeModalProps = {
  open: boolean;
  onClose: () => void;
  projectId?: string;
  knowledgeBaseGroupId: number | null;
  problemTypeData?: ProblemTypeItem[];
  refreshProblemType: () => void;
  actionRef: React.MutableRefObject<ActionType | undefined>;
};

export default function QuestionTypeModal({
  open,
  onClose,
  projectId,
  knowledgeBaseGroupId,
  problemTypeData,
  refreshProblemType,
  actionRef,
}: QuestionTypeModalProps) {
  const [inputVisible, setInputVisible] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [editIndex, setEditIndex] = useState<number | null>(null);

  const handleAdd = async () => {
    if (!inputValue.trim()) {
      setInputVisible(false);
      setInputValue('');
      return;
    }

    if (problemTypeData?.some((item) => item.name === inputValue.trim())) {
      message.warning('该问题类型已存在');
      return;
    }

    if (projectId && knowledgeBaseGroupId) {
      const res = await CreateProblemType({
        projectId,
        knowledgeBaseGroupId,
        problemTypeName: inputValue.trim(),
      });
      if (res.code === 0) {
        message.success('新增问题类型成功');
        refreshProblemType();
      } else {
        message.error('新增问题类型失败');
      }
    }

    setInputValue('');
    setInputVisible(false);
  };

  const handleDelete = async (id: number) => {
    if (projectId && knowledgeBaseGroupId) {
      const res = await DeleteProblemType({ projectId, knowledgeBaseGroupId }, [id]);
      if (res.code === 0) {
        message.success('删除问题类型成功');
        refreshProblemType();
      } else {
        message.error('删除问题类型失败');
      }
    }
  };

  const startEdit = (index: number) => {
    setEditIndex(index);
    setInputValue(problemTypeData?.[index].name ?? '');
  };

  const handleEdit = async () => {
    if (!inputValue.trim()) {
      cancelEdit();
      return;
    }

    const otherTypes = problemTypeData?.filter((_, i) => i !== editIndex);
    if (otherTypes?.some((item) => item.name === inputValue.trim())) {
      message.warning('该问题类型已存在');
      return;
    }

    if (editIndex !== null && projectId && knowledgeBaseGroupId && problemTypeData) {
      const res = await UpdateProblemType({
        projectId,
        knowledgeBaseGroupId,
        problemTypeName: inputValue.trim(),
        id: problemTypeData?.[editIndex].value,
      });
      if (res.code === 0) {
        message.success('更新问题类型成功');
        refreshProblemType();
        actionRef.current?.reload();
      } else {
        message.error('更新问题类型失败');
      }
      setEditIndex(null);
      setInputValue('');
    }
  };

  const cancelEdit = () => {
    setEditIndex(null);
    setInputValue('');
  };

  return (
    <Modal open={open} onCancel={onClose} title="问题类型管理" width={600} footer={null}>
      <div className="mt-5 space-y-4">
        <div className="flex flex-wrap gap-2">
          {problemTypeData?.map((item, index) => (
            <div key={index} className="group relative">
              {editIndex === index ? (
                <Input
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  onPressEnter={handleEdit}
                  onBlur={cancelEdit}
                  autoFocus
                  className="w-24"
                />
              ) : (
                <Tag
                  className={cn(
                    'cursor-pointer px-3 py-1 text-sm group-hover:pr-12',
                    'transition-all duration-200',
                  )}
                  color="#335FF6"
                >
                  {item.name}
                  <span className="absolute right-2 hidden group-hover:inline-block">
                    <EditOutlined className="mr-1" onClick={() => startEdit(index)} />
                    <DeleteOutlined
                      className="hover:text-red-500"
                      onClick={() => handleDelete(item.value)}
                    />
                  </span>
                </Tag>
              )}
            </div>
          ))}
          {inputVisible ? (
            <Input
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              onPressEnter={handleAdd}
              onBlur={() => setInputVisible(false)}
              autoFocus
              maxLength={8}
              className="w-24"
            />
          ) : (
            <Tag
              className="cursor-pointer border-dashed px-3 py-1 text-sm"
              onClick={() => setInputVisible(true)}
            >
              <PlusOutlined /> 新增类型
            </Tag>
          )}
        </div>
        <div className="text-sm text-gray-400">
          提示：鼠标悬停在标签上可以编辑或删除，新建与编辑问题按 Enter (回车) 键确认
        </div>
      </div>
    </Modal>
  );
}
