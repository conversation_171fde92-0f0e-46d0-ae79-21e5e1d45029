import { Avatar } from 'antd';
import defaultAvatar from '@/assets/default-avatar.png';
import { cn } from '@/lib/utils';

type ChatBubbleProps = {
  content: React.ReactNode;
  isUser?: boolean;
};

export default function ChatBubble({ content, isUser }: ChatBubbleProps) {
  return (
    <div
      className={cn('mb-4 flex items-start gap-2', {
        'justify-end': isUser,
      })}
    >
      {!isUser && <Avatar size="small" src={<img src={defaultAvatar} />} alt="avatar" />}
      <div
        className={cn('max-w-[80%] rounded-lg p-2 text-sm shadow-sm', {
          'bg-[#4386F7] text-white': isUser,
          'bg-[#F3F3F4]': !isUser,
        })}
        style={{ lineBreak: 'anywhere' }}
      >
        {content}
      </div>
      {isUser && <Avatar size="small" src={<img src={defaultAvatar} />} alt="avatar" />}
    </div>
  );
}
