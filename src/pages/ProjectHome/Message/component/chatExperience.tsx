import { Bubble, Sender, Welcome } from '@ant-design/x';
import { useRequest } from 'ahooks';
import { PostExperience } from '@/services/message-robot';
import { useState, useRef, useEffect } from 'react';
import { cn } from '@/lib/utils';
import useProjectId from '@/hooks/useProjectId';
import RobotEnable from '@/assets/robot-enable.png';
import defaultAvatar from '@/assets/default-avatar.png';

export default function ChatExperience() {
  const projectId = useProjectId();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const conversationIdRef = useRef<string>();
  const [messages, setMessages] = useState<
    Array<{
      content: string;
      type: 'user' | 'robot';
      loading?: boolean;
    }>
  >([]);
  const [inputValue, setInputValue] = useState('');

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // 当消息列表更新时，滚动到底部
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const { loading, run } = useRequest(
    async (content: string) => {
      const res = await PostExperience({
        projectId,
        content,
        conversationId: conversationIdRef.current,
      });
      return res.data;
    },
    {
      manual: true,
      onSuccess: (data) => {
        if (data?.contentList?.length) {
          if (data.conversationId) {
            conversationIdRef.current = data.conversationId;
          }
          setMessages((prev) => [
            ...prev.slice(0, -1),
            ...data.contentList.map((content) => ({
              content,
              type: 'robot' as const,
            })),
          ]);
        } else {
          setMessages((prev) => prev.slice(0, -1));
        }
      },
    },
  );

  const handleSubmit = (content: string) => {
    setMessages((prev) => [
      ...prev,
      { content, type: 'user' },
      { content: '', type: 'robot', loading: true },
    ]);
    setInputValue('');
    run(content);
  };

  return (
    <div className="flex h-[600px] flex-col">
      <div className="no-scrollbar flex-1 overflow-y-auto p-4">
        <Welcome
          icon={<img src={RobotEnable} alt="robot" className="h-10" />}
          title="智能销售客服"
          description="您好,我是智能销售客服,请问有什么可以帮您?"
          style={{
            backgroundImage: 'linear-gradient(97deg, #f2f9fe 0%, #f7f3ff 100%)',
            borderStartStartRadius: 4,
          }}
        />

        <div className="mt-4 space-y-4">
          {messages.map((message, index) => (
            <Bubble
              key={index}
              content={message.content}
              placement={message.type === 'user' ? 'end' : 'start'}
              className={cn('max-w-[80%]', message.type === 'user' ? 'ml-auto' : 'mr-auto')}
              loading={message.loading}
              avatar={
                message.type === 'user' ? (
                  <img
                    src={defaultAvatar}
                    className="flex h-8 w-8 items-center justify-center rounded-full"
                  />
                ) : (
                  <img
                    src={RobotEnable}
                    className="flex h-8 w-8 items-center justify-center rounded-full"
                  />
                )
              }
            />
          ))}
          <div ref={messagesEndRef} />
        </div>
      </div>

      <div className="border-t p-4">
        <Sender
          loading={loading}
          disabled={loading}
          onSubmit={handleSubmit}
          placeholder="请输入您的问题..."
          value={inputValue}
          onChange={setInputValue}
        />
      </div>
    </div>
  );
}
