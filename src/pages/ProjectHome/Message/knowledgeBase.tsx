import BreadCrumbSimple from '@/components/ui/breadcrumbSimple';
import { PageContainer, ProCard } from '@ant-design/pro-components';
import { useState } from 'react';
import KnowledgeLeftMenu from './component/knowledge/knowledgeLeftMenu';
import KnowledgeRightTable from './component/knowledge/knowledgeRightTable';

export default function KnowledgeBase() {
  const [selectedGroupId, setSelectedGroupId] = useState<number | null>(null);

  return (
    <PageContainer title={<BreadCrumbSimple breadcrumbs={['私信管理', '知识库']} />}>
      <div className="h-[calc(100vh-56px-100px)] rounded-md bg-white">
        <ProCard ghost split="vertical" className="h-full">
          <ProCard colSpan="250px" ghost style={{ padding: '10px' }}>
            <KnowledgeLeftMenu
              onGroupSelect={setSelectedGroupId}
              selectedGroupId={selectedGroupId}
            />
          </ProCard>
          <ProCard className="h-full overflow-hidden">
            <KnowledgeRightTable selectedGroupId={selectedGroupId} />
          </ProCard>
        </ProCard>
      </div>
    </PageContainer>
  );
}
