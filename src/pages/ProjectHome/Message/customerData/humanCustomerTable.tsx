import DynamicTree from '@/components/dynamicTree';
import ExportButton from '@/components/exportButton';
import FieldSelect from '@/components/fieldSelect';
import { usePollingExport } from '@/hooks/usePollingExport';
import {
  ExportHumanCustomData,
  HumanCustomerData,
  HumanCustomerDataParams,
  HumanCustomerDataVO,
} from '@/services/custom-data';
import { GetTeamDepthSelect } from '@/services/team';
import {
  proTableOptionsConfig,
  proTablePaginationConfig,
  proTableSearchConfig,
} from '@/utils/proTableConfig';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { FormInstance, message } from 'antd';
import { useEffect, useRef, useState } from 'react';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { omit } from 'lodash-es';

type HumanCustomerTableProps = {
  rangeTime: (string | undefined)[];
  platform: number;
  projectId?: string;
  lastFieldId?: number;
  teamCodeList?: string[];
};

export default function HumanCustomerTable({
  rangeTime,
  platform,
  projectId,
  lastFieldId,
  teamCodeList,
}: HumanCustomerTableProps) {
  const actionRef = useRef<ActionType>();
  const isLocalSortRef = useRef(false);
  const [fieldId, setFieldId] = useState<number>();
  const [selectedDimensionName, setSelectedDimensionName] = useState<string>('');
  const { pollingExport, percent, pollingLoading } = usePollingExport();
  const paramsRef = useRef<any>({});
  const formRef = useRef<FormInstance>();

  const { data: teamDepthData } = useRequest(() => GetTeamDepthSelect({ projectId }), {
    onSuccess: (data) => {
      if (data?.[0]) {
        const item = data[0];
        setFieldId(Number(item.value));
        setSelectedDimensionName(item.name || '');
      }
    },
  });

  useEffect(() => {
    if (teamCodeList && teamCodeList?.length > 0) {
      formRef.current?.setFieldsValue({
        teamCodeList: teamCodeList,
      });
      formRef.current?.submit();
    }
  }, [teamCodeList]);

  const columns: ProColumns<HumanCustomerDataVO>[] = [
    {
      title: '动态维度筛选',
      dataIndex: 'teamCodeList',
      hideInTable: true,
      formItemProps: {
        label: null,
      },
      renderFormItem: () => {
        return <DynamicTree actionRef={actionRef} />;
      },
    },
    {
      title: '经销商编码',
      dataIndex: lastFieldId,
      hideInTable: true,
      formItemProps: {
        label: null,
      },
      search: {
        transform: (value) => {
          return {
            fieldList: [
              {
                fieldId: lastFieldId,
                value,
              },
            ],
          };
        },
      },
      renderFormItem: () => {
        if (!lastFieldId) {
          return null;
        }
        return <FieldSelect fieldId={lastFieldId} name="经销商编码" />;
      },
    },
    {
      title: selectedDimensionName || '聚合维度',
      dataIndex: 'aggregationName',
      width: 200,
      fixed: 'left',
      search: false,
    },
    {
      title: '会话总数',
      dataIndex: 'conversationCount',
      sorter: (a, b) => (Number(a.conversationCount) || 0) - (Number(b.conversationCount) || 0),
      width: 150,
      search: false,
      align: 'right',
      tooltip: '人工客服接待的会话总数，对应的具体会话可在历史会话页面查看',
    },
    {
      title: '咨询人数',
      dataIndex: 'consultUvCount',
      sorter: (a, b) => (Number(a.consultUvCount) || 0) - (Number(b.consultUvCount) || 0),
      width: 150,
      search: false,
      align: 'right',
      tooltip: '进入到咨询页的访客数，并按用户的抖音号等ID进行去重得到的数字',
    },
    {
      title: '消息数',
      dataIndex: 'messageCount',
      sorter: (a, b) => (Number(a.messageCount) || 0) - (Number(b.messageCount) || 0),
      width: 150,
      search: false,
      align: 'right',
      tooltip: '客服与用户间互发的对话总消息数',
    },
    {
      title: '留资数',
      dataIndex: 'leadsCount',
      sorter: (a, b) => (Number(a.leadsCount) || 0) - (Number(b.leadsCount) || 0),
      width: 150,
      search: false,
      align: 'right',
      tooltip: '有留下销售线索的访客人数',
    },
  ];

  const handleExportAccount = async () => {
    const omitPageParams = omit(paramsRef.current, [
      'pageSize',
      'current',
    ]) as HumanCustomerDataParams;
    const res = await ExportHumanCustomData(omitPageParams);
    if (res.data) {
      pollingExport({ taskId: res.data });
    } else {
      message.error('导出失败');
    }
  };

  return (
    <ProTable<HumanCustomerDataVO, HumanCustomerDataParams>
      ghost
      params={{
        projectId: Number(projectId),
        startDate: rangeTime[0] || '',
        endDate: rangeTime[1] || '',
        platform,
        fieldId,
      }}
      formRef={formRef}
      actionRef={actionRef}
      columns={columns}
      rowKey="aggregationName"
      tableClassName="custom-table"
      search={{ ...proTableSearchConfig }}
      options={{ ...proTableOptionsConfig }}
      pagination={{ ...proTablePaginationConfig }}
      scroll={{ x: 'max-content' }}
      onChange={(_, __, sorter, extra) => {
        if (extra.action === 'sort') {
          isLocalSortRef.current = true;
        }
      }}
      request={async (params) => {
        paramsRef.current = params;
        if (isLocalSortRef.current) {
          return Promise.reject();
        }
        if (!fieldId) {
          return Promise.reject();
        }
        const { data } = await HumanCustomerData(params);
        if (!data) {
          return {
            data: [],
            success: true,
            total: 0,
          };
        }
        // 处理分页
        const current = params.current || 1;
        const pageSize = params.pageSize || 10;
        const start = (current - 1) * pageSize;
        const end = start + pageSize;
        const pageData = data.slice(start, end);

        return {
          data: pageData,
          success: true,
          total: data.length,
        };
      }}
      headerTitle={
        teamDepthData && (
          <Tabs value={fieldId?.toString()} onValueChange={(value) => setFieldId(Number(value))}>
            <TabsList value={fieldId?.toString()}>
              {teamDepthData.map((item) => (
                <TabsTrigger
                  key={item.value}
                  value={item.value}
                  onClick={() => {
                    setFieldId(Number(item.value));
                    setSelectedDimensionName(item.name || '');
                  }}
                >
                  {item.name}
                </TabsTrigger>
              ))}
            </TabsList>
          </Tabs>
        )
      }
      toolBarRender={() => [
        <ExportButton
          exportFn={() => handleExportAccount()}
          loading={pollingLoading}
          percent={percent}
          key="export"
        />,
      ]}
      tableAlertRender={false}
      columnsState={{
        persistenceKey: 'HumanCustomerTable-Table-Columns',
        persistenceType: 'localStorage',
      }}
    />
  );
}
