import { Ava<PERSON>, Spin } from 'antd';
import defaultAvatar from '@/assets/default-avatar.png';
import { useInfiniteQuery } from '@umijs/max';
import { GetChatPage, ChatMessage } from '@/services/douyin-im/chat';
import { useInViewport } from 'ahooks';
import { cn } from '@/lib/utils';
import UserInfo from '@/pages/CustomerSystem/component/userInfo';
import { useRef, useEffect } from 'react';
import { Bubble } from '@ant-design/x';
import { renderMessageInChat } from '@/pages/CustomerSystem/component/utils';
import { nanoid } from 'nanoid';
import { ConversationDetailTableData } from '@/services/custom-data';

type ChatPreviewProps = {
  conversationInfo?: ConversationDetailTableData;
  projectId?: string;
};

export type ChatMessageType = ChatMessage & {
  direction: number;
  imageUrl?: string;
  title?: string;
  componentList?: number[];
};

export default function ChatPreview({ conversationInfo, projectId }: ChatPreviewProps) {
  const loadMoreRef = useRef<HTMLDivElement>(null);
  const [inViewport] = useInViewport(loadMoreRef);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const conversationId = conversationInfo?.conversationId;

  const { data, fetchNextPage, hasNextPage, isFetchingNextPage, isLoading } = useInfiniteQuery({
    queryKey: ['previewChatMessages', conversationId, projectId],
    queryFn: async ({ pageParam = 1 }) => {
      if (!projectId || !conversationId) {
        throw new Error('缺少必要参数');
      }

      const response = await GetChatPage({
        projectId,
        conversationId: conversationId,
        page: pageParam,
        size: 20,
      });

      return {
        list: response.data?.items || [],
        current: pageParam,
        total: response.data?.total || 0,
      };
    },
    enabled: !!projectId && !!conversationId,
    getNextPageParam: (lastPage) => {
      const hasMore = lastPage.list.length > 0 && lastPage.current * 20 < lastPage.total;
      return hasMore ? lastPage.current + 1 : undefined;
    },
    refetchOnWindowFocus: false,
  });

  useEffect(() => {
    if (inViewport && hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [inViewport, hasNextPage, isFetchingNextPage, fetchNextPage]);

  // 首次加载时滚动到底部
  useEffect(() => {
    if (!isLoading && messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [isLoading]);

  // 获取所有消息并转换为 MessageType 类型
  const messages: ChatMessageType[] =
    data?.pages.flatMap((page) =>
      page.list.map((item: any) => ({
        ...item,
        direction: item.messageEvent === 0 ? 2 : 1, // 0: 发送(客服) 1: 接收(客户)
      })),
    ) || [];

  return (
    <div className="flex h-full">
      <div className="flex h-full flex-1 flex-col overflow-hidden">
        {/* 用户信息头部 */}
        <div className="flex w-full items-center justify-between gap-4 px-4 py-4">
          <div className="flex items-center gap-2">
            <Avatar src={conversationInfo?.customerAvatar || <img src={defaultAvatar} />} />
            <div className="ml-1 flex flex-col items-start gap-1">
              <div className="font-medium">{conversationInfo?.customerNickname}</div>
            </div>
          </div>
        </div>

        {/* 分割线 */}
        <div className="h-[1px] w-full bg-gray-100" />

        {/* 聊天记录区域 */}
        <div
          className="no-scrollbar flex flex-1 flex-col-reverse overflow-y-auto p-4"
          style={{ overflowAnchor: 'none' }}
        >
          {/* 用于自动滚动到底部的参考元素 */}
          <div ref={messagesEndRef} />

          {/* 消息列表 */}
          <div className="flex flex-1 flex-col-reverse gap-4">
            {/* 空白占位元素，用于消息少时将消息撑到底部 */}
            <div className="block flex-shrink flex-grow" />

            {messages.map((message) => (
              <div key={`${message.serverMessageId}-${nanoid()}`} className="group">
                {message.direction === 1 ? (
                  // 客户发送的消息
                  <div className="flex items-end">
                    <Bubble
                      content={renderMessageInChat(message)}
                      placement="start"
                      avatar={
                        <Avatar
                          src={conversationInfo?.customerAvatar || <img src={defaultAvatar} />}
                        />
                      }
                    />
                  </div>
                ) : (
                  // 客服发送的消息
                  <div className="flex flex-col items-end">
                    <div className="mb-1 text-xs text-gray-400">
                      {message.sendSourceType === 1
                        ? '该消息由人工客服回复'
                        : message.sendSourceType === 2
                          ? '该消息由机器人自动回复'
                          : ''}
                    </div>
                    <Bubble
                      content={renderMessageInChat(message)}
                      placement="end"
                      avatar={<Avatar src={<img src={defaultAvatar} />} />}
                    />
                  </div>
                )}
                {/* 修改时间显示的样式 */}
                <div
                  className={cn(
                    'mt-1 text-xs text-gray-400 opacity-0 transition-opacity duration-200 group-hover:opacity-100',
                    message.direction === 1 ? 'text-left' : 'text-right',
                    message.direction === 1 ? 'ml-12' : 'mr-12',
                  )}
                >
                  {message.messageCreateTime
                    ? new Date(message.messageCreateTime).toLocaleString()
                    : ''}
                </div>
              </div>
            ))}
          </div>

          {/* 添加没有更多消息的提示 */}
          {!hasNextPage && messages.length > 0 && (
            <div className="flex justify-center py-2 text-sm text-gray-400">没有更多消息了</div>
          )}

          {/* 加载状态 */}
          {isFetchingNextPage ? (
            <div className="flex justify-center py-2">
              <Spin size="small" />
            </div>
          ) : (
            hasNextPage && <div ref={loadMoreRef} className="h-1 w-full" />
          )}

          {/* 无消息提示 */}
          {!isLoading && messages.length === 0 && (
            <div className="flex h-full items-center justify-center text-gray-400">
              暂无聊天记录
            </div>
          )}
        </div>
      </div>

      {/* 右侧用户信息栏 */}
      <div className="h-full w-[320px] border-l border-gray-100">
        <UserInfo previewConversationId={conversationId} previewProjectId={projectId} previewMode />
      </div>
    </div>
  );
}
