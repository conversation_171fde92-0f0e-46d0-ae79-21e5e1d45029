import DataCard from '@/components/dataCard';
import { GetDataCardTrend } from '@/services/data-card';
import { HideScrollBarRow } from '@/utils/commonStyle';
import { useRequest } from '@umijs/max';
import { Col } from 'antd';

type HumanCustomerTrendProps = {
  rangeTime: (string | undefined)[];
  platform: number;
  projectId?: string;
};

export default function HumanCustomerTrend({
  rangeTime,
  platform,
  projectId,
}: HumanCustomerTrendProps) {
  const requestFn = (type: number) =>
    GetDataCardTrend({
      projectId,
      platform,
      startDate: rangeTime[0],
      endDate: rangeTime[1],
      type,
    });

  const { data: humanConversationsData, loading: humanConversationsLoading } = useRequest(
    () => requestFn(605),
    {
      refreshDeps: [rangeTime, platform],
    },
  );

  const { data: humanReceptionsData, loading: humanReceptionsLoading } = useRequest(
    () => requestFn(606),
    {
      refreshDeps: [rangeTime, platform],
    },
  );

  const { data: humanMessagesData, loading: humanMessagesLoading } = useRequest(
    () => requestFn(607),
    {
      refreshDeps: [rangeTime, platform],
    },
  );

  const { data: humanLeadsData, loading: humanLeadsLoading } = useRequest(() => requestFn(608), {
    refreshDeps: [rangeTime, platform],
  });

  return (
    <HideScrollBarRow gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }} wrap={false} className="mb-5">
      <Col xs={8} sm={8} md={7} lg={7} xl={8} xxl={5} span={5}>
        <DataCard
          title="会话总数"
          data={humanConversationsData}
          loading={humanConversationsLoading}
          className="border"
        />
      </Col>
      <Col xs={8} sm={8} md={7} lg={7} xl={8} xxl={5} span={5}>
        <DataCard
          title="咨询人数"
          data={humanReceptionsData}
          loading={humanReceptionsLoading}
          className="border"
        />
      </Col>
      <Col xs={8} sm={8} md={7} lg={7} xl={8} xxl={5} span={5}>
        <DataCard
          title="消息数"
          data={humanMessagesData}
          loading={humanMessagesLoading}
          className="border"
        />
      </Col>
      <Col xs={8} sm={8} md={7} lg={7} xl={8} xxl={5} span={5}>
        <DataCard
          title="留资数"
          data={humanLeadsData}
          loading={humanLeadsLoading}
          className="border"
        />
      </Col>
    </HideScrollBarRow>
  );
}
