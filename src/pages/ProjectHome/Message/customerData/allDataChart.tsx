import DynamicTree from '@/components/dynamicTree';
import FieldSelect from '@/components/fieldSelect';
import useProjectId from '@/hooks/useProjectId';
import { QueryFilter } from '@ant-design/pro-components';
import { Form } from 'antd';
import { useAtomValue } from 'jotai';
import { selectPlatformAtom } from '../../atom';
import FunnelChart from './funnelChart';
import LineChart from './lineChart';
import { useRequest } from '@umijs/max';
import { CustomerServiceAllData } from '@/services/custom-data';
import { useEffect } from 'react';

type FilterFormType = {
  sourceAccountNickname?: string;
  teamCodeList?: string[];
  tagIds?: string;
  fieldValue?: string;
};

type AllDataChartProps = {
  lastFieldId?: number;
  rangeTime: (string | undefined)[];
  teamCodeList?: string[];
};

export default function AllDataChart({ lastFieldId, rangeTime, teamCodeList }: AllDataChartProps) {
  const projectId = useProjectId();
  const platform = useAtomValue(selectPlatformAtom);
  const [form] = Form.useForm<FilterFormType>();

  const { data: chartData, run: fetchData } = useRequest(
    (params: FilterFormType) => {
      const [startDate, endDate] = rangeTime;
      if (!startDate || !endDate) return Promise.reject();
      return CustomerServiceAllData({
        projectId,
        platform,
        startDate,
        endDate,
        sourceAccountNickname: params?.sourceAccountNickname,
        teamCodeList: params?.teamCodeList,
        fieldList: params?.fieldValue
          ? [
              {
                fieldId: lastFieldId,
                value: params.fieldValue,
              },
            ]
          : undefined,
      });
    },
    {
      manual: false,
      ready: !!projectId && !!platform && !!rangeTime[0] && !!rangeTime[1],
      refreshDeps: [projectId, platform, rangeTime],
    },
  );

  const handleFinish = async (values: FilterFormType) => {
    await fetchData(values);
  };

  useEffect(() => {
    if (teamCodeList && teamCodeList?.length > 0) {
      form.setFieldsValue({
        teamCodeList: teamCodeList,
      });
      form.submit();
    }
  }, [teamCodeList]);

  return (
    <div className="mt-3 flex w-full flex-col rounded-lg bg-white pb-4">
      <QueryFilter<FilterFormType>
        form={form}
        onFinish={handleFinish}
        style={{ marginBottom: 0 }}
        labelWidth="auto"
        span={{
          xs: 24,
          sm: 12,
          md: 8,
          lg: 8,
          xl: 8,
          xxl: 4,
        }}
        searchGutter={10}
        defaultCollapsed={true}
        submitter={{
          searchConfig: {
            resetText: '重置',
            submitText: '查询',
          },
        }}
      >
        <Form.Item name="teamCodeList" label={null}>
          <DynamicTree />
        </Form.Item>
        {lastFieldId && (
          <Form.Item name="fieldValue" label={null}>
            <FieldSelect fieldId={lastFieldId} name="经销商编码" />
          </Form.Item>
        )}
      </QueryFilter>
      <div className="flex h-[328px] w-full overflow-hidden px-[24px] py-[16px]">
        <div className="w-[320px]">
          <div className="mb-4 text-base font-medium">会话漏斗</div>
          <FunnelChart data={chartData || []} />
        </div>
        <div className="mr-6 h-full w-[1px] bg-new-media-gray-200" />
        <div className="min-w-0 flex-1">
          <div className="mb-4 text-base font-medium">会话数据趋势</div>
          <LineChart data={chartData || []} rangeTime={rangeTime} />
        </div>
      </div>
    </div>
  );
}
