import ReactECharts from 'echarts-for-react';
import { CustomerServiceAllDataVO } from '@/services/custom-data';
import { Select } from 'antd';
import { useState } from 'react';
import dayjs from 'dayjs';
import { echartAreaStyleGradient } from '@/utils/commonStyle';

type LineChartProps = {
  data: CustomerServiceAllDataVO[];
  rangeTime?: (string | undefined)[];
};

type DataType = '会话总数' | '咨询客户数' | '留资数';

export default function LineChart({ data, rangeTime }: LineChartProps) {
  const [selectedType, setSelectedType] = useState<DataType>('会话总数');

  const getSeriesData = (type: DataType) => {
    switch (type) {
      case '会话总数':
        return {
          data: data.map((item) => item.conversationCount),
          color: '#1677FF',
        };
      case '咨询客户数':
        return {
          data: data.map((item) => item.consultCustomerCount),
          color: '#1677FF',
        };
      case '留资数':
        return {
          data: data.map((item) => item.leadsCount),
          color: '#1677FF',
        };
    }
  };

  const currentSeries = getSeriesData(selectedType);

  const formatXAxisData = (dateTime: string | undefined) => {
    if (!dateTime) return '';

    // 如果 rangeTime 存在且开始和结束时间是同一天，显示小时格式
    if (rangeTime?.[0] && rangeTime?.[1] && dayjs(rangeTime[0]).isSame(rangeTime[1], 'day')) {
      return dayjs(dateTime).format('HH:00');
    }

    return dayjs(dateTime).format('MM.DD');
  };

  const option = {
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(255, 255, 255, 0.9)',
      borderWidth: 0,
      textStyle: {
        color: '#333',
      },
    },
    grid: {
      left: '1%',
      right: '1%',
      bottom: '10%',
      top: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: data.map((item) => formatXAxisData(item.dateTime)),
      axisLine: {
        lineStyle: {
          color: '#E5E7EB',
        },
      },
      axisLabel: {
        color: '#6B7280',
      },
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        lineStyle: {
          color: '#E5E7EB',
          type: 'dashed',
        },
      },
      axisLabel: {
        color: '#6B7280',
      },
    },
    series: [
      {
        name: selectedType,
        type: 'line',
        smooth: true,
        data: currentSeries.data,
        symbol: 'circle',
        symbolSize: 6,
        itemStyle: {
          color: currentSeries.color,
        },
        lineStyle: {
          width: 2,
        },
        areaStyle: echartAreaStyleGradient,
      },
    ],
  };

  return (
    <div className="relative h-full w-full">
      <div className="absolute -top-[40px] right-4 z-10">
        <Select
          value={selectedType}
          onChange={(value) => setSelectedType(value)}
          options={[
            { label: '会话总数', value: '会话总数' },
            { label: '咨询客户数', value: '咨询客户数' },
            { label: '留资数', value: '留资数' },
          ]}
          className="w-32"
        />
      </div>
      <ReactECharts option={option} style={{ height: '100%', width: '100%' }} />
    </div>
  );
}
