import useProjectId from '@/hooks/useProjectId';
import { ProCard } from '@ant-design/pro-components';
import { useAtomValue } from 'jotai';
import { selectPlatformAtom } from '../../atom';
import HumanCustomerTable from './humanCustomerTable';
import HumanCustomerTrend from './humanCustomerTrend';

type HumanCustomerDataProps = {
  rangeTime: (string | undefined)[];
  lastFieldId?: number;
  teamCodeList?: string[];
};

export default function HumanCustomerData({
  rangeTime,
  lastFieldId,
  teamCodeList,
}: HumanCustomerDataProps) {
  const projectId = useProjectId();
  const platform = useAtomValue(selectPlatformAtom);

  return (
    <ProCard style={{ marginTop: 16 }} title="人工客服数据">
      <HumanCustomerTrend rangeTime={rangeTime} platform={platform} projectId={projectId} />

      <HumanCustomerTable
        rangeTime={rangeTime}
        platform={platform}
        projectId={projectId}
        lastFieldId={lastFieldId}
        teamCodeList={teamCodeList}
      />
    </ProCard>
  );
}
