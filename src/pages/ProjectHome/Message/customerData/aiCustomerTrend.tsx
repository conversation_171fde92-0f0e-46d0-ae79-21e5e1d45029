import DataCard from '@/components/dataCard';
import { GetDataCardTrend } from '@/services/data-card';
import { HideScrollBarRow } from '@/utils/commonStyle';
import { useRequest } from '@umijs/max';
import { Col } from 'antd';

type AICustomerTrendProps = {
  rangeTime: (string | undefined)[];
  platform: number;
  projectId?: string;
};

export default function AICustomerTrend({ rangeTime, platform, projectId }: AICustomerTrendProps) {
  const requestFn = (type: number) =>
    GetDataCardTrend({
      projectId,
      platform,
      startDate: rangeTime[0],
      endDate: rangeTime[1],
      type,
    });

  const { data: robotConversationsData, loading: robotConversationsLoading } = useRequest(
    () => requestFn(601),
    {
      refreshDeps: [rangeTime, platform],
    },
  );

  const { data: robotReceptionsData, loading: robotReceptionsLoading } = useRequest(
    () => requestFn(602),
    {
      refreshDeps: [rangeTime, platform],
    },
  );

  const { data: robotRepliesData, loading: robotRepliesLoading } = useRequest(
    () => requestFn(603),
    {
      refreshDeps: [rangeTime, platform],
    },
  );

  const { data: robotLeadsData, loading: robotLeadsLoading } = useRequest(() => requestFn(604), {
    refreshDeps: [rangeTime, platform],
  });

  return (
    <HideScrollBarRow gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }} wrap={false} className="mb-5">
      <Col xs={8} sm={8} md={7} lg={7} xl={8} xxl={5} span={5}>
        <DataCard
          title="机器人会话量"
          data={robotConversationsData}
          loading={robotConversationsLoading}
          className="border"
        />
      </Col>
      <Col xs={8} sm={8} md={7} lg={7} xl={8} xxl={5} span={5}>
        <DataCard
          title="机器人回复消息数"
          data={robotReceptionsData}
          loading={robotReceptionsLoading}
          className="border"
        />
      </Col>
      <Col xs={8} sm={8} md={7} lg={7} xl={8} xxl={5} span={5}>
        <DataCard
          title="机器人参与留资数"
          data={robotRepliesData}
          loading={robotRepliesLoading}
          className="border"
        />
      </Col>
      <Col xs={8} sm={8} md={7} lg={7} xl={8} xxl={5} span={5}>
        <DataCard
          title="机器人参与留资率"
          data={robotLeadsData}
          loading={robotLeadsLoading}
          isPercentage
          className="border"
        />
      </Col>
    </HideScrollBarRow>
  );
}
