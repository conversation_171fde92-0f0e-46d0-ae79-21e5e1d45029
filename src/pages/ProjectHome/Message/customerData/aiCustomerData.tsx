import useProjectId from '@/hooks/useProjectId';
import { ProCard } from '@ant-design/pro-components';
import { useAtomValue } from 'jotai';
import { selectPlatformAtom } from '../../atom';
import AICustomerTable from './aiCustomerTable';
import AICustomerTrend from './aiCustomerTrend';

type AICustomerDataProps = {
  rangeTime: (string | undefined)[];
  lastFieldId?: number;
  teamCodeList?: string[];
};

export default function AICustomerData({
  rangeTime,
  lastFieldId,
  teamCodeList,
}: AICustomerDataProps) {
  const projectId = useProjectId();
  const platform = useAtomValue(selectPlatformAtom);

  return (
    <ProCard style={{ marginTop: 16 }} title="智能客服数据">
      <AICustomerTrend rangeTime={rangeTime} platform={platform} projectId={projectId} />

      <AICustomerTable
        rangeTime={rangeTime}
        platform={platform}
        projectId={projectId}
        lastFieldId={lastFieldId}
        teamCodeList={teamCodeList}
      />
    </ProCard>
  );
}
