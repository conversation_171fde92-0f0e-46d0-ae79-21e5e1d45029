import DynamicTree from '@/components/dynamicTree';
import ExportButton from '@/components/exportButton';
import FieldSelect from '@/components/fieldSelect';
import { usePollingExport } from '@/hooks/usePollingExport';
import {
  AICustomData,
  AICustomDataParams,
  AICustomResponseData,
  ExportAICustomData,
} from '@/services/custom-data';
import { GetTeamDepthSelect } from '@/services/team';
import {
  proTableOptionsConfig,
  proTablePaginationConfig,
  proTableSearchConfig,
} from '@/utils/proTableConfig';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { FormInstance, message } from 'antd';
import { useEffect, useRef, useState } from 'react';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { omit } from 'lodash-es';

type AICustomerTableProps = {
  rangeTime: (string | undefined)[];
  platform: number;
  projectId?: string;
  lastFieldId?: number;
  teamCodeList?: string[];
};

export default function AICustomerTable({
  rangeTime,
  platform,
  projectId,
  lastFieldId,
  teamCodeList,
}: AICustomerTableProps) {
  const actionRef = useRef<ActionType>();
  const isLocalSortRef = useRef(false);
  const [fieldId, setFieldId] = useState<number>();
  const [selectedDimensionName, setSelectedDimensionName] = useState<string>('');
  const { pollingExport, percent, pollingLoading } = usePollingExport();
  const paramsRef = useRef<any>({});
  const formRef = useRef<FormInstance>();

  const { data: teamDepthData } = useRequest(() => GetTeamDepthSelect({ projectId }), {
    onSuccess: (data) => {
      if (data?.[0]) {
        const item = data[0];
        setFieldId(Number(item.value));
        setSelectedDimensionName(item.name || '');
      }
    },
  });

  useEffect(() => {
    if (teamCodeList && teamCodeList?.length > 0) {
      formRef.current?.setFieldsValue({
        teamCodeList: teamCodeList,
      });
      formRef.current?.submit();
    }
  }, [teamCodeList]);

  const columns: ProColumns<AICustomResponseData>[] = [
    {
      title: '动态维度筛选',
      dataIndex: 'teamCodeList',
      hideInTable: true,
      formItemProps: {
        label: null,
      },
      renderFormItem: () => {
        return <DynamicTree actionRef={actionRef} />;
      },
    },
    {
      title: '经销商编码',
      dataIndex: lastFieldId,
      hideInTable: true,
      formItemProps: {
        label: null,
      },
      search: {
        transform: (value) => {
          return {
            fieldList: [
              {
                fieldId: lastFieldId,
                value,
              },
            ],
          };
        },
      },
      renderFormItem: () => {
        if (!lastFieldId) {
          return null;
        }
        return <FieldSelect fieldId={lastFieldId} name="经销商编码" />;
      },
    },
    {
      title: selectedDimensionName || '聚合维度',
      dataIndex: 'aggregationName',
      width: 200,
      fixed: 'left',
      search: false,
    },
    {
      title: '机器人会话量',
      dataIndex: 'conversationCount',
      sorter: (a, b) => (Number(a.conversationCount) || 0) - (Number(b.conversationCount) || 0),
      width: 150,
      search: false,
      align: 'right',
      tooltip: '选择日期内，由用户发起会话且智能客服接待时间段接待的会话数量',
    },
    {
      title: '机器人回复消息数',
      dataIndex: 'replyCount',
      sorter: (a, b) => (Number(a.replyCount) || 0) - (Number(b.replyCount) || 0),
      width: 150,
      search: false,
      align: 'right',
      tooltip:
        '选择日期内，智能客服接待时间段中，由智能客服发送的消息数（不包含人工客服中的智能回复）',
    },
    {
      title: '机器人参与留资数',
      dataIndex: 'leadsParticipateCount',
      sorter: (a, b) =>
        (Number(a.leadsParticipateCount) || 0) - (Number(b.leadsParticipateCount) || 0),
      width: 150,
      search: false,
      align: 'right',
      tooltip: '选择日期内，智能客服接待时间段中，用户留下的销售线索数',
    },
    {
      title: '机器人参与留资率',
      dataIndex: 'leadsParticipateRate',
      sorter: (a, b) => {
        const aValue = parseFloat(a.leadsParticipateRate?.replace('%', '') || '0');
        const bValue = parseFloat(b.leadsParticipateRate?.replace('%', '') || '0');
        return aValue - bValue;
      },
      width: 150,
      search: false,
      align: 'right',
      tooltip: '机器人参与留资数据/机器人会话量',
      renderText: (text) => {
        return text + '%';
      },
    },
  ];

  const handleExportAccount = async () => {
    const omitPageParams = omit(paramsRef.current, ['pageSize', 'current']) as AICustomDataParams;
    const res = await ExportAICustomData(omitPageParams);
    if (res.data) {
      pollingExport({ taskId: res.data });
    } else {
      message.error('导出失败');
    }
  };

  return (
    <ProTable<AICustomResponseData, AICustomDataParams>
      ghost
      params={{
        projectId,
        startDate: rangeTime[0] || '',
        endDate: rangeTime[1] || '',
        platform,
        fieldId,
      }}
      formRef={formRef}
      actionRef={actionRef}
      columns={columns}
      rowKey="aggregationName"
      tableClassName="custom-table"
      search={{ ...proTableSearchConfig }}
      options={{ ...proTableOptionsConfig }}
      pagination={{ ...proTablePaginationConfig }}
      scroll={{ x: 'max-content' }}
      onChange={(_, __, sorter, extra) => {
        if (extra.action === 'sort') {
          isLocalSortRef.current = true;
        }
      }}
      request={async (params) => {
        paramsRef.current = params;
        if (isLocalSortRef.current) {
          return Promise.reject();
        }
        if (!fieldId) {
          return Promise.reject();
        }
        const { data } = await AICustomData(params);
        if (!data) {
          return {
            data: [],
            success: true,
            total: 0,
          };
        }
        // 处理分页
        const current = params.current || 1;
        const pageSize = params.pageSize || 10;
        const start = (current - 1) * pageSize;
        const end = start + pageSize;
        const pageData = data.slice(start, end);

        return {
          data: pageData,
          success: true,
          total: data.length,
        };
      }}
      headerTitle={
        teamDepthData && (
          <Tabs value={fieldId?.toString()} onValueChange={(value) => setFieldId(Number(value))}>
            <TabsList value={fieldId?.toString()}>
              {teamDepthData.map((item) => (
                <TabsTrigger
                  key={item.value}
                  value={item.value}
                  onClick={() => {
                    setFieldId(Number(item.value));
                    setSelectedDimensionName(item.name || '');
                  }}
                >
                  {item.name}
                </TabsTrigger>
              ))}
            </TabsList>
          </Tabs>
        )
      }
      toolBarRender={() => [
        <ExportButton
          exportFn={() => handleExportAccount()}
          loading={pollingLoading}
          percent={percent}
          key="export"
        />,
      ]}
      tableAlertRender={false}
      columnsState={{
        persistenceKey: 'AICustomerTable-Table-Columns',
        persistenceType: 'localStorage',
      }}
    />
  );
}
