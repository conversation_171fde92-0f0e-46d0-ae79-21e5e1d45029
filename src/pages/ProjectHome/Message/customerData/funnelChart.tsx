import { cn } from '@/lib/utils';
import ApertureRatio from '@/assets/aperture-ratio.png';
import ConversionRate from '@/assets/conversion-rate.png';
import { sumBy } from 'lodash-es';
import { CustomerServiceAllDataVO } from '@/services/custom-data';
import { formatNum } from '@/utils/common';
import { InfoCircleOutlined } from '@ant-design/icons';
import { Tooltip } from 'antd';

type FunnelItem = {
  label: string;
  value: number | string;
  tooltip: string;
};

type FunnelChartProps = {
  data: CustomerServiceAllDataVO[];
};

export default function FunnelChart({ data }: FunnelChartProps) {
  const totalConversation = sumBy(data, (item) => Number(item.conversationCount) || 0);
  const totalConsultUsers = sumBy(data, (item) => Number(item.consultCustomerCount) || 0);
  const totalLeads = sumBy(data, (item) => Number(item.leadsCount) || 0);

  const funnelData: FunnelItem[] = [
    {
      label: '会话总数',
      value: formatNum(totalConversation),
      tooltip: '客服（智能客服+人工客服）接待的会话总数，对应的具体会话可在历史会话页面查看。',
    },
    {
      label: '咨询用户数',
      value: formatNum(totalConsultUsers),
      tooltip: '进入到会话页的访客人数，并按用户的抖音号（openID）等ID进行去重得到的数字',
    },
    {
      label: '留资数',
      value: formatNum(totalLeads),
      tooltip: '用户留下的销售线索数',
    },
  ];

  return (
    <div className="w-[330px] pr-16">
      <div className="relative">
        {funnelData.map((item, index) => (
          <div
            key={item.label}
            className={cn(
              'relative mb-1 flex h-16 items-center justify-between',
              'my-2 px-6 text-white',
              index === 0 && 'w-full bg-new-media-blue-900',
              index === 1 && 'w-[90%] bg-new-media-blue-600',
              index === 2 && 'w-[80%] bg-new-media-blue-300',
              'ml-auto',
            )}
            style={{
              clipPath: 'polygon(0 0, 100% 0, 100% 100%, 8% 100%)',
            }}
          >
            <div className="space-x-1">
              <span className="ml-2 font-bold">{item.label}</span>
              <Tooltip title={item.tooltip}>
                <InfoCircleOutlined />
              </Tooltip>
            </div>
            <span className="text-2xl font-medium">{item.value}</span>
          </div>
        ))}

        <img
          src={ApertureRatio}
          alt="开口率"
          className="absolute right-[-30px] top-[20px] h-[70px]"
        />

        <img
          src={ConversionRate}
          alt="转化率"
          className="absolute bottom-[20px] right-[-30px] h-[70px]"
        />
      </div>
    </div>
  );
}
