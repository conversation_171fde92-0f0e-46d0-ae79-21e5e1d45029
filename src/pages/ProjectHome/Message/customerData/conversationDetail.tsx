import defaultAvatar from '@/assets/default-avatar.png';
import ConversationAccountSelect from '@/components/conversationAccountSelect';
import CustomerSelect from '@/components/customerSelect';
import DynamicTree from '@/components/dynamicTree';
import ExportButton from '@/components/exportButton';
import FieldSelect from '@/components/fieldSelect';
import { usePollingExport } from '@/hooks/usePollingExport';
import useProjectId from '@/hooks/useProjectId';
import {
  ConversationDetailBody,
  ConversationDetailData,
  ConversationDetailTableData,
  ExportConversationDetail,
} from '@/services/custom-data';
import { proTableRequestAdapterParamsAndData } from '@/utils';
import {
  proTableOptionsConfig,
  proTablePaginationConfig,
  proTableSearchConfig,
} from '@/utils/proTableConfig';
import { ActionType, ProCard, ProColumns, ProTable } from '@ant-design/pro-components';
import { Avatar, Button, Drawer, FormInstance, message } from 'antd';
import { omit } from 'lodash-es';
import { useEffect, useRef, useState } from 'react';
import ChatPreview from './chatPreview';

type ConversationDetailProps = {
  rangeTime: (string | undefined)[];
  lastFieldId?: number;
  teamCodeList?: string[];
};

export default function ConversationDetail({
  rangeTime,
  lastFieldId,
  teamCodeList,
}: ConversationDetailProps) {
  const projectId = useProjectId();
  const actionRef = useRef<ActionType>();
  const { pollingExport, percent, pollingLoading } = usePollingExport();
  const paramsRef = useRef<any>({});
  const [open, setOpen] = useState(false);
  const [conversationInfo, setConversationInfo] = useState<
    ConversationDetailTableData | undefined
  >();
  const formRef = useRef<FormInstance>();

  const showDrawer = (record: ConversationDetailTableData) => {
    setOpen(true);
    setConversationInfo(record);
  };

  const onClose = () => {
    setOpen(false);
  };

  useEffect(() => {
    if (teamCodeList && teamCodeList?.length > 0) {
      formRef.current?.setFieldsValue({
        teamCodeList: teamCodeList,
      });
      formRef.current?.submit();
    }
  }, [teamCodeList]);

  const columns: ProColumns<ConversationDetailTableData>[] = [
    {
      title: '动态维度筛选',
      dataIndex: 'teamCodeList',
      hideInTable: true,
      formItemProps: {
        label: null,
      },
      renderFormItem: () => {
        return <DynamicTree actionRef={actionRef} />;
      },
    },
    {
      title: '来源账号',
      dataIndex: 'sourceAccountIdList',
      fieldProps: {
        placeholder: '来源账号',
      },
      formItemProps: {
        label: null,
      },
      width: 150,
      renderFormItem: () => {
        return <ConversationAccountSelect />;
      },
      render: (_, record) => {
        return (
          <div className="flex flex-col gap-1">
            <span className="text-sm">{record.sourceAccountDouyinNickname}</span>
            <span className="text-xs text-gray-500">{record.sourceAccountDouyinShowAccountId}</span>
          </div>
        );
      },
    },
    {
      title: '会话 ID',
      dataIndex: 'conversationId',
      hideInSearch: true,
      width: 400,
      render: (_, record) => {
        return <span className="line-clamp-2 max-w-[400px]">{record.conversationId}</span>;
      },
    },
    {
      title: '用户昵称',
      dataIndex: 'customerNickname',
      width: 100,
      fieldProps: {
        placeholder: '用户昵称',
      },
      formItemProps: {
        label: null,
      },
      render: (_, record) => {
        return (
          <div className="flex flex-col gap-1">
            <div className="flex items-center gap-1">
              <Avatar
                size="small"
                src={record.customerAvatar || <img src={defaultAvatar} />}
                alt="avatar"
                className="h-6 w-6 rounded-full object-cover"
              />
              <span className="text-sm">{record.customerNickname}</span>
            </div>
            <span className="text-xs text-gray-500">openID: {record.customerOpenId}</span>
          </div>
        );
      },
    },
    {
      title: '客服昵称',
      dataIndex: 'serviceNickname',
      width: 100,
      fieldProps: {
        placeholder: '客服昵称',
      },
      formItemProps: {
        label: null,
      },
      search: {
        transform: (value) => {
          return {
            serviceSysUserIdList: value,
          };
        },
      },
      renderFormItem: () => {
        return <CustomerSelect />;
      },
    },
    {
      title: '是否留资',
      dataIndex: 'leadsFlag',
      width: 100,
      fieldProps: {
        placeholder: '是否留资',
      },
      formItemProps: {
        label: null,
      },
      valueEnum: {
        0: { text: '否' },
        1: { text: '是' },
      },
    },
    {
      title: '消息来源',
      dataIndex: 'imEnterUserType',
      hideInTable: true,
      fieldProps: {
        placeholder: '消息来源',
      },
      formItemProps: {
        label: null,
      },
      valueEnum: {
        1: { text: '自然用户' },
        2: { text: '投流用户' },
      },
    },

    {
      title: '经销商编码',
      dataIndex: lastFieldId,
      hideInTable: true,
      formItemProps: {
        label: null,
      },
      search: {
        transform: (value) => {
          return {
            fieldList: [
              {
                fieldId: lastFieldId,
                value,
              },
            ],
          };
        },
      },
      renderFormItem: () => {
        if (!lastFieldId) {
          return null;
        }
        return <FieldSelect fieldId={lastFieldId} name="经销商编码" />;
      },
    },
    {
      title: '智能助手接待',
      dataIndex: 'robotFlag',
      width: 50,
      search: false,
      valueEnum: {
        0: { text: '否' },
        1: { text: '是' },
      },
    },
    {
      title: '操作',
      dataIndex: 'operation',
      width: 130,
      search: false,
      render: (_, record) => (
        <Button type="link" className="p-0" onClick={() => showDrawer(record)}>
          查看会话
        </Button>
      ),
    },
  ];

  const handleExport = async () => {
    const omitPageParams = omit(paramsRef.current, [
      'pageSize',
      'current',
    ]) as ConversationDetailBody;
    const res = await ExportConversationDetail(omitPageParams);
    if (res.data) {
      pollingExport({ taskId: res.data });
    } else {
      message.error('导出失败');
    }
  };

  return (
    <>
      <ProCard title="会话明细记录" style={{ marginTop: 16 }}>
        <ProTable<ConversationDetailTableData, ConversationDetailBody>
          ghost
          formRef={formRef}
          params={{
            projectId,
            startDate: rangeTime[0] || '',
            endDate: rangeTime[1] || '',
          }}
          actionRef={actionRef}
          columns={columns}
          rowKey="conversationId"
          tableClassName="custom-table"
          search={{ ...proTableSearchConfig }}
          options={{ ...proTableOptionsConfig }}
          pagination={{ ...proTablePaginationConfig }}
          scroll={{ x: 'max-content' }}
          request={(params, sorter) => {
            paramsRef.current = params;
            return proTableRequestAdapterParamsAndData(params, sorter, ConversationDetailData);
          }}
          toolBarRender={() => [
            <ExportButton
              exportFn={() => handleExport()}
              loading={pollingLoading}
              percent={percent}
              key="export"
            />,
          ]}
          tableAlertRender={false}
          columnsState={{
            persistenceKey: 'ConversationDetail-Table-Columns',
            persistenceType: 'localStorage',
          }}
        />
      </ProCard>
      <Drawer
        title={`「${conversationInfo?.customerNickname}」的会话信息`}
        onClose={onClose}
        open={open}
        width="40%"
      >
        <ChatPreview conversationInfo={conversationInfo} projectId={projectId} />
      </Drawer>
    </>
  );
}
