import { Tabs, TabsProps } from 'antd';
import { useState } from 'react';
import CustomerSetting from '../customerSetting';
import MessageSetting from '../messageSetting';

export default function HumanCustomer() {
  const [activeSubKey, onSubTabChange] = useState('human-customer-setting');

  const aiCustomerItems: TabsProps['items'] = [
    {
      label: '人工客服设置',
      key: 'human-customer-setting',
      children: <CustomerSetting isActiveKey={activeSubKey === 'human-customer-setting'} />,
    },
    {
      label: '欢迎语设置',
      key: 'welcome-message-setting',
      children: <MessageSetting />,
    },
  ];

  return <Tabs activeKey={activeSubKey} onChange={onSubTabChange} items={aiCustomerItems} />;
}
