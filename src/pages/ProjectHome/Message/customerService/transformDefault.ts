/**
 * {{purchaseTime}}  用户所选购车时间 - 获取购车时间
 * {{carSeries}} 用户所选车系 - 获取意向车系
 * {{intentionCity}} 用户所选城市 - 获取意向城市
 */
export const transformDefaultContent = (text: string) => {
  const templateMap = {
    '{{purchaseTime}}': '[用户所选购车时间]',
    '{{carSeries}}': '[用户所选车系]',
    '{{intentionCity}}': '[用户所选城市]',
  };

  return Object.entries(templateMap).reduce((result, [template, value]) => {
    return result.replace(template, value);
  }, text);
};

export const transformBackToTemplate = (text: string) => {
  const reverseTemplateMap = {
    '[用户所选购车时间]': '{{purchaseTime}}',
    '[用户所选车系]': '{{carSeries}}',
    '[用户所选城市]': '{{intentionCity}}',
  };

  return Object.entries(reverseTemplateMap).reduce((result, [value, template]) => {
    return result.replace(value, template);
  }, text);
};
