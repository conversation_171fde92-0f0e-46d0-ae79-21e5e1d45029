import { RobotConfig } from '@/services/message-robot';
import { Tabs, TabsProps } from 'antd';
import { useState } from 'react';
import SmartConversation from '../smartConversation';
import SmartRetention from '../smartRetention';

export type AiCustomerProps = {
  robotConfig?: RobotConfig;
  refreshConfig: () => Promise<RobotConfig | undefined>;
  switchStatus: boolean;
};

export default function AiCustomer({ robotConfig, refreshConfig, switchStatus }: AiCustomerProps) {
  const [activeSubKey, onSubTabChange] = useState('smart-conversation');

  const aiCustomerItems: TabsProps['items'] = [
    {
      label: '智能会话',
      key: 'smart-conversation',
      children: <SmartConversation robotConfig={robotConfig} refreshConfig={refreshConfig} />,
    },
    {
      label: '智能挽留',
      key: 'smart-retention',
      children: (
        <SmartRetention
          robotConfig={robotConfig}
          refreshConfig={refreshConfig}
          activeSubKey={activeSubKey}
          masterSwitchStatus={switchStatus}
        />
      ),
    },
  ];

  return <Tabs activeKey={activeSubKey} onChange={onSubTabChange} items={aiCustomerItems} />;
}
