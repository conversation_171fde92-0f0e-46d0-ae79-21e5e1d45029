import { cn } from '@/lib/utils';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import { Button, Input, message, Switch } from 'antd';
import { useState } from 'react';
import KnowledgeTableModal from '../smartConversation/knowledgeTableModal';
import useProjectId from '@/hooks/useProjectId';
import { useRequest } from '@umijs/max';
import {
  ActiveGuideQuery,
  ActiveGuideSwitch,
  ActiveGuideUpdate,
} from '@/services/douyin-im/manual-config';
import { KnowledgeItem } from '@/services/message-robot';

const columns: ProColumns<KnowledgeItem>[] = [
  {
    dataIndex: 'sort',
    hideInTable: true,
  },
  {
    title: '序号',
    width: 80,
    render: (_, record, index) => index + 1,
  },
  {
    title: '用户问题',
    dataIndex: 'question',
    width: 200,
  },
  {
    title: '回复答案',
    dataIndex: 'answer',
    width: 200,
  },
];

export default function MessageSetting() {
  const projectId = useProjectId();
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [inputValue, setInputValue] = useState('');

  const { data: config, refresh: refreshConfig } = useRequest(
    () => ActiveGuideQuery({ projectId }),
    {
      onSuccess: (data) => {
        setInputValue(data?.guideTitle || '');
      },
    },
  );

  const handleEditKnowledge = async (values: any) => {
    const res = await ActiveGuideUpdate({
      projectId,
      activeGuideConfig: {
        guideTitle: config?.guideTitle || '',
        questionList: values,
      },
    });
    if (res.code === 0) {
      setEditModalOpen(false);
      message.success('提交成功');
      await refreshConfig();
    } else {
      message.error('提交失败');
    }
  };

  const handleUpdateGuideTitle = async () => {
    const res = await ActiveGuideUpdate({
      projectId,
      activeGuideConfig: {
        questionList: config?.questionList || [],
        guideTitle: inputValue,
      },
    });
    if (res.code === 0) {
      message.success('更新成功');
      await refreshConfig();
    } else {
      message.error('更新失败');
    }
  };

  const handleSwitchChange = async (checked: boolean) => {
    await ActiveGuideSwitch({
      projectId,
      status: checked ? 1 : 0,
    });
    await refreshConfig();
  };

  return (
    <>
      <div className="w-full rounded-md bg-new-media-blue-100 p-4">
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            <span className="text-base font-medium">用户进入对话时</span>
            <Switch value={Boolean(config?.activeGuideStatus)} onChange={handleSwitchChange} />
          </div>
        </div>
        <div className="mt-4 text-muted-foreground">
          开启后，用户进入对话马上向用户发送欢迎语与附加内容，提升咨询效率与消费者好感，最多1天1次。仅在人工客服中生效，智能客服不生效
        </div>
        <div className="mt-4">
          <div className="flex items-center gap-2">
            <span className="text-nowrap">引导文案</span>
            <Input
              defaultValue={config?.guideTitle}
              className="flex-1"
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              maxLength={50}
              allowClear
              showCount
            />
            <Button type="primary" onClick={handleUpdateGuideTitle}>
              更新
            </Button>
            <Button>取消</Button>
          </div>
        </div>
      </div>

      <ProTable<KnowledgeItem>
        columns={columns}
        dataSource={config?.questionList}
        rowKey={(record) => `${record.sort}-${record.question}-${record.answer}`}
        search={false}
        options={false}
        pagination={false}
        ghost
        tableClassName={cn('custom-table')}
        headerTitle={
          <div className="flex items-center gap-3">
            <div className="text-sm">知识列表 ({config?.questionList?.length || 0}/6)</div>
            <div className="text-sm font-normal text-muted-foreground">知识列表上限为6条</div>
          </div>
        }
        toolBarRender={() => [
          <Button
            key="edit"
            onClick={() => {
              setEditModalOpen(true);
            }}
          >
            编辑知识
          </Button>,
        ]}
      />

      <KnowledgeTableModal
        open={editModalOpen}
        onCancel={() => setEditModalOpen(false)}
        onOk={handleEditKnowledge}
        initialValues={config?.questionList}
        guideTitle={config?.guideTitle}
        hideLeadsAnswer
      />
    </>
  );
}
