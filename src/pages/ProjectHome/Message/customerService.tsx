import DynamicTree from '@/components/dynamicTree';
import BreadCrumbSimple from '@/components/ui/breadcrumbSimple';
import useTabKeySearchParams from '@/hooks/useTabKeySearchParams';
import { PageContainer } from '@ant-design/pro-components';
import { message, Modal, Select, Space, Switch, Tabs, TabsProps, Tooltip } from 'antd';
import React, { useEffect, useState } from 'react';
import TimePeriodSelector, { formatSelectedTimesToString } from './component/timePeriodSelector';
import { InfoCircleFilled, QuestionCircleOutlined } from '@ant-design/icons';
import RobotEnable from '@/assets/robot-enable.png';
import RobotDisable from '@/assets/robot-disable.png';
import HumanCustomerAvatar from '@/assets/customer.png';
import {
  RobotTimeConfig,
  GetDouyinPrivateMessageRobotConfig,
  GetEnableTeam,
  PostSwitchStatus,
  PostEffectiveTime,
  PostEnableTeamUpdate,
} from '@/services/message-robot';
import { Link, useAccess, useRequest } from '@umijs/max';
import useProjectId from '@/hooks/useProjectId';
import AiCustomer from './customerService/aiCustomer';
import { transformDefaultContent } from './customerService/transformDefault';
import { withAuth } from '@/hoc/withAuth';
import { FunctionCode } from '@/services/system';
import { cn } from '@/lib/utils';
import HumanCustomer from './customerService/humanCustomer';

const CustomerService = () => {
  const access = useAccess();
  const canWriteProject = access.canWriteProject;
  const projectId = useProjectId();
  const [activeKey, onTabChange] = useTabKeySearchParams('ai-customer');
  const [isTimeModalOpen, setIsTimeModalOpen] = useState(false);
  const [selectedTime, setSelectedTime] = useState<RobotTimeConfig>();
  const [selectedTimeText, setSelectedTimeText] = useState('');
  const [modal, contextHolder] = Modal.useModal();

  const items: TabsProps['items'] = [
    {
      label: '智能客服模式',
      key: 'ai-customer',
    },
    {
      label: '人工客服模式',
      key: 'human-customer',
    },
  ];

  // 智能客服模式一整个页面的全部 Config
  const { data: robotConfig, refresh: refreshConfig } = useRequest(
    () => GetDouyinPrivateMessageRobotConfig({ projectId }),
    {
      onSuccess: (res) => {
        if (res?.leadsCustomerConfig) {
          // 转换 intentionCarSeries、intentionCity 和 purchaseTime 的 responseContent
          if (res.leadsCustomerConfig.intentionCarSeries) {
            res.leadsCustomerConfig.intentionCarSeries.responseContent = transformDefaultContent(
              res.leadsCustomerConfig.intentionCarSeries.responseContent,
            );
          }
          if (res.leadsCustomerConfig.intentionCity) {
            res.leadsCustomerConfig.intentionCity.responseContent = transformDefaultContent(
              res.leadsCustomerConfig.intentionCity.responseContent,
            );
          }
          if (res.leadsCustomerConfig.purchaseTime) {
            res.leadsCustomerConfig.purchaseTime.responseContent = transformDefaultContent(
              res.leadsCustomerConfig.purchaseTime.responseContent,
            );
          }
        }
      },
    },
  );

  const timePeriodSelectorValue = robotConfig?.effectiveTimeConfig;

  // 可用门店
  const { data: treeValue, refresh: refreshTreeValue } = useRequest(() =>
    GetEnableTeam({ projectId }),
  );

  useEffect(() => {
    if (robotConfig?.effectiveTimeConfig) {
      const result = formatSelectedTimesToString(robotConfig.effectiveTimeConfig || {});
      setSelectedTimeText(result);
    }
  }, [robotConfig?.effectiveTimeConfig]);

  const handleTimeSelectOk = async () => {
    const res = await PostEffectiveTime({
      projectId,
      effectiveTimeConfig: selectedTime || timePeriodSelectorValue || {},
    });
    if (res.code === 0) {
      message.success('提交成功');
      setIsTimeModalOpen(false);
      await refreshConfig();
    } else {
      message.error('提交失败');
    }
  };

  const handleRobotSwitchChange = async (checked: boolean) => {
    if (checked) {
      await PostSwitchStatus({ projectId, status: 1 });
      await refreshConfig();
    } else {
      modal.confirm({
        title: (
          <Space>
            <InfoCircleFilled style={{ color: '#1E5EFF' }} />
            建议开启夜间托管
          </Space>
        ),
        icon: null,
        content:
          '智能客服支持夜间托管，可以为您的账号在23点~次日9点 智能接管客户消息，建议您开启使用。若彻底关闭，可能影响留资转化率。',
        okText: '开启夜间托管',
        cancelText: '暂不考虑, 彻底关闭',
        async onCancel() {
          await PostSwitchStatus({ projectId, status: 0 });
          await refreshConfig();
        },
      });
    }
  };

  const handleEnableTeamChange = async (teamCodeList: string[]) => {
    await PostEnableTeamUpdate({
      projectId,
      enableType: 0,
      teamCodeList: teamCodeList.length > 0 ? teamCodeList : undefined,
    });
    await refreshTreeValue();
  };

  const handleEnableTypeChange = async (value: 0 | 1) => {
    await PostEnableTeamUpdate({
      projectId,
      enableType: value,
    });
    await refreshConfig();
  };

  const switchStatus = Boolean(robotConfig?.status);

  return (
    <PageContainer title={<BreadCrumbSimple breadcrumbs={['私信管理', '客服中心']} />}>
      <div className="rounded-md bg-white px-4 py-3 pt-0">
        <Tabs activeKey={activeKey} onChange={onTabChange} items={items} />
        {activeKey === 'ai-customer' && (
          <div className="h-[150px] w-full rounded-md bg-new-media-blue-100 p-4">
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2">
                <img
                  src={robotConfig?.status ? RobotEnable : RobotDisable}
                  alt="robot"
                  className="h-8 w-8"
                />
                <span className="text-base font-medium">智能销售机器人接待</span>
                <Tooltip title="智能客服适用于出外景、夜间无人值守、直播流量暴增时确保能够及时回复用户的咨询，有效获取线索留资。">
                  <QuestionCircleOutlined style={{ fontSize: '18px' }} />
                </Tooltip>
                <Switch value={switchStatus || false} onChange={handleRobotSwitchChange} />
              </div>
            </div>
            <div className="mt-4 text-muted-foreground">
              对话过程中，人工客服介入后，当天智能客服对该用户不生效
            </div>
            <div className="mt-4 flex items-center gap-8">
              <div className="flex items-center gap-2">
                <span>可用门店</span>
                <Select
                  style={{ width: 120 }}
                  value={robotConfig?.enableAllTeam}
                  onChange={handleEnableTypeChange}
                  disabled={!switchStatus}
                  options={[
                    { label: '全部门店', value: 1 },
                    { label: '部分门店', value: 0 },
                  ]}
                />
                {robotConfig?.enableAllTeam === 0 && (
                  <DynamicTree
                    style={{ width: 250 }}
                    value={treeValue}
                    onChange={handleEnableTeamChange}
                    disabled={!switchStatus}
                  />
                )}
              </div>
              <div className="flex items-center gap-2">
                <span>接待时段设置</span>
                <Tooltip
                  title={
                    selectedTimeText ? (
                      <pre className="whitespace-pre-wrap font-pingfang">{selectedTimeText}</pre>
                    ) : (
                      '选择时间'
                    )
                  }
                >
                  <Select
                    placeholder="选择时间"
                    style={{ width: 250 }}
                    value={selectedTimeText || undefined}
                    onClick={() => {
                      if (!switchStatus) return;
                      setIsTimeModalOpen(true);
                    }}
                    open={false}
                    disabled={!switchStatus}
                  />
                </Tooltip>
              </div>
            </div>
          </div>
        )}
        {activeKey === 'human-customer' && (
          <div className="h-[100px] w-full rounded-md bg-new-media-blue-100 p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <img src={HumanCustomerAvatar} alt="human" className="h-8 w-8" />
                <span className="text-base font-medium">人工客服接待中</span>
              </div>
              <Link
                to={`/customer-system?projectId=${projectId}`}
                target="_blank"
                className="text-right"
              >
                跳转人工客服页面
              </Link>
            </div>
            <div className="mt-4 text-muted-foreground">
              如已开启智能客服，对话过程中，人工客服一旦介入，当天智能客服对该用户不生效。
            </div>
          </div>
        )}
      </div>

      {/* pointer-events-none 阻止事件冒泡 与 cursor-not-allowed 阻止用户点击 同时存在在一个元素上时候 禁用指针效果会不显示 所以分两个 div */}
      <div
        className={cn(
          'mt-5 rounded-md bg-white px-4',
          !switchStatus && activeKey === 'ai-customer' && 'cursor-not-allowed',
          !canWriteProject && 'cursor-not-allowed',
        )}
      >
        <div
          className={cn(
            'w-full',
            !switchStatus && activeKey === 'ai-customer' && 'pointer-events-none opacity-60',
            !canWriteProject && 'pointer-events-none opacity-60',
          )}
        >
          {activeKey === 'ai-customer' && (
            <AiCustomer
              robotConfig={robotConfig}
              refreshConfig={refreshConfig}
              switchStatus={switchStatus}
            />
          )}
          {activeKey === 'human-customer' && <HumanCustomer />}
        </div>
      </div>

      <Modal
        title="设置接待时段"
        open={isTimeModalOpen}
        onCancel={() => setIsTimeModalOpen(false)}
        onOk={handleTimeSelectOk}
        width={800}
        destroyOnHidden
      >
        <TimePeriodSelector value={timePeriodSelectorValue} onChange={setSelectedTime} />
      </Modal>

      <div>{contextHolder}</div>
    </PageContainer>
  );
};

export default withAuth(CustomerService, FunctionCode.PrivateMessage);
