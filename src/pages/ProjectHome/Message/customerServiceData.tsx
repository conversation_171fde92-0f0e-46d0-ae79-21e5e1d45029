import { PageContainer } from '@ant-design/pro-components';
import BreadCrumbSimple from '@/components/ui/breadcrumbSimple';
import { Space, Tabs, TabsProps } from 'antd';
import { useRef, useState } from 'react';
import PlatformSwitch from '@/components/platformSwitch';
import TimeFilter from '@/components/ui/timeFilter';
import { getTimeByType } from '@/utils/time';
import AllDataChart from './customerData/allDataChart';
import HumanCustomerData from './customerData/humanCustomerData';
import AICustomerData from './customerData/aiCustomerData';
import useProjectId from '@/hooks/useProjectId';
import useGetTeamFieldList from '@/hooks/useFields';
import { cn } from '@/lib/utils';
import { useSticky } from '@reactuses/core';
import ConversationDetail from './customerData/conversationDetail';
import DynamicTree from '@/components/dynamicTree';

const CustomerServiceData = () => {
  const projectId = useProjectId();
  const teamFieldListRef = useGetTeamFieldList(projectId);
  const lastFieldId = teamFieldListRef.current?.filter(
    (item) => item.bizType === 0 && item.type === 0,
  )[0]?.id;

  const [activeKey, setActiveKey] = useState('all-customer-data');
  const [rangeTime, setRangeTime] = useState<(string | undefined)[]>(getTimeByType('day'));
  const headerRef = useRef<HTMLDivElement>(null);
  const [isSticky] = useSticky(headerRef, { nav: 56 });
  const [teamCodeList, setTeamCodeList] = useState<string[]>([]);

  const items: TabsProps['items'] = [
    {
      label: '全部数据',
      key: 'all-customer-data',
    },
    {
      label: '智能客服数据',
      key: 'ai-customer-data',
    },
    {
      label: '人工客服数据',
      key: 'human-customer-data',
    },
    {
      label: '会话明细记录',
      key: 'session-detail',
    },
  ];

  const handleTabChange = (key: string) => {
    setActiveKey(key);
    const element = document.getElementById(key);
    if (element) {
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
      });
    }
  };

  return (
    <PageContainer
      title={<BreadCrumbSimple breadcrumbs={['私信管理', '客服数据']} />}
      style={{
        position: 'relative',
      }}
    >
      <div
        ref={headerRef}
        className={cn(
          'sticky top-[56px] z-[99] transition-all duration-200',
          isSticky &&
            'rounded-lg bg-white shadow-[0_1px_2px_-2px_#00000029,0_3px_6px_#0000001f,0_5px_12px_4px_#00000017]',
        )}
      >
        <Tabs
          activeKey={activeKey}
          onChange={handleTabChange}
          items={items}
          className="horizontal-tab"
        />

        <div className="rounded-lg bg-white px-4 py-2">
          <Space>
            <TimeFilter value={rangeTime} onChange={(value) => setRangeTime(value)} />
            <DynamicTree
              style={{ width: '266px' }}
              value={teamCodeList}
              onChange={setTeamCodeList}
            />
            <PlatformSwitch onlyDouyin />
          </Space>
        </div>
      </div>

      <div id="all-customer-data">
        <AllDataChart rangeTime={rangeTime} lastFieldId={lastFieldId} teamCodeList={teamCodeList} />
      </div>
      <div id="ai-customer-data">
        <AICustomerData
          rangeTime={rangeTime}
          lastFieldId={lastFieldId}
          teamCodeList={teamCodeList}
        />
      </div>
      <div id="human-customer-data">
        <HumanCustomerData
          rangeTime={rangeTime}
          lastFieldId={lastFieldId}
          teamCodeList={teamCodeList}
        />
      </div>
      <div id="session-detail">
        <ConversationDetail
          rangeTime={rangeTime}
          lastFieldId={lastFieldId}
          teamCodeList={teamCodeList}
        />
      </div>
    </PageContainer>
  );
};

export default CustomerServiceData;
