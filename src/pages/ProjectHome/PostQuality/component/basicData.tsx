import BlueVip from '@/assets/blue-vip.png';
import FailImg from '@/assets/fail-img.png';
import DefaultAvatar from '@/assets/default-avatar.png';
import AggregateQueryInput from '@/components/aggregateQueryInput';
import DataCard from '@/components/dataCard';
import DynamicTree from '@/components/dynamicTree';
import ExInfoDetail from '@/components/exInfo';
import ExportButton from '@/components/exportButton';
import FilterDropdownRadio from '@/components/filterDropdownRadio';
import QualityTypeSelect from '@/components/qualityTypeSelect';
import { renderCustomColumns } from '@/components/renderCustomColumns';
import TagsSelect from '@/components/tags/tagsSelect';
import TagsShow from '@/components/tags/tagsShow';
import PlatformSwitch from '@/components/platformSwitch';
import TimeFilter from '@/components/ui/timeFilter';
import XGVideoCard from '@/components/xgVideoCard';
import useGetTeamFieldList from '@/hooks/useFields';
import { usePollingExport } from '@/hooks/usePollingExport';
import useProjectId from '@/hooks/useProjectId';
import { AccountPlatformEnum } from '@/utils/platform';
import { GetDataCardTrend } from '@/services/data-card';
import { ExportPostQuality, PostQualityItem, PostQualityPage } from '@/services/quality';
import { proTableRequestAdapter } from '@/utils';
import { formatNum, STICKY_OFFSETHEADER, transferParams } from '@/utils/common';
import { HideScrollBarRow, TagSpan } from '@/utils/commonStyle';
import {
  proTableOptionsConfig,
  proTablePaginationConfig,
  proTableSearchConfig,
} from '@/utils/proTableConfig';
import { disabledDate, formatSecond, getTimeByType } from '@/utils/time';
import {
  CaretRightOutlined,
  DownloadOutlined,
  FilterFilled,
  InfoCircleOutlined,
  LeftOutlined,
  RightOutlined,
} from '@ant-design/icons';
import {
  ActionType,
  ProCard,
  ProColumns,
  ProFormInstance,
  ProTable,
} from '@ant-design/pro-components';
import { FileTextIcon } from '@radix-ui/react-icons';
import { useParams, useRequest } from '@umijs/max';
import { Avatar, Button, Col, Flex, message, Popover, Radio, Space, Tooltip } from 'antd';
import { FilterDropdownProps } from 'antd/es/table/interface';
import copy from 'copy-to-clipboard';
import dayjs from 'dayjs';
import saveAs from 'file-saver';
import { AnimatePresence, motion } from 'framer-motion';
import { useAtomValue } from 'jotai';
import { omit } from 'lodash-es';
import { useEffect, useRef, useState } from 'react';
import styled from 'styled-components';
import { selectPlatformAtom } from '../../atom';
import { Copy } from 'lucide-react';
import { useGetState } from 'ahooks';
import { getSumColumnsWidth } from '@/utils/table';
import { useHorizontalDragScroll } from '@/hooks/useHorizontalDragScroll';

export type HitRuleTimes = {
  hitRuleStartTime: string;
  hitRuleEndTime: string;
};

const TitleHref = styled.a`
  width: 300px;
  word-break: break-all;
  margin: 0 auto;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const PostInspection = () => {
  const projectId = useProjectId();
  const { industryType } = useParams();
  const platform = useAtomValue(selectPlatformAtom);
  const options = [
    { value: 'nickname', label: '账号名称' },
    { value: 'title', label: '作品标题' },
  ];
  const [qualityResult, setQualityResult, getQualityResult] = useGetState<number | undefined>();
  const [qualityStatus, setQualityStatus, getQualityStatus] = useGetState<number | undefined>();

  const handleDownload = async (record: any) => {
    try {
      const response = await fetch(record.url);
      if (!response.ok) {
        throw new Error('无法下载视频');
      }
      const blob = await response.blob();
      saveAs(blob, record.title.slice(0, 10) + '...');
    } catch (error) {
      console.error('下载失败:', error);
    }
  };

  const defaultColumns: ProColumns<PostQualityItem>[] = [
    {
      title: '聚合查询',
      dataIndex: 'aggregateQuery',
      hideInTable: true,
      formItemProps: {
        label: null,
      },
      search: {
        transform: (value: string[]) => {
          if (value.length > 0) {
            return {
              [value[0]]: value[1],
            };
          }
        },
      },
      renderFormItem: () => {
        return <AggregateQueryInput selectOptions={options} />;
      },
    },
    {
      title: '动态维度筛选',
      dataIndex: 'teamCodeList',
      hideInTable: true,
      formItemProps: {
        label: null,
      },
      renderFormItem: () => {
        return <DynamicTree />;
      },
    },
    {
      title: '作品信息',
      dataIndex: 'cover',
      fixed: 'left',
      width: 100,
      hideInSearch: true,
      render: (text, record) => {
        return (
          <Flex justify="flex-start" align="center" gap={5}>
            {record.cleanFlag ? (
              <>
                <div
                  style={{ position: 'relative' }}
                  onClick={() => {
                    window.open(record.shareLink, '_blank');
                  }}
                >
                  <img
                    src={record.cover || FailImg}
                    width={81}
                    height={108}
                    style={{ objectFit: 'cover' }}
                  />
                  <Button
                    type="text"
                    size="large"
                    shape="circle"
                    style={{
                      position: 'absolute',
                      left: '50%',
                      top: '50%',
                      color: '#fff',
                      transform: 'translate(-50%, -50%)',
                      backgroundColor: 'rgba(0,0,0,.5)',
                    }}
                    icon={<CaretRightOutlined />}
                  />
                </div>
                <Tooltip title={record.title}>
                  <TitleHref href={record.shareLink} target="_blank" rel="noreferrer">
                    {record.title}
                  </TitleHref>
                </Tooltip>
              </>
            ) : (
              <Flex justify="flex-start" align="stretch">
                <XGVideoCard
                  videoUrl={record.postUrl}
                  coverUrl={record.cover || FailImg}
                  playableVideoUrl={record.postUrl}
                  videoWidth={315}
                  videoHeight={562}
                  width={81}
                  height={108}
                  noShowTag={true}
                />
                <Flex vertical justify="space-between" gap={4} style={{ marginLeft: '8px' }}>
                  <Tooltip title={record.title}>
                    <TitleHref href={record.shareLink} target="_blank" rel="noreferrer">
                      {record.title}
                    </TitleHref>
                  </Tooltip>
                  <Tooltip title={record.topic}>
                    {record.topic && (
                      <div className="line-clamp-1 text-xs font-normal text-black/60">
                        话题 {record.topic}
                      </div>
                    )}
                  </Tooltip>
                  <div className="text-xs font-normal text-black/60">
                    发布时间 {record.publishTime} 作品时长 {formatSecond(record.duration)}
                  </div>
                  <Space size="large" className="text-sm font-normal text-[#1e5eff]">
                    {record.url && (
                      <div onClick={() => handleDownload(record)} className="hover:cursor-pointer">
                        <DownloadOutlined /> 下载视频
                      </div>
                    )}
                    <div
                      onClick={() => {
                        window.open(
                          `${window.location.origin}/post/detail/${record.postId}/${record.platform}/${industryType}`,
                        );
                      }}
                      className="hover:cursor-pointer"
                    >
                      <Flex align="center" justify="center" gap={2}>
                        <FileTextIcon /> 详情
                      </Flex>
                    </div>
                  </Space>
                </Flex>
              </Flex>
            )}
          </Flex>
        );
      },
    },
    {
      title: '账号信息',
      dataIndex: 'nickname',
      width: 300,
      ellipsis: true,
      hideInSearch: true,
      fixed: 'left',
      align: 'left',
      render: (text, record) => {
        return (
          <Flex vertical gap={5}>
            <Space>
              <Avatar
                size="small"
                src={record.avatar || DefaultAvatar}
                style={{ width: '16px', height: '16px' }}
              />
              <span>{record.nickname}</span>
              {record.blueVipFlag ? <img src={BlueVip} width={18} /> : null}
            </Space>
            <Space>
              <div className="text-xs font-normal text-black/60">
                {AccountPlatformEnum[platform]?.text}ID: {record.showAccountId}
              </div>
              <Tooltip title="复制账号ID">
                <Copy
                  size={13}
                  className="mt-1 hover:cursor-pointer"
                  onClick={() => {
                    if (record.showAccountId) {
                      copy(record.showAccountId);
                      message.success('账号ID已复制到剪切板');
                    }
                  }}
                />
              </Tooltip>
            </Space>
            <div>
              <TagsShow tags={record.tags} />
            </div>
          </Flex>
        );
      },
    },
    {
      title: '账号标签',
      dataIndex: 'tagIds',
      align: 'left',
      fieldProps: {
        placeholder: '账号标签',
      },
      formItemProps: {
        label: null,
      },
      hideInTable: true,
      renderFormItem: () => {
        return <TagsSelect />;
      },
    },
    {
      title: '质检结果',
      dataIndex: 'qualityResultStatus',
      width: 120,
      valueEnum: () => {
        return {
          0: { text: '正常', color: 'green' },
          1: { text: '异常', color: 'red' },
        };
      },
      hideInSearch: true,
      align: 'left',
      filterIcon: () => (
        <FilterFilled style={{ color: getQualityResult() !== undefined ? '#3B77F6' : undefined }} />
      ),
      filterDropdown: (props: FilterDropdownProps) => {
        return (
          <FilterDropdownRadio
            title="质检结果"
            options={[
              { label: '正常', value: 0 },
              { label: '异常', value: 1 },
            ]}
            filterDropdownProps={props}
            setValueChange={setQualityResult}
          />
        );
      },
    },
    {
      title: '质检状态',
      dataIndex: 'qualityStatus',
      width: 120,
      valueEnum: {
        0: { text: '分析中' },
        1: { text: '已结束' },
      },
      hideInSearch: true,
      align: 'left',
      filterIcon: () => (
        <FilterFilled style={{ color: getQualityStatus() !== undefined ? '#3B77F6' : undefined }} />
      ),
      filterDropdown: (props: FilterDropdownProps) => {
        return (
          <FilterDropdownRadio
            title="质检状态"
            options={[
              { label: '分析中', value: 0 },
              { label: '已结束', value: 1 },
            ]}
            filterDropdownProps={props}
            setValueChange={setQualityStatus}
          />
        );
      },
    },
    {
      title: '质检类型筛选',
      dataIndex: 'monitoringWordIdList',
      hidden: true,
      renderFormItem: () => <QualityTypeSelect projectId={projectId} isPost />,
    },
    {
      title: '质检类型',
      dataIndex: 'monitoringNameStr',
      width: 300,
      align: 'left',
      hideInSearch: true,
      render: (_, record) => {
        const { monitoringNameInfoList } = record;
        if (monitoringNameInfoList && monitoringNameInfoList?.length > 0) {
          return (
            <Flex wrap="wrap" gap={5} justify="flex-start">
              {monitoringNameInfoList.map(({ monitoringName, monitoringNameId }) => (
                <Popover
                  key={monitoringName}
                  content={
                    <ExInfoDetail
                      targetType={2}
                      platform={record.platform}
                      projectId={projectId}
                      monitoringNameId={monitoringNameId}
                      targetId={record.postId}
                    />
                  }
                  title={monitoringName}
                >
                  <TagSpan key={monitoringName} $bgColor="#fbebe2" $textColor="#FF7533">
                    {monitoringName}
                  </TagSpan>
                </Popover>
              ))}
            </Flex>
          );
        } else {
          return '-';
        }
      },
    },
    {
      title: '点赞数',
      dataIndex: 'diggCount',
      width: 80,
      hideInSearch: true,
      align: 'right',
      render: (text, record) => formatNum(record.diggCount),
    },
    {
      title: '评论数',
      dataIndex: 'commentCount',
      width: 60,
      hideInSearch: true,
      align: 'right',
      render: (text, record) => formatNum(record.commentCount),
    },
    {
      title: '收藏数',
      dataIndex: 'collectCount',
      width: 60,
      hideInSearch: true,
      align: 'right',
      render: (text, record) => formatNum(record.collectCount),
    },
    {
      title: '分享数',
      dataIndex: 'shareCount',
      width: 60,
      hideInSearch: true,
      align: 'right',
      render: (text, record) => formatNum(record.shareCount),
    },
  ];

  const actionRef = useRef<ActionType>();
  const formRef = useRef<ProFormInstance>();
  const firstAddColumns = useRef(false);
  const filedIdArrRef = useRef<string[]>([]);
  const [columns, setColumns] = useState<ProColumns<PostQualityItem>[]>(defaultColumns);
  const [hitRuleTimes, setHitRuleTimes] = useState<HitRuleTimes | undefined>(undefined);
  const [radioValue, setRadioValue] = useState<number | null>(null);
  const teamFieldListRef = useGetTeamFieldList(projectId);
  const paramsRef = useRef<any>({});
  const { pollingExport, percent, pollingLoading } = usePollingExport();
  const [showLeftBtn, setShowLeftBtn] = useState(false);
  const scrollContainerRef = useHorizontalDragScroll<HTMLDivElement>();
  const [hasHorizontalScroll, setHasHorizontalScroll] = useState(false);
  const [rangeTime, setRangeTime] = useState<(string | undefined)[]>(getTimeByType('today'));
  const timeFilterRef = useRef<{ value: string } | null>({ value: '' });
  const [isEmptyTable, setIsEmptyTable] = useState(false);

  const onChangeHitRuleTimes = (minuteTime: number) => {
    const hitRuleStartTime = dayjs().subtract(minuteTime, 'minute').format('YYYY-MM-DD HH:mm:ss');
    const hitRuleEndTime = dayjs().format('YYYY-MM-DD HH:mm:ss');
    setRadioValue(minuteTime);
    setHitRuleTimes({ hitRuleEndTime, hitRuleStartTime });
  };

  const radioClick = (value: number) => {
    if (radioValue === value) {
      setRadioValue(null);
      setHitRuleTimes(undefined);
      actionRef.current?.reload();
    }
  };

  // 导出数据量大特殊处理
  const handleExportPostQuality = async () => {
    const omitPageParams = omit(paramsRef.current, ['pageSize', 'current']);
    const res = await ExportPostQuality({
      ...omitPageParams,
    });
    if (res.data) {
      pollingExport({ taskId: res.data });
    } else {
      message.error('导出失败,请重试');
    }
  };

  const requestFn = (type: number) =>
    GetDataCardTrend({
      projectId,
      platform,
      startDate: rangeTime[0],
      endDate: rangeTime[1],
      type,
    });

  const { data: storeNumData, loading: storeNumDataLoading } = useRequest(() => requestFn(401), {
    refreshDeps: [rangeTime, platform],
  });

  const { data: postNumData, loading: postNumDataLoading } = useRequest(() => requestFn(402), {
    refreshDeps: [rangeTime, platform],
  });

  const { data: postViolationNumData, loading: postViolationNumDataLoading } = useRequest(
    () => requestFn(403),
    {
      refreshDeps: [rangeTime, platform],
    },
  );

  const { data: postViolationRateData, loading: postViolationRateDataLoading } = useRequest(
    () => requestFn(404),
    {
      refreshDeps: [rangeTime, platform],
    },
  );

  const handleScroll = () => {
    if (scrollContainerRef.current) {
      const { scrollLeft } = scrollContainerRef.current;
      setShowLeftBtn(scrollLeft >= 30);
    }
  };

  useEffect(() => {
    const container = scrollContainerRef.current;
    if (container) {
      container.addEventListener('scroll', handleScroll);
    }

    return () => {
      // 清理滚动监听
      if (container) {
        container.removeEventListener('scroll', handleScroll);
      }
    };
  }, []);

  const scroll = (width: number) => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: width, behavior: 'smooth' });
    }
  };

  // 检查容器是否有横向滚动条
  const checkScrollBar = () => {
    if (scrollContainerRef.current) {
      const { scrollWidth, clientWidth } = scrollContainerRef.current;
      // 如果 scrollWidth > clientWidth，表示有横向滚动条
      setHasHorizontalScroll(scrollWidth > clientWidth);
    }
  };

  useEffect(() => {
    // 初次检查
    checkScrollBar();

    // 添加 resize 事件监听
    window.addEventListener('resize', checkScrollBar);

    // 清理事件监听
    return () => {
      window.removeEventListener('resize', checkScrollBar);
    };
  }, []);

  useEffect(() => {
    if (timeFilterRef.current?.value !== 'today') {
      setRadioValue(null);
      setHitRuleTimes(undefined);
    }
  }, [timeFilterRef.current?.value]);

  useEffect(() => {
    setColumns(defaultColumns);
    firstAddColumns.current = false;
  }, [platform]);

  return (
    <>
      <div className="rounded-lg bg-white px-4 py-2">
        <Space>
          <TimeFilter
            showToday
            value={rangeTime}
            onChange={(value) => setRangeTime(value)}
            rangeDisable={disabledDate}
            ref={timeFilterRef}
          />
          <PlatformSwitch />
        </Space>
      </div>
      <Flex justify="center" align="center">
        <AnimatePresence>
          {showLeftBtn && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.5 }}
              style={{
                zIndex: '1000',
              }}
            >
              <Button shape="circle" icon={<LeftOutlined />} onClick={() => scroll(-300)} />
            </motion.div>
          )}
        </AnimatePresence>
        <HideScrollBarRow
          gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}
          ref={scrollContainerRef}
          className="py-4"
          wrap={false}
        >
          <Col xs={8} sm={8} md={7} lg={7} xl={8} xxl={5} span={5}>
            <DataCard title="违规门店数" data={storeNumData} loading={storeNumDataLoading} />
          </Col>
          <Col xs={8} sm={8} md={7} lg={7} xl={8} xxl={5} span={5}>
            <DataCard title="总作品数" data={postNumData} loading={postNumDataLoading} />
          </Col>
          <Col xs={8} sm={8} md={7} lg={7} xl={8} xxl={5} span={5}>
            <DataCard
              title="违规作品数"
              data={postViolationNumData}
              loading={postViolationNumDataLoading}
            />
          </Col>
          <Col xs={8} sm={8} md={7} lg={7} xl={8} xxl={5} span={5}>
            <DataCard
              title="总违规率"
              isPercentage
              data={postViolationRateData}
              loading={postViolationRateDataLoading}
            />
          </Col>
        </HideScrollBarRow>
        {hasHorizontalScroll && (
          <Button
            shape="circle"
            icon={<RightOutlined />}
            style={{
              zIndex: '1000',
            }}
            onClick={() => scroll(300)}
          />
        )}
      </Flex>
      <ProCard>
        <ProTable<PostQualityItem>
          columns={columns}
          actionRef={actionRef}
          formRef={formRef}
          params={{
            projectId,
            ...hitRuleTimes,
            platform,
            qualityResult,
            qualityStatus,
            publishStartTime: dayjs(rangeTime[0]).format('YYYY-MM-DD 00:00:00'),
            publishEndTime: dayjs(rangeTime[1]).format('YYYY-MM-DD 23:59:59'),
          }}
          onReset={() => {
            setRadioValue(null);
          }}
          tableClassName="custom-table"
          scroll={{ x: isEmptyTable ? getSumColumnsWidth(columns) : 'max-content' }}
          ghost
          sticky={{ offsetHeader: STICKY_OFFSETHEADER }}
          postData={(data: PostQualityItem[]) => {
            // 判断表格数据是否为空
            data.length === 0 ? setIsEmptyTable(true) : setIsEmptyTable(false);
            if (!firstAddColumns.current && data.length > 0) {
              renderCustomColumns(teamFieldListRef.current).then(
                ({ customColumns, fieldIdArr }) => {
                  filedIdArrRef.current = fieldIdArr;
                  const newColumns = [
                    ...columns,
                    ...customColumns,
                  ] as ProColumns<PostQualityItem>[];
                  setColumns(newColumns);
                },
              );
              firstAddColumns.current = true;
            }
            // 将teamFields内的字段都抽出来
            const formatData = data.map((item) => {
              const res = { ...item };
              const teamFields = res.teamFieldList;
              if (teamFields) {
                teamFields.forEach((field) => {
                  (res as any)[field.fieldId] = field.value;
                });
              }
              return res;
            });
            return formatData;
          }}
          beforeSearchSubmit={(params) => {
            const fieldList = transferParams(params, filedIdArrRef.current);
            if (fieldList.length > 0) {
              return { ...params, fieldList };
            }
            return params;
          }}
          request={(params, sorter, filter) => {
            paramsRef.current = params;
            return proTableRequestAdapter(params, sorter, filter, PostQualityPage);
          }}
          search={{ ...proTableSearchConfig }}
          options={{ ...proTableOptionsConfig }}
          pagination={{ ...proTablePaginationConfig }}
          rowKey="postId"
          dateFormatter="string"
          columnsState={{
            persistenceKey: 'PostQuality-Table-Columns',
            persistenceType: 'localStorage',
          }}
          toolBarRender={() => [
            <ExportButton
              exportFn={() => handleExportPostQuality()}
              loading={pollingLoading}
              percent={percent}
              key="export"
            />,
          ]}
          toolbar={{
            title: (
              <>
                {timeFilterRef?.current?.value === 'today' ? (
                  <Space>
                    <Radio.Group
                      value={radioValue}
                      onChange={(e) => onChangeHitRuleTimes(e.target.value)}
                      buttonStyle="solid"
                    >
                      <Radio.Button value={10} onClick={() => radioClick(10)}>
                        近10分钟
                      </Radio.Button>
                      <Radio.Button value={30} onClick={() => radioClick(30)}>
                        近半小时
                      </Radio.Button>
                      <Radio.Button value={60} onClick={() => radioClick(60)}>
                        近1小时
                      </Radio.Button>
                      <Radio.Button value={60 * 24} onClick={() => radioClick(60 * 24)}>
                        近1天内
                      </Radio.Button>
                    </Radio.Group>
                    <Tooltip title="命中规则时间">
                      <InfoCircleOutlined />
                    </Tooltip>
                  </Space>
                ) : null}
              </>
            ),
          }}
        />
      </ProCard>
    </>
  );
};

export default PostInspection;
