import { PageContainer } from '@ant-design/pro-components';
import { Space, Tabs, TabsProps } from 'antd';
import EditWordModal from '@/components/editWordModal';
import QualityVisualData from '@/components/qualityVisual/qualityVisualData';
import useProjectId from '@/hooks/useProjectId';
import useTabKeySearchParams from '@/hooks/useTabKeySearchParams';
import PostInspection from './component/basicData';
import BreadCrumbSimple from '@/components/ui/breadcrumbSimple';

const AccountSetting = () => {
  const projectId = useProjectId();
  const [activeKey, onTabChange] = useTabKeySearchParams('basicdata');

  const items: TabsProps['items'] = [
    {
      label: '基础数据',
      key: 'basicdata',
      children: <PostInspection />,
    },
    {
      label: '质检可视化',
      key: 'visualdata',
      children: <QualityVisualData projectId={projectId} type="post" />,
    },
  ];

  return (
    <PageContainer
      title={<BreadCrumbSimple breadcrumbs={['质检管理', '作品质检']} />}
      extra={
        <Space>
          <EditWordModal key={'editModal'} projectId={projectId} />
        </Space>
      }
    >
      <Tabs activeKey={activeKey} onChange={onTabChange} items={items} className="horizontal-tab" />
    </PageContainer>
  );
};

export default AccountSetting;
