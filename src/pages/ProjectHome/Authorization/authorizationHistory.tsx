import BreadCrumbSimple from '@/components/ui/breadcrumbSimple';
import { PageContainer, ProCard, ProColumns, ProTable } from '@ant-design/pro-components';
import {
  proTableOptionsConfig,
  proTablePaginationConfig,
  proTableSearchConfig,
} from '@/utils/proTableConfig';
import { STICKY_OFFSETHEADER } from '@/utils/common';
import { proTableRequestAdapter } from '@/utils';
import {
  ExportOceanengineAdvertiserPage,
  GetOceanengineAdvertiserPage,
  OceanengineAdvertiserResult,
  UpdateOceanengineAdvertiserStatus,
} from '@/services/oceanengine';
import useProjectId from '@/hooks/useProjectId';
import ExportButton from '@/components/exportButton';
import { usePollingExport } from '@/hooks/usePollingExport';
import { useRef } from 'react';
import { omit } from 'lodash-es';
import { Button, message } from 'antd';
import dayjs from 'dayjs';
import { useNavigate, useParams } from '@umijs/max';

export default function AuthorizationHistory() {
  const projectId = useProjectId();
  const { projectKey, industryType = '1' } = useParams();
  const paramsRef = useRef<any>({});
  const navigate = useNavigate();
  const { pollingExport, percent, pollingLoading } = usePollingExport(
    dayjs().format('YYYYMMDD-HH:mm:ss') + '广告主信息',
  );

  const columns: ProColumns<OceanengineAdvertiserResult>[] = [
    {
      title: '广告主ID',
      dataIndex: 'advertiserId',
      align: 'left',
      fieldProps: {
        placeholder: '广告主ID',
      },
    },
    {
      title: '广告主名称',
      dataIndex: 'advertiserName',
      align: 'left',
      fieldProps: {
        placeholder: '广告主名称',
      },
    },
    {
      title: '授权方式',
      dataIndex: 'identityType',
      align: 'left',
      valueType: 'select',
      fieldProps: {
        placeholder: '授权方式',
      },
      valueEnum: {
        1: '广告主类型',
        2: '代理商类型',
      },
    },
    {
      title: '授权日期',
      dataIndex: 'authorizationDateTime',
      align: 'left',
      valueType: 'dateRange',
      fieldProps: {
        placeholder: ['授权日期-开始', '授权日期-结束'],
        disabledDate: (current: dayjs.Dayjs) => {
          return current && current > dayjs().startOf('day');
        },
      },
      search: {
        transform: (value) => {
          return {
            authorizationDateTimeStart: value[0],
            authorizationDateTimeEnd: value[1],
          };
        },
      },
      render: (_, record) => {
        return <span>{record.authorizationDateTime}</span>;
      },
    },
    {
      title: '操作人',
      dataIndex: 'operatorName',
      align: 'left',
      hideInSearch: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      align: 'left',
      hideInSearch: true,
      valueEnum: {
        0: {
          text: '已启用',
          color: 'green',
        },
        1: {
          text: '已禁用',
          color: 'red',
        },
      },
    },
    {
      title: '操作',
      valueType: 'option',
      fixed: 'right',
      align: 'center',
      hideInSearch: true,
      render: (_, record) => {
        return (
          <>
            {record.status === 1 ? (
              '-'
            ) : (
              <Button
                type="link"
                onClick={() => {
                  if (!record.advertiserId) {
                    message.error('广告主ID不能为空');
                    return;
                  }
                  UpdateOceanengineAdvertiserStatus({
                    advertiserId: record.advertiserId,
                    projectId,
                    status: 1,
                  });
                }}
              >
                停用
              </Button>
            )}
          </>
        );
      },
    },
  ];

  const handleExport = async () => {
    const omitPageParams = omit(paramsRef.current, ['pageSize', 'current']);
    const res = await ExportOceanengineAdvertiserPage({
      ...omitPageParams,
    });
    if (res.data) {
      pollingExport({ taskId: res.data });
    } else {
      message.error('导出失败');
    }
  };

  const handleContinueAuthorization = () => {
    navigate(`/project/${projectKey}/${industryType}/authorization/account-authorization`);
  };

  return (
    <PageContainer
      title={<BreadCrumbSimple breadcrumbs={['授权管理', '授权历史']} />}
      extra={
        <Button type="link" onClick={handleContinueAuthorization}>
          继续授权
        </Button>
      }
    >
      <ProCard>
        <ProTable
          columns={columns}
          params={{ projectId }}
          scroll={{ x: 'max-content' }}
          rowKey="advertiserId"
          ghost
          sticky={{ offsetHeader: STICKY_OFFSETHEADER }}
          tableClassName="custom-table"
          search={{ ...proTableSearchConfig }}
          options={{ ...proTableOptionsConfig }}
          pagination={{ ...proTablePaginationConfig }}
          toolBarRender={() => [
            <ExportButton
              exportFn={() => handleExport()}
              key="export"
              loading={pollingLoading}
              percent={percent}
            />,
          ]}
          request={(params, sorter, filter) => {
            paramsRef.current = params;
            return proTableRequestAdapter(params, sorter, filter, GetOceanengineAdvertiserPage);
          }}
        />
      </ProCard>
    </PageContainer>
  );
}
