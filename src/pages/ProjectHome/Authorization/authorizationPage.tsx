import BreadCrumbSimple from '@/components/ui/breadcrumbSimple';
import { PageContainer } from '@ant-design/pro-components';
import { Button, message } from 'antd';
import { RightOutlined } from '@ant-design/icons';
import useProjectId from '@/hooks/useProjectId';
import {
  GetAuthorizationUrl,
  GetOceanengineAuthorizationTaskPage,
  TaskStatus,
} from '@/services/oceanengine';
import { useState, useEffect } from 'react';
import { useNavigate, useParams, useRequest } from '@umijs/max';
import { cn } from '@/lib/utils';
import { statusMap } from '@/pages/AuthorizationMiddlePage';
import AgentIcon from '@/assets/authStatus/agent.png';
import AdvertiserIcon from '@/assets/authStatus/advertiser.png';
import DocumentIcon from '@/assets/authStatus/document.png';

type AuthCardProps = {
  icon: React.ReactNode;
  title: string;
  description: string;
  buttonText: string;
  onClick: () => void;
};

const AuthCard = ({ icon, title, description, buttonText, onClick }: AuthCardProps) => {
  return (
    <div className="flex w-[360px] flex-col justify-between gap-6 rounded-lg bg-white p-6 shadow-[4px_4px_24px_0px_rgba(15,20,33,0.04)]">
      <div className="flex flex-col items-center gap-5">
        {icon}
        <div className="flex flex-col items-center gap-3">
          <h3 className="text-base font-semibold text-[#0E0F14]">{title}</h3>
          <p className="text-center text-xs text-[#A2A4A9]">{description}</p>
        </div>
      </div>
      <Button
        type="primary"
        className="h-9 w-full bg-[#1E5EFF] text-sm font-normal text-white"
        onClick={onClick}
      >
        {buttonText}
      </Button>
    </div>
  );
};

export default function AuthorizationPage() {
  const projectId = useProjectId();
  const { projectKey, industryType = '1' } = useParams();
  const navigate = useNavigate();
  const [taskId, setTaskId] = useState<string | null>(null);
  const [showAuthPage, setShowAuthPage] = useState(false);
  const [authUrl, setAuthUrl] = useState<string | null>(null);
  const [authWindow, setAuthWindow] = useState<Window | null>(null);

  useEffect(() => {
    if (authWindow) {
      const checkWindow = setInterval(() => {
        if (authWindow.closed) {
          setShowAuthPage(false);
          setAuthWindow(null);
          clearInterval(checkWindow);
        }
      }, 1000);

      return () => clearInterval(checkWindow);
    }
  }, [authWindow]);

  const handleAgentAuth = async () => {
    try {
      const res = await GetAuthorizationUrl({
        identityType: 2,
        projectId,
      });
      if (res.data?.authorizationUrl) {
        setTaskId(res.data.taskId);
        setShowAuthPage(true);
        setAuthUrl(res.data.authorizationUrl);
        const newWindow = window.open(res.data.authorizationUrl, '_blank');
        setAuthWindow(newWindow);
      }
    } catch (error) {
      message.error('获取授权链接失败');
    }
  };

  const handleAdvertiserAuth = async () => {
    try {
      const res = await GetAuthorizationUrl({
        identityType: 1,
        projectId,
      });
      if (res.data?.authorizationUrl) {
        setTaskId(res.data.taskId);
        setShowAuthPage(true);
        setAuthUrl(res.data.authorizationUrl);
        const newWindow = window.open(res.data.authorizationUrl, '_blank');
        setAuthWindow(newWindow);
      }
    } catch (error) {
      message.error('获取授权链接失败');
    }
  };

  const { data, cancel } = useRequest(
    () => {
      if (!taskId) {
        return Promise.reject(null);
      }
      return GetOceanengineAuthorizationTaskPage({ taskId });
    },
    {
      pollingInterval: 3000,
      pollingWhenHidden: false,
      ready: !!taskId && showAuthPage,
      onSuccess: (result) => {
        if (result?.status === TaskStatus.Success || result?.status === TaskStatus.Failed) {
          // 成功后停止轮询
          cancel();
        }
      },
    },
  );

  const currentStatus = data?.status ?? TaskStatus.NotExist;
  const statusInfo = statusMap[currentStatus];

  return (
    <PageContainer title={<BreadCrumbSimple breadcrumbs={['授权管理', '账号授权']} />}>
      <div className="relative flex h-[calc(100vh-56px-100px)] flex-col items-center gap-8 rounded-md bg-white p-8">
        {showAuthPage ? (
          <div className="mb-[82px] flex h-full flex-col items-center justify-center gap-2">
            <div className="mb-8 flex justify-center">
              <div className="relative">{statusInfo.icon}</div>
            </div>

            <div className={cn('mb-6 text-3xl font-semibold', statusInfo.color)}>
              {statusInfo.text}
            </div>

            {currentStatus === TaskStatus.Pending && authUrl && (
              <>
                <p className="mb-6 text-base text-gray-500">
                  请前往授权页面进行授权:
                  <a href={authUrl} target="_blank" rel="noreferrer" className="ml-2">
                    {authUrl.length > 30 ? `${authUrl.slice(0, 30)}...` : authUrl}
                  </a>
                </p>
              </>
            )}

            {currentStatus === TaskStatus.InProgress && (
              <p className="text-base text-gray-500">正在处理授权请求，请稍候...</p>
            )}

            {currentStatus === TaskStatus.Success && (
              <p className="text-base text-green-500">授权成功，请关闭页面</p>
            )}

            {currentStatus === TaskStatus.Failed && (
              <p className="text-base text-red-500">授权失败，请重试</p>
            )}
          </div>
        ) : (
          <div className="mb-[82px] flex h-full flex-col items-center justify-center gap-8">
            <div className="flex flex-col items-center gap-2">
              <h1 className="text-2xl font-semibold text-[#0E0F14]">选择授权方式</h1>
              <p className="text-center text-sm text-[#64666B]">
                欢迎使用巨量引擎开发者，请选择您的角色以获取相应的授权和数据
              </p>
            </div>
            <div className="flex gap-6">
              <AuthCard
                icon={<img src={AgentIcon} alt="代理商授权" className="w-24" />}
                title="代理商授权"
                description="适用于代理商、服务商等机构；登录【巨量引整方舟】-右上角-进入代理商后台，能进入说明为代理商角色"
                buttonText="开始代理商授权"
                onClick={handleAgentAuth}
              />
              <AuthCard
                icon={<img src={AdvertiserIcon} alt="广告主授权" className="w-24" />}
                title="广告主授权"
                description="适用于直接投放广告的企业主；登录【巨量引整工作台】设置-查看认证主体信息，若已认证为品牌的主体，则说明为广告主角色"
                buttonText="开始广告主授权"
                onClick={handleAdvertiserAuth}
              />
            </div>
            <div className="text-center text-lg text-red-500">
              {`点击"开始代理商授权/开始广告主授权",请在15min内完成授权,否则会失效。失效后请重新点击按钮,生成新的授权链接。`}
            </div>
          </div>
        )}
        <div className="absolute bottom-8 flex w-[98%] items-center justify-between rounded border border-[#E8E8E8] p-4">
          <div className="flex items-center gap-4">
            <img src={DocumentIcon} alt="授权记录" className="w-10" />
            <div className="flex flex-col">
              <span className="text-base font-medium text-black">授权记录</span>
              <span className="text-base text-[#666666]">查看授权的历史记录</span>
            </div>
          </div>
          <Button
            type="link"
            className="flex items-center gap-1 p-0 text-[#1E5EFF]"
            onClick={() => {
              navigate(
                `/project/${projectKey}/${industryType}/authorization/authorization-history`,
              );
            }}
            icon={<RightOutlined />}
          >
            前往查看
          </Button>
        </div>
      </div>
    </PageContainer>
  );
}
