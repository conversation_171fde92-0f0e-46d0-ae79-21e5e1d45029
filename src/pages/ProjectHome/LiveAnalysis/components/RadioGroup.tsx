import { useState, useEffect } from 'react';
import { styled } from 'styled-components';

export type RadioOption = {
  value: string;
  label: string;
  percent?: string;
};

export const RadioGroupContainer = styled.div`
  display: flex;
  border-radius: 8px;
  padding: 4px;
  gap: 4px;
  flex-wrap: wrap;
`;

export const RadioInput = styled.input`
  display: none;
`;

export const RadioLabel = styled.label`
  background-color: transparent;
  border: none;
  border-radius: 4px;
  padding: 4px 6px;
  cursor: pointer;
  transition: all 0.3s;

  font-size: 14px;

  ${RadioInput}:checked + & {
    background-color: #1e5eff;
    color: white;
    transition: all 0.3s;
  }
`;

export const RadioGroupLabel = styled.label`
  background-color: transparent;
  border: none;
  border-radius: 4px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  flex-direction: row;

  justify-content: space-between;
  width: 100%;
  border: 1px solid;
  color: #0e1015;
  font-weight: 500;
  font-size: 14px;
  margin-bottom: 16px;
  white-space: nowrap;

  ${RadioInput}:checked + & {
    background-color: rgba(234, 240, 255, 1);
    border: 1px solid #1e5eff;
    color: #1e5eff;
    transition: all 0.3s;
    /* background-color: #eaf0ff; */
  }
`;

export const RadioGroup = ({
  defaultValue,
  options,
  onChange,
  value,
  showValue = true,
}: {
  options: RadioOption[];
  defaultValue?: string;
  onChange: (value: string) => void;
  value?: string;
  showValue?: boolean;
}) => {
  const [selectedRadioGroupValue, setSelectedRadioGroupValue] = useState(
    value || defaultValue || options?.[0]?.label || '',
  );
  useEffect(() => {
    setSelectedRadioGroupValue(value || defaultValue || options?.[0]?.label || '');
  }, [options, value, defaultValue]);

  const handleChange = (value: string) => {
    setSelectedRadioGroupValue(value);
    onChange(value);
  };

  return (
    <RadioGroupContainer>
      {options?.map((option) => (
        <div key={`group-${option.label}-${option.value}`}>
          <RadioInput
            type="radio"
            id={`group-${option.label}`}
            name="carModel"
            value={option.label}
            checked={selectedRadioGroupValue === option.label}
            onChange={() => handleChange(option.label)}
          />
          <RadioLabel
            htmlFor={`group-${option.label}`}
            className="text-color-[#64666b] hover:bg-[#eaf0ff]"
          >
            <span style={{ color: selectedRadioGroupValue === option.label ? '#fff' : '#37393E' }}>
              {option.label}
            </span>
            <span style={{ color: selectedRadioGroupValue === option.label ? '#fff' : '#95979C' }}>
              {showValue && `（${option.value}）`}
            </span>
          </RadioLabel>
        </div>
      ))}
    </RadioGroupContainer>
  );
};
