import { Row, Col } from 'antd';
import ReactECharts, { EChartsOption } from 'echarts-for-react';
import { echartDefaultColor } from '@/utils/commonStyle';

import styles from '../style.module.less';
import { Anchor, CarVersion, Content, LiveRoom } from '@/services/ai-live/statistic';
import { useRef } from 'react';

interface OverviewChartsProps {
  darkMode?: boolean;
  carVersion: CarVersion[];
  content: Content[];
  liveRoom: LiveRoom[];
  anchor: Anchor[];
  roomTotal: number;
}

export const OverviewCharts = (props: OverviewChartsProps) => {
  const { darkMode, carVersion, content, liveRoom, anchor, roomTotal } = props;
  const myChart = useRef<ReactECharts | null>(null);

  const commonLegend = {
    icon: 'circle',
    top: 354,
    itemWidth: 12,
    itemHeight: 12,
    padding: 0,
    itemGap: 20,
  };

  const commonPieSerie: EChartsOption = {
    type: 'pie',
    left: 'center',
    radius: ['70', '110'],
    center: ['50%', '35%'], // 设置饼图的圆心
    color: ['#73A0FA', ...echartDefaultColor],
    stillShowZeroSum: false,
    // avoidLabelOverlap: false,
    width: 480,
    label: {
      formatter: function (params: any) {
        const value = params.value;
        const percent = ((value / roomTotal) * 100).toFixed(2);
        return `${params.name} ${percent}%`;
      },
      // alignTo: 'edge',
    },
    labelLine: {
      normal: {
        show: true,
        legend: 8,
        legend2: 15,
      },
    },
  };

  const barrageTypeChartOption = {
    title: {
      textStyle: {
        color: 'grey',
        fontSize: 20,
      },
      text: '暂无数据',
      left: 'center',
      top: '33%',
      show: liveRoom.length === 0,
    },
    tooltip: {
      trigger: 'item',
      formatter: function (params: any) {
        const value = params.value;
        const name = params.data.name;
        const percent = ((value / roomTotal) * 100).toFixed(2);
        return `<div>
        <div>${name}</div>
        <div>直播间数：${value}</div>
        <div>直播占比：${percent}%</div>
        </div>`;
      },
    },
    series: [
      {
        ...commonPieSerie,
        data: liveRoom.map((item) => ({
          name: item.key,
          value: item.value,
        })),
      },
    ],
    legend: {
      ...commonLegend,
      formatter: function (name: string) {
        return `${name} ${liveRoom.find((item) => item.key === name)?.value || 0}场`;
      },
      textStyle: {
        color: 'gray',
      },
    },
  };

  const carsTypeChartOption = {
    title: {
      textStyle: {
        color: 'grey',
        fontSize: 20,
      },
      text: '暂无数据',
      left: 'center',
      top: '33%',
      show: anchor.length === 0,
    },
    tooltip: {
      trigger: 'item',
      formatter: function (params: any) {
        const value = params.value;
        const percent = ((value / roomTotal) * 100).toFixed(2);
        const name = params.data.name;
        return `<div>
        <div>${name}</div>
        <div>直播间数：${value}</div>
        <div>直播占比：${percent}%</div>
        </div>`;
      },
    },
    series: [
      {
        ...commonPieSerie,
        emphasis: {
          scale: true,
          scaleSize: 2,
        },
        data: anchor.map((item) => ({
          name: item.key,
          value: item.value,
        })),
      },
    ],
    legend: {
      ...commonLegend,
      formatter: function (name: string) {
        return `${name} ${anchor.find((item) => item.key === name)?.value || 0}场`;
      },
      textStyle: {
        color: 'gray',
      },
    },
  };
  const carVersionOption = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'line',
      },
      formatter: function (params: any) {
        return `<div>
        <div>${params[0].name}</div>
        <div>直播间数：${params[0].value}</div>
        <div>直播占比：${((params[0].value / roomTotal) * 100).toFixed(2)}%</div>
        </div>`;
      },
    },
    xAxis: {
      type: 'category',
      data: carVersion?.map((item) => item.key),
      axisLabel: {
        rotate: 45,
      },
    },
    yAxis: {
      type: 'value',
      max: Math.max(...carVersion.map((item) => Number(item.value))),
    },
    series: [
      {
        data: carVersion?.map((item) => item.value),
        color: '#73A0FA',
        type: 'bar',
      },
    ],
  };
  const contentOption = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'line',
      },
      formatter: function (params: any) {
        return `<div>
        <div>${params[0].name}</div>
        <div>直播间数：${params[0].value}</div>
        <div>直播占比：${((params[0].value / roomTotal) * 100).toFixed(2)}%</div>
        </div>`;
      },
    },
    xAxis: {
      type: 'category',
      data: content?.map((item) => item.key),
      axisLabel: {
        rotate: 45,
      },
    },
    yAxis: {
      type: 'value',
      max: Math.max(...content.map((item) => Number(item.value))),
    },
    series: [
      {
        data: content?.map((item) => item.value),
        color: '#73A0FA',
        type: 'bar',
      },
    ],
  };

  return (
    <>
      <Row gutter={{ xs: 28, sm: 28, md: 28, lg: 32 }}>
        <Col span={12}>
          <div
            className={
              darkMode
                ? [styles.barrageOverviewChartWrap, styles.dark].join(' ')
                : styles.barrageOverviewChartWrap
            }
          >
            <div className={styles.chartTitle}>车型分布</div>
            <div>
              <ReactECharts
                style={{ width: '100%', height: '412px' }}
                option={carVersionOption}
                notMerge={true}
              />
            </div>
          </div>
        </Col>
        <Col span={12}>
          <div
            className={
              darkMode
                ? [styles.barrageOverviewChartWrap, styles.dark].join(' ')
                : styles.barrageOverviewChartWrap
            }
          >
            <div className={styles.chartTitle}>内容分布</div>
            <div>
              <ReactECharts
                style={{ width: '100%', height: '412px' }}
                option={contentOption}
                notMerge={true}
              />
            </div>
          </div>
        </Col>
        <Col span={12}>
          <div
            className={
              darkMode
                ? [styles.barrageOverviewChartWrap, styles.dark].join(' ')
                : styles.barrageOverviewChartWrap
            }
          >
            <div className={styles.chartTitle}>直播间环境</div>
            <div>
              <ReactECharts
                ref={myChart}
                style={{ width: '100%', height: '412px' }}
                option={barrageTypeChartOption}
                notMerge={true}
              />
            </div>
          </div>
        </Col>
        <Col span={12}>
          <div
            className={
              darkMode
                ? [styles.barrageOverviewChartWrap, styles.dark].join(' ')
                : styles.barrageOverviewChartWrap
            }
          >
            <div className={styles.chartTitle}>主播出镜情况</div>
            <div>
              <ReactECharts
                style={{ width: '100%', height: '412px' }}
                option={carsTypeChartOption}
                notMerge={true}
              />
            </div>
          </div>
        </Col>
      </Row>
    </>
  );
};
