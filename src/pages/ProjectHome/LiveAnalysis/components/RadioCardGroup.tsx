import React, { useEffect, useState } from 'react';
import { Progress } from 'antd';
import { RadioGroupContainer, RadioGroupLabel, RadioInput, RadioOption } from './RadioGroup';

export const RadioCardGroup = ({
  options = [],
  onChange,
  value = '',
  defaultValue = '',
}: {
  options: RadioOption[];
  onChange: (value: string) => void;
  value: string;
  defaultValue?: string;
}) => {
  const [selectedValue, setSelectedValue] = useState(value);

  useEffect(() => {
    setSelectedValue(value || defaultValue || options?.[0]?.value);
  }, [value, defaultValue, options]);

  const handleChange = (value: string) => {
    setSelectedValue(value);
    onChange(value);
  };

  return (
    <RadioGroupContainer style={{ paddingRight: '10px' }}>
      {options.map((option) => (
        <React.Fragment key={`${option.value}-${option.label}`}>
          <RadioInput
            type="radio"
            id={`group-card--${option.label}-${option.value}`}
            name="carModelGroup"
            value={option.label}
            checked={selectedValue === option.label}
            onChange={() => handleChange(option.label)}
          />
          <RadioGroupLabel
            htmlFor={`group-card--${option.label}-${option.value}`}
            style={{ color: '#64666b', backgroundColor: '#dcdde1' }}
          >
            <div
              style={{
                display: 'flex',
                justifyContent: 'space-between',
                flexDirection: 'column',
              }}
            >
              <div>{option.label}</div>
              <div>直播间数：{option.value}</div>
            </div>
            <Progress
              type="circle"
              percent={Number(option.percent)}
              size={60}
              trailColor="#F9FCFF"
              status="normal"
              format={(percent) => `${percent}%`}
            />
          </RadioGroupLabel>
        </React.Fragment>
      ))}
    </RadioGroupContainer>
  );
};
