import { useEffect, useMemo, useState } from 'react';
import { Pagination, Flex, Space, Timeline, PaginationProps, Empty, Button } from 'antd';
import styles from '../style.module.less';
import { ProCard } from '@ant-design/pro-components';
import { styled } from 'styled-components';
import {
  getStatisticContent,
  IStatisticVersionResponse,
  StatisticContentParams,
} from '@/services/ai-live/statistic';
import { RadioGroup } from './RadioGroup';
import { RadioCardGroup } from './RadioCardGroup';
import useProjectId from '@/hooks/useProjectId';
import { useQuery, Link, useParams } from '@umijs/max';
import { PageBasicParams } from '@/services/common';
import { updateLocalStorage } from '@/utils/quality';

export const LiveStreamSummaryContainer = styled.div`
  .content-box {
    position: relative;

    margin-top: 10px;
    padding: 10px;
    border-radius: 4px;

    .title {
      font-size: 14px;
      font-weight: 400;
      margin-bottom: 5px;
    }

    .summary {
      font-size: 14px;
      font-weight: 500;
      margin-top: 5px;
    }
  }
`;

function useLiveAnalysisContent(
  params: PageBasicParams,
  queryData: Partial<StatisticContentParams>,
) {
  const projectId = useProjectId();
  const { data } = useQuery({
    queryKey: [
      'liveAnalysisContentData',
      queryData.liveStartDate,
      queryData.liveEndDate,
      queryData.contentType,
      params.size,
      params.page,
      queryData.carVersion,
    ],
    queryFn: () => getStatisticContent(params, { ...queryData, projectId: Number(projectId) }),
    enabled:
      !!projectId &&
      !!queryData.liveStartDate &&
      !!queryData.liveEndDate &&
      !!queryData.contentType &&
      !!params.size &&
      !!params.page &&
      !!queryData.carVersion,
  });
  return { data: data?.data?.items, total: data?.data?.total };
}

interface BarrageListProps {
  darkMode?: boolean;
  versionData: IStatisticVersionResponse[];
  teamCodeList: string[];
  rangeTime: string[];
}

export const LiveAnalysisList = (props: BarrageListProps) => {
  const { darkMode, versionData, rangeTime, teamCodeList } = props;
  const { industryType } = useParams();
  // 车型
  const [selectedRadioGroupValue, setSelectedRadioGroupValue] = useState('');
  // 内容类型
  const [selectedCardGroupValue, setSelectedCardGroupValue] = useState('');

  const [queryParams, setQueryParams] = useState({
    size: 10,
    page: 1,
  });
  const projectId = useProjectId();
  const { data: contentData, total: contentTotal } = useLiveAnalysisContent(queryParams, {
    projectId: Number(projectId),
    contentType: selectedCardGroupValue,
    liveStartDate: rangeTime[0],
    liveEndDate: rangeTime[1],
    carVersion: selectedRadioGroupValue,
    teamCodeList: teamCodeList,
  });
  const options = useMemo(() => {
    return versionData?.map((item) => ({
      label: item.carVersion,
      value: String(item.cnt),
    }));
  }, [versionData]);

  useEffect(() => {
    setSelectedRadioGroupValue(versionData?.[0]?.carVersion);
  }, [versionData]);

  const cardOptions = useMemo(() => {
    const carVersion = versionData?.find((item) => item.carVersion === selectedRadioGroupValue);
    const contents = carVersion?.contents;
    return contents?.map((item) => ({
      label: item.contentType,
      value: item.cnt,
      percent: ((Number(item.cnt) / Number(carVersion?.cnt)) * 100).toFixed(1),
    }));
  }, [versionData, selectedRadioGroupValue]);

  useEffect(() => {
    if (cardOptions?.[0]?.label) {
      setSelectedCardGroupValue(cardOptions?.[0]?.label);
    }
  }, [cardOptions]);

  // 切换车型
  const handleChange = (value: string) => {
    setSelectedRadioGroupValue(value);
    setQueryParams({ ...queryParams, page: 1 });
  };

  // 切换内容类型
  const handleCardChange = (value: string) => {
    setSelectedCardGroupValue(value);
    setQueryParams({ ...queryParams, page: 1 });
  };
  const onShowSizeChange: PaginationProps['onShowSizeChange'] = (current, pageSize) => {
    setQueryParams({ ...queryParams, page: current, size: pageSize });
  };
  if (versionData.length === 0) {
    return <Empty description="暂无数据" />;
  }
  return (
    <>
      <Flex align="center">
        <div className="text-nowrap">直播间数：</div>
        <Space className={darkMode ? styles.barrageListFiltersDark : undefined}>
          <RadioGroup
            options={options || []}
            onChange={handleChange}
            value={selectedRadioGroupValue}
          />
        </Space>
      </Flex>
      <ProCard
        ghost
        split="vertical"
        style={{
          marginTop: '10px',
          border: 'none',
        }}
        className={darkMode ? [styles.barrageList, styles.dark].join(' ') : styles.barrageList}
      >
        <ProCard ghost colSpan={'230px'}>
          <RadioCardGroup
            options={cardOptions || []}
            onChange={handleCardChange}
            value={selectedCardGroupValue}
          />
        </ProCard>

        <ProCard>
          <Flex vertical gap={20} style={{ padding: '10px' }}>
            <Timeline
              items={contentData?.map((item) => ({
                children: (
                  <LiveStreamSummaryContainer>
                    <div className="timestamp" style={{ color: '#95979C' }}>
                      {item.startTime}-{item.endTime}
                    </div>
                    <Flex
                      justify="space-between"
                      className="content-box"
                      align="center"
                      style={{ backgroundColor: '#1e5eff05', color: '#0e1015' }}
                    >
                      <div>
                        <div className="title">{item.roomTitle}</div>
                        <div style={{ fontSize: 12 }}>
                          <span style={{ color: '#0E1015' }}>账号名称：</span>
                          <span style={{ color: '#64666B' }}>{item.accountName}</span>
                          &nbsp;&nbsp;&nbsp;&nbsp;
                          <span style={{ color: '#0E1015' }}>经销商名称：</span>
                          <span style={{ color: '#64666B' }}>{item.teamName}</span>
                        </div>
                        <div className="summary">{item.contentSummary}</div>
                      </div>

                      <Link to={`/live/detail/${item.roomId}/1/${industryType}`} target="_blank">
                        <Button
                          type="link"
                          onClick={() => {
                            updateLocalStorage('liveDetailRecords', {
                              headerActiveKey: '100006',
                            });
                          }}
                        >
                          查看详情
                        </Button>
                      </Link>
                    </Flex>
                  </LiveStreamSummaryContainer>
                ),
              }))}
            />
            <Flex justify="end">
              <Pagination
                className={
                  darkMode
                    ? [styles.barrageListPagination, styles.dark].join(' ')
                    : styles.barrageListPagination
                }
                size="small"
                style={{ padding: '15px 0' }}
                pageSizeOptions={[10, 20, 30]}
                defaultPageSize={queryParams.size}
                total={contentTotal}
                onChange={(page, pageSize) => {
                  setQueryParams({ ...queryParams, page, size: pageSize });
                }}
                onShowSizeChange={onShowSizeChange}
                showSizeChanger
              />
            </Flex>
          </Flex>
        </ProCard>
      </ProCard>
    </>
  );
};
