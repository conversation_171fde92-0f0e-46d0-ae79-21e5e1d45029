import { DateType } from '@/services/business';
import { getTimeByTypeBusiness } from '@/utils/time';
import { PageContainer, ProCard } from '@ant-design/pro-components';
import { useSticky } from '@reactuses/core';
import { useState, useRef } from 'react';
import { AnalysisDetailFilters } from './components/LiveAnalysisAnalysisDetailFilters';
import { LiveAnalysisList } from './components/LiveAnalysisList';
import { OverviewCharts } from './components/LiveAnalysisOverviewCharts';
import { useQuery } from '@umijs/max';
import { getStatistic, getStatisticVersion } from '@/services/ai-live/statistic';
import useProjectId from '@/hooks/useProjectId';
import { withAuth } from '@/hoc/withAuth';
import { FunctionCode } from '@/services/system';
import BreadCrumbSimple from '@/components/ui/breadcrumbSimple';

function useLiveAnalysisData(params: {
  liveStartDate: string;
  liveEndDate: string;
  teamCodeList: string[];
}) {
  const projectId = useProjectId();
  const { data } = useQuery({
    queryKey: ['liveAnalysisData', params.liveStartDate, params.liveEndDate, params.teamCodeList],
    queryFn: () => getStatistic({ projectId, ...params }),
    enabled: !!projectId,
  });
  return { data: data?.data };
}

function useLiveAnalysisVersionVersion(params: {
  liveStartDate: string;
  liveEndDate: string;
  teamCodeList: string[];
}) {
  const projectId = useProjectId();
  const { data } = useQuery({
    queryKey: [
      'liveAnalysisVersionData',
      params.liveStartDate,
      params.liveEndDate,
      params.teamCodeList,
    ],
    queryFn: () => getStatisticVersion({ projectId, ...params }),
    enabled: !!projectId,
  });
  return { data: data?.data };
}

function LiveAnalysis() {
  const headerRef = useRef(null);

  const [isSticky] = useSticky(headerRef, { nav: 56 });
  const [radioValue, setRadioValue] = useState<DateType>(DateType.Yesterday);
  const [rangeTime, setRangeTime] = useState<string[]>(getTimeByTypeBusiness(DateType.Yesterday));
  const [treeValue, setTreeValue] = useState<string[] | undefined>(undefined);

  const { data } = useLiveAnalysisData({
    liveStartDate: rangeTime[0],
    liveEndDate: rangeTime[1],
    teamCodeList: treeValue || [],
  });

  const { data: versionData } = useLiveAnalysisVersionVersion({
    liveStartDate: rangeTime[0],
    liveEndDate: rangeTime[1],
    teamCodeList: treeValue || [],
  });

  return (
    <PageContainer title={<BreadCrumbSimple breadcrumbs={['智能分析', '直播分析']} />}>
      <ProCard
        boxShadow={isSticky ? true : false}
        ref={headerRef}
        colSpan={{ md: 24, lg: 18 }}
        style={{ position: 'sticky', top: 56, zIndex: 99 }}
      >
        <AnalysisDetailFilters
          radioValue={radioValue}
          setRadioValue={setRadioValue}
          rangeTime={rangeTime}
          setRangeTime={setRangeTime}
          treeValue={treeValue}
          setTreeValue={setTreeValue}
        />
      </ProCard>

      <ProCard
        style={{ marginTop: 20 }}
        title="直播间总览"
        extra={
          <div
            style={{
              color: '#0E1015',
              fontWeight: '500',
            }}
          >
            直播间总数：
            <span style={{ color: '#1E5EFF' }}>{data?.roomTotal || 0}</span>
          </div>
        }
      >
        <OverviewCharts
          carVersion={data?.carVersion || []}
          content={data?.content || []}
          liveRoom={data?.liveRoom || []}
          anchor={data?.anchor || []}
          roomTotal={data?.roomTotal || 0}
        />
      </ProCard>

      <ProCard style={{ marginTop: 20, position: 'relative' }} title="车型分析">
        <LiveAnalysisList
          versionData={versionData || []}
          rangeTime={rangeTime}
          teamCodeList={treeValue || []}
        />
      </ProCard>
    </PageContainer>
  );
}

export default withAuth(LiveAnalysis, FunctionCode.LiveAnalysis);
