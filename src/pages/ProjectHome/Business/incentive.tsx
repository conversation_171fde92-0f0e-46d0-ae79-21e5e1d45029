import { SvgIcon } from '@/components/SvgIcon';
import BreadCrumbSimple from '@/components/ui/breadcrumbSimple';
import useProjectId from '@/hooks/useProjectId';
import { ActivityDelete, ActivityPageItem, GetActivityPage } from '@/services/activity';
import { AccountPlatformEnum, PlatForm } from '@/utils/platform';
import { proTableRequestAdapter } from '@/utils';
import { STICKY_OFFSETHEADER } from '@/utils/common';
import {
  proTableOptionsConfig,
  proTablePaginationConfig,
  proTableSearchConfig,
} from '@/utils/proTableConfig';
import { getSumColumnsWidth } from '@/utils/table';
import { CloseOutlined, PlusCircleOutlined } from '@ant-design/icons';
import {
  ActionType,
  PageContainer,
  ProCard,
  ProColumns,
  ProFormInstance,
  ProTable,
  TableDropdown,
} from '@ant-design/pro-components';
import { Link, useAccess, useParams } from '@umijs/max';
import { Button, message, Modal, Space } from 'antd';
import { useRef, useState } from 'react';
import CopyIncentiveModal from './component/copyIncentiveModal';
import IncentiveDetail from './component/incentiveDetail';
import { EllipsisVertical } from 'lucide-react';

const Incentive = () => {
  const projectId = useProjectId();
  const actionRef = useRef<ActionType>();
  const formRef = useRef<ProFormInstance>();
  const params = useParams();
  const access = useAccess();
  const [isEmptyTable, setIsEmptyTable] = useState(false);

  const handleDeleteActivity = (
    activityId: string | number,
    activityStatus: number,
    action: any,
  ) => {
    Modal.confirm({
      title: '警示',
      content: (
        <>
          <span style={{ color: '#1C1F2399' }}>您是否确认删除该激励活动</span>
          {activityStatus == 2 ? (
            <div style={{ color: '#F54848', fontSize: '12px' }}>注意：该激励活动正在进行中</div>
          ) : null}
        </>
      ),
      closeIcon: <CloseOutlined />,
      closable: true,
      icon: <SvgIcon icon="local:outline/warning" vertical="middle" stroke="none" y={3} x={-5} />,
      maskClosable: true,
      async onOk() {
        const res = await ActivityDelete({ activityId });
        res.code === 0 ? message.success('删除成功') : message.error('删除失败');
        action?.reload();
      },
    });
  };

  const columns: ProColumns<ActivityPageItem>[] = [
    {
      title: '序号',
      dataIndex: 'serialNumber',
      hideInSearch: true,
      width: 50,
      align: 'left',
    },
    {
      title: '激励名称',
      dataIndex: 'activityName',
      width: 120,
      align: 'left',
      fieldProps: {
        placeholder: '请输入激励活动名称',
      },
      formItemProps: {
        label: null,
      },
    },
    {
      title: '参与平台',
      dataIndex: 'platformList',
      align: 'left',
      width: 100,
      hideInSearch: true,
      valueEnum: {
        [PlatForm.Douyin]: { text: '抖音' },
        [PlatForm.XiaoHongShu]: { text: '小红书' },
        [PlatForm.WXVideo]: { text: '视频号' },
      },
      render: (_, record) => {
        return record.platformList
          ?.map((p) => AccountPlatformEnum[p as keyof typeof AccountPlatformEnum]?.text)
          .filter(Boolean)
          .join('、');
      },
    },
    {
      title: '活动状态',
      width: 80,
      align: 'left',
      dataIndex: 'activityStatus',
      valueType: 'select',
      fieldProps: {
        placeholder: '活动状态',
      },
      formItemProps: {
        label: null,
      },
      valueEnum: {
        1: { text: '未开始' },
        2: { text: '进行中' },
        3: { text: '已结束' },
      },
    },
    {
      title: '数据状态',
      width: 80,
      align: 'left',
      dataIndex: 'dataStatus',
      valueType: 'select',
      fieldProps: {
        placeholder: '数据状态',
      },
      formItemProps: {
        label: null,
      },
      valueEnum: {
        1: { text: '正常' },
        2: { text: '重跑中' },
      },
    },
    {
      title: '开始时间',
      dataIndex: 'activityStartTime',
      width: 160,
      align: 'left',
      hideInSearch: true,
    },
    {
      title: '结束时间',
      dataIndex: 'activityEndTime',
      width: 160,
      align: 'left',
      hideInSearch: true,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 160,
      align: 'left',
      valueType: 'dateRange',
      fieldProps: {
        placeholder: ['创建时间', '创建时间'],
      },
      search: {
        transform: (value) => {
          return { startTime: value?.[0], endTime: value?.[1] };
        },
      },
      render: (_, record) => {
        return <span>{record.createTime}</span>;
      },
    },
    {
      title: '操作',
      dataIndex: 'options',
      align: 'left',
      hideInSearch: true,
      hideInTable: false,
      fixed: 'right',
      width: 180,
      render: (_, record, index, action) => {
        return (
          <Space>
            <IncentiveDetail activityId={record.id} />
            {access.isInsideAccount && (
              <Link
                to={`/project/${params?.projectKey}/${params?.industryType}/business/${record.id}/edit-incentive`}
                key={`${record.id}-edit`}
                reloadDocument
              >
                编辑
              </Link>
            )}
            <Link
              to={`/project/${params?.projectKey}/${params?.industryType}/business/${record.id}/rank-incentive?name=${record.activityName}`}
              target="_blank"
              key={`${record.id}-rank`}
            >
              排名
            </Link>
            {access.isInsideAccount && (
              <TableDropdown
                key={`${record.id}-more`}
                menus={[
                  {
                    key: '1',
                    name: <CopyIncentiveModal record={record} action={action} />,
                  },
                  {
                    key: '2',
                    name: (
                      <span
                        onClick={() =>
                          handleDeleteActivity(record.id, record.activityStatus, action)
                        }
                      >
                        删除
                      </span>
                    ),
                  },
                ]}
              >
                <div style={{ width: '20px' }}>
                  <EllipsisVertical size={16} onClick={(e) => e.preventDefault()} />
                </div>
              </TableDropdown>
            )}
          </Space>
        );
      },
    },
  ];

  return (
    <PageContainer
      title={<BreadCrumbSimple breadcrumbs={['商务政策管理', '激励活动']} />}
      extra={
        access.isInsideAccount && (
          <Link
            to={`/project/${params?.projectKey}/${params?.industryType}/business/create-incentive`}
            reloadDocument
          >
            <Button type="primary" style={{ transform: 'translate(0px,-5px)' }}>
              <PlusCircleOutlined />
              添加激励方案
            </Button>
          </Link>
        )
      }
    >
      <ProCard>
        <ProTable<ActivityPageItem>
          columns={columns}
          actionRef={actionRef}
          formRef={formRef}
          ghost
          sticky={{ offsetHeader: STICKY_OFFSETHEADER }}
          params={{ projectId }}
          tableClassName="custom-table"
          scroll={{ x: isEmptyTable ? getSumColumnsWidth(columns) : 'max-content' }}
          postData={(data: ActivityPageItem[]) => {
            // 判断表格数据是否为空
            data.length === 0 ? setIsEmptyTable(true) : setIsEmptyTable(false);
            return data;
          }}
          request={(params, sorter, filter) => {
            return proTableRequestAdapter(params, sorter, filter, GetActivityPage);
          }}
          search={{ ...proTableSearchConfig }}
          options={{ ...proTableOptionsConfig }}
          pagination={{ ...proTablePaginationConfig }}
          rowKey="id"
          dateFormatter="string"
          columnsState={{
            persistenceKey: 'Incentive-Table-Columns',
            persistenceType: 'localStorage',
          }}
        />
      </ProCard>
    </PageContainer>
  );
};

export default Incentive;
