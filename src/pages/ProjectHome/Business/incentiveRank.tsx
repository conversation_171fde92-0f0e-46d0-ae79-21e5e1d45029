import useTabKeySearchParams from '@/hooks/useTabKeySearchParams';
import { GetActivityRecord } from '@/services/activity';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import { useLocation, useParams } from '@umijs/max';
import { useAsyncEffect } from 'ahooks';
import { Space, Tabs, TabsProps, Tooltip } from 'antd';
import { useState } from 'react';
import DetailRank from './component/detailRank';
import SummaryRank from './component/summaryRank';

const IncentiveRank = () => {
  const [activeKey, onTabChange] = useTabKeySearchParams('summary');
  const { incentiveId } = useParams();
  const [recordTime, setReacordTime] = useState<string | undefined>(undefined);
  const location = useLocation();
  const query = new URLSearchParams(location.search);

  const items: TabsProps['items'] = [
    {
      label: '汇总',
      key: 'summary',
      children: <SummaryRank />,
    },
    {
      label: '明细',
      key: 'detail',
      children: <DetailRank />,
    },
  ];

  useAsyncEffect(async () => {
    const res = await GetActivityRecord({ activityId: incentiveId });
    setReacordTime(res.data?.[0]?.produceTime);
  });

  return (
    <PageContainer
      title={
        <>
          <span style={{ color: '#4a4a4b' }}>商务政策管理/激励活动</span>
          <span>/排名({query.get('name')})</span>
        </>
      }
      extra={
        <span style={{ color: '#979797', fontSize: '12px' }}>
          <Space>
            <span>系统数据最近更新于: {recordTime}</span>
            <Tooltip title="排名数据的刷新以D+1进行，第二天的数据更新为前一天的数据">
              <QuestionCircleOutlined />
            </Tooltip>
          </Space>
        </span>
      }
    >
      <Tabs activeKey={activeKey} onChange={onTabChange} items={items} className="horizontal-tab" />
    </PageContainer>
  );
};

export default IncentiveRank;
