import { PlatForm } from '@/utils/platform';
import { CustomInputNumber } from '@/utils/commonStyle';
import { MinusCircleOutlined, PlusCircleOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import { ProCard, ProFormDependency, ProFormItem, ProFormSelect } from '@ant-design/pro-components';
import { Empty, Flex, Form, message, Space, Tooltip } from 'antd';
import { groupBy } from 'lodash-es';
import React from 'react';
import { RuleType } from '../incentiveForm';

type RuleConfig = {
  label: string;
  unit: string;
};

type RuleFormProps = {
  formName: string;
  headerTitle: string;
  ruleTypeConfig: {
    title: string;
    tooltip: string;
    type: number;
    rangeConfig?: RuleConfig;
    mulConfig?: RuleConfig;
    fieldName: string;
  }[];
};

function checkOverlap(arr: any[]) {
  // 对数组按照 start 值进行升序排序
  arr.sort((a, b) => a.start - b.start);
  for (let i = 0; i < arr.length; i++) {
    for (let j = i + 1; j < arr.length; j++) {
      if (arr[j] !== null && arr[i].end > arr[j].start && arr[i].start < arr[j].end) {
        return true;
      }
    }
  }
  return false;
}

const RuleForm = (props: RuleFormProps) => {
  const { formName, headerTitle, ruleTypeConfig } = props;
  const addRefs: React.RefObject<(defaultValue?: any, insertIndex?: number | undefined) => void>[] =
    ruleTypeConfig.map(() => React.createRef());

  const renderInputRange = (
    name: number,
    type: number,
    ruleData: RuleType[],
    config?: RuleConfig,
  ) => {
    // 校验是否存在重叠区间
    const checkIntersection = () => {
      const ruleDataFilterNull = ruleData.filter(Boolean);
      const groupByAccountType = groupBy(ruleDataFilterNull, 'accountType');
      Object.values(groupByAccountType).forEach((item) => {
        const checkOverlapRes = checkOverlap(item);
        checkOverlapRes && message.error('同一指标的计算数据不可重叠');
      });
      return Promise.resolve();
    };

    switch (type) {
      case 6:
        return (
          <>
            <span>私信回复占比≥</span>
            <ProFormItem name={[name, 'percentage']} rules={[{ required: true }]} noStyle>
              <CustomInputNumber
                defaultValue={0}
                precision={2}
                size="middle"
                style={{ minWidth: '70px' }}
              />
            </ProFormItem>
            <span>%</span>
          </>
        );
      case 7:
        return (
          <>
            <span>3分钟回复率≥</span>
            <ProFormItem name={[name, 'efficiency']} rules={[{ required: true }]} noStyle>
              <CustomInputNumber
                size="middle"
                precision={2}
                style={{ minWidth: '70px' }}
                defaultValue={0}
              />
            </ProFormItem>
            <span>%</span>
          </>
        );
      default:
        return (
          <>
            <span>{config?.label}</span>
            <ProFormItem name={[name, 'start']} rules={[{ required: true }]} noStyle>
              <CustomInputNumber
                size="middle"
                precision={config?.unit === '小时' ? 1 : 0}
                step={config?.unit === '小时' ? 0.1 : 1}
                style={{ minWidth: '70px' }}
                defaultValue={0}
              />
            </ProFormItem>
            <span>至</span>
            <ProFormItem
              name={[name, 'end']}
              rules={[
                { required: true },
                {
                  validator: checkIntersection,
                  validateTrigger: ['onChange', 'onBlur'],
                },
              ]}
              noStyle
            >
              <CustomInputNumber
                size="middle"
                precision={config?.unit === '小时' ? 1 : 0}
                step={config?.unit === '小时' ? 0.1 : 1}
                style={{ minWidth: '70px' }}
                defaultValue={0}
              />
            </ProFormItem>
            <span>{config?.unit},</span>
          </>
        );
    }
  };

  const renderMultiplicationInput = (name: number, config?: RuleConfig) => {
    return (
      <>
        <span>
          {config?.label}X{config?.unit},
        </span>
        <span>得分=X * </span>
        <ProFormItem name={[name, 'mulPoint']} rules={[{ required: true }]} noStyle>
          <CustomInputNumber
            size="middle"
            precision={3}
            style={{ minWidth: '70px' }}
            defaultValue={0}
            min={0}
          />
        </ProFormItem>
        <span>分,</span>
      </>
    );
  };

  return (
    <ProCard
      style={{
        background: '#FBFCFE',
        maxWidth: '980px',
        minWidth: '980px',
        margin: '30px 0px',
        padding: '20px',
      }}
      ghost
    >
      <h3>{headerTitle}</h3>
      {ruleTypeConfig.map((item, index) => {
        return (
          <div key={`ruleform-${item.type}`}>
            <Flex justify="space-between" style={{ marginBlock: '10px' }}>
              <Space>
                {item.title}
                <Tooltip title={item.tooltip}>
                  <QuestionCircleOutlined style={{ color: 'rgba(0, 0, 0, 0.45)' }} />
                </Tooltip>
              </Space>
              <div>
                <ProFormDependency name={[[formName, index]]}>
                  {(value) => {
                    const ruleData = value[item.fieldName][index];
                    return (
                      <Space
                        style={{ color: '#2160F9' }}
                        onClick={() =>
                          addRefs[index].current?.({
                            accountType: '1',
                            calcType: ruleData.length > 0 ? ruleData[0]?.calcType : '1',
                          })
                        }
                      >
                        <PlusCircleOutlined />
                        添加规则
                      </Space>
                    );
                  }}
                </ProFormDependency>
              </div>
            </Flex>
            <Form.List name={[formName, index]}>
              {(fields, { add, remove }, { errors }) => {
                // @ts-ignore
                addRefs[index].current = add;
                if (fields.length > 0) {
                  return (
                    <>
                      {fields.map(({ key, name, ...restField }) => (
                        <ProFormDependency key={key} name={[[formName, index]]}>
                          {(value) => {
                            const ruleData = value[item.fieldName][index];
                            return (
                              <ProFormItem {...restField} key={key} noStyle>
                                <div
                                  style={{
                                    background: '#fff',
                                    borderRadius: '4px',
                                    padding: '5px',
                                    width: 'fit-content',
                                    marginBlock: '3px',
                                  }}
                                >
                                  <Flex
                                    wrap="nowrap"
                                    align="center"
                                    gap={10}
                                    style={{ whiteSpace: 'nowrap' }}
                                  >
                                    <span>账号类型为</span>
                                    {/* 依赖platformList改变了后就主动选择成个人号 */}
                                    <ProFormDependency name={['platformList']}>
                                      {(dependencyValues, form) => {
                                        const { platformList } = dependencyValues;
                                        const hasOtherPlatforms = platformList?.some(
                                          (platform: string) =>
                                            Number(platform) !== PlatForm.Douyin,
                                        );
                                        if (hasOtherPlatforms) {
                                          form.setFieldValue(
                                            [formName, index, name, 'accountType'],
                                            '4',
                                          );
                                        }
                                        return (
                                          <ProFormSelect
                                            name={[name, 'accountType']}
                                            fieldProps={{
                                              defaultValue: '1',
                                              allowClear: false,
                                              options: [
                                                {
                                                  label: '不限类型',
                                                  value: '1',
                                                  disabled: hasOtherPlatforms,
                                                },
                                                {
                                                  label: '企业号',
                                                  value: '2',
                                                  disabled: hasOtherPlatforms,
                                                },
                                                {
                                                  label: '员工号',
                                                  value: '3',
                                                  disabled: hasOtherPlatforms,
                                                },
                                                { label: '个人号', value: '4' },
                                              ],
                                            }}
                                            noStyle
                                          />
                                        );
                                      }}
                                    </ProFormDependency>
                                    <span>, 计算方式为</span>
                                    <ProFormSelect
                                      name={[name, 'calcType']}
                                      fieldProps={{
                                        defaultValue: '1',
                                        allowClear: false,
                                      }}
                                      valueEnum={{
                                        1: {
                                          text: '范围总分',
                                          disabled:
                                            ruleData[name - 1]?.calcType == 2 ||
                                            ruleData[name + 1]?.calcType == 2,
                                        },
                                        2: {
                                          text: '数值相乘',
                                          disabled:
                                            ruleData[name - 1]?.calcType == 1 ||
                                            ruleData[name + 1]?.calcType == 1,
                                        },
                                      }}
                                      noStyle
                                    />
                                    <span>,</span>
                                    <ProFormDependency name={[formName, index, name, 'calcType']}>
                                      {(value) => {
                                        const calcType =
                                          value[item.fieldName][index][name]?.calcType;
                                        if (calcType == 1) {
                                          return (
                                            <>
                                              {renderInputRange(
                                                name,
                                                item.type,
                                                ruleData,
                                                item?.rangeConfig,
                                              )}
                                              <span>增加</span>
                                            </>
                                          );
                                        } else {
                                          return (
                                            <>
                                              {renderMultiplicationInput(name, item?.mulConfig)}
                                              <span>上限为</span>
                                            </>
                                          );
                                        }
                                      }}
                                    </ProFormDependency>
                                    <ProFormItem
                                      name={[name, 'point']}
                                      rules={[{ required: true }]}
                                      noStyle
                                    >
                                      <CustomInputNumber
                                        defaultValue={0}
                                        size="middle"
                                        style={{ minWidth: '70px' }}
                                      />
                                    </ProFormItem>
                                    <span>分</span>
                                    <Space style={{ color: '#bfbfbf' }}>
                                      <MinusCircleOutlined onClick={() => remove(name)} />
                                      <PlusCircleOutlined
                                        onClick={() => {
                                          add({
                                            accountType: '1',
                                            calcType:
                                              ruleData.length > 0 ? ruleData[0]?.calcType : '1',
                                          });
                                        }}
                                      />
                                    </Space>
                                  </Flex>
                                </div>
                              </ProFormItem>
                            );
                          }}
                        </ProFormDependency>
                      ))}
                      <Form.Item noStyle>
                        <Form.ErrorList errors={errors} />
                      </Form.Item>
                    </>
                  );
                } else {
                  return (
                    <Empty
                      key="none"
                      image={Empty.PRESENTED_IMAGE_SIMPLE}
                      description={<span>暂无数据</span>}
                    />
                  );
                }
              }}
            </Form.List>
          </div>
        );
      })}
    </ProCard>
  );
};

export default RuleForm;
