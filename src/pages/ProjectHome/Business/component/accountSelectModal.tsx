import BlueVip from '@/assets/blue-vip.png';
import DynamicTree from '@/components/dynamicTree';
import TagsSelect from '@/components/tags/tagsSelect';
import useProjectId from '@/hooks/useProjectId';
import { ActivityUserItem, GetActivityUserList } from '@/services/activity';
import { HideScrollBarDiv } from '@/utils/commonStyle';
import { CloseOutlined, PlusOutlined } from '@ant-design/icons';
import { ActionType, ProCard, ProFormInstance, ProList } from '@ant-design/pro-components';
import { useParams } from '@umijs/max';
import {
  Avatar,
  Button,
  Checkbox,
  CheckboxProps,
  ConfigProvider,
  Flex,
  List,
  Modal,
  Space,
  theme,
  Tooltip,
} from 'antd';
import { isEqual, uniqBy } from 'lodash-es';
import { useCallback, useEffect, useRef, useState } from 'react';

type AccountSelectModalProps = {
  value?: any[];
  onChange?: (value: any) => void;
  showAccountName?: any[];
  setShowAccountName?: React.Dispatch<React.SetStateAction<ActivityUserItem[] | undefined>>;
  checkModifyAccount?: boolean;
  setCheckModifyAccount?: React.Dispatch<React.SetStateAction<boolean>>;
};

const AccountSelectModal = (props: AccountSelectModalProps) => {
  const {
    onChange,
    showAccountName,
    setShowAccountName,
    checkModifyAccount,
    setCheckModifyAccount,
  } = props;
  const projectId = useProjectId();
  const { incentiveId } = useParams();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectRow, setSelectRow] = useState<ActivityUserItem[]>([]);
  const [treeValue, setTreeValue] = useState<string[] | undefined>(undefined);
  const actionRef = useRef<ActionType>();
  const formRef = useRef<ProFormInstance>();
  const [allUserData, setAllUserData] = useState<ActivityUserItem[]>([]);
  const [checkedAll, setCheckedAll] = useState(false);
  const tempSelectRow = useRef<ActivityUserItem[]>([]);
  const firstSetFetchValue = useRef(false);

  const currentAllUserIds = allUserData.map((item) => item.id);
  const currentSelectRow = selectRow.filter((item) => currentAllUserIds.includes(item.id));
  const indeterminate = currentSelectRow.length > 0 && currentSelectRow.length < allUserData.length;

  const showModal = () => {
    setIsModalOpen(true);
  };

  const handleOk = () => {
    setIsModalOpen(false);
    const selectedValue = allUserData.filter((item) => item.isJoin === 1);
    const checkModifyAccount = !isEqual(selectedValue, selectRow);
    setCheckModifyAccount?.(checkModifyAccount);
    const changeValue = selectRow.map((item) => item.id);
    tempSelectRow.current = selectRow;
    onChange?.(changeValue);
    setShowAccountName?.(selectRow);
  };

  const handleCancel = () => {
    setIsModalOpen(false);
    setSelectRow(tempSelectRow.current);
    setTreeValue(undefined);
    firstSetFetchValue.current = false;
  };

  const onSelectAllChange: CheckboxProps['onChange'] = useCallback(
    (e: any) => {
      setCheckedAll(e.target.checked);
      if (e.target.checked) {
        const currentAllSelectedRow = allUserData;
        const allSelectRow = [...selectRow, ...currentAllSelectedRow];
        // 根据id去重
        const uniqSelect = uniqBy(allSelectRow, 'id');
        setSelectRow(uniqSelect);
      } else {
        // 只剔除当前筛选看到的行
        const newSeletRow = selectRow.filter((item) => !currentAllUserIds.includes(item.id));
        setSelectRow(newSeletRow);
      }
    },
    [allUserData, selectRow, currentAllUserIds],
  );

  useEffect(() => {
    const selectRowIds = selectRow.map((item) => item.id);
    if (currentAllUserIds.every((item) => selectRowIds.includes(item))) {
      setCheckedAll(true);
    } else {
      setCheckedAll(false);
    }
  }, [selectRow, allUserData]);

  return (
    <>
      <div onClick={showModal}>
        {showAccountName && showAccountName.length > 0 ? (
          <Flex wrap="wrap" gap={10}>
            {showAccountName.slice(0, 4).map((item) => (
              <span
                className="overflow-hidden text-ellipsis whitespace-nowrap rounded bg-[#ebedf2] p-1 text-xs"
                key={item.id}
              >
                {item.nickname}
              </span>
            ))}
            {showAccountName.length > 4 ? (
              <span className="overflow-hidden text-ellipsis whitespace-nowrap rounded bg-[#ebedf2] p-1 text-xs">
                ...
              </span>
            ) : null}
            <a>编辑</a>
          </Flex>
        ) : (
          <Button icon={<PlusOutlined />} block />
        )}
      </div>
      <Modal
        title="参与对象"
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        width="50%"
      >
        <ProCard split="vertical" ghost>
          <ProCard colSpan="70%" ghost>
            <ConfigProvider
              theme={{
                algorithm: [theme.compactAlgorithm],
              }}
            >
              <ProList<ActivityUserItem>
                size="small"
                style={{ maxHeight: 580, overflow: 'hidden', overflowY: 'auto' }}
                metas={{
                  title: {
                    dataIndex: 'dynamicDimension',
                    title: '用户',
                    formItemProps: {
                      label: null,
                    },
                    renderFormItem: () => {
                      return <DynamicTree value={treeValue} setValue={setTreeValue} />;
                    },
                  },
                  subTitle: {
                    dataIndex: 'nickname',
                    title: '账号名称',
                    fieldProps: {
                      placeholder: '账号名称搜索',
                    },
                    formItemProps: {
                      label: null,
                    },
                    render: (_, row) => {
                      return (
                        <Space>
                          <Avatar size="small" src={row.avatar} />
                          <span>{row.nickname}</span>
                          {row.blueVipFlag ? <img src={BlueVip} width={18} /> : null}
                        </Space>
                      );
                    },
                  },
                  status: {
                    // 自己扩展的字段，主要用于筛选，不在列表中显示
                    title: '标签',
                    dataIndex: 'tagIds',
                    fieldProps: {
                      placeholder: '账号标签',
                    },
                    formItemProps: {
                      label: null,
                    },
                    renderFormItem: () => {
                      return <TagsSelect />;
                    },
                  },
                }}
                rowKey="id"
                formRef={formRef}
                actionRef={actionRef}
                className="activity-user-list"
                search={{
                  className: 'account-select-search',
                  labelWidth: 'auto',
                  defaultCollapsed: false,
                  searchGutter: 10,
                  span: {
                    xs: 12,
                    sm: 12,
                    md: 12,
                    lg: 12,
                    xl: 12,
                    xxl: 12,
                  },
                }}
                headerTitle={
                  <Flex vertical gap={10}>
                    <Checkbox
                      checked={checkedAll}
                      style={{ marginLeft: '12px' }}
                      onChange={onSelectAllChange}
                      indeterminate={indeterminate}
                    >
                      <span style={{ marginLeft: '12px', textWrap: 'nowrap' }}>全选</span>
                    </Checkbox>
                  </Flex>
                }
                params={{ projectId, teamCodeList: treeValue, activityId: incentiveId }}
                request={async (params = {} as Record<string, any>) => {
                  const res = await GetActivityUserList(params);
                  setAllUserData(res.data || []);
                  return { data: res.data };
                }}
                ghost
                onReset={() => {
                  setTreeValue(undefined);
                }}
                postData={(data: ActivityUserItem[]) => {
                  // 未修改的情况下使用接口的勾选
                  if (!checkModifyAccount && !firstSetFetchValue.current) {
                    const selectedValue = data.filter((item) => item.isJoin === 1);
                    setSelectRow(selectedValue);
                    tempSelectRow.current = selectedValue;
                    firstSetFetchValue.current = true;
                  }
                  return data;
                }}
                rowSelection={{
                  type: 'checkbox',
                  preserveSelectedRowKeys: true,
                  onChange: (_, selectedRows) => {
                    setSelectRow(selectedRows);
                  },
                  selectedRowKeys: selectRow.map((item) => item.id),
                }}
                tableAlertRender={false}
                options={false}
              />
            </ConfigProvider>
          </ProCard>
          <ProCard colSpan="30%">
            <HideScrollBarDiv style={{ maxHeight: 560 }}>
              <div style={{ width: '100%', color: '#989898' }}>
                <span>已选 {selectRow.length} 项</span>
                <span
                  style={{ float: 'right' }}
                  onClick={() => {
                    setSelectRow([]);
                    actionRef.current?.clearSelected?.();
                  }}
                >
                  清空
                </span>
              </div>
              <List
                size="small"
                dataSource={selectRow}
                renderItem={(item) => (
                  <List.Item
                    style={{ paddingInline: '0px', borderBlockEnd: 'none', paddingBlock: '5px' }}
                  >
                    <Tooltip title={item.nickname}>
                      <span className="overflow-hidden text-ellipsis whitespace-nowrap rounded bg-[#ebedf2] p-1 text-xs">
                        {item.nickname}
                        <CloseOutlined
                          onClick={() => {
                            const newSelectRow = selectRow.filter((row) => row.id !== item.id);
                            setSelectRow(newSelectRow);
                          }}
                        />
                      </span>
                    </Tooltip>
                  </List.Item>
                )}
              />
            </HideScrollBarDiv>
          </ProCard>
        </ProCard>
      </Modal>
    </>
  );
};

export default AccountSelectModal;
