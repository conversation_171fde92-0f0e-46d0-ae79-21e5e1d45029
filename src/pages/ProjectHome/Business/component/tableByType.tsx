import ExportButton from '@/components/exportButton';
import {
  ActivityRankExport,
  ActivityRankPageItem,
  ExportRuleBody,
  GetActivityRankPage,
  GetRuleList,
  RuleTypeList,
} from '@/services/activity';
import { proTableRequestAdapter } from '@/utils';
import { RankBgMap } from '@/utils/commonStyle';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import { useParams } from '@umijs/max';
import { useAsyncEffect } from 'ahooks';
import { saveAs } from 'file-saver';
import { useRef, useState } from 'react';
import { customPaginationRender } from '../../style';
import { accountTypeMap, calcTypeTooltipMap, ruleKeyMap } from '../defaultConfig';

type TableByTypeProps = {
  type: number;
  headerTitle: string;
};

export const renderFetchColumns = (data: RuleTypeList[]) => {
  if (!data) return [];
  return data.map((item) => {
    const { accountType, ruleKey, calcType } = item;
    const accountName = accountTypeMap[accountType];
    const title = `${accountName ? accountName + '-' : ''}${ruleKeyMap[ruleKey].title}`;
    const dataIndex = `${ruleKey}-${accountType}`;
    const tooltip = ruleKeyMap[ruleKey].tooltip + ' ' + calcTypeTooltipMap[calcType];
    return {
      title,
      dataIndex,
      tooltip,
      align: 'center',
    };
  });
};

// 为了根据规则类型给表头归类
export const typeSortMap: { [key: number]: number } = {
  1: 0,
  2: 0,
  3: 0,
  9: 0,
  10: 0,
  4: 1,
  5: 1,
  6: 2,
  7: 2,
  8: 2,
};

export const exportRuleColumnsData = (data: RuleTypeList[]) => {
  if (!data) return [];
  return data.map((item) => {
    const { accountType, ruleKey } = item;
    const acountName = accountTypeMap[accountType];
    const title = `${acountName ? acountName + '-' : ''}${ruleKeyMap[ruleKey].title}`;
    return {
      title,
      accountType,
      ruleKey,
    };
  });
};

const TableByType = (props: TableByTypeProps) => {
  const { type, headerTitle } = props;
  const { incentiveId } = useParams();
  const [columns, setColumns] = useState<ProColumns<any>[]>([]);
  const exportRuleColumnsRef = useRef<ExportRuleBody[]>();

  useAsyncEffect(async () => {
    const res = await GetRuleList({ activityId: incentiveId });
    const defaultColumns = [
      {
        title: '排名',
        dataIndex: 'rank',
        align: 'center',
        render: (_: any, record: any) => {
          return RankBgMap[record.rank] || <div>{record.rank}</div>;
        },
        width: 50,
      },
      {
        title: `${headerTitle}`,
        dataIndex: 'name',
        align: 'center',
      },
      {
        title: '总得分',
        dataIndex: 'point',
        align: 'center',
        width: 80,
      },
    ];
    const sortByType = res.data?.sort((a, b) => typeSortMap[a.type] - typeSortMap[b.type]);
    const fetchColumns = renderFetchColumns(sortByType || []);
    const exportRuleColumns = exportRuleColumnsData(res.data || []);
    exportRuleColumnsRef.current = exportRuleColumns;

    const newColumns = defaultColumns.concat(fetchColumns) as ProColumns<any>[];
    setColumns(newColumns);
  }, [headerTitle]);

  const handleExportRank = async () => {
    if (exportRuleColumnsRef.current) {
      const resBlob = await ActivityRankExport(
        { activityId: incentiveId, type },
        exportRuleColumnsRef.current,
      );
      saveAs(resBlob, `${headerTitle}排行榜.xls`);
    }
  };

  return (
    <ProTable
      rowKey="name"
      tableClassName="custom-table"
      style={{ zIndex: 4 - type, position: 'static' }}
      params={{ type, activityId: incentiveId }}
      pagination={{
        defaultPageSize: 4,
        showTotal: (total, range) => `${range[0]}-${range[1]} 共 ${total} 条`,
        itemRender: customPaginationRender,
        showSizeChanger: true,
        hideOnSinglePage: true,
        responsive: true,
      }}
      scroll={{ x: 'max-content' }}
      postData={(data: ActivityRankPageItem[]) => {
        return data.map((item) => {
          const { rankDetail, ...rest } = item;
          return rankDetail?.reduce(
            (obj, item2) => {
              // @ts-ignore
              obj[`${item2.ruleKey}-${item2.accountType}`] = item2.point;
              return obj;
            },
            { ...rest },
          );
        });
      }}
      request={(params, sorter, filter) => {
        return proTableRequestAdapter(params, sorter, filter, GetActivityRankPage);
      }}
      columns={columns}
      search={false}
      dateFormatter="string"
      headerTitle={headerTitle}
      options={false}
      toolBarRender={() => [<ExportButton exportFn={handleExportRank} key="export" />]}
    />
  );
};

export default TableByType;
