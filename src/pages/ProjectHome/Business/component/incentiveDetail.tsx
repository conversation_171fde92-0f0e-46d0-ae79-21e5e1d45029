import {
  ActivityDetailRes,
  ActivityUserItem,
  GetActivityDetail,
  GetActivityUserJoinList,
  Rules,
} from '@/services/activity';
import { AccountPlatformEnum } from '@/utils/platform';
import { CloseOutlined } from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';
import { useRequest } from 'ahooks';
import { Drawer, Flex, Table, TableColumnsType } from 'antd';
import { useEffect, useState } from 'react';
import styled from 'styled-components';
import { convertSecondsToHours } from '../incentiveForm';

type DataType = {
  key: React.Key;
  type: string;
  ruleStr: string;
  accountType: string;
  calcType: string;
};

type OuterColumnsDataType = {
  type: string;
  tableCategory: string;
};

const LiveTypes: Record<number, string> = {
  1: '累计直播时长',
  2: '场均直播观看次数',
  3: '场均直播互动次数',
  9: '直播场次(≥1小时的直播场次)',
  10: '直播平均场观人数',
};

const PostTypes: Record<number, string> = {
  4: '短视频条数',
  5: '条均短视频播放量',
};

const OtherTypes: Record<number, string> = {
  6: '私信回复占比',
  7: '私信回复效率',
  8: '账号增量粉丝',
};

const AccountTypes: Record<string | number, string> = {
  1: '不限类型',
  2: '企业号',
  3: '员工号',
  4: '个人号',
};

const CalcType: Record<string | number, string> = {
  1: '范围总分',
  2: '数值相乘',
};

const outerColumns: TableColumnsType<OuterColumnsDataType> = [
  {
    title: '类别',
    dataIndex: 'type',
  },
  {
    title: '表格属性',
    dataIndex: 'tableCategory',
    hidden: true,
  },
];

const columns: TableColumnsType<DataType> = [
  {
    title: '账号类型',
    dataIndex: 'accountType',
    align: 'center',
  },
  {
    title: '计算方式',
    dataIndex: 'calcType',
    align: 'center',
  },
  {
    title: '计算逻辑',
    dataIndex: 'ruleStr',
    align: 'center',
  },
];

type IncentiveDetailProps = {
  activityId: number;
};

const TableTitle = styled.div`
  font-size: 14px;
  font-weight: 600;
  margin-left: 6px;
`;

const AccountUserTag = styled.div`
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  border-radius: 3px;
  font-size: 12px;
  padding: 4px;
  width: fit-content;
`;

const IncentiveDetail = (props: IncentiveDetailProps) => {
  const { activityId } = props;
  const [open, setOpen] = useState(false);
  const [detailData, setDetailData] = useState<ActivityDetailRes>();
  const [liveTableData, setLiveTableData] = useState<DataType[]>([]);
  const [postTableData, setPostTableData] = useState<DataType[]>([]);
  const [otherTableData, setOtherTableData] = useState<DataType[]>([]);
  const [allActivityUser, setAllActivityUser] = useState<ActivityUserItem[]>([]);

  const { loading, run: getActivityDetail } = useRequest(GetActivityDetail, {
    manual: true,
    onSuccess: (res) => {
      setDetailData(res.data);
    },
  });

  const showDrawer = async () => {
    setOpen(true);
    await getActivityDetail({ activityId });
  };

  const onClose = () => {
    setOpen(false);
  };

  const renderTableData = (
    typeRuleTypeStr: Record<number, string[]>,
    typeNameMap: Record<number, string>,
    type: string,
    data?: Rules[],
  ): DataType[] => {
    const result: DataType[] = [];
    if (!data || data.length === 0) return result;

    // 中文特殊
    const mulTypeStr: Record<number, string[]> = {
      6: ['私信回复占比为', '%'],
      7: ['3分钟回复率为', '%'],
      8: ['增粉达', '人'],
    };

    if (data.length > 0) {
      data.forEach((item) => {
        const parseValueRuleStr = JSON.parse(item.ruleStr);
        const ruleTypeStr: Record<number, string[]> =
          type === 'other' && item.calcType == 1 ? mulTypeStr : typeRuleTypeStr;

        const renderRuleStr = () => {
          const parseValue = Object.values(parseValueRuleStr)[0] as number[];
          const specialCases: Record<number, () => string> = {
            6: () => `${parseValue}`,
            7: () => `${parseValue}`,
            8: () => `${parseValue[0]} ~ ${parseValue[1]} `,
          };

          if (item.calcType == 1) {
            if (specialCases[item.type]) {
              return `${ruleTypeStr[item.type][0]} ${specialCases[item.type]()} ${
                ruleTypeStr[item.type][1]
              }, 增加 ${item.point} 分`;
            } else {
              const value =
                type === 'live' && item.type === 1
                  ? (parseValue as number[])?.map(convertSecondsToHours)
                  : (parseValue as number[]);
              return `${ruleTypeStr[item.type][0]} ${value[0]} ~ ${value[1]} ${
                ruleTypeStr[item.type][1]
              }, 增加 ${item.point} 分`;
            }
          } else if (item.calcType == 2) {
            return `${ruleTypeStr[item.type][0]}X${ruleTypeStr[item.type][1]},得分=X * ${
              parseValueRuleStr['mulPoint']
            }分, 上限为${item.point}分`;
          } else {
            return '未知';
          }
        };

        result.push({
          key: item.ruleStr,
          type: typeNameMap[item.type] || '未知',
          accountType: AccountTypes[item.accountType] || '未知',
          calcType: CalcType[item.calcType] || '未知',
          ruleStr: renderRuleStr(),
        });
      });
    }
    return result;
  };

  const renderLiveTableData = (data?: Rules[]) => {
    const ruleTypeStr: Record<number, string[]> = {
      1: ['时长为', '小时'],
      2: ['人次为', '次'],
      3: ['互动为', '次'],
      9: ['场次为', '次'],
      10: ['人数为', '次'],
    };
    return renderTableData(ruleTypeStr, LiveTypes, 'live', data);
  };

  const renderPostTableData = (data?: Rules[]) => {
    const ruleTypeStr: Record<number, string[]> = {
      4: ['条数为', '条'],
      5: ['播放量为', '次'],
    };
    return renderTableData(ruleTypeStr, PostTypes, 'post', data);
  };

  const renderOtherTableData = (data?: Rules[]) => {
    const ruleTypeStr: Record<number, string[]> = {
      6: ['私信回复占比≥', '%'],
      7: ['3分钟回复率≥', '%'],
      8: ['增粉达', '人'],
    };
    return renderTableData(ruleTypeStr, OtherTypes, 'other', data);
  };

  useEffect(() => {
    const liveData = renderLiveTableData(detailData?.liveRules);
    const postData = renderPostTableData(detailData?.postRules);
    const otherData = renderOtherTableData(detailData?.otherRules);
    setLiveTableData(liveData);
    setPostTableData(postData);
    setOtherTableData(otherData);
  }, [detailData]);

  const expandedRowRender = (record: OuterColumnsDataType) => {
    const { type, tableCategory } = record;
    let data: DataType[] | [] = [];

    switch (tableCategory) {
      case 'live':
        data = liveTableData.filter((item) => item.type === type);
        break;
      case 'post':
        data = postTableData.filter((item) => item.type === type);
        break;
      case 'other':
        data = otherTableData.filter((item) => item.type === type);
        break;
      default:
        data = [];
        break;
    }

    return (
      <Table
        columns={columns}
        dataSource={data}
        size="middle"
        pagination={false}
        loading={loading}
        rowKey="ruleStr"
      />
    );
  };

  return (
    <>
      <a onClick={showDrawer}>详情</a>
      <Drawer
        title="活动方案详情"
        onClose={onClose}
        open={open}
        size="large"
        closeIcon={null}
        extra={<CloseOutlined onClick={onClose} />}
      >
        <Flex vertical gap={25}>
          <div>激励方案: {detailData?.activityName}</div>
          <div>
            参与对象:
            <Flex wrap="wrap" gap={10} style={{ marginTop: '10px' }}>
              {allActivityUser.length > 0 ? (
                <>
                  {allActivityUser.map((item) => (
                    <AccountUserTag key={item.id} style={{ backgroundColor: '#ebedf2' }}>
                      {item.nickname}
                    </AccountUserTag>
                  ))}
                </>
              ) : (
                <>
                  {detailData?.activityUserList?.map((item) => (
                    <AccountUserTag key={item.id} style={{ backgroundColor: '#ebedf2' }}>
                      {item.nickname}
                    </AccountUserTag>
                  ))}
                  <AccountUserTag
                    onClick={async () => {
                      const res = await GetActivityUserJoinList({ activityId });
                      res.data && setAllActivityUser(res.data);
                    }}
                    style={{ backgroundColor: '#ebedf2' }}
                  >
                    查看全部
                  </AccountUserTag>
                </>
              )}
            </Flex>
          </div>
          <div>
            参与平台:{' '}
            {detailData?.platformList &&
              detailData.platformList
                .map((p) => AccountPlatformEnum[p as keyof typeof AccountPlatformEnum]?.text)
                .filter(Boolean)
                .join('、')}
          </div>
          <div>
            时间范围: {detailData?.activityStartTime} ~ {detailData?.activityEndTime}
          </div>
          <div>活动规则配置</div>
          <ProCard style={{ background: '#FBFCFE', padding: '12px 16px' }} ghost>
            <TableTitle style={{ color: '#000000' }}>直播:</TableTitle>
            <Table
              columns={outerColumns}
              dataSource={Object.values(LiveTypes).map((item) => {
                return {
                  type: item,
                  tableCategory: 'live',
                };
              })}
              size="middle"
              expandable={{ expandedRowRender }}
              pagination={false}
              loading={loading}
              rowKey="type"
            />
          </ProCard>
          <ProCard style={{ background: '#FBFCFE', padding: '12px 16px' }} ghost>
            <TableTitle style={{ color: '#000000' }}>短视频:</TableTitle>
            <Table
              columns={outerColumns}
              dataSource={Object.values(PostTypes).map((item) => {
                return {
                  type: item,
                  tableCategory: 'post',
                };
              })}
              size="middle"
              expandable={{ expandedRowRender }}
              pagination={false}
              loading={loading}
              rowKey="type"
            />
          </ProCard>
          <ProCard style={{ background: '#FBFCFE', padding: '12px 16px' }} ghost>
            <TableTitle style={{ color: '#000000' }}>其他:</TableTitle>
            <Table
              columns={outerColumns}
              dataSource={Object.values(OtherTypes).map((item) => {
                return {
                  type: item,
                  tableCategory: 'other',
                };
              })}
              size="middle"
              expandable={{ expandedRowRender }}
              pagination={false}
              loading={loading}
              rowKey="type"
            />
          </ProCard>
        </Flex>
      </Drawer>
    </>
  );
};

export default IncentiveDetail;
