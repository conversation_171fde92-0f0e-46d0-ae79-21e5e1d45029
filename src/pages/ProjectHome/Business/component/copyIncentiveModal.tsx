import { ActivityCopy } from '@/services/activity';
import { disableDateLimitThreeMouth } from '@/utils/time';
import {
  ModalForm,
  ProCoreActionType,
  ProFormDateTimeRangePicker,
  ProFormText,
} from '@ant-design/pro-components';
import { message } from 'antd';
import dayjs from 'dayjs';

type CopyIncentiveModalProps = {
  record: any;
  action: ProCoreActionType<any> | undefined;
};

const CopyIncentiveModal = (props: CopyIncentiveModalProps) => {
  const { record, action } = props;

  return (
    <ModalForm
      layout="horizontal"
      title="激励方案复制"
      labelCol={{ span: 5 }}
      wrapperCol={{ span: 15 }}
      trigger={<span>复制</span>}
      modalProps={{
        destroyOnHidden: true,
      }}
      onFinish={async (value) => {
        const { activityName, activityTime } = value;
        const [activityStartTime, activityEndTime] = activityTime;
        const submitValue = {
          activityId: record?.id,
          activityName,
          activityStartTime,
          activityEndTime,
        };
        const res = await ActivityCopy(submitValue);
        if (res.code === 0) {
          message.success('复制成功');
          action?.reload();
          return true;
        } else {
          message.error('复制失败');
          return false;
        }
      }}
    >
      <ProFormText
        name="activityName"
        label="激励名称"
        initialValue={`${record.activityName}-副本`}
        placeholder="请输入激励名称"
        rules={[{ required: true, message: '请输入激励名称' }]}
      />
      <ProFormDateTimeRangePicker
        name="activityTime"
        label="时间范围"
        tooltip="时间范围不超过3个月"
        placeholder={['开始时间', '结束时间']}
        initialValue={[dayjs(), dayjs().add(2, 'day')]}
        rules={[{ required: true, message: '请选择时间' }]}
        fieldProps={{
          disabledDate: disableDateLimitThreeMouth,
        }}
      />
    </ModalForm>
  );
};

export default CopyIncentiveModal;
