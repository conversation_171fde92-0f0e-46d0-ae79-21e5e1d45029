import ExportButton from '@/components/exportButton';
import {
  ActivityRankDetailExport,
  ActivityRankPageItem,
  ExportRuleBody,
  GetActivityRankDetail,
  GetRuleList,
} from '@/services/activity';
import { proTableRequestAdapter } from '@/utils';
import { RankBgMap } from '@/utils/commonStyle';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import { useParams } from '@umijs/max';
import { useAsyncEffect } from 'ahooks';
import saveAs from 'file-saver';
import { useRef, useState } from 'react';
import { customPaginationRender } from '../../style';
import { exportRuleColumnsData, renderFetchColumns, typeSortMap } from './tableByType';

const DetailRank = () => {
  const { incentiveId } = useParams();
  const [columns, setColumns] = useState<ProColumns<any>[]>([]);
  const exportRuleColumnsRef = useRef<ExportRuleBody[]>();

  useAsyncEffect(async () => {
    const res = await GetRuleList({ activityId: incentiveId });
    const defaultColumns = [
      {
        title: '排名',
        dataIndex: 'rank',
        align: 'center',
        render: (_: any, record: any) => {
          return RankBgMap[record.rank] || <div>{record.rank}</div>;
        },
        width: 50,
      },
      {
        title: '名称',
        dataIndex: 'nickName',
        align: 'center',
      },
      {
        title: '总得分',
        dataIndex: 'point',
        align: 'center',
        width: 80,
      },
    ];
    const sortByType = res.data?.sort((a, b) => typeSortMap[a.type] - typeSortMap[b.type]) || [];
    const fetchColumns = renderFetchColumns(sortByType);
    const exportRuleColumns = exportRuleColumnsData(res.data || []);
    exportRuleColumnsRef.current = exportRuleColumns;

    const newColumns = defaultColumns.concat(fetchColumns) as ProColumns<any>[];
    setColumns(newColumns);
  }, []);

  const handleExportRank = async () => {
    if (exportRuleColumnsRef.current) {
      const resBlob = await ActivityRankDetailExport(
        { activityId: incentiveId },
        exportRuleColumnsRef.current,
      );
      saveAs(resBlob, `排行榜.xls`);
    }
  };

  return (
    <ProTable
      rowKey="nickName"
      tableClassName="custom-table"
      params={{ activityId: incentiveId }}
      pagination={{
        showTotal: (total, range) => `${range[0]}-${range[1]} 共 ${total} 条`,
        itemRender: customPaginationRender,
        showSizeChanger: true,
      }}
      scroll={{ x: 'max-content' }}
      postData={(data: ActivityRankPageItem[]) => {
        return data.map((item) => {
          const { rankDetail, ...rest } = item;
          return rankDetail?.reduce(
            (obj, item2) => {
              // @ts-ignore
              obj[`${item2.ruleKey}-${item2.accountType}`] = item2.point;
              return obj;
            },
            { ...rest },
          );
        });
      }}
      request={(params, sorter, filter) => {
        return proTableRequestAdapter(params, sorter, filter, GetActivityRankDetail);
      }}
      columns={columns}
      search={false}
      dateFormatter="string"
      headerTitle="排名明细"
      options={false}
      toolBarRender={() => [<ExportButton exportFn={handleExportRank} key="export" />]}
    />
  );
};

export default DetailRank;
