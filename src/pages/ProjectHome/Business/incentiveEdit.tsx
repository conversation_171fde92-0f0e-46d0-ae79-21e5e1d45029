import { GetActivityDetail } from '@/services/activity';
import { PageContainer } from '@ant-design/pro-components';
import { useParams } from '@umijs/max';
import { useRequest } from 'ahooks';
import IncentiveForm from './incentiveForm';

const IncentiveEdit = () => {
  const { incentiveId } = useParams();
  const { data, loading } = useRequest(() => GetActivityDetail({ activityId: incentiveId }));

  return (
    <PageContainer
      title={
        <>
          <span style={{ color: '#4a4a4b' }}>商务政策管理/激励活动</span>
          <span>/编辑激励方案</span>
        </>
      }
    >
      <IncentiveForm initData={data?.data} loading={loading} isEdit />
    </PageContainer>
  );
};

export default IncentiveEdit;
