// 数组index对应
export const defaultData = {
  liveRules: [
    [
      {
        accountType: '1',
        calcType: '1',
        start: 4,
        end: 10,
        point: 10,
      },
    ],
    [
      {
        accountType: '1',
        calcType: '1',
        start: 300,
        end: 600,
        point: 10,
      },
    ],
    [
      {
        accountType: '1',
        calcType: '1',
        start: 100,
        end: 300,
        point: 10,
      },
    ],
    [
      {
        accountType: '1',
        calcType: '1',
        start: 1,
        end: 20,
        point: 10,
      },
    ],
    [
      {
        accountType: '1',
        calcType: '1',
        start: 300,
        end: 5000,
        point: 40,
      },
    ],
  ],
  postRules: [
    [
      {
        accountType: '1',
        calcType: '1',
        start: 4,
        end: 8,
        point: 10,
      },
    ],
    [
      {
        accountType: '1',
        calcType: '1',
        start: 500,
        end: 1000,
        point: 10,
      },
    ],
  ],
  otherRules: [
    [
      {
        accountType: '1',
        calcType: '1',
        percentage: 90,
        point: 10,
      },
    ],
    [
      {
        accountType: '1',
        calcType: '1',
        efficiency: 70,
        point: 20,
      },
    ],
    [
      {
        accountType: '1',
        calcType: '1',
        start: 300,
        end: 1000,
        point: 20,
      },
    ],
  ],
};

export const liveRuleTypeConfig = [
  {
    title: '累计直播时长',
    tooltip:
      '累计直播时长: 活动时间内的累计直播时长。可精确到小数点后一位，例如:2.1小时，0.1小时=6分钟。',
    type: 1,
    rangeConfig: {
      label: '时长为',
      unit: '小时',
    },
    mulConfig: {
      label: '时长为',
      unit: '小时',
    },
    fieldName: 'liveRules',
  },
  {
    title: '场均直播观看次数',
    tooltip: '场均直播观看次数: 场均直播观看次数: 活动时间内，总直播观看次数/总直播场数。',
    type: 2,
    rangeConfig: {
      label: '观看次数',
      unit: '次',
    },
    mulConfig: {
      label: '观看次数',
      unit: '次',
    },
    fieldName: 'liveRules',
  },
  {
    title: '场均直播互动次数',
    tooltip:
      '场均直播互动次数: 1. 互动次数包括点赞数+评论数。2.活动时间内，总直播互动数/总直播场数。',
    type: 3,
    rangeConfig: {
      label: '互动为',
      unit: '次',
    },
    mulConfig: {
      label: '互动为',
      unit: '次',
    },
    fieldName: 'liveRules',
  },
  {
    title: '直播场次(≥1小时的直播场次)',
    tooltip: '活动时间内，所选参与账号的直播时长≥1小时的总直播场',
    type: 9,
    rangeConfig: {
      label: '场次为',
      unit: '次',
    },
    mulConfig: {
      label: '场次为',
      unit: '次',
    },
    fieldName: 'liveRules',
  },
  {
    title: '直播平均场观人数',
    tooltip: '活动时间内，总直播观看人数/总直播场数',
    type: 10,
    rangeConfig: {
      label: '人数为',
      unit: '次',
    },
    mulConfig: {
      label: '人数为',
      unit: '次',
    },
    fieldName: 'liveRules',
  },
];

export const PostRuleTypeConfig = [
  {
    title: '短视频条数',
    tooltip: '短视频条数: 活动时间内发布的短视频条数。',
    type: 4,
    rangeConfig: {
      label: '条数为',
      unit: '条',
    },
    mulConfig: {
      label: '条数为',
      unit: '条',
    },
    fieldName: 'postRules',
  },
  {
    title: '条均短视频播放量',
    tooltip: '条均短视频播放量: 活动时间内，总短视频播放量/总短视频条数。',
    type: 5,
    rangeConfig: {
      label: '播放量为',
      unit: '次',
    },
    mulConfig: {
      label: '播放量为',
      unit: '次',
    },
    fieldName: 'postRules',
  },
];

export const OtherRuleTypeConfig = [
  {
    // 非区间特殊处理
    title: '私信回复占比',
    tooltip: '私信回复占比: 活动时间内，已回复的私信条数/总私信条数。',
    type: 6,
    mulConfig: {
      label: '私信回复占比为',
      unit: '%',
    },
    fieldName: 'otherRules',
  },
  {
    // 非区间特殊处理
    title: '私信回复率',
    tooltip:
      '私信回复效率:设定时间内回复的私信条数/总私信条数;示例:设定3min内的回复率为3min内回复的私信条数/总私信条数。',
    type: 7,
    mulConfig: {
      label: '3分钟回复率为',
      unit: '%',
    },
    fieldName: 'otherRules',
  },
  {
    title: '账号增量粉丝',
    tooltip: '账号增量粉丝: 活动时间内，账号增粉量包括直播与短视频的增粉量，并且是总增粉量。',
    type: 8,
    rangeConfig: {
      label: '增粉达',
      unit: '人',
    },
    mulConfig: {
      label: '增粉达',
      unit: '人',
    },
    fieldName: 'otherRules',
  },
];

export const ruleKeyMap: Record<string, { title: string; tooltip: string }> = {
  liveDuration: {
    title: '累计直播时长(分数)',
    tooltip: '活动时间内的累计直播时长。可精确到小数点后一位，例如: 2.1小时，0.1小时=6分钟',
  },
  liveViewCount: {
    title: '直播平均场观人数(分数)',
    tooltip: '活动时间内，总直播观看人数/总直播场数',
  },
  liveInteraction: {
    title: '场均直播互动次数(分数)',
    tooltip: '1. 互动次数包括点赞数+评论数。2. 活动时间内，总直播互动数/总直播场数',
  },
  postCount: {
    title: '短视频条数(分数)',
    tooltip: '活动时间内发布的短视频条数',
  },
  postPlayCount: {
    title: '条均短视频播放量(分数)',
    tooltip: '活动时间内，总短视频播放量/总短视频条数',
  },
  privateMsgPercentage: {
    title: '私信回复占比(分数)',
    tooltip: '活动时间内，已回复的私信条数/总私信条数',
  },
  privateMsgEfficiency: {
    title: '私信回复效率(分数)',
    tooltip:
      '设定时间内回复的私信条数/总私信条数;示例:设定3min内的回复率为3min内回复的私信条数/总私信条数',
  },
  accountNewFollower: {
    title: '账号增量粉丝(分数)',
    tooltip: '活动时间内，账号增粉量包括直播与短视频的增粉量，并且是总增粉量',
  },
  liveCount: {
    title: '直播场次(分数)',
    tooltip: '活动时间内，所选参与账号的直播时长≥1小时的总直播场',
  },
  liveViewTime: {
    title: '场均直播观看次数(分数)',
    tooltip: '活动时间内，总直播观看次数/总直播场数',
  },
};

export const accountTypeMap: Record<number, string> = {
  1: '',
  2: '企业号',
  3: '员工号',
};

export const calcTypeTooltipMap: Record<number, string> = {
  1: '范围总增：按阶梯式计算规则。数据指标数值范围内，可增加的总分，详见规则说明。',
  2: '单项相乘：按照数据指标上限范围内的数值*单项得分，详见规则说明。',
};
