import useProjectId from '@/hooks/useProjectId';
import {
  ActivityCreate,
  ActivityCreateOrUpdateParams,
  ActivityUpdate,
  ActivityUserItem,
  Rules,
} from '@/services/activity';
import { PlatForm } from '@/utils/platform';
import { disableDateLimitThreeMouth } from '@/utils/time';
import { QuestionCircleOutlined } from '@ant-design/icons';
import {
  FooterToolbar,
  ProCard,
  ProForm,
  ProFormDateTimeRangePicker,
  ProFormInstance,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { history, useParams } from '@umijs/max';
import { Flex, message, Space, Tooltip } from 'antd';
import { isEqual, isEqualWith, pick } from 'lodash-es';
import { useEffect, useRef, useState } from 'react';
import AccountSelectModal from './component/accountSelectModal';
import RuleForm from './component/ruleForm';
import {
  defaultData,
  liveRuleTypeConfig,
  OtherRuleTypeConfig,
  PostRuleTypeConfig,
} from './defaultConfig';

const formItemLayout = {
  labelCol: { xs: '60px', sm: '60px', md: '60px', lg: '60px', xl: '60px', xxl: '60px' },
  wrapperCol: { xs: 5, sm: 5, md: 5, lg: 5, xl: 5, xxl: 5 },
};

export const waitTimePromise = async (time: number = 100) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(true);
    }, time);
  });
};

export const waitTime = async (time: number = 100) => {
  await waitTimePromise(time);
};

type IncentiveFormProps = {
  initData?: ActivityCreateOrUpdateParams;
  loading?: boolean;
  isEdit?: boolean;
};

export type RuleType = {
  start?: number;
  end?: number;
  efficiency?: number;
  percentage: number;
  point: number;
  accountType: string | number;
  calcType: string | number;
  mulPoint?: number;
};

export const convertSecondsToHours = (seconds?: string | number): number => {
  if (!seconds) {
    return 0;
  }
  const hours = Number(seconds) / 3600;
  return parseFloat(hours.toFixed(1));
};

const convertHoursToSeconds = (hours?: string | number) => {
  if (!hours) {
    return 0;
  }
  const secondsPerHour = 3600;
  return `${Math.round(Number(hours) * secondsPerHour)}`;
};

// 是区间类型的规则 computational
const computationalTypeArr = [1, 2, 3, 4, 5, 8, 9, 10];

type IncentiveFormType = {
  activityName: string;
  accountPrimaryIds: number[];
  platformList: PlatForm[];
  activityStartTime: string;
  activityEndTime: string;
  /**
   * 短视频激励规则
   */
  postRules: RuleType[][];
  /**
   * 直播激励规则
   */
  liveRules: RuleType[][];
  /**
   * 其他激励规则
   */
  otherRules: RuleType[][];
};

const IncentiveForm = (props: IncentiveFormProps) => {
  const { initData, loading, isEdit } = props;
  const formRef = useRef<ProFormInstance>();
  const projectId = useProjectId();
  const [showAccountName, setShowAccountName] = useState<ActivityUserItem[] | undefined>([]);
  const [checkModifyAccount, setCheckModifyAccount] = useState(false);
  const { projectKey, industryType = '1' } = useParams();

  const transformValue = (stringifyValue: string, type: string | number | undefined) => {
    let value: any;
    if (stringifyValue) {
      if (computationalTypeArr.includes(Number(type))) {
        value = JSON.parse(stringifyValue)['computational'];
      } else if (type == 6) {
        value = JSON.parse(stringifyValue)['percentage'];
      } else if (type == 7) {
        value = JSON.parse(stringifyValue)['efficiency'];
      }
    }
    return value;
  };

  const transferFetchValue = (rules: Rules[] | undefined, arrLen: number) => {
    if (!rules) {
      return [];
    }
    const resultArr: any = Array.from({ length: arrLen }, () => []);

    // 创建映射，将类型映射到特定的索引
    const typeToIndexMap: Record<string, number> = {
      '1': 0,
      '2': 1,
      '3': 2,
      '9': 3,
      '10': 4,
      '4': 0, // 类型1和4映射到索引0
      '5': 1, // 类型2和5映射到索引1
      '6': 0, // 类型6映射到索引0，特殊处理
      '7': 1, // 类型7映射到索引1，特殊处理
      '8': 2, // 类型3和8映射到索引2
    };

    rules.forEach((item) => {
      const { ruleStr, type, point, accountType, calcType } = pick(item, [
        'point',
        'type',
        'ruleStr',
        'accountType',
        'calcType',
      ]);
      let obj;
      if (calcType == 1) {
        const value = transformValue(ruleStr, type);
        if (computationalTypeArr.includes(Number(type))) {
          obj = {
            start: type == 1 ? convertSecondsToHours(value[0]) : value[0],
            end: type == 1 ? convertSecondsToHours(value[1]) : value[1],
            point,
            accountType,
            calcType,
          };
        } else if (type == 6) {
          obj = { percentage: value, point, accountType, calcType };
        } else if (type == 7) {
          obj = { efficiency: value, point, accountType, calcType };
        }
      } else {
        obj = {
          mulPoint: JSON.parse(ruleStr)['mulPoint'],
          point,
          calcType,
          accountType,
        };
      }

      // 根据type确定插入的数组索引
      const index = typeToIndexMap[type];
      if (index !== undefined && obj) {
        resultArr[index].push(obj);
      }
    });
    return resultArr;
  };

  const checkDuplicateAccountType = (arr: RuleType[]): boolean => {
    const map: { [key: string]: boolean } = {};
    for (const obj of arr) {
      if (obj.calcType == 2) {
        if (map[obj.accountType]) {
          return false;
        } else {
          map[obj.accountType] = true;
        }
      }
    }
    return true;
  };

  const transferSubmitOtherRules = (
    rules: RuleType[][],
    ruleTypeConfig: any[],
    activityId?: number,
  ) => {
    return rules
      .flatMap((items, index) => {
        if (items && items?.length > 0) {
          return items.map((subitem) => {
            let rulestr;
            if (subitem.calcType == 1) {
              if (index === 2) {
                rulestr = JSON.stringify({ computational: [subitem.start, subitem.end] });
              } else if (index === 0) {
                rulestr = JSON.stringify({ percentage: subitem.percentage });
              } else if (index === 1) {
                rulestr = JSON.stringify({ efficiency: subitem.efficiency });
              }
            } else {
              rulestr = JSON.stringify({ mulPoint: subitem.mulPoint });
            }
            return {
              activityId,
              projectId,
              point: subitem.point,
              type: ruleTypeConfig[index].type,
              ruleStr: rulestr,
              accountType: subitem.accountType,
              calcType: subitem.calcType,
            };
          });
        }
      })
      .filter(Boolean);
  };

  const transferSubmitRules = (rules: RuleType[][], ruleTypeConfig: any[], activityId?: number) => {
    return rules
      .flatMap((item, index) => {
        return item.map((subitem) => {
          let rulestr;
          if (subitem.calcType == 1) {
            if (ruleTypeConfig[index]?.rangeConfig?.unit === '小时') {
              rulestr = JSON.stringify({
                computational: [
                  convertHoursToSeconds(subitem.start),
                  convertHoursToSeconds(subitem.end),
                ],
              });
            } else {
              rulestr = JSON.stringify({ computational: [subitem.start, subitem.end] });
            }
          } else {
            rulestr = JSON.stringify({ mulPoint: subitem.mulPoint });
          }

          return {
            activityId,
            projectId,
            point: subitem.point,
            type: ruleTypeConfig[index].type,
            ruleStr: rulestr,
            accountType: subitem.accountType,
            calcType: subitem.calcType,
          };
        });
      })
      .filter(Boolean);
  };

  const setFormInit = () => {
    if (initData) {
      formRef.current?.setFieldsValue(initData);
      formRef.current?.setFieldValue('activityTime', [
        initData.activityStartTime,
        initData.activityEndTime,
      ]);
      formRef.current?.setFieldValue('accountPrimaryIds', initData.accountPrimaryIds);
      setShowAccountName(initData.activityUserList);
      formRef.current?.setFieldValue(
        'platformList',
        initData.platformList?.map((item) => item.toString()),
      );

      const { liveRules, otherRules, postRules } = initData;
      formRef.current?.setFieldValue('liveRules', transferFetchValue(liveRules, 5));
      formRef.current?.setFieldValue('postRules', transferFetchValue(postRules, 2));
      formRef.current?.setFieldValue('otherRules', transferFetchValue(otherRules, 3));
    }
  };

  useEffect(() => {
    setFormInit();
  }, [initData]);

  const checkHasDuplicate = (rules: RuleType[][]) => {
    return rules.map((item) => checkDuplicateAccountType(item)).some((item) => item === false);
  };

  return (
    <ProCard style={{ minWidth: '1000px' }}>
      <ProForm<IncentiveFormType>
        layout="horizontal"
        {...formItemLayout}
        autoFocusFirstInput
        formRef={formRef}
        submitter={{
          searchConfig: {
            resetText: '取消',
          },
          render: (_, dom) => <FooterToolbar>{dom}</FooterToolbar>,
        }}
        onReset={() => {
          history.replace(`/project/${projectKey}/${industryType}/business/incentive`);
        }}
        loading={loading}
        initialValues={isEdit ? undefined : defaultData}
        onFinish={async (values) => {
          const activityId = initData?.activityId;
          const { liveRules, postRules, otherRules } = values;

          const rules: Record<string, RuleType[][]> = { liveRules, postRules, otherRules };
          let hasDuplicate = false;
          Object.keys(rules).forEach((key) => {
            const result = checkHasDuplicate(rules[key]);
            if (result) {
              hasDuplicate = true;
            }
          });

          if (hasDuplicate) {
            message.error('存在计算方式为[数值相同]并且[账号类型]完全相同,请检查');
            return;
          }

          const submitRules = (rules: RuleType[][], ruleTypeConfig: any[], activityId?: number) => {
            // 根据规则类型选择相应的转换函数
            const transformFn =
              ruleTypeConfig === OtherRuleTypeConfig
                ? transferSubmitOtherRules
                : transferSubmitRules;
            return transformFn(rules, ruleTypeConfig, activityId);
          };

          const transferRulesObj = {
            liveRules: submitRules(
              values.liveRules as RuleType[][],
              liveRuleTypeConfig,
              activityId,
            ),
            postRules: submitRules(
              values.postRules as RuleType[][],
              PostRuleTypeConfig,
              activityId,
            ),
            otherRules: submitRules(
              values.otherRules as RuleType[][],
              OtherRuleTypeConfig,
              activityId,
            ),
          };
          const pickInitData = pick(initData, 'liveRules', 'postRules', 'otherRules');
          const checkModifyRule = isEqualWith(pickInitData, transferRulesObj, (obj1, obj2, key) => {
            if (key === 'ruleStr') {
              const parsedObj1 = JSON.parse(obj1);
              const parsedObj2 = JSON.parse(obj2);
              return isEqual(parsedObj1, parsedObj2);
            }
          });
          if (isEdit) {
            const submitValue = {
              projectId,
              activityId: initData?.activityId,
              isModifyRule: !checkModifyRule ? 1 : 0, // 是否修改规则 0:否 1:是
              ...values,
              ...transferRulesObj,
            };
            const res = await ActivityUpdate(
              submitValue as unknown as ActivityCreateOrUpdateParams,
            );
            if (res.code === 0) {
              message.success('更新成功');
              await waitTime(2000);
              history.replace(`/project/${projectKey}/${industryType}/business/incentive`);
            }
          } else {
            const submitValue = {
              projectId,
              ...values,
              ...transferRulesObj,
            };
            const res = await ActivityCreate(
              submitValue as unknown as ActivityCreateOrUpdateParams,
            );
            if (res.code === 0) {
              message.success('新建成功');
              await waitTime(1000);
              history.replace(`/project/${projectKey}/${industryType}/business/incentive`);
            }
          }
        }}
      >
        <div style={{ height: '35px', fontWeight: 'bold' }}>活动方案</div>
        <ProFormText
          rules={[
            { required: true },
            {
              max: 20,
              message: '20个字符以内',
            },
          ]}
          name="activityName"
          label="激励名称"
          placeholder="请输入激励活动名称"
        />
        <ProForm.Item
          label="参与对象"
          name="accountPrimaryIds"
          rules={[{ required: true, message: '请选择参与对象' }]}
        >
          <AccountSelectModal
            showAccountName={showAccountName}
            setShowAccountName={setShowAccountName}
            checkModifyAccount={checkModifyAccount}
            setCheckModifyAccount={setCheckModifyAccount}
          />
        </ProForm.Item>
        <ProFormSelect
          name="platformList"
          label="参与平台"
          initialValue={[PlatForm.Douyin.toString()]}
          fieldProps={{
            defaultValue: [PlatForm.Douyin.toString()],
            mode: 'multiple',
          }}
          valueEnum={{
            [PlatForm.Douyin]: { text: '抖音' },
            [PlatForm.XiaoHongShu]: { text: '小红书' },
            [PlatForm.WXVideo]: { text: '视频号' },
            [PlatForm.KuaiShou]: { text: '快手' },
            [PlatForm.Weibo]: { text: '微博' },
            [PlatForm.DongCheDi]: { text: '懂车帝' },
            [PlatForm.Bilibili]: { text: 'B站' },
            [PlatForm.YiChe]: { text: '易车' },
            [PlatForm.QiCheZhiJia]: { text: '汽车之家' },
          }}
          placeholder="参与平台"
          rules={[{ required: true, message: '请选择参与平台' }]}
        />
        <ProFormDateTimeRangePicker
          name="activityTime"
          label="时间范围"
          tooltip="时间范围不超过3个月"
          placeholder={['开始时间', '结束时间']}
          transform={(value) => {
            const [activityStartTime, activityEndTime] = value;
            return { activityStartTime, activityEndTime };
          }}
          fieldProps={{
            disabledDate: disableDateLimitThreeMouth,
          }}
          rules={[{ required: true }]}
          wrapperCol={{ span: 24 }}
        />
        <Space>
          活动规则配置
          <Tooltip
            title={
              <>
                <div>1. 同一指标可配置多条</div>
                <div>2. 同一指标的计算数据不可重叠</div>
                <div>3. 范围型数据为{`[闭区间 ，开区间 )`}</div>
                <div>4. 当行为触达两个相同指标后，将结算得分最高的指标</div>
                <div>5. 计算数值为时长类型，则数值输入可精确到小数点后一位</div>
                <div>6. 计算数值为百分比类型，则数值输入可精确到小数点后两位</div>
                <div>7. 其余计算数值输入为整数型</div>
                <div>
                  8.
                  同个指标计算方式选择为数值相乘，则不能再选择范围总分。不同指标可以选择不同计算方式。
                </div>
              </>
            }
          >
            <QuestionCircleOutlined style={{ color: 'rgba(0, 0, 0, 0.45)' }} />
          </Tooltip>
        </Space>
        <Flex vertical>
          <RuleForm formName="liveRules" headerTitle="直播" ruleTypeConfig={liveRuleTypeConfig} />
          <RuleForm formName="postRules" headerTitle="短视频" ruleTypeConfig={PostRuleTypeConfig} />
          <RuleForm formName="otherRules" headerTitle="其他" ruleTypeConfig={OtherRuleTypeConfig} />
        </Flex>
      </ProForm>
    </ProCard>
  );
};

export default IncentiveForm;
