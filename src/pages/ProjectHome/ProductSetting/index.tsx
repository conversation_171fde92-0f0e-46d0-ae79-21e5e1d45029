import BreadCrumbSimple from '@/components/ui/breadcrumbSimple';
import PlatformSwitch from '@/components/platformSwitch';
import TimeFilter from '@/components/ui/timeFilter';
import useProjectId from '@/hooks/useProjectId';
import { GetDataCardTrend } from '@/services/data-card';
import { getTimeByType } from '@/utils/time';
import { useLocation, useRequest } from '@umijs/max';
import { Button, Col, Flex, Space } from 'antd';
import { useAtomValue } from 'jotai';
import { useEffect, useState } from 'react';
import { selectPlatformAtom } from '../atom';
import BasicData from './component/basicData';
import { AnimatePresence, motion } from 'framer-motion';
import { LeftOutlined, RightOutlined } from '@ant-design/icons';
import DataCard from '@/components/dataCard';
import { HideScrollBarRow } from '@/utils/commonStyle';
import { useHorizontalDragScroll } from '@/hooks/useHorizontalDragScroll';

const ProductSetting = () => {
  const { search } = useLocation();
  const query = new URLSearchParams(search);
  const { dateStart, dateEnd } = Object.fromEntries(query.entries());

  const defaultDateRange =
    dateStart && dateEnd && !isNaN(Date.parse(dateStart)) && !isNaN(Date.parse(dateEnd))
      ? [dateStart, dateEnd]
      : undefined;

  const projectId = useProjectId();
  const [rangeTime, setRangeTime] = useState<(string | undefined)[]>(
    defaultDateRange || getTimeByType('day'),
  );
  const platform = useAtomValue(selectPlatformAtom);
  const scrollContainerRef = useHorizontalDragScroll<HTMLDivElement>();
  const [showLeftBtn, setShowLeftBtn] = useState(false);

  const requestFn = (type: number) =>
    GetDataCardTrend({
      projectId,
      platform,
      startDate: rangeTime[0],
      endDate: rangeTime[1],
      type,
    });

  const { data: postCountData, loading: postCountDataLoading } = useRequest(() => requestFn(201), {
    refreshDeps: [rangeTime, platform],
  });

  const { data: postPlayCountData, loading: postPlayCountDataLoading } = useRequest(
    () => requestFn(202),
    {
      refreshDeps: [rangeTime, platform],
    },
  );

  const { data: postDiggCountData, loading: postDiggCountDataLoading } = useRequest(
    () => requestFn(203),
    {
      refreshDeps: [rangeTime, platform],
    },
  );

  const { data: postCommentCountData, loading: postCommentCountDataLoading } = useRequest(
    () => requestFn(204),
    {
      refreshDeps: [rangeTime, platform],
    },
  );

  const { data: postCollectCountData, loading: postCollectCountDataLoading } = useRequest(
    () => requestFn(205),
    {
      refreshDeps: [rangeTime, platform],
    },
  );

  const { data: postShareCountData, loading: postShareCountDataLoading } = useRequest(
    () => requestFn(206),
    {
      refreshDeps: [rangeTime, platform],
    },
  );

  const scroll = (width: number) => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: width, behavior: 'smooth' });
    }
  };

  const handleScroll = () => {
    if (scrollContainerRef.current) {
      const { scrollLeft } = scrollContainerRef.current;
      setShowLeftBtn(scrollLeft >= 30);
    }
  };

  useEffect(() => {
    const container = scrollContainerRef.current;
    if (container) {
      container.addEventListener('scroll', handleScroll);
    }

    return () => {
      // 清理滚动监听
      if (container) {
        container.removeEventListener('scroll', handleScroll);
      }
    };
  }, []);

  return (
    <div>
      <BreadCrumbSimple breadcrumbs={['平台管理', '作品列表']} />
      <div className="rounded-lg bg-white px-4 py-2">
        <Space>
          <TimeFilter value={rangeTime} onChange={(value) => setRangeTime(value)} />
          <PlatformSwitch />
        </Space>
      </div>
      <Flex justify="center" align="center">
        <AnimatePresence>
          {showLeftBtn && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.5 }}
              style={{
                zIndex: '1000',
              }}
            >
              <Button shape="circle" icon={<LeftOutlined />} onClick={() => scroll(-300)} />
            </motion.div>
          )}
        </AnimatePresence>
        <HideScrollBarRow
          gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}
          ref={scrollContainerRef}
          className="py-4"
          wrap={false}
        >
          <Col xs={8} sm={8} md={7} lg={7} xl={8} xxl={5} span={5}>
            <DataCard title="作品数" data={postCountData} loading={postCountDataLoading} />
          </Col>
          <Col xs={8} sm={8} md={7} lg={7} xl={8} xxl={5} span={5}>
            <DataCard title="播放量" data={postPlayCountData} loading={postPlayCountDataLoading} />
          </Col>
          <Col xs={8} sm={8} md={7} lg={7} xl={8} xxl={5} span={5}>
            <DataCard title="点赞量" data={postDiggCountData} loading={postDiggCountDataLoading} />
          </Col>
          <Col xs={8} sm={8} md={7} lg={7} xl={8} xxl={5} span={5}>
            <DataCard
              title="评论数"
              data={postCommentCountData}
              loading={postCommentCountDataLoading}
            />
          </Col>
          <Col xs={8} sm={8} md={7} lg={7} xl={8} xxl={5} span={5}>
            <DataCard
              title="收藏数"
              data={postCollectCountData}
              loading={postCollectCountDataLoading}
            />
          </Col>
          <Col xs={8} sm={8} md={7} lg={7} xl={8} xxl={5} span={5}>
            <DataCard
              title="分享数"
              data={postShareCountData}
              loading={postShareCountDataLoading}
            />
          </Col>
        </HideScrollBarRow>
        <Button
          shape="circle"
          icon={<RightOutlined />}
          style={{
            zIndex: '1000',
          }}
          onClick={() => scroll(300)}
        />
      </Flex>
      <BasicData projectId={projectId} rangeTime={rangeTime} />
    </div>
  );
};

export default ProductSetting;
