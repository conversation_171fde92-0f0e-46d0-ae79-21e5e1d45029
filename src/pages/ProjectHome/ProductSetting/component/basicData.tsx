import BlueVip from '@/assets/blue-vip.png';
import FailImg from '@/assets/fail-img.png';
import LogoImg from '@/assets/logo.png';
import DefaultAvatar from '@/assets/default-avatar.png';
import AggregateQueryInput from '@/components/aggregateQueryInput';
import DataFilter from '@/components/dataFilter';
import DynamicTree from '@/components/dynamicTree';
import ExportButton from '@/components/exportButton';
import FilterDropdownRadio from '@/components/filterDropdownRadio';
import { renderCustomColumns } from '@/components/renderCustomColumns';
import TagsSelect from '@/components/tags/tagsSelect';
import TagsShow from '@/components/tags/tagsShow';
import XGVideoCard from '@/components/xgVideoCard';
import useProjectFeature from '@/hooks/fg/useProjectFeature';
import useGetTeamFieldList from '@/hooks/useFields';
import { usePollingExport } from '@/hooks/usePollingExport';
import { BlueVipFlag } from '@/services/common';
import { AccountPlatformEnum } from '@/utils/platform';
import { getCarInfo, getScreenType, getVideoType } from '@/services/project-work-analysis';
import { ExportPost, PostBasicItem, PostSettingPage } from '@/services/setting';
import { FunctionCode } from '@/services/system';
import { proTableRequestAdapter } from '@/utils';
import { formatNum, initDataFilter, STICKY_OFFSETHEADER, transferParams } from '@/utils/common';
import {
  proTableOptionsConfig,
  proTablePaginationConfig,
  proTableSearchConfig,
} from '@/utils/proTableConfig';
import { formatSecond } from '@/utils/time';
import {
  CaretDownOutlined,
  CaretRightOutlined,
  CaretUpOutlined,
  DownloadOutlined,
  FilterFilled,
} from '@ant-design/icons';
import type { ActionType, ProColumns, ProFormInstance } from '@ant-design/pro-components';
import { ProCard, ProFormSelect, ProTable } from '@ant-design/pro-components';
import { FileTextIcon } from '@radix-ui/react-icons';
import { useLocation, useParams } from '@umijs/max';
import { useGetState, useSetState } from 'ahooks';
import { Button, Col, Flex, message, Popover, Row, Space, Switch, Tooltip } from 'antd';
import { FilterDropdownProps } from 'antd/es/table/interface';
import copy from 'copy-to-clipboard';
import dayjs from 'dayjs';
import duration from 'dayjs/plugin/duration';
import saveAs from 'file-saver';
import { useAtomValue } from 'jotai';
import { omit } from 'lodash-es';
import { useEffect, useRef, useState } from 'react';
import styled from 'styled-components';
import { selectingDealsAtom, selectPlatformAtom } from '../../atom';
import { Copy } from 'lucide-react';
import { getSumColumnsWidth } from '@/utils/table';
import { IndustryType } from '@/utils/const';
dayjs.extend(duration);

type BasicDataProps = {
  projectId?: string;
  rangeTime: (string | undefined)[];
};

const ProCardWrapper = styled(ProCard)`
  .screen-tag {
    height: 24px;
    padding: 0 8px;
    margin-right: 8px;
    display: inline-block !important;
    margin-bottom: 8px;
  }
  .limit-decs {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 3;
    height: 65;
  }
  .formula-part {
    display: inline-block;
    .formula-part-name {
      display: inline-block;
      font-weight: 500;
      font-size: 14px;
    }
    .formula-part-name::after {
      content: '';
      display: block;
      width: '100%';
      height: 4px;
    }
  }
`;
const BasicData = (props: BasicDataProps) => {
  const { projectId, rangeTime } = props;
  const { industryType } = useParams();
  const { search } = useLocation();
  const query = new URLSearchParams(search);
  const { videoType, carInfo } = Object.fromEntries(query.entries());
  const defaultShowAnalyzedProduct = videoType || carInfo ? true : false;
  /** 是否开启作品分析 */
  const { enable: enableWorkAnalysis } = useProjectFeature({
    featureId: FunctionCode.ProjectWorksAnalysis,
  });
  // url上的查询参数
  const defaultTreeValue = useAtomValue(selectingDealsAtom);
  const [showAnalyzedProduct, setShowAnalyzedProduct] = useState<boolean>(
    defaultShowAnalyzedProduct,
  ); // 当url上有查询参数时，默认显示已分析作品
  const platform = useAtomValue(selectPlatformAtom);
  const options = [
    { value: 'nickname', label: '账号名称' },
    { value: 'title', label: '作品标题' },
    { value: 'topic', label: '话题' },
  ];
  const [blueVipFlag, setBlueVipFlag, getBlueVipFlag] = useGetState<BlueVipFlag | undefined>();

  useEffect(() => {
    // 初始化表单参数为url上的查询参数
    formRef?.current?.setFieldsValue({
      videoType,
      productInfo: carInfo,
      teamCodeList: defaultTreeValue,
    });
    formRef.current?.submit();
  }, []);

  const handleDownload = async (record: any) => {
    try {
      const response = await fetch(record.url);
      if (!response.ok) {
        throw new Error('无法下载视频');
      }
      const blob = await response.blob();
      saveAs(blob, record.title.slice(0, 10) + '...');
    } catch (error) {
      console.error('下载失败:', error);
    }
  };

  const defaultColumns: ProColumns<PostBasicItem>[] = [
    {
      title: '聚合查询',
      dataIndex: 'aggregateQuery',
      hideInTable: true,
      formItemProps: {
        label: null,
      },
      search: {
        transform: (value: string[]) => {
          if (value.length > 0) {
            return {
              [value[0]]: value[1],
            };
          }
        },
      },
      renderFormItem: () => {
        return <AggregateQueryInput selectOptions={options} />;
      },
    },
    {
      title: '动态维度筛选',
      dataIndex: 'teamCodeList',
      hideInTable: true,
      formItemProps: {
        label: null,
      },
      renderFormItem: () => {
        return <DynamicTree />;
      },
    },
    {
      title: '作品信息',
      dataIndex: 'cover',
      hideInSearch: true,
      fixed: 'left',
      width: 336,
      className: 'video-info',
      render: (text, record) => {
        return (
          <Flex align="center" gap={5}>
            {record.cleanFlag ? (
              <>
                <div
                  style={{ position: 'relative' }}
                  onClick={() => {
                    window.open(record.shareLink, '_blank');
                  }}
                >
                  <img
                    src={record.cover || FailImg}
                    width={81}
                    height={108}
                    style={{ objectFit: 'contain' }}
                    referrerPolicy="no-referrer"
                  />
                  <Button
                    type="text"
                    size="large"
                    shape="circle"
                    style={{
                      position: 'absolute',
                      left: '50%',
                      top: '50%',
                      color: '#fff',
                      transform: 'translate(-50%, -50%)',
                      backgroundColor: 'rgba(0,0,0,.5)',
                    }}
                    icon={<CaretRightOutlined />}
                  />
                </div>
                <a
                  href={record.shareLink}
                  target="_blank"
                  rel="noreferrer"
                  style={{ width: '300px', wordBreak: 'break-all', margin: '0 auto' }}
                >
                  {record.title}
                </a>
                {record.systemReleaseFlag ? (
                  <Tooltip title="该作品由乾坤圈矩阵管理系统发布">
                    <div className="absolute bottom-[12px] right-[20px]">
                      <img src={LogoImg} className="pointer-events-none h-4 w-4" />
                    </div>
                  </Tooltip>
                ) : null}
              </>
            ) : (
              <Flex justify="center w-full" align="stretch" style={{ width: '100%' }}>
                <XGVideoCard
                  playableVideoUrl={record.url}
                  videoUrl={record.url}
                  coverUrl={record.cover || FailImg}
                  width={81}
                  height={108}
                  videoWidth={315}
                  videoHeight={562}
                  noShowTag={true}
                />
                <Flex
                  vertical
                  justify="space-between w-full"
                  gap={4}
                  style={{ marginLeft: '8px', width: '100%' }}
                >
                  <div
                    className="line-clamp-2 font-medium text-primary"
                    onClick={() => {
                      window.open(record.shareLink, '_blank');
                    }}
                  >
                    {record.title}
                  </div>
                  <Tooltip title={record.topic}>
                    {record.topic && (
                      <div className="line-clamp-1 text-xs font-normal text-black/60">
                        话题 {record.topic}
                      </div>
                    )}
                  </Tooltip>
                  <div className="text-xs font-normal text-black/60">
                    发布时间 {record.publishTime}
                  </div>
                  <div className="text-xs font-normal text-black/60">
                    作品时长 {formatSecond(record.duration)}
                  </div>
                  <Space
                    size="large"
                    className="relative w-full text-sm font-normal text-[#1e5eff]"
                  >
                    {record.url && (
                      <div onClick={() => handleDownload(record)} className="hover:cursor-pointer">
                        <DownloadOutlined /> 下载视频
                      </div>
                    )}
                    {record.qualityFlag === 1 && (
                      <div
                        onClick={() => {
                          window.open(
                            `${window.location.origin}/post/detail/${record.postId}/${record.platform}/${industryType}`,
                          );
                        }}
                        className="hover:cursor-pointer"
                      >
                        <Flex align="center" justify="center" gap={2}>
                          <FileTextIcon /> 详情
                        </Flex>
                      </div>
                    )}

                    {record.systemReleaseFlag ? (
                      <Tooltip title="该作品由乾坤圈矩阵管理系统发布">
                        <div className="absolute -top-[2px] right-3">
                          <img src={LogoImg} className="pointer-events-none h-4 w-4" />
                        </div>
                      </Tooltip>
                    ) : null}
                  </Space>
                </Flex>
              </Flex>
            )}
          </Flex>
        );
      },
    },
    {
      title: '账号信息',
      dataIndex: 'nickname',
      width: 300,
      ellipsis: true,
      fixed: 'left',
      hideInSearch: true,
      align: 'left',
      filterIcon: () => (
        <FilterFilled style={{ color: getBlueVipFlag() !== undefined ? '#3B77F6' : undefined }} />
      ),
      filterDropdown: (props: FilterDropdownProps) => {
        return (
          <FilterDropdownRadio
            title="是否是蓝V"
            options={[
              { label: '是', value: BlueVipFlag.Vip },
              { label: '否', value: BlueVipFlag.NoVip },
            ]}
            filterDropdownProps={props}
            setValueChange={setBlueVipFlag}
          />
        );
      },
      render: (text, record) => {
        return (
          <Flex vertical gap={5}>
            <Space size="small">
              <img
                src={record.avatar || DefaultAvatar}
                alt="avatar"
                style={{ width: '16px', height: '16px' }}
                referrerPolicy="no-referrer"
              />
              <span>{record.nickname}</span>
              {record.blueVipFlag ? <img src={BlueVip} width={18} /> : null}
            </Space>
            <Space>
              <div className="text-xs font-normal text-black/60">
                {AccountPlatformEnum[platform]?.text}ID: {record.showAccountId}
              </div>
              <Tooltip title="复制账号ID">
                <Copy
                  size={13}
                  className="mt-1 hover:cursor-pointer"
                  onClick={() => {
                    if (record.showAccountId) {
                      copy(record.showAccountId);
                      message.success('账号ID已复制到剪切板');
                    }
                  }}
                />
              </Tooltip>
            </Space>
            <div>
              <TagsShow tags={record.tags} />
            </div>
          </Flex>
        );
      },
    },
    {
      title: '视频类型',
      key: 'videoType',
      dataIndex: 'videoType',
      width: 110,
      align: 'right',
      // 默认先设置隐藏
      hideInSearch: !showAnalyzedProduct,
      hideInTable: !showAnalyzedProduct,
      renderFormItem() {
        return (
          <ProFormSelect
            placeholder="视频类型"
            request={async () => {
              if (!projectId) {
                return [];
              }
              const res = await getVideoType({
                projectId,
                industryType: industryType ? Number(industryType) : undefined,
              });
              return (
                res?.data?.map((item) => {
                  return {
                    label: item,
                    value: item,
                  };
                }) || []
              );
            }}
          />
        );
      },
    },
    {
      title: Number(industryType) === IndustryType.E_COMMERCE ? '画面类型' : '镜头类型',
      width: 356,
      dataIndex: 'screenTypes',
      key: 'screenTypes',
      align: 'right',
      // 默认先设置隐藏
      hideInSearch: !showAnalyzedProduct,
      hideInTable: !showAnalyzedProduct,
      render: (_, rec) => {
        return (
          <Popover content={rec?.screenTypes?.join('、')}>
            <div className="limit-decs">
              {rec?.screenTypes?.map((item) => {
                return (
                  <div key={item + rec?.title} className="screen-tag bg-[#edf3ff] text-[#1e5eff]">
                    {item}
                  </div>
                );
              })}
            </div>
          </Popover>
        );
      },
      renderFormItem() {
        return (
          <ProFormSelect
            placeholder={Number(industryType) === IndustryType.E_COMMERCE ? '画面类型' : '镜头类型'}
            request={async () => {
              if (!projectId) {
                return [];
              }
              const res = await getScreenType({
                projectId,
                industryType: industryType ? Number(industryType) : undefined,
              });
              return (
                res?.data?.map((item) => {
                  return {
                    label: item,
                    value: item,
                  };
                }) || [
                  {
                    label: '-',
                    value: undefined,
                  },
                ]
              );
            }}
          />
        );
      },
    },
    {
      title: Number(industryType) === IndustryType.E_COMMERCE ? '商品' : '车型',
      key: 'productInfo',
      dataIndex: 'productInfo',
      width: 110,
      align: 'right',
      // 默认先设置隐藏
      hideInSearch: !showAnalyzedProduct,
      hideInTable: !showAnalyzedProduct,
      renderFormItem() {
        return (
          <ProFormSelect
            placeholder={Number(industryType) === IndustryType.E_COMMERCE ? '商品' : '车型'}
            request={async () => {
              if (!projectId) {
                return [];
              }
              const res = await getCarInfo({
                industryType: industryType ? Number(industryType) : undefined,
                projectId,
              });
              return (
                res?.data?.map((item) => {
                  return {
                    label: item,
                    value: item,
                  };
                }) || []
              );
            }}
          />
        );
      },
    },
    {
      dataIndex: 'tagIds',
      fieldProps: {
        placeholder: '账号标签',
      },
      formItemProps: {
        label: null,
      },
      hideInTable: true,
      renderFormItem: () => {
        return <TagsSelect />;
      },
    },
    {
      title: '作品时长',
      dataIndex: 'duration',
      align: 'right',
      width: 130, // 转成xx小时xx分钟xx秒的最大宽度
      renderText: (text) => {
        const formatTime = formatSecond(text);
        return <span>{formatTime}</span>;
      },
      search: false,
      sorter: true,
    },
    {
      title: '互动量',
      dataIndex: 'interactiveCount',
      align: 'right',
      width: 100,
      hideInSearch: true,
      render: (text, record) => formatNum(record.interactiveCount),
      sorter: true,
    },
    {
      title: '播放量',
      dataIndex: 'playCount',
      align: 'right',
      width: 100,
      hideInSearch: true,
      render: (text, record) => formatNum(record.playCount),
      sorter: true,
    },
    {
      title: '点赞数',
      dataIndex: 'diggCount',
      align: 'right',
      width: 100,
      hideInSearch: true,
      render: (text, record) => formatNum(record.diggCount),
      sorter: true,
    },
    {
      title: '评论数',
      dataIndex: 'commentCount',
      align: 'right',
      width: 100,
      hideInSearch: true,
      render: (text, record) => formatNum(record.commentCount),
      sorter: true,
    },
    {
      title: '收藏数',
      dataIndex: 'collectCount',
      align: 'right',
      width: 100,
      hideInSearch: true,
      render: (text, record) => formatNum(record.collectCount),
      sorter: true,
    },
    {
      title: '分享数',
      dataIndex: 'shareCount',
      align: 'right',
      width: 100,
      hideInSearch: true,
      render: (text, record) => formatNum(record.shareCount),
      sorter: true,
    },
  ];

  const actionRef = useRef<ActionType>();
  const formRef = useRef<ProFormInstance>();
  const [columns, setColumns] = useState<ProColumns<PostBasicItem>[]>(defaultColumns);
  const filedIdArrRef = useRef<string[]>([]);
  const firstAddColumns = useRef(false);
  const [dataFilterState, setDataFilterState] = useSetState(initDataFilter);
  const teamFieldListRef = useGetTeamFieldList(projectId);
  const paramsRef = useRef<any>({});
  const { pollingExport, percent, pollingLoading } = usePollingExport();
  const [isEmptyTable, setIsEmptyTable] = useState(false);

  // 导出数据量大特殊处理
  const handleExportPost = async () => {
    const omitPageParams = omit(paramsRef.current, ['pageSize', 'current']);
    const res = await ExportPost({
      ...omitPageParams,
    });
    if (res.data) {
      pollingExport({ taskId: res.data });
    } else {
      message.error('导出失败,请重试');
    }
  };

  useEffect(() => {
    const SWITCH_COLUMN_KEYS = ['videoType', 'screenTypes', 'productInfo'];

    if (enableWorkAnalysis) {
      const newColumns = columns.map((item) => {
        if (SWITCH_COLUMN_KEYS.includes(item.dataIndex as string)) {
          return {
            ...item,
            hideInTable: !showAnalyzedProduct,
            hideInSearch: !showAnalyzedProduct,
          };
        }
        return item;
      }) as ProColumns<PostBasicItem>[];
      setColumns(newColumns);
    }
  }, [showAnalyzedProduct]);

  useEffect(() => {
    setColumns(defaultColumns);
    firstAddColumns.current = false;
  }, [platform]);

  return (
    <ProCardWrapper>
      <ProTable<PostBasicItem>
        columns={columns}
        actionRef={actionRef}
        formRef={formRef}
        params={{
          projectId,
          startTime: rangeTime[0],
          endTime: rangeTime[1],
          postPlayCount: dataFilterState.videoFilter ? dataFilterState.postPlayCount : undefined,
          duration: dataFilterState.postDurationFilter
            ? (dataFilterState.postDuration || 0) * 60
            : undefined,
          isAnalysis: showAnalyzedProduct,
          platform,
          blueVipFlag,
        }}
        onReset={() => {
          setDataFilterState(initDataFilter);
        }}
        ghost
        sticky={{ offsetHeader: STICKY_OFFSETHEADER }}
        tableClassName="custom-table"
        scroll={{ x: isEmptyTable ? getSumColumnsWidth(columns) : 'max-content' }}
        postData={(data: PostBasicItem[]) => {
          // 判断表格数据是否为空
          data.length === 0 ? setIsEmptyTable(true) : setIsEmptyTable(false);
          if (!firstAddColumns.current && data.length > 0) {
            renderCustomColumns(teamFieldListRef.current).then(({ customColumns, fieldIdArr }) => {
              filedIdArrRef.current = fieldIdArr;
              setColumns((prev) => {
                const SWITCHER_FEATURE_KEYS = ['videoType', 'productInfo', 'screenTypes'];
                const cols = prev.map((item) => {
                  if (SWITCHER_FEATURE_KEYS.includes((item.dataIndex || '') as string)) {
                    return {
                      ...item,
                      hideInTable: enableWorkAnalysis === false || !showAnalyzedProduct,
                      hideInSearch: enableWorkAnalysis === false || !showAnalyzedProduct,
                    };
                  }
                  return item;
                });

                return [...cols, ...customColumns] as ProColumns<PostBasicItem>[];
              });
            });
            firstAddColumns.current = true;
          }
          // 将teamFields内的字段都抽出来
          const formatData = data.map((item) => {
            const res = { ...item };
            const teamFields = res.teamFieldList;
            if (teamFields) {
              teamFields.forEach((field) => {
                (res as any)[field.fieldId] = field.value;
              });
            }
            return res;
          });
          return formatData;
        }}
        beforeSearchSubmit={(params) => {
          const fieldList = transferParams(params, filedIdArrRef.current);
          if (fieldList.length > 0) {
            return { ...params, fieldList };
          }
          return params;
        }}
        columnsState={{
          persistenceKey: 'ProductSetting-Table-Columns',
          persistenceType: 'localStorage',
        }}
        request={(params, sorter, filter) => {
          const processedParams = showAnalyzedProduct
            ? params
            : {
                ...params,
                productInfo: undefined,
                screenTypes: undefined,
                videoType: undefined,
              };

          paramsRef.current = processedParams;
          return proTableRequestAdapter(processedParams, sorter, filter, PostSettingPage);
        }}
        search={{ ...proTableSearchConfig }}
        options={{ ...proTableOptionsConfig }}
        pagination={{ ...proTablePaginationConfig }}
        headerTitle={
          <Space>
            <DataFilter
              dataFilterState={dataFilterState}
              setDataFilterState={setDataFilterState}
              type="post"
            />
          </Space>
        }
        toolBarRender={() => [
          enableWorkAnalysis ? (
            <Space>
              查看已分析作品
              <Switch
                checkedChildren="开启"
                unCheckedChildren="关闭"
                defaultChecked={showAnalyzedProduct}
                onChange={(status) => {
                  setShowAnalyzedProduct(status);
                  if (actionRef.current && actionRef.current.reloadAndRest) {
                    actionRef.current.reloadAndRest();
                  }
                }}
              />
            </Space>
          ) : null,
          <ExportButton
            exportFn={() => handleExportPost()}
            loading={pollingLoading}
            percent={percent}
            key="export"
          />,
        ]}
        rowKey={(rec) => `${rec.postId}-${enableWorkAnalysis}`}
        dateFormatter="string"
        expandable={{
          expandIcon:
            enableWorkAnalysis && showAnalyzedProduct
              ? ({ expanded, onExpand, record }) =>
                  expanded ? (
                    <CaretUpOutlined onClick={(e) => onExpand(record, e)} />
                  ) : (
                    <CaretDownOutlined onClick={(e) => onExpand(record, e)} />
                  )
              : undefined,
          expandedRowRender:
            enableWorkAnalysis && showAnalyzedProduct
              ? (record) => {
                  const hasFormula = record.scriptFormula && record.scriptFormula !== '-';
                  return (
                    <Flex key={record.title} vertical style={{ fontSize: 14 }}>
                      <Row style={{ marginBottom: 30 }}>
                        <Col span={2}>脚本公式</Col>
                        <Col span={21}>
                          {hasFormula ? (
                            <span>
                              {record.scriptFormula?.split('+').map((formulaPart, i) => {
                                return (
                                  <div key={formulaPart + record.title} className="formula-part">
                                    {i > 0 && <span style={{ margin: '0 5px' }}>+</span>}
                                    <span className="formula-part-name text-[#0e1015] after:bg-[#1e5eff]">
                                      {formulaPart}
                                    </span>
                                  </div>
                                );
                              })}
                            </span>
                          ) : (
                            '-'
                          )}
                        </Col>
                      </Row>
                      <Row>
                        <Col span={2}>本视频脚本</Col>
                        <Col span={21}>
                          <span style={{ color: '#64666B' }}>{record.subtitles}</span>
                        </Col>
                      </Row>
                    </Flex>
                  );
                }
              : undefined,
        }}
      />
    </ProCardWrapper>
  );
};

export default BasicData;
