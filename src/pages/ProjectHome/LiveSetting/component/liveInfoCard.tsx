import Clean from '@/assets/clean.png';
import FailImg from '@/assets/fail-img.png';
import RecordImg from '@/assets/record-img.png';
import NotRecord from '@/assets/not-record.png';
import XGVideoCard from '@/components/xgVideoCard';
import { LiveBasicItem, LiveBasicItemEcommerce } from '@/services/setting';
import { Flex, Space } from 'antd';
import dayjs from 'dayjs';
import { formatSecond } from '@/utils/time';
import { saveAs } from 'file-saver';
import { DownloadOutlined } from '@ant-design/icons';
import { FileTextIcon } from '@radix-ui/react-icons';
import { isEmpty } from 'lodash-es';
import { Link } from '@umijs/max';

type LiveInfoCardProps = {
  record: LiveBasicItem | LiveBasicItemEcommerce;
  industryType?: string;
};

export const videoType = (liveData: LiveBasicItem) => {
  if (liveData.isPullUrl === 0) {
    return 'mp4';
  } else {
    return 'm3u8';
  }
};

export default function LiveInfoCard({ record, industryType }: LiveInfoCardProps) {
  const replayUrlsArr = record.isPullUrl === 0 ? [record.videoUrl] : record.liveReplay;
  const renderImg = () => {
    if (record.cleanFlag === 1) {
      return <img src={Clean} width={81} height={108} style={{ objectFit: 'cover' }} />;
    } else if (replayUrlsArr && replayUrlsArr.length > 0) {
      return (
        <XGVideoCard
          playableVideoUrl={replayUrlsArr[0]}
          videoUrl={replayUrlsArr[0]}
          coverUrl={record.cover || FailImg}
          width={81}
          height={108}
          videoWidth={315}
          videoHeight={562}
          type={videoType(record)}
          noShowTag={true}
        />
      );
    } else {
      // 如果当前时间大于直播结束时间一小时 就显示 -
      const currentTime = dayjs();
      const liveEndTime = dayjs(record.liveEndTime);
      const hourDifference = currentTime.diff(liveEndTime, 'hour');
      if (hourDifference > 1) {
        return <img src={NotRecord} width={81} height={108} style={{ objectFit: 'cover' }} />;
      } else {
        return <img src={RecordImg} width={81} height={108} style={{ objectFit: 'cover' }} />;
      }
    }
  };
  return (
    <Flex align="center" gap={5}>
      <Flex justify="center" align="stretch">
        {renderImg()}
        <Flex vertical justify="space-between" gap={4} style={{ marginLeft: '8px' }}>
          <div className="line-clamp-2 font-medium text-primary">{record.title}</div>
          <div className="text-xs font-normal text-black/60">
            {record.liveStartTime}~{record.liveEndTime}
          </div>
          <div className="text-xs font-normal text-black/60">
            历时{formatSecond(record.liveDuration)}
          </div>
          <Space size="large" className="text-sm font-normal text-[#1e5eff]">
            {replayUrlsArr && replayUrlsArr?.length > 0 && record.cleanFlag == 0 && (
              <div
                onClick={async () => {
                  for (const [index, url] of replayUrlsArr.entries()) {
                    if (url) {
                      saveAs(url, `video-${index}.m3u8`);
                    }
                  }
                }}
                className="flex items-center gap-[2px] hover:cursor-pointer"
              >
                <DownloadOutlined /> 下载视频
              </div>
            )}
            {(!isEmpty(record.liveReplay) || !isEmpty(record.videoUrl)) && (
              <Link
                to={`/live/detail/${record.roomId}/${record.platform}/${industryType}`}
                target="_blank"
                style={{ textDecoration: 'none', color: 'inherit' }}
              >
                <div className="flex items-center gap-[2px] hover:cursor-pointer">
                  <FileTextIcon /> 直播复盘
                </div>
              </Link>
            )}
          </Space>
        </Flex>
      </Flex>
    </Flex>
  );
}
