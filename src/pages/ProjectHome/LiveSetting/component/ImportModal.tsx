import { DouyinLiveExternalTemplate } from '@/services/setting';
import { InboxOutlined } from '@ant-design/icons';
import { ActionType } from '@ant-design/pro-components';
import { Button, message, Modal, Upload } from 'antd';
import saveAs from 'file-saver';

type ImportModalProps = {
  open: boolean;
  closeModal: () => void;
  actionRef: React.MutableRefObject<ActionType | undefined>;
  projectId?: string;
};

const ImportModal = ({ open, closeModal, actionRef, projectId }: ImportModalProps) => {
  const uploadProps = {
    name: 'file',
    maxCount: 1,
    multiple: false,
    accept: '.xlsx,.xls',
    action: `/new-media-api/douyin/live/external/import?projectId=${projectId}`,
    headers: {
      contentType: 'multipart/form-data',
    },
    showUploadList: false,
    onChange: (info: any) => {
      const { status } = info.file;
      if (status === 'done') {
        const respond = info.file.response;
        if (respond.code !== 0) {
          message.error(`${info.file.name} 文件上传失败, 错误原因: ${respond.msg}`);
        } else {
          message.success(`${info.file.name} 文件上传成功`);
        }
        closeModal();
        actionRef.current?.reload();
      } else if (status === 'error') {
        message.error(`${info.file.name} 文件上传失败`);
      }
    },
  };

  return (
    <Modal title="导入数据" open={open} footer={null} onCancel={closeModal}>
      <div className="mt-5">
        <div className="mb-6">
          <h3 className="mb-4 text-base font-medium">
            第一步：下载【批量导入数据】并按照规范填写信息
          </h3>
          <div className="flex items-center justify-between">
            <div> 批量导入数据模版.xlsx</div>
            <Button
              type="link"
              onClick={async () => {
                const downloadBlob = await DouyinLiveExternalTemplate();
                saveAs(downloadBlob, '导入模版.xlsx');
              }}
            >
              下载模版
            </Button>
          </div>
        </div>
        <div>
          <h3 className="mb-4 text-base font-medium">第二步：将信息填入表格后，上传模版</h3>
          <Upload.Dragger {...uploadProps}>
            <p className="ant-upload-drag-icon">
              <InboxOutlined />
            </p>
            <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
          </Upload.Dragger>
          <div className="mt-2 text-xs text-gray-400">支持.xlsx格式文件</div>
        </div>
      </div>
    </Modal>
  );
};

export default ImportModal;
