import AggregateQueryInput from '@/components/aggregateQueryInput';
import DynamicTree from '@/components/dynamicTree';
import ExportButton from '@/components/exportButton';
import FilterDropdownRadio from '@/components/filterDropdownRadio';
import { renderCustomColumns } from '@/components/renderCustomColumns';
import TagsSelect from '@/components/tags/tagsSelect';
import useGetTeamFieldList from '@/hooks/useFields';
import { usePollingExport } from '@/hooks/usePollingExport';
import { BlueVipFlag } from '@/services/common';
import {
  ExportEcommerceLive,
  IProjectLive,
  LiveBasicItemEcommerce,
  LiveSettingPageEcommerce,
  LiveSettingPageEcommerceHightLightExport,
} from '@/services/setting';
import { proTableRequestAdapter } from '@/utils';
import { formatNum, STICKY_OFFSETHEADER, transferParams } from '@/utils/common';
import { TOYOTA } from '@/utils/constant';
import {
  proTableOptionsConfig,
  proTablePaginationConfig,
  proTableSearchConfig,
} from '@/utils/proTableConfig';
import { getSumColumnsWidth } from '@/utils/table';
import { FilterFilled } from '@ant-design/icons';
import {
  ActionType,
  ColumnsState,
  ProCard,
  ProColumns,
  ProFormInstance,
  ProTable,
} from '@ant-design/pro-components';
import { useGetState } from 'ahooks';
import { message } from 'antd';
import { FilterDropdownProps } from 'antd/es/table/interface';
import { useAtomValue } from 'jotai';
import { omit } from 'lodash-es';
import { useEffect, useRef, useState } from 'react';
import { selectPlatformAtom } from '../../atom';
import AccountInfo from './accountInfo';
import LiveInfoCard from './liveInfoCard';

type EcommerceDataProps = {
  projectId?: string;
  industryType?: string;
  rangeTime: (string | undefined)[];
};

export default function EcommerceData(props: EcommerceDataProps) {
  const { projectId, rangeTime, industryType } = props;
  const platform = useAtomValue(selectPlatformAtom);
  const options = [
    { value: 'nickname', label: '账号名称' },
    { value: 'title', label: '直播标题' },
    { value: 'roomId', label: '直播间ID' },
  ];
  const [blueVipFlag, setBlueVipFlag, getBlueVipFlag] = useGetState<BlueVipFlag | undefined>();

  const defaultColumns: ProColumns<LiveBasicItemEcommerce>[] = [
    {
      title: '聚合查询',
      dataIndex: 'aggregateQuery',
      hideInTable: true,
      formItemProps: {
        label: null,
      },
      search: {
        transform: (value: string[]) => {
          if (value.length > 0) {
            return {
              [value[0]]: value[1],
            };
          }
        },
      },
      renderFormItem: () => {
        return <AggregateQueryInput selectOptions={options} />;
      },
    },
    {
      title: '动态维度筛选',
      dataIndex: 'teamCodeList',
      hideInTable: true,
      formItemProps: {
        label: null,
      },
      renderFormItem: () => {
        return <DynamicTree />;
      },
    },
    {
      title: '直播信息',
      dataIndex: 'cover',
      hideInSearch: true,
      fixed: 'left',
      width: 336,
      className: 'live-info',
      render: (text, record) => <LiveInfoCard record={record} industryType={industryType} />,
    },
    {
      title: '账号信息',
      dataIndex: 'nickname',
      width: 300,
      className: 'table-300-col',
      ellipsis: true,
      fixed: 'left',
      align: 'left',
      hideInSearch: true,
      filterIcon: () => (
        <FilterFilled style={{ color: getBlueVipFlag() !== undefined ? '#3B77F6' : undefined }} />
      ),
      filterDropdown: (props: FilterDropdownProps) => {
        return (
          <FilterDropdownRadio
            title="是否是蓝V"
            options={[
              { label: '是', value: BlueVipFlag.Vip },
              { label: '否', value: BlueVipFlag.NoVip },
            ]}
            filterDropdownProps={props}
            setValueChange={setBlueVipFlag}
          />
        );
      },
      render: (text, record) => <AccountInfo record={record} platform={platform} />,
    },
    {
      title: '直播间 ID',
      dataIndex: 'roomId',
      hideInSearch: true,
      align: 'right',
    },
    {
      dataIndex: 'tagIds',
      fieldProps: {
        placeholder: '账号标签',
      },
      formItemProps: {
        label: null,
      },
      hideInTable: true,
      renderFormItem: () => {
        return <TagsSelect />;
      },
    },
    {
      title: '直播时长筛选',
      dataIndex: 'liveDuration',
      hideInTable: true,
      fieldProps: {
        placeholder: '直播时长≥(单位分钟)',
      },
      valueType: 'digit',
      search: {
        transform: (value: number) => {
          return {
            liveDuration: Number(value) * 60,
          };
        },
      },
    },
    {
      title: '直播状态筛选',
      dataIndex: 'liveStatus',
      hideInTable: true,
      fieldProps: {
        placeholder: '直播状态',
      },
      valueType: 'select',
      valueEnum: {
        1: '直播中',
        0: '已结束',
      },
    },
    {
      title: '直播时长是否≥25min',
      dataIndex: 'over25minLiveDurationStatus',
      align: 'right',
      hideInSearch: true,
      width: 180,
      hidden: projectId === TOYOTA,
      valueEnum: {
        0: '否',
        1: '是',
      },
    },
    {
      title: '直播时长是否≥60min',
      dataIndex: 'over60minLiveDurationStatus',
      align: 'right',
      hideInSearch: true,
      width: 180,
      hidden: projectId !== TOYOTA,
      valueEnum: {
        0: '否',
        1: '是',
      },
    },
    {
      title: '观看次数',
      dataIndex: 'viewTime',
      width: 100,
      align: 'right',
      hideInSearch: true,
      render: (text, record) => formatNum(record.viewTime),
      sorter: true,
    },
    {
      title: '评论次数',
      dataIndex: 'commentCount',
      align: 'right',
      width: 150,
      hideInSearch: true,
      render: (text, record) => formatNum(record.commentCount),
      sorter: true,
    },
    {
      title: '点赞次数',
      dataIndex: 'diggCount',
      width: 100,
      align: 'right',
      hideInSearch: true,
      render: (text, record) => formatNum(record.diggCount),
      sorter: true,
    },
    {
      title: '整体消耗',
      dataIndex: 'statCost',
      align: 'right',
      width: 150,
      hideInSearch: true,
      sorter: true,
    },
    {
      title: '整体成交金额',
      dataIndex: 'totalPayOrderGmvIncludeCoupon',
      align: 'right',
      width: 150,
      hideInSearch: true,
      sorter: true,
    },
    {
      title: '整体成交订单数',
      dataIndex: 'totalPayOrderCount',
      align: 'right',
      width: 150,
      hideInSearch: true,
      render: (text, record) => formatNum(record.totalPayOrderCount),
      sorter: true,
    },
    {
      title: '整体支付ROI',
      dataIndex: 'totalPayOrderRoi',
      align: 'right',
      width: 150,
      hideInSearch: true,
      sorter: true,
    },
    {
      title: '整体成交订单成本',
      dataIndex: 'totalCostPerPayOrder',
      align: 'right',
      width: 150,
      hideInSearch: true,
      sorter: true,
    },
    {
      title: '用户实际支付金额',
      dataIndex: 'totalPayOrderGmv',
      align: 'right',
      width: 150,
      hideInSearch: true,
      sorter: true,
    },
    {
      title: '整体成交智能优惠券金额',
      dataIndex: 'totalPayOrderCouponAmount',
      align: 'right',
      width: 200,
      hideInSearch: true,
      sorter: true,
    },
  ];

  const actionRef = useRef<ActionType>();
  const formRef = useRef<ProFormInstance>();
  const [columns, setColumns] = useState<ProColumns<LiveBasicItemEcommerce>[]>(defaultColumns);
  const filedIdArrRef = useRef<string[]>([]);
  const firstAddColumns = useRef(false);
  const teamFieldListRef = useGetTeamFieldList(projectId);
  const paramsRef = useRef<any>({});
  const { pollingExport, percent, pollingLoading } = usePollingExport('直播数据导出');
  const {
    pollingExport: pollingExportHighLight,
    percent: percentHighLight,
    pollingLoading: pollingLoadingHighLight,
  } = usePollingExport('高光片段数据导出');
  const [columnsStateMap, setColumnsStateMap] = useState<Record<string, ColumnsState>>({
    roomId: {
      show: false,
    },
  });
  const [isEmptyTable, setIsEmptyTable] = useState(false);

  // 导出数据量大特殊处理
  const handleExportLive = async () => {
    const omitPageParams = omit(paramsRef.current, ['pageSize', 'current']);
    const res = await ExportEcommerceLive({
      ...omitPageParams,
    });
    if (res.data) {
      pollingExport({ taskId: res.data });
    } else {
      message.error('导出失败,请重试');
    }
  };

  useEffect(() => {
    setColumns(defaultColumns);
    firstAddColumns.current = false;
  }, [platform]);

  const handleExportHighLight = async () => {
    const omitPageParams = omit(paramsRef.current, ['pageSize', 'current']) as IProjectLive;
    const res = await LiveSettingPageEcommerceHightLightExport({
      ...omitPageParams,
    });
    if (res.data) {
      pollingExportHighLight({ taskId: res.data });
    } else {
      message.error('导出失败,请重试');
    }
  };

  return (
    <ProCard>
      <ProTable<LiveBasicItemEcommerce>
        columns={columns}
        actionRef={actionRef}
        formRef={formRef}
        ghost
        params={{
          projectId,
          startTime: rangeTime[0],
          endTime: rangeTime[1],
          platform,
          blueVipFlag,
        }}
        sticky={{ offsetHeader: STICKY_OFFSETHEADER }}
        tableClassName="custom-table"
        scroll={{ x: isEmptyTable ? getSumColumnsWidth(columns) : 'max-content' }}
        postData={(data: LiveBasicItemEcommerce[]) => {
          // 判断表格数据是否为空
          data.length === 0 ? setIsEmptyTable(true) : setIsEmptyTable(false);
          if (!firstAddColumns.current && data.length > 0) {
            renderCustomColumns(teamFieldListRef.current).then(({ customColumns, fieldIdArr }) => {
              filedIdArrRef.current = fieldIdArr;
              const newColumns = [
                ...columns,
                ...customColumns,
              ] as ProColumns<LiveBasicItemEcommerce>[];
              setColumns(newColumns);
            });
            firstAddColumns.current = true;
          }
          // 将teamFields内的字段都抽出来
          const formatData = data.map((item) => {
            const res = { ...item };
            const teamFields = res.teamFieldList;
            if (teamFields) {
              teamFields.forEach((field) => {
                (res as any)[field.fieldId] = field.value;
              });
            }
            return res;
          });
          return formatData;
        }}
        beforeSearchSubmit={(params) => {
          const fieldList = transferParams(params, filedIdArrRef.current);
          if (fieldList.length > 0) {
            return { ...params, fieldList };
          }
          return params;
        }}
        request={(params, sorter, filter) => {
          paramsRef.current = params;
          return proTableRequestAdapter(params, sorter, filter, LiveSettingPageEcommerce);
        }}
        search={{ ...proTableSearchConfig }}
        options={{ ...proTableOptionsConfig }}
        pagination={{ ...proTablePaginationConfig }}
        toolBarRender={() => [
          <ExportButton
            exportFn={handleExportHighLight}
            loading={pollingLoadingHighLight}
            percent={percentHighLight}
            text="导出高光"
            key="export-highlight"
          />,
          <ExportButton
            exportFn={handleExportLive}
            loading={pollingLoading}
            percent={percent}
            key="export"
          />,
        ]}
        // @ts-ignore
        rowKey={(record) => record?.roomId + Math.random()}
        columnsState={{
          value: columnsStateMap,
          onChange: setColumnsStateMap,
          persistenceKey: 'LiveSetting-Ecommerce-Table-Columns',
          persistenceType: 'localStorage',
        }}
        dateFormatter="string"
      />
    </ProCard>
  );
}
