import { Col, Row } from 'antd';
import DataCard from '@/components/dataCard';
import { CardTrend } from '@/services/data-card';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  rectSortingStrategy,
} from '@dnd-kit/sortable';
import { SortableItem } from './sortableItem';
import { useState, useEffect } from 'react';
import { SetState } from 'ahooks/lib/createUseStorageState';

type DataCardItem = {
  id: string;
  title: string;
  data?: CardTrend;
  loading: boolean;
  isTimeType?: boolean;
};

type VisualDataProps = {
  dataCardList: DataCardItem[];
  setSortedIds: (this: unknown, value: SetState<string[]>) => void;
};

export default function VisualData({ dataCardList, setSortedIds }: VisualDataProps) {
  const [items, setItems] = useState(dataCardList);

  useEffect(() => {
    setItems(dataCardList);
  }, [dataCardList]);

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    }),
  );

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      setItems((items) => {
        const oldIndex = items.findIndex((item) => item.id === active.id);
        const newIndex = items.findIndex((item) => item.id === over.id);
        const newItems = arrayMove(items, oldIndex, newIndex);
        setSortedIds(newItems.map((item) => item.id));
        return newItems;
      });
    }
  };

  return (
    <div className="-ml-[8px] py-4">
      <DndContext sensors={sensors} collisionDetection={closestCenter} onDragEnd={handleDragEnd}>
        <Row gutter={[16, 16]}>
          <SortableContext items={items.map((item) => item.id)} strategy={rectSortingStrategy}>
            {items.map((item) => (
              <Col xs={24} sm={12} md={8} lg={6} key={item.id}>
                <SortableItem id={item.id}>
                  <DataCard
                    title={item.title}
                    data={item.data}
                    loading={item.loading}
                    {...(item.isTimeType ? { isTimeType: true } : {})}
                    className="h-[150px]"
                  />
                </SortableItem>
              </Col>
            ))}
          </SortableContext>
        </Row>
      </DndContext>
    </div>
  );
}
