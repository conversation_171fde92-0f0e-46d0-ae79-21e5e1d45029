import { Avatar, Flex, message, Space, Tooltip } from 'antd';
import { LiveBasicItem, LiveBasicItemEcommerce } from '@/services/setting';
import DefaultAvatar from '@/assets/default-avatar.png';
import BlueVip from '@/assets/blue-vip.png';
import { AccountPlatformEnum, PlatForm } from '@/utils/platform';
import { Copy } from 'lucide-react';
import copy from 'copy-to-clipboard';
import TagsShow from '@/components/tags/tagsShow';

type AccountInfoProps = {
  record: LiveBasicItem | LiveBasicItemEcommerce;
  platform: PlatForm;
};

export default function AccountInfo({ record, platform }: AccountInfoProps) {
  return (
    <Flex vertical gap={5}>
      <Space size="small">
        <Avatar
          size="small"
          src={record.avatar || DefaultAvatar}
          style={{ width: '16px', height: '16px' }}
        />
        <span>{record.nickname}</span>
        {record.blueVipFlag ? <img src={BlueVip} width={18} /> : null}
      </Space>
      <Space>
        <div className="text-xs font-normal text-black/60">
          {AccountPlatformEnum[platform]?.text}ID: {record.showAccountId}
        </div>
        <Tooltip title="复制账号ID">
          <Copy
            size={13}
            className="mt-1 hover:cursor-pointer"
            onClick={() => {
              if (record.showAccountId) {
                copy(record.showAccountId);
                message.success('账号ID已复制到剪切板');
              }
            }}
          />
        </Tooltip>
      </Space>
      <Space>
        <div className="text-xs font-normal text-black/60">
          {platform === PlatForm.Douyin && `抖音 uid ${record.uid}`}
        </div>
        <Tooltip title="复制账号ID">
          <Copy
            size={13}
            className="mt-1 hover:cursor-pointer"
            onClick={() => {
              if (record.uid) {
                copy(record.uid);
                message.success('抖音 uid 已复制到剪切板');
              }
            }}
          />
        </Tooltip>
      </Space>
      <div>
        <TagsShow tags={record.tags} />
      </div>
    </Flex>
  );
}
