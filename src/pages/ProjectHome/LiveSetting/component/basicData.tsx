import AggregateQueryInput from '@/components/aggregateQueryInput';
import DynamicTree from '@/components/dynamicTree';
import ExportButton from '@/components/exportButton';
import FilterDropdownRadio from '@/components/filterDropdownRadio';
import { renderCustomColumns } from '@/components/renderCustomColumns';
import TagsSelect from '@/components/tags/tagsSelect';
import useLiveAfkFG from '@/hooks/fg/useLiveAfkFG';
import useProjectFeature from '@/hooks/fg/useProjectFeature';
import useGetTeamFieldList from '@/hooks/useFields';
import { usePollingExport } from '@/hooks/usePollingExport';
import { BlueVipFlag } from '@/services/common';
import {
  ExportLive,
  getLiveQuotationRecordGroup,
  LiveBasicItem,
  LiveSettingPage,
} from '@/services/setting';
import { FunctionCode } from '@/services/system';
import { proTableRequestAdapter } from '@/utils';
import { formatNum, STICKY_OFFSETHEADER, transferParams } from '@/utils/common';
import { TagSpan } from '@/utils/commonStyle';
import { TOYOTA } from '@/utils/constant';
import { PlatForm } from '@/utils/platform';
import {
  proTableOptionsConfig,
  proTablePaginationConfig,
  proTableSearchConfig,
} from '@/utils/proTableConfig';
import { getSumColumnsWidth } from '@/utils/table';
import { formatSecond } from '@/utils/time';
import { FilterFilled } from '@ant-design/icons';
import {
  ActionType,
  ColumnsState,
  ProCard,
  ProColumns,
  ProFormInstance,
  ProTable,
} from '@ant-design/pro-components';
import { useGetState } from 'ahooks';
import { Button, message, Space, Switch } from 'antd';
import { FilterDropdownProps } from 'antd/es/table/interface';
import dayjs from 'dayjs';
import { useAtomValue } from 'jotai';
import { isNil, omit } from 'lodash-es';
import { FolderInput } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import { selectPlatformAtom } from '../../atom';
import AccountInfo from './accountInfo';
import ImportModal from './ImportModal';
import LiveInfoCard from './liveInfoCard';

type BasicDataProps = {
  projectId?: string;
  industryType?: string;
  rangeTime: (string | undefined)[];
};

const BasicData = (props: BasicDataProps) => {
  const { projectId, rangeTime, industryType } = props;
  // 如果 rangTime 是同一天并且等于今天
  const isToday =
    rangeTime[0] && rangeTime[1]
      ? dayjs(rangeTime[0]).isSame(rangeTime[1], 'day') &&
        dayjs(rangeTime[0]).isSame(dayjs(), 'day')
      : false;
  const [showAnalyzedLive, setShowAnalyzedLive] = useState<boolean>(false);
  /** 是否开启直播内容分析 */
  const { enable: enableLiveAnalysis } = useProjectFeature({
    featureId: FunctionCode.LiveAnalysis,
  });
  const platform = useAtomValue(selectPlatformAtom);
  const options = [
    { value: 'nickname', label: '账号名称' },
    { value: 'title', label: '直播标题' },
    { value: 'roomId', label: '直播间ID' },
  ];
  const [blueVipFlag, setBlueVipFlag, getBlueVipFlag] = useGetState<BlueVipFlag | undefined>();
  const [importModalOpen, setImportModalOpen] = useState(false);

  const defaultColumns: ProColumns<LiveBasicItem>[] = [
    {
      title: '聚合查询',
      dataIndex: 'aggregateQuery',
      hideInTable: true,
      formItemProps: {
        label: null,
      },
      search: {
        transform: (value: string[]) => {
          if (value.length > 0) {
            return {
              [value[0]]: value[1],
            };
          }
        },
      },
      renderFormItem: () => {
        return <AggregateQueryInput selectOptions={options} />;
      },
    },
    {
      title: '动态维度筛选',
      dataIndex: 'teamCodeList',
      hideInTable: true,
      formItemProps: {
        label: null,
      },
      renderFormItem: () => {
        return <DynamicTree />;
      },
    },
    {
      title: '直播信息',
      dataIndex: 'cover',
      hideInSearch: true,
      fixed: 'left',
      width: 336,
      className: 'live-info',
      render: (text, record) => <LiveInfoCard record={record} industryType={industryType} />,
    },
    {
      title: '账号信息',
      dataIndex: 'nickname',
      width: 300,
      className: 'table-300-col',
      ellipsis: true,
      fixed: 'left',
      align: 'left',
      hideInSearch: true,
      filterIcon: () => (
        <FilterFilled style={{ color: getBlueVipFlag() !== undefined ? '#3B77F6' : undefined }} />
      ),
      filterDropdown: (props: FilterDropdownProps) => {
        return (
          <FilterDropdownRadio
            title="是否是蓝V"
            options={[
              { label: '是', value: BlueVipFlag.Vip },
              { label: '否', value: BlueVipFlag.NoVip },
            ]}
            filterDropdownProps={props}
            setValueChange={setBlueVipFlag}
          />
        );
      },
      render: (text, record) => <AccountInfo record={record} platform={platform} />,
    },
    {
      title: '直播间 ID',
      dataIndex: 'roomId',
      hideInSearch: true,
      align: 'right',
    },
    {
      dataIndex: 'tagIds',
      fieldProps: {
        placeholder: '账号标签',
      },
      formItemProps: {
        label: null,
      },
      hideInTable: true,
      renderFormItem: () => {
        return <TagsSelect />;
      },
    },
    {
      title: '出镜情况',
      dataIndex: 'appearanceStatus',
      align: 'left',
      hidden: !showAnalyzedLive,
      hideInSearch: !showAnalyzedLive,
      valueType: 'select',
      fieldProps: {
        placeholder: '出镜情况',
      },
      formItemProps: {
        label: null,
      },
      width: 150,
      render: (text, record) => {
        return <span>{record.appearanceStatus}</span>;
      },
    },
    // 直播背景
    {
      title: '直播背景',
      dataIndex: 'liveBackground',
      align: 'left',
      valueType: 'select',
      hidden: !showAnalyzedLive,
      hideInSearch: !showAnalyzedLive,
      fieldProps: {
        placeholder: '直播背景',
      },
      formItemProps: {
        label: null,
      },
      width: 150,
      render: (text, record) => {
        return <span>{record.liveBackground}</span>;
      },
    },
    // 讲解车型
    {
      title: '讲解车型',
      dataIndex: 'carModel',
      align: 'left',
      valueType: 'select',
      hidden: !showAnalyzedLive,
      hideInSearch: !showAnalyzedLive,
      fieldProps: {
        placeholder: '讲解车型',
      },
      formItemProps: {
        label: null,
      },
      width: 150,
      render: (text, record) => {
        return <span>{record?.carModel?.join(', ') || '-'}</span>;
      },
    },
    {
      title: '空挂播',
      dataIndex: 'afkLevel',
      width: 130,
      align: 'left',
      tooltip:
        '空播率≥20%以上判定为疑似空播挂播，其中空播率≥50%为严重空播挂播，20%≤空播率＜50%为轻微空播挂播。次日更新判断结果',
      fieldProps: {
        placeholder: '是否疑似空挂播',
        mode: 'multiple',
      },
      formItemProps: {
        label: null,
      },
      valueType: 'select',
      valueEnum: {
        1: '严重空挂播',
        2: '轻微空挂播',
        3: '正常',
        4: '未判定',
      },
      search: {
        transform: (value: number[]) => {
          return {
            afkLevelList: value,
          };
        },
      },
      render: (text, record) => {
        const { afkLevel } = record;
        switch (afkLevel) {
          case 1:
            return (
              <TagSpan $bgColor="#fff2f0" $textColor="#ff4d4f">
                {text}
              </TagSpan>
            );
          case 2:
            return (
              <TagSpan $bgColor="#fbebe2" $textColor="#FF7533">
                {text}
              </TagSpan>
            );
          case 3:
            return (
              <TagSpan $bgColor="#dfe7fd" $textColor="#1E5EFF">
                {text}
              </TagSpan>
            );
          case 4:
            return (
              <TagSpan $bgColor="#ebeced" $textColor="#474a4e">
                {text}
              </TagSpan>
            );
        }
      },
    },
    {
      title: '直播时长筛选',
      dataIndex: 'liveDuration',
      hideInTable: true,
      fieldProps: {
        placeholder: '直播时长≥(单位分钟)',
      },
      valueType: 'digit',
      search: {
        transform: (value: number) => {
          return {
            liveDuration: Number(value) * 60,
          };
        },
      },
    },
    {
      title: '直播状态筛选',
      dataIndex: 'liveStatus',
      hideInTable: true,
      fieldProps: {
        placeholder: '直播状态',
      },
      valueType: 'select',
      valueEnum: {
        1: '直播中',
        0: '已结束',
      },
    },
    {
      title: '检测时长',
      dataIndex: 'liveAfkCheckDuration',
      align: 'right',
      tooltip: '用于检测空播挂播的片段时长',
      width: 150,
      hideInSearch: true,
      render: (text, record) => formatSecond(record.liveAfkCheckDuration),
    },
    {
      title: '空播时长',
      dataIndex: 'liveAfkDuration',
      align: 'right',
      tooltip: '画面无变化且讲解内容和直播无关的时长',
      width: 150,
      hideInSearch: true,
      render: (text, record) => formatSecond(record.liveAfkDuration, '未检测'),
      sorter: true,
    },
    {
      title: '空播率',
      dataIndex: 'liveAfkRate',
      align: 'right',
      tooltip: '空播时长/实际直播时长，由于实际直播时长需要次日更新，所以空播率也需要次日更新',
      width: 150,
      hideInSearch: true,
      render: (text, record) => {
        return (
          <span>
            {record.liveAfkRate
              ? (Number(record.liveAfkRate) * 100).toFixed(2) + '%'
              : record.liveAfkDuration
                ? '未判定'
                : '未检测'}
          </span>
        );
      },
    },
    {
      title: '直播时长是否≥25min',
      dataIndex: 'over25minLiveDurationStatus',
      align: 'right',
      hideInSearch: true,
      width: 180,
      hidden: projectId === TOYOTA,
      valueEnum: {
        0: '否',
        1: '是',
      },
    },
    {
      title: '直播时长是否≥60min',
      dataIndex: 'over60minLiveDurationStatus',
      align: 'right',
      hideInSearch: true,
      width: 180,
      hidden: projectId !== TOYOTA,
      valueEnum: {
        0: '否',
        1: '是',
      },
    },
    {
      title: '是否挂载转化组件',
      dataIndex: 'componentStatus',
      align: 'right',
      hideInSearch: true,
      width: 150,
      valueEnum: {
        0: '否',
        1: '是',
      },
    },
    {
      title: '投放消耗',
      dataIndex: 'adSpent',
      align: 'right',
      width: 150,
      hideInSearch: true,
    },
    {
      title: '投流CPL',
      dataIndex: 'adCpl',
      align: 'right',
      width: 150,
      hideInSearch: true,
    },
    {
      title: '曝光人数',
      dataIndex: 'exposureUcount',
      align: 'right',
      width: 150,
      hideInSearch: true,
      render: (text, record) => formatNum(record.exposureUcount),
      sorter: true,
    },
    {
      title: '曝光次数',
      dataIndex: 'exposureCount',
      align: 'right',
      width: 150,
      hideInSearch: true,
      render: (text, record) => formatNum(record.exposureCount),
      sorter: true,
    },
    {
      title: '观看次数',
      dataIndex: 'viewTime',
      width: 90,
      align: 'right',
      hideInSearch: true,
      render: (text, record) => formatNum(record.viewTime),
      sorter: true,
    },
    {
      title: '观看人数',
      dataIndex: 'viewCount',
      width: 90,
      align: 'right',
      hideInSearch: true,
      render: (text, record) => formatNum(record.viewCount),
      sorter: true,
    },
    {
      title: '曝光进入率',
      dataIndex: 'exposureEntryRate',
      align: 'right',
      width: 150,
      hideInSearch: true,
      render: (_, record) =>
        isNil(record.exposureEntryRate) ? '--' : `${record.exposureEntryRate}%`,
    },
    {
      title: 'PCU',
      tooltip: '峰值在线人数',
      dataIndex: 'peakConcurrentUsers',
      align: 'right',
      width: 150,
      hideInSearch: true,
    },
    {
      title: 'ACU',
      tooltip: '平均在线人数',
      dataIndex: 'avgConcurrentUsers',
      align: 'right',
      width: 150,
      hideInSearch: true,
    },
    {
      title: '广告流量观看次数',
      dataIndex: 'adFlowCategoryWatchCount',
      align: 'right',
      width: 150,
      hideInSearch: true,
      render: (text, record) => formatNum(record.adFlowCategoryWatchCount),
      sorter: true,
    },
    {
      title: '自然流量观看次数',
      dataIndex: 'naturalFlowCategoryWatchCount',
      align: 'right',
      width: 150,
      hideInSearch: true,
      render: (text, record) => formatNum(record.naturalFlowCategoryWatchCount),
      sorter: true,
    },
    {
      title: '广告流量观看人数',
      dataIndex: 'adFlowCategoryWatchUcount',
      align: 'right',
      width: 150,
      hideInSearch: true,
      render: (text, record) => formatNum(record.adFlowCategoryWatchUcount),
      sorter: true,
    },
    {
      title: '自然流量观看人数',
      dataIndex: 'naturalFlowCategoryWatchUcount',
      align: 'right',
      width: 150,
      hideInSearch: true,
      render: (text, record) => formatNum(record.naturalFlowCategoryWatchUcount),
      sorter: true,
    },
    {
      title: '广告流量观看时长(分)',
      dataIndex: 'adFlowCategoryWatchDuration',
      align: 'right',
      width: 150,
      hideInSearch: true,
    },
    {
      title: '自然流量观看时长(分)',
      dataIndex: 'naturalFlowCategoryWatchDuration',
      align: 'right',
      width: 150,
      hideInSearch: true,
    },
    {
      title: '加粉丝团人数',
      dataIndex: 'fansClubCount',
      align: 'right',
      width: 150,
      hideInSearch: true,
      render: (text, record) => formatNum(record.fansClubCount),
      sorter: true,
    },
    {
      title: '互动人数',
      dataIndex: 'interactionUcount',
      align: 'right',
      width: 150,
      hideInSearch: true,
      render: (text, record) => formatNum(record.interactionUcount),
      sorter: true,
    },
    {
      title: '互动次数',
      dataIndex: 'interactionCount',
      align: 'right',
      width: 150,
      hideInSearch: true,
      render: (text, record) => formatNum(record.interactionCount),
      sorter: true,
    },
    {
      title: '互动率',
      dataIndex: 'interactionRate',
      align: 'right',
      width: 150,
      hideInSearch: true,
      render: (_, record) => (isNil(record.interactionRate) ? '--' : `${record.interactionRate}%`),
    },
    {
      title: '分享人数',
      dataIndex: 'shareUcount',
      align: 'right',
      width: 150,
      hideInSearch: true,
      render: (text, record) => formatNum(record.shareUcount),
      sorter: true,
    },
    {
      title: '分享次数',
      dataIndex: 'shareCount',
      align: 'right',
      width: 150,
      hideInSearch: true,
      render: (text, record) => formatNum(record.shareCount),
      sorter: true,
    },
    {
      title: '点赞人数',
      dataIndex: 'diggUcount',
      align: 'right',
      width: 150,
      hideInSearch: true,
      render: (text, record) => formatNum(record.diggUcount),
      sorter: true,
    },
    {
      title: '打赏金额',
      dataIndex: 'liveGiftMoney',
      align: 'right',
      width: 150,
      hideInSearch: true,
    },
    {
      title: '评论人数',
      dataIndex: 'commentUcount',
      align: 'right',
      width: 150,
      hideInSearch: true,
      render: (text, record) => formatNum(record.commentUcount),
      sorter: true,
    },
    {
      title: '评论次数',
      dataIndex: 'commentCount',
      align: 'right',
      width: 150,
      hideInSearch: true,
      render: (text, record) => formatNum(record.commentCount),
      sorter: true,
    },
    {
      title: '广告流量关注数',
      dataIndex: 'adFlowCategoryFollowerCount',
      align: 'right',
      width: 150,
      hideInSearch: true,
      render: (text, record) => formatNum(record.adFlowCategoryFollowerCount),
      sorter: true,
    },
    {
      title: '自然流量关注数',
      dataIndex: 'naturalFlowCategoryFollowerCount',
      align: 'right',
      width: 150,
      hideInSearch: true,
      render: (text, record) => formatNum(record.naturalFlowCategoryFollowerCount),
      sorter: true,
    },
    {
      title: '小风车点击次数',
      dataIndex: 'liveCardIconComponentClickCount',
      align: 'right',
      width: 150,
      hideInSearch: true,
      render: (text, record) => formatNum(record.liveCardIconComponentClickCount),
      sorter: true,
    },
    {
      title: '小风车曝光次数',
      tooltip: '不含小雪花',
      dataIndex: 'liveCardIconComponentShowCount',
      align: 'right',
      width: 150,
      hideInSearch: true,
      render: (text, record) => formatNum(record.liveCardIconComponentShowCount),
      sorter: true,
    },
    {
      title: '直播间进入私信数',
      dataIndex: 'roomEnterPrivateMsgCount',
      align: 'right',
      width: 150,
      hideInSearch: true,
      sorter: true,
    },
    {
      title: '全场景线索人数',
      dataIndex: 'allSceneLeadsUcount',
      align: 'right',
      width: 150,
      hideInSearch: true,
      sorter: true,
    },
    {
      title: '直播间线索数',
      dataIndex: 'liveFormSubmitCount',
      align: 'right',
      width: 150,
      hideInSearch: true,
      sorter: true,
    },
    {
      title: '留资率',
      dataIndex: 'leadsRate',
      align: 'right',
      width: 150,
      hideInSearch: true,
      render: (_, record) => (isNil(record.leadsRate) ? '--' : `${record.leadsRate}%`),
    },
    {
      title: '人均停留时长',
      dataIndex: 'avgViewDuration',
      width: 120,
      align: 'right',
      hideInSearch: true,
      renderText: (text) => {
        const formatTime = formatSecond(text);
        return <span>{formatTime}</span>;
      },
      sorter: true,
    },
    {
      title: '点赞次数',
      dataIndex: 'diggCount',
      width: 90,
      align: 'right',
      hideInSearch: true,
      render: (text, record) => formatNum(record.diggCount),
      sorter: true,
    },
    {
      title: '关注人数',
      dataIndex: 'followerGrowthCount',
      width: 90,
      align: 'right',
      hideInSearch: true,
      render: (text, record) => formatNum(record.followerGrowthCount),
      sorter: true,
    },
  ];

  const actionRef = useRef<ActionType>();
  const formRef = useRef<ProFormInstance>();
  const [columns, setColumns] = useState<ProColumns<LiveBasicItem>[]>(defaultColumns);
  const filedIdArrRef = useRef<string[]>([]);
  const firstAddColumns = useRef(false);
  const teamFieldListRef = useGetTeamFieldList(projectId);
  const paramsRef = useRef<any>({});
  const { pollingExport, percent, pollingLoading } = usePollingExport();
  const afkState = useLiveAfkFG(projectId);
  const [columnsStateMap, setColumnsStateMap] = useState<Record<string, ColumnsState>>({
    roomId: {
      show: false,
    },
  });
  const [isEmptyTable, setIsEmptyTable] = useState(false);

  // 导出数据量大特殊处理
  const handleExportLive = async () => {
    const omitPageParams = omit(paramsRef.current, ['pageSize', 'current']);
    const res = await ExportLive({
      ...omitPageParams,
    });
    if (res.data) {
      pollingExport({ taskId: res.data });
    } else {
      message.error('导出失败,请重试');
    }
  };

  useEffect(() => {
    if (enableLiveAnalysis && projectId) {
      getLiveQuotationRecordGroup({
        projectId,
      }).then((res: any) => {
        if (res.code === 0 && res.data) {
          const groupToValueEnum = (group: string[]) => {
            return group.reduce<{ [key: string]: { text: string } }>((obj, item) => {
              obj[item] = { text: item };

              return obj;
            }, {});
          };

          const { appearanceStatus, carModel, liveBackground } = res.data;
          const valueEnumMap = {
            appearanceStatus: groupToValueEnum(appearanceStatus),
            carModel: groupToValueEnum(carModel),
            liveBackground: groupToValueEnum(liveBackground),
          };

          setColumns((oldColumns) => {
            return oldColumns.map((col) => {
              const colKey = col.dataIndex as string;

              if (colKey in valueEnumMap) {
                return {
                  // @ts-ignore
                  valueEnum: valueEnumMap[colKey],
                  ...col,
                };
              } else {
                return col;
              }
            });
          });
        }
      });
    }
  }, [enableLiveAnalysis]);

  useEffect(() => {
    const afkColumns = ['afkLevel', 'liveAfkCheckDuration', 'liveAfkDuration', 'liveAfkRate'];
    const SWITCH_FEATURE_COLUMN_KEYS = ['liveBackground', 'carModel', 'appearanceStatus'];
    // 这里条件的原因是下面ProTable的postData会修改columns列 要保证排在动态列设置后再修改一次 避免修改失效
    if (firstAddColumns.current) {
      const newColumns = columns.map((item) => {
        if (afkColumns.includes(item.dataIndex as string)) {
          return {
            ...item,
            hidden: !afkState,
            // search 会有一些自定义 transform 配置 所以需要继承下来
            search: afkState ? item.search : false,
          };
        }
        if (SWITCH_FEATURE_COLUMN_KEYS.includes(item.dataIndex as string)) {
          return {
            ...item,
            hidden: !enableLiveAnalysis || !showAnalyzedLive,
            hideInSearch: !enableLiveAnalysis || !showAnalyzedLive,
          };
        }
        return item;
      }) as ProColumns<LiveBasicItem>[];
      setColumns(newColumns);
    }
  }, [firstAddColumns.current, afkState, enableLiveAnalysis, showAnalyzedLive]);

  useEffect(() => {
    const SWITCH_FEATURE_COLUMN_KEYS = ['liveBackground', 'carModel', 'appearanceStatus'];

    if (enableLiveAnalysis && !firstAddColumns.current) {
      const newColumns = columns.map((item) => {
        if (SWITCH_FEATURE_COLUMN_KEYS.includes(item.dataIndex as string)) {
          return {
            ...item,
            hidden: !showAnalyzedLive,
            hideInSearch: !showAnalyzedLive,
          };
        }
        return item;
      }) as ProColumns<LiveBasicItem>[];
      setColumns(newColumns);
    }
  }, [showAnalyzedLive]);

  useEffect(() => {
    setColumns(defaultColumns);
    firstAddColumns.current = false;
  }, [platform]);

  return (
    <ProCard>
      <ImportModal
        open={importModalOpen}
        closeModal={() => setImportModalOpen(false)}
        actionRef={actionRef}
        projectId={projectId}
      />
      <ProTable<LiveBasicItem>
        columns={columns}
        actionRef={actionRef}
        formRef={formRef}
        ghost
        // 如果是今天并且平台为抖音就 10 分钟轮询一次
        polling={isToday && platform === PlatForm.Douyin ? 10 * 60 * 1000 : 0}
        params={{
          projectId,
          startTime: rangeTime[0],
          endTime: rangeTime[1],
          isAnalysis: showAnalyzedLive,
          platform,
          blueVipFlag,
        }}
        sticky={{ offsetHeader: STICKY_OFFSETHEADER }}
        tableClassName="custom-table"
        scroll={{ x: isEmptyTable ? getSumColumnsWidth(columns) : 'max-content' }}
        postData={(data: LiveBasicItem[]) => {
          // 判断表格数据是否为空
          data.length === 0 ? setIsEmptyTable(true) : setIsEmptyTable(false);
          if (!firstAddColumns.current && data.length > 0) {
            renderCustomColumns(teamFieldListRef.current).then(({ customColumns, fieldIdArr }) => {
              filedIdArrRef.current = fieldIdArr;
              const newColumns = [...columns, ...customColumns] as ProColumns<LiveBasicItem>[];
              setColumns(newColumns);
            });
            firstAddColumns.current = true;
          }
          // 将teamFields内的字段都抽出来
          const formatData = data.map((item) => {
            const res = { ...item };
            const teamFields = res.teamFieldList;
            if (teamFields) {
              teamFields.forEach((field) => {
                (res as any)[field.fieldId] = field.value;
              });
            }
            return res;
          });
          return formatData;
        }}
        beforeSearchSubmit={(params) => {
          const fieldList = transferParams(params, filedIdArrRef.current);
          if (fieldList.length > 0) {
            return { ...params, fieldList };
          }
          return params;
        }}
        request={(params, sorter, filter) => {
          paramsRef.current = params;
          return proTableRequestAdapter(params, sorter, filter, LiveSettingPage);
        }}
        search={{ ...proTableSearchConfig }}
        options={{ ...proTableOptionsConfig }}
        pagination={{ ...proTablePaginationConfig }}
        toolBarRender={() => [
          enableLiveAnalysis ? (
            <Space style={{ marginRight: '5px' }}>
              <Switch
                defaultChecked={false}
                onChange={(status) => {
                  setShowAnalyzedLive(status);
                  if (actionRef.current && actionRef.current.reloadAndRest) {
                    actionRef.current.reloadAndRest();
                  }
                }}
              />
              查看已分析直播
            </Space>
          ) : null,
          <Button key="import" onClick={() => setImportModalOpen(true)}>
            <FolderInput size={16} strokeWidth={1.5} />
            导入外部数据
          </Button>,
          <ExportButton
            exportFn={handleExportLive}
            loading={pollingLoading}
            percent={percent}
            key="export"
          />,
        ]}
        rowKey="roomId"
        columnsState={{
          value: columnsStateMap,
          onChange: setColumnsStateMap,
          persistenceKey: 'LiveSetting-Table-Columns',
          persistenceType: 'localStorage',
        }}
        dateFormatter="string"
      />
    </ProCard>
  );
};

export default BasicData;
