import DataCard from '@/components/dataCard';
import BreadCrumbSimple from '@/components/ui/breadcrumbSimple';
import PlatformSwitch from '@/components/platformSwitch';
import TimeFilter from '@/components/ui/timeFilter';
import useProjectId from '@/hooks/useProjectId';
import { GetDataCardTrend } from '@/services/data-card';
import { getTimeByType } from '@/utils/time';
import { RightOutlined, LeftOutlined } from '@ant-design/icons';
import { useParams, useRequest } from '@umijs/max';
import { Space, Col, Button, Flex } from 'antd';
import { useAtom } from 'jotai';
import { useEffect, useState } from 'react';
import { selectPlatformAtom } from '../atom';
import BasicData from './component/basicData';
import { AnimatePresence, motion } from 'framer-motion';
import { HideScrollBarRow } from '@/utils/commonStyle';
import { PlatForm } from '@/utils/platform';
import { useHorizontalDragScroll } from '@/hooks/useHorizontalDragScroll';
import dayjs from 'dayjs';
import VisualData from './component/visualData';
import { cn } from '@/lib/utils';
import { useLocalStorageState } from 'ahooks';
import { TOYOTA } from '@/utils/constant';
import { IndustryType } from '@/utils/const';
import EcommerceData from './component/ecommerceData';

const LiveSetting = () => {
  const projectId = useProjectId();
  const { industryType } = useParams();
  const [rangeTime, setRangeTime] = useState<(string | undefined)[]>(getTimeByType('day'));
  const [platform, setPlatform] = useAtom(selectPlatformAtom);
  const [showLeftBtn, setShowLeftBtn] = useState(false);
  const scrollContainerRef = useHorizontalDragScroll<HTMLDivElement>();
  // 如果 rangTime 是同一天并且等于今天
  const isToday =
    rangeTime[0] && rangeTime[1]
      ? dayjs(rangeTime[0]).isSame(rangeTime[1], 'day') &&
        dayjs(rangeTime[0]).isSame(dayjs(), 'day')
      : false;
  const [showVisualData, setShowVisualData] = useState(false);

  // 设置默认平台为抖音
  useEffect(() => {
    setPlatform(PlatForm.Douyin);
  }, [setPlatform]);

  const requestFn = (type: number) =>
    GetDataCardTrend({
      projectId,
      platform,
      startDate: rangeTime[0],
      endDate: rangeTime[1],
      type,
    });

  const carRequestConfig = {
    refreshDeps: [rangeTime, platform],
    pollingWhenHidden: false,
    pollingInterval: platform === PlatForm.Douyin && isToday ? 10 * 60 * 1000 : 0,
    ready: industryType === '1',
  };

  const { data: liveCountData, loading: liveCountDataLoading } = useRequest(() => requestFn(301), {
    ...carRequestConfig,
  });

  const { data: userCountData, loading: userCountDataLoading } = useRequest(() => requestFn(302), {
    ...carRequestConfig,
  });

  const { data: viewTimeData, loading: viewTimeDataLoading } = useRequest(() => requestFn(303), {
    ...carRequestConfig,
    ready: true,
  });

  const { data: liveDurationData, loading: liveDurationDataLoading } = useRequest(
    () => requestFn(304),
    {
      ...carRequestConfig,
    },
  );

  const { data: liveDiggCountData, loading: liveDiggCountDataLoading } = useRequest(
    () => requestFn(305),
    {
      ...carRequestConfig,
      ready: true,
    },
  );

  const { data: liveCommentCountData, loading: liveCommentCountDataLoading } = useRequest(
    () => requestFn(306),
    {
      ...carRequestConfig,
      ready: true,
    },
  );

  const { data: liveFollowerGrowthCountData, loading: liveFollowerGrowthCountDataLoading } =
    useRequest(() => requestFn(307), {
      ...carRequestConfig,
    });

  const { data: liveCountOver25MinData, loading: liveCountOver25MinDataLoading } = useRequest(
    () => requestFn(308),
    { ...carRequestConfig },
  );

  const { data: liveDurationOver25MinData, loading: liveDurationOver25MinDataLoading } = useRequest(
    () => requestFn(309),
    { ...carRequestConfig },
  );

  const { data: avgLiveDurationData, loading: avgLiveDurationDataLoading } = useRequest(
    () => requestFn(310),
    { ...carRequestConfig },
  );

  const { data: interactionCountData, loading: interactionCountDataLoading } = useRequest(
    () => requestFn(311),
    { ...carRequestConfig },
  );

  const { data: avgViewerCountData, loading: avgViewerCountDataLoading } = useRequest(
    () => requestFn(312),
    { ...carRequestConfig },
  );

  const { data: avgViewCountData, loading: avgViewCountDataLoading } = useRequest(
    () => requestFn(313),
    { ...carRequestConfig },
  );

  const { data: avgInteractionCountData, loading: avgInteractionCountDataLoading } = useRequest(
    () => requestFn(314),
    { ...carRequestConfig },
  );

  const { data: interactionRateData, loading: interactionRateDataLoading } = useRequest(
    () => requestFn(315),
    { ...carRequestConfig },
  );

  const { data: liveClueCountData, loading: liveClueCountDataLoading } = useRequest(
    () => requestFn(316),
    { ...carRequestConfig },
  );

  const { data: avgLiveClueCountData, loading: avgLiveClueCountDataLoading } = useRequest(
    () => requestFn(317),
    { ...carRequestConfig },
  );

  const { data: clueRateData, loading: clueRateDataLoading } = useRequest(() => requestFn(318), {
    ...carRequestConfig,
  });

  const { data: adCostData, loading: adCostDataLoading } = useRequest(() => requestFn(319), {
    ...carRequestConfig,
  });

  const { data: adCplData, loading: adCplDataLoading } = useRequest(() => requestFn(320), {
    ...carRequestConfig,
  });

  const { data: liveCountOver60MinData, loading: liveCountOver60MinDataLoading } = useRequest(
    () => requestFn(321),
    { ...carRequestConfig },
  );

  const { data: liveDurationOver60MinData, loading: liveDurationOver60MinDataLoading } = useRequest(
    () => requestFn(322),
    { ...carRequestConfig },
  );

  const ecommerceRequestConfig = {
    refreshDeps: [rangeTime, platform],
    pollingWhenHidden: false,
    pollingInterval: 10 * 60 * 1000,
    ready: industryType === '2',
  };

  // 千川平台电商指标数据请求
  const { data: qcTotalCostData, loading: qcTotalCostDataLoading } = useRequest(
    () => requestFn(701),
    {
      ...ecommerceRequestConfig,
    },
  );

  const { data: qcTotalAmountData, loading: qcTotalAmountDataLoading } = useRequest(
    () => requestFn(702),
    {
      ...ecommerceRequestConfig,
    },
  );

  const { data: qcTotalOrderCountData, loading: qcTotalOrderCountDataLoading } = useRequest(
    () => requestFn(703),
    {
      ...ecommerceRequestConfig,
    },
  );

  const { data: qcTotalPayRoiData, loading: qcTotalPayRoiDataLoading } = useRequest(
    () => requestFn(704),
    {
      ...ecommerceRequestConfig,
    },
  );

  const { data: qcTotalOrderCostData, loading: qcTotalOrderCostDataLoading } = useRequest(
    () => requestFn(705),
    {
      ...ecommerceRequestConfig,
    },
  );

  const { data: qcUserActualPayAmountData, loading: qcUserActualPayAmountDataLoading } = useRequest(
    () => requestFn(706),
    {
      ...ecommerceRequestConfig,
    },
  );

  const { data: qcTotalCouponAmountData, loading: qcTotalCouponAmountDataLoading } = useRequest(
    () => requestFn(707),
    {
      ...ecommerceRequestConfig,
    },
  );

  const scroll = (width: number) => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: width, behavior: 'smooth' });
    }
  };

  const handleScroll = () => {
    if (scrollContainerRef.current) {
      const { scrollLeft } = scrollContainerRef.current;
      setShowLeftBtn(scrollLeft >= 30);
    }
  };

  useEffect(() => {
    const container = scrollContainerRef.current;
    if (container) {
      container.addEventListener('scroll', handleScroll);
    }

    return () => {
      // 清理滚动监听
      if (container) {
        container.removeEventListener('scroll', handleScroll);
      }
    };
  }, []);

  const dataCardList = [
    { id: 'live-count', title: '直播场次', data: liveCountData, loading: liveCountDataLoading },
    { id: 'user-count', title: '观看人数', data: userCountData, loading: userCountDataLoading },
    { id: 'view-time', title: '观看次数', data: viewTimeData, loading: viewTimeDataLoading },
    {
      id: 'live-duration',
      title: '直播时长(小时)',
      data: liveDurationData,
      loading: liveDurationDataLoading,
      isTimeType: true,
    },
    {
      id: 'digg-count',
      title: '点赞次数',
      data: liveDiggCountData,
      loading: liveDiggCountDataLoading,
    },
    {
      id: 'comment-count',
      title: '弹幕数量',
      data: liveCommentCountData,
      loading: liveCommentCountDataLoading,
    },
    {
      id: 'follower-growth',
      title: '粉丝增量',
      data: liveFollowerGrowthCountData,
      loading: liveFollowerGrowthCountDataLoading,
    },
    ...(projectId === TOYOTA
      ? [
          {
            id: 'live-count-60min',
            title: '≥60分钟直播场次',
            data: liveCountOver60MinData,
            loading: liveCountOver60MinDataLoading,
          },
          {
            id: 'live-duration-60min',
            title: '≥60分钟直播时长(小时)',
            data: liveDurationOver60MinData,
            loading: liveDurationOver60MinDataLoading,
            isTimeType: true,
          },
        ]
      : [
          {
            id: 'live-count-25min',
            title: '≥25分钟直播场次',
            data: liveCountOver25MinData,
            loading: liveCountOver25MinDataLoading,
          },
          {
            id: 'live-duration-25min',
            title: '≥25分钟直播时长(小时)',
            data: liveDurationOver25MinData,
            loading: liveDurationOver25MinDataLoading,
            isTimeType: true,
          },
        ]),
    {
      id: 'avg-live-duration',
      title: '场均直播时长(小时)',
      data: avgLiveDurationData,
      loading: avgLiveDurationDataLoading,
      isTimeType: true,
    },
    {
      id: 'interaction-count',
      title: '互动次数',
      data: interactionCountData,
      loading: interactionCountDataLoading,
    },
    {
      id: 'avg-viewer-count',
      title: '场均观看人数',
      data: avgViewerCountData,
      loading: avgViewerCountDataLoading,
    },
    {
      id: 'avg-view-count',
      title: '场均观看次数',
      data: avgViewCountData,
      loading: avgViewCountDataLoading,
    },
    {
      id: 'avg-interaction-count',
      title: '场均互动次数',
      data: avgInteractionCountData,
      loading: avgInteractionCountDataLoading,
    },
    {
      id: 'interaction-rate',
      title: '互动率',
      data: interactionRateData,
      loading: interactionRateDataLoading,
    },
    {
      id: 'live-clue-count',
      title: '直播间线索数',
      data: liveClueCountData,
      loading: liveClueCountDataLoading,
    },
    {
      id: 'avg-live-clue',
      title: '场均直播线索量',
      data: avgLiveClueCountData,
      loading: avgLiveClueCountDataLoading,
    },
    { id: 'clue-rate', title: '留资率', data: clueRateData, loading: clueRateDataLoading },
    { id: 'ad-cost', title: '投放消耗', data: adCostData, loading: adCostDataLoading },
    { id: 'ad-cpl', title: '投流CPL', data: adCplData, loading: adCplDataLoading },
  ];

  // 电商行业数据卡片列表
  const ecommerceDataCardList = [
    { id: 'view-time', title: '观看次数', data: viewTimeData, loading: viewTimeDataLoading },
    {
      id: 'comment-count',
      title: '评论次数',
      data: liveCommentCountData,
      loading: liveCommentCountDataLoading,
    },
    {
      id: 'digg-count',
      title: '点赞次数',
      data: liveDiggCountData,
      loading: liveDiggCountDataLoading,
    },
    // 千川平台电商指标
    {
      id: 'qc-total-cost',
      title: '整体消耗',
      data: qcTotalCostData,
      loading: qcTotalCostDataLoading,
    },
    {
      id: 'qc-total-amount',
      title: '整体成交金额',
      data: qcTotalAmountData,
      loading: qcTotalAmountDataLoading,
    },
    {
      id: 'qc-total-order-count',
      title: '整体成交订单数',
      data: qcTotalOrderCountData,
      loading: qcTotalOrderCountDataLoading,
    },
    {
      id: 'qc-total-pay-roi',
      title: '整体支付ROI',
      data: qcTotalPayRoiData,
      loading: qcTotalPayRoiDataLoading,
    },
    {
      id: 'qc-total-order-cost',
      title: '整体成交订单成本',
      data: qcTotalOrderCostData,
      loading: qcTotalOrderCostDataLoading,
    },
    {
      id: 'qc-user-actual-pay-amount',
      title: '用户实际支付金额',
      data: qcUserActualPayAmountData,
      loading: qcUserActualPayAmountDataLoading,
    },
    {
      id: 'qc-total-coupon-amount',
      title: '整体成交智能优惠券金额',
      data: qcTotalCouponAmountData,
      loading: qcTotalCouponAmountDataLoading,
    },
  ];

  // 根据行业类型选择对应的数据卡片列表
  const currentDataCardList = industryType === '2' ? ecommerceDataCardList : dataCardList;

  const [sortedIds = [], setSortedIds] = useLocalStorageState<string[]>('visual-data-order', {
    defaultValue: currentDataCardList.map((item) => item.id),
    listenStorageChange: true,
  });

  const getSortedItems = (list: typeof dataCardList, orderIds: string[]) => {
    const itemMap = new Map(list.map((item) => [item.id, item]));
    return orderIds
      .filter((id) => itemMap.has(id))
      .map((id) => itemMap.get(id) as (typeof list)[0])
      .concat(list.filter((item) => !orderIds.includes(item.id)));
  };

  // 获取排序后的数据卡片列表
  const sortedDataCardList = getSortedItems(currentDataCardList, sortedIds);

  return (
    <div>
      <div className="flex items-center justify-between">
        <BreadCrumbSimple breadcrumbs={['平台管理', '直播列表']} />

        <div className="text-xs text-[#979797]">
          {platform === PlatForm.Douyin && isToday
            ? '时间筛选为今天时，大部分数据还未更新，最新数据更新到:'
            : '最新数据更新到:'}
          {dayjs().subtract(1, 'day').format('YYYY-MM-DD')}
        </div>
      </div>
      <div className="rounded-lg bg-white px-4 py-2">
        <Space>
          <TimeFilter value={rangeTime} onChange={(value) => setRangeTime(value)} />
          <PlatformSwitch onlyDouyin />
          <Button type="primary" onClick={() => setShowVisualData(!showVisualData)}>
            {showVisualData ? '查看表格数据' : '数据可视化概览'}
          </Button>
        </Space>
      </div>
      <div className="relative">
        <motion.div
          className={cn(
            'absolute w-full',
            showVisualData ? 'pointer-events-none opacity-0' : 'pointer-events-auto opacity-100',
          )}
          initial={false}
          animate={{ opacity: showVisualData ? 0 : 1, y: showVisualData ? -20 : 0 }}
          transition={{ duration: 0.3 }}
        >
          <Flex justify="center" align="center">
            <AnimatePresence>
              {showLeftBtn && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  transition={{ duration: 0.5 }}
                  style={{
                    zIndex: '1000',
                  }}
                >
                  <Button shape="circle" icon={<LeftOutlined />} onClick={() => scroll(-300)} />
                </motion.div>
              )}
            </AnimatePresence>
            <HideScrollBarRow
              gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}
              ref={scrollContainerRef}
              className="py-4"
              wrap={false}
            >
              {sortedDataCardList.map((item) => (
                <Col xs={8} sm={8} md={7} lg={7} xl={8} xxl={5} span={5} key={item.id}>
                  <DataCard
                    title={item.title}
                    data={item.data}
                    loading={item.loading}
                    {...(item.isTimeType ? { isTimeType: true } : {})}
                  />
                </Col>
              ))}
            </HideScrollBarRow>
            <Button
              shape="circle"
              icon={<RightOutlined />}
              style={{
                zIndex: '1000',
              }}
              onClick={() => scroll(300)}
            />
          </Flex>
          {Number(industryType) === IndustryType.CAR ? (
            <BasicData projectId={projectId} rangeTime={rangeTime} industryType={industryType} />
          ) : (
            <EcommerceData
              projectId={projectId}
              rangeTime={rangeTime}
              industryType={industryType}
            />
          )}
        </motion.div>

        <motion.div
          className={cn(
            'absolute w-full',
            !showVisualData ? 'pointer-events-none opacity-0' : 'pointer-events-auto opacity-100',
          )}
          initial={false}
          animate={{ opacity: showVisualData ? 1 : 0, y: showVisualData ? 0 : 20 }}
          transition={{ duration: 0.3 }}
        >
          <VisualData dataCardList={sortedDataCardList} setSortedIds={setSortedIds} />
        </motion.div>
      </div>
    </div>
  );
};

export default LiveSetting;
