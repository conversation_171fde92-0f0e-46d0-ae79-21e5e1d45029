import ExportButton from '@/components/exportButton';
import TimeFilter from '@/components/ui/timeFilter';
import BreadCrumbSimple from '@/components/ui/breadcrumbSimple';
import useProjectId from '@/hooks/useProjectId';
import { PlatForm } from '@/utils/platform';
import {
  ExportIndustryLiveTop,
  GetIndustryLive,
  GetIndustryLiveTop,
  IndustryLive,
  IndustryLiveTopData,
} from '@/services/industry';
import { getDateType, getTimeByType } from '@/utils/time';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { PageContainer, ProCard } from '@ant-design/pro-components';
import { useRequest } from 'ahooks';
import { Flex, message, Select, Space, Tooltip } from 'antd';
import { isUndefined } from 'lodash-es';
import { useEffect, useRef, useState } from 'react';
import CardInfo from './component/cardInfo';
import LiveRankList from './component/liveRankList';
import { ScrollProCard } from './postOverview';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { usePollingExport } from '@/hooks/usePollingExport';

const options = [
  {
    value: 'liveDiggCount',
    label: '直播点赞数量',
  },
  {
    value: 'liveCount',
    label: '直播场次',
  },
  {
    value: 'liveViewTime',
    label: '直播观看人次',
  },
];

const LiveOverview = () => {
  const projectId = useProjectId();
  const [calculateType, setCalculateType] = useState<string | null>('0');
  const [rangeTime, setRangeTime] = useState<(string | undefined)[]>(getTimeByType('day'));
  const radioValueRef = useRef<{ value: string } | null>({ value: '' });
  const [rankType, setRankType] = useState<string>('liveDiggCount');
  const [accountType, setAccountType] = useState<number>(0);
  const [isMeRank, setIsMeRank] = useState<number | null>(null);
  const [liveData, setLiveData] = useState<IndustryLive[]>([]);
  const [sumDataState, setSumDataState] = useState<IndustryLive | null>(null);
  const [rankListData, setRankListData] = useState<IndustryLiveTopData[]>([]);
  const [tagId, setTagId] = useState<number | undefined>(undefined);
  const dataSourceRef = useRef<IndustryLive[]>([]);
  const [platform, setPlatform] = useState<PlatForm>(PlatForm.ALL);
  const { pollingExport, percent, pollingLoading } = usePollingExport();

  const { loading: getIndustryLiveLoading } = useRequest(
    () =>
      GetIndustryLive({
        projectId,
        calculateType,
        startDate: rangeTime[0],
        endDate: rangeTime[1],
        dateType: getDateType(radioValueRef.current?.value),
        accountType: accountType === 0 ? undefined : accountType,
        platform,
      }),
    {
      refreshDeps: [projectId, rangeTime, accountType, calculateType, platform],
      onSuccess: (res) => {
        const liveDataRes = res.data;
        dataSourceRef.current = liveDataRes || [];
        renderSumData(liveDataRes || []);
        renderShowData(liveDataRes || []);
      },
    },
  );

  const { loading: getIndustryLiveTopLoading } = useRequest(
    () =>
      GetIndustryLiveTop({
        projectId,
        calculateType,
        startDate: rangeTime[0],
        endDate: rangeTime[1],
        tagId: tagId ? tagId : undefined,
        accountType: accountType === 0 ? undefined : accountType,
        platform,
      }),
    {
      refreshDeps: [projectId, rangeTime, accountType, calculateType, platform, tagId],
      onSuccess: (res) => {
        setRankListData(res.data || []);
      },
    },
  );

  const renderShowData = (data: IndustryLive[]) => {
    // @ts-ignore
    const sortLiveData = data.sort((a, b) => Number(b[rankType]) - Number(a[rankType]));
    const isMeRank = sortLiveData.findIndex((item) => item.isMe === 1) + 1;
    setIsMeRank(isMeRank);
    setLiveData([...sortLiveData]);
  };

  const renderSumData = (data: IndustryLive[]) => {
    const initData = {
      industryIcon:
        'https://media-**********.cos.ap-guangzhou.myqcloud.com/project_icon/allaccount.png',
      industryName: '全部账号',
      accountCount: 0,
      liveDiggCount: 0,
      liveViewTime: 0,
      liveCount: 0,
    };

    const sumData = data.reduce((acc, cur) => {
      acc.accountCount += Number(cur.accountCount);
      acc.liveDiggCount += Number(cur.liveDiggCount);
      acc.liveViewTime += Number(cur.liveViewTime);
      acc.liveCount += Number(cur.liveCount);
      return acc;
    }, initData);

    const newSumData = {
      industryName: sumData.industryName,
      industryIcon: sumData.industryIcon,
      accountCount: Math.round(sumData.accountCount),
      liveDiggCount: Math.round(sumData.liveDiggCount).toString(),
      liveViewTime: Math.round(sumData.liveDiggCount).toString(),
      liveCount: Math.round(sumData.liveCount).toString(),
    };
    setSumDataState(newSumData);
  };

  useEffect(() => {
    renderShowData(dataSourceRef.current);
  }, [rankType]);

  const handleExport = async () => {
    const res = await ExportIndustryLiveTop({
      projectId,
      startDate: rangeTime[0],
      endDate: rangeTime[1],
      accountType: accountType === 0 ? undefined : accountType,
      tagId,
    });
    if (res.data) {
      pollingExport({ taskId: res.data });
    } else {
      message.error('导出失败,请重试');
    }
  };

  return (
    <PageContainer title={<BreadCrumbSimple breadcrumbs={['行业管理', '行业直播榜']} />}>
      <ProCard colSpan={{ md: 24, lg: 18 }}>
        <Flex justify="space-between">
          <Space size="middle" direction="horizontal" wrap={true}>
            <div style={{ textWrap: 'nowrap' }}>
              <span>排行: </span>
              <Select
                style={{ width: '166px' }}
                value={rankType}
                onChange={(value) => setRankType(value)}
                options={options}
              />
            </div>
            <div style={{ textWrap: 'nowrap' }}>
              <span>账号来源: </span>
              <Select
                style={{ width: '166px' }}
                defaultValue={0}
                onChange={(value) => setAccountType(value)}
                options={[
                  { value: 0, label: '全部' },
                  { value: 1, label: '官号' },
                  { value: 2, label: '经销商' },
                ]}
              />
            </div>
            <div style={{ textWrap: 'nowrap' }}>
              <span>平台: </span>
              <Select
                style={{ width: '166px' }}
                value={platform}
                onChange={(value) => setPlatform(value)}
                options={[
                  { value: PlatForm.ALL, label: '全平台' },
                  { value: PlatForm.Douyin, label: '抖音' },
                ]}
              />
            </div>
          </Space>
        </Flex>

        <Flex justify="space-between" style={{ marginTop: '20px' }}>
          <Space>
            <Tabs value={calculateType} onValueChange={(value) => setCalculateType(value)}>
              <TabsList value={calculateType}>
                <TabsTrigger value="0">总值</TabsTrigger>
                <TabsTrigger value="1">均值</TabsTrigger>
              </TabsList>
            </Tabs>
            <Tooltip title="均值计算方式：对应数据指标/相关账号总数">
              <QuestionCircleOutlined />
            </Tooltip>
            <TimeFilter
              value={rangeTime}
              onChange={(value) => setRangeTime(value)}
              ref={radioValueRef}
            />
          </Space>

          <Space size={20}>
            <div style={{ color: '#1E5EFF', fontSize: '16px', fontWeight: '500' }}>
              我的排名: {isMeRank}
            </div>
            <ExportButton
              exportFn={() => handleExport()}
              loading={pollingLoading}
              percent={percent}
              key="export"
            />
          </Space>
        </Flex>

        <div>
          <ProCard ghost style={{ marginTop: '20px' }}>
            <ScrollProCard
              ghost
              colSpan={{
                xs: '450px',
                sm: '450px',
                md: '450px',
                lg: '450px',
                xl: '450px',
                xxl: '450px',
              }}
              loading={getIndustryLiveLoading}
            >
              <CardInfo
                type="live"
                data={sumDataState}
                selected={isUndefined(tagId)}
                onClick={() => setTagId(undefined)}
              />
              {liveData?.map((item, index) => {
                return (
                  <CardInfo
                    key={`livecard-${index}`}
                    type="live"
                    data={item}
                    selected={tagId === item.tagId}
                    onClick={() => setTagId(item.tagId)}
                    index={index + 1}
                  />
                );
              })}
            </ScrollProCard>
            <ProCard
              ghost
              style={{
                background: `linear-gradient(rgba(47, 108, 255, 0.05), #ffffff)`,
                backgroundRepeat: 'no-repeat',
                backgroundPosition: 'center',
                backgroundSize: '100% 100%',
                overflow: 'hidden',
                marginLeft: '10px',
                padding: '16px',
                paddingRight: '0px',
              }}
            >
              <LiveRankList rankListData={rankListData} loading={getIndustryLiveTopLoading} />
            </ProCard>
          </ProCard>
        </div>
      </ProCard>
    </PageContainer>
  );
};

export default LiveOverview;
