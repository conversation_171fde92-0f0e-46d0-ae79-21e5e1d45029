import { IndustryLiveTopData } from '@/services/industry';
import { formatNum } from '@/utils/common';
import { ProList } from '@ant-design/pro-components';
import { Avatar } from 'antd';
import { useEffect, useState } from 'react';
import FailImg from '@/assets/fail-img.png';
import DefaultAvatar from '@/assets/default-avatar.png';
import styles from './style.module.less';

type RankListProps = {
  rankListData: IndustryLiveTopData[];
  loading: boolean;
};

const LiveRankList = (props: RankListProps) => {
  const { rankListData, loading } = props;
  const [dataSource, setDataSource] = useState<IndustryLiveTopData[]>(rankListData);

  useEffect(() => {
    setDataSource(rankListData);
  }, [rankListData]);

  return (
    <ProList<IndustryLiveTopData>
      rowKey="title"
      headerTitle="直播Top10"
      dataSource={dataSource}
      showActions="hover"
      loading={loading}
      editable={{
        onSave: async (key, record, originRow) => {
          console.log(key, record, originRow);
          return true;
        },
      }}
      style={{
        overflowY: 'auto',
        maxHeight: 'calc(100vh - 56px)',
      }}
      ghost
      onDataSourceChange={setDataSource}
      metas={{
        title: {
          dataIndex: 'title',
          render: (_, record) => {
            return (
              <a href={record.replayUrls[0]} target="_blank" rel="noreferrer">
                {record.title}
              </a>
            );
          },
        },
        avatar: {
          dataIndex: 'image',
          render: (_, record) => {
            return (
              <img
                src={record.cover || FailImg}
                width={150}
                height={100}
                style={{ borderRadius: '4px', marginTop: '10px', objectFit: 'cover' }}
              />
            );
          },
        },
        description: {
          dataIndex: 'desc',
          render: (_, record) => {
            return (
              <div className={styles.rankInfo}>
                <div className="header">
                  <div>
                    <Avatar size="small" src={record.avatar || DefaultAvatar} />
                    &nbsp;
                    <span>{record.nickname}</span>
                  </div>
                </div>
                <div className="infos">
                  <div className="info">
                    <div>观看人次</div>
                    <div>{formatNum(record.liveViewTime)}</div>
                  </div>
                  <div className="info">
                    <div>直播点赞数</div>
                    <div>{formatNum(record.liveDiggCount)}</div>
                  </div>
                </div>
              </div>
            );
          },
        },
      }}
    />
  );
};

export default LiveRankList;
