import { IndustryPostTopData } from '@/services/industry';
import { formatNum } from '@/utils/common';
import { formatSecond } from '@/utils/time';
import { ProList } from '@ant-design/pro-components';
import { Avatar } from 'antd';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import FailImg from '@/assets/fail-img.png';
import DefaultAvatar from '@/assets/default-avatar.png';
import styles from './style.module.less';

type RankListProps = {
  rankListData: IndustryPostTopData[];
  loading: boolean;
};

const PostRankList = (props: RankListProps) => {
  const { rankListData, loading } = props;
  const [dataSource, setDataSource] = useState<IndustryPostTopData[]>(rankListData);

  useEffect(() => {
    setDataSource(rankListData);
  }, [rankListData]);

  return (
    <ProList<IndustryPostTopData>
      rowKey="title"
      headerTitle="作品Top10"
      dataSource={dataSource}
      showActions="hover"
      loading={loading}
      editable={{
        onSave: async (key, record, originRow) => {
          console.log(key, record, originRow);
          return true;
        },
      }}
      style={{
        overflowY: 'auto',
        maxHeight: 'calc(100vh - 56px)',
      }}
      ghost
      onDataSourceChange={setDataSource}
      metas={{
        title: {
          dataIndex: 'title',
          render: (_, record) => {
            return (
              <a href={record.url} target="_blank" rel="noreferrer">
                {record.title}
              </a>
            );
          },
        },
        avatar: {
          dataIndex: 'image',
          render: (_, record) => {
            return (
              <img
                src={record.cover || FailImg}
                width={75}
                height={100}
                style={{ borderRadius: '4px' }}
              />
            );
          },
        },
        description: {
          dataIndex: 'desc',
          render: (_, record) => {
            return (
              <div className={styles.rankInfo}>
                <div className="header">
                  <div>
                    <Avatar size="small" src={record.avatar || DefaultAvatar} />
                    &nbsp;
                    <span>{record.nickname}</span>
                    <span style={{ marginLeft: '10px' }}>
                      时间: {dayjs(record.publishTime).format('YYYY-MM-DD')}
                    </span>
                  </div>
                </div>
                <div className="infos">
                  <div className="info">
                    <div>互动量</div>
                    <div>{formatNum(record.postInteractionCount)}</div>
                  </div>

                  <div className="info">
                    <div>点赞量</div>
                    <div>{formatNum(record.postDiggCount)}</div>
                  </div>
                  <div className="info">
                    <div>评论量</div>
                    <div>{formatNum(record.postCommentCount)}</div>
                  </div>
                  <div className="info">
                    <div>转发量</div>
                    <div>{formatNum(record.postShareCount)}</div>
                  </div>
                  <div className="info">
                    <div>作品时长</div>
                    <div>{formatSecond(record.duration)}</div>
                  </div>
                </div>
              </div>
            );
          },
        },
      }}
    />
  );
};

export default PostRankList;
