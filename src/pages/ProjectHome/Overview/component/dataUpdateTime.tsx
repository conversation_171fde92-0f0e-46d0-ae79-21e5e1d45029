import { ReloadOutlined } from '@ant-design/icons';
import { Button, Flex, Space } from 'antd';

type DataUpdateTimeProps = {
  rangeTime: (string | undefined)[];
  updateTime: string | null;
  fetchData: () => void;
};

const DataUpdateTime = (props: DataUpdateTimeProps) => {
  const { rangeTime = [], updateTime, fetchData } = props;
  return (
    <Space>
      <Flex gap={10} style={{ color: '#979797', fontSize: '12px' }}>
        <span>
          统计周期: {rangeTime.length > 0 ? `${rangeTime[0]} 00:00 - ${rangeTime[1]} 23:59` : null}
        </span>
        <span>最新数据更新到: {updateTime}</span>
      </Flex>
      <Button
        size="small"
        icon={<ReloadOutlined />}
        onClick={async () => {
          await fetchData();
        }}
      >
        手动刷新
      </Button>
    </Space>
  );
};

export default DataUpdateTime;
