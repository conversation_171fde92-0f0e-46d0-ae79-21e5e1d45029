import defaultImage from '@/assets/default-image.png';
import MomData from '@/components/momData';
import { SvgIcon } from '@/components/SvgIcon';
import { IndustryLive, IndustryPost } from '@/services/industry';
import { formatNum } from '@/utils/common';
import { cn } from '@/lib/utils';
import { Tooltip } from 'antd';

const IndexIconMap: Record<number, React.JSX.Element> = {
  1: <SvgIcon icon="local:outline/card-rank-1" vertical="middle" fill="none" stroke="none" />,
  2: <SvgIcon icon="local:outline/card-rank-2" vertical="middle" fill="none" stroke="none" />,
  3: <SvgIcon icon="local:outline/card-rank-3" vertical="middle" fill="none" stroke="none" />,
};

type CardInfoProps = {
  selected: boolean;
  onClick: () => void;
  index?: number;
};

type PostCardInfoProps = {
  type: 'post';
  data: IndustryPost | null;
} & CardInfoProps;

type LiveCardInfoProps = {
  type: 'live';
  data: IndustryLive | null;
} & CardInfoProps;

const CardInfo = (props: PostCardInfoProps | LiveCardInfoProps) => {
  const { selected, onClick, index } = props;
  if (!props.data) return null;

  return (
    <div
      className={cn(
        'relative mb-4 mr-[5px] flex w-full flex-row rounded p-4',
        selected ? 'border border-[#335ff5] bg-[#eaeffd]' : 'border-none bg-[#fbfcfe]',
      )}
      onClick={onClick}
    >
      {index && (
        <div className="absolute right-0 top-0">
          <span className="absolute right-[10px] top-0 z-[99] text-white">{index}</span>
          {IndexIconMap[index] || (
            <SvgIcon
              icon="local:outline/card-rank-default"
              vertical="middle"
              fill="none"
              stroke="none"
            />
          )}
        </div>
      )}
      <div className="flex min-w-[90px] flex-col flex-nowrap items-center justify-center gap-1">
        <img src={props.data.industryIcon || defaultImage} width={50} height={50} />
        <Tooltip title={props.data.industryName}>
          <div className="line-clamp-2 max-w-[100px]">{props.data.industryName}</div>
        </Tooltip>
      </div>
      <div className="flex-1 pl-[50px] text-[#6e6e6e]">
        {props.type === 'live' ? (
          <>
            <div>
              直播点赞数{' '}
              <span className="ml-4 text-lg text-[#1e5eff]">
                {formatNum(props.data.liveDiggCount)}
              </span>
            </div>
            <div className="flex flex-row gap-10">
              <div>
                <div className="whitespace-nowrap">账号总数</div>
                <div className="text-lg text-[#0e1015]">{formatNum(props.data.accountCount)}</div>
              </div>
              <div>
                <div className="whitespace-nowrap">观看人次</div>
                <div className="flex flex-col">
                  <div className="text-lg text-[#0e1015]">{formatNum(props.data.liveViewTime)}</div>
                  {props.data.liveViewTimeRate && (
                    <div>
                      <MomData rate={props.data.liveViewTimeRate} />
                    </div>
                  )}
                </div>
              </div>
              <div>
                <div className="whitespace-nowrap">直播场次</div>
                <div className="flex flex-col">
                  <div className="text-lg text-[#0e1015]">{formatNum(props.data.liveCount)}</div>
                  {props.data.liveCountRate && (
                    <div>
                      <MomData rate={props.data.liveCountRate} />
                    </div>
                  )}
                </div>
              </div>
            </div>
          </>
        ) : (
          <>
            <div>
              账号相关数{' '}
              <span className="ml-4 text-lg text-[#1e5eff]">{props.data.accountCount}</span>
            </div>
            <div className="flex flex-row gap-10">
              <div>
                <div className="whitespace-nowrap">作品互动量</div>
                <div className="flex flex-col">
                  <div className="text-lg text-[#0e1015]">
                    {formatNum(props.data.postInteractionCount)}
                  </div>
                  {props.data.postInteractionCountRate && (
                    <div>
                      <MomData rate={props.data.postInteractionCountRate} />
                    </div>
                  )}
                </div>
              </div>
              <div>
                <div className="whitespace-nowrap">作品发布量</div>
                <div className="flex flex-col">
                  <div className="text-lg text-[#0e1015]">
                    {formatNum(props.data.postPublishCount)}
                  </div>
                  {props.data.postPublishCountRate && (
                    <div>
                      <MomData rate={props.data.postPublishCountRate} />
                    </div>
                  )}
                </div>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default CardInfo;
