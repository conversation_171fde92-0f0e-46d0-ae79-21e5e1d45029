.rankInfo {
  display: flex;
  flex-direction: column;
  gap: 16px;

  :global {
    .infos {
      display: flex;
      flex-direction: row;
      gap: 30px;

      .info {
        display: flex;
        flex-direction: column;
        gap: 5px;
        white-space: nowrap;

        div:nth-last-child(1) {
          color: #0e1015;
          font-size: 16px;
          font-family: OPPOSans;
        }
      }

      .info:first-child {
        div:nth-last-child(1) {
          color: #1e5eff;
        }
      }
    }
  }
}
