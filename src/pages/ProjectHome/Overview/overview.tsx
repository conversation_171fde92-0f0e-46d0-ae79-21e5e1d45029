import defaultImage from '@/assets/default-image.png';
import ExportButton from '@/components/exportButton';
import { SvgIcon } from '@/components/SvgIcon';
import TimeFilter from '@/components/ui/timeFilter';
import BreadCrumbSimple from '@/components/ui/breadcrumbSimple';
import useProjectId from '@/hooks/useProjectId';
import Rank1 from '@/icons/outline/rank-1.svg';
import Rank2 from '@/icons/outline/rank-2.svg';
import Rank3 from '@/icons/outline/rank-3.svg';
import { PlatForm, PlatformSelectOptions } from '@/utils/platform';
import {
  ExportIndustryDashboard,
  GetIndustryDashboard,
  IndustryDashboard,
} from '@/services/industry';
import { formatNum, STICKY_OFFSETHEADER } from '@/utils/common';
import { getDateType, getTimeByType } from '@/utils/time';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { PageContainer, ProCard, ProColumns, ProTable } from '@ant-design/pro-components';
import { useRequest } from 'ahooks';
import { Flex, message, Select, Space, Tooltip } from 'antd';
import { useEffect, useRef, useState } from 'react';
import styled from 'styled-components';
import { Decline, Rise } from '../style';
import DataUpdateTime from './component/dataUpdateTime';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { usePollingExport } from '@/hooks/usePollingExport';

const renderData = (data: string | number, rate: string | number, isBlue = false) => {
  return (
    <Flex vertical gap={2}>
      {isBlue ? (
        <span style={{ color: '#1E5EFF', fontSize: '18px', fontFamily: 'OPPOSans' }}>
          {formatNum(data)}
        </span>
      ) : (
        <span style={{ color: '#0E1015', fontSize: '18px', fontFamily: 'OPPOSans' }}>
          {formatNum(data)}
        </span>
      )}
      {Number(rate) >= 0 ? (
        <Rise style={{ color: '#f54848' }}>
          <SvgIcon icon={'local:outline/rate-rise'} />
          {rate}%
        </Rise>
      ) : (
        <Decline style={{ color: '#30b824' }}>
          <SvgIcon icon={'local:outline/rate-decline'} />
          {rate}%
        </Decline>
      )}
    </Flex>
  );
};

const RankBg = styled.div`
  width: 100%;
  height: 100px;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
`;

const RankOneBg = styled(RankBg)`
  background: linear-gradient(91deg, rgba(255, 244, 217, 0.9) -46.66%, #fff 99.43%);
  .dark & {
    background: linear-gradient(91deg, rgba(62, 47, 8, 0.9) -46.66%, rgb(32, 32, 35) 99.43%);
  }
`;

const RankTwoBg = styled(RankBg)`
  background: linear-gradient(90deg, rgba(207, 227, 255, 0.58) -49.68%, #fff 99.84%);
  .dark & {
    background: linear-gradient(90deg, rgba(44, 45, 49, 0.58) -49.68%, rgb(32, 32, 35) 99.84%);
  }
`;

const RankThreeBg = styled(RankBg)`
  background: linear-gradient(90deg, rgba(255, 206, 188, 0.23) -44.04%, #fff 99.86%);
  .dark & {
    background: linear-gradient(90deg, rgba(95, 31, 8, 0.23) -44.04%, rgb(32, 32, 35) 99.86%);
  }
`;

const RangBgMap: Record<number, React.JSX.Element> = {
  1: (
    <RankOneBg>
      <img src={Rank1} />
    </RankOneBg>
  ),
  2: (
    <RankTwoBg>
      <img src={Rank2} />
    </RankTwoBg>
  ),
  3: (
    <RankThreeBg>
      <img src={Rank3} />
    </RankThreeBg>
  ),
};

const columns: Array<ProColumns<IndustryDashboard>> = [
  {
    dataIndex: 'rank',
    title: '排名',
    fixed: 'left',
    align: 'center',
    render: (_, record, index) => {
      return (
        RangBgMap[index + 1] || (
          <RankBg>
            <div>{index + 1}</div>
          </RankBg>
        )
      );
    },
  },
  {
    dataIndex: 'account',
    title: '账号',
    fixed: 'left',
    align: 'center',
    render(_, record) {
      return (
        <div
          style={{
            width: '100%',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            gap: '6px',
            marginBlock: 'auto',
            marginInline: '0px',
          }}
        >
          <img
            src={record.industryIcon ? record.industryIcon : defaultImage}
            width={50}
            height={50}
          />
          <div className="line-clamp-2 max-w-[150px]" title={record.industryName}>
            {record.industryName}
          </div>
        </div>
      );
    },
  },
];

export const options = [
  { value: 'accountCount', label: '账号数量', rate: 'accountCountRate' },
  { value: 'postInteractionCount', label: '作品互动', rate: 'postInteractionCountRate' },
  { value: 'liveCount', label: '直播场次', rate: 'liveCountRate' },
  { value: 'liveDiggCount', label: '直播点赞数', rate: 'liveDiggCountRate' },
  { value: 'postPublishCount', label: '作品发布', rate: 'postPublishCountRate' },
  { value: 'liveViewTime', label: '直播观看人次', rate: 'liveViewTimeRate' },
  { value: 'followerCount', label: '粉丝总量', rate: 'followerCountRate' },
  { value: 'followerGrowthCount', label: '粉丝增量', rate: 'followerGrowthCountRate' },
];

const renderColumns = (rankType: string): Array<ProColumns> => {
  const optionsColumns = options
    .map((item) => ({
      dataIndex: item.value,
      title: item.label,
      width: 150,
      // @ts-ignore
      render: (_, record) =>
        renderData(record[item.value], record[item.rate], rankType === item.value),
    }))
    .sort((a) => (a.dataIndex === rankType ? -1 : 1));
  const newColumns = [...columns, ...optionsColumns] as ProColumns[];
  return newColumns;
};

const Overview = () => {
  const projectId = useProjectId();
  const radioValueRef = useRef<{ value: string } | null>({ value: '' });
  const [rangeTime, setRangeTime] = useState<(string | undefined)[]>(getTimeByType('day'));
  const [calculateType, setCalculateType] = useState<string | null>('0');
  const [rankType, setRankType] = useState<string>('followerCount');
  const [showDataSource, setShowDataSource] = useState<IndustryDashboard[]>([]);
  const [isMeRank, setIsMeRank] = useState<number | null>(null);
  const [accountType, setAccountType] = useState<number>(0);
  const dataSourceRef = useRef<IndustryDashboard[]>([]);
  const updateTime = useRef<string | null>(null);
  const [platform, setPlatform] = useState<PlatForm>(PlatForm.ALL);
  const { pollingExport, percent, pollingLoading } = usePollingExport();

  const { loading, run: getIndustryDashboard } = useRequest(GetIndustryDashboard, {
    manual: true,
    onSuccess: (res: any) => {
      dataSourceRef.current = res.data;
      updateTime.current = res.time;
      renderShowData(res.data);
    },
  });

  const renderShowData = (data: IndustryDashboard[]) => {
    // @ts-ignore
    const sortDataSource = data.sort((a, b) => Number(b[rankType]) - Number(a[rankType]));
    const isMeRank = sortDataSource.findIndex((item) => item.isMe === 1) + 1;
    setIsMeRank(isMeRank);
    setShowDataSource([...sortDataSource]);
  };

  const fetchIndustryDashboard = async () => {
    getIndustryDashboard({
      projectId,
      startDate: rangeTime[0],
      endDate: rangeTime[1],
      dateType: getDateType(radioValueRef.current?.value),
      accountType: accountType === 0 ? undefined : accountType,
      calculateType,
      platform,
    });
  };

  useEffect(() => {
    fetchIndustryDashboard();
  }, [projectId, rangeTime, accountType, calculateType, platform]);

  useEffect(() => {
    renderShowData(dataSourceRef.current);
  }, [rankType]);

  const handleExport = async () => {
    const res = await ExportIndustryDashboard({
      projectId,
      startDate: rangeTime[0],
      endDate: rangeTime[1],
      dateType: getDateType(radioValueRef.current?.value),
      accountType: accountType === 0 ? undefined : accountType,
      calculateType,
      platform,
    });
    if (res.data) {
      pollingExport({ taskId: res.data });
    } else {
      message.error('导出失败,请重试');
    }
  };

  return (
    <PageContainer
      title={<BreadCrumbSimple breadcrumbs={['行业管理', '行业总榜']} />}
      extra={
        <DataUpdateTime
          rangeTime={rangeTime}
          updateTime={updateTime.current}
          fetchData={fetchIndustryDashboard}
        />
      }
      style={{ minHeight: 'calc(100vh - 56px)' }}
    >
      <ProCard colSpan={{ md: 24, lg: 18 }} style={{ minHeight: 'calc(100vh - 56px - 100px)' }}>
        <Flex justify="space-between">
          <Space size="middle" direction="horizontal" wrap={true}>
            <div style={{ textWrap: 'nowrap' }}>
              <span>排行: </span>
              <Select
                style={{ width: '166px' }}
                value={rankType}
                onChange={(value) => {
                  setRankType(value);
                }}
                fieldNames={{ label: 'label', value: 'value' }}
                options={options}
              />
            </div>
            <div style={{ textWrap: 'nowrap' }}>
              <span>账号来源: </span>
              <Select
                style={{ width: '166px' }}
                defaultValue={0}
                onChange={(value) => setAccountType(value)}
                options={[
                  { value: 0, label: '全部' },
                  { value: 1, label: '官号' },
                  { value: 2, label: '经销商' },
                ]}
              />
            </div>
            <div style={{ textWrap: 'nowrap' }}>
              <span>平台: </span>
              <Select
                style={{ width: '166px' }}
                value={platform}
                onChange={(value) => setPlatform(value)}
                options={PlatformSelectOptions}
              />
            </div>
          </Space>
        </Flex>

        <Flex justify="space-between" style={{ marginTop: '20px' }}>
          <Space>
            <Tabs value={calculateType} onValueChange={(value) => setCalculateType(value)}>
              <TabsList value={calculateType}>
                <TabsTrigger value="0">总值</TabsTrigger>
                <TabsTrigger value="1">均值</TabsTrigger>
              </TabsList>
            </Tabs>
            <Tooltip title="均值计算方式：对应数据指标/相关账号总数">
              <QuestionCircleOutlined />
            </Tooltip>
            <TimeFilter
              value={rangeTime}
              onChange={(value) => setRangeTime(value)}
              ref={radioValueRef}
            />
          </Space>

          <Space size={20}>
            <div style={{ color: '#1E5EFF', fontSize: '16px', fontWeight: '500' }}>
              我的排名: {isMeRank}
            </div>
            <ExportButton
              exportFn={() => handleExport()}
              loading={pollingLoading}
              percent={percent}
              key="export"
            />
          </Space>
        </Flex>

        <ProTable
          ghost
          rowKey="industryName"
          tableClassName="custom-table"
          rowClassName="custom-table-row"
          style={{ marginTop: '20px' }}
          scroll={{ x: 'max-content' }}
          sticky={{ offsetHeader: STICKY_OFFSETHEADER }}
          pagination={false}
          search={false}
          toolBarRender={false}
          columns={renderColumns(rankType)}
          dataSource={showDataSource}
          loading={loading}
        />
      </ProCard>
    </PageContainer>
  );
};

export default Overview;
