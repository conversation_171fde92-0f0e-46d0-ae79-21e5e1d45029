import {
  DownloadAnchorScheduleTemplate,
  ExportAnchorSchedule,
  GetProjectAccountUserOverviewList,
  ProjectAccountDouyin,
} from '@/services/anchor';
import { DownOutlined, InboxOutlined, SearchOutlined } from '@ant-design/icons';
import { useRequest } from '@umijs/max';
import type { MenuProps } from 'antd';
import {
  Avatar,
  Button,
  DatePicker,
  DatePickerProps,
  Dropdown,
  Input,
  message,
  Modal,
  Space,
  Upload,
} from 'antd';
import dayjs from 'dayjs';
import { useAtom } from 'jotai';
import { useEffect, useState } from 'react';
import { accountIdAtom } from '../model/accountIdAtom';
import { Download, FolderInput } from 'lucide-react';
import ExportButton from '@/components/exportButton';
import { usePollingExport } from '@/hooks/usePollingExport';
import saveAs from 'file-saver';

const { RangePicker } = DatePicker;

type ScheduleActionType = {
  showRangeTime: string[];
  setShowRangeTime: React.Dispatch<React.SetStateAction<string[]>>;
  projectId?: string;
  platform: number;
  refreshSchedules: () => void;
};

export default function ScheduleAction(props: ScheduleActionType) {
  const { showRangeTime, setShowRangeTime, projectId, platform, refreshSchedules } = props;
  const atomKey = `${projectId || 'none'}_${platform}`;
  const [accountIdMap, setAccountIdMap] = useAtom(accountIdAtom);
  const selectedAccountId = accountIdMap[atomKey];
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { pollingExport, percent, pollingLoading } = usePollingExport(
    `主播排班_${dayjs().format('YYYYMMDD')}`,
  );
  const [searchValue, setSearchValue] = useState('');
  const [filteredAccounts, setFilteredAccounts] = useState<ProjectAccountDouyin[]>([]);

  const handleExport = async () => {
    const res = await ExportAnchorSchedule({
      projectId,
      platform,
      startDate: showRangeTime[0],
      endDate: showRangeTime[1],
    });
    if (res.data) {
      pollingExport({ taskId: res.data });
    } else {
      message.error('导出失败,请重试');
    }
  };

  const setSelectedAccountId = (id: string | undefined) => {
    setAccountIdMap((prev) => ({ ...prev, [atomKey]: id }));
  };

  const { data: accounts } = useRequest(
    () => GetProjectAccountUserOverviewList({ projectId, platform }),
    {
      refreshDeps: [projectId, platform],
      cacheKey: `GetProjectAccountUserOverviewList_${projectId || 'none'}_${platform}`,
      cacheTime: 10 * 60 * 1000, // 10 分钟
      onSuccess: (data) => {
        if (data) {
          let user = data.find((item) => item.accountId === selectedAccountId);
          if (!user) {
            user = data[0];
          }
          setSelectedAccountId(user?.accountId);
          setFilteredAccounts(data);
        }
      },
    },
  );

  // 切换账号时
  const handleAccountChange = (account: ProjectAccountDouyin) => {
    setSelectedAccountId(account?.accountId);
  };

  useEffect(() => {
    if (accounts && accounts?.length > 0 && selectedAccountId) {
      const user = accounts?.find((item) => item.accountId === selectedAccountId);
      if (user) {
        setSelectedAccountId(user.accountId);
      }
    }
  }, [selectedAccountId, accounts]);

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchValue(value);
    if (!accounts) return;

    if (!value.trim()) {
      setFilteredAccounts(accounts);
      return;
    }

    const filtered = accounts.filter(
      (item) =>
        item.nickname?.toLowerCase().includes(value.toLowerCase()) ||
        item.showAccountId?.toLowerCase().includes(value.toLowerCase()),
    );
    setFilteredAccounts(filtered);
  };

  const onChangeRange = (dates: any) => {
    setShowRangeTime([dates[0].format('YYYY-MM-DD'), dates[1].format('YYYY-MM-DD')]);
  };

  const disabledTwoWeekDate: DatePickerProps['disabledDate'] = (current, { from }) => {
    if (from) {
      return Math.abs(current.diff(from, 'days')) >= 30;
    }
    return false;
  };

  const items: MenuProps['items'] = filteredAccounts?.map((item) => ({
    key: String(item.accountId),
    label: (
      <div className="flex items-center">
        <div>
          <div className="text-sm font-bold">{item.nickname}</div>
          <div className="text-xs text-gray-400">抖音号：{item.showAccountId || '-'}</div>
        </div>
      </div>
    ),
    icon: <Avatar src={item.avatar} size={28} />,
  }));

  const onMenuClick: MenuProps['onClick'] = (info) => {
    const account = accounts?.find((item) => String(item.accountId) === info.key);
    if (account) {
      handleAccountChange(account);
    }
  };

  // 自定义下拉菜单内容
  const dropdownRender = (menu: React.ReactNode) => (
    <div className="rounded-md border border-new-media-gray-100 bg-white p-2 shadow-lg">
      <div className="pb-2">
        <Input
          placeholder="搜索账号或抖音号"
          prefix={<SearchOutlined />}
          value={searchValue}
          onChange={(e) => handleSearch(e.target.value)}
          className="w-full"
        />
      </div>
      {menu}
    </div>
  );

  const handleDownloadTemplate = async () => {
    const downloadBlob = await DownloadAnchorScheduleTemplate();
    saveAs(downloadBlob, '排班导入模版.xlsx');
  };

  const uploadProps = {
    name: 'file',
    maxCount: 1,
    multiple: false,
    accept: '.xlsx,.xls',
    action: `/new-media-api/anchor/schedule/import?projectId=${projectId}&platform=${platform}`,
    headers: {
      contentType: 'multipart/form-data',
    },
    showUploadList: false,
    onChange: (info: any) => {
      const { status } = info.file;
      if (status === 'done') {
        const respond = info.file.response;
        if (respond.code === 4001) {
          if (respond.data) {
            message.error(
              <span>
                文件内容有误，请
                <a
                  href={respond.data}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-500 underline"
                >
                  下载错误文件
                </a>
                ，修改后重新上传
              </span>,
              5,
            );
          } else {
            message.error('生成错误文件失败，请重试');
          }
        } else if (respond.code !== 0) {
          message.error(`${info.file.name} 文件上传失败, 错误原因: ${respond.msg}`);
        } else {
          message.success(`${info.file.name} 文件上传成功`);
        }
        setIsModalOpen(false);
        refreshSchedules();
      } else if (status === 'error') {
        message.error(`${info.file.name} 文件上传失败`);
      }
    },
  };

  return (
    <div className="flex rounded-lg bg-white px-5 py-[14px]">
      <div className="flex min-w-[260px] items-center gap-1">
        <Dropdown
          trigger={['click']}
          placement="bottomLeft"
          menu={{
            items,
            onClick: onMenuClick,
            className: 'max-h-80 overflow-y-auto',
          }}
          popupRender={dropdownRender}
        >
          <Space>
            <div className="flex cursor-pointer select-none items-center">
              <Avatar
                src={accounts?.find((item) => item.accountId === selectedAccountId)?.avatar}
                size={36}
              />
              <div className="ml-2">
                <div className="flex gap-2">
                  <div className="text-sm font-bold">
                    {accounts?.find((item) => item.accountId === selectedAccountId)?.nickname}
                  </div>
                  <DownOutlined />
                </div>
                <div className="text-xs text-gray-400">
                  抖音号：
                  {accounts?.find((item) => item.accountId === selectedAccountId)?.showAccountId ||
                    '-'}
                </div>
              </div>
            </div>
          </Space>
        </Dropdown>
      </div>
      <Space className="ml-auto">
        <RangePicker
          onChange={onChangeRange}
          disabledDate={disabledTwoWeekDate}
          allowClear={false}
          defaultValue={[dayjs(showRangeTime[0]), dayjs(showRangeTime[1])]}
          value={[dayjs(showRangeTime[0]), dayjs(showRangeTime[1])]}
          presets={[
            { label: '过去 7 天', value: [dayjs().add(-7, 'd'), dayjs()] },
            { label: '过去 14 天', value: [dayjs().add(-13, 'd'), dayjs()] },
            { label: '未来 7 天', value: [dayjs(), dayjs().add(7, 'd')] },
            { label: '未来 14 天', value: [dayjs(), dayjs().add(13, 'd')] },
          ]}
        />
        <Button onClick={() => setIsModalOpen(true)}>
          <FolderInput size={16} strokeWidth={1.5} />
          <span>导入排班</span>
        </Button>
        <ExportButton
          exportFn={() => handleExport()}
          loading={pollingLoading}
          percent={percent}
          text="导出排班"
          key="export"
        />
      </Space>

      <Modal
        title="批量导入主播排班"
        open={isModalOpen}
        onCancel={() => setIsModalOpen(false)}
        footer={null}
      >
        <Upload.Dragger {...uploadProps}>
          <p className="ant-upload-drag-icon">
            <InboxOutlined />
          </p>
          <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
        </Upload.Dragger>

        <Space style={{ marginBlock: '4px' }}>
          <Button type="link" onClick={handleDownloadTemplate} style={{ padding: 0 }}>
            <Download size={14} />
            下载导入模板
          </Button>
          <div className="text-[#A2A4A8]">支持 .xlsx、.xls 格式的文件</div>
        </Space>
      </Modal>
    </div>
  );
}
