import { GetAnchorLeads } from '@/services/anchor';
import { echartDefaultColor } from '@/utils/commonStyle';
import { PlatForm } from '@/utils/platform';
import { PieChartOutlined, TableOutlined } from '@ant-design/icons';
import { useRequest } from '@umijs/max';
import { ConfigProvider, Empty, Segmented, Spin, Table } from 'antd';
import { ColumnType } from 'antd/es/table';
import ReactECharts from 'echarts-for-react';
import { useMemo, useState } from 'react';

type AnchorLeadsProps = {
  anchorIds: string[];
  accountIds: string[];
  projectId?: string;
  rangeTime: (string | undefined)[];
  platform: PlatForm;
};

type ProcessedAnchorLeads = {
  anchorName: string;
  leadsCount: number;
  percentage: string;
  percentageValue: number;
};

export default function AnchorLeads(props: AnchorLeadsProps) {
  const { anchorIds, accountIds, projectId, rangeTime, platform } = props;
  const [viewMode, setViewMode] = useState<'chart' | 'table'>('chart');

  // 获取数据
  const { data: originalData, loading } = useRequest(
    () => {
      const [startDate, endDate] = rangeTime;
      if (!startDate || !endDate) return Promise.reject();

      return GetAnchorLeads({
        startDate,
        endDate,
        platform,
        projectId,
        anchorIds,
        accountIds,
      });
    },
    {
      refreshDeps: [rangeTime, anchorIds, accountIds, projectId, platform],
    },
  );

  const data = originalData?.map((item) => ({
    ...item,
    leadsCount: Number(item.leadsCount),
  }));

  // 处理数据，计算占比，并按线索数排序
  const processedData = useMemo(() => {
    const anchorLeadsData = data || [];
    if (anchorLeadsData.length === 0) return [] as ProcessedAnchorLeads[];

    // 计算总线索数
    const totalLeads = anchorLeadsData.reduce((total, item) => total + (item.leadsCount || 0), 0);

    // 处理数据，添加百分比
    return anchorLeadsData
      .map((item) => ({
        anchorName: item.anchorName || '未知主播',
        leadsCount: item.leadsCount || 0,
        percentage: totalLeads
          ? `${(((item.leadsCount || 0) / totalLeads) * 100).toFixed(2)}%`
          : '0%',
        percentageValue: totalLeads ? ((item.leadsCount || 0) / totalLeads) * 100 : 0,
      }))
      .sort((a, b) => b.leadsCount - a.leadsCount);
  }, [data]);

  // 表格列定义
  const columns: ColumnType<ProcessedAnchorLeads>[] = [
    {
      title: '主播名称',
      dataIndex: 'anchorName',
      key: 'anchorName',
    },
    {
      title: '线索条数',
      dataIndex: 'leadsCount',
      key: 'leadsCount',
      sorter: (a, b) => a.leadsCount - b.leadsCount,
    },
    {
      title: '线索占比',
      dataIndex: 'percentage',
      key: 'percentage',
      sorter: (a, b) => a.percentageValue - b.percentageValue,
    },
  ];

  // 饼图配置
  const pieOption = useMemo(() => {
    if (processedData.length === 0) return {};

    // 取前5名主播
    const top5 = processedData.slice(0, 5);

    // 计算其他主播的总线索数
    const othersData = processedData.slice(5).reduce(
      (acc, curr) => {
        return {
          leadsCount: acc.leadsCount + curr.leadsCount,
          percentageValue: acc.percentageValue + curr.percentageValue,
        };
      },
      { leadsCount: 0, percentageValue: 0 },
    );

    // 构建饼图数据
    const pieData = [
      ...top5.map((item) => ({
        name: item.anchorName,
        value: item.leadsCount,
        percentage: item.percentage,
      })),
    ];

    // 如果有其他数据，添加到饼图数据中
    if (othersData.leadsCount > 0) {
      pieData.push({
        name: '其他',
        value: othersData.leadsCount,
        percentage: `${othersData.percentageValue.toFixed(2)}%`,
      });
    }

    return {
      tooltip: {
        trigger: 'item',
        formatter: (params: any) => {
          return `${params.name}<br/>线索条数：${params.value}<br/>占比：${params.data.percentage}`;
        },
      },
      color: echartDefaultColor,
      legend: {
        orient: 'vertical',
        right: 10,
        top: 'center',
        type: 'scroll',
      },
      series: [
        {
          name: '线索贡献',
          type: 'pie',
          radius: '70%',
          avoidLabelOverlap: false,
          itemStyle: {
            borderColor: '#fff',
            borderWidth: 2,
          },
          label: {
            show: true,
            formatter: (params: any) => params.data.percentage,
            position: 'outside',
            fontSize: 12,
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 16,
              fontWeight: 'bold',
            },
          },
          labelLine: {
            show: true,
            length: 10,
            length2: 10,
          },
          data: pieData,
        },
      ],
    };
  }, [processedData]);

  // 渲染内容
  const renderContent = () => {
    if (loading) {
      return (
        <div className="flex h-[240px] items-center justify-center">
          <Spin />
        </div>
      );
    }

    if (processedData.length === 0) {
      return (
        <div className="flex h-[240px] items-center justify-center">
          <Empty description="暂无数据" />
        </div>
      );
    }

    if (viewMode === 'chart') {
      return (
        <ReactECharts option={pieOption} notMerge={true} style={{ height: 300 }} className="mt-4" />
      );
    } else {
      return (
        <ConfigProvider
          theme={{
            components: {
              Table: {
                headerBorderRadius: 0,
              },
            },
          }}
        >
          <Table
            dataSource={processedData}
            columns={columns}
            rowKey="anchorName"
            pagination={false}
            className="mt-[10px]"
            scroll={{ y: 240 }}
            size="middle"
          />
        </ConfigProvider>
      );
    }
  };

  return (
    <div className="min-h-[420px] w-[49%] rounded-lg bg-white p-[20px]">
      <div className="flex items-center justify-between">
        <div className="text-base font-medium">线索贡献概览</div>
        <Segmented
          options={[
            {
              label: <PieChartOutlined />,
              value: 'chart',
            },
            {
              label: <TableOutlined />,
              value: 'table',
            },
          ]}
          value={viewMode}
          onChange={(value) => setViewMode(value as 'chart' | 'table')}
        />
      </div>

      {renderContent()}
    </div>
  );
}
