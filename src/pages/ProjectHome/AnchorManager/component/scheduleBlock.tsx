import defaultAvatar from '@/assets/default-avatar.png';
import { AnchorScheduleItem, AnchorVO } from '@/services/anchor';
import { Avatar, Popover, Tooltip } from 'antd';
import dayjs from 'dayjs';
import duration from 'dayjs/plugin/duration';
import { Trash2 } from 'lucide-react';
import { SelectionArea, totalMinutes } from './const';
import PopoverContent from './popoverContent';
import { cn } from '@/lib/utils';
import { useDarkMode } from '@/hooks/useDarkMode';

dayjs.extend(duration);

type ScheduleBlockState = {
  isDragging: boolean;
  originalSchedule: AnchorScheduleItem | null;
  showSchedulePopover: boolean;
  showPopover: boolean;
  setShowSchedulePopover: React.Dispatch<React.SetStateAction<boolean>>;
  setShowPopover: React.Dispatch<React.SetStateAction<boolean>>;
};

type ScheduleBlockHandlers = {
  handleDragStart: (
    e: React.MouseEvent,
    schedule: AnchorScheduleItem,
    type: 'left' | 'right',
  ) => void;
  handleSaveSchedule: () => void;
  handleCancelSchedule: () => void;
  handleDeleteSchedule: (schedule: AnchorScheduleItem) => void;
};

type ScheduleBlockProps = {
  handlers: ScheduleBlockHandlers;
  state: ScheduleBlockState;
  schedule: AnchorScheduleItem;
  allSchedules?: AnchorScheduleItem[];
  currentSchedule: Partial<AnchorScheduleItem>;
  setCurrentSchedule: (schedule: AnchorScheduleItem) => void;
  setSelectionArea: (area: SelectionArea | null) => void;
  projectId?: string;
  platform: number;
  anchorListData?: AnchorVO[];
  canPaste: boolean;
};

const statusClassMap = {
  light: {
    0: 'bg-[#F4F7FF]',
    1: 'bg-[#E7F6F0]',
    2: 'bg-[#F4F7FF]',
    3: 'bg-[#ECEEF2]',
  },
  dark: {
    0: 'bg-[#1B2A4F]',
    1: 'bg-[#18322D]',
    2: 'bg-[#1B2A4F]',
    3: 'bg-[#313439]',
  },
};

// const statusTextMap = {
//   0: '未设置',
//   1: '已上播',
//   2: '未开播',
//   3: '未上播',
// };

export default function ScheduleBlock(props: ScheduleBlockProps) {
  const {
    handlers,
    state,
    schedule,
    currentSchedule,
    setCurrentSchedule,
    setSelectionArea,
    allSchedules,
    projectId,
    platform,
    anchorListData,
    canPaste,
  } = props;

  const {
    isDragging,
    originalSchedule,
    showSchedulePopover,
    showPopover,
    setShowSchedulePopover,
    setShowPopover,
  } = state;

  const { isDarkMode } = useDarkMode();
  const { handleDragStart, handleSaveSchedule, handleCancelSchedule, handleDeleteSchedule } =
    handlers;

  const startTime = dayjs(schedule.startTime, 'YYYY-MM-DD HH:mm:ss');
  const endTime = dayjs(schedule.endTime, 'YYYY-MM-DD HH:mm:ss');
  const startOfDay = dayjs(schedule.startTime, 'YYYY-MM-DD HH:mm:ss').startOf('day');

  const startMinutes = startTime.diff(startOfDay, 'minute');
  const endMinutes = endTime.diff(startOfDay, 'minute');
  const duration = endMinutes - startMinutes;
  const left = (startMinutes / totalMinutes) * 100;
  const width = (duration / totalMinutes) * 100;

  const isCurrentDragging = isDragging && originalSchedule?.scheduleId === schedule.scheduleId;
  const currentLeft =
    isCurrentDragging && currentSchedule.startTime
      ? ((startMinutes + 1) / totalMinutes) * 100
      : left;
  const currentWidth =
    isCurrentDragging && currentSchedule.startTime && currentSchedule.endTime
      ? ((duration - 2) / totalMinutes) * 100
      : width;

  const calculateDuration = () => {
    const startTime = dayjs(
      isCurrentDragging ? currentSchedule.startTime : schedule.startTime,
      'YYYY-MM-DD HH:mm:ss',
    );
    const endTime = dayjs(
      isCurrentDragging ? currentSchedule.endTime : schedule.endTime,
      'YYYY-MM-DD HH:mm:ss',
    );
    const diff = dayjs.duration(endTime.diff(startTime));
    const hours = diff.hours();
    const minutes = diff.minutes();
    return `${hours}h${minutes}m`;
  };

  // 判断是否为过去排班（以排班的日期+startTime为准）
  const isPastSchedule = dayjs(schedule.startTime).isBefore(dayjs());

  const bgClass =
    (isDarkMode ? statusClassMap.dark : statusClassMap.light)[schedule.liveStatus ?? 0] || '';

  return (
    <Tooltip
      title={
        <div>
          <div>主播: {schedule.anchor?.name}</div>
          <div className="text-nowrap">
            时间: {dayjs(schedule.startTime).format('HH:mm')}-
            {dayjs(schedule.endTime).format('HH:mm')}
            <span className="ml-1">{calculateDuration()}</span>
          </div>
          {schedule.description && <div>备注: {schedule.description}</div>}
        </div>
      }
      mouseEnterDelay={0.5}
    >
      <Popover
        open={
          showSchedulePopover &&
          schedule.scheduleId === currentSchedule.scheduleId &&
          !showPopover &&
          !isDragging
        }
        onOpenChange={setShowSchedulePopover}
        content={
          <PopoverContent
            currentSchedule={schedule}
            handleSaveSchedule={handleSaveSchedule}
            handleCancelSchedule={handleCancelSchedule}
            allSchedules={allSchedules}
            projectId={projectId}
            platform={platform}
            anchorListData={anchorListData}
            isPastSchedule={isPastSchedule}
          />
        }
        trigger="click"
        placement="rightTop"
        overlayClassName="render-schedule-popover"
        destroyOnHidden
        key={schedule.scheduleId}
        getPopupContainer={() => document.querySelector('.schedule-body') || document.body}
      >
        <div
          key={schedule.scheduleId}
          className={cn(
            'schedule-block group absolute z-50 box-border h-[55px] cursor-pointer select-none overflow-hidden border border-white',
            bgClass,
            {
              'border border-[#3369EE]':
                isDragging && currentSchedule.scheduleId === schedule.scheduleId,
            },
          )}
          style={{
            left: `${currentLeft}%`,
            width: `${currentWidth}%`,
            top: '0px',
          }}
          onClick={() => {
            if (isDragging) return;
            // 点击已有排班 Dom 的时候把划选区域状态清空
            setSelectionArea(null);
            setShowPopover(false);
            setCurrentSchedule(schedule);
            setShowSchedulePopover(true);
          }}
        >
          <div className="relative h-full">
            {!canPaste && schedule.liveStatus !== 1 && (
              <>
                {/* 左侧拖拽区域 */}
                <div
                  className="resize-handle absolute -left-[10px] bottom-0 top-0 z-[60] w-4 cursor-ew-resize"
                  onMouseDown={(e) => handleDragStart(e, schedule, 'left')}
                  onClick={(e) => e.stopPropagation()}
                />
                {/* 右侧拖拽区域 */}
                <div
                  className="resize-handle absolute -right-[10px] bottom-0 top-0 z-[60] w-4 cursor-ew-resize"
                  onMouseDown={(e) => handleDragStart(e, schedule, 'right')}
                  onClick={(e) => e.stopPropagation()}
                />
              </>
            )}
            <div className="flex h-full flex-col justify-center gap-1 px-2 text-xs">
              <div className="flex items-center">
                <div className="flex items-center gap-1">
                  <Avatar
                    size={20}
                    src={schedule.anchor?.avatarUrl || <img src={defaultAvatar} />}
                    alt="avatar"
                  />
                  <div className="text-nowrap text-sm">{schedule.anchor?.name}</div>
                </div>
              </div>
              <div className="text-nowrap text-new-media-gray-600">
                {schedule?.description && <span className="mr-1">{schedule?.description}</span>}
                {isCurrentDragging
                  ? `${dayjs(currentSchedule.startTime).format('HH:mm')}-${dayjs(currentSchedule.endTime).format('HH:mm')}`
                  : `${dayjs(schedule.startTime).format('HH:mm')}-${dayjs(schedule.endTime).format('HH:mm')}`}
                {/* 显示持续了多长时间 */}
                <span className="ml-1">{calculateDuration()}</span>
              </div>
            </div>
            <div className="absolute right-0 top-0 rounded-bl-md bg-new-media-blue-200 pb-[2px] pl-[2px] opacity-0 group-hover:opacity-100">
              {!isPastSchedule && (
                <Trash2
                  size={15}
                  className="cursor-pointer text-new-media-blue-900"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDeleteSchedule(schedule);
                  }}
                />
              )}
            </div>
          </div>
        </div>
      </Popover>
    </Tooltip>
  );
}
