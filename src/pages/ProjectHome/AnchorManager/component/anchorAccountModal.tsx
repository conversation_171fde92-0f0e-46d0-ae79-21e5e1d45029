import { AnchorAccountReportVO, AnchorOverviewReport, GetAnchorAccount } from '@/services/anchor';
import { useRequest } from '@umijs/max';
import { Modal, Table } from 'antd';
import { useEffect } from 'react';
import { PlatForm } from '@/utils/platform';
import { formatSecond } from '@/utils/time';
import { ColumnsType } from 'antd/es/table';

type AnchorAccountModalProps = {
  anchor?: AnchorOverviewReport;
  anchorAccountVisible: boolean;
  setAnchorAccountVisible: (visible: boolean) => void;
  rangeTime?: (string | undefined)[];
  projectId?: string;
  platform: PlatForm;
};

export default function AnchorAccountModal(props: AnchorAccountModalProps) {
  const { anchor, anchorAccountVisible, setAnchorAccountVisible, rangeTime, projectId, platform } =
    props;

  const { data, loading, run } = useRequest(GetAnchorAccount, {
    manual: true,
  });

  useEffect(() => {
    if (
      anchorAccountVisible &&
      anchor?.anchorId &&
      rangeTime?.[0] &&
      rangeTime?.[1] &&
      projectId &&
      platform
    ) {
      run({
        anchorId: anchor.anchorId,
        startDate: rangeTime[0],
        endDate: rangeTime[1],
        projectId,
        platform,
      });
    }
  }, [anchorAccountVisible, anchor?.anchorId, rangeTime, projectId, platform]);

  const columns: ColumnsType<AnchorAccountReportVO> = [
    {
      title: '抖音号',
      dataIndex: 'showAccountId',
      key: 'showAccountId',
      align: 'right',
      width: 120,
    },
    {
      title: '直播时长(小时)',
      dataIndex: 'liveDuration',
      key: 'liveDuration',
      render: (_, record) => formatSecond(record.liveDuration),
      align: 'right',
      width: 160,
    },
    {
      title: '曝光人数',
      dataIndex: 'exposureUcount',
      key: 'exposureUcount',
      align: 'right',
      width: 120,
    },
    {
      title: '曝光次数',
      dataIndex: 'exposureCount',
      key: 'exposureCount',
      align: 'right',
      width: 120,
    },
    {
      title: '观看人数',
      dataIndex: 'viewCount',
      key: 'viewCount',
      align: 'right',
      width: 120,
    },
    {
      title: '观看次数',
      dataIndex: 'viewTime',
      key: 'viewTime',
      align: 'right',
      width: 120,
    },
    {
      title: '小风车点击次数',
      dataIndex: 'componentClickCount',
      key: 'componentClickCount',
      align: 'right',
      width: 160,
    },
    {
      title: '点赞次数',
      dataIndex: 'diggCount',
      key: 'diggCount',
      align: 'right',
      width: 120,
    },
    {
      title: '评论次数',
      dataIndex: 'commentCount',
      key: 'commentCount',
      align: 'right',
      width: 120,
    },
    {
      title: '分享次数',
      dataIndex: 'shareCount',
      key: 'shareCount',
      align: 'right',
      width: 120,
    },
    {
      title: '关注人数',
      dataIndex: 'followCount',
      key: 'followCount',
      align: 'right',
      width: 120,
    },
    {
      title: '留资线索数',
      dataIndex: 'leadsCount',
      key: 'leadsCount',
      align: 'right',
      width: 120,
    },
  ];

  return (
    <Modal
      title={`${anchor?.anchorName || ''}的明细数据`}
      open={anchorAccountVisible}
      width={1300}
      footer={null}
      onCancel={() => setAnchorAccountVisible(false)}
    >
      <Table<AnchorAccountReportVO>
        columns={columns}
        dataSource={data}
        loading={loading}
        pagination={false}
        size="small"
        rowKey="accountId"
        className="mt-8"
      />
    </Modal>
  );
}
