import { useEffect, useMemo, useRef } from 'react';
import ReactECharts from 'echarts-for-react';

// 定义星期类型
type WeekDay = '星期一' | '星期二' | '星期三' | '星期四' | '星期五' | '星期六' | '星期日';

// 定义时间段类型
type TimeSlot = '0:00' | '3:00' | '6:00' | '9:00' | '12:00' | '15:00' | '18:00' | '24:00';

// 定义热力图单元格数据类型
interface HeatMapCell {
  value: number;
  weekDay: WeekDay;
  timeSlot: TimeSlot;
}

// 定义热力图数据类型
interface HeatMapData {
  title: string;
  cells: HeatMapCell[];
}

// 定义热力图组件属性
interface AnchorHeatMapProps {
  data?: HeatMapData;
}

// 模拟数据
const mockData: HeatMapData = {
  title: '直播时段热力分析',
  cells: [
    // 星期一
    { weekDay: '星期一', timeSlot: '0:00', value: 1 },
    { weekDay: '星期一', timeSlot: '3:00', value: 5 },
    { weekDay: '星期一', timeSlot: '6:00', value: 2 },
    { weekDay: '星期一', timeSlot: '9:00', value: 1 },
    { weekDay: '星期一', timeSlot: '12:00', value: 7 },
    { weekDay: '星期一', timeSlot: '15:00', value: 3 },
    { weekDay: '星期一', timeSlot: '18:00', value: 3 },
    { weekDay: '星期一', timeSlot: '24:00', value: 0 },
    // 星期二
    { weekDay: '星期二', timeSlot: '0:00', value: 1 },
    { weekDay: '星期二', timeSlot: '3:00', value: 5 },
    { weekDay: '星期二', timeSlot: '6:00', value: 2 },
    { weekDay: '星期二', timeSlot: '9:00', value: 1 },
    { weekDay: '星期二', timeSlot: '12:00', value: 7 },
    { weekDay: '星期二', timeSlot: '15:00', value: 3 },
    { weekDay: '星期二', timeSlot: '18:00', value: 1 },
    { weekDay: '星期二', timeSlot: '24:00', value: 0 },
    // 星期三
    { weekDay: '星期三', timeSlot: '0:00', value: 1 },
    { weekDay: '星期三', timeSlot: '3:00', value: 5 },
    { weekDay: '星期三', timeSlot: '6:00', value: 2 },
    { weekDay: '星期三', timeSlot: '9:00', value: 1 },
    { weekDay: '星期三', timeSlot: '12:00', value: 7 },
    { weekDay: '星期三', timeSlot: '15:00', value: 3 },
    { weekDay: '星期三', timeSlot: '18:00', value: 1 },
    { weekDay: '星期三', timeSlot: '24:00', value: 0 },
    // 星期四
    { weekDay: '星期四', timeSlot: '0:00', value: 1 },
    { weekDay: '星期四', timeSlot: '3:00', value: 5 },
    { weekDay: '星期四', timeSlot: '6:00', value: 2 },
    { weekDay: '星期四', timeSlot: '9:00', value: 1 },
    { weekDay: '星期四', timeSlot: '12:00', value: 7 },
    { weekDay: '星期四', timeSlot: '15:00', value: 3 },
    { weekDay: '星期四', timeSlot: '18:00', value: 1 },
    { weekDay: '星期四', timeSlot: '24:00', value: 0 },
    // 星期五
    { weekDay: '星期五', timeSlot: '0:00', value: 1 },
    { weekDay: '星期五', timeSlot: '3:00', value: 5 },
    { weekDay: '星期五', timeSlot: '6:00', value: 2 },
    { weekDay: '星期五', timeSlot: '9:00', value: 1 },
    { weekDay: '星期五', timeSlot: '12:00', value: 7 },
    { weekDay: '星期五', timeSlot: '15:00', value: 3 },
    { weekDay: '星期五', timeSlot: '18:00', value: 3 },
    { weekDay: '星期五', timeSlot: '24:00', value: 0 },
    // 星期六
    { weekDay: '星期六', timeSlot: '0:00', value: 1 },
    { weekDay: '星期六', timeSlot: '3:00', value: 5 },
    { weekDay: '星期六', timeSlot: '6:00', value: 2 },
    { weekDay: '星期六', timeSlot: '9:00', value: 1 },
    { weekDay: '星期六', timeSlot: '12:00', value: 7 },
    { weekDay: '星期六', timeSlot: '15:00', value: 3 },
    { weekDay: '星期六', timeSlot: '18:00', value: 1 },
    { weekDay: '星期六', timeSlot: '24:00', value: 0 },
    // 星期日
    { weekDay: '星期日', timeSlot: '0:00', value: 1 },
    { weekDay: '星期日', timeSlot: '3:00', value: 5 },
    { weekDay: '星期日', timeSlot: '6:00', value: 2 },
    { weekDay: '星期日', timeSlot: '9:00', value: 1 },
    { weekDay: '星期日', timeSlot: '12:00', value: 7 },
    { weekDay: '星期日', timeSlot: '15:00', value: 3 },
    { weekDay: '星期日', timeSlot: '18:00', value: 1 },
    { weekDay: '星期日', timeSlot: '24:00', value: 0 },
  ],
};

// 定义星期顺序（从下到上）
const weekDays: WeekDay[] = ['星期日', '星期六', '星期五', '星期四', '星期三', '星期二', '星期一'];

// 定义时间段顺序（从左到右）
const timeSlots: TimeSlot[] = ['0:00', '3:00', '6:00', '9:00', '12:00', '15:00', '18:00', '24:00'];

// 热力图组件
export default function AnchorHeatMap({ data = mockData }: AnchorHeatMapProps) {
  // 图表实例引用
  const chartRef = useRef<any>(null);

  // 转换数据为 ECharts 热力图格式
  const heatmapData = useMemo(() => {
    return data.cells.map((cell) => {
      const yIndex = weekDays.indexOf(cell.weekDay);
      const xIndex = timeSlots.indexOf(cell.timeSlot);
      return [xIndex, yIndex, cell.value];
    });
  }, [data.cells]);

  // 获取数据的最大值
  const maxValue = useMemo(() => {
    return Math.max(...data.cells.map((cell) => cell.value));
  }, [data.cells]);

  // 配置 ECharts 选项
  const option: any = useMemo(() => {
    return {
      tooltip: {
        position: 'top',
        formatter: (params: any) => {
          const value = params.data[2];
          const weekDay = weekDays[params.data[1]];
          const timeSlot = timeSlots[params.data[0]];
          return `${weekDay} ${timeSlot}<br/>数值: ${value}`;
        },
      },
      grid: {
        top: '15%',
        left: '5%',
        right: '0%',
        bottom: '0%',
      },
      xAxis: {
        type: 'category',
        data: timeSlots,
        splitArea: {
          show: false,
        },
        splitLine: {
          show: false,
        },
        axisLabel: {
          color: '#64666B',
          fontSize: 12,
        },
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
      },
      yAxis: {
        type: 'category',
        data: weekDays,
        splitArea: {
          show: false,
        },
        splitLine: {
          show: false,
        },
        axisLabel: {
          color: '#64666B',
          fontSize: 12,
        },
        axisLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
      },
      visualMap: {
        min: 0,
        max: maxValue,
        calculable: false,
        type: 'continuous',
        orient: 'horizontal',
        top: 10,
        left: 0,
        precision: 0,
        text: ['10', '0'],
        textStyle: {
          color: '#86909C',
          fontSize: 12,
        },
        inRange: {
          color: ['#F8FAFF', '#8EAEFF', '#1E5EFF'],
        },
      },
      series: [
        {
          name: '热力值',
          type: 'heatmap',
          data: heatmapData,
          label: {
            show: true,
            color: (params: any) => {
              const value = params.data[2];
              // 根据值的大小决定文字颜色
              if (value <= 1) return '#0E0F14';
              if (value <= 3) return '#0E0F14';
              return '#FFFFFF';
            },
          },
          itemStyle: {
            borderColor: '#FFFFFF',
            borderWidth: 1,
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowColor: 'rgba(0, 0, 0, 0.5)',
              borderColor: '#FFFFFF',
              borderWidth: 1,
            },
          },
        },
      ],
    };
  }, [heatmapData, maxValue]);

  // 当窗口大小变化时，调整图表大小
  useEffect(() => {
    const handleResize = () => {
      chartRef.current?.resize();
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // 获取图表实例
  const onChartReady = (instance: any) => {
    chartRef.current = instance;
  };

  return (
    <div className="min-h-[420px] w-[49%] rounded-lg border border-[#ECEEF2] bg-white p-5">
      <div className="text-base font-medium">直播时段热力分析</div>
      <ReactECharts
        option={option}
        style={{ height: '100%', width: '100%' }}
        onChartReady={onChartReady}
        notMerge={true}
        lazyUpdate={true}
      />
    </div>
  );
}
