import {
  AnchorScheduleItem,
  AnchorVO,
  CreateAnchorSchedule,
  UpdateAnchorSchedule,
} from '@/services/anchor';
import { Button, Form, Input, message, Select, Space, TimePicker } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import { useAtomValue } from 'jotai';
import { useEffect } from 'react';
import { accountIdAtom } from '../model/accountIdAtom';

type PopoverContentProps = {
  currentSchedule: Partial<AnchorScheduleItem>;
  handleSaveSchedule: (scheduleId?: string) => void;
  handleCancelSchedule: () => void;
  allSchedules?: AnchorScheduleItem[];
  projectId?: string;
  platform: number;
  anchorListData?: AnchorVO[];
  isPastSchedule?: boolean;
};

type ScheduleItemForm = {
  anchor?: string | number;
  startTime: Dayjs;
  endTime: Dayjs;
  description: string;
  date: Dayjs;
};

export default function PopoverContent(props: PopoverContentProps) {
  const {
    currentSchedule,
    handleSaveSchedule,
    handleCancelSchedule,
    allSchedules,
    projectId,
    platform,
    anchorListData,
    // isPastSchedule = false,
  } = props;
  const [form] = Form.useForm<ScheduleItemForm>();
  const atomKey = `${projectId || 'none'}_${platform}`;
  const accountIdMap = useAtomValue(accountIdAtom);
  const selectedAccountId = accountIdMap[atomKey];

  // 检查时间是否重叠（只检测，不做自动调整）
  const checkTimeOverlap = (
    startTime: Dayjs,
    endTime: Dayjs,
    scheduleId?: string,
  ): {
    hasOverlap: boolean;
  } => {
    // 过滤出当天的日程
    const sameDaySchedules = allSchedules?.filter(
      (schedule) =>
        schedule.scheduleDate === currentSchedule?.scheduleDate &&
        schedule.scheduleId !== scheduleId,
    );

    let hasOverlap = false;

    if (sameDaySchedules) {
      // 检查是否有时间重叠
      for (const schedule of sameDaySchedules) {
        const scheduleStart = dayjs(schedule.startTime);
        const scheduleEnd = dayjs(schedule.endTime);

        // 检查时间重叠的条件
        const isOverlapping =
          ((startTime.isSame(scheduleStart) || startTime.isAfter(scheduleStart)) &&
            startTime.isBefore(scheduleEnd)) ||
          (endTime.isAfter(scheduleStart) &&
            (endTime.isSame(scheduleEnd) || endTime.isBefore(scheduleEnd))) ||
          (startTime.isBefore(scheduleStart) && endTime.isAfter(scheduleEnd));

        if (isOverlapping) {
          hasOverlap = true;
          break;
        }
      }
    }

    return {
      hasOverlap,
    };
  };

  const onFinish = async (values: ScheduleItemForm) => {
    // 检查时间是否重叠
    const { hasOverlap } = checkTimeOverlap(
      values.startTime,
      values.endTime,
      currentSchedule?.scheduleId,
    );

    if (hasOverlap) {
      message.error('时间冲突，请选择其他时间段！');
      return;
    }

    if (!selectedAccountId) {
      message.error('请先选择账号');
      return;
    }

    // 获取当前日期部分
    const currentDate = currentSchedule?.scheduleDate;
    // 获取所选的时间部分，例如通过 values.timeRange 获取
    const selectedStartTime = values.startTime.format('HH:mm:00');
    const selectedEndTime = values.endTime.format('HH:mm:00');
    // 将日期部分与所选的时间部分拼接
    const startTime = `${currentDate} ${selectedStartTime}`;
    const endTime = `${currentDate} ${selectedEndTime}`;

    const payload = {
      accountId: selectedAccountId,
      anchorId: Number(values.anchor),
      startTime,
      endTime,
      description: values.description,
    };

    const commonParams = { projectId, platform };

    // 判断是否是临时scheduleId（selectionArea的拖拽）
    const isTemporarySchedule = currentSchedule.scheduleId?.toString().startsWith('temp-');

    // 如果是批量设置未设置主播的时候提交
    if (currentSchedule.liveStatus === 0 || isTemporarySchedule) {
      await CreateAnchorSchedule(commonParams, payload);
    } else if (currentSchedule.scheduleId && selectedAccountId) {
      // 已存在 scheduleId 表示更新
      await UpdateAnchorSchedule(commonParams, {
        ...payload,
        id: currentSchedule.scheduleId,
      });
    } else {
      await CreateAnchorSchedule(commonParams, payload);
    }

    handleSaveSchedule(currentSchedule?.scheduleId);
  };

  const anchorInitialValue = () => {
    if (currentSchedule?.anchor?.id) {
      return currentSchedule.anchor.id;
    } else if (currentSchedule.liveStatus === 0) {
      return undefined;
    }
    return undefined;
  };

  const initialValues = {
    anchor: anchorInitialValue(),
    startTime: dayjs(currentSchedule?.startTime),
    endTime: dayjs(currentSchedule?.endTime),
    description: currentSchedule?.description,
  };

  useEffect(() => {
    form.setFieldsValue(initialValues);
  }, [currentSchedule]);

  // 获取禁用时间配置
  const getDisabledTime = () => {
    // 移除对当天过去时间的禁用逻辑
    return {};

    // 原代码：
    // if (
    //   currentSchedule?.scheduleDate &&
    //   dayjs(currentSchedule.scheduleDate).isSame(dayjs(), 'day')
    // ) {
    //   const now = dayjs();
    //   return {
    //     disabledHours: () => Array.from({ length: 24 }, (_, i) => i).filter((h) => h < now.hour()),
    //     disabledMinutes: (selectedHour: number) => {
    //       if (selectedHour === now.hour()) {
    //         return Array.from({ length: 60 }, (_, i) => i).filter((m) => m < now.minute());
    //       }
    //       return [];
    //     },
    //   };
    // }
    // return {};
  };

  return (
    <div
      className="w-64 select-none"
      onClick={(e) => e.stopPropagation()}
      onMouseDown={(e) => e.stopPropagation()}
      // 很神奇如果打开的话，点击 Select 的滚动条，会导致 Select 卡住，原因可能是 antd 的 ant-select-dropdown 全局监听相关事件了但阻止冒泡了导致监听失效
      // onMouseMove={(e) => e.stopPropagation()}
      // onMouseUp={(e) => e.stopPropagation()}
    >
      <Form<ScheduleItemForm>
        layout="vertical"
        onFinish={onFinish}
        form={form}
        initialValues={initialValues}
      >
        <Form.Item
          label="主播"
          name="anchor"
          rules={[{ required: true, message: '请选择主播' }]}
          style={{ marginBottom: 10 }}
        >
          <Select
            showSearch
            filterOption={(input, option) =>
              (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
            }
            options={
              anchorListData && anchorListData.length > 0
                ? anchorListData.map((item) => ({ value: item.id, label: item.name }))
                : []
            }
            style={{ width: '100%' }}
            getPopupContainer={(triggerNode) => triggerNode.parentElement || document.body}
          />
        </Form.Item>

        <Form.Item label="时间" style={{ marginBottom: 10 }}>
          <div className="flex items-center">
            <Form.Item
              name="startTime"
              noStyle
              dependencies={['endTime']}
              rules={[
                { required: true, message: '请选择开始时间' },
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    const endTime = getFieldValue('endTime');
                    if (value && endTime) {
                      const startMinutes = value.hour() * 60 + value.minute();
                      const endMinutes = endTime.hour() * 60 + endTime.minute();
                      if (startMinutes > endMinutes) {
                        return Promise.reject('开始时间不能大于结束时间');
                      }
                    }
                    return Promise.resolve();
                  },
                }),
              ]}
            >
              <TimePicker
                format="HH:mm"
                style={{ width: '100%' }}
                disabledTime={getDisabledTime}
                showNow={false}
                needConfirm={false}
                // 移除禁用类名
                // className={isPastSchedule ? 'pointer-events-none opacity-50' : ''}
              />
            </Form.Item>
            <span className="mx-2">至</span>
            <Form.Item
              name="endTime"
              noStyle
              dependencies={['startTime']}
              rules={[
                { required: true, message: '请选择结束时间' },
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    const startTime = getFieldValue('startTime');
                    if (value && startTime) {
                      const startMinutes = startTime.hour() * 60 + startTime.minute();
                      const endMinutes = value.hour() * 60 + value.minute();
                      if (endMinutes < startMinutes) {
                        return Promise.reject('结束时间不能小于开始时间');
                      }
                    }
                    return Promise.resolve();
                  },
                }),
              ]}
            >
              <TimePicker
                format="HH:mm"
                style={{ width: '100%' }}
                disabledTime={getDisabledTime}
                showNow={false}
                needConfirm={false}
                // 移除禁用类名
                // className={isPastSchedule ? 'pointer-events-none opacity-50' : ''}
              />
            </Form.Item>
          </div>
          {/* 移除过去时间的警告提示 */}
          {/* {isPastSchedule && (
            <div className="mt-1">
              <span className="text-xs text-red-500">不可 创建/修改 过去时间，请重新选择时间</span>
            </div>
          )} */}
        </Form.Item>

        <Form.Item
          label="备注"
          name="description"
          style={{ marginBottom: 10 }}
          rules={[{ max: 20, message: '不能超过20个字' }]}
        >
          <Input placeholder="请输入描述" />
        </Form.Item>

        <Space className="flex w-full justify-end">
          <Button
            onClick={(e) => {
              e.stopPropagation();
              handleCancelSchedule();
            }}
          >
            取消
          </Button>
          <Button
            type="primary"
            htmlType="submit"
            onClick={(e) => {
              e.stopPropagation();
            }}
          >
            保存
          </Button>
        </Space>
      </Form>
    </div>
  );
}
