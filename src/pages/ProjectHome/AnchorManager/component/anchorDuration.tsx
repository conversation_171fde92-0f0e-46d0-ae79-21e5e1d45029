import { GetAnchorLiveDuration } from '@/services/anchor';
import { PlatForm } from '@/utils/platform';
import { useRequest } from '@umijs/max';
import { Empty, Segmented } from 'antd';
import ReactECharts from 'echarts-for-react';
import { useState } from 'react';
import dayjs from 'dayjs';

type AnchorDurationProps = {
  anchorIds: string[];
  accountIds: string[];
  projectId?: string;
  rangeTime: (string | undefined)[];
  platform: PlatForm;
};

export default function AnchorDuration(props: AnchorDurationProps) {
  const { anchorIds, accountIds, projectId, rangeTime, platform } = props;
  const [viewMode, setViewMode] = useState<'total' | 'daily'>('total');

  // 获取主播直播时长数据
  const { data, loading } = useRequest(
    () => {
      const [startDate, endDate] = rangeTime;
      if (!startDate || !endDate) return Promise.reject();
      return GetAnchorLiveDuration({
        anchorIds,
        accountIds,
        projectId,
        platform,
        startDate,
        endDate,
      });
    },
    {
      refreshDeps: [anchorIds, accountIds, projectId, platform, rangeTime],
    },
  );

  // 计算日期范围内的总天数
  const calculateDays = () => {
    const [startDate, endDate] = rangeTime;
    if (!startDate || !endDate) return 1;

    const start = dayjs(startDate);
    const end = dayjs(endDate);
    const diffDays = end.diff(start, 'day') + 1; // +1 包含当天

    return diffDays || 1; // 至少返回1，避免除以0
  };

  // 处理数据并配置图表选项
  const getOption = () => {
    if (!data || data.length === 0) return {};

    const totalDays = calculateDays();

    // 对数据进行排序 - 按照时长从大到小
    const sortedData = [...data].sort((a, b) => {
      if (viewMode === 'total') {
        return (b.liveDuration || 0) - (a.liveDuration || 0);
      } else {
        // 日均时长排序
        return (b.liveDuration || 0) / totalDays - (a.liveDuration || 0) / totalDays;
      }
    });

    // 准备图表数据
    const anchorNames = sortedData.map((item) => item.anchorName || '未知');
    const durations = sortedData.map((item) => {
      if (viewMode === 'total') {
        // 将秒转换为小时，保留1位小数
        const hours = ((item.liveDuration || 0) / 3600).toFixed(1);
        return parseFloat(hours);
      } else {
        // 计算日均时长
        const avgHours = ((item.liveDuration || 0) / 3600 / totalDays).toFixed(1);
        return parseFloat(avgHours);
      }
    });

    // 找出最大时长，用于设置X轴的最大值
    const maxHours = Math.ceil(Math.max(...durations)) || 1;

    return {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        formatter: (params: any) => {
          const data = params[0];
          return `${data.name}: ${data.value}小时`;
        },
      },
      grid: {
        left: '3%',
        right: '10%',
        bottom: '3%',
        top: '15%',
        containLabel: true,
      },
      xAxis: {
        type: 'value',
        nameLocation: 'end',
        min: 0,
        max: maxHours,
        interval: maxHours > 5 ? Math.ceil(maxHours / 5) : 1,
        axisLabel: {
          formatter: '{value}小时',
        },
      },
      yAxis: {
        type: 'category',
        data: anchorNames,
        inverse: true, // 倒序显示，时长最长的在上面
      },
      series: [
        {
          name: viewMode === 'total' ? '累计时长' : '日均时长',
          type: 'bar',
          data: durations,
          barWidth: 24,
          itemStyle: {
            color: '#7D95FF',
          },
          label: {
            show: true,
            position: 'right',
            formatter: '{c}H',
          },
        },
      ],
    };
  };

  const renderContent = () => {
    // 确保有数据
    if (!data || data.length === 0) {
      return (
        <div className="flex h-[240px] items-center justify-center">
          <Empty description="暂无数据" />
        </div>
      );
    }

    return (
      <ReactECharts
        option={getOption()}
        style={{ height: '280px', width: '100%' }}
        opts={{ renderer: 'svg' }}
        loadingOption={{
          show: loading,
        }}
      />
    );
  };

  return (
    <div className="min-h-[420px] w-[49%] rounded-lg bg-white p-[20px]">
      <div className="flex items-center justify-between">
        <div className="text-base font-medium">主播上播时长</div>
        <Segmented
          options={[
            {
              label: '累计时长',
              value: 'total',
            },
            {
              label: '日均时长',
              value: 'daily',
            },
          ]}
          value={viewMode}
          onChange={(value) => setViewMode(value as 'total' | 'daily')}
        />
      </div>
      {renderContent()}
    </div>
  );
}
