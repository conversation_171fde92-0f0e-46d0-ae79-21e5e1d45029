import { cn } from '@/lib/utils';
import React, { useEffect, useRef, useState } from 'react';
import { minScheduleDuration, timeSlots, totalMinutes, weekDays } from './const';
import { Trash2 } from 'lucide-react';
import { DatePicker, Switch, Tooltip } from 'antd';
import { useLocalStorageState } from 'ahooks';
import dayjs from 'dayjs';
import duration from 'dayjs/plugin/duration';
import { BatchTimeRangeType } from '@/services/anchor';

dayjs.extend(duration);

const { RangePicker } = DatePicker;

type SelectionType = {
  startX: number;
  width: number;
};

// 添加拖拽状态类型
type DragStateType = {
  isDragging: boolean;
  dragType: 'left' | 'right' | null;
  dragStartX: number;
  originalSelection: SelectionType | null;
  originalIndex: number;
};

type ScheduleBatchSetProps = {
  showRangeTime?: string[];
  setShowRangeTime?: React.Dispatch<React.SetStateAction<string[]>>;
  onTimeRangesChange?: (timeRanges: BatchTimeRangeType[]) => void;
  batchDraftData?: BatchTimeRangeType[];
};

// 批量设置排班的 Modal 窗内的内容
export default function ScheduleBatchSet({
  showRangeTime,
  setShowRangeTime,
  onTimeRangesChange,
  batchDraftData,
}: ScheduleBatchSetProps) {
  const fixedWeekDays = weekDays.slice(1).concat(weekDays[0]); // 调整为周一到周日的顺序
  const timelineRef = useRef<HTMLDivElement>(null);
  const [selectionArea, setSelectionArea] = useState<SelectionType | null>(null);
  const [confirmedSelections, setConfirmedSelections] = useState<SelectionType[]>([]);
  const [isSelecting, setIsSelecting] = useState(false);
  const [timeRanges, setTimeRanges] = useState<BatchTimeRangeType[]>(batchDraftData || []);
  // 添加吸附功能状态
  const [snapToTenMinutes, setSnapToTenMinutes] = useLocalStorageState<boolean>('schedule-snap', {
    defaultValue: false,
    listenStorageChange: true,
  });
  // 本地日期范围状态，当没有传入 showRangeTime 时使用
  const [localRangeTime, setLocalRangeTime] = useState<string[]>([
    dayjs().format('YYYY-MM-DD'),
    dayjs().add(13, 'd').format('YYYY-MM-DD'),
  ]);

  // 使用传入的 showRangeTime 或本地状态
  const currentRangeTime = showRangeTime || localRangeTime;
  const handleSetRangeTime = setShowRangeTime || setLocalRangeTime;

  // 当timeRanges变化时，通知父组件
  useEffect(() => {
    if (onTimeRangesChange) {
      onTimeRangesChange(timeRanges);
    }
  }, [timeRanges]);

  // 添加拖拽状态引用
  const dragStateRef = useRef<DragStateType>({
    isDragging: false,
    dragType: null,
    dragStartX: 0,
    originalSelection: null,
    originalIndex: -1,
  });

  // 处理 batchDraftData 数据回显
  useEffect(() => {
    if (batchDraftData && batchDraftData.length > 0) {
      // 等待 DOM 渲染完成后再进行计算
      const timer = setTimeout(() => {
        if (!timelineRef.current) return;

        const newSelections: SelectionType[] = [];

        batchDraftData.forEach((timeRange) => {
          const startX = timeToPosition(timeRange.startTime);
          const endX = timeToPosition(timeRange.endTime);

          newSelections.push({
            startX,
            width: endX - startX,
          });
        });

        setConfirmedSelections(newSelections);
        setTimeRanges(batchDraftData);
      }, 300); // 给一点延迟确保 DOM 已渲染

      return () => clearTimeout(timer);
    }
  }, [batchDraftData]);

  // 日期选择器禁用日期函数
  const disabledTwoWeekDate = (current: any, { from }: any) => {
    if (from) {
      return Math.abs(current.diff(from, 'days')) >= 14;
    }
    return false;
  };

  // 日期范围变化处理函数
  const onChangeRange = (dates: any) => {
    handleSetRangeTime([dates[0].format('YYYY-MM-DD'), dates[1].format('YYYY-MM-DD')]);
  };

  const convertPositionToTime = (position: number) => {
    const timelineWidth = timelineRef.current?.clientWidth || 0;
    const minutes = Math.floor((position / timelineWidth) * totalMinutes);
    const hours = Math.floor(minutes / 60);
    const mins = Math.floor(minutes % 60);
    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
  };

  // 添加吸附到最近整半小时的函数
  const snapToNearestHalfHour = (time: string) => {
    const [h, m] = time.split(':').map(Number);
    const timeScale = 30;
    let snapped = Math.round(m / timeScale) * timeScale;
    let hour = h;
    if (snapped === 60) {
      snapped = 0;
      hour = hour + 1;
      // 防止超出 23:59
      if (hour === 24) {
        hour = 23;
        snapped = 59;
      }
    }
    return `${hour.toString().padStart(2, '0')}:${snapped.toString().padStart(2, '0')}`;
  };

  // 添加时间转像素的函数
  const timeToPosition = (time: string) => {
    const [h, m] = time.split(':').map(Number);
    const minutes = h * 60 + m;
    const timelineWidth = timelineRef.current?.clientWidth || 0;
    return (minutes / totalMinutes) * timelineWidth;
  };

  // 检查是否与已有选择重叠
  const checkOverlap = (selection: SelectionType, excludeIndex: number = -1) => {
    const currentStart =
      selection.width >= 0 ? selection.startX : selection.startX + selection.width;
    const currentEnd = selection.width >= 0 ? selection.startX + selection.width : selection.startX;

    return confirmedSelections.some((confirmed, index) => {
      // 排除正在拖拽的选择区域
      if (index === excludeIndex) return false;

      // @ts-ignore
      const confirmedStart =
        confirmed.width >= 0 ? confirmed.startX : confirmed.startX + confirmed.width;
      const confirmedEnd =
        confirmed.width >= 0 ? confirmed.startX + confirmed.width : confirmed.startX;

      // 检查是否有重叠
      return !(currentEnd <= confirmedStart || currentStart >= confirmedEnd);
    });
  };

  // 处理鼠标按下事件
  const handleMouseDown = (e: React.MouseEvent) => {
    if (!timelineRef.current) return;

    // 只清空当前的选择区域，保留之前确认的选择
    setSelectionArea(null);

    // 设置新的选择区域
    const rect = timelineRef.current.getBoundingClientRect();
    const startX = e.clientX - rect.left;

    setIsSelecting(true);
    setSelectionArea({
      startX,
      width: 0,
    });
  };

  // 处理鼠标移动事件
  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isSelecting || !timelineRef.current || !selectionArea) return;

    const rect = timelineRef.current.getBoundingClientRect();
    const currentX = e.clientX - rect.left;

    // 防止超出边界
    const boundedX = Math.max(0, Math.min(rect.width, currentX));
    const width = boundedX - selectionArea.startX;

    const timelineWidth = rect.width;
    const minWidth = (minScheduleDuration / totalMinutes) * timelineWidth;

    // 确保宽度至少为最小宽度，但允许负值表示向左选择
    let adjustedWidth = width;
    if (Math.abs(width) < minWidth) {
      adjustedWidth = width >= 0 ? minWidth : -minWidth;
    }

    // 确保选择区域不会超出时间线边界
    if (width < 0 && Math.abs(width) > selectionArea.startX) {
      adjustedWidth = -selectionArea.startX;
    }

    const newSelection = {
      ...selectionArea,
      width: adjustedWidth,
    };

    // 检查是否与已有选择重叠
    const overlap = checkOverlap(newSelection);
    if (overlap) {
      return;
    }

    setSelectionArea(newSelection);
  };

  // 处理鼠标抬起事件
  const handleMouseUp = () => {
    if (!selectionArea || !timelineRef.current) return;

    const width = selectionArea.width;
    // 当宽度为负数时，起始位置需要调整
    const startX = width >= 0 ? selectionArea.startX : Math.max(0, selectionArea.startX + width);
    const absWidth = Math.abs(width);

    // 计算时间线宽度与分钟的比例
    const timelineWidth = timelineRef.current.clientWidth;
    const pixelsPerMinute = timelineWidth / totalMinutes;

    // 确保选择区域至少为最小排班时长
    const finalWidth = Math.max(absWidth, minScheduleDuration * pixelsPerMinute);

    // 计算时间
    let startTime = convertPositionToTime(startX);
    let endTime = convertPositionToTime(startX + finalWidth);
    let snappedStartX = startX;
    let snappedEndX = startX + finalWidth;

    // 应用吸附逻辑
    if (snapToTenMinutes) {
      startTime = snapToNearestHalfHour(startTime);
      endTime = snapToNearestHalfHour(endTime);
      // 重新计算像素
      snappedStartX = timeToPosition(startTime);
      snappedEndX = timeToPosition(endTime);
    }

    // snappedEndX 不能超过 23:59
    const maxX = timeToPosition('23:59');
    if (snappedEndX > maxX) {
      snappedEndX = maxX;
    }
    // 这里重新计算 endTime，保证不会超出 23:59
    endTime = convertPositionToTime(snappedEndX);

    // 添加新的选择区域到数组中
    const newSelection: SelectionType = {
      startX: snappedStartX,
      width: snappedEndX - snappedStartX,
    };

    // 如果没有重叠，则添加新的选择
    setConfirmedSelections((prev) => [...prev, newSelection]);
    setTimeRanges((prev) => [...prev, { startTime, endTime }]);

    setIsSelecting(false);
    setSelectionArea(null);
  };

  // 添加拖拽开始处理函数
  const handleDragStart = (
    e: React.MouseEvent,
    selection: SelectionType,
    index: number,
    type: 'left' | 'right',
  ) => {
    e.stopPropagation();
    if (!timelineRef.current) return;

    const rect = timelineRef.current.getBoundingClientRect();
    const dragStartX = e.clientX - rect.left;

    // 更新拖拽状态引用
    dragStateRef.current = {
      isDragging: true,
      dragType: type,
      dragStartX,
      originalSelection: { ...selection },
      originalIndex: index,
    };

    // 添加全局鼠标事件监听
    document.addEventListener('mousemove', handleDragMove);
    document.addEventListener('mouseup', handleDragEnd);
  };

  // 添加拖拽移动处理函数
  const handleDragMove = (e: MouseEvent) => {
    const { isDragging, dragType, originalSelection, originalIndex, dragStartX } =
      dragStateRef.current;
    if (!isDragging || !timelineRef.current || !originalSelection || !dragType) return;

    const rect = timelineRef.current.getBoundingClientRect();
    const currentX = e.clientX - rect.left;
    const deltaX = currentX - dragStartX;

    let newStartX = originalSelection.startX;
    let newWidth = originalSelection.width;

    // 计算时间轴的边界
    const minX = 0; // 对应 00:00
    // 计算 23:59 对应的像素位置
    const maxTime = '23:59';
    const maxX = timeToPosition(maxTime); // 不能到 24:00

    // 计算最小宽度（对应30分钟）
    const minWidth = (minScheduleDuration / totalMinutes) * rect.width;

    if (dragType === 'left') {
      // 左侧拖拽，调整起始位置和宽度
      const maxDeltaX = originalSelection.width - minWidth;
      const limitedDeltaX = Math.min(maxDeltaX, deltaX);
      const potentialNewStartX = originalSelection.startX + limitedDeltaX;
      if (potentialNewStartX < minX) {
        newStartX = minX;
        newWidth = originalSelection.width + originalSelection.startX - minX;
      } else {
        newStartX = potentialNewStartX;
        newWidth = originalSelection.width - limitedDeltaX;
      }
      // 右侧不能超过 23:59
      if (newStartX + newWidth > maxX) {
        newWidth = maxX - newStartX;
      }
    } else {
      // 右侧拖拽，只调整宽度
      const potentialEndX = originalSelection.startX + originalSelection.width + deltaX;
      // 不能超过 23:59
      if (potentialEndX > maxX) {
        newWidth = maxX - originalSelection.startX;
      } else {
        newWidth = Math.max(minWidth, originalSelection.width + deltaX);
      }
    }

    // 创建新的选择对象
    const newSelection = {
      startX: newStartX,
      width: newWidth,
    };

    // 检查是否与其他选择重叠
    const overlap = checkOverlap(newSelection, originalIndex);
    if (overlap) return;

    // 应用吸附逻辑
    let finalStartX = newStartX;
    let finalWidth = newWidth;

    if (snapToTenMinutes) {
      let startTime = convertPositionToTime(newStartX);
      let endTime = convertPositionToTime(newStartX + newWidth);

      if (dragType === 'left') {
        startTime = snapToNearestHalfHour(startTime);
        // 吸附后不能超过 23:59
        if (startTime > maxTime) startTime = maxTime;
        finalStartX = timeToPosition(startTime);
        finalWidth = newStartX + newWidth - finalStartX;
        // 右侧不能超过 23:59
        if (finalStartX + finalWidth > maxX) {
          finalWidth = maxX - finalStartX;
        }
      } else {
        endTime = snapToNearestHalfHour(endTime);
        // 吸附后不能超过 23:59
        if (endTime > maxTime) endTime = maxTime;
        const snappedEndX = timeToPosition(endTime);
        finalWidth = snappedEndX - newStartX;
        // 右侧不能超过 23:59
        if (newStartX + finalWidth > maxX) {
          finalWidth = maxX - newStartX;
        }
      }

      // 确保最小宽度
      if (finalWidth < minWidth) {
        if (dragType === 'left') {
          finalStartX = newStartX + newWidth - minWidth;
          finalWidth = minWidth;
        } else {
          finalWidth = minWidth;
        }
      }

      // 重新检查边界
      if (finalStartX < minX) finalStartX = minX;
      if (finalStartX + finalWidth > maxX) finalWidth = maxX - finalStartX;

      // 再次检查重叠
      const snappedSelection = {
        startX: finalStartX,
        width: finalWidth,
      };
      const snappedOverlap = checkOverlap(snappedSelection, originalIndex);
      if (snappedOverlap) return;
    } else {
      finalStartX = newStartX;
      finalWidth = newWidth;
    }

    setConfirmedSelections((prev) => {
      const updated = [...prev];
      updated[originalIndex] = {
        startX: finalStartX,
        width: finalWidth,
      };
      return updated;
    });

    setTimeRanges((prev) => {
      const updated = [...prev];
      updated[originalIndex] = {
        startTime: convertPositionToTime(finalStartX),
        endTime: convertPositionToTime(finalStartX + finalWidth),
      };
      return updated;
    });
  };

  // 添加拖拽结束处理函数
  const handleDragEnd = () => {
    // 移除全局鼠标事件监听
    document.removeEventListener('mousemove', handleDragMove);
    document.removeEventListener('mouseup', handleDragEnd);

    // 重置拖拽状态
    dragStateRef.current = {
      isDragging: false,
      dragType: null,
      dragStartX: 0,
      originalSelection: null,
      originalIndex: -1,
    };
  };

  const handleDeleteSchedule = (selection: SelectionType) => {
    setConfirmedSelections((prev) => prev.filter((s) => s !== selection));
    const startTime = convertPositionToTime(selection.startX);
    setTimeRanges((prev) => prev.filter((t) => t.startTime !== startTime));
  };

  return (
    <div className="batch-schedule-container select-none">
      <div className="mb-4">
        <p className="text-gray-500">
          说明：上播时间段为时间排班计划模板，设置好后，在编辑排班计划时只需要点击对应时段的成员即可。每次编辑上播时段只会影响未来还没有设置排班的日期，已经设置的排班计划不受影响，可放心设置。
        </p>
      </div>

      <div className="mb-4 flex items-center justify-between">
        <div className="flex items-center">
          <Tooltip title="划选时间时自动吸附到整点时间,吸附到 00 或 30 分">
            <div className="flex items-center">
              <span className="mr-1">时间吸附</span>
              <Switch size="small" checked={snapToTenMinutes} onChange={setSnapToTenMinutes} />
            </div>
          </Tooltip>
        </div>

        <RangePicker
          onChange={onChangeRange}
          disabledDate={disabledTwoWeekDate}
          allowClear={false}
          defaultValue={[dayjs(currentRangeTime[0]), dayjs(currentRangeTime[1])]}
          value={[dayjs(currentRangeTime[0]), dayjs(currentRangeTime[1])]}
          presets={[
            { label: '过去 7 天', value: [dayjs().add(-7, 'd'), dayjs()] },
            { label: '过去 14 天', value: [dayjs().add(-14, 'd'), dayjs()] },
            { label: '未来 7 天', value: [dayjs(), dayjs().add(7, 'd')] },
            { label: '未来 14 天', value: [dayjs(), dayjs().add(13, 'd')] },
          ]}
        />
      </div>

      <div className="relative overflow-x-auto pt-4">
        {/* 时间轴标尺 */}
        <div className="relative flex min-w-[1600px] select-none border-b">
          <div className="sticky left-0 z-[100] w-40 min-w-40 border-r bg-white" />
          <div className="flex min-w-[1440px] flex-1 pb-1">
            {timeSlots.map((time) => (
              <div key={time} className="flex-1 translate-x-1/2 py-1 text-center text-xs">
                {time}
              </div>
            ))}
          </div>
          {/* 添加选择区域时间显示 */}
          {selectionArea && (
            <div className="absolute bottom-[16px] left-40 flex items-center">
              <div
                className="absolute whitespace-nowrap bg-white px-6 text-xs text-blue-500"
                style={{
                  left: `${selectionArea.width >= 0 ? selectionArea.startX : selectionArea.startX + selectionArea.width}px`,
                  transform: 'translateX(-50%)',
                }}
              >
                {convertPositionToTime(
                  selectionArea.width >= 0
                    ? selectionArea.startX
                    : selectionArea.startX + selectionArea.width,
                )}
              </div>
              <div
                className="absolute whitespace-nowrap bg-white px-6 text-xs text-blue-500"
                style={{
                  left: `${selectionArea.width >= 0 ? selectionArea.startX + selectionArea.width : selectionArea.startX}px`,
                  transform: 'translateX(-50%)',
                }}
              >
                {convertPositionToTime(
                  selectionArea.width >= 0
                    ? selectionArea.startX + selectionArea.width
                    : selectionArea.startX,
                )}
              </div>
            </div>
          )}
        </div>

        {/* 内容区 */}
        <div className="flex flex-col">
          <div className="flex min-w-[1600px]">
            <div className="sticky left-0 z-[100] w-40 min-w-40 bg-white">
              <div className="h-10 border-b border-r px-4" />
            </div>
            <div
              ref={timelineRef}
              className="relative h-10 min-w-[1440px] flex-1 border-b"
              onMouseDown={handleMouseDown}
              onMouseMove={handleMouseMove}
              onMouseUp={handleMouseUp}
              onMouseLeave={handleMouseUp}
            >
              <div className="flex w-full">
                {Array.from({ length: timeSlots.length }).map((_, index) => (
                  <div
                    key={index}
                    className={cn('h-10 flex-1 border-r', {
                      'border-r-0': index === timeSlots.length - 1,
                    })}
                  />
                ))}
              </div>

              {/* 已确认的选择区域 */}
              {confirmedSelections.map((selection, index) => (
                <div
                  key={index}
                  className="absolute z-50 h-10 select-none border border-[#3369EE] bg-[#E5EBFE]"
                  style={{
                    left: `${selection.startX}px`,
                    width: `${selection.width}px`,
                    top: '0',
                  }}
                  onClick={(e) => e.stopPropagation()}
                  onMouseDown={(e) => e.stopPropagation()}
                >
                  <div className="relative h-full">
                    {/* 左侧拖拽区域 */}
                    <div
                      className="resize-handle absolute -left-[10px] bottom-0 top-0 z-[60] w-4 cursor-ew-resize"
                      onMouseDown={(e) => handleDragStart(e, selection, index, 'left')}
                      onClick={(e) => e.stopPropagation()}
                    />
                    {/* 右侧拖拽区域 */}
                    <div
                      className="resize-handle absolute -right-[10px] bottom-0 top-0 z-[60] w-4 cursor-ew-resize"
                      onMouseDown={(e) => handleDragStart(e, selection, index, 'right')}
                      onClick={(e) => e.stopPropagation()}
                    />

                    <Tooltip
                      title={
                        <div>
                          {timeRanges[index].startTime} - {timeRanges[index].endTime}
                        </div>
                      }
                      mouseEnterDelay={0.5}
                    >
                      {timeRanges[index] && (
                        <div className="flex h-full items-center justify-center overflow-hidden text-nowrap text-xs text-blue-600">
                          {timeRanges[index].startTime} - {timeRanges[index].endTime}
                        </div>
                      )}
                    </Tooltip>
                    <div className="absolute right-0 top-0 rounded-bl-md bg-[#a9b5db] pb-[2px] pl-[2px] opacity-0 hover:text-blue-800 hover:opacity-100">
                      <Trash2
                        size={15}
                        className="cursor-pointer"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteSchedule(selection);
                        }}
                        onMouseDown={(e) => {
                          e.stopPropagation();
                        }}
                      />
                    </div>
                  </div>
                </div>
              ))}

              {/* 当前正在划选的区域 */}
              {isSelecting && selectionArea && (
                <div
                  className="absolute z-50 h-10 select-none border border-[#3369EE] bg-[#E5EBFE] px-1"
                  style={{
                    left: `${selectionArea.width >= 0 ? selectionArea.startX : Math.max(0, selectionArea.startX + selectionArea.width)}px`,
                    width: `${Math.max(1, Math.abs(selectionArea.width))}px`,
                    top: '0',
                  }}
                />
              )}

              {/* 划选区域左侧虚线 */}
              {selectionArea && (
                <div
                  className="absolute bottom-0 top-0 z-[90] border-l border-dashed border-[#3369EE]"
                  style={{
                    left: `${selectionArea.width >= 0 ? selectionArea.startX : Math.max(0, selectionArea.startX + selectionArea.width)}px`,
                  }}
                />
              )}
              {/* 划选区域右侧虚线 */}
              {selectionArea && (
                <div
                  className="absolute bottom-0 top-0 z-[90] border-l border-dashed border-[#3369EE]"
                  style={{
                    left: `${selectionArea.width >= 0 ? Math.min(selectionArea.startX + selectionArea.width, timelineRef.current?.clientWidth || 0) - 1 : selectionArea.startX}px`,
                  }}
                />
              )}
            </div>
          </div>

          <div className="relative select-none">
            {/* 每天的行 */}
            {fixedWeekDays.map((day) => (
              <div key={day} className="flex min-w-[1600px] border-b">
                <div className="sticky left-0 z-[100] w-40 min-w-40 cursor-pointer border-r bg-white">
                  <div className="flex h-[55px] items-center px-4">{day}</div>
                </div>
                <div className="relative h-[55px] min-w-[1440px] flex-1">
                  <div className="flex w-full">
                    {Array.from({ length: timeSlots.length }).map((_, i) => (
                      <div
                        key={i}
                        className={cn('h-[55px] flex-1 cursor-not-allowed border-r bg-gray-100', {
                          'border-r-0': i === timeSlots.length - 1,
                        })}
                      />
                    ))}
                  </div>
                </div>
              </div>
            ))}

            {/* 已确认的选择区域 */}
            {confirmedSelections.map((block, index) => (
              <div
                key={`confirmed-${index}`}
                className="absolute z-40 border border-[#3369EE] bg-[#E5EBFE] opacity-60"
                style={{
                  left: `${block.startX + 160}px`,
                  width: `${block.width}px`,
                  top: '0',
                  height: `${fixedWeekDays.length * 56}px`,
                }}
              />
            ))}

            {/* 当前正在划选的区域 */}
            {isSelecting && selectionArea && (
              <div
                className="absolute z-40 border border-[#3369EE] bg-[#E5EBFE] opacity-60"
                style={{
                  left: `${(selectionArea.width >= 0 ? selectionArea.startX : Math.max(0, selectionArea.startX + selectionArea.width)) + 160}px`,
                  width: `${Math.abs(selectionArea.width)}px`,
                  top: '0',
                  height: `${fixedWeekDays.length * 56}px`,
                }}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
