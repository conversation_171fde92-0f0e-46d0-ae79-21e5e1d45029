import ExportButton from '@/components/exportButton';
import { usePollingExport } from '@/hooks/usePollingExport';
import { AnchorDetailReport, ExportAnchorDetail, GetAnchorDetailPage } from '@/services/anchor';
import { proTableRequestAdapter } from '@/utils';
import { formatNum } from '@/utils/common';
import { PlatForm } from '@/utils/platform';
import { proTableOptionsConfig, proTablePaginationConfig } from '@/utils/proTableConfig';
import { formatSecond } from '@/utils/time';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import { Link, useParams } from '@umijs/max';
import { Button, message, Tooltip } from 'antd';
import copy from 'copy-to-clipboard';
import dayjs from 'dayjs';
import { Copy } from 'lucide-react';

type AnchorDetailTableProps = {
  anchorId?: number;
  projectId?: string;
  platform: PlatForm;
  rangeTime: (string | undefined)[];
};

export default function AnchorDetailTable(props: AnchorDetailTableProps) {
  const { anchorId, projectId, platform, rangeTime } = props;
  const { industryType } = useParams();
  const { pollingExport, percent, pollingLoading } = usePollingExport(
    `主播明细数据_${dayjs().format('YYYYMMDD')}`,
  );

  const handleExport = async () => {
    if (!anchorId || !rangeTime?.[0] || !rangeTime?.[1]) {
      message.error('必传参数缺失');
      return;
    }
    const res = await ExportAnchorDetail({
      anchorId,
      startDate: rangeTime?.[0],
      endDate: rangeTime?.[1],
      projectId,
      platform,
    });

    if (res?.data) {
      pollingExport({ taskId: res.data });
    } else {
      message.error('导出失败');
    }
  };

  const columns: ProColumns<AnchorDetailReport>[] = [
    {
      title: '直播间',
      dataIndex: 'roomId',
      key: 'roomId',
      width: 120,
      fixed: 'left',
      render: (_, record) => (
        <div className="flex items-center gap-1">
          <Link to={`/live/detail/${record.roomId}/1/${industryType}`} target="_blank">
            <Button type="link" className="px-0">
              {record.roomTitle}
            </Button>
          </Link>

          <Tooltip title="复制直播间ID">
            <Copy
              size={13}
              className="hover:cursor-pointer"
              onClick={() => {
                if (record.roomId) {
                  copy(record.roomId);
                  message.success('直播间ID已复制到剪切板');
                }
              }}
            />
          </Tooltip>
        </div>
      ),
    },
    {
      title: '关联抖音号',
      dataIndex: 'showAccountId',
      key: 'showAccountId',
      width: 120,
      align: 'right',
    },
    {
      title: '总时长',
      dataIndex: 'liveDuration',
      key: 'liveDuration',
      width: 120,
      align: 'right',
      render: (_, record) => formatSecond(record.liveDuration),
      sorter: true,
    },
    {
      title: '曝光人数',
      dataIndex: 'exposureUcount',
      key: 'exposureUcount',
      width: 120,
      align: 'right',
      render: (_, record) => formatNum(record.exposureUcount),
      sorter: true,
    },
    {
      title: '曝光次数',
      dataIndex: 'exposureCount',
      key: 'exposureCount',
      width: 120,
      align: 'right',
      render: (_, record) => formatNum(record.exposureCount),
      sorter: true,
    },
    {
      title: '观看人数',
      dataIndex: 'viewCount',
      key: 'viewCount',
      width: 120,
      align: 'right',
      render: (_, record) => formatNum(record.viewCount),
      sorter: true,
    },
    {
      title: '观看次数',
      dataIndex: 'viewTime',
      key: 'viewTime',
      width: 120,
      align: 'right',
      render: (_, record) => formatNum(record.viewTime),
      sorter: true,
    },
    {
      title: '小风车点击次数',
      dataIndex: 'componentClickCount',
      key: 'componentClickCount',
      width: 120,
      align: 'right',
      render: (_, record) => formatNum(record.componentClickCount),
      sorter: true,
    },
    {
      title: '点赞次数',
      dataIndex: 'diggCount',
      key: 'diggCount',
      width: 120,
      align: 'right',
      render: (_, record) => formatNum(record.diggCount),
      sorter: true,
    },
    {
      title: '评论次数',
      dataIndex: 'commentCount',
      key: 'commentCount',
      width: 120,
      align: 'right',
      render: (_, record) => formatNum(record.commentCount),
      sorter: true,
    },
    {
      title: '分享次数',
      dataIndex: 'shareCount',
      key: 'shareCount',
      width: 120,
      align: 'right',
      render: (_, record) => formatNum(record.shareCount),
      sorter: true,
    },
    {
      title: '关注人数',
      dataIndex: 'followCount',
      key: 'followCount',
      width: 120,
      align: 'right',
      render: (_, record) => formatNum(record.followCount),
      sorter: true,
    },
    {
      title: '留资线索数',
      dataIndex: 'leadsCount',
      key: 'leadsCount',
      width: 120,
      align: 'right',
      render: (_, record) => formatNum(record.leadsCount),
      sorter: true,
    },
  ];

  return (
    <div className="rounded-lg bg-white p-4 pt-0">
      <ProTable<AnchorDetailReport>
        ghost
        columns={columns}
        tableClassName="custom-table"
        scroll={{ x: 'max-content' }}
        params={{
          anchorId,
          projectId,
          platform,
          startDate: rangeTime[0]!,
          endDate: rangeTime[1]!,
        }}
        request={(params, sorter, filter) => {
          return proTableRequestAdapter(params, sorter, filter, GetAnchorDetailPage);
        }}
        search={false}
        options={{ ...proTableOptionsConfig }}
        pagination={{ ...proTablePaginationConfig }}
        headerTitle={<div>主播明细数据</div>}
        toolBarRender={() => [
          <ExportButton
            exportFn={() => handleExport()}
            loading={pollingLoading}
            percent={percent}
            key="export"
          />,
        ]}
        rowKey="roomId"
      />
    </div>
  );
}
