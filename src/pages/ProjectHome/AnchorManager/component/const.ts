export type ScheduleItem = {
  id: string;
  date: string;
  startTime: string;
  endTime: string;
  anchor: string;
  description?: string;
};

export type SelectionArea = {
  startX: number;
  width: number;
  rowIndex: number;
  endX?: number;
} | null;

export const timeSlots = Array.from({ length: 24 }).map((_, index) => {
  return `${(index + 1).toString().padStart(2, '0')}:00`;
});

export const weekDays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
export const totalMinutes = 24 * 60;
export const minScheduleDuration = 30; // 最小排班时长（分钟）
