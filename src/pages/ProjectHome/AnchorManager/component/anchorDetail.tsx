import {
  AnchorOverviewDetailData,
  GetAnchorList,
  GetAnchorOverviewDetail,
} from '@/services/anchor';
import { PlatForm } from '@/utils/platform';
import { formatSecond } from '@/utils/time';
import { useRequest } from '@umijs/max';
import { Avatar, Drawer, Dropdown, Flex, MenuProps, Space } from 'antd';
import { useEffect, useState } from 'react';
import TimeFilter from '@/components/ui/timeFilter';
import { getTimeByType } from '@/utils/time';
import { DownOutlined } from '@ant-design/icons';
import defaultAvatar from '@/assets/default-avatar.png';
import {
  Clock3,
  Eye,
  MessageCircleMore,
  SquareArrowOutUpRight,
  Sun,
  ThumbsUp,
  TvMinimalPlay,
  UserRound,
} from 'lucide-react';
import LeadsIcon from '@/assets/svg/leads.svg';
import ComponentIcon from '@/assets/svg/component.svg';
import ViewCountIcon from '@/assets/svg/viewcount.svg';
import ExposureCountIcon from '@/assets/svg/exposure.svg';
import AnchorTrendChart from './anchorTrendChart';
import AnchorDetailTable from './anchorDetailTable';

type AnchorDetailProps = {
  rangeTime?: (string | undefined)[];
  projectId?: string;
  platform: PlatForm;
  currentAnchorId?: number;
  detailDrawerVisible?: boolean;
  onClose?: () => void;
};

// 主播数据统计组件
const AnchorStatistics = ({ anchorDetail }: { anchorDetail?: AnchorOverviewDetailData }) => {
  const statItems = [
    {
      title: '总直播场次',
      value: anchorDetail?.liveCount,
      bgColor: 'rgba(30, 165, 255, 0.16)',
      icon: <TvMinimalPlay size={16} color="#1EA5FF" />,
    },
    {
      title: '总时长',
      value: formatSecond(anchorDetail?.liveDuration),
      bgColor: 'rgba(90, 30, 255, 0.16)',
      icon: <Clock3 size={16} color="#5A1EFF" />,
    },
    {
      title: '曝光人数',
      value: anchorDetail?.exposureUcount,
      bgColor: 'rgba(15, 169, 100, 0.16)',
      icon: <img src={ExposureCountIcon} className="block h-[16px] w-[16px]" alt="曝光人数" />,
    },
    {
      title: '曝光次数',
      value: anchorDetail?.exposureCount,
      bgColor: 'rgba(241, 138, 35, 0.16)',
      icon: <Sun size={16} color="#F18A23" />,
    },
    {
      title: '观看人数',
      value: anchorDetail?.viewCount,
      bgColor: 'rgba(227, 50, 131, 0.16)',
      icon: <img src={ViewCountIcon} className="block h-[16px] w-[16px]" alt="观看人数" />,
    },
    {
      title: '观看次数',
      value: anchorDetail?.viewTime,
      bgColor: 'rgba(231, 25, 25, 0.16)',
      icon: <Eye size={16} color="#E71919" />,
    },
    {
      title: '小风车点击次数',
      value: anchorDetail?.componentClickCount,
      bgColor: 'rgba(231, 25, 25, 0.16)',
      icon: <img src={ComponentIcon} className="block h-[16px] w-[16px]" alt="小风车点击次数" />,
    },
    {
      title: '点赞次数',
      value: anchorDetail?.diggCount,
      bgColor: 'rgba(227, 50, 131, 0.16)',
      icon: <ThumbsUp size={16} color="#E33283" />,
    },
    {
      title: '评论次数',
      value: anchorDetail?.commentCount,
      bgColor: 'rgba(241, 138, 35, 0.16)',
      icon: <MessageCircleMore size={16} color="#F18A23" />,
    },
    {
      title: '分享次数',
      value: anchorDetail?.shareCount,
      bgColor: 'rgba(15, 169, 100, 0.16)',
      icon: <SquareArrowOutUpRight size={16} color="#0FA964" />,
    },
    {
      title: '关注人数',
      value: anchorDetail?.followCount,
      bgColor: 'rgba(90, 30, 255, 0.16)',
      icon: <UserRound size={16} color="#5A1EFF" />,
    },
    {
      title: '留资线索数',
      value: anchorDetail?.leadsCount,
      bgColor: 'rgba(30, 165, 255, 0.16)',
      icon: <img src={LeadsIcon} className="block h-[16px] w-[16px]" alt="留资线索数" />,
    },
  ];

  // 第一行数据
  const firstRow = statItems.slice(0, 6);
  // 第二行数据
  const secondRow = statItems.slice(6, 12);

  return (
    <div className="flex flex-col rounded-lg bg-white p-4">
      <div className="pb-[18px] text-base font-medium">概览</div>
      <div className="flex flex-col gap-6">
        <div className="grid grid-cols-6 gap-16">
          {firstRow.map((item, index) => (
            <div key={index} className="flex flex-col">
              <div className="mb-2 flex items-center gap-2">
                <div className="rounded p-1" style={{ backgroundColor: item.bgColor }}>
                  {item.icon}
                </div>
                <span className="text-sm text-gray-600">{item.title}</span>
              </div>
              <div className="text-2xl font-medium">{item.value}</div>
            </div>
          ))}
        </div>
        <div className="grid grid-cols-6 gap-16">
          {secondRow.map((item, index) => (
            <div key={index} className="flex flex-col">
              <div className="mb-2 flex items-center gap-2">
                <div className="rounded p-1" style={{ backgroundColor: item.bgColor }}>
                  {item.icon}
                </div>
                <span className="text-sm text-gray-600">{item.title}</span>
              </div>
              <div className="text-2xl font-medium">{item.value}</div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default function AnchorDetail(props: AnchorDetailProps) {
  const {
    projectId,
    platform,
    currentAnchorId,
    detailDrawerVisible,
    rangeTime: parentRangeTime,
    onClose,
  } = props;
  const [rangeTime, setRangeTime] = useState<(string | undefined)[]>(
    parentRangeTime || getTimeByType('today'),
  );
  const [account, setAccount] = useState<number | undefined>(undefined);

  useEffect(() => {
    if (currentAnchorId) {
      setAccount(currentAnchorId);
    }
  }, [currentAnchorId]);

  useEffect(() => {
    if (parentRangeTime) {
      setRangeTime(parentRangeTime);
    }
  }, [parentRangeTime]);

  // 主播列表
  const { data: anchorListData } = useRequest(() => GetAnchorList({ projectId, platform }), {
    cacheKey: `GetAnchorList_${projectId}_${platform}`,
    cacheTime: 10 * 60 * 1000, // 10 分钟
    refreshDeps: [projectId, platform],
  });

  // 获取主播详情数据
  const { data: anchorDetail } = useRequest(
    () =>
      account && rangeTime?.[0] && rangeTime?.[1]
        ? GetAnchorOverviewDetail({
            anchorId: account,
            startDate: rangeTime?.[0],
            endDate: rangeTime?.[1],
            projectId,
            platform,
          })
        : Promise.resolve({ success: false, data: null }),
    {
      refreshDeps: [account, rangeTime, projectId, platform],
      ready: !!account && !!rangeTime?.[0] && !!rangeTime?.[1] && detailDrawerVisible,
    },
  );

  const onMenuClick: MenuProps['onClick'] = (info) => {
    const anchor = anchorListData?.find((item) => String(item.id) === info.key);
    if (anchor) {
      setAccount(Number(anchor.id));
    }
  };

  const items: MenuProps['items'] = anchorListData?.map((item) => ({
    key: String(item.id),
    label: (
      <div className="flex items-center">
        <Avatar src={item.avatarUrl || <img src={defaultAvatar} alt="avatar" />} size={28} />
        <div className="ml-2">
          <div className="text-sm font-bold">{item.name}</div>
        </div>
      </div>
    ),
  }));

  return (
    <Drawer
      title="主播详情"
      width="85%"
      open={detailDrawerVisible}
      destroyOnHidden
      onClose={onClose}
      styles={{
        body: {
          backgroundColor: '#F5F5F5',
        },
      }}
    >
      <div className="flex flex-col gap-4">
        <div className="rounded-lg bg-white p-4">
          <Flex justify="space-between" align="center">
            <Dropdown
              trigger={['click']}
              placement="bottomLeft"
              menu={{ items, onClick: onMenuClick, className: 'max-h-80 overflow-y-auto' }}
            >
              <Space>
                <div className="flex cursor-pointer select-none items-center">
                  <Avatar src={anchorDetail?.anchorAvatarUrl} size={36} />
                  <div className="ml-2">
                    <div className="flex gap-2">
                      <div className="text-sm font-bold">{anchorDetail?.anchorName}</div>
                      <DownOutlined />
                    </div>
                    <div className="text-xs text-gray-400">
                      抖音号：
                      {anchorDetail?.showAccountIds?.join('、') || '-'}
                    </div>
                  </div>
                </div>
              </Space>
            </Dropdown>
            <div>
              <TimeFilter value={rangeTime} onChange={(value) => setRangeTime(value)} showToday />
            </div>
          </Flex>
        </div>

        <AnchorStatistics anchorDetail={anchorDetail} />

        <AnchorTrendChart
          anchorId={account}
          projectId={projectId}
          platform={platform}
          rangeTime={rangeTime}
        />

        <AnchorDetailTable
          anchorId={account}
          projectId={projectId}
          platform={platform}
          rangeTime={rangeTime}
        />
      </div>
    </Drawer>
  );
}
