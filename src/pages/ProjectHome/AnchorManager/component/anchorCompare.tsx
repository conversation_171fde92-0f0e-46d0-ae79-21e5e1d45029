import defaultAvatar from '@/assets/default-avatar.png';
import TimeFilter from '@/components/ui/timeFilter';
import Rank1 from '@/icons/outline/rank-1.svg';
import { AnchorReportCompare, GetAnchorCompare, GetAnchorList } from '@/services/anchor';
import { formatNum } from '@/utils/common';
import { PlatForm } from '@/utils/platform';
import { formatSecond, getTimeByType } from '@/utils/time';
import { useRequest } from '@umijs/max';
import { Avatar, Button, message, Modal, Progress, Select, Table, TableColumnsType } from 'antd';
import { FolderDown } from 'lucide-react';
import { useEffect, useState } from 'react';
import * as XLSX from 'xlsx';

type AnchorCompareProps = {
  anchorCompareVisible: boolean;
  setAnchorCompareVisible: (visible: boolean) => void;
  rangeTime?: (string | undefined)[];
  projectId?: string;
  platform: PlatForm;
};

// 计算每个指标的TOP1主播id
const getTop1Ids = (data: AnchorReportCompare[] | undefined, key: keyof AnchorReportCompare) => {
  if (!data || data.length === 0) return [];
  const max = Math.max(...data.map((item) => Number(item[key]) || 0));
  return data.filter((item) => Number(item[key]) === max).map((item) => item.anchorId);
};

export default function AnchorCompare(props: AnchorCompareProps) {
  const {
    anchorCompareVisible,
    setAnchorCompareVisible,
    rangeTime: parentRangeTime,
    projectId,
    platform,
  } = props;
  const [rangeTime, setRangeTime] = useState<(string | undefined)[]>(
    parentRangeTime || getTimeByType('today'),
  );
  // 选中的主播id
  const [anchorIds, setAnchorIds] = useState<string[]>([]);
  const [exportLoading, setExportLoading] = useState(false);
  const [percent, setPercent] = useState(0);

  useEffect(() => {
    if (parentRangeTime) {
      setRangeTime(parentRangeTime);
    }
  }, [parentRangeTime]);

  // 全部主播
  const { data: anchorListData } = useRequest(() => GetAnchorList({ projectId, platform }), {
    cacheKey: `GetAnchorList_${projectId}_${platform}`,
    cacheTime: 10 * 60 * 1000, // 10 分钟
    refreshDeps: [projectId, platform],
    // 默认勾选前五个
    onSuccess: (data) => {
      if (data && data.length > 0) {
        setAnchorIds(data.slice(0, 5).map((item) => item.id?.toString() || ''));
      }
    },
  });

  // 请求主播对比数据
  const { data: compareDataRaw, loading } = useRequest(
    () => {
      if (!anchorIds.length || !rangeTime[0] || !rangeTime[1]) return Promise.reject();
      return GetAnchorCompare({
        anchorIds,
        projectId,
        platform,
        startDate: rangeTime[0]!,
        endDate: rangeTime[1]!,
      });
    },
    {
      refreshDeps: [anchorIds, rangeTime, projectId, platform],
      ready: !!anchorIds.length && !!rangeTime[0] && !!rangeTime[1],
    },
  );

  // 计算平均值
  const calcAvg = (key: keyof AnchorReportCompare) => {
    if (!compareDataRaw || compareDataRaw.length === 0) return null;
    const sum = compareDataRaw.reduce(
      (acc: number, cur: AnchorReportCompare) => acc + (Number(cur[key]) || 0),
      0,
    );
    return sum / compareDataRaw.length;
  };

  const liveDurationTop1Ids = getTop1Ids(compareDataRaw, 'liveDuration');
  const leadsCountTop1Ids = getTop1Ids(compareDataRaw, 'leadsCount');
  const viewCountTop1Ids = getTop1Ids(compareDataRaw, 'viewCount');
  const followCountTop1Ids = getTop1Ids(compareDataRaw, 'followCount');

  // 动态columns，插入Rank1图标
  const anchorCompareColumns: TableColumnsType<AnchorReportCompare> = [
    {
      title: '主播',
      dataIndex: 'anchorName',
      fixed: true,
      width: 120,
      render: (_, record) => {
        return (
          <div className="flex items-center">
            <Avatar size="small" src={record.anchorAvatarUrl || defaultAvatar} />
            <div className="ml-2">
              <div>{record.anchorName}</div>
            </div>
          </div>
        );
      },
    },
    {
      title: '开播时长',
      dataIndex: 'liveDuration',
      align: 'right',
      width: 130,
      render: (_, record) => {
        return (
          <div className="flex items-center justify-end">
            {liveDurationTop1Ids.includes(record.anchorId) && Number(record.liveDuration) > 0 && (
              <img src={Rank1} alt="rank1" className="mr-1 h-4 w-4" />
            )}
            <span>{formatSecond(record.liveDuration)}</span>
          </div>
        );
      },
    },
    {
      title: '留资线索数',
      dataIndex: 'leadsCount',
      align: 'right',
      width: 130,
      render: (_, record) => {
        return (
          <div className="flex items-center justify-end">
            {leadsCountTop1Ids.includes(record.anchorId) && Number(record.leadsCount) > 0 && (
              <img src={Rank1} alt="rank1" className="mr-1 h-4 w-4" />
            )}
            <span>{formatNum(record.leadsCount)}</span>
          </div>
        );
      },
    },
    {
      title: '观看人数',
      dataIndex: 'viewCount',
      align: 'right',
      width: 130,
      render: (_, record) => {
        return (
          <div className="flex items-center justify-end">
            {viewCountTop1Ids.includes(record.anchorId) && Number(record.viewCount) > 0 && (
              <img src={Rank1} alt="rank1" className="mr-1 h-4 w-4" />
            )}
            <span>{formatNum(record.viewCount)}</span>
          </div>
        );
      },
    },
    {
      title: '新增粉丝数',
      dataIndex: 'followCount',
      align: 'right',
      width: 130,
      render: (_, record) => {
        return (
          <div className="flex items-center justify-end">
            {followCountTop1Ids.includes(record.anchorId) && Number(record.followCount) > 0 && (
              <img src={Rank1} alt="rank1" className="mr-1 h-4 w-4" />
            )}
            <span>{formatNum(record.followCount)}</span>
          </div>
        );
      },
    },
  ];

  // 导出 Excel
  const handleExport = async () => {
    if (!compareDataRaw || compareDataRaw.length === 0) {
      message.info('没有可导出数据');
      return;
    }

    setExportLoading(true);
    setPercent(0);

    // 模拟进度（实际导出数据量大时可分步处理，这里简单模拟）
    setPercent(30);

    // 表头
    const header = ['主播', '开播时长', '留资线索数', '观看人数', '新增粉丝数'];
    const dataRows = compareDataRaw.map((item) => [
      item.anchorName,
      formatSecond(item.liveDuration),
      formatNum(item.leadsCount),
      formatNum(item.viewCount),
      formatNum(item.followCount),
    ]);
    setPercent(60);

    const avgRow = [
      '平均值',
      formatSecond(calcAvg('liveDuration')),
      formatNum(calcAvg('leadsCount')),
      formatNum(calcAvg('viewCount')),
      formatNum(calcAvg('followCount')),
    ];

    const exportData = [header, ...dataRows, avgRow];
    setPercent(80);

    const ws = XLSX.utils.aoa_to_sheet(exportData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, '主播对比');

    XLSX.writeFile(wb, '主播对比.xlsx');
    setPercent(100);

    setTimeout(() => {
      setExportLoading(false);
      setPercent(0);
    }, 500);
  };

  return (
    <Modal
      title="主播对比"
      open={anchorCompareVisible}
      width={1200}
      footer={null}
      onCancel={() => setAnchorCompareVisible(false)}
    >
      <div className="mt-6 flex flex-col gap-6">
        {/* 筛选区域 */}
        <div className="flex items-center gap-2">
          <TimeFilter value={rangeTime} onChange={(value) => setRangeTime(value)} showToday />
          <Select
            showSearch
            mode="multiple"
            maxCount={5}
            filterOption={(input, option) =>
              (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
            }
            options={
              anchorListData && anchorListData.length > 0
                ? anchorListData.map((item) => ({ value: item.id, label: item.name }))
                : []
            }
            maxTagCount="responsive"
            placeholder="请选择主播（最多可选5个）"
            style={{ width: 300 }}
            value={anchorIds}
            onChange={(value) => setAnchorIds(value)}
          />
        </div>

        {/* 对比表格 */}
        <Table<AnchorReportCompare>
          title={() => (
            <div className="flex items-center justify-between">
              <div className="text-base font-medium">主播详细数据 </div>
              <Button onClick={handleExport} disabled={exportLoading}>
                <FolderDown size={16} strokeWidth={1.5} />
                <span>导出</span>
                {exportLoading && (
                  <Progress
                    percent={percent}
                    type="circle"
                    size={15}
                    style={{ marginLeft: '5px' }}
                  />
                )}
              </Button>
            </div>
          )}
          columns={anchorCompareColumns}
          dataSource={compareDataRaw}
          loading={loading}
          pagination={false}
          scroll={{ x: 'max-content', y: 500 }}
          className="customer-compare-table"
          summary={() => (
            <Table.Summary fixed>
              <Table.Summary.Row className="text-new-media-blue-900">
                <Table.Summary.Cell index={0}>
                  <div className="ml-1 font-bold">平均值</div>
                </Table.Summary.Cell>
                <Table.Summary.Cell index={1} className="text-right font-bold">
                  {formatSecond(calcAvg('liveDuration'))}
                </Table.Summary.Cell>
                <Table.Summary.Cell index={2} className="text-right font-bold">
                  {formatNum(calcAvg('leadsCount'))}
                </Table.Summary.Cell>
                <Table.Summary.Cell index={3} className="text-right font-bold">
                  {formatNum(calcAvg('viewCount'))}
                </Table.Summary.Cell>
                <Table.Summary.Cell index={4} className="!pr-8 text-right font-bold">
                  {formatNum(calcAvg('followCount'))}
                </Table.Summary.Cell>
              </Table.Summary.Row>
            </Table.Summary>
          )}
        />
      </div>
    </Modal>
  );
}
