import { AnchorTrendType, GetAnchorTrend, TrendDate } from '@/services/anchor';
import { PlatForm } from '@/utils/platform';
import ReactECharts from 'echarts-for-react';
import { useState, useMemo, useEffect, useRef } from 'react';
import { Checkbox } from 'antd';

type AnchorTrendChartProps = {
  anchorId?: number;
  projectId?: string;
  platform: PlatForm;
  rangeTime: (string | undefined)[];
};

const trendTypeOptions = [
  { label: '直播场次', value: 1 },
  { label: '观看人数', value: 2 },
  { label: '观看次数', value: 3 },
  { label: '曝光人数', value: 4 },
  { label: '曝光次数', value: 5 },
  { label: '小风车点击次数', value: 6 },
  { label: '点赞次数', value: 7 },
  { label: '评论次数', value: 8 },
  { label: '分享次数', value: 9 },
  { label: '关注人数', value: 10 },
  { label: '留资线索数', value: 11 },
];

export default function AnchorTrendChart(props: AnchorTrendChartProps) {
  const { anchorId, projectId, platform, rangeTime } = props;
  const [selectedTrendTypes, setSelectedTrendTypes] = useState<AnchorTrendType[]>([1, 2, 3, 4]);
  const [trendData, setTrendData] = useState<Partial<Record<AnchorTrendType, TrendDate[]>>>({});
  const [loading, setLoading] = useState(false);
  const chartRef = useRef<any>(null);

  const handleCheckboxChange = (checkedValues: AnchorTrendType[]) => {
    // 始终保留 1
    const newSelected = [1, ...checkedValues];
    setSelectedTrendTypes(newSelected);
    setTrendData((prev) => {
      const newData = { ...prev };
      Object.keys(newData).forEach((key) => {
        if (!newSelected.includes(Number(key))) {
          delete newData[key as unknown as AnchorTrendType];
        }
      });
      return newData;
    });
  };

  useEffect(() => {
    chartRef.current?.getEchartsInstance().resize();
  }, [chartRef.current]);

  // 获取趋势数据
  useEffect(() => {
    const fetchData = async () => {
      if (!anchorId || !rangeTime || selectedTrendTypes.length === 0) return;

      setLoading(true);
      try {
        const promises = selectedTrendTypes.map((type) =>
          GetAnchorTrend({
            anchorId,
            projectId,
            platform,
            startDate: rangeTime[0]!,
            endDate: rangeTime[1]!,
            type,
          }),
        );
        const results = await Promise.all(promises);
        const newTrendData: Partial<Record<AnchorTrendType, TrendDate[]>> = {};
        selectedTrendTypes.forEach((type, index) => {
          if (results[index].data) {
            newTrendData[type] = results[index].data;
          }
        });
        setTrendData(newTrendData);
      } catch (error) {
        console.error('获取趋势数据失败:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [anchorId, projectId, platform, rangeTime, selectedTrendTypes]);

  const option = useMemo(() => {
    if (
      selectedTrendTypes.length === 0 ||
      selectedTrendTypes.every((type) => !trendData[type] || trendData[type]?.length === 0)
    ) {
      return {
        xAxis: {
          type: 'category',
          data: [],
        },
        yAxis: {
          type: 'value',
        },
        series: [],
      };
    }
    const firstType = selectedTrendTypes[0];
    const firstData = trendData[firstType];
    if (!firstData)
      return {
        xAxis: {
          type: 'category',
          data: [],
        },
        yAxis: {
          type: 'value',
        },
        series: [],
      };

    const xAxisData = firstData.map((item) => item.date || '');
    const series = selectedTrendTypes.map((type) => {
      const data = trendData[type]?.map((item) => Number(item.count)) || [];
      const trendOption = trendTypeOptions.find((option) => option.value === type);

      return {
        name: trendOption?.label,
        type: type === 1 ? 'bar' : 'line',
        data,
        ...(type === 1
          ? {
              barWidth: 24,
              itemStyle: {
                color: '#7D95FF',
              },
              yAxisIndex: 1,
            }
          : {
              smooth: true,
              symbol: 'circle',
              symbolSize: 8,
              yAxisIndex: 0,
            }),
      };
    });

    return {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          crossStyle: {
            color: '#999',
          },
        },
      },
      legend: {
        data: selectedTrendTypes.map((type) => {
          const option = trendTypeOptions.find((item) => item.value === type);
          return option?.label || '';
        }),
      },
      grid: {
        top: '10%',
        left: '0%',
        right: '2%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: xAxisData,
        axisPointer: {
          type: 'shadow',
        },
      },
      yAxis: [
        {
          type: 'value',
          position: 'left',
        },
        {
          type: 'value',
          position: 'right',
        },
      ],
      series,
    };
  }, [trendData, selectedTrendTypes]);

  return (
    <div className="flex flex-col gap-4">
      <div className="rounded-lg bg-white p-6 px-4">
        <div className="mb-10 flex flex-col gap-2">
          <div className="text-base font-medium">趋势数据</div>
          <div className="border-gray-100">
            <Checkbox.Group
              options={trendTypeOptions.slice(1, -1)}
              value={selectedTrendTypes.filter((v) => v !== 1)}
              onChange={handleCheckboxChange}
              style={{ float: 'right' }}
            />
          </div>
        </div>
        {anchorId && (
          <ReactECharts
            ref={chartRef}
            option={option}
            style={{ height: 400, width: '100%' }}
            showLoading={loading}
            notMerge={true}
          />
        )}
      </div>
    </div>
  );
}
