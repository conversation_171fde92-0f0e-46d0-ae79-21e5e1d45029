import { <PERSON>chor<PERSON>, CreateAnchor, UpdateAnchor } from '@/services/anchor';
import { uploadImage } from '@/utils/oss';
import { PlatForm } from '@/utils/platform';
import { EditOutlined } from '@ant-design/icons';
import { Button, Form, Input, message, Upload } from 'antd';
import { useState, useEffect } from 'react';
import defaultAvatar from '@/assets/default-avatar.png';

type AnchorFormProps = {
  type: 'add' | 'edit';
  onSuccess: () => void;
  projectId: string;
  editRecord?: AnchorVO;
};

const AnchorForm = ({ type, onSuccess, projectId, editRecord }: AnchorFormProps) => {
  const [form] = Form.useForm();
  const [avatarUrl, setAvatarUrl] = useState<string>(defaultAvatar); // 本地预览
  const [avatarFile, setAvatarFile] = useState<File | undefined>(); // 选中的文件
  const [submitLoading, setSubmitLoading] = useState(false);

  // 编辑模式下回显头像
  useEffect(() => {
    if (type === 'edit') {
      if (editRecord?.avatarUrl) {
        setAvatarUrl(editRecord.avatarUrl);
      } else {
        setAvatarUrl(defaultAvatar);
      }
      setAvatarFile(undefined);
    }
    if (type === 'add') {
      setAvatarUrl(defaultAvatar);
      setAvatarFile(undefined);
    }
  }, [type, editRecord]);

  // 选择图片后本地预览
  const beforeUpload = (file: File) => {
    setAvatarFile(file);
    setAvatarUrl(URL.createObjectURL(file));
    form.setFieldsValue({ avatarUrl: file.name }); // 只是占位
    return false; // 阻止 antd 自动上传
  };

  // 提交时上传图片到 COS
  const uploadToCos = async (file: File) => {
    const ext = file.name.split('.').pop();
    const key = `anchor/avatar/${Date.now()}.${ext}`;
    await uploadImage({ file, key });
    return `https://media-1307444343.cos.ap-guangzhou.myqcloud.com/${key}`;
  };

  // 将图片 url 转为 blob
  const urlToBlob = async (url: string): Promise<Blob> => {
    const res = await fetch(url);
    return await res.blob();
  };

  const onFinish = async (values: any) => {
    // 如果当前头像是 defaultAvatar，自动上传 defaultAvatar 到 COS
    let finalAvatarUrl = avatarUrl;
    let needUploadDefault = false;
    if (avatarUrl === defaultAvatar) {
      needUploadDefault = true;
    }
    setSubmitLoading(true);
    try {
      // 用户主动上传头像
      if (avatarFile && avatarUrl.startsWith('blob:')) {
        finalAvatarUrl = await uploadToCos(avatarFile);
      } else if (needUploadDefault) {
        const blob = await urlToBlob(defaultAvatar);
        const file = new File([blob], 'default-avatar.png', { type: blob.type });
        finalAvatarUrl = await uploadToCos(file);
        if (!finalAvatarUrl) {
          setSubmitLoading(false);
          return;
        }
      }
      const params = {
        projectId,
        platform: PlatForm.Douyin,
      };
      if (type === 'add') {
        const body = {
          name: values.name,
          avatarUrl: finalAvatarUrl,
        };
        const res = await CreateAnchor(params, body);
        if (res.data) {
          message.success('添加成功');
          onSuccess();
        }
      } else {
        const body = {
          id: editRecord?.id,
          name: values.name,
          avatarUrl: finalAvatarUrl,
        };
        const res = await UpdateAnchor(params, body);
        if (res.data) {
          message.success('编辑成功');
          onSuccess();
        }
      }
    } catch (e) {
      message.error('操作失败');
    } finally {
      setSubmitLoading(false);
    }
  };

  return (
    <Form
      form={form}
      layout="horizontal"
      onFinish={onFinish}
      className="pt-4"
      initialValues={type === 'add' ? { name: '', avatarUrl: '' } : editRecord}
      wrapperCol={{ offset: 1 }}
    >
      <Form.Item label="主播头像" name="avatarPreview" labelCol={{ style: { marginTop: 30 } }}>
        <Upload
          showUploadList={false}
          beforeUpload={beforeUpload}
          accept="image/*"
          listType="picture-circle"
          className="avatar-uploader overflow-hidden"
        >
          <div className="group relative h-[100px] w-[100px]">
            <img
              src={avatarUrl || defaultAvatar}
              alt="头像"
              style={{ width: '100px', height: '100px', borderRadius: '50%', overflow: 'hidden' }}
              className="h-full w-full rounded-full object-cover"
            />
            <div className="absolute inset-0 flex cursor-pointer items-center justify-center rounded-full bg-black/40 opacity-0 transition-opacity group-hover:opacity-100">
              <EditOutlined className="text-white" style={{ fontSize: 25 }} />
            </div>
          </div>
        </Upload>
      </Form.Item>
      <Form.Item
        label="主播姓名"
        name="name"
        rules={[
          { required: true, message: '请输入主播姓名' },
          { max: 10, message: '主播姓名不能超过10个字' },
        ]}
      >
        <Input placeholder="请输入主播姓名" />
      </Form.Item>
      <Form.Item name="avatarUrl" hidden>
        <Input />
      </Form.Item>
      <div className="mt-8 flex justify-end gap-4">
        <Button onClick={() => onSuccess()}>取消</Button>
        <Button type="primary" htmlType="submit" loading={submitLoading}>
          确认
        </Button>
      </div>
    </Form>
  );
};

export default AnchorForm;
