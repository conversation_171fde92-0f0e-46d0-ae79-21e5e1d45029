import defaultAvatar from '@/assets/default-avatar.png';
import ExportButton from '@/components/exportButton';
import MomData from '@/components/momData';
import { usePollingExport } from '@/hooks/usePollingExport';
import {
  AnchorOverviewReport,
  GetAnchorOverviewExport,
  GetAnchorOverviewPage,
} from '@/services/anchor';
import { proTableRequestAdapter } from '@/utils';
import { formatNum, STICKY_OFFSETHEADER } from '@/utils/common';
import { PlatForm } from '@/utils/platform';
import { proTableOptionsConfig, proTablePaginationConfig } from '@/utils/proTableConfig';
import { formatSecond } from '@/utils/time';
import { UserOutlined } from '@ant-design/icons';
import type { ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import { Avatar, Button, message, Space, Switch, Tooltip } from 'antd';
import dayjs from 'dayjs';
import { Kanban } from 'lucide-react';
import { useState } from 'react';
import AnchorAccountModal from './anchorAccountModal';
import AnchorCompare from './anchorCompare';
import AnchorDetail from './anchorDetail';

type AnchorTableProps = {
  anchorIds?: string[];
  accountIds?: string[];
  projectId?: string;
  rangeTime?: (string | undefined)[];
  platform: PlatForm;
};

export default function AnchorTable(props: AnchorTableProps) {
  const { anchorIds, accountIds, projectId, rangeTime, platform } = props;
  const { pollingExport, percent, pollingLoading } = usePollingExport(
    `主播详细数据_${dayjs().format('YYYYMMDD')}`,
  );
  const [detailDrawerVisible, setDetailDrawerVisible] = useState(false);
  const [currentAnchorId, setCurrentAnchorId] = useState<number>();
  const [anchorAccountVisible, setAnchorAccountVisible] = useState(false);
  const [anchor, setAnchor] = useState<AnchorOverviewReport>();
  const [compare, setCompare] = useState(false);
  const [anchorCompareVisible, setAnchorCompareVisible] = useState(false);

  const handleExport = async () => {
    if (!rangeTime?.[0] || !rangeTime?.[1]) {
      message.error('请选择时间范围');
      return;
    }
    const res = await GetAnchorOverviewExport({
      anchorIds,
      accountIds,
      startDate: rangeTime?.[0],
      endDate: rangeTime?.[1],
      projectId,
      platform,
    });

    if (res?.data) {
      pollingExport({ taskId: res.data });
    } else {
      message.error('导出失败');
    }
  };

  const showDetailDrawer = (anchorId: number) => {
    setCurrentAnchorId(anchorId);
    setDetailDrawerVisible(true);
  };

  const showDetailModal = (record: AnchorOverviewReport) => {
    setAnchor(record);
    setAnchorAccountVisible(true);
  };

  // 渲染环比数据
  const renderCompareData = (
    value: number | undefined,
    compareValue: number | undefined,
    compareRate: string | undefined,
  ) => {
    if (!compare) {
      return formatNum(value);
    } else {
      return (
        <Tooltip title={`上周期：${formatNum(compareValue)}`}>
          <div className="flex flex-col justify-end gap-1">
            <div>{formatNum(value)}</div>
            <MomData rate={compareRate} className="ml-auto mr-0" />
          </div>
        </Tooltip>
      );
    }
  };

  // 渲染时长环比数据
  const renderDurationCompareData = (
    value: number | undefined,
    compareValue: number | undefined,
    compareRate: string | undefined,
  ) => {
    if (!compare) {
      return formatSecond(value);
    } else {
      return (
        <Tooltip title={`上周期：${formatSecond(compareValue)}`}>
          <div className="flex flex-col justify-end gap-1">
            <div>{formatSecond(value)}</div>
            <MomData rate={compareRate} className="ml-auto mr-0" />
          </div>
        </Tooltip>
      );
    }
  };

  const columns: ProColumns<AnchorOverviewReport>[] = [
    {
      title: '主播信息',
      dataIndex: 'anchorName',
      key: 'anchorName',
      width: 180,
      fixed: 'left',
      render: (_, record) => (
        <div className="flex items-center">
          <div className="relative">
            <Avatar
              src={record.anchorAvatarUrl || <img src={defaultAvatar} />}
              style={{ border: record.liveStatus === 1 ? '1px solid #F82074' : 'none' }}
            />
            {record.liveStatus === 1 && (
              <div className="absolute -bottom-1 left-1/2 z-[1000] flex h-[12px] w-[20px] -translate-x-1/2 transform items-center justify-center rounded bg-[#F82074]">
                <Kanban className="size-full rotate-180 text-white" />
              </div>
            )}
          </div>
          <div className="ml-2">
            <div>{record.anchorName}</div>
          </div>
        </div>
      ),
    },
    {
      title: '关联抖音号数',
      dataIndex: 'accountCount',
      key: 'accountCount',
      width: 120,
      align: 'right',
      render: (_, record) => (
        <Button type="link" style={{ padding: 0 }} onClick={() => showDetailModal(record)}>
          {record.accountCount || 0}
        </Button>
      ),
    },
    {
      title: '累计直播场次',
      dataIndex: 'liveCount',
      key: 'liveCount',
      width: 120,
      align: 'right',
      render: (_, record) =>
        renderCompareData(record.liveCount, record.momLiveCount, record.momLiveCountRate),
    },
    {
      title: '累计直播时长',
      dataIndex: 'liveDuration',
      key: 'liveDuration',
      width: 120,
      align: 'right',
      render: (_, record) =>
        renderDurationCompareData(
          record.liveDuration,
          record.momLiveDuration,
          record.momLiveDurationRate,
        ),
    },
    {
      title: '曝光人数',
      dataIndex: 'exposureUcount',
      key: 'exposureUcount',
      width: 100,
      align: 'right',
      render: (_, record) =>
        renderCompareData(
          record.exposureUcount,
          record.momExposureUcount,
          record.momExposureUcountRate,
        ),
    },
    {
      title: '曝光次数',
      dataIndex: 'exposureCount',
      key: 'exposureCount',
      width: 100,
      align: 'right',
      render: (_, record) =>
        renderCompareData(
          record.exposureCount,
          record.momExposureCount,
          record.momExposureCountRate,
        ),
    },
    {
      title: '观看人数',
      dataIndex: 'viewCount',
      key: 'viewCount',
      width: 100,
      align: 'right',
      render: (_, record) =>
        renderCompareData(record.viewCount, record.momViewCount, record.momViewCountRate),
    },
    {
      title: '观看次数',
      dataIndex: 'viewTime',
      key: 'viewTime',
      width: 100,
      align: 'right',
      render: (_, record) =>
        renderCompareData(record.viewTime, record.momViewTime, record.momViewTimeRate),
    },
    {
      title: '小风车点击次数',
      dataIndex: 'componentClickCount',
      key: 'componentClickCount',
      width: 120,
      align: 'right',
      render: (_, record) =>
        renderCompareData(
          record.componentClickCount,
          record.momComponentClickCount,
          record.momComponentClickCountRate,
        ),
    },
    {
      title: '点赞次数',
      dataIndex: 'diggCount',
      key: 'diggCount',
      width: 100,
      align: 'right',
      render: (_, record) =>
        renderCompareData(record.diggCount, record.momDiggCount, record.momDiggCountRate),
    },
    {
      title: '评论次数',
      dataIndex: 'commentCount',
      key: 'commentCount',
      width: 100,
      align: 'right',
      render: (_, record) =>
        renderCompareData(record.commentCount, record.momCommentCount, record.momCommentCountRate),
    },
    {
      title: '分享次数',
      dataIndex: 'shareCount',
      key: 'shareCount',
      width: 100,
      align: 'right',
      render: (_, record) =>
        renderCompareData(record.shareCount, record.momShareCount, record.momShareCountRate),
    },
    {
      title: '关注人数',
      dataIndex: 'followCount',
      key: 'followCount',
      width: 100,
      align: 'right',
      render: (_, record) =>
        renderCompareData(record.followCount, record.momFollowCount, record.momFollowCountRate),
    },
    {
      title: '留资线索数',
      dataIndex: 'leadsCount',
      key: 'leadsCount',
      width: 100,
      align: 'right',
      render: (_, record) =>
        renderCompareData(record.leadsCount, record.momLeadsCount, record.momLeadsCountRate),
    },
    {
      title: '操作',
      key: 'operation',
      width: 80,
      align: 'right',
      fixed: 'right',
      render: (_, record) => (
        <Button
          type="link"
          style={{ padding: 0 }}
          onClick={() => {
            if (!record.anchorId) return;
            showDetailDrawer(record.anchorId);
          }}
        >
          查看详情
        </Button>
      ),
    },
  ];

  return (
    <>
      <ProTable<AnchorOverviewReport>
        ghost
        columns={columns}
        sticky={{ offsetHeader: STICKY_OFFSETHEADER }}
        tableClassName="custom-table"
        scroll={{ x: 'max-content' }}
        params={{
          anchorIds,
          accountIds,
          startDate: rangeTime?.[0],
          endDate: rangeTime?.[1],
          projectId,
          platform,
        }}
        request={(params, sorter, filter) => {
          return proTableRequestAdapter(params, sorter, filter, GetAnchorOverviewPage);
        }}
        search={false}
        options={{ ...proTableOptionsConfig }}
        pagination={{ ...proTablePaginationConfig }}
        headerTitle={<div>主播详细数据</div>}
        toolBarRender={() => [
          <Space key="compare" style={{ marginRight: '5px' }}>
            <Switch checked={compare} onChange={(checked) => setCompare(checked)} />
            上周期对比
          </Space>,
          <ExportButton
            exportFn={() => handleExport()}
            loading={pollingLoading}
            percent={percent}
            key="export"
          />,
          <Button
            key="anchorCompare"
            icon={<UserOutlined />}
            onClick={() => setAnchorCompareVisible(true)}
          >
            主播对比
          </Button>,
        ]}
        rowKey="anchorId"
      />
      {/* 主播详情抽屉 */}
      <AnchorDetail
        rangeTime={rangeTime}
        projectId={projectId}
        platform={platform}
        currentAnchorId={currentAnchorId}
        detailDrawerVisible={detailDrawerVisible}
        onClose={() => setDetailDrawerVisible(false)}
      />

      {/* 关联抖音号弹窗 */}
      <AnchorAccountModal
        anchor={anchor}
        anchorAccountVisible={anchorAccountVisible}
        setAnchorAccountVisible={setAnchorAccountVisible}
        rangeTime={rangeTime}
        projectId={projectId}
        platform={platform}
      />

      {/* 主播对比 */}
      <AnchorCompare
        anchorCompareVisible={anchorCompareVisible}
        setAnchorCompareVisible={setAnchorCompareVisible}
        rangeTime={rangeTime}
        projectId={projectId}
        platform={platform}
      />
    </>
  );
}
