import BreadCrumbSimple from '@/components/ui/breadcrumbSimple';
import useProjectId from '@/hooks/useProjectId';
import { cn } from '@/lib/utils';
import {
  AnchorScheduleClear,
  AnchorScheduleItem,
  BatchTimeRangeType,
  DeleteAnchorSchedule,
  GetAnchorBatchSetDraft,
  GetAnchorList,
  GetAnchorSchedule,
  UpdateAnchorSchedule,
  UpsertAnchorBatchSetDraft,
} from '@/services/anchor';
import { PlatForm } from '@/utils/platform';
import { nanoid, PageContainer, ProCard } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { useGetState, useLocalStorageState, useUpdate } from 'ahooks';
import { Button, message, Modal, Popover, Space, Switch, Tooltip } from 'antd';
import dayjs from 'dayjs';
import duration from 'dayjs/plugin/duration';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import { useAtomValue } from 'jotai';
import { Clock3, X } from 'lucide-react';
import React, { useEffect, useRef, useState } from 'react';
import {
  minScheduleDuration,
  SelectionArea,
  timeSlots,
  totalMinutes,
  weekDays,
} from './component/const';
import PopoverContent from './component/popoverContent';
import ScheduleAction from './component/scheduleAction';
import ScheduleBlock from './component/scheduleBlock';
import { useCopyPasteSchedule } from './hooks/useCopyPasteSchedule';
import { accountIdAtom } from './model/accountIdAtom';
import ScheduleBatchSet from './component/scheduleBatchSet';
import { useDarkMode } from '@/hooks/useDarkMode';
import { QuestionCircleOutlined } from '@ant-design/icons';

dayjs.extend(duration);
dayjs.extend(isSameOrBefore);

export default function AnchorSchedule() {
  const projectId = useProjectId();
  // 只有抖音平台
  const platform = PlatForm.Douyin;
  const [currentSchedule, setCurrentSchedule, getCurrentSchedule] = useGetState<
    Partial<AnchorScheduleItem>
  >({});
  // 划选区域信息
  const [selectionArea, setSelectionArea] = useState<SelectionArea>(null);
  // 是否正在划选
  const [isSelecting, setIsSelecting] = useState(false);
  // 划选新建日程的 Dom 的 Popover
  const [showPopover, setShowPopover] = useState(false);
  // 已经有日程的 Dom 的 Popover
  const [showSchedulePopover, setShowSchedulePopover] = useState(false);
  const timelineRef = useRef<HTMLDivElement>(null);
  const [showRangeTime, setShowRangeTime] = useState<string[]>([
    dayjs().format('YYYY-MM-DD'),
    dayjs().add(29, 'd').format('YYYY-MM-DD'),
  ]);
  // 新增吸附开关状态
  const [snapToTenMinutes, setSnapToTenMinutes] = useLocalStorageState<boolean>('schedule-snap', {
    defaultValue: false,
    listenStorageChange: true,
  });
  // 使用ref存储当前拖拽状态，确保在mousemove事件中可以访问到最新值
  const dragStateRef = useRef({
    isDragging: false,
    dragType: null as 'left' | 'right' | null,
    dragStartX: 0,
    originalSchedule: null as AnchorScheduleItem | null,
  });
  const atomKey = `${projectId || 'none'}_${platform}`;
  const accountIdMap = useAtomValue(accountIdAtom);
  const selectedAccountId = accountIdMap[atomKey];
  const [modal, contextHolder] = Modal.useModal();
  const [showSetScheduleModal, setShowSetScheduleModal] = useState(false);
  // 存储批量设置的时间段
  const [batchTimeRanges, setBatchTimeRanges] = useState<BatchTimeRangeType[]>([]);
  // 存储批量排班未设置主播的日程块
  const batchTimeDataRef = useRef<AnchorScheduleItem[]>([]);
  const update = useUpdate();
  const { isDarkMode } = useDarkMode();

  const { data: anchorListData } = useRequest(() => GetAnchorList({ projectId, platform }), {
    cacheKey: `GetAnchorList_${projectId}_${platform}`,
    cacheTime: 10 * 60 * 1000, // 10 分钟
    refreshDeps: [projectId, platform],
  });

  const {
    data: schedules,
    refresh: refreshSchedules,
    mutate: mutateSchedules,
  } = useRequest(
    () => {
      if (!selectedAccountId && !projectId) {
        return Promise.reject();
      }
      // 如果正在拖拽，则不请求避免轮询触发导致更新
      if (dragStateRef.current.isDragging && isSelecting) {
        return Promise.reject();
      }
      return GetAnchorSchedule({
        startDate: showRangeTime[0],
        endDate: showRangeTime[1],
        accountId: selectedAccountId,
        projectId,
        platform,
      });
    },
    {
      refreshDeps: [projectId, platform, selectedAccountId, showRangeTime],
      ready: !!projectId && !!selectedAccountId,
      // 10分钟轮询一次
      pollingInterval: 1000 * 60 * 10,
      pollingWhenHidden: false,
      // 请求成功后，将当前前端的 liveStatus 为 0 的状态保留下来
      onSuccess: (data) => {
        if (data) {
          mutateSchedules(() => {
            return [...data, ...batchTimeDataRef.current];
          });
        }
      },
    },
  );

  // 添加重置状态的函数
  const resetSelectionState = () => {
    setShowPopover(false);
    setShowSchedulePopover(false);
    setSelectionArea(null);
    setCurrentSchedule({});
    setIsSelecting(false);
  };

  // 切换日期的时候状态需要全部清空
  useEffect(() => {
    resetSelectionState();
  }, [showRangeTime]);

  const convertPositionToTime = (position: number) => {
    const timelineWidth = timelineRef.current?.clientWidth || 0;
    const minutes = Math.floor((position / timelineWidth) * totalMinutes);
    const hours = Math.floor(minutes / 60);
    const mins = Math.floor(minutes % 60);
    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
  };

  // 吸附到最近整半小时
  const snapToNearestHalfHour = (time: string) => {
    const [h, m] = time.split(':').map(Number);
    const timeScale = 30;
    let snapped = Math.round(m / timeScale) * timeScale;
    let hour = h;
    if (snapped === 60) {
      snapped = 0;
      hour = hour + 1;
      // 防止超出 23:59
      if (hour === 24) {
        hour = 23;
        snapped = 59;
      }
    }
    return `${hour.toString().padStart(2, '0')}:${snapped.toString().padStart(2, '0')}`;
  };

  // 时间转像素
  const timeToPosition = (time: string) => {
    const [h, m] = time.split(':').map(Number);
    const minutes = h * 60 + m;
    const timelineWidth = timelineRef.current?.clientWidth || 0;
    return (minutes / totalMinutes) * timelineWidth;
  };

  // 检查时间是否重叠的辅助函数
  const isTimeOverlapping = (
    date: string,
    startX: number,
    width: number,
    excludeScheduleId?: string,
  ): boolean => {
    // 确保 width 可以是负数，但我们需要规范化起点和终点
    const leftX = Math.min(startX, startX + width);
    const rightX = Math.max(startX, startX + width);

    const timelineWidth = timelineRef.current?.getBoundingClientRect().width || 0;

    // 转换为分钟以便比较
    const startMinutes = Math.floor((leftX / timelineWidth) * totalMinutes);
    const endMinutes = Math.floor((rightX / timelineWidth) * totalMinutes);
    const currentDateZero = dayjs(`${date} 00:00:00`);

    // 如果起始时间等于结束时间，认为是无效区间
    if (startMinutes === endMinutes) return false;

    if (schedules) {
      const checkResult = schedules?.some((schedule) => {
        // 排除当前正在拖拽的日程
        if (excludeScheduleId && schedule.scheduleId === excludeScheduleId) return false;
        // 排除不同日期的日程
        if (schedule.scheduleDate !== date) return false;
        // 排除liveStatus为0的排班（批量创建的临时排班）
        if (schedule.liveStatus === 0) return false;
        // 获取已有日程的开始和结束时间（分钟表示）
        const scheduleStartMinutes = dayjs(schedule.startTime).diff(
          currentDateZero.startOf('day'),
          'minutes',
        );
        const scheduleEndMinutes = dayjs(schedule.endTime).diff(
          currentDateZero.startOf('day'),
          'minutes',
        );
        // 检查是否有重叠：经典区间重叠检测
        // A的开始 < B的结束 且 A的结束 > B的开始
        return startMinutes < scheduleEndMinutes && endMinutes > scheduleStartMinutes;
      });

      return checkResult;
    }
    return false;
  };

  const handleMouseDown = (e: React.MouseEvent, rowIndex: number) => {
    // 如果点击的是已存在的排班块或其子元素，则不触发新的划选
    if (e.target instanceof Element) {
      const scheduleBlock = e.target.closest('.schedule-block');
      const cancelSchedule = e.target.closest('.cancel-schedule');
      if (scheduleBlock || cancelSchedule) {
        return;
      }
    }

    if (!timelineRef.current) return;

    // 清空所有相关状态
    resetSelectionState();

    // 设置新的选择区域
    const rect = timelineRef.current.getBoundingClientRect();
    const startX = e.clientX - rect.left;

    // 延迟一帧设置新的选择区域，确保之前的状态已经被清空
    requestAnimationFrame(() => {
      setIsSelecting(true);
      setSelectionArea({
        startX,
        width: 0,
        rowIndex,
      });
    });
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isSelecting || !timelineRef.current || !selectionArea) return;

    const rect = timelineRef.current.getBoundingClientRect();
    const currentX = e.clientX - rect.left;
    const width = currentX - selectionArea.startX;

    // 如果宽度没有变化，直接返回，避免不必要的状态更新
    if (Math.abs(selectionArea.width - width) < 1) return;

    // 根据 selectionArea.rowIndex 计算正确的日期 如果直接从参数传入 date 循环渲染会导致 date 错误
    const correctDate = dayjs(showRangeTime[0]).add(selectionArea.rowIndex, 'days');

    // 检查新的位置是否会与现有的 schedule 重叠
    const wouldOverlap = isTimeOverlapping(
      correctDate.format('YYYY-MM-DD'),
      selectionArea.startX,
      width,
    );

    if (wouldOverlap) {
      // 如果会重叠，不更新选择区域
      return;
    }

    setSelectionArea({
      ...selectionArea,
      width,
    });
  };

  const handleMouseUp = () => {
    // 如果 Popover 显示了则不需要触发这个事件
    if (showPopover) return;
    if (!selectionArea || !timelineRef.current) return;

    const width = selectionArea.width;
    const startX = width >= 0 ? selectionArea.startX : selectionArea.startX + width;
    const absWidth = Math.abs(width);

    // 计算时间线宽度与分钟的比例
    const timelineWidth = timelineRef.current.clientWidth;
    const pixelsPerMinute = timelineWidth / totalMinutes;

    // 确保选择区域至少为最小排班时长
    const finalWidth = Math.max(absWidth, minScheduleDuration * pixelsPerMinute);

    let startTime = convertPositionToTime(startX);
    let endTime = convertPositionToTime(startX + finalWidth);
    let snappedStartX = startX;
    let snappedEndX = startX + finalWidth;
    if (snapToTenMinutes) {
      startTime = snapToNearestHalfHour(startTime);
      endTime = snapToNearestHalfHour(endTime);
      // 重新计算像素
      snappedStartX = timeToPosition(startTime);
      snappedEndX = timeToPosition(endTime);
    }
    // snappedEndX 不能超过 23:59
    const maxX = timeToPosition('23:59');
    if (snappedEndX > maxX) {
      snappedEndX = maxX;
    }
    // 这里重新计算 endTime，保证不会超出 23:59
    endTime = convertPositionToTime(snappedEndX);

    // 根据 selectionArea.rowIndex 计算正确的日期 如果直接从参数传入 date 循环渲染会导致 date 错误
    const correctDate = dayjs(showRangeTime[0]).add(selectionArea.rowIndex, 'days');
    const correctDateFormat = correctDate.format('YYYY-MM-DD');

    setCurrentSchedule({
      startTime: `${correctDateFormat} ${startTime}`,
      endTime: `${correctDateFormat} ${endTime}`,
      scheduleDate: correctDateFormat,
    });

    setSelectionArea((prev) => {
      if (!prev) return null;
      return {
        ...prev,
        startX: snappedStartX,
        endX: snappedEndX,
        width: snappedEndX - snappedStartX,
      };
    });

    setShowPopover(true);
    setIsSelecting(false);
  };

  const handleSaveSchedule = (scheduleId?: string) => {
    if (scheduleId) {
      // 从batchTimeDataRef中删除该scheduleId
      batchTimeDataRef.current = batchTimeDataRef.current.filter(
        (schedule) => schedule.scheduleId !== scheduleId,
      );
    }
    refreshSchedules();

    setShowPopover(false);
    setShowSchedulePopover(false);
    setSelectionArea(null);
  };

  const handleCancelSchedule = () => {
    setShowPopover(false);
    setShowSchedulePopover(false);
    setSelectionArea(null);
  };

  // 添加拖拽移动处理函数
  const handleDragMove = (e: MouseEvent) => {
    // 使用ref中的值而不是state
    const { isDragging, dragType, originalSchedule, dragStartX } = dragStateRef.current;
    if (!isDragging || !timelineRef.current || !originalSchedule || !dragType) return;

    const rect = timelineRef.current.getBoundingClientRect();
    const currentX = e.clientX - rect.left;

    // 超出就不动了
    if (currentX < 0 || currentX > rect.width) {
      return;
    }

    const deltaX = currentX - dragStartX;

    const startOfDay = dayjs(originalSchedule.scheduleDate).startOf('day');

    const startMinutes = dayjs(originalSchedule.startTime).diff(startOfDay, 'minutes');
    const endMinutes = dayjs(originalSchedule.endTime).diff(startOfDay, 'minutes');
    const minutesPerPixel = totalMinutes / rect.width;
    const deltaMinutes = Math.round(deltaX * minutesPerPixel);

    let newStartTime = originalSchedule.startTime;
    let newEndTime = originalSchedule.endTime;

    // 避免把左边拖拽超过到右边 有一个最小排班时长 30min
    if (dragType === 'left') {
      const newStartMinutes = Math.max(
        0,
        Math.min(endMinutes - minScheduleDuration, startMinutes + deltaMinutes),
      );
      newStartTime = startOfDay.add(newStartMinutes, 'minutes').format('YYYY-MM-DD HH:mm:ss');
    } else {
      // 限制 newEndMinutes 最大为 1439，不能超过 23:59
      const newEndMinutes = Math.max(
        startMinutes + minScheduleDuration,
        Math.min(1439, endMinutes + deltaMinutes),
      );
      newEndTime = startOfDay.add(newEndMinutes, 'minutes').format('YYYY-MM-DD HH:mm:ss');
    }

    // 将新的开始和结束时间转换为分钟
    const newStartMinutes = dayjs(newStartTime).diff(startOfDay, 'minutes');
    const newEndMinutes = dayjs(newEndTime).diff(startOfDay, 'minutes');

    // 计算对应的位置
    const newStartX = (newStartMinutes / totalMinutes) * rect.width;
    const newEndX = (newEndMinutes / totalMinutes) * rect.width;
    const newWidth = newEndX - newStartX;

    // 检查是否与其他排班重叠
    const wouldOverlap = isTimeOverlapping(
      originalSchedule.scheduleDate as string,
      newStartX,
      newWidth,
      originalSchedule.scheduleId,
    );

    if (wouldOverlap) return;

    // 更新本地状态
    setCurrentSchedule({
      ...originalSchedule,
      startTime: newStartTime,
      endTime: newEndTime,
    });

    // 判断是否是临时scheduleId（selectionArea的拖拽）
    const isTemporarySchedule = originalSchedule.scheduleId?.toString().startsWith('temp-');

    if (isTemporarySchedule && selectionArea) {
      // 如果是selectionArea的拖拽，不需要更新schedules数据
      // 但可以实时更新selectionArea的视觉效果
      if (dragType === 'left') {
        setSelectionArea({
          ...selectionArea,
          startX: newStartX,
          width: selectionArea.width + (selectionArea.startX - newStartX),
        });
      } else {
        setSelectionArea({
          ...selectionArea,
          width: newEndX - selectionArea.startX,
        });
      }
    } else {
      // 使用 mutate 进行乐观更新
      mutateSchedules((oldSchedules) => {
        if (!oldSchedules) return oldSchedules;

        return oldSchedules.map((schedule) => {
          if (schedule.scheduleId === originalSchedule.scheduleId) {
            return {
              ...schedule,
              startTime: newStartTime,
              endTime: newEndTime,
            };
          }
          return schedule;
        });
      });
    }
  };

  // 添加拖拽结束处理函数
  const handleDragEnd = async () => {
    // 使用ref中的值
    const { isDragging, originalSchedule } = dragStateRef.current;
    if (!isDragging || !originalSchedule) return;

    // 移除全局鼠标事件监听
    document.removeEventListener('mousemove', handleDragMove);
    document.removeEventListener('mouseup', handleDragEnd);

    const latestScheduleData = getCurrentSchedule();

    // 判断是否是临时scheduleId（selectionArea的拖拽）
    const isTemporarySchedule = originalSchedule.scheduleId?.toString().startsWith('temp-');

    if (isTemporarySchedule && selectionArea) {
      // 如果是selectionArea的拖拽，更新selectionArea的位置和大小
      if (latestScheduleData.startTime && latestScheduleData.endTime && timelineRef.current) {
        const rect = timelineRef.current.getBoundingClientRect();
        const startOfDay = dayjs(latestScheduleData.scheduleDate).startOf('day');
        const startMinutes = dayjs(latestScheduleData.startTime).diff(startOfDay, 'minutes');
        const endMinutes = dayjs(latestScheduleData.endTime).diff(startOfDay, 'minutes');

        // 计算新的位置
        const newStartX = (startMinutes / totalMinutes) * rect.width;
        const newEndX = (endMinutes / totalMinutes) * rect.width;

        // 更新selectionArea
        setSelectionArea({
          ...selectionArea,
          startX: newStartX,
          width: newEndX - newStartX,
        });
      }
    } else {
      // 原有的更新排班时间逻辑
      if (
        latestScheduleData.startTime &&
        latestScheduleData.endTime &&
        selectedAccountId &&
        latestScheduleData.anchor?.id
      ) {
        try {
          await UpdateAnchorSchedule(
            { projectId, platform },
            {
              accountId: selectedAccountId,
              id: latestScheduleData.scheduleId,
              startTime: latestScheduleData.startTime,
              endTime: latestScheduleData.endTime,
              description: latestScheduleData.description,
              anchorId: latestScheduleData.anchor?.id,
            },
          );
          // 由于已经在 handleDragMove 中使用了 mutate 进行乐观更新
          // 这里不需要再刷新全部数据，除非服务端有其他变动
          // refreshSchedules();
        } catch (error) {
          // 如果更新失败，回滚乐观更新的数据
          message.error('更新失败，请重试');
          refreshSchedules();
        }
      }
    }

    // 同时重置ref中的状态
    dragStateRef.current = {
      isDragging: false,
      dragType: null,
      dragStartX: 0,
      originalSchedule: null,
    };

    // 强制更新视图 使得dragStateRef.current传入 hook 是最新的
    update();
  };

  // 添加拖拽开始处理函数
  const handleDragStart = (
    e: React.MouseEvent,
    schedule: AnchorScheduleItem,
    type: 'left' | 'right',
  ) => {
    e.stopPropagation();
    if (!timelineRef.current) return;
    setShowPopover(false);

    const rect = timelineRef.current.getBoundingClientRect();
    const currentDragStartX = e.clientX - rect.left;

    // 更新状态
    setCurrentSchedule(schedule);

    // 如果是selectionArea的拖拽，确保有一个临时scheduleId
    const scheduleWithId = schedule.scheduleId
      ? schedule
      : {
          ...schedule,
          scheduleId: `temp-${nanoid()}`, // 添加临时ID
        };

    // 同时更新ref，确保在事件处理函数中能获取到最新值
    dragStateRef.current = {
      isDragging: true,
      dragType: type,
      dragStartX: currentDragStartX,
      originalSchedule: scheduleWithId,
    };

    // 添加全局鼠标事件监听
    document.addEventListener('mousemove', handleDragMove);
    document.addEventListener('mouseup', handleDragEnd);
  };

  const handleDeleteSchedule = async (schedule: AnchorScheduleItem) => {
    if (!schedule.scheduleId) return;

    if (schedule.liveStatus === 0) {
      batchTimeDataRef.current = batchTimeDataRef.current.filter(
        (s) => s.scheduleId !== schedule.scheduleId,
      );
      mutateSchedules((oldSchedules) => {
        if (!oldSchedules) return oldSchedules;
        return oldSchedules.filter((s) => s.scheduleId !== schedule.scheduleId);
      });
      return;
    }

    const result = await DeleteAnchorSchedule(
      {
        projectId,
        platform,
      },
      {
        id: Number(schedule.scheduleId),
      },
    );
    if (result.code === 0) {
      message.success('删除成功');
      refreshSchedules();
    } else {
      message.error('删除失败');
    }
  };

  const scheduleBlockHandler = {
    handleDragStart,
    handleSaveSchedule,
    handleCancelSchedule,
    handleDeleteSchedule,
  };

  const scheduleBlockState = {
    isDragging: dragStateRef.current.isDragging,
    originalSchedule: dragStateRef.current.originalSchedule,
    showSchedulePopover,
    showPopover,
    setShowSchedulePopover,
    setShowPopover,
  };

  // 复制粘贴相关逻辑
  const { canPaste, copiedDate, handleClearCopy, renderCopyPasteButton } = useCopyPasteSchedule(
    schedules,
    refreshSchedules,
    projectId,
    platform,
    batchTimeDataRef,
  );

  // 清空排班逻辑
  const handleClearSchedules = async () => {
    if (!selectedAccountId) return;
    const result = await AnchorScheduleClear(
      {
        projectId,
        platform,
      },
      {
        accountId: selectedAccountId,
        startDate: showRangeTime[0],
        endDate: showRangeTime[1],
      },
    );
    if (result.code === 0) {
      message.success('已清空当前区间内未来时间排班');
      // 把未设置的也都清空掉
      batchTimeDataRef.current = [];
      refreshSchedules();
    } else {
      message.error(result.msg);
    }
  };

  const modalConfig = {
    title: '清空排班',
    content: (
      <div>是否确认清空，所选时间段内历史已产生的记录不会清空，只对未发生的记录进行清空。</div>
    ),
    onOk: handleClearSchedules,
    okText: '确认清空',
    cancelText: '取消',
  };

  const { data: batchDraftData, refresh: refreshBatchDraftData } = useRequest(
    () => {
      if (!selectedAccountId) return Promise.reject();
      return GetAnchorBatchSetDraft({ projectId, platform, accountId: selectedAccountId });
    },
    {
      refreshDeps: [selectedAccountId, projectId, platform],
    },
  );

  // 处理批量设置排班
  const handleBatchSetSchedules = async () => {
    if (!selectedAccountId) {
      message.warning('未选择账号');
      return;
    }
    // 获取日期范围内的所有日期
    const startDate = dayjs(showRangeTime[0]);
    const endDate = dayjs(showRangeTime[1]);
    const dateRange: string[] = [];
    let currentDate = startDate;
    while (currentDate.isSameOrBefore(endDate)) {
      dateRange.push(currentDate.format('YYYY-MM-DD'));
      currentDate = currentDate.add(1, 'day');
    }
    // 新建前先删掉所有的批量创建的排班
    batchTimeDataRef.current = [];

    // 先更新schedules状态，过滤掉所有liveStatus为0的排班
    await new Promise<void>((resolve) => {
      mutateSchedules((oldSchedules) => {
        if (!oldSchedules) return oldSchedules;
        const filtered = oldSchedules.filter((s) => s.liveStatus !== 0);
        resolve();
        return filtered;
      });
    });

    // 为每个日期的每个时间段创建排班
    const newSchedules: AnchorScheduleItem[] = [];
    dateRange.forEach((date) => {
      batchTimeRanges.forEach((timeRange) => {
        // 检查是否与现有排班冲突
        const startX = timeToPosition(timeRange.startTime);
        const endX = timeToPosition(timeRange.endTime);
        const width = endX - startX;

        // 检查是否与现有排班时间重叠
        const hasConflict = isTimeOverlapping(date, startX, width);

        // 如果没有冲突，才创建新排班
        if (!hasConflict) {
          // 创建新排班
          const newSchedule: AnchorScheduleItem = {
            scheduleId: nanoid(),
            scheduleDate: date,
            startTime: `${date} ${timeRange.startTime}:00`,
            endTime: `${date} ${timeRange.endTime}:00`,
            anchor: {
              name: '未设置主播',
            },
            liveStatus: 0, // 未设置
          };
          newSchedules.push(newSchedule);
        }
      });
    });
    batchTimeDataRef.current = newSchedules;

    // 使用mutateSchedules更新排班数据
    mutateSchedules((oldSchedules) => {
      if (!oldSchedules) return newSchedules;
      return [...oldSchedules, ...newSchedules];
    });

    setShowSetScheduleModal(false);

    // 接口存储一下当前的排班草稿
    await UpsertAnchorBatchSetDraft(
      { projectId, platform },
      {
        accountId: selectedAccountId,
        draftTimeList: batchTimeRanges,
      },
    );

    await refreshBatchDraftData();
  };

  // 移除 isPastSchedule 变量的使用
  // const isPastSchedule = dayjs(currentSchedule.startTime).isBefore(dayjs());

  return (
    <PageContainer title={<BreadCrumbSimple breadcrumbs={['主播管理', '主播排班']} />}>
      {/* 顶部操作栏与筛选 */}
      <ScheduleAction
        showRangeTime={showRangeTime}
        setShowRangeTime={setShowRangeTime}
        projectId={projectId}
        platform={platform}
        refreshSchedules={refreshSchedules}
      />

      {/* 排班内容区 */}
      <ProCard
        style={{
          marginTop: '16px',
          height: 'calc(100vh - 56px - 180px)',
        }}
        className="scrollbar-hide no-scrollbar schedule-body"
      >
        <div className="flex h-[24px] items-center gap-2">
          <div className="flex items-center gap-2">
            <div className={cn('h-2 w-2 rounded-full bg-[#0FA964]')} />
            <div className="flex items-center">
              已上播
              <Tooltip
                title={
                  <div>
                    <p>提前10分钟(开播时间 - 主播上播时间≤=10分钟),则算作该主播的上播记录。</p>
                    <p>延迟10分钟(主播上播时间 - 开播时间≤=10分钟),则算作该主播的上播记录。</p>
                    <p>上述时间段外的都不算，特殊情况下需要到主播数据概览中手动关联数据。</p>
                  </div>
                }
              >
                <QuestionCircleOutlined className="ml-1" />
              </Tooltip>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <div className={cn('h-2 w-2 rounded-full bg-new-media-blue-900')} />
            <div>未开播</div>
          </div>
          <div className="flex items-center gap-2">
            <div className={cn('h-2 w-2 rounded-full bg-new-media-gray-600')} />
            <div>未上播</div>
          </div>
          <div className="flex items-center gap-1">
            <Tooltip title="划选时间时自动吸附到整点时间,吸附到 00 或 30 分">
              <div className="flex items-center">
                <span className="mr-1">时间吸附</span>
                <Switch size="small" checked={snapToTenMinutes} onChange={setSnapToTenMinutes} />
              </div>
            </Tooltip>
          </div>
          {canPaste && (
            <div className="flex items-center gap-2">
              <Button size="small" type="link" onClick={handleClearCopy}>
                退出复制排班
              </Button>
            </div>
          )}

          <div className="ml-auto">
            <Space>
              <Button onClick={() => modal.confirm(modalConfig)}>清空排班</Button>
              <Button type="primary" onClick={() => setShowSetScheduleModal(true)}>
                <Clock3 size={16} strokeWidth={1.5} />
                <span>设置上播时间</span>
              </Button>
            </Space>
          </div>
        </div>

        {/* 整体排班表区域 */}
        <div className="scrollbar-hide no-scrollbar relative mt-4 max-h-[calc(100vh-56px-180px-100px)] overflow-x-auto overflow-y-auto rounded-md border border-new-media-gray-100">
          {/* 时间轴标尺 */}
          {/* 24*60+192(w-48的 px) */}
          <div className="sticky top-0 z-[100] bg-white">
            <div className="relative flex min-w-[1632px] select-none border-b border-new-media-gray-100">
              <div className="sticky left-0 top-0 z-[100] w-48 min-w-48 border-r border-new-media-gray-100 bg-[#F8FAFF]" />
              <div className="flex min-w-[1440px] flex-1 bg-[#F8FAFF] pb-1">
                {timeSlots.map((time, index) => (
                  <div
                    key={time}
                    className={cn(
                      'flex-1 translate-x-1/2 bg-[#F8FAFF] py-1 text-center text-xs text-new-media-gray-600',
                      {
                        'translate-x-0 bg-transparent': index === timeSlots.length - 1,
                      },
                    )}
                  >
                    {index === timeSlots.length - 1 ? '' : `${time}`}
                  </div>
                ))}
              </div>
              {selectionArea && (
                <div className="absolute bottom-[16px] left-48 flex items-center">
                  <div
                    className={cn(
                      'absolute whitespace-nowrap bg-[#F8FAFF] px-6 text-xs text-blue-500',
                      // {
                      //   'text-[#E71919]': isPastSchedule,
                      // },
                    )}
                    style={{
                      left: `${selectionArea.width >= 0 ? selectionArea.startX : selectionArea.startX + selectionArea.width}px`,
                      transform: 'translateX(-50%)',
                    }}
                  >
                    {convertPositionToTime(
                      selectionArea.width >= 0
                        ? selectionArea.startX
                        : selectionArea.startX + selectionArea.width,
                    )}
                  </div>
                  <div
                    className={cn(
                      'absolute whitespace-nowrap bg-[#F8FAFF] px-6 text-xs text-blue-500',
                      // {
                      //   'text-[#E71919]': isPastSchedule,
                      // },
                    )}
                    style={{
                      left: `${selectionArea.width >= 0 ? selectionArea.startX + selectionArea.width : selectionArea.startX}px`,
                      transform: 'translateX(-50%)',
                    }}
                  >
                    {convertPositionToTime(
                      selectionArea.width >= 0
                        ? selectionArea.startX + selectionArea.width
                        : selectionArea.startX,
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
          {/* 排班内容区 */}
          <div className="flex flex-col">
            {Array.from({ length: dayjs(showRangeTime[1]).diff(showRangeTime[0], 'days') + 1 }).map(
              (_, index) => {
                const date = dayjs(showRangeTime[0]).add(index, 'days');
                const formattedDate = date.format('YYYY-MM-DD');
                const daySchedules = schedules?.filter((s) => s.scheduleDate === formattedDate);
                const isSourceDate = formattedDate === copiedDate;

                return (
                  // 24*60+192(w-48的 px)
                  <div
                    key={formattedDate}
                    className="flex min-w-[1632px] border-b border-new-media-gray-100"
                    // 在边界的时候触发比如末尾行 24:00 的时候触发
                    onMouseLeave={handleMouseUp}
                  >
                    <div className="group sticky left-0 z-[60] w-48 min-w-48 select-none border-r border-new-media-gray-100 bg-white px-4">
                      <div
                        className={cn('my-auto flex h-full items-center justify-between gap-1', {
                          'text-green-600': date.day() === 6 || date.day() === 0,
                          'text-blue-600': date.isSame(dayjs(), 'day'),
                        })}
                      >
                        <div>{date.format('MM/DD')}</div>
                        <div
                          className={cn('text-gray-500', {
                            'text-green-600': date.day() === 6 || date.day() === 0,
                            'text-blue-600': date.isSame(dayjs(), 'day'),
                          })}
                        >
                          {weekDays[date.day()]}
                        </div>
                        <div
                          className={cn('flex w-[72px] gap-1', {
                            'opacity-100': canPaste, // 复制排班状态下始终显示
                            'opacity-0 transition-opacity group-hover:opacity-100': !canPaste, // 非复制状态下鼠标悬停才显示
                          })}
                        >
                          {renderCopyPasteButton(isSourceDate, formattedDate)}
                        </div>
                      </div>
                    </div>
                    <div
                      ref={timelineRef}
                      // 为了应对划选话切换日期，再次 handleMouseDown 的时候会导致 timelineRef 丢失未加载好。因此使用key 强制重渲染
                      key={`${showRangeTime[0]}_${showRangeTime[1]}_${index}`}
                      className="relative h-[55px] min-w-[1440px] flex-1"
                      onMouseDown={(e) => handleMouseDown(e, index)}
                      onMouseMove={handleMouseMove}
                      onMouseUp={handleMouseUp}
                    >
                      <div className="flex w-full">
                        {Array.from({ length: timeSlots.length }).map((_, index) => (
                          <div
                            key={index}
                            className={cn('h-[55px] flex-1 border-r border-new-media-gray-100', {
                              'border-r-0': index === timeSlots.length - 1,
                            })}
                          />
                        ))}
                      </div>
                      {daySchedules?.map((schedule) => (
                        <ScheduleBlock
                          key={schedule.scheduleId}
                          handlers={scheduleBlockHandler}
                          state={scheduleBlockState}
                          schedule={schedule}
                          currentSchedule={currentSchedule}
                          setCurrentSchedule={setCurrentSchedule}
                          setSelectionArea={setSelectionArea}
                          allSchedules={schedules}
                          projectId={projectId}
                          platform={platform}
                          anchorListData={anchorListData}
                          canPaste={canPaste}
                        />
                      ))}
                      {selectionArea && selectionArea.rowIndex === index && (
                        <Popover
                          open={showPopover && selectionArea.rowIndex === index}
                          content={
                            <PopoverContent
                              currentSchedule={currentSchedule}
                              handleSaveSchedule={handleSaveSchedule}
                              handleCancelSchedule={handleCancelSchedule}
                              allSchedules={schedules}
                              projectId={projectId}
                              platform={platform}
                              anchorListData={anchorListData}
                              // isPastSchedule={isPastSchedule}
                            />
                          }
                          trigger="click"
                          placement="rightTop"
                          overlayClassName="schedule-popover"
                          destroyOnHidden
                          getPopupContainer={() =>
                            document.querySelector('.schedule-body') || document.body
                          }
                        >
                          <div
                            className={cn(
                              'group absolute z-50 h-[55px] overflow-hidden border border-[#3369EE] px-1',
                              {
                                'bg-[#F4F7FF]': !isDarkMode,
                                'bg-[#1B2A4F]': isDarkMode,
                                // 'border-red-500 bg-[#FDE4E3]': isPastSchedule,
                              },
                            )}
                            style={{
                              left: `${selectionArea.width >= 0 ? selectionArea.startX : selectionArea.startX + selectionArea.width}px`,
                              width: `${Math.abs(selectionArea.width)}px`,
                              top: '0',
                            }}
                          >
                            <div className="relative size-full">
                              {/* 左侧拖拽区域 */}
                              <div
                                className="resize-handle absolute -left-[10px] bottom-0 top-0 z-[60] w-4 cursor-ew-resize"
                                onMouseDown={(e) => {
                                  // 确保currentSchedule包含scheduleDate
                                  if (!currentSchedule.scheduleDate && selectionArea) {
                                    const correctDate = dayjs(showRangeTime[0]).add(
                                      selectionArea.rowIndex,
                                      'days',
                                    );
                                    const correctDateFormat = correctDate.format('YYYY-MM-DD');
                                    setCurrentSchedule({
                                      ...currentSchedule,
                                      scheduleDate: correctDateFormat,
                                    });
                                  }
                                  handleDragStart(e, currentSchedule as AnchorScheduleItem, 'left');
                                }}
                                onClick={(e) => e.stopPropagation()}
                              />
                              {/* 右侧拖拽区域 */}
                              <div
                                className="resize-handle absolute -right-[10px] bottom-0 top-0 z-[60] w-4 cursor-ew-resize"
                                onMouseDown={(e) => {
                                  // 确保currentSchedule包含scheduleDate
                                  if (!currentSchedule.scheduleDate && selectionArea) {
                                    const correctDate = dayjs(showRangeTime[0]).add(
                                      selectionArea.rowIndex,
                                      'days',
                                    );
                                    const correctDateFormat = correctDate.format('YYYY-MM-DD');
                                    setCurrentSchedule({
                                      ...currentSchedule,
                                      scheduleDate: correctDateFormat,
                                    });
                                  }
                                  handleDragStart(
                                    e,
                                    currentSchedule as AnchorScheduleItem,
                                    'right',
                                  );
                                }}
                                onClick={(e) => e.stopPropagation()}
                              />
                            </div>
                            <div
                              className="cancel-schedule absolute left-1/2 top-1/2 hidden -translate-x-1/2 -translate-y-1/2 cursor-pointer group-hover:block"
                              onClick={handleCancelSchedule}
                            >
                              <X
                                className={cn('h-4 w-4 text-[#5B89FA]', {
                                  // 'text-[#E71919]': isPastSchedule,
                                })}
                              />
                            </div>
                          </div>
                        </Popover>
                      )}
                      {/* 划选区域左侧虚线 */}
                      {selectionArea && (
                        <div
                          className={cn(
                            'absolute bottom-0 top-0 z-[90] border-l border-dashed border-[#3369EE]',
                            {
                              // 'border-red-500': isPastSchedule,
                            },
                          )}
                          style={{
                            left: `${selectionArea.width >= 0 ? selectionArea.startX : selectionArea.startX + selectionArea.width}px`,
                          }}
                        />
                      )}
                      {/* 划选区域右侧虚线 */}
                      {selectionArea && (
                        <div
                          className={cn(
                            'absolute bottom-0 top-0 z-[90] border-l border-dashed border-[#3369EE]',
                            {
                              // 'border-red-500': isPastSchedule,
                            },
                          )}
                          style={{
                            left: `${selectionArea.width >= 0 ? selectionArea.startX + selectionArea.width - 1 : selectionArea.startX}px`,
                          }}
                        />
                      )}
                    </div>
                  </div>
                );
              },
            )}
          </div>
        </div>

        <div className="h-8 w-full" />
      </ProCard>

      {/* 清空排班 Modal */}
      {contextHolder}

      <Modal
        title="设置上播时间"
        open={showSetScheduleModal}
        onCancel={() => setShowSetScheduleModal(false)}
        onOk={handleBatchSetSchedules}
        destroyOnHidden
        width={1200}
        maskClosable={false}
      >
        <ScheduleBatchSet
          showRangeTime={showRangeTime}
          setShowRangeTime={setShowRangeTime}
          onTimeRangesChange={setBatchTimeRanges}
          batchDraftData={batchDraftData}
        />
      </Modal>
    </PageContainer>
  );
}
