import { useState } from 'react';
import { message, Modal, Tooltip } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import React from 'react';
import dayjs from 'dayjs';
import {
  AnchorScheduleBody,
  AnchorScheduleItem,
  BatchCreateAnchorSchedule,
  BatchDeleteAnchorSchedule,
} from '@/services/anchor';
import { useAtomValue } from 'jotai';
import { accountIdAtom } from '../model/accountIdAtom';
import { nanoid } from '@ant-design/pro-components';

// 复制粘贴排班相关 Hook
export function useCopyPasteSchedule(
  schedules?: AnchorScheduleItem[],
  refreshSchedules?: () => Promise<AnchorScheduleItem[] | undefined>,
  projectId?: string,
  platform?: number,
  batchTimeDataRef?: React.MutableRefObject<AnchorScheduleItem[]>,
) {
  const [copiedSchedules, setCopiedSchedules] = useState<AnchorScheduleItem[]>([]);
  const [canPaste, setCanPaste] = useState(false);
  const [copiedDate, setCopiedDate] = useState('');
  const [pastedDates, setPastedDates] = useState<string[]>([]);
  const atomKey = `${projectId || 'none'}_${platform}`;
  const accountIdMap = useAtomValue(accountIdAtom);
  const selectedAccountId = accountIdMap[atomKey];

  const handleCopySchedule = (date: string) => {
    const daySchedules = schedules?.filter((s) => s.scheduleDate === date);
    if (daySchedules?.length === 0) {
      message.warning('当前日期没有排班数据可复制');
      return;
    }
    setCopiedSchedules(daySchedules || []);
    setCanPaste(true);
    setCopiedDate(date);
    message.success('排班已复制，可粘贴到其他日期');
  };

  const formatTimeWithDate = (time: string | undefined, date: string): string => {
    if (!time) return '';
    return dayjs(`${date} ${dayjs(time).format('HH:mm:ss')}`).format('YYYY-MM-DD HH:mm:ss');
  };

  // 创建新排班数据
  const createNewSchedules = (
    scheduleData: AnchorScheduleItem[],
    targetDate: string,
  ): AnchorScheduleBody[] => {
    return scheduleData.map((schedule) => ({
      accountId: selectedAccountId,
      anchorId: schedule.anchor?.id,
      startTime: formatTimeWithDate(schedule.startTime, targetDate),
      endTime: formatTimeWithDate(schedule.endTime, targetDate),
      description: schedule.description,
    })) as AnchorScheduleBody[];
  };

  const handlePasteSchedule = async (targetDate: string) => {
    // 参数校验
    if (!projectId || !platform || !selectedAccountId) {
      message.warning('项目ID或平台ID不存在');
      return;
    }
    if (!canPaste || copiedSchedules.length === 0) {
      message.warning('没有可粘贴的排班数据');
      return;
    }
    if (targetDate === copiedDate) {
      message.warning('不能粘贴到源日期');
      return;
    }

    try {
      const existingSchedules = schedules?.filter((s) => s.scheduleDate === targetDate);
      if (existingSchedules?.length) {
        Modal.confirm({
          title: '覆盖排班',
          content: '您确定要覆盖当天的排班计划吗？',
          onOk: async () => {
            try {
              // 分离 liveStatus === 0 和其他排班
              const normalSchedules = existingSchedules.filter(
                (schedule) => schedule.liveStatus !== 0,
              );
              const draftSchedules = existingSchedules.filter(
                (schedule) => schedule.liveStatus === 0,
              );

              // 删除正常排班（需要调用接口）
              const scheduleIdsToDelete = normalSchedules.map((schedule) =>
                Number(schedule.scheduleId),
              );

              if (scheduleIdsToDelete.length > 0) {
                await BatchDeleteAnchorSchedule({ projectId, platform }, scheduleIdsToDelete);
              }

              // 处理草稿排班（从 batchTimeDataRef 中移除）
              if (batchTimeDataRef && draftSchedules.length > 0) {
                const draftIds = draftSchedules.map((s) => s.scheduleId);
                batchTimeDataRef.current = batchTimeDataRef.current.filter(
                  (s) => !draftIds.includes(s.scheduleId),
                );
              }

              // 创建新的排班
              const copiedSchedulesFilterTemp = copiedSchedules.filter((s) => s.liveStatus !== 0);
              const newSchedules = createNewSchedules(copiedSchedulesFilterTemp, targetDate);
              await BatchCreateAnchorSchedule({ projectId, platform }, newSchedules);

              // 如果复制的排班中有 liveStatus === 0 的，也要复制到目标日期
              if (batchTimeDataRef) {
                const copiedDraftSchedules = copiedSchedules.filter((s) => s.liveStatus === 0);
                if (copiedDraftSchedules.length > 0) {
                  const newDraftSchedules = copiedDraftSchedules.map((schedule) => ({
                    ...schedule,
                    scheduleId: nanoid(), // 生成新的ID
                    scheduleDate: targetDate,
                    startTime: formatTimeWithDate(schedule.startTime, targetDate),
                    endTime: formatTimeWithDate(schedule.endTime, targetDate),
                  }));

                  batchTimeDataRef.current = [...batchTimeDataRef.current, ...newDraftSchedules];
                }
              }

              setPastedDates((prev) => [...prev, targetDate]);
              message.success('排班已覆盖到目标日期');

              await refreshSchedules?.();
            } catch (error) {
              message.error('覆盖排班失败，请重试');
              console.error('覆盖排班失败:', error);
            }
          },
        });
        return;
      }

      // 直接粘贴到没有排班的日期
      const copiedSchedulesFilterTemp = copiedSchedules.filter((s) => s.liveStatus !== 0);
      const newSchedules = createNewSchedules(copiedSchedulesFilterTemp, targetDate);
      await BatchCreateAnchorSchedule({ projectId, platform }, newSchedules);

      // 处理复制的排班中 liveStatus === 0 的数据
      if (batchTimeDataRef) {
        const copiedDraftSchedules = copiedSchedules.filter((s) => s.liveStatus === 0);
        if (copiedDraftSchedules.length > 0) {
          const newDraftSchedules = copiedDraftSchedules.map((schedule) => ({
            ...schedule,
            scheduleId: nanoid(), // 生成新的ID
            scheduleDate: targetDate,
            startTime: formatTimeWithDate(schedule.startTime, targetDate),
            endTime: formatTimeWithDate(schedule.endTime, targetDate),
          }));

          batchTimeDataRef.current = [...batchTimeDataRef.current, ...newDraftSchedules];
        }
      }

      setPastedDates((prev) => [...prev, targetDate]);
      message.success('排班已粘贴到目标日期');
      await refreshSchedules?.();
    } catch (error) {
      message.error('粘贴排班失败，请重试');
      console.error('粘贴排班失败:', error);
    }
  };

  const handleClearCopy = () => {
    setCopiedSchedules([]);
    setCanPaste(false);
    setCopiedDate('');
    setPastedDates([]);
    message.info('已取消复制排班状态');
  };

  const renderCopyPasteButton = (isSourceDate: boolean, formattedDate: string) => {
    const isPastDate = dayjs(formattedDate).isBefore(dayjs().startOf('day'), 'day');
    switch (true) {
      case isSourceDate && canPaste:
        return (
          <button
            disabled
            className="flex h-6 w-full cursor-not-allowed items-center justify-center rounded bg-gray-200 px-2 text-xs text-gray-500"
          >
            已复制
          </button>
        );
      case pastedDates.includes(formattedDate):
        return (
          <button
            disabled
            className="flex h-6 w-full cursor-not-allowed items-center justify-center rounded bg-gray-200 px-2 text-xs text-gray-500"
          >
            已粘贴
          </button>
        );
      case canPaste && isPastDate:
        return (
          <Tooltip title="不允许修改过去时间排班">
            <button
              disabled
              className="flex h-6 w-full cursor-not-allowed items-center justify-center gap-1 rounded bg-gray-200 px-2 text-xs text-gray-500"
            >
              <PlusOutlined className="text-xs" />
            </button>
          </Tooltip>
        );
      case canPaste:
        return (
          <button
            onClick={() => handlePasteSchedule(formattedDate)}
            className="flex h-6 w-full items-center justify-center gap-1 rounded bg-[#1e5eff] px-2 text-xs text-white"
          >
            <PlusOutlined className="text-xs" />
          </button>
        );
      default:
        return (
          <button
            onClick={() => handleCopySchedule(formattedDate)}
            className="flex h-6 w-full items-center justify-center rounded bg-[#1e5eff] px-2 text-xs text-white opacity-0 group-hover:opacity-100"
          >
            复制排班
          </button>
        );
    }
  };

  return {
    copiedSchedules,
    canPaste,
    copiedDate,
    pastedDates,
    handleCopySchedule,
    handlePasteSchedule,
    handleClearCopy,
    renderCopyPasteButton,
  };
}
