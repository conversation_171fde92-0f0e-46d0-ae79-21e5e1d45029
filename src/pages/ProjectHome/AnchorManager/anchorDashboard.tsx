import BreadCrumbSimple from '@/components/ui/breadcrumbSimple';
import TimeFilter from '@/components/ui/timeFilter';
import useProjectId from '@/hooks/useProjectId';
import { GetAnchorList, GetProjectAccountUserOverviewList } from '@/services/anchor';
import { PlatForm } from '@/utils/platform';
import { getTimeByType } from '@/utils/time';
import { PageContainer, ProCard } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Flex, Select, Space } from 'antd';
import dayjs from 'dayjs';
import { useState } from 'react';
import AnchorDuration from './component/anchorDuration';
import AnchorLeads from './component/anchorLeads';
import AnchorTable from './component/anchorTable';
import AnchorHeatMap from './component/anchorHeatMap';
import LiveFunnelChart from './component/liveFunnelChart';
import { Tabs } from '@/components/ui/tabs';
import { GetAnchorRoleList } from '@/services/anchor-role';

export default function AnchorDashboard() {
  const projectId = useProjectId();
  const platform = PlatForm.Douyin;
  const [rangeTime, setRangeTime] = useState<(string | undefined)[]>(getTimeByType('today'));
  const [anchorIds, setAnchorIds] = useState<string[]>([]);
  const [accountIds, setAccountIds] = useState<string[]>([]);

  // 获取全部角色
  const { data: roleList } = useRequest(
    () =>
      GetAnchorRoleList({
        projectId,
        platform,
      }),
    {
      refreshDeps: [projectId, platform],
      cacheKey: `GetAnchorRoleList_${projectId}_${platform}`,
      cacheTime: 10 * 60 * 1000, // 10 分钟
    },
  );

  console.log('hjy===roleList', roleList);

  // 当前项目下所有抖音号
  const { data: accounts } = useRequest(
    () => GetProjectAccountUserOverviewList({ projectId, platform }),
    {
      refreshDeps: [projectId, platform],
      cacheKey: `GetProjectAccountUserOverviewList_${projectId || 'none'}_${platform}`,
      cacheTime: 10 * 60 * 1000, // 10 分钟
    },
  );

  // 主播列表
  const { data: anchorListData } = useRequest(() => GetAnchorList({ projectId, platform }), {
    cacheKey: `GetAnchorList_${projectId}_${platform}`,
    cacheTime: 10 * 60 * 1000, // 10 分钟
    refreshDeps: [projectId, platform],
  });

  return (
    <PageContainer
      title={<BreadCrumbSimple breadcrumbs={['主播管理', '主播数据概览']} />}
      extra={
        <Space>
          <Flex gap={10} style={{ color: '#979797', fontSize: '12px' }}>
            <span>最新数据更新到: {dayjs().subtract(1, 'day').format('YYYY-MM-DD')}</span>
          </Flex>
        </Space>
      }
    >
      <div className="rounded-lg bg-white px-4 py-2">
        <Space>
          <TimeFilter value={rangeTime} onChange={(value) => setRangeTime(value)} showToday />
          <Select
            showSearch
            allowClear
            mode="multiple"
            maxTagCount="responsive"
            placeholder="主播昵称"
            filterOption={(input, option) =>
              (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
            }
            options={anchorListData?.map((item) => ({
              label: item.name,
              value: item.id,
            }))}
            style={{ width: 200 }}
            onChange={(value) => setAnchorIds(value)}
            value={anchorIds}
          />
          <Select
            showSearch
            allowClear
            mode="multiple"
            maxTagCount="responsive"
            placeholder="抖音号"
            filterOption={(input, option) =>
              (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
            }
            options={accounts?.map((item) => ({
              label: item.nickname,
              value: item.accountId,
            }))}
            style={{ width: 200 }}
            onChange={(value) => setAccountIds(value)}
            value={accountIds}
          />
        </Space>

        <Tabs value></Tabs>
      </div>

      <div className="mt-4 flex w-full flex-wrap gap-4">
        <AnchorLeads
          anchorIds={anchorIds}
          accountIds={accountIds}
          projectId={projectId}
          rangeTime={rangeTime}
          platform={platform}
        />
        <AnchorDuration
          anchorIds={anchorIds}
          accountIds={accountIds}
          projectId={projectId}
          rangeTime={rangeTime}
          platform={platform}
        />
        <AnchorHeatMap />
        <LiveFunnelChart />
      </div>

      <ProCard className="mt-4" bodyStyle={{ paddingTop: '0px' }}>
        <AnchorTable
          anchorIds={anchorIds}
          accountIds={accountIds}
          projectId={projectId}
          rangeTime={rangeTime}
          platform={platform}
        />
      </ProCard>
    </PageContainer>
  );
}
