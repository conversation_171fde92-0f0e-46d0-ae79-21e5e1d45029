import defaultAvatar from '@/assets/default-avatar.png';
import ExportButton from '@/components/exportButton';
import BreadCrumbSimple from '@/components/ui/breadcrumbSimple';
import { usePollingExport } from '@/hooks/usePollingExport';
import useProjectId from '@/hooks/useProjectId';
import { AnchorVO, DeleteAnchor, ExportAnchorPage, GetAnchorPage } from '@/services/anchor';
import { proTableRequestAdapter } from '@/utils';
import { STICKY_OFFSETHEADER } from '@/utils/common';
import { PlatForm } from '@/utils/platform';
import { proTableOptionsConfig, proTablePaginationConfig } from '@/utils/proTableConfig';
import { PlusOutlined } from '@ant-design/icons';
import { ActionType, PageContainer, ProColumns, ProTable } from '@ant-design/pro-components';
import { Avatar, Button, Flex, message, Modal, Popconfirm, Space } from 'antd';
import dayjs from 'dayjs';
import { useRef, useState } from 'react';
import AnchorDetail from './component/anchorDetail';
import AnchorForm from './component/anchorForm';

const AnchorList = () => {
  const projectId = useProjectId();
  const platform = PlatForm.Douyin;
  const [modalVisible, setModalVisible] = useState(false);
  const [modalType, setModalType] = useState<'add' | 'edit'>('add');
  const [editRecord, setEditRecord] = useState<AnchorVO>();
  const actionRef = useRef<ActionType>();
  const { pollingExport, percent, pollingLoading } = usePollingExport(
    `主播列表${dayjs().format('YYYYMMDD HH-mm-ss')}`,
  );
  const [detailDrawerVisible, setDetailDrawerVisible] = useState(false);
  const [currentAnchorId, setCurrentAnchorId] = useState<number>();

  const handleEdit = (record: AnchorVO) => {
    setModalType('edit');
    setModalVisible(true);
    setEditRecord(record);
  };

  const handleDelete = async (record: AnchorVO) => {
    if (!record.id) return;
    await DeleteAnchor({ projectId, platform: PlatForm.Douyin }, { id: record.id });
    message.success('删除成功');
    actionRef.current?.reload();
  };

  const handleAdd = () => {
    setModalType('add');
    setModalVisible(true);
  };

  const handleExport = async () => {
    const res = await ExportAnchorPage({ projectId, platform: PlatForm.Douyin });
    if (res.data) {
      pollingExport({ taskId: res.data });
    } else {
      message.error('导出失败');
    }
  };

  const showDetailDrawer = (anchorId: number) => {
    setCurrentAnchorId(anchorId);
    setDetailDrawerVisible(true);
  };

  const columns: ProColumns<AnchorVO>[] = [
    {
      title: '姓名',
      dataIndex: 'name',
      width: 120,
      hideInSearch: true,
      render: (_, record) => (
        <Flex align="center" gap={10}>
          <Avatar src={record.avatarUrl || defaultAvatar} size={40} />
          {record.name}
        </Flex>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 180,
      hideInSearch: true,
      render: (_, record) =>
        record.createTime ? dayjs(record.createTime).format('YYYY-MM-DD HH:mm:ss') : '-',
    },
    {
      title: '操作',
      dataIndex: 'option',
      width: 100,
      align: 'left',
      valueType: 'option',
      render: (_, record) => (
        <Space>
          <Button
            key="detail"
            type="link"
            onClick={() => {
              if (!record.id) return;
              showDetailDrawer(record.id);
            }}
          >
            查看详情
          </Button>
          <Button key="edit" type="link" onClick={() => handleEdit(record)}>
            编辑
          </Button>
          <Popconfirm key="delete" title="是否确认删除" onConfirm={() => handleDelete(record)}>
            <Button type="link" danger>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <>
      <PageContainer title={<BreadCrumbSimple breadcrumbs={['主播管理', '主播列表']} />}>
        <ProTable
          columns={columns}
          rowKey="id"
          headerTitle="主播列表"
          params={{
            projectId,
            platform: PlatForm.Douyin,
          }}
          sticky={{ offsetHeader: STICKY_OFFSETHEADER }}
          tableClassName="custom-table"
          search={false}
          options={{ ...proTableOptionsConfig }}
          pagination={{ ...proTablePaginationConfig, defaultPageSize: 10 }}
          actionRef={actionRef}
          request={(params, sorter, filter) => {
            return proTableRequestAdapter(params, sorter, filter, GetAnchorPage);
          }}
          toolBarRender={() => [
            <Button key="add" type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
              添加主播
            </Button>,
            <ExportButton
              exportFn={() => handleExport()}
              loading={pollingLoading}
              percent={percent}
              key="export"
            />,
          ]}
        />
        <Modal
          open={modalVisible}
          title={modalType === 'add' ? '添加主播' : '编辑主播'}
          onCancel={() => setModalVisible(false)}
          footer={null}
          destroyOnHidden
        >
          <AnchorForm
            type={modalType}
            onSuccess={() => {
              actionRef.current?.reload();
              setModalVisible(false);
            }}
            projectId={String(projectId)}
            editRecord={editRecord}
          />
        </Modal>
      </PageContainer>

      {/* 主播详情抽屉 */}
      <AnchorDetail
        projectId={projectId}
        platform={platform}
        currentAnchorId={currentAnchorId}
        detailDrawerVisible={detailDrawerVisible}
        onClose={() => setDetailDrawerVisible(false)}
      />
    </>
  );
};

export default AnchorList;
