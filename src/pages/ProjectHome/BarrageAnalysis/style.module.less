.timeRangeRadioGroup {
  :global(.ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled)) {
    background: #edf3ff;
  }
}

.barrageOverviewChartWrap {
  background-color: #f9fcff;
  border-radius: 4px;
  padding: 21px 27px 0px;

  .chartTitle {
    position: relative;
    line-height: 22px;
    font-size: 16px;
    color: #0e1015;

    .chartDesc {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;

      text-align: center;

      .chartDescNum {
        color: #1e5eff;
      }
    }
  }

  &.dark {
    background-color: #272c36;

    .chartTitle {
      color: rgba(255, 255, 255, 0.8);
    }
  }
}

.barrageList {
  margin-top: 14px;

  .barrageItem {
    padding: 0 8px;
    height: 32px;
    border-radius: 4px;
    background-color: #1e5eff0d;

    .barrageContext {
      color: #0e1015;
      font-size: 14px;
    }

    .barrageCount {
      height: 18px;
      padding: 0 6px;
      border-radius: 999px;

      font-size: 12px;
      line-height: 18px;
      color: #1e5eff;
    }
  }

  &.dark {
    .barrageItem {
      background-color: #272c36;

      .barrageContext {
        color: rgba(255, 255, 255, 0.8);
      }

      .barrageCount {
        background-color: #2d437b;

        color: rgba(255, 255, 255, 0.8);
      }
    }
  }
}

.barrageAiChatModalWrap {
  padding-bottom: 0 !important;
  background-color: #fff !important;

  :global(.ant-modal-close-x svg) {
    color: #a1a1a1 !important;
  }

  .barrageAiChatModalHeader {
    border-bottom: 1px solid #f1f1f1 !important;
    background-color: #fff !important;

    :global(.ant-modal-title) {
      color: rgba(0, 0, 0, 0.88) !important;
    }
  }
}

.barrageAiChatModalBody {
  height: 620px;
  padding-top: 8px;

  .barrageContentCard {
    flex-shrink: 0;

    border-radius: 4px;
    box-shadow: 2px 4px 12px rgba(0, 0, 0, 0.08);
    padding: 0 16px;

    .barrageContentCardHeader {
      height: 32px;

      .barrageContentCardTitle {
        font-size: 14px;
        color: #000;
      }

      .barrageContentExpandBtn {
        padding: 0;
        color: #1e5eff;
      }
    }

    .barrageContent {
      padding: 6px 0 12px;
      max-height: 80px;
      overflow: auto;

      border-top: 1px solid #f1f1f1;

      .barrageItem {
        height: 24px;
        border-radius: 4px;
        padding: 4px 8px;
        background-color: #f9fcff;

        font-size: 12px;
        color: #64666b;
        line-height: 16px;
      }
    }
  }

  .aiChatWrap {
    flex: 1;

    margin-top: 15px;

    overflow: auto;

    &::-webkit-scrollbar {
      width: 0;
    }

    .sayContent {
      max-width: 668px;
      padding: 8px 10px;
      margin-top: 20px;
      border-radius: 4px;

      font-size: 14px;
      line-height: 22px;
      color: #0e1015;

      &.aiSayContent {
        align-self: flex-start;

        background-color: #fafafa;
      }

      &.userSayContent {
        align-self: flex-end;

        background-color: #eaf0fe;
      }
    }
  }

  .bottomInputBar {
    flex-shrink: 0;

    position: relative;

    height: 48px;
    padding: 9px 0;
    box-sizing: border-box;

    .sayInputWrap {
      position: relative;

      width: 100%;

      .sayInput {
        width: 100%;
        height: 30px;
        padding-right: 50px;
      }

      .sendMsgBtn {
        position: absolute;
        top: 50%;
        right: 12px;

        transform: translateY(-50%);

        width: 18px;
        height: 18px;
        cursor: pointer;
      }
    }
  }
}

.barrageListFiltersDark {
  :global(.ant-select-outlined:not(.ant-select-customize-input) .ant-select-selector) {
    background-color: #191b1f;

    color: rgba(255, 255, 255, 0.5);
  }
}

.barrageListPagination {
  display: flex;
  justify-content: end;

  &:global(.ant-pagination) {
    :global(.ant-pagination-options) {
      margin-left: 15px;
    }

    :global(.ant-select-outlined:not(.ant-select-customize-input) .ant-select-selector) {
      background-color: #f5f6fa;
      border-color: #ebedf2;
    }

    :global(.ant-pagination-item-active) {
      width: 24px;
      height: 24px;
      line-height: 24px;
      background-color: #1e5eff;
      margin-right: 8px;
      border: none;

      a {
        color: #fff;
      }
    }

    &:global(
        .ant-pagination.ant-pagination-mini:not(.ant-pagination-disabled)
          .ant-pagination-item:not(.ant-pagination-item-active)
      ) {
      background-color: #f5f6fa;
      border-color: #ebedf2;
      margin-right: 8px;
    }

    :global(.ant-pagination-prev) {
      margin-left: 16px;
      margin-right: 8px;
    }
  }

  &.dark:global(.ant-pagination) {
    &:global(
        .ant-pagination.ant-pagination-mini:not(.ant-pagination-disabled)
          .ant-pagination-item:not(.ant-pagination-item-active)
      ) {
      background-color: #272c36;
      border-color: #343945;
    }

    :global(.ant-select-outlined:not(.ant-select-customize-input) .ant-select-selector) {
      background-color: #272c36;
      border-color: #343945;
    }
  }
}
