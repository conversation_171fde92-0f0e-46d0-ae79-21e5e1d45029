import { useState, useMemo, useEffect, useCallback } from 'react';
import { Row, Col } from 'antd';
import ReactECharts, { EChartsOption } from 'echarts-for-react';
import { echartDefaultColor } from '@/utils/commonStyle';
import {
  GetBarrageAnalysisOverview,
  BarrageAnalysisOverviewResult,
} from '@/services/intelligent-analysis';

import styles from '../style.module.less';

const CHART_HEIGHT = 467;

interface BarrageOverviewChartsProps {
  darkMode?: boolean;
  rangeTime?: string[];
  teamCodeList?: string[];
  liveRoomId?: string;
  projectId?: string;
  onBarrageTotalChange?: (total: number | null) => void;
  onBarrageGrowthRateChange?: (rate: number | null) => void;
}

const BarrageOverviewCharts = (props: BarrageOverviewChartsProps) => {
  const {
    darkMode,
    rangeTime,
    teamCodeList,
    liveRoomId,
    projectId,
    onBarrageTotalChange,
    onBarrageGrowthRateChange,
  } = props;
  const [barrageOverviewData, setBarrageOverviewData] =
    useState<BarrageAnalysisOverviewResult | null>(null);
  const [loadingData, setLoadingData] = useState<boolean>(false);
  const [carTypeSelected, setCarTypeSelected] = useState<Record<string, boolean>>({ 其他: false });
  const [barrageTypeSelected, setBarrageTypeSelected] = useState<Record<string, boolean>>({
    其他: false,
  });

  // 弹幕词性图表数据
  const barrageChartData = useMemo(() => {
    if (barrageOverviewData && barrageOverviewData.barragDistribution) {
      return barrageOverviewData.barragDistribution.map((item) => {
        return {
          name: item.emotion,
          value: item.total,
        };
      });
    } else {
      return [];
    }
  }, [barrageOverviewData]);

  // 车型图表数据
  const carsTypeChartData = useMemo(() => {
    if (barrageOverviewData && barrageOverviewData.carDistribution) {
      const carDistribution = barrageOverviewData.carDistribution;

      return carDistribution.map((item) => {
        return {
          name: item.carInfo === '-' ? '其他' : item.carInfo,
          value: item.total,
        };
      });
    } else {
      return [];
    }
  }, [barrageOverviewData, carTypeSelected]);

  // 弹幕类型分布数据
  const barrageTypeChartData = useMemo(() => {
    if (barrageOverviewData && barrageOverviewData.barrageTypeDistribution) {
      return barrageOverviewData.barrageTypeDistribution.map((item) => {
        return {
          name: item.barrageType === '-' ? '其他' : item.barrageType,
          value: item.total,
        };
      });
    } else {
      return [];
    }
  }, [barrageOverviewData, barrageTypeSelected]);

  const barrageNumFilterOther = useMemo(() => {
    let totalNum = 0;
    let exceptOtherNum = 0;
    carsTypeChartData.forEach((item) => {
      if (item.name !== '其他') {
        exceptOtherNum += item.value;
      }

      totalNum += item.value;
    });

    return {
      num: exceptOtherNum,
      percent: totalNum === 0 ? 0 : Math.floor((exceptOtherNum / totalNum) * 100),
    };
  }, [carsTypeChartData]);

  useEffect(() => {
    if (projectId) {
      setLoadingData(true);
      GetBarrageAnalysisOverview({
        projectId,
        startTime: rangeTime ? rangeTime[0] : undefined,
        endTime: rangeTime ? rangeTime[1] : undefined,
        teamCodeList,
        liveRoomId,
      })
        .then((res) => {
          if (res.code === 0) {
            setBarrageOverviewData(res.data || null);
            onBarrageTotalChange && onBarrageTotalChange(res.data?.barrageTotal || null);
            onBarrageGrowthRateChange?.(res.data?.growthRate || null);
          } else {
            setBarrageOverviewData(null);
            onBarrageTotalChange && onBarrageTotalChange(0);
            onBarrageGrowthRateChange?.(res.data?.growthRate || null);
          }
        })
        .finally(() => {
          setLoadingData(false);
          setCarTypeSelected({ 其他: false });
        });
    }
  }, [rangeTime, teamCodeList, liveRoomId, projectId]);

  const onCarTypeLegendselectchanged = useCallback((e: any) => {
    if (e.selected) {
      setCarTypeSelected({ ...e.selected });
    }
  }, []);

  const onBarrageTypeLegendselectchanged = useCallback((e: any) => {
    if (e.selected) {
      setBarrageTypeSelected({ ...e.selected });
    }
  }, []);

  const commonOption = useMemo(() => {
    return {
      color: echartDefaultColor,
      tooltip: {
        trigger: 'item',
      },
      title: {
        textStyle: {
          color: 'grey',
          fontSize: 20,
        },
        text: '暂无数据',
        left: 'center',
        top: '33%',
      },
    };
  }, []);

  const commonLegend = useMemo(() => {
    return {
      icon: 'circle',
      top: 354,
      itemWidth: 12,
      itemHeight: 12,
      padding: 0,
      textStyle: {
        rich: {
          dataName: {
            color: darkMode ? 'rgba(255,255,255,0.8)' : '#0E1015',
            // fontSize: 14,
            // lineHeight: 20,
            padding: [0, 0, 0, 8],
          },
          dataValue: {
            color: darkMode ? 'rgba(255,255,255,0.5)' : '#95979C',
            // fontSize: 14,
            // lineHeight: 20,
            padding: [0, 0, 0, 8],
          },
        },
      },
      itemGap: 20,
    };
  }, []);

  const commonPieSerie = useMemo(() => {
    return {
      type: 'pie',
      center: ['50%', '35%'], // 设置饼图的圆心
      stillShowZeroSum: false,
      radius: ['70', '110'],
      // avoidLabelOverlap: false,
      label: {
        formatter: '{dataName|{b}} {dataValue|{d}%}',
        rich: {
          dataName: {
            color: darkMode ? 'rgba(255,255,255,0.8)' : '#0E1015',
            padding: [0, 0, 0, 8],
          },
          dataValue: {
            color: darkMode ? 'rgba(255,255,255,0.5)' : '#95979C',
            padding: [0, 0, 0, 8],
          },
        },
      },
      labelLine: {
        normal: {
          show: true,
          legend: 8, //第一条折现
          legend2: 15, //第二条折现
          // lineStyle: {
          //   color: '#166AC1', //折现颜色
          // },
        },
      },
    };
  }, []);

  const barrageChartOption = useMemo(() => {
    return {
      ...commonOption,
      title: {
        ...commonOption.title,
        show: barrageChartData.length === 0,
      },
      series: [
        {
          ...commonPieSerie,
          data: barrageChartData,
        },
      ],
      legend: {
        ...commonLegend,
        formatter: function (name: string) {
          const value = barrageChartData.find((item) => item.name === name)?.value;
          return `{dataName|${name}}{dataValue|${value || 0}次}`;
        },
      },
    };
  }, [barrageChartData]);

  const carsTypeChartOption: EChartsOption = useMemo(() => {
    return {
      ...commonOption,
      title: {
        ...commonOption.title,
        show: carsTypeChartData.length === 0,
        top: '45%',
      },
      series: [
        {
          ...commonPieSerie,
          center: [carsTypeChartData.length === 0 ? '50%' : '25%', '50%'], // 设置饼图的圆心
          data: carsTypeChartData,
        },
      ],
      legend: {
        ...commonLegend,
        left: '50%',
        top: 'center',
        formatter: function (name: string) {
          const value = carsTypeChartData.find((item) => item.name === name)?.value;
          return `{dataName|${name}}{dataValue|${value || 0}次}`;
        },
        selected: carTypeSelected,
      },
    };
  }, [carsTypeChartData, carTypeSelected]);

  const barrageTypeChartOption = useMemo(() => {
    return {
      ...commonOption,
      title: {
        ...commonOption.title,
        show: barrageTypeChartData.length === 0,
      },
      series: [
        {
          ...commonPieSerie,
          data: barrageTypeChartData,
        },
      ],
      legend: {
        ...commonLegend,
        formatter: function (name: string) {
          const value = barrageTypeChartData.find((item) => item.name === name)?.value;
          return `{dataName|${name}}{dataValue|${value || 0}次}`;
        },
        selected: barrageTypeSelected,
      },
    };
  }, [barrageTypeChartData, barrageTypeSelected]);

  return (
    <Row gutter={[{ xs: 28, sm: 28, md: 28, lg: 32 }, 16]}>
      <Col span={12}>
        <div
          className={
            darkMode
              ? [styles.barrageOverviewChartWrap, styles.dark].join(' ')
              : styles.barrageOverviewChartWrap
          }
        >
          <div className={styles.chartTitle}>弹幕词性分布</div>
          <div style={{ height: CHART_HEIGHT }}>
            <ReactECharts
              style={{ width: '100%', height: '100%' }}
              option={barrageChartOption}
              notMerge={true}
              showLoading={loadingData}
            />
          </div>
        </div>
      </Col>

      <Col span={12}>
        <div
          className={
            darkMode
              ? [styles.barrageOverviewChartWrap, styles.dark].join(' ')
              : styles.barrageOverviewChartWrap
          }
        >
          <div className={styles.chartTitle}>弹幕类型分布</div>
          <div style={{ height: CHART_HEIGHT }}>
            <ReactECharts
              style={{ width: '100%', height: '100%' }}
              option={barrageTypeChartOption}
              notMerge={true}
              showLoading={loadingData}
              onEvents={{
                legendselectchanged: onBarrageTypeLegendselectchanged,
              }}
            />
          </div>
        </div>
      </Col>

      <Col span={24}>
        <div
          className={
            darkMode
              ? [styles.barrageOverviewChartWrap, styles.dark].join(' ')
              : styles.barrageOverviewChartWrap
          }
        >
          <div className={styles.chartTitle}>
            车型分布
            <div className={styles.chartDesc}>
              共<span className={styles.chartDescNum}>{barrageNumFilterOther.num}</span>
              条弹幕提及车型，约占总弹幕
              <span className={styles.chartDescNum}>{barrageNumFilterOther.percent}</span>%
            </div>
          </div>
          <div style={{ height: CHART_HEIGHT - 100 }}>
            <ReactECharts
              style={{ width: '100%', height: '100%' }}
              option={carsTypeChartOption}
              notMerge={true}
              showLoading={loadingData}
              onEvents={{
                legendselectchanged: onCarTypeLegendselectchanged,
              }}
            />
          </div>
        </div>
      </Col>
    </Row>
  );
};

export default BarrageOverviewCharts;
