import { Space } from 'antd';
import TimeFilterCustomTypeText from '@/components/timeFilterCustomTypeText';
import { DateType } from '@/services/business';
import DynamicTree from '@/components/dynamicTree';
import styles from '../style.module.less';

interface BarrageAnalysisDetailFiltersProps {
  radioValue: DateType;
  setRadioValue: React.Dispatch<React.SetStateAction<DateType>>;
  rangeTime: string[];
  setRangeTime: React.Dispatch<React.SetStateAction<string[]>>;
  treeValue: string[] | undefined;
  setTreeValue: React.Dispatch<React.SetStateAction<string[] | undefined>>;
  dateOptions?: [DateType, string][];
}

const BarrageAnalysisDetailFilters = (props: BarrageAnalysisDetailFiltersProps) => {
  const {
    radioValue,
    setRadioValue,
    rangeTime,
    setRangeTime,
    treeValue,
    setTreeValue,
    dateOptions = [
      [DateType.Today, '当天'],
      [DateType.Yesterday, '近一天'],
      [DateType.LastWeek, '近一周'],
      [DateType.LastMonth, '近一个月'],
    ],
  } = props;
  return (
    <Space size={16}>
      <TimeFilterCustomTypeText
        radioValue={radioValue}
        setRadioValue={setRadioValue}
        rangeTime={rangeTime}
        setRangeTime={setRangeTime}
        style={{ marginLeft: 'auto' }}
        dateOptions={dateOptions}
        radioGroupClassName={styles.timeRangeRadioGroup}
      />
      <DynamicTree value={treeValue} setValue={setTreeValue} style={{ width: '300px' }} />
    </Space>
  );
};

export default BarrageAnalysisDetailFilters;
