import { useEffect, useState, useMemo } from 'react';
import {
  Pagination,
  Flex,
  Space,
  Select,
  Button,
  Spin,
  Empty,
  Input,
  message,
  MenuProps,
  Dropdown,
} from 'antd';
import { CloseOutlined, DownOutlined, LoadingOutlined } from '@ant-design/icons';
import BarrageAIChatModal from './barrageAIChatModal';
import {
  GetBarrageAnalysisCarInfo,
  GetBarragesPage,
  BarrageEmotion,
  BarrageItem,
  GetBarrageAnalysisType,
  exportBarrage,
} from '@/services/intelligent-analysis';
import { useRequest } from '@umijs/max';
import styles from '../style.module.less';
import { PagintaionData } from '@/services/common';
import { customPaginationRender } from '@/pages/ProjectHome/style/index';
import { usePollingExport } from '@/hooks/usePollingExport';
import { Download } from 'lucide-react';

interface BarrageListProps {
  darkMode?: boolean;
  rangeTime?: string[];
  teamCodeList?: string[];
  liveRoomId?: string;
  projectId?: string;
}

const BarrageList = (props: BarrageListProps) => {
  const {
    darkMode,
    rangeTime: rangeTimeProp,
    teamCodeList: teamCodeListProp,
    liveRoomId: liveRoomIdProp,
    projectId,
  } = props;

  // 这里定义多一份state是因为避免传进来的参数改变了 初始页面时触发多一次请求
  const [rangeTime, setRangeTime] = useState<string[] | undefined>(rangeTimeProp);
  const [teamCodeList, setTeamCodeList] = useState<string[] | undefined>(teamCodeListProp);
  const [liveRoomId, setLiveRoomId] = useState<string | undefined>(liveRoomIdProp);
  const [emotion, setEmotion] = useState<BarrageEmotion | 'all'>('积极弹幕');
  const [carType, setCarType] = useState<string>('all');
  const [barrageType, setBarrageType] = useState<string>('all');
  const [openAiChatModal, setOpenAiChaModal] = useState<boolean>(false);
  const [barrageSearchKey, setBarrageSearchKey] = useState<string | undefined>();
  const [pageSize, setPageSize] = useState<number>(100); // 每页条数
  const [currentPage, setCurrentPage] = useState<number>(1); // 当前页码
  const [barragePageData, setBarragePageData] = useState<PagintaionData<BarrageItem> | null>(null); // 弹幕列表数据
  const [loadingBarrageData, setLoadingBarrageData] = useState<boolean>(false);
  const { pollingExport, pollingLoading } = usePollingExport();

  useEffect(() => {
    if (rangeTimeProp !== rangeTime) {
      setRangeTime(rangeTimeProp);
      setCurrentPage(1);
    }

    if (teamCodeListProp !== teamCodeList) {
      setTeamCodeList(teamCodeListProp);
      setCurrentPage(1);
    }

    if (liveRoomIdProp !== liveRoomId) {
      setLiveRoomId(liveRoomIdProp);
      setCurrentPage(1);
    }
  }, [rangeTimeProp, teamCodeListProp, liveRoomIdProp]);

  useEffect(() => {
    if (projectId) {
      setLoadingBarrageData(true);
      GetBarragesPage({
        size: pageSize,
        page: currentPage,
        projectId: projectId!,
        startTime: rangeTime ? rangeTime[0] : undefined,
        endTime: rangeTime ? rangeTime[1] : undefined,
        teamCodeList,
        liveRoomId,
        emotion: emotion === 'all' ? undefined : emotion,
        carInfo: carType !== 'all' ? carType : undefined,
        barrageType: barrageType !== 'all' ? barrageType : undefined,
        barrage: barrageSearchKey,
      })
        .then((res) => {
          if (res.code === 0) {
            setBarragePageData(res.data || null);
          } else {
            setBarragePageData(null);
          }
        })
        .finally(() => {
          setLoadingBarrageData(false);
        });
    }
  }, [
    barrageSearchKey,
    pageSize,
    currentPage,
    projectId,
    rangeTime,
    teamCodeList,
    liveRoomId,
    emotion,
    carType,
    barrageType,
  ]);

  const { data: carTypesData, loading: loadingCarType } = useRequest(
    async () => {
      return GetBarrageAnalysisCarInfo({
        projectId: projectId!,
        liveRoomId,
        startTime: rangeTime ? rangeTime[0] : undefined,
        endTime: rangeTime ? rangeTime[1] : undefined,
        teamCodeList,
      });
    },
    {
      ready: !!projectId,
      refreshDeps: [projectId, liveRoomId, rangeTime, teamCodeList],
    },
  );

  const { data: barrageTypesData, loading: loadingBarrageTypes } = useRequest(
    async () => {
      return GetBarrageAnalysisType({
        projectId: projectId!,
        liveRoomId,
        startTime: rangeTime ? rangeTime[0] : undefined,
        endTime: rangeTime ? rangeTime[1] : undefined,
        teamCodeList,
      });
    },
    {
      ready: !!projectId,
      refreshDeps: [projectId, liveRoomId, rangeTime, teamCodeList],
    },
  );

  const barrageList = barragePageData?.items;

  const barrageContentList = useMemo(() => {
    return barrageList && barrageList.map((item) => item.barrage);
  }, [barrageList]);

  const emotionOptions = [
    { value: 'all', label: '全部' },
    { value: '积极弹幕', label: '积极弹幕' },
    { value: '中性弹幕', label: '中性弹幕' },
    { value: '消极弹幕', label: '消极弹幕' },
  ];

  const carTypeOptions = [
    {
      value: 'all',
      label: '全部车型',
    },
    ...(carTypesData
      ? carTypesData.map((item) => {
          return {
            value: item,
            label: item === '-' ? '其他' : item,
          };
        })
      : []),
  ];

  const barrageTypeOptions = [
    {
      value: 'all',
      label: '全部',
    },
    ...(barrageTypesData
      ? barrageTypesData.map((item) => {
          return {
            value: item,
            label: item === '-' ? '其他' : item,
          };
        })
      : []),
  ];

  const items: MenuProps['items'] = [
    {
      label: '导出弹幕详情数据',
      key: '1',
      disabled: !barragePageData?.items?.length || pollingLoading,
      async onClick() {
        const res = await exportBarrage({
          projectId: projectId!,
          startTime: rangeTime ? rangeTime[0] : undefined,
          endTime: rangeTime ? rangeTime[1] : undefined,
          teamCodeList,
          liveRoomId,
          emotion: emotion === 'all' ? undefined : emotion,
          carInfo: carType !== 'all' ? carType : undefined,
          barrageType: barrageType !== 'all' ? barrageType : undefined,
          barrage: barrageSearchKey,
          isBarrageDetail: true,
        });
        if (res.data) {
          pollingExport({ taskId: res.data });
        } else {
          message.error('导出失败');
        }
      },
    },
    {
      label: '导出弹幕汇总数据',
      key: '2',
      disabled: !barragePageData?.items?.length || pollingLoading,
      async onClick() {
        const res = await exportBarrage({
          projectId: projectId!,
          startTime: rangeTime ? rangeTime[0] : undefined,
          endTime: rangeTime ? rangeTime[1] : undefined,
          teamCodeList,
          liveRoomId,
          emotion: emotion === 'all' ? undefined : emotion,
          carInfo: carType !== 'all' ? carType : undefined,
          barrageType: barrageType !== 'all' ? barrageType : undefined,
          barrage: barrageSearchKey,
        });
        if (res.data) {
          pollingExport({ taskId: res.data });
        } else {
          message.error('导出失败');
        }
      },
    },
  ];

  return (
    <div>
      <Flex justify="space-between">
        <Space className={darkMode ? styles.barrageListFiltersDark : undefined}>
          <Select
            style={{ width: 166 }}
            options={emotionOptions}
            defaultValue={emotion}
            onChange={(val) => {
              setCurrentPage(1);
              setEmotion(val);
            }}
          />
          <Select
            style={{ width: 166 }}
            options={carTypeOptions}
            defaultValue={carType}
            onChange={(val) => {
              setCurrentPage(1);
              setCarType(val);
            }}
            loading={loadingCarType}
          />
          <Select
            style={{ width: 166 }}
            options={barrageTypeOptions}
            defaultValue={barrageType}
            onChange={(val) => {
              setCurrentPage(1);
              setBarrageType(val);
            }}
            loading={loadingBarrageTypes}
          />
          <Input.Search
            placeholder="弹幕内容"
            style={{ width: 166 }}
            onSearch={(val) => {
              setBarrageSearchKey(val || undefined);
            }}
          />
        </Space>
        <Space>
          <Dropdown menu={{ items }} placement="bottomRight" trigger={['click', 'hover']}>
            <Dropdown.Button
              type="default"
              menu={{ items: [] }}
              trigger={[]}
              overlayStyle={{ width: 100 }}
              icon={!pollingLoading ? <DownOutlined /> : <LoadingOutlined />}
            >
              <Download size={16} strokeWidth={1.5} />
              <span>导出</span>
            </Dropdown.Button>
          </Dropdown>
          <Button
            disabled={!barrageContentList || !barrageContentList.length}
            type="primary"
            style={{ width: 100 }}
            onClick={() => setOpenAiChaModal(true)}
          >
            AI总结
          </Button>
        </Space>
      </Flex>
      <Spin spinning={loadingBarrageData}>
        {barrageList && barrageList.length ? (
          <Flex
            className={darkMode ? [styles.barrageList, styles.dark].join(' ') : styles.barrageList}
            gap={8}
            style={{ flexWrap: 'wrap' }}
          >
            {barrageList.map((item, index) => {
              return (
                <Flex align="center" gap={2} className={styles.barrageItem} key={index}>
                  <div className={styles.barrageContext}>{item.barrage}</div>
                  <Flex align="center" gap={2} className={styles.barrageCount}>
                    <CloseOutlined style={{ fontSize: 8 }} />
                    {item.count}
                  </Flex>
                </Flex>
              );
            })}
          </Flex>
        ) : (
          <div style={{ padding: '30px' }}>
            <Empty />
          </div>
        )}
        <Pagination
          className={
            darkMode ? [styles.barrageListPagination, styles.dark].join(' ') : 'custom-pagination'
          }
          size={darkMode ? 'small' : undefined}
          style={{ padding: '15px 0' }}
          showSizeChanger={true}
          pageSizeOptions={[100, 300, 500]}
          current={currentPage}
          defaultPageSize={pageSize}
          total={barragePageData?.total || 0}
          itemRender={darkMode ? undefined : customPaginationRender}
          showTotal={
            darkMode ? undefined : (total, range) => `${range[0]}-${range[1]} 共 ${total} 条`
          }
          onChange={(page, pageSize) => {
            setPageSize(pageSize);
            setCurrentPage(page);
          }}
        />
      </Spin>

      <BarrageAIChatModal
        open={openAiChatModal}
        setOpen={setOpenAiChaModal}
        barrageDataParams={{
          page: 1,
          size: 1000,
          projectId: projectId,
          startTime: rangeTime ? rangeTime[0] : undefined,
          endTime: rangeTime ? rangeTime[1] : undefined,
          emotion: emotion === 'all' ? undefined : emotion,
          teamCodeList: teamCodeList,
          carInfo: carType !== 'all' ? carType : undefined,
          barrageType: barrageType !== 'all' ? barrageType : undefined,
          liveRoomId: liveRoomId,
          barrage: barrageSearchKey,
        }}
      />
    </div>
  );
};

export default BarrageList;
