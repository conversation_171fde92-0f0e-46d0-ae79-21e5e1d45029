import { useState, useEffect, useRef } from 'react';
import { Spin, Flex, Modal, Button } from 'antd';
import { UpOutlined, DownOutlined } from '@ant-design/icons';

import { BarrageEmotion, GetBarragesPage } from '@/services/intelligent-analysis';
import qs from 'qs';
import { isEqual } from 'lodash-es';

import styles from '../style.module.less';

const isDev = process.env.NODE_ENV === 'development';
const isDevDomain = window.location.host.includes('dev');

interface BarrageDataParams {
  page: number;
  size: number;
  projectId?: string;
  startTime?: string;
  endTime?: string;
  teamCodeList?: string[];
  emotion?: BarrageEmotion;
  carInfo?: string;
  barrageType?: string;
  liveRoomId?: string;
  barrage?: string;
}

interface BarrageAIChatModalProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  barrageDataParams: BarrageDataParams;
}

const BarrageAIChatModal = (props: BarrageAIChatModalProps) => {
  const { open, setOpen, barrageDataParams } = props;

  const [barrageList, setBarrageList] = useState<string[]>([]);
  const [loadingBarrageData, setLoadingBarrageData] = useState<boolean>(false);
  const [showBarrageContent, setShowBarrageContent] = useState<boolean>(true);
  const [difyIframeLink, setDifyIframeLink] = useState<string | null>(null);

  const preParams = useRef<BarrageDataParams>();

  const fetchBarrageList = () => {
    if (barrageDataParams.projectId) {
      setLoadingBarrageData(true);
      GetBarragesPage({
        ...barrageDataParams,
        projectId: barrageDataParams.projectId,
      })
        .then((res) => {
          if (res.code === 0 && res.data && res.data.items) {
            const barrageList = res.data.items.map((item) => item.barrage);
            setBarrageList(barrageList);
            preParams.current = barrageDataParams;
          }
        })
        .finally(() => {
          setLoadingBarrageData(false);
        });
    }
  };

  const getDifyLink = () => {
    const difyPrams = {
      ...barrageDataParams,
      teamCodeList: Array.isArray(barrageDataParams.teamCodeList)
        ? barrageDataParams.teamCodeList.join(',')
        : barrageDataParams.teamCodeList,
      cookie: document.cookie,
      isDev: (isDev || isDevDomain).toString(),
    };

    const link = `https://dify.xiaofeilun.cn/chatbot/7lVJrfIdZaCSLRBq?${qs.stringify(difyPrams)}`;

    setDifyIframeLink(link);
  };

  useEffect(() => {
    if (open) {
      if (barrageDataParams.projectId) {
        if (!preParams.current || !isEqual(preParams.current, barrageDataParams)) {
          fetchBarrageList();

          getDifyLink();
        }
      }
    }
  }, [open]);

  const onBarrageContentShowToggle = () => {
    setShowBarrageContent(!showBarrageContent);
  };

  return (
    <Modal
      open={open}
      onCancel={() => setOpen(false)}
      title="AI对话"
      footer={null}
      width={754}
      classNames={{
        content: styles.barrageAiChatModalWrap,
        header: styles.barrageAiChatModalHeader,
      }}
    >
      <Flex className={styles.barrageAiChatModalBody} vertical={true}>
        <div className={styles.barrageContentCard}>
          <Flex justify="space-between" align="center" className={styles.barrageContentCardHeader}>
            <div className={styles.barrageContentCardTitle}>弹幕内容</div>
            <Button
              type="link"
              className={styles.barrageContentExpandBtn}
              onClick={onBarrageContentShowToggle}
            >
              {showBarrageContent ? (
                <>
                  收起
                  <UpOutlined style={{ fontSize: 10, marginLeft: 6 }} />
                </>
              ) : (
                <>
                  展开
                  <DownOutlined style={{ fontSize: 10, marginLeft: 6 }} />
                </>
              )}
            </Button>
          </Flex>
          <Spin spinning={loadingBarrageData}>
            <div className={styles.barrageContent} hidden={!showBarrageContent}>
              <Flex gap={8} style={{ flexWrap: 'wrap' }}>
                {barrageList
                  ? barrageList.map((item, index) => {
                      return (
                        <div className={styles.barrageItem} key={index}>
                          {item}
                        </div>
                      );
                    })
                  : null}
              </Flex>
            </div>
          </Spin>
        </div>

        <div className={styles.aiChatWrap}>
          {difyIframeLink && (
            <iframe
              src={difyIframeLink}
              style={{ width: '100%', height: '100%' }}
              frameBorder="0"
              allow="microphone"
            />
          )}
        </div>
      </Flex>
    </Modal>
  );
};

export default BarrageAIChatModal;
