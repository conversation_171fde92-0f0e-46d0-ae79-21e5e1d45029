import { useState, useRef } from 'react';
import { PageContainer, ProCard } from '@ant-design/pro-components';
import { DateType } from '@/services/business';
import { getTimeByTypeBusiness } from '@/utils/time';
import { useSticky } from '@reactuses/core';
import BarrageAnalysisDetailFilters from './components/barrageAnalysisDetailFilters';
import BarrageOverviewCharts from './components/barrageOverviewCharts';
import BarrageList from './components/barrageList';
import useProjectId from '@/hooks/useProjectId';
import { withAuth } from '@/hoc/withAuth';
import { FunctionCode } from '@/services/system';
import { Space } from 'antd';
import { Decline, Rise } from '../style';
import { SvgIcon } from '@/components/SvgIcon';
import BreadCrumbSimple from '@/components/ui/breadcrumbSimple';
import { useDarkMode } from '@/hooks/useDarkMode';

const BarrageAnalysis = () => {
  const projectId = useProjectId();
  const [radioValue, setRadioValue] = useState<DateType>(DateType.Today);
  const [rangeTime, setRangeTime] = useState<string[]>(getTimeByTypeBusiness(DateType.Today));
  const [treeValue, setTreeValue] = useState<string[] | undefined>(undefined);
  const [barrageTotal, setBarrageTotal] = useState<null | number>(null);
  const [barrageGrowthRate, setBarrageGrowthRate] = useState<null | number>(null);
  const headerRef = useRef(null);
  const [isSticky] = useSticky(headerRef, { nav: 56 });
  const { isDarkMode } = useDarkMode();

  return (
    <PageContainer title={<BreadCrumbSimple breadcrumbs={['智能分析', '弹幕分析']} />}>
      <ProCard
        boxShadow={isSticky ? true : false}
        ref={headerRef}
        colSpan={{ md: 24, lg: 18 }}
        style={{ position: 'sticky', top: 56, zIndex: 99 }}
      >
        <BarrageAnalysisDetailFilters
          radioValue={radioValue}
          setRadioValue={setRadioValue}
          rangeTime={rangeTime}
          setRangeTime={setRangeTime}
          treeValue={treeValue}
          setTreeValue={setTreeValue}
        />
      </ProCard>

      <ProCard
        style={{ marginTop: 20 }}
        title="弹幕总览"
        extra={
          <Space>
            {barrageTotal !== null && (
              <span>
                弹幕总数：<span style={{ color: '#1e5eff' }}>{barrageTotal}</span>
              </span>
            )}
            {barrageGrowthRate !== null &&
              (Number(barrageGrowthRate) >= 0 ? (
                <Rise className="ml-4 flex items-center" style={{ color: '#f54848' }}>
                  <SvgIcon icon={'local:outline/rate-rise'} y={2} />
                  {barrageGrowthRate.toFixed(1)}%
                </Rise>
              ) : (
                <Decline className="ml-4 flex items-center" style={{ color: '#30b824' }}>
                  <SvgIcon icon={'local:outline/rate-decline'} y={2} />
                  {barrageGrowthRate.toFixed(1)}%
                </Decline>
              ))}
          </Space>
        }
      >
        <BarrageOverviewCharts
          rangeTime={rangeTime}
          teamCodeList={treeValue}
          projectId={projectId}
          onBarrageTotalChange={setBarrageTotal}
          onBarrageGrowthRateChange={setBarrageGrowthRate}
          darkMode={isDarkMode}
        />
      </ProCard>

      <ProCard style={{ marginTop: 20 }} title="弹幕列表">
        <BarrageList rangeTime={rangeTime} teamCodeList={treeValue} projectId={projectId} />
      </ProCard>
    </PageContainer>
  );
};

export default withAuth(BarrageAnalysis, FunctionCode.BarrageAnalysis);
