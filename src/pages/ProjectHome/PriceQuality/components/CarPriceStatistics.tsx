import { Button, Empty, Flex, RadioChangeEvent, message } from 'antd';
import { useEffect, useRef, useState } from 'react';
import styled from 'styled-components';
import { ProCard, ProColumns, ProTable } from '@ant-design/pro-components';
import { GetCarType } from '@/services/price';
import { useAsyncEffect } from 'ahooks';
import TimeFilterByNatural from '@/components/qualityVisual/timeFilterByNatural';
import { getTimeByTypeSimple } from '@/utils/time';
import { CustomDateRadioButton, CustomDateRadioGroup } from '@/utils/commonStyle';
import useProjectId from '@/hooks/useProjectId';
import { formatNum } from '@/utils/common';
import { IAggregation, aggregationExport, getAggregation } from '@/services/quality';
import ExportButton from '@/components/exportButton';

import { usePollingExport } from '@/hooks/usePollingExport';
import { useQuery } from '@umijs/max';
import { SvgIcon } from '@/components/SvgIcon';
import { GetPageField, FiledTableItem } from '@/services/team';
import { CarPriceStatisticsCharts } from './ECharts';

const CartItem = styled(Flex)<{ $selected: boolean }>`
  margin-right: 12px;
  border: 1px solid transparent;
  border-color: ${(props) => (props.$selected ? '#1E5EFF' : '#E2E2E2')};
  transition: border-color 0.3s ease;
  border-radius: 4px;
  padding: 8px;
  position: relative;
  cursor: pointer;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  gap: 10px;

  // 伪元素用作蒙层
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(30, 94, 255, 0.1);
    pointer-events: none; // 确保蒙层不会影响元素的交互操作
    display: block;
    opacity: ${(props) => (props.$selected ? '1' : '0')};
    transition: opacity 0.3s ease;
  }
`;

const PercentSpan = styled.span<{ $percent: number }>`
  display: inline-flex;
  white-space: nowrap;
  align-items: center;
  column-gap: 2px;
  color: ${(props) => (Number(props.$percent) > 0 ? '#F54848' : '#30B824')};
  white-space: nowrap;
`;

type CartType = {
  label: string;
  value: string;
  img: string;
  minPrice: number;
  maxPrice: number;
  cnt: number;
};

const SubsidiaryPricesSpan = styled.span`
  color: #95979c;
  font-size: 12;
`;

const useAggregation = (params: {
  projectId: number;
  carType: string;
  aggregationField: string;
  liveStartDate: string;
  liveEndDate: string;
}) => {
  const { data, error, isFetching } = useQuery({
    queryFn: () => getAggregation(params),
    queryKey: [
      `aggregation`,
      params.aggregationField,
      params.carType,
      params.liveEndDate,
      params.liveStartDate,
      params.projectId,
    ],
    enabled:
      !!params.projectId && !!params.carType && !!params.liveStartDate && !!params.liveEndDate,
  });

  return { data: data?.data, error, loading: isFetching };
};
function useCarType({
  projectId,
  startDate,
  endDate,
}: {
  projectId?: string;
  startDate?: string;
  endDate?: string;
}) {
  const { data } = useQuery({
    queryKey: ['carType', projectId, startDate, endDate],
    queryFn: () => GetCarType({ projectId, startDate, endDate }),
    enabled: !!projectId && !!startDate && !!endDate,
  });

  return {
    data: data?.data?.map((item) => ({
      value: item.value,
      label: item.name,
      img: item.attributes?.icon,
      minPrice: item.attributes?.minPrice,
      maxPrice: item.attributes?.maxPrice,
      cnt: item.attributes?.cnt,
    })),
  };
}
export function CarPriceStatistics() {
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const projectId = useProjectId();
  const columns: ProColumns<IAggregation>[] = [
    {
      title: '门店范围',
      dataIndex: 'fieldValue',
      key: 'fieldValue',
      fixed: 'left',
      search: false,
    },
    {
      title: '报价范围',
      dataIndex: 'quotationScope',
      key: 'quotationScope',
      render: (_, { quotationScope }) => (
        <span>
          {formatNum(quotationScope[0])}~{formatNum(quotationScope[1])}
        </span>
      ),
      search: false,
    },
    {
      title: '平均价',
      tooltip: '括号内的报价为对应历史报价',
      dataIndex: 'averagePrice',
      key: 'averagePrice',
      search: false,
      render: (_, { averagePrice }) => (
        <span className="">
          {averagePrice.price ? (
            <>
              {formatNum(averagePrice.price)}
              {dateType !== 4 && (
                <SubsidiaryPricesSpan>
                  （{formatNum(averagePrice.historyPrice)}）
                </SubsidiaryPricesSpan>
              )}
            </>
          ) : (
            '-'
          )}
        </span>
      ),
      sorter: (a, b) => a.averagePrice.price - b.averagePrice.price,
    },
    {
      title: '平均价涨跌幅',
      dataIndex: '',
      width: '600',
      key: '',
      render: (_, { averagePrice }) => (
        <>
          {averagePrice.percent ? (
            <PercentSpan $percent={averagePrice.percent}>
              {averagePrice.percent > 0 ? (
                <SvgIcon icon="local:outline/rate-rise" />
              ) : (
                <SvgIcon icon="local:outline/rate-decline" />
              )}
              {Math.abs(averagePrice.percent)}%
            </PercentSpan>
          ) : (
            '-'
          )}
        </>
      ),
    },
    {
      title: '最低价',
      dataIndex: 'minPrice',
      key: 'minPrice',
      render: (_, { minPrice }) => (
        <span className="whitespace-nowrap">
          <>{formatNum(minPrice.price)}</>
          <>
            {minPrice.historyPrice && (
              <>
                {dateType !== 4 && (
                  <SubsidiaryPricesSpan>
                    （{formatNum(minPrice.historyPrice)}）
                  </SubsidiaryPricesSpan>
                )}
              </>
            )}
          </>
        </span>
      ),
      sorter: (a, b) => a.minPrice.price - b.minPrice.price,
      tooltip: '括号内的报价为对应历史报价',
    },
    {
      title: '最低价涨跌幅',
      dataIndex: '',
      key: '',
      render: (_, { minPrice }) => (
        <>
          {minPrice.percent ? (
            <PercentSpan $percent={minPrice.percent}>
              {minPrice.percent > 0 ? (
                <SvgIcon icon="local:outline/rate-rise" />
              ) : (
                <SvgIcon icon="local:outline/rate-decline" />
              )}
              {Math.abs(minPrice.percent)}%
            </PercentSpan>
          ) : (
            '-'
          )}
        </>
      ),
    },
    {
      title: '最高价',
      tooltip: '括号内的报价为对应历史报价',
      dataIndex: 'maxPrice',
      key: 'maxPrice',
      render: (_, { maxPrice }) => (
        <span className="whitespace-nowrap">
          <span>{formatNum(maxPrice.price)}</span>
          {maxPrice.historyPrice && (
            <>
              {dateType !== 4 && (
                <SubsidiaryPricesSpan>（{formatNum(maxPrice.historyPrice)}）</SubsidiaryPricesSpan>
              )}
            </>
          )}
        </span>
      ),
      sorter: (a, b) => a.maxPrice.price - b.maxPrice.price,
    },
    {
      title: '最高价涨跌幅',
      dataIndex: '',
      key: '',
      render: (_, { maxPrice }) => (
        <span className="whitespace-nowrap">
          {maxPrice.percent ? (
            <PercentSpan $percent={maxPrice.percent}>
              {maxPrice.percent > 0 ? (
                <SvgIcon icon="local:outline/rate-rise" />
              ) : (
                <SvgIcon icon="local:outline/rate-decline" />
              )}
              {maxPrice.percent}%
            </PercentSpan>
          ) : (
            '-'
          )}
        </span>
      ),
    },
    {
      title: '中位数',
      dataIndex: 'median',
      width: '250',
      key: 'median',
      render: (_, { median }) => <span>{median.price ? median.price : '-'}</span>,
      sorter: (a, b) => a.median.price - b.median.price,
    },
    {
      title: '中位数涨跌幅',
      dataIndex: '',
      key: '',
      render: (_, { median }) => (
        <>
          {median.percent ? (
            <PercentSpan $percent={median.percent}>
              {median.percent > 0 ? (
                <SvgIcon icon="local:outline/rate-rise" />
              ) : (
                <SvgIcon icon="local:outline/rate-decline" />
              )}
              {median.percent}%
            </PercentSpan>
          ) : (
            '-'
          )}
        </>
      ),
    },
  ];

  const [selectedCarType, setSelectedCarType] = useState<string>('');
  const [rangeTime, setRangeTime] = useState<string[]>(getTimeByTypeSimple(1));
  const [dateType, setDateType] = useState<number>(1); // 时间类型 1-近一天 2-近一周 3-近一月 4-自定义
  const [field, setField] = useState<FiledTableItem[]>([]);
  const [radioValue, setRadioValue] = useState<string>('');
  const [showScrollButtons, setShowScrollButtons] = useState(false);

  const { data: carType } = useCarType({
    projectId,
    startDate: rangeTime[0],
    endDate: rangeTime[1],
  });
  useEffect(() => {
    if (carType && carType.length > 0 && carType[0].label && !selectedCarType) {
      setSelectedCarType(carType[0].label);
    }
  }, [carType, selectedCarType, radioValue]);

  const { data: dataSource } = useAggregation({
    projectId: Number(projectId),
    liveStartDate: rangeTime[0],
    liveEndDate: rangeTime[1],
    aggregationField: radioValue,
    carType: selectedCarType,
  });

  const checkForOverflow = () => {
    const container = scrollContainerRef.current;
    if (container) {
      const isOverflowing = container.scrollWidth > container.clientWidth;
      setShowScrollButtons(isOverflowing);
    }
  };

  useEffect(() => {
    const container = scrollContainerRef.current;
    if (container) {
      const resizeObserver = new ResizeObserver(() => {
        checkForOverflow();
      });
      resizeObserver.observe(container);
      return () => resizeObserver.unobserve(container);
    }
  }, []);

  const scrollLeft = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollTo({
        left: scrollContainerRef.current.scrollLeft - 300,
        behavior: 'smooth',
      });
    }
  };

  const scrollRight = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollTo({
        left: scrollContainerRef.current.scrollLeft + 300,
        behavior: 'smooth',
      });
    }
  };
  useAsyncEffect(async () => {
    const res = await GetPageField({ projectId, page: 1, size: 1000 });

    const data =
      res?.data?.items?.filter((item) => item.showFlag === 1 && item.bizType === 2) || [];
    setField(data);
    if (data.length > 0 && data[0].fieldName) {
      setRadioValue(data[0].fieldName);
    }
  }, []);

  const handleSelectCartType = (car: CartType) => {
    setSelectedCarType(car.label);
  };

  const onChange = (e: RadioChangeEvent) => {
    const value = e.target.value;
    setRadioValue(value);
  };
  const { pollingExport, percent, pollingLoading } = usePollingExport();
  const handelExport = async () => {
    const res = await aggregationExport({
      projectId: Number(projectId),
      liveStartDate: rangeTime[0],
      liveEndDate: rangeTime[1],
      aggregationField: radioValue,
      carType: selectedCarType,
    });
    if (res.data) {
      pollingExport({ taskId: res.data });
    } else {
      message.error('导出失败,请重试');
    }
  };

  return (
    <ProCard>
      <Flex align="center" justify="space-between">
        <TimeFilterByNatural
          rangeTime={rangeTime}
          setRangeTime={(value) => {
            setRangeTime(value);
            setSelectedCarType('');
          }}
          dateType={dateType}
          setDataType={(value) => {
            setDateType(value);
            setSelectedCarType('');
          }}
          maxDays={180}
        />
        <ExportButton
          exportFn={handelExport}
          style={{
            marginLeft: '5px',
          }}
          loading={pollingLoading}
          percent={percent}
          disabled={!dataSource?.length}
        />
      </Flex>
      <div style={{ marginTop: '20px' }}>
        <span>车型选择</span>
        <Flex className="" align="center" justify="start">
          {showScrollButtons && (
            <Button
              onClick={scrollLeft}
              icon={<SvgIcon icon="local:outline/left-arrow" />}
              type="text"
            />
          )}
          <Flex
            wrap="nowrap"
            gap="small"
            ref={scrollContainerRef}
            className="flex overflow-x-scroll"
            style={{
              marginTop: '20px',
            }}
          >
            {carType?.map((item) => {
              return (
                <CartItem
                  key={item.value}
                  className="w-auto"
                  vertical
                  align="center"
                  onClick={() => {
                    handleSelectCartType(item);
                    // 滚动选中的卡片到视窗内
                    const element = document.getElementById(`cart-item-${item.value}`);
                    if (element) {
                      element.scrollIntoView({
                        behavior: 'smooth',
                        block: 'nearest',
                        inline: 'center',
                      });
                    }
                  }}
                  $selected={selectedCarType === item.label}
                  id={`cart-item-${item.value}`}
                >
                  <div className="flex flex-col items-center">
                    <img
                      width={70}
                      height={46}
                      className="mb-2 object-fill"
                      src={item.img}
                      alt={item.label}
                    />
                    <span className="whitespace-nowrap text-lg">{item.label}</span>
                  </div>
                  <div
                    className="flex flex-col flex-nowrap items-start whitespace-nowrap"
                    style={{
                      fontSize: '12px',
                      color: '#0E1015',
                    }}
                  >
                    <div className="mb-2 whitespace-nowrap text-base">
                      直播间报价数量：{item.cnt ? item.cnt : '-'}
                    </div>
                    <div className="flex-nowrap whitespace-nowrap text-base">
                      直播间报价范围：
                      {item.minPrice ? formatNum(item.minPrice) : '-'} -
                      {item.maxPrice ? formatNum(item.maxPrice) : '-'}
                    </div>
                  </div>
                </CartItem>
              );
            })}
          </Flex>
          {showScrollButtons && (
            <Button
              onClick={scrollRight}
              icon={<SvgIcon icon="local:outline/right-arrow" />}
              type="text"
            />
          )}
        </Flex>
      </div>
      <div
        className="flex items-center"
        style={{
          margin: '20px 0',
        }}
      >
        <CustomDateRadioGroup
          value={radioValue}
          onChange={onChange}
          buttonStyle="solid"
          className="flex-1"
        >
          {field.map((item) => {
            return (
              <CustomDateRadioButton key={item.fieldName} value={item.fieldName}>
                {item.fieldName}
              </CustomDateRadioButton>
            );
          })}
        </CustomDateRadioGroup>
      </div>
      <>
        {dataSource && dataSource?.length > 0 ? (
          <>
            {dataSource.map((itemData, index) => (
              <div key={itemData.title} style={{ marginTop: '20px' }}>
                <div
                  style={{
                    overflowX: 'auto',
                  }}
                >
                  <CarPriceStatisticsCharts
                    dataSource={itemData.data}
                    label={itemData.title}
                    type={index === 0}
                  />
                </div>
                <ProTable
                  style={{
                    marginTop: '20px',
                  }}
                  dataSource={itemData.data}
                  columns={columns}
                  rowKey={'fieldValue'}
                  scroll={{
                    x: 'max-content',
                  }}
                  pagination={false}
                  toolBarRender={false}
                  search={false}
                />
              </div>
            ))}
          </>
        ) : (
          <Empty
            style={{
              marginTop: '250px',
            }}
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description={<span>暂无数据</span>}
          />
        )}
      </>
    </ProCard>
  );
}
