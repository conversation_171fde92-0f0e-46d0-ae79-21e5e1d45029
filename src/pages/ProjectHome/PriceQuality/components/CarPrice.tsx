import { usePollingExport } from '@/hooks/usePollingExport';
import useProjectId from '@/hooks/useProjectId';
import {
  LivePriceItem,
  ExportLivePriceRecordPage,
  LivePriceRecordPage,
  GetCarType,
} from '@/services/price';
import { formatNum, STICKY_OFFSETHEADER, transferParams } from '@/utils/common';
import { ProCard, ProColumns, ProTable, ProFormSelect } from '@ant-design/pro-components';
import { Flex, Popover, Space, message } from 'antd';
import { omit } from 'lodash-es';
import { useRef, useState } from 'react';
import { getTimeByTypeSimple } from '@/utils/time';
import TimeFilterByNatural from '@/components/qualityVisual/timeFilterByNatural';
import DynamicTree from '@/components/dynamicTree';
import ExportButton from '@/components/exportButton';
import useGetTeamFieldList from '@/hooks/useFields';
import { proTableRequestAdapterParamsAndData } from '@/utils';
import { customPaginationRender } from '../../style';
import { renderCustomColumns } from '@/components/renderCustomColumns';
import { proTableSearchConfig } from '@/utils/proTableConfig';
import { useParams } from '@umijs/max';

export function CarPrice() {
  const projectId = useProjectId();
  const { industryType } = useParams();
  const firstAddColumns = useRef(false);
  const teamFieldListRef = useGetTeamFieldList(projectId);
  const [treeValue, setTreeValue] = useState<string[] | undefined>(undefined);
  const defaultColumns: ProColumns<LivePriceItem>[] = [
    {
      title: '直播间ID',
      dataIndex: 'roomId',
      align: 'center',
      fixed: 'left',
      fieldProps: {
        placeholder: '直播间ID',
      },
      formItemProps: {
        label: null,
      },
    },
    {
      title: '直播间名称',
      dataIndex: 'roomTitle',
      align: 'center',
      fixed: 'left',
      fieldProps: {
        placeholder: '直播间名称',
      },
      formItemProps: {
        label: null,
      },
    },
    {
      title: '动态维度筛选',
      dataIndex: 'dynamicDimension',
      hideInTable: true,
      formItemProps: {
        label: null,
      },
      renderFormItem: () => {
        return <DynamicTree value={treeValue} setValue={setTreeValue} />;
      },
    },
    {
      title: '车型',
      dataIndex: 'carQuotationList',
      align: 'left',
      hideInSearch: true,
      render(_, record) {
        const { carQuotationList } = record;
        return (
          <Popover
            content={
              <div
                style={{
                  width: '300px',
                }}
              >
                {carQuotationList.map((item) => {
                  return (
                    <div key={item.carType} style={{ marginBottom: '10px' }}>
                      {item.carTypeCn}报价：
                      {item.minQuotation ? formatNum(item.minQuotation) : '暂无报价'} -
                      {item.maxQuotation ? formatNum(item.maxQuotation) : '暂无报价'}
                    </div>
                  );
                })}
              </div>
            }
            title={null}
            arrow={false}
          >
            {carQuotationList.map((item) => {
              return (
                <span key={item.carType} style={{ marginRight: '10px', whiteSpace: 'nowrap' }}>
                  {item.carTypeCn}
                </span>
              );
            })}
          </Popover>
        );
      },
    },
    {
      title: '车型选择',
      hideInTable: true,
      dataIndex: 'carType',
      renderFormItem: () => {
        return (
          <ProFormSelect
            showSearch
            allowClear
            request={async () => {
              const res = await GetCarType({ projectId });
              // @ts-ignore
              return res.data.map((item) => ({
                label: item.name,
                value: item.value,
              }));
            }}
            placeholder="查找车型"
            style={{
              width: '200px',
            }}
          />
        );
      },
    },
    {
      title: '直播开始时间',
      dataIndex: 'liveStartTime',
      align: 'center',
      hideInSearch: true,
      fixed: 'right',
    },
    {
      title: '操作',
      dataIndex: 'options',
      hideInSearch: true,
      align: 'center',
      width: 80,
      fixed: 'right',
      render(_, record) {
        return (
          <Space>
            <a
              onClick={() => {
                window.open(
                  `${window.location.origin}/live/detail/${record.roomId}/1/${industryType}`,
                );
              }}
            >
              详情
            </a>
          </Space>
        );
      },
    },
  ];

  const [columns, setColumns] = useState<ProColumns<LivePriceItem>[]>(defaultColumns);
  const paramsRef = useRef<any>({});
  const filedIdArrRef = useRef<string[]>([]);
  const [dateType, setDateType] = useState<number>(1); // 时间类型 1-近一天 2-近一周 3-近一月 4-自定义
  const [rangeTime, setRangeTime] = useState<string[]>(getTimeByTypeSimple(1));

  const { pollingExport, percent, pollingLoading } = usePollingExport();

  const handleExportPrice = async () => {
    const omitPageParams = omit(paramsRef.current, ['pageSize', 'current']);
    const res = await ExportLivePriceRecordPage(omitPageParams);
    if (res.data) {
      pollingExport({ taskId: res.data });
    } else {
      message.error('导出失败,请重试');
    }
  };

  return (
    <ProCard>
      <ProTable<LivePriceItem>
        columns={columns}
        params={{
          projectId,
          teamCodeList: treeValue,
          liveStartDate: rangeTime[0],
          liveEndDate: rangeTime[1],
        }}
        onReset={() => setTreeValue(undefined)}
        tableClassName="custom-table"
        scroll={{ x: 'max-content' }}
        ghost
        sticky={{ offsetHeader: STICKY_OFFSETHEADER }}
        postData={(data: LivePriceItem[]) => {
          if (!firstAddColumns.current && data.length > 0) {
            let newColumns = [...columns];
            renderCustomColumns(teamFieldListRef.current).then(({ customColumns, fieldIdArr }) => {
              filedIdArrRef.current = fieldIdArr;
              // @ts-ignore
              newColumns = newColumns.concat(...customColumns) as ProColumns<LivePriceItem>[];

              setColumns(newColumns);
            });
            firstAddColumns.current = true;
          }
          // 将teamFields内的字段都抽出来
          const formatData = data.map((item) => {
            const res = { ...item };
            const teamFields = res.teamFieldList;
            if (teamFields) {
              teamFields.forEach((field) => {
                (res as any)[field.fieldId] = field.value;
              });
            }
            return res;
          });
          return formatData;
        }}
        beforeSearchSubmit={(params) => {
          const fieldList = transferParams(params, filedIdArrRef.current);
          if (fieldList.length > 0) {
            return { ...params, fieldList };
          }
          return {
            ...params,
          };
        }}
        request={(params, sorter) => {
          paramsRef.current = params;
          return proTableRequestAdapterParamsAndData(params, sorter, LivePriceRecordPage);
        }}
        search={{ ...proTableSearchConfig }}
        toolBarRender={() => [
          <ExportButton
            exportFn={handleExportPrice}
            key="export"
            loading={pollingLoading}
            percent={percent}
          />,
        ]}
        rowKey={(record) => record.teamCode + Math.random()}
        pagination={{
          defaultPageSize: 10,
          showTotal: (total, range) => `${range[0]}-${range[1]} 共 ${total} 条`,
          itemRender: customPaginationRender,
          showSizeChanger: true,
        }}
        dateFormatter="string"
        headerTitle={
          <Flex
            justify="space-between"
            style={{
              width: '100%',
              flex: '1',
            }}
          >
            <TimeFilterByNatural
              rangeTime={rangeTime}
              setRangeTime={setRangeTime}
              dateType={dateType}
              setDataType={setDateType}
              maxDays={180}
            />
          </Flex>
        }
      />
    </ProCard>
  );
}
