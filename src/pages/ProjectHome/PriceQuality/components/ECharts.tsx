import { IAggregation } from '@/services/quality';
import { EChartsOption } from 'echarts';
import ReactECharts from 'echarts-for-react';
import { get } from 'lodash-es';

import { useMemo } from 'react';

interface EChartsProps {
  dataSource: IAggregation[];
  label: string;
  type?: boolean;
}

export function CarPriceStatisticsCharts({ dataSource, label, type }: EChartsProps) {
  const trendIllegalWords = useMemo(() => {
    return {
      color: ['#FFA51E', '#95979C', '#73A0FA', '#5EE0D8'],
      tooltip: {
        trigger: 'axis',
      },
      legend: {
        data: ['指导价', '均价', '最低价', '最高价'],
        textStyle: {
          color: 'gray',
        },
      },
      dataZoom:
        Number(dataSource?.length) >= 20
          ? [
              {
                type: 'inside', // 使用内置型 dataZoom
                start: 0,
                end: 60, // 最初显示前20%的数据
                zoomOnMouseWheel: false, // 禁止使用鼠标滚轮进行缩放
                moveOnMouseMove: true, // 允许使用鼠标移动进行左右移动
                moveOnMouseWheel: 'shift', // 开启鼠标滚轮移动
                moveOnMouseWheelEdge: 0.2, // 边缘滚动的灵敏度
                preventDefaultMouseMove: true, // 阻止图表内容的默认鼠标滚动行为
              },
            ]
          : undefined,
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        boundaryGap: ['5%', '5%'],
        data: dataSource?.map((item) => item.fieldValue),
        axisLabel: {
          interval: 0,
        },
      },
      yAxis: {
        boundaryGap: ['5%', '5%'],
        min: !type
          ? get(dataSource, '[0].guidePrice', 0) * 0.5
          : Math.min(...dataSource.map((item) => item.minPrice.price)) * 0.5,
        max: !type
          ? get(dataSource, '[0].guidePrice', 0) * 1.5
          : Math.max(...dataSource.map((item) => item.maxPrice.price)) * 1.5,
        type: 'value',
        name: '每个车型（w）',
        axisLabel: {
          formatter: function (value: any) {
            return (value / 10000).toFixed(4); // 将数值除以10000，保留两位小数，并加上"w"的单位
          },
        },
      },
      series: [
        {
          name: '指导价',
          type: 'line',
          data: dataSource?.map((item) => `${item.guidePrice}`),
        },
        {
          name: '均价',
          type: 'line',
          data: dataSource?.map((item) => `${item.averagePrice.price}`),
          lineStyle: {
            type: 'dashed',
          },
        },
        {
          name: '最低价',
          type: 'line',
          data: dataSource?.map((item) => `${item.minPrice.price}`),
        },
        {
          name: '最高价',
          type: 'line',
          data: dataSource?.map((item) => `${item.maxPrice.price}`),
        },
      ],
    } as EChartsOption;
  }, [dataSource, type]);

  return (
    <div>
      <div
        style={{
          fontSize: '16px',
          fontWeight: 'medium',
          marginBottom: '10px',
          color: '#0E1015',
        }}
      >
        {`${label}价格分布`}
      </div>
      <ReactECharts
        option={trendIllegalWords}
        notMerge={true}
        style={{ width: '100%', height: '500px' }}
      />
    </div>
  );
}
