import { PageContainer } from '@ant-design/pro-components';
import { Tabs, TabsProps } from 'antd';
import useTabKeySearchParams from '@/hooks/useTabKeySearchParams';
import { CarPrice } from './components/CarPrice';
import { CarPriceStatistics } from './components/CarPriceStatistics';

const PriceInspection = () => {
  const [activeKey, onTabChange] = useTabKeySearchParams('price');
  const items: TabsProps['items'] = [
    {
      label: '车型报价',
      key: 'price',
      children: <CarPrice />,
    },
    {
      label: '车价聚合看板',
      key: 'statistics',
      children: <CarPriceStatistics />,
    },
  ];
  return (
    <PageContainer title={'质检管理/报价质检'}>
      <Tabs activeKey={activeKey} onChange={onTabChange} items={items} className="horizontal-tab" />
    </PageContainer>
  );
};

export default PriceInspection;
