import DynamicTree from '@/components/dynamicTree';
import TimeFilterDataDashboard from '@/components/timeFilterDataDashboard';
import useProjectId from '@/hooks/useProjectId';
import { GetTeamDepthSelect } from '@/services/team';
import { getTimeByTypeBusiness } from '@/utils/time';
import { PageContainer, ProCard } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Alert, Drawer, Flex, Space, Tabs as AntdTabs, Tooltip } from 'antd';
import { useRef, useState } from 'react';
import DataLineChart from './component/dataLineChart';
import SingleRank from './component/singleRank';
import TotalRank from './component/totalRank';
import { useSticky } from '@reactuses/core';
import TagsSelect from '@/components/tags/tagsSelect';
import DrawerContent from './component/drawerContent';
import StepsCard from './component/stepsCard';
import {
  BusinessOverview,
  BusinessTips,
  DateType,
  FieldList,
  GetDataUpdateTime,
} from '@/services/business';
import { renderBusinessTips } from './component/businessTips';
import { QuestionCircleOutlined } from '@ant-design/icons';
import useLiveAfkFG from '@/hooks/fg/useLiveAfkFG';
import useBehaviorViolateFG from '@/hooks/fg/useBehaviorFG';
import useAnchorImageFG from '@/hooks/fg/useAnchorImageFG';
import { selectPlatformAtom } from '../atom';
import { useAtomValue } from 'jotai';
import PlatformSwitch from '@/components/platformSwitch';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';

const DataDashboard = () => {
  const projectId = useProjectId();
  const [treeValue, setTreeValue] = useState<string[] | undefined>(undefined);
  const [radioValue, setRadioValue] = useState<DateType | undefined>(DateType.LastWeek);
  const [rangeTime, setRangeTime] = useState<string[]>(
    getTimeByTypeBusiness(DateType.LastWeek, projectId),
  );
  const [tagIdList, setTagIdList] = useState<string[]>([]);
  const [fieldId, setFieldId] = useState<number>();
  const headerRef = useRef(null);
  const [isSticky] = useSticky(headerRef, { nav: 56 });
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [drawerFieldList, setDrawerFieldList] = useState<FieldList[]>([]);
  const liveBehaviorViolateFG = useBehaviorViolateFG(projectId);
  const afkState = useLiveAfkFG(projectId);
  const anchorImageState = useAnchorImageFG(projectId);
  const platform = useAtomValue(selectPlatformAtom);

  const { data: teamDepthData } = useRequest(() => GetTeamDepthSelect({ projectId }), {
    onSuccess: (data) => {
      const res = data?.[0].value;
      setFieldId(Number(res));
    },
  });

  const fieldName = teamDepthData?.find((item) => item.value === String(fieldId))?.name;
  // 是否选中了最细的维度
  const isLastField =
    teamDepthData?.find((item) => item.value === String(fieldId))?.attributes?.bizType === 1;

  const onTagIdListChange = (value: any) => {
    setTagIdList(value);
  };

  const showDrawer = (fieldList: FieldList[]) => {
    setDrawerFieldList(fieldList);
    setDrawerOpen(true);
  };

  const onClose = () => {
    setDrawerOpen(false);
  };

  const items = [
    {
      label: (
        <>
          综合排名{' '}
          <Tooltip title="将作品、直播、粉丝、线索、私信四个模块的重要数据指标进行综合排名，然后将所有指标的排名相加，再进行总排名排序，得出综合排名。">
            <QuestionCircleOutlined />
          </Tooltip>
        </>
      ),
      key: 'total-rank',
      children: (
        <TotalRank
          showDrawer={showDrawer}
          fieldName={fieldName}
          projectId={projectId}
          dateType={radioValue}
          rangeTime={rangeTime}
          teamCodeList={treeValue}
          tagIdList={tagIdList}
          fieldId={fieldId}
          liveBehaviorViolateFG={liveBehaviorViolateFG}
          anchorImageState={anchorImageState}
          platform={platform}
        />
      ),
    },
    {
      label: '单指标排名',
      key: 'single-rank',
      children: (
        <SingleRank
          showDrawer={showDrawer}
          fieldName={fieldName}
          projectId={projectId}
          dateType={radioValue}
          rangeTime={rangeTime}
          teamCodeList={treeValue}
          tagIdList={tagIdList}
          fieldId={fieldId}
          afkState={afkState}
          liveBehaviorViolateFG={liveBehaviorViolateFG}
          isLastField={isLastField}
          anchorImageState={anchorImageState}
          platform={platform}
        />
      ),
    },
  ];

  const operations = (
    <>
      {teamDepthData && (
        <Tabs value={fieldId?.toString()} onValueChange={(value) => setFieldId(Number(value))}>
          <TabsList value={fieldId?.toString()}>
            {teamDepthData.map((item) => (
              <TabsTrigger
                key={item.value}
                value={item.value}
                onClick={() => setFieldId(Number(item.value))}
              >
                {item.name}
              </TabsTrigger>
            ))}
          </TabsList>
        </Tabs>
      )}
    </>
  );

  const { data: stepsData, loading: stepsDataLoading } = useRequest(
    () =>
      BusinessOverview({
        projectId,
        dateType: radioValue,
        startDate: rangeTime?.[0],
        endDate: rangeTime?.[1],
        teamCodeList: treeValue,
        tagIdList,
        platform,
      }),
    {
      ready: !!radioValue && !!projectId && !!platform,
      refreshDeps: [radioValue, projectId, rangeTime, treeValue, tagIdList, platform],
    },
  );

  const { data: updateTime } = useRequest(() => GetDataUpdateTime({ projectId }), {
    ready: !!projectId,
  });

  const { data: businessTips } = useRequest(
    () =>
      BusinessTips({
        projectId,
        dateType: radioValue,
        startDate: rangeTime?.[0],
        endDate: rangeTime?.[1],
        teamCodeList: treeValue,
        tagIdList,
        platform,
      }),
    {
      ready: !!radioValue && !!projectId && !!platform,
      refreshDeps: [radioValue, projectId, rangeTime, treeValue, tagIdList, platform],
    },
  );

  return (
    <PageContainer
      title="总览看板"
      extra={
        <Space>
          <span style={{ color: '#979797', fontSize: '12px' }}>
            最新数据更新到: {updateTime} 下次数据更新时间: 次日21点后
          </span>
        </Space>
      }
      style={{ position: 'relative' }}
    >
      <ProCard
        boxShadow={isSticky ? true : false}
        ref={headerRef}
        colSpan={{ md: 24, lg: 18 }}
        style={{ position: 'sticky', top: 56, zIndex: 99 }}
      >
        <Flex>
          <Flex style={{ marginRight: '10px', width: '50%', gap: '10px', alignItems: 'center' }}>
            <DynamicTree
              value={treeValue}
              setValue={setTreeValue}
              style={{ maxWidth: '300px', height: '32px', width: '30%' }}
            />
            <TagsSelect
              style={{ maxWidth: '300px', height: '32px', width: '30%' }}
              value={tagIdList}
              onChange={onTagIdListChange}
            />
            <PlatformSwitch />
          </Flex>
          <TimeFilterDataDashboard
            radioValue={radioValue}
            setRadioValue={setRadioValue}
            setRangeTime={setRangeTime}
            style={{ justifyContent: 'flex-end', width: '50%' }}
            projectId={projectId}
          />
        </Flex>
      </ProCard>

      <ProCard colSpan={{ md: 24, lg: 18 }} style={{ marginTop: '10px' }}>
        {businessTips && businessTips?.length > 0 && (
          <Alert
            message={renderBusinessTips(rangeTime, businessTips)}
            type="info"
            showIcon
            closable
          />
        )}

        <StepsCard
          stepsData={stepsData}
          loading={stepsDataLoading}
          liveBehaviorViolateFG={liveBehaviorViolateFG}
          afkState={afkState}
          anchorImageState={anchorImageState}
        />
        <DataLineChart
          rangeTime={rangeTime}
          projectId={projectId}
          dateType={radioValue}
          teamCodeList={treeValue}
          tagIdList={tagIdList}
          platform={platform}
        />
      </ProCard>

      <ProCard colSpan={{ md: 24, lg: 6 }} style={{ marginTop: '24px' }}>
        <AntdTabs tabBarExtraContent={operations} items={items} />
      </ProCard>

      <Drawer
        title={drawerFieldList.length > 0 ? `${drawerFieldList[0].value}数据大盘` : ''}
        width="85%"
        onClose={onClose}
        open={drawerOpen}
      >
        <DrawerContent
          fieldList={drawerFieldList}
          drawerRangTime={rangeTime}
          drawerRadioValue={radioValue}
          drawerTagIdList={tagIdList}
          liveBehaviorViolateFG={liveBehaviorViolateFG}
          afkState={afkState}
          anchorImageState={anchorImageState}
          platform={platform}
        />
      </Drawer>
    </PageContainer>
  );
};

export default DataDashboard;
