import { Col, Radio, RadioChangeEvent, Row, Select, Space } from 'antd';
import { useEffect, useMemo, useState } from 'react';
import ReactECharts from 'echarts-for-react';
import ContributionTable from './contributionTable';
import RedTable from './redTable';
import BlackTable from './blackTable';
import DataDetailsTable from './dataDetailsTable';
import { pieColor } from '@/utils/commonStyle';
import {
  BusinessChineseName,
  BusinessContributionList,
  BusinessContributionOverview,
  BusinessContributionTop,
  BusinessDataType,
  DateType,
  FieldList,
  ModuleType,
} from '@/services/business';
import { useRequest } from '@umijs/max';
import { formatSecond } from '@/utils/time';
import { formatNum } from '@/utils/common';
import { PlatForm } from '@/utils/platform';
import { useDarkMode } from '@/hooks/useDarkMode';
import { PieChartOutlined, TableOutlined } from '@ant-design/icons';

type SingleRankProps = {
  showDrawer: (fieldList: FieldList[]) => void;
  fieldName?: string;
  projectId?: string;
  dateType?: DateType;
  rangeTime?: (string | undefined)[];
  teamCodeList?: string[];
  tagIdList?: string[];
  fieldId?: number;
  liveBehaviorViolateFG?: boolean;
  isLastField?: boolean;
  afkState?: boolean;
  anchorImageState?: boolean;
  platform: PlatForm;
};

const SingleRank = (props: SingleRankProps) => {
  const {
    showDrawer,
    fieldName,
    projectId,
    dateType,
    rangeTime,
    teamCodeList,
    tagIdList,
    fieldId,
    liveBehaviorViolateFG,
    isLastField,
    afkState,
    anchorImageState,
    platform,
  } = props;
  const { isDarkMode } = useDarkMode();

  const selectOptionsMap: Record<ModuleType, { value: BusinessDataType; label: string }[]> =
    useMemo(
      () => ({
        [ModuleType.Post]: [
          {
            value: BusinessDataType.WorksPublished,
            label: BusinessChineseName[BusinessDataType.WorksPublished],
          },
          {
            value: BusinessDataType.WorksInteractions,
            label: BusinessChineseName[BusinessDataType.WorksInteractions],
          },
          {
            value: BusinessDataType.WorksViewsIncrement,
            label: BusinessChineseName[BusinessDataType.WorksViewsIncrement],
          },
          {
            value: BusinessDataType.WorksLikesIncrement,
            label: BusinessChineseName[BusinessDataType.WorksLikesIncrement],
          },
          {
            value: BusinessDataType.WorksCommentsIncrement,
            label: BusinessChineseName[BusinessDataType.WorksCommentsIncrement],
          },
          {
            value: BusinessDataType.WorksSharesIncrement,
            label: BusinessChineseName[BusinessDataType.WorksSharesIncrement],
          },
        ],
        [ModuleType.Live]: [
          {
            value: BusinessDataType.LiveSessions,
            label: BusinessChineseName[BusinessDataType.LiveSessions],
          },
          {
            value: BusinessDataType.LiveDuration,
            label: BusinessChineseName[BusinessDataType.LiveDuration],
          },
          {
            value: BusinessDataType.LiveViewers,
            label: BusinessChineseName[BusinessDataType.LiveViewers],
          },
          {
            value: BusinessDataType.LiveViewerTimes,
            label: BusinessChineseName[BusinessDataType.LiveViewerTimes],
          },
          {
            value: BusinessDataType.LiveDanmuCount,
            label: BusinessChineseName[BusinessDataType.LiveDanmuCount],
          },
          {
            value: BusinessDataType.LiveLikes,
            label: BusinessChineseName[BusinessDataType.LiveLikes],
          },
          {
            value: BusinessDataType.SensitivityViolations,
            label: BusinessChineseName[BusinessDataType.SensitivityViolations],
          },
          ...(afkState
            ? [
                {
                  value: BusinessDataType.IdleLiveSessions,
                  label: BusinessChineseName[BusinessDataType.IdleLiveSessions],
                },
              ]
            : []),
          ...(liveBehaviorViolateFG
            ? [
                {
                  value: BusinessDataType.BehavioralViolations,
                  label: BusinessChineseName[BusinessDataType.BehavioralViolations],
                },
              ]
            : []),
          ...(anchorImageState
            ? [
                {
                  value: BusinessDataType.AnchorImageViolation,
                  label: BusinessChineseName[BusinessDataType.AnchorImageViolation],
                },
              ]
            : []),
        ],
        [ModuleType.Fans]: [
          // { value: BusinessDataType.FollowersTotal, label: '粉丝总量' },
          // { value: BusinessDataType.AverageFollowersTotal, label: '店均总粉' },
          {
            value: BusinessDataType.FollowersIncrement,
            label: BusinessChineseName[BusinessDataType.FollowersIncrement],
          },
          ...(!isLastField
            ? [
                {
                  value: BusinessDataType.AverageFollowersIncrement,
                  label: BusinessChineseName[BusinessDataType.AverageFollowersIncrement],
                },
              ]
            : []),
        ],
        [ModuleType.Clue]: [
          {
            value: BusinessDataType.LeadsGenerated,
            label: BusinessChineseName[BusinessDataType.LeadsGenerated],
          },
          {
            value: BusinessDataType.LeadsFromLive,
            label: BusinessChineseName[BusinessDataType.LeadsFromLive],
          },
          {
            value: BusinessDataType.LeadsFromWorks,
            label: BusinessChineseName[BusinessDataType.LeadsFromWorks],
          },
          {
            value: BusinessDataType.LeadsMessages,
            label: BusinessChineseName[BusinessDataType.LeadsMessages],
          },
          {
            value: BusinessDataType.LeadsFromOther,
            label: BusinessChineseName[BusinessDataType.LeadsFromOther],
          },
          ...(liveBehaviorViolateFG
            ? [
                {
                  value: BusinessDataType.LeadsAverage,
                  label: BusinessChineseName[BusinessDataType.LeadsAverage],
                },
              ]
            : []),
        ],
        [ModuleType.Message]: [
          {
            value: BusinessDataType.MessagesSent,
            label: BusinessChineseName[BusinessDataType.MessagesSent],
          },
          {
            value: BusinessDataType.MessagesReceived,
            label: BusinessChineseName[BusinessDataType.MessagesReceived],
          },
          {
            value: BusinessDataType.MessagesSentTotal,
            label: BusinessChineseName[BusinessDataType.MessagesSentTotal],
          },
          {
            value: BusinessDataType.RepliesIn3Min,
            label: BusinessChineseName[BusinessDataType.RepliesIn3Min],
          },
          {
            value: BusinessDataType.RepliesNotIn3Min,
            label: BusinessChineseName[BusinessDataType.RepliesNotIn3Min],
          },
          ...(!isLastField
            ? [
                {
                  value: BusinessDataType.AverageMessagesSent,
                  label: BusinessChineseName[BusinessDataType.AverageMessagesSent],
                },
              ]
            : []),
        ],
      }),
      [liveBehaviorViolateFG, isLastField, anchorImageState],
    );

  const [moduleType, setModuleType] = useState<ModuleType>(ModuleType.Post);
  const [dataIndexType, setDataIndexType] = useState(selectOptionsMap[moduleType][0].value);
  const [viewMode, setViewMode] = useState<'chart' | 'table'>('chart');

  useEffect(() => {
    setDataIndexType(selectOptionsMap[moduleType][0].value);
  }, [moduleType, selectOptionsMap]);

  const typeSelectValueName = selectOptionsMap[moduleType].find(
    (item) => item.value === dataIndexType,
  )?.label;

  const { data: singleRankPie, loading: singleRankPieLoading } = useRequest(
    () =>
      BusinessContributionOverview({
        projectId,
        dateType,
        startDate: rangeTime?.[0],
        endDate: rangeTime?.[1],
        teamCodeList,
        tagIdList,
        type: 2,
        dataIndexType,
        fieldId,
        moduleType,
        platform,
      }),
    {
      ready: !!dateType && !!projectId && !!fieldId && !!platform,
      refreshDeps: [
        dateType,
        projectId,
        rangeTime,
        teamCodeList,
        tagIdList,
        moduleType,
        dataIndexType,
        fieldId,
        platform,
      ],
    },
  );

  const totalValue = singleRankPie?.length
    ? singleRankPie.reduce((acc, cur) => acc + Number(cur.value), 0)
    : '-';

  const pieOption = {
    grid: {
      containLabel: true,
      top: 10,
      bottom: 10,
      left: 10,
      right: 10,
    },
    title: {
      text: typeSelectValueName,
      subtext: totalValue
        ? dataIndexType === BusinessDataType.LiveDuration
          ? formatSecond(totalValue)
          : formatNum(totalValue)
        : '0',
      left: 'center', //对齐方式居中
      top: '43%', //距离顶部
      textStyle: {
        fontSize: 14, //字号
        color: isDarkMode ? '#E6E8EC' : '#000000',
        align: 'center', //对齐方式
        fontWeight: 500,
      },
      subtextStyle: {
        fontSize: 14,
        color: isDarkMode ? '#E6E8EC' : '#000000',
        align: 'center', //对齐方式
        fontWeight: 500,
      },
    },
    tooltip: {
      trigger: 'item',
      formatter: function (params: any) {
        return `${params.marker} ${params.name}: ${
          dataIndexType === BusinessDataType.LiveDuration
            ? formatSecond(params.value)
            : formatNum(params.value)
        }`;
      },
    },
    series: [
      {
        type: 'pie',
        radius: ['40%', '60%'],
        center: ['50%', '50%'],
        color: pieColor,
        label: {
          formatter: '{b|{b}}  {d|{d}%}',
          lineHeight: 15,
          rich: {
            b: {
              color: '#95979C',
              fontSize: 11,
            },
            d: {
              color: isDarkMode ? '#E6E8EC' : '#0E1015',
              fontSize: 12,
            },
          },
        },
        labelLine: {
          show: true,
          length: 8,
          length2: 8,
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 14,
            fontWeight: 'bold',
          },
        },
        data: singleRankPie,
        percentPrecision: 2,
        stillShowZeroSum: false,
      },
    ],
  };

  const { data: contributionList, loading: contributionListLoading } = useRequest(
    () =>
      BusinessContributionList({
        projectId,
        dateType,
        startDate: rangeTime?.[0],
        endDate: rangeTime?.[1],
        teamCodeList,
        tagIdList,
        type: 2,
        fieldId,
        moduleType,
        dataIndexType,
        platform,
      }),
    {
      ready: !!dateType && !!projectId && !!fieldId && !!platform,
      refreshDeps: [
        dateType,
        projectId,
        rangeTime,
        teamCodeList,
        tagIdList,
        fieldId,
        moduleType,
        dataIndexType,
        platform,
      ],
    },
  );

  const { data: contributionRank, loading: contributionRankLoading } = useRequest(
    () =>
      BusinessContributionTop({
        projectId,
        dateType,
        startDate: rangeTime?.[0],
        endDate: rangeTime?.[1],
        teamCodeList,
        tagIdList,
        type: 2,
        dataIndexType,
        fieldId,
        moduleType,
        platform,
      }),
    {
      ready: !!dateType && !!projectId && !!fieldId && !!platform,
      refreshDeps: [
        dateType,
        projectId,
        rangeTime,
        teamCodeList,
        tagIdList,
        moduleType,
        dataIndexType,
        fieldId,
        platform,
      ],
    },
  );

  const redTableData = contributionRank?.topUpList;
  const blackTableData = contributionRank?.topDownList;

  return (
    <>
      <Space>
        <Radio.Group
          value={moduleType}
          onChange={(e: RadioChangeEvent) => setModuleType(e.target.value)}
          buttonStyle="solid"
          style={{ whiteSpace: 'nowrap', marginLeft: 'auto' }}
        >
          <Radio.Button value={ModuleType.Post}>作品</Radio.Button>
          <Radio.Button value={ModuleType.Live}>直播</Radio.Button>
          <Radio.Button value={ModuleType.Message}>私信</Radio.Button>
          <Radio.Button value={ModuleType.Fans}>粉丝</Radio.Button>
          <Radio.Button value={ModuleType.Clue}>线索</Radio.Button>
        </Radio.Group>
        <Select
          value={dataIndexType}
          style={{ width: 300 }}
          onChange={(value) => setDataIndexType(value)}
          options={selectOptionsMap[moduleType]}
        />
      </Space>

      <div className="mt-2.5 flex gap-6">
        <div className="flex-1">
          <div className="min-h-[300px] rounded-lg border bg-white px-[20px] pb-[10px] pt-[10px]">
            <div className="flex items-center justify-between">
              <div className="text-base font-bold">贡献分布榜Top 12</div>
              <Radio.Group
                value={viewMode}
                onChange={(e) => setViewMode(e.target.value)}
                className="flex items-center"
              >
                <Radio.Button value="chart">
                  <PieChartOutlined />
                </Radio.Button>
                <Radio.Button value="table">
                  <TableOutlined />
                </Radio.Button>
              </Radio.Group>
            </div>

            {viewMode === 'chart' ? (
              <ReactECharts
                option={pieOption}
                notMerge={true}
                key="single-pie-charts"
                showLoading={singleRankPieLoading}
              />
            ) : (
              <ContributionTable
                fieldName={fieldName}
                type="single"
                typeSelectValueName={typeSelectValueName}
                data={contributionList}
                loading={contributionListLoading}
              />
            )}
          </div>
        </div>
        <div className="flex-1">
          <RedTable
            showDrawer={showDrawer}
            type="single"
            fieldName={fieldName}
            typeSelectValueName={typeSelectValueName}
            data={redTableData}
            loading={contributionRankLoading}
          />
        </div>
        <div className="flex-1">
          <BlackTable
            showDrawer={showDrawer}
            type="single"
            fieldName={fieldName}
            typeSelectValueName={typeSelectValueName}
            data={blackTableData}
            loading={contributionRankLoading}
          />
        </div>
      </div>

      <Row style={{ marginTop: '24px' }}>
        <Col span={24}>
          <DataDetailsTable
            type={2}
            moduleType={moduleType}
            dataIndexType={dataIndexType}
            projectId={projectId}
            dateType={dateType}
            rangeTime={rangeTime}
            teamCodeList={teamCodeList}
            tagIdList={tagIdList}
            fieldId={fieldId}
            fieldName={fieldName}
            liveBehaviorViolateFG={liveBehaviorViolateFG}
            anchorImageState={anchorImageState}
            platform={platform}
            storageKey={`SingleRank-${moduleType}-${dataIndexType}`}
          />
        </Col>
      </Row>
    </>
  );
};

export default SingleRank;
