import { BusinessTipsResult } from '@/services/business';

export const renderBusinessTips = (rangeTime?: string[], businessTips?: BusinessTipsResult[]) => {
  const tipTypeMap: Record<number, string> = {
    1: '线索总量',
    2: '涨粉总量',
  };
  const momTypeMap: Record<number, string> = {
    1: '环比上升',
    2: '环比下降',
  };
  const focusTypeMap: Record<number, string> = {
    1: '直播',
    2: '作品',
    3: '私信',
  };
  return (
    <>
      <span>
        {rangeTime?.[0]}至{rangeTime?.[1]}{' '}
      </span>
      {businessTips?.map((item) => {
        return (
          <span key={item.tipType}>
            {tipTypeMap[item.tipType]}转化{momTypeMap[item.momType]},其中
            {tipTypeMap[item.tipType]}
            {momTypeMap[item.momType]}较快的为{item.aggregationNameList.join(',')},需要重点关注
            {focusTypeMap[item.focusType]}相关指标的影响。
          </span>
        );
      })}
    </>
  );
};
