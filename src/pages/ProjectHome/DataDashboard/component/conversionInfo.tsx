import styled from 'styled-components';
import { Decline, Rise } from '../../style';
import { SvgIcon } from '@/components/SvgIcon';

const TrapezoidTop = styled.div`
  width: 320px;
  height: 50px;
  clip-path: polygon(0 0, 100% 0, 85% 100%, 15% 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 5px auto;
`;

const TrapezoidText = styled.div`
  font-size: 18px;
  font-weight: bold;
`;

const ConversionRate = styled.div`
  font-size: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const TrapezoidBottom = styled.div`
  width: 220px;
  height: 50px;
  clip-path: polygon(0 0, 100% 0, 85% 100%, 15% 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 5px auto;
`;

type ConversionInfoProps = {
  topText: string;
  bottomText: string;
  conversionRate?: string;
  momConversionRate?: string;
};

const ConversionInfo = (props: ConversionInfoProps) => {
  const { topText, bottomText, conversionRate, momConversionRate } = props;

  return (
    <>
      <TrapezoidTop style={{ backgroundColor: '#335ff5' }}>
        <TrapezoidText style={{ color: '#ffffff' }}>{topText}</TrapezoidText>
      </TrapezoidTop>
      <ConversionRate style={{ color: '#ffffff' }}>
        转化率 {conversionRate}%
        {momConversionRate && (
          <div style={{ marginLeft: '10px' }}>
            {Number(momConversionRate) >= 0 ? (
              <Rise style={{ color: '#f54848' }}>
                <SvgIcon icon={'local:outline/rate-rise'} />
                {momConversionRate}%
              </Rise>
            ) : (
              <Decline style={{ color: '#30b824' }}>
                <SvgIcon icon={'local:outline/rate-decline'} />
                {momConversionRate}%
              </Decline>
            )}
          </div>
        )}
      </ConversionRate>
      <TrapezoidBottom style={{ backgroundColor: '#78a0f8' }}>
        <TrapezoidText>{bottomText}</TrapezoidText>
      </TrapezoidBottom>
    </>
  );
};

export default ConversionInfo;
