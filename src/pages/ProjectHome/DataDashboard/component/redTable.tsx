import { BusinessTop, FieldList } from '@/services/business';
import MomData from '@/components/momData';
import { formatSecond } from '@/utils/time';
import { formatNum } from '@/utils/common';
import { QuestionCircleOutlined } from '@ant-design/icons';
import { RankBgMap } from '@/utils/commonStyle';
import { Tooltip } from 'antd';
import { cn } from '@/lib/utils';
import RedTableBg from '@/assets/red-table-bg.png';

type RedTableProps = {
  type: 'total' | 'single';
  showDrawer: (fieldList: FieldList[]) => void;
  fieldName?: string;
  typeSelectValueName?: string;
  data?: BusinessTop[];
  loading?: boolean;
};

const RedTable = (props: RedTableProps) => {
  const { type, showDrawer, fieldName, typeSelectValueName, data, loading } = props;

  const renderRank = (value: number) => {
    return RankBgMap[value] || <div>{value}</div>;
  };

  return (
    <div
      style={{
        background: 'linear-gradient(to bottom, #FFE4E7, #FFFFFF)',
        overflow: 'hidden',
        padding: '20px',
        paddingBottom: '5px',
        minHeight: '352px',
        borderRadius: '8px',
        border: '1px solid #EDEEF2',
        position: 'relative',
        width: '100%',
      }}
    >
      <img src={RedTableBg} className="absolute right-8 top-0 h-24" />

      <div className="relative">
        <div className="mb-4 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <span
              className="text-lg text-[#E71919]"
              style={{
                fontFamily: 'OPPOSans',
              }}
            >
              红榜-涨幅 Top 5
            </span>
            <Tooltip title="综合数据指标环比涨幅最大的排在前面">
              <QuestionCircleOutlined />
            </Tooltip>
          </div>
        </div>

        {loading ? (
          <div className="flex h-[300px] items-center justify-center">加载中...</div>
        ) : (
          <div className="no-scrollbar overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="w-[50px] whitespace-nowrap px-4 py-3 text-center">排名</th>
                  <th className="w-[200px] whitespace-nowrap px-4 py-3 text-center">{fieldName}</th>
                  {type === 'single' && (
                    <th className="w-[150px] whitespace-nowrap px-4 py-3 text-center">
                      {typeSelectValueName}
                    </th>
                  )}
                  <th className="w-[120px] whitespace-nowrap px-4 py-3 text-center">环比</th>
                  <th className="w-[100px] whitespace-nowrap px-4 py-3 text-center">操作</th>
                </tr>
              </thead>
              <tbody>
                {data?.map((item, index) => (
                  <tr
                    key={item.name + type + typeSelectValueName}
                    className={cn(index !== data.length - 1 && 'border-b')}
                  >
                    <td className="w-[50px] px-4 py-3 text-center">{renderRank(item.rank)}</td>
                    <td className="w-[200px] whitespace-nowrap px-4 py-3 text-center">
                      {item.name}
                    </td>
                    {type === 'single' && (
                      <td className="w-[150px] whitespace-nowrap px-4 py-3 text-center">
                        {typeSelectValueName === '直播时长'
                          ? formatSecond(item.value)
                          : formatNum(item.value)}
                      </td>
                    )}
                    <td className="w-[120px] whitespace-nowrap px-4 py-3 text-center">
                      <div className="flex justify-center">
                        <MomData rate={item.momRate} />
                      </div>
                    </td>
                    <td className="w-[100px] whitespace-nowrap px-4 py-3 text-center">
                      <button
                        className="bg-transparent text-blue-600 hover:text-blue-800"
                        onClick={() => showDrawer([{ fieldId: item.fieldId, value: item.name }])}
                      >
                        详情
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};

export default RedTable;
