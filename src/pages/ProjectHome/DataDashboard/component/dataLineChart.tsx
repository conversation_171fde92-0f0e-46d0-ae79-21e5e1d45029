import {
  BusinessTrend,
  BusinessTrendResult,
  DateType,
  FieldList,
  ModuleType,
} from '@/services/business';
import { PlatForm } from '@/utils/platform';
import { formatNum } from '@/utils/common';
import { renderXAxis } from '@/utils/time';
import { useRequest } from '@umijs/max';
import { Flex } from 'antd';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import EChartsReact from 'echarts-for-react';
import ReactECharts from 'echarts-for-react';
import { useEffect, useRef, useState } from 'react';

type DataLineChartProps = {
  isInDrawer?: boolean;
  rangeTime?: string[];
  projectId?: string;
  dateType?: DateType;
  teamCodeList?: string[];
  tagIdList?: (number | string)[];
  fieldList?: FieldList[];
  platform: PlatForm;
};

const DataLineChart = (props: DataLineChartProps) => {
  const {
    isInDrawer,
    dateType,
    rangeTime,
    projectId,
    teamCodeList,
    tagIdList,
    fieldList,
    platform,
  } = props;
  const xAxisData = renderXAxis(rangeTime || [], 'MM-DD');
  const [moduleType, setModuleType] = useState<ModuleType>(ModuleType.Post);
  const chartRef = useRef<EChartsReact | null>(null);

  const { data: trendData, loading: trendDataLoading } = useRequest(
    () =>
      BusinessTrend({
        projectId,
        dateType,
        startDate: rangeTime?.[0],
        endDate: rangeTime?.[1],
        moduleType,
        teamCodeList,
        tagIdList,
        fieldList,
        platform,
      }),
    {
      refreshDeps: [
        projectId,
        dateType,
        rangeTime,
        moduleType,
        teamCodeList,
        tagIdList,
        fieldList,
        platform,
      ],
    },
  );

  const barNameList = ['发布作品数', '直播场次', '线索总数', '私信人数'];

  const legendData = trendData?.map((item) => {
    const iconStyle = barNameList.includes(item.name) ? 'rect' : 'circle';
    return { name: item.name, icon: iconStyle };
  });

  const renderSeries = (data: BusinessTrendResult[]) => {
    const result: (
      | {
          name: string;
          type: string;
          barWidth: number;
          barGap: string;
          data: number[];
          yAxisIndex: number;
        }
      | {
          name: string;
          type: string;
          data: number[];
        }
    )[] = [];

    data?.forEach((item) => {
      if (barNameList.includes(item.name)) {
        result.push({
          name: item.name,
          type: 'bar',
          barWidth: 20,
          barGap: '30%',
          data: item.value,
          yAxisIndex: 1,
        });
      } else {
        result.push({
          name: item.name,
          type: 'line',
          data: item.value,
        });
      }
    });
    return result;
  };

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999',
        },
      },
      formatter: function (params: any) {
        let tooltipText = '<div style="text-align: left;">';
        params.forEach((param: any) => {
          tooltipText += `
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <div>${param.marker} ${param.seriesName}</div>
            &nbsp;&nbsp;<div style="color: #666; font-weight: 900; font-size: 14px;">${formatNum(
              param.value,
            )}</div>
          </div>
        `;
        });
        tooltipText += '</div>';
        return tooltipText;
      },
    },
    legend: {
      data: legendData,
      textStyle: {
        color: 'gray',
      },
    },
    grid: {
      top: '8%',
      left: '0%',
      right: '0%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: [
      {
        type: 'category',
        data: xAxisData,
        axisPointer: {
          type: 'shadow',
        },
        axisLabel: {
          show: true,
          fontSize: 12,
          interval: 'auto',
          rotate: 45,
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
      },
      {
        type: 'value',
        alignTicks: true,
      },
    ],
    series: renderSeries(trendData || []),
    dataZoom: [
      {
        type: 'inside',
        show: Number(xAxisData?.length) >= 20,
        start: 0,
        end: 50,
        zoomOnMouseWheel: true,
        moveOnMouseMove: true,
        moveOnMouseWheel: true,
        preventDefaultMouseMove: true,
      },
    ],
  };

  const typeName: Record<ModuleType, string> = {
    [ModuleType.Post]: '作品',
    [ModuleType.Live]: '直播',
    [ModuleType.Message]: '私信',
    [ModuleType.Fans]: '粉丝',
    [ModuleType.Clue]: '线索',
  };

  // 抽屉内这个图表首次会缩在一起没有成功resize 手动刷新resize一下
  useEffect(() => {
    if (chartRef.current && isInDrawer) {
      chartRef.current.getEchartsInstance().resize();
    }
  }, [chartRef.current, isInDrawer]);

  return (
    <>
      <Flex className="mb-5" align="center">
        <div className="text-base font-medium">{typeName[moduleType]}趋势</div>
        <Tabs
          value={moduleType}
          onValueChange={(value) => setModuleType(value as unknown as ModuleType)}
          className="ml-auto whitespace-nowrap"
        >
          <TabsList value={moduleType}>
            <TabsTrigger value={ModuleType.Post}>作品</TabsTrigger>
            <TabsTrigger value={ModuleType.Live}>直播</TabsTrigger>
            <TabsTrigger value={ModuleType.Message}>私信</TabsTrigger>
            <TabsTrigger value={ModuleType.Fans}>粉丝</TabsTrigger>
            <TabsTrigger value={ModuleType.Clue}>线索</TabsTrigger>
          </TabsList>
        </Tabs>
      </Flex>
      <ReactECharts
        showLoading={trendDataLoading}
        option={option}
        notMerge={true}
        ref={chartRef}
        style={{ height: '335px', width: '100%', display: 'flex', justifyContent: 'center' }}
      />
    </>
  );
};

export default DataLineChart;
