import { Flex } from 'antd';
import { SvgIcon } from '@/components/SvgIcon';
import styled from 'styled-components';

const Rise = styled.div`
  font-family: OPPOSans;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 1px;
  float: right;
`;

const Decline = styled.div`
  font-family: OPPOSans;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 1px;
  float: right;
`;

type MomDetailInfoProps = {
  data?: {
    title?: string;
    momNumber?: string;
    currentNumber?: string;
    previousNumber?: string;
  };
};

const MomDetailInfo = (props: MomDetailInfoProps) => {
  const { data } = props;

  return (
    <Flex vertical>
      <div>
        <span
          style={{
            color: '#989898',
            fontSize: 12,
            fontWeight: '400',
            marginRight: '3px',
          }}
        >
          当前周期:
        </span>
        <span>{data?.currentNumber}</span>
        {data?.momNumber && (
          <>
            {Number(data?.momNumber) >= 0 ? (
              <Rise style={{ color: '#f54848' }}>
                <SvgIcon icon={'local:outline/rate-rise'} />
                {data?.momNumber}%
              </Rise>
            ) : (
              <Decline style={{ color: '#30b824' }}>
                <SvgIcon icon={'local:outline/rate-decline'} />
                {data?.momNumber}%
              </Decline>
            )}
          </>
        )}
      </div>
      <div>
        <span
          style={{
            color: '#989898',
            fontSize: 12,
            fontWeight: '400',
            marginRight: '3px',
          }}
        >
          上一周期:
        </span>
        <span>{data?.previousNumber}</span>
      </div>
    </Flex>
  );
};

export default MomDetailInfo;
