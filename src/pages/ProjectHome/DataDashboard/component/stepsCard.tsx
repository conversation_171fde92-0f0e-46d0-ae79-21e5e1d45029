import { BusinessResult } from '@/services/business';
import { formatNum } from '@/utils/common';
import { formatSecond } from '@/utils/time';
import { DoubleRightOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import { Divider, Flex, Popover, Skeleton, Tooltip } from 'antd';
import { debounce } from 'lodash-es';
import { useEffect, useRef, useState } from 'react';
import ConversionInfo from './conversionInfo';
import MomData from './momData';
import styles from './styles.module.less';

type StepsCardProps = {
  stepsData?: BusinessResult;
  loading?: boolean;
  liveBehaviorViolateFG?: boolean;
  afkState?: boolean;
  anchorImageState?: boolean;
};

const StepsCard = (props: StepsCardProps) => {
  const { stepsData, loading, liveBehaviorViolateFG, afkState, anchorImageState } = props;

  const firstStepTitleRef = useRef<HTMLDivElement | null>(null);
  const secondStepTitleRef = useRef<HTMLDivElement | null>(null);
  const thirdStepTitleRef = useRef<HTMLDivElement | null>(null);
  const [margins, setMargins] = useState({
    firstStepMarginRight: '0px',
    secondStepMarginLeft: '0px',
    secondStepMarginRight: '0px',
    thirdStepMarginLeft: '0px',
  });

  const updateMargin = () => {
    if (firstStepTitleRef.current && secondStepTitleRef.current && thirdStepTitleRef.current) {
      const firstStepStyles = window.getComputedStyle(firstStepTitleRef.current);
      const secondStepStyles = window.getComputedStyle(secondStepTitleRef.current);
      const thirdStepStyles = window.getComputedStyle(thirdStepTitleRef.current);
      setMargins({
        firstStepMarginRight: firstStepStyles.marginRight,
        secondStepMarginLeft: secondStepStyles.marginLeft,
        secondStepMarginRight: secondStepStyles.marginRight,
        thirdStepMarginLeft: thirdStepStyles.marginLeft,
      });
    }
  };

  useEffect(() => {
    const debouncedUpdateMargins = debounce(updateMargin, 1000);
    debouncedUpdateMargins();
    window.addEventListener('resize', debouncedUpdateMargins);
    return () => {
      window.removeEventListener('resize', debouncedUpdateMargins);
    };
  }, []);

  return (
    <div
      style={{
        width: '100%',
        marginBlock: '10px',
        padding: '20px',
        background:
          'linear-gradient(90deg, rgba(254, 253, 242, 0.60) 0%, rgba(244, 255, 252, 0.60) 50%, rgba(255, 247, 247, 0.60) 100%)',
      }}
    >
      <Flex align="center">
        <div style={{ flex: 1, textAlign: 'center' }}>
          <div
            className={styles.stepTitle}
            style={{
              background: '#F6BC64',
            }}
            ref={firstStepTitleRef}
          >
            勤奋度
          </div>
        </div>
        <div
          className={styles.stepsSubTitle}
          style={
            {
              '--left-width': margins.firstStepMarginRight,
              '--right-width': margins.secondStepMarginLeft,
              '--left-color': '#ecbe72',
              '--right-color': '#62b793',
            } as React.CSSProperties
          }
        >
          <span>促进</span>
          <DoubleRightOutlined />
        </div>
        <div style={{ flex: 2, textAlign: 'center' }}>
          <div
            className={styles.stepTitle}
            style={{
              background: '#30BA91',
            }}
            ref={secondStepTitleRef}
          >
            影响力
          </div>
        </div>
        <div
          className={styles.stepsSubTitle}
          style={
            {
              '--left-width': margins.secondStepMarginRight,
              '--right-width': margins.thirdStepMarginLeft,
              '--left-color': '#62b793',
              '--right-color': '#f54848',
            } as React.CSSProperties
          }
        >
          <span>提升</span>
          <DoubleRightOutlined />
        </div>
        <div style={{ flex: 1, textAlign: 'center' }}>
          <div
            className={styles.stepTitle}
            style={{
              background: '#F54848',
            }}
            ref={thirdStepTitleRef}
          >
            转化力
          </div>
        </div>
      </Flex>

      <Flex gap={30}>
        <div
          className={styles.cardContainer}
          style={{ flex: 1, minWidth: '287px', backgroundColor: '#fefdf3' }}
        >
          <Skeleton active loading={loading} style={{ minHeight: '370px' }}>
            <div className={styles.card}>
              <div className={styles.title}>
                作品发布数
                <Tooltip title="统计周期内发布的短视频数量">
                  <QuestionCircleOutlined className="ml-1" />
                </Tooltip>
              </div>
              <div className={styles.number}>{formatNum(stepsData?.newPostCount)}</div>
              <MomData
                momNumber={stepsData?.momNewPostCountRate}
                fontSize="14px"
                marginInline="10px"
                momDetailInfo={{
                  title: '作品发布数',
                  momNumber: stepsData?.momNewPostCountRate,
                  currentNumber: `${formatNum(stepsData?.newPostCount)}`,
                  previousNumber: `${formatNum(stepsData?.momNewPostCount)}`,
                }}
              />
            </div>

            <Divider type="vertical" style={{ height: '100%' }} />
            <div className={styles.card}>
              <div className={styles.title}>
                直播场次
                <Tooltip title="统计周期内开播次数">
                  <QuestionCircleOutlined className="ml-1" />
                </Tooltip>
              </div>
              <div className={styles.number}>{formatNum(stepsData?.liveCount)}</div>
              <MomData
                momNumber={stepsData?.momLiveCountRate}
                fontSize="14px"
                marginInline="10px"
                momDetailInfo={{
                  title: '直播场次',
                  momNumber: stepsData?.momLiveCountRate,
                  currentNumber: `${formatNum(stepsData?.liveCount)}`,
                  previousNumber: `${formatNum(stepsData?.momLiveCount)}`,
                }}
              />

              <div className={styles.detailInfo}>
                <div className={styles.description}>
                  <span className="color-[#777] text-xs">
                    直播时长
                    <Tooltip title="统计周期内累计的直播时长">
                      <QuestionCircleOutlined className="ml-1" />
                    </Tooltip>{' '}
                    :
                  </span>
                  <span className="data">{formatSecond(stepsData?.liveDuration)}</span>
                  <MomData
                    momNumber={stepsData?.momLiveDurationRate}
                    momDetailInfo={{
                      title: '直播时长',
                      momNumber: stepsData?.momLiveDurationRate,
                      currentNumber: `${formatSecond(stepsData?.liveDuration)}`,
                      previousNumber: `${formatSecond(stepsData?.momLiveDuration)}`,
                    }}
                  />
                </div>
                {afkState && (
                  <div className={styles.description}>
                    <span className="color-[#777] text-xs">
                      空挂播场次
                      <Tooltip title="统计周期内有空挂播违规的直播场次数">
                        <QuestionCircleOutlined className="ml-1" />
                      </Tooltip>{' '}
                      :
                    </span>
                    <span className="data">{formatNum(stepsData?.liveAfkCount)}</span>
                    <MomData
                      momNumber={stepsData?.momLiveAfkCountRate}
                      momDetailInfo={{
                        title: '空挂播场次',
                        momNumber: stepsData?.momLiveAfkCountRate,
                        currentNumber: `${formatNum(stepsData?.liveAfkCount)}`,
                        previousNumber: `${formatNum(stepsData?.momLiveAfkCount)}`,
                      }}
                    />
                  </div>
                )}
                <div className={styles.description}>
                  <span className="color-[#777] text-xs">
                    敏感词场次
                    <Tooltip title="统计周期内直播中存在敏感词违规的直播场次数">
                      <QuestionCircleOutlined className="ml-1" />
                    </Tooltip>{' '}
                    :
                  </span>
                  <span className="data">{formatNum(stepsData?.liveSensitiveCount)}</span>
                  <MomData
                    momNumber={stepsData?.momLiveSensitiveCountRate}
                    momDetailInfo={{
                      title: '敏感词场次',
                      momNumber: stepsData?.momLiveSensitiveCountRate,
                      currentNumber: `${formatNum(stepsData?.liveSensitiveCount)}`,
                      previousNumber: `${formatNum(stepsData?.momLiveSensitiveCount)}`,
                    }}
                  />
                </div>
                {liveBehaviorViolateFG && (
                  <div className={styles.description}>
                    <span className="color-[#777] text-xs">
                      行为违规场次
                      <Tooltip title="统计周期内直播中存在行为违规的直播场次数。例如：物料违规、画面报价违规等">
                        <QuestionCircleOutlined className="ml-1" />
                      </Tooltip>{' '}
                      :
                    </span>
                    <span className="data">{formatNum(stepsData?.liveBehaviorCount)}</span>
                    <MomData
                      momNumber={stepsData?.momLiveBehaviorCountRate}
                      momDetailInfo={{
                        title: '行为违规场次',
                        momNumber: stepsData?.momLiveBehaviorCountRate,
                        currentNumber: `${formatNum(stepsData?.liveBehaviorCount)}`,
                        previousNumber: `${formatNum(stepsData?.momLiveBehaviorCount)}`,
                      }}
                    />
                  </div>
                )}
                {anchorImageState && (
                  <div className={styles.description}>
                    <span className="color-[#777] text-xs">
                      主播形象违规
                      <Tooltip title="主要包含主播形象违规（着装违规、发型凌乱、妆容浓厚、眼妆浓厚、指甲过长、配饰繁琐）检测">
                        <QuestionCircleOutlined className="ml-1" />
                      </Tooltip>{' '}
                      :
                    </span>
                    <span className="data">{formatNum(stepsData?.anchorImageViolationCount)}</span>
                    <MomData
                      momNumber={stepsData?.momAnchorImageViolationRate}
                      momDetailInfo={{
                        title: '主播形象违规',
                        momNumber: stepsData?.momAnchorImageViolationRate,
                        currentNumber: `${formatNum(stepsData?.anchorImageViolationCount)}`,
                        previousNumber: `${formatNum(stepsData?.momAnchorImageViolation)}`,
                      }}
                    />
                  </div>
                )}
              </div>
            </div>
          </Skeleton>
        </div>

        <div
          className={styles.cardContainer}
          style={{ flex: 2, minWidth: '470px', backgroundColor: '#f6fffc' }}
        >
          <Skeleton active loading={loading}>
            <div className={styles.card}>
              <div className={styles.title}>
                作品互动总数
                <Tooltip title="统计周期内发布的短视频获得的合计点赞+评论+分享次数">
                  <QuestionCircleOutlined className="ml-1" />
                </Tooltip>
              </div>
              <div className={styles.number}>{formatNum(stepsData?.postInteractionCount)}</div>
              <MomData
                momNumber={stepsData?.momPostInteractionCountRate}
                fontSize="14px"
                marginInline="10px"
                momDetailInfo={{
                  title: '作品互动总数',
                  momNumber: stepsData?.momPostInteractionCountRate,
                  currentNumber: `${formatNum(stepsData?.postInteractionCount)}`,
                  previousNumber: `${formatNum(stepsData?.momPostInteractionCount)}`,
                }}
              />

              <div className={styles.detailInfo}>
                <div className={styles.description}>
                  <span className="color-[#777] text-xs">
                    播放量
                    <Tooltip title="统计周期内发布的短视频的播放">
                      <QuestionCircleOutlined className="ml-1" />
                    </Tooltip>{' '}
                    :
                  </span>
                  <span className="data">{formatNum(stepsData?.postPlayCount)}</span>
                  <MomData
                    momNumber={stepsData?.momPostPlayCountRate}
                    momDetailInfo={{
                      title: '作品播放量',
                      momNumber: stepsData?.momPostPlayCountRate,
                      currentNumber: `${formatNum(stepsData?.postPlayCount)}`,
                      previousNumber: `${formatNum(stepsData?.momPostPlayCount)}`,
                    }}
                  />
                </div>
                <div className={styles.description}>
                  <span className="color-[#777] text-xs">
                    点赞数
                    <Tooltip title="统计周期内发布的短视频的点赞数">
                      <QuestionCircleOutlined className="ml-1" />
                    </Tooltip>{' '}
                    :
                  </span>
                  <span className="data">{formatNum(stepsData?.postDiggCount)}</span>
                  <MomData
                    momNumber={stepsData?.momPostDiggCountRate}
                    momDetailInfo={{
                      title: '作品点赞数',
                      momNumber: stepsData?.momPostDiggCountRate,
                      currentNumber: `${formatNum(stepsData?.postDiggCount)}`,
                      previousNumber: `${formatNum(stepsData?.momPostDiggCount)}`,
                    }}
                  />
                </div>
                <div className={styles.description}>
                  <span className="color-[#777] text-xs">
                    评论数
                    <Tooltip title="统计周期内发布的短视频的评论数">
                      <QuestionCircleOutlined className="ml-1" />
                    </Tooltip>{' '}
                    :
                  </span>
                  <span className="data">{formatNum(stepsData?.postCommentCount)}</span>
                  <MomData
                    momNumber={stepsData?.momPostCommentCountRate}
                    momDetailInfo={{
                      title: '作品评论数',
                      momNumber: stepsData?.momPostCommentCountRate,
                      currentNumber: `${formatNum(stepsData?.postCommentCount)}`,
                      previousNumber: `${formatNum(stepsData?.momPostCommentCount)}`,
                    }}
                  />
                </div>
                <div className={styles.description}>
                  <span className="color-[#777] text-xs">
                    分享数
                    <Tooltip title="统计周期内发布的短视频的分享数">
                      <QuestionCircleOutlined className="ml-1" />
                    </Tooltip>{' '}
                    :
                  </span>
                  <span className="data">{formatNum(stepsData?.postShareCount)}</span>
                  <MomData
                    momNumber={stepsData?.momPostShareCountRate}
                    momDetailInfo={{
                      title: '作品分享数',
                      momNumber: stepsData?.momPostShareCountRate,
                      currentNumber: `${formatNum(stepsData?.postShareCount)}`,
                      previousNumber: `${formatNum(stepsData?.momPostShareCount)}`,
                    }}
                  />
                </div>
              </div>
            </div>

            <Divider type="vertical" style={{ height: '100%' }} />

            <div className={styles.card}>
              <div className={styles.title}>
                直播观看次数
                <Tooltip title="统计周期内开播的累计观看次数">
                  <QuestionCircleOutlined className="ml-1" />
                </Tooltip>
              </div>
              <div className={styles.number}>{formatNum(stepsData?.liveViewTime)}</div>
              <MomData
                momNumber={stepsData?.momLiveViewTimeRate}
                fontSize="14px"
                marginInline="10px"
                momDetailInfo={{
                  title: '直播观看次数',
                  momNumber: stepsData?.momLiveViewTimeRate,
                  currentNumber: `${formatNum(stepsData?.liveViewTime)}`,
                  previousNumber: `${formatNum(stepsData?.momLiveViewTime)}`,
                }}
              />
              <div className={styles.detailInfo}>
                <div className={styles.description}>
                  <span className="color-[#777] text-xs">
                    观看人数
                    <Tooltip title="统计周期内开播的累计观看人数">
                      <QuestionCircleOutlined className="ml-1" />
                    </Tooltip>{' '}
                    :
                  </span>
                  <span className="data">{formatNum(stepsData?.liveViewCount)}</span>
                  <MomData
                    momNumber={stepsData?.momLiveViewCountRate}
                    momDetailInfo={{
                      title: '直播观看人数',
                      momNumber: stepsData?.momLiveViewCountRate,
                      currentNumber: `${formatNum(stepsData?.liveViewCount)}`,
                      previousNumber: `${formatNum(stepsData?.momLiveViewCount)}`,
                    }}
                  />
                </div>
                <div className={styles.description}>
                  <span className="color-[#777] text-xs">
                    直播点赞
                    <Tooltip title="统计周期内通过直播获得的累计点赞数">
                      <QuestionCircleOutlined className="ml-1" />
                    </Tooltip>{' '}
                    :
                  </span>
                  <span className="data">{formatNum(stepsData?.liveDiggCount)}</span>
                  <MomData
                    momNumber={stepsData?.momLiveDiggCountRate}
                    momDetailInfo={{
                      title: '直播点赞数',
                      momNumber: stepsData?.momLiveDiggCountRate,
                      currentNumber: `${formatNum(stepsData?.liveDiggCount)}`,
                      previousNumber: `${formatNum(stepsData?.momLiveDiggCount)}`,
                    }}
                  />
                </div>
                <div className={styles.description}>
                  <span className="color-[#777] text-xs">
                    直播弹幕
                    <Tooltip title="统计周期内通过直播获得的累计弹幕数">
                      <QuestionCircleOutlined className="ml-1" />
                    </Tooltip>{' '}
                    :
                  </span>
                  <span className="data">{formatNum(stepsData?.liveDmCount)}</span>
                  <MomData
                    momNumber={stepsData?.momLiveDmCountRate}
                    momDetailInfo={{
                      title: '直播弹幕数',
                      momNumber: stepsData?.momLiveDmCountRate,
                      currentNumber: `${formatNum(stepsData?.liveDmCount)}`,
                      previousNumber: `${formatNum(stepsData?.momLiveDmCount)}`,
                    }}
                  />
                </div>
              </div>
            </div>

            <Divider type="vertical" style={{ height: '100%' }} />

            <div className={styles.card}>
              <div className={styles.title}>
                私信人数
                <Tooltip title="统计周期内有开口发起咨询的用户人数">
                  <QuestionCircleOutlined className="ml-1" />
                </Tooltip>
              </div>
              <div className={styles.number}>{formatNum(stepsData?.douyinChatUserCount)}</div>
              <MomData
                momNumber={stepsData?.momDouyinChatThreeMinReplyCountRate}
                fontSize="14px"
                marginInline="10px"
                momDetailInfo={{
                  title: '私信人数',
                  momNumber: stepsData?.momDouyinChatThreeMinReplyCountRate,
                  currentNumber: `${formatNum(stepsData?.douyinChatUserCount)}`,
                  previousNumber: `${formatNum(stepsData?.momDouyinChatUserCount)}`,
                }}
              />
              <div className={styles.detailInfo}>
                <div className={styles.description}>
                  <span className="color-[#777] text-xs">
                    接收消息
                    <Tooltip title="统计周期内接收到的用户发起的消息次数">
                      <QuestionCircleOutlined className="ml-1" />
                    </Tooltip>{' '}
                    :
                  </span>
                  <span className="data">{formatNum(stepsData?.douyinChatReceiveCount)}</span>
                  <MomData
                    momNumber={stepsData?.momDouyinChatReceiveCountRate}
                    momDetailInfo={{
                      title: '接收消息',
                      momNumber: stepsData?.momDouyinChatReceiveCountRate,
                      currentNumber: `${formatNum(stepsData?.douyinChatReceiveCount)}`,
                      previousNumber: `${formatNum(stepsData?.momDouyinChatReceiveCount)}`,
                    }}
                  />
                </div>
                <div className={styles.description}>
                  <span className="color-[#777] text-xs">
                    3min回复
                    <Tooltip title="统计周期内有在3min内回复的私信消息数">
                      <QuestionCircleOutlined className="ml-1" />
                    </Tooltip>{' '}
                    :
                  </span>
                  <span className="data">{formatNum(stepsData?.douyinChatThreeMinReplyCount)}</span>
                  <MomData
                    momNumber={stepsData?.momDouyinChatThreeMinReplyCountRate}
                    momDetailInfo={{
                      title: '3min回复',
                      momNumber: stepsData?.momDouyinChatThreeMinReplyCountRate,
                      currentNumber: `${formatNum(stepsData?.douyinChatThreeMinReplyCount)}`,
                      previousNumber: `${formatNum(stepsData?.momDouyinChatThreeMinReplyCount)}`,
                    }}
                  />
                </div>
              </div>
            </div>
          </Skeleton>
        </div>

        <div
          className={styles.cardContainer}
          style={{ flex: 1, minWidth: '260px', backgroundColor: '#fdf7f7' }}
        >
          <Skeleton active loading={loading}>
            <div className={styles.card}>
              <div className={styles.title}>
                涨粉总数
                <Tooltip title="统计周期内通过账号下的新增粉丝总数">
                  <QuestionCircleOutlined className="ml-1" />
                </Tooltip>
              </div>
              <div className={styles.number}>{formatNum(stepsData?.newFollowerCount)}</div>
              <MomData
                momNumber={stepsData?.momNewFollowerCountRate}
                fontSize="14px"
                marginInline="10px"
                momDetailInfo={{
                  title: '涨粉总数',
                  momNumber: stepsData?.momNewFollowerCountRate,
                  currentNumber: `${formatNum(stepsData?.newFollowerCount)}`,
                  previousNumber: `${formatNum(stepsData?.momNewFollowerCount)}`,
                }}
              />
              <div className={styles.detailInfo}>
                <div className={styles.description}>
                  <span className="color-[#777] text-xs">
                    直播涨粉
                    <Tooltip title="统计周期内通过直播获得的新增粉丝数">
                      <QuestionCircleOutlined className="ml-1" />
                    </Tooltip>{' '}
                    :
                  </span>
                  <span className="data">{formatNum(stepsData?.liveNewFollowerCount)}</span>
                  <MomData
                    momNumber={stepsData?.momLiveNewFollowerCountRate}
                    momDetailInfo={{
                      title: '涨粉总数',
                      momNumber: stepsData?.momLiveNewFollowerCountRate,
                      currentNumber: `${formatNum(stepsData?.liveNewFollowerCount)}`,
                      previousNumber: `${formatNum(stepsData?.momLiveNewFollowerCount)}`,
                    }}
                  />
                </div>
                <div className={styles.description}>
                  <span className="color-[#777] text-xs">
                    其他涨粉
                    <Tooltip title="统计周期内，除了直播之外的途径获得的粉丝数">
                      <QuestionCircleOutlined className="ml-1" />
                    </Tooltip>{' '}
                    :
                  </span>
                  <span className="data">{formatNum(stepsData?.otherNewFollowerCount)}</span>
                  <MomData
                    momNumber={stepsData?.momOtherNewFollowerCountRate}
                    momDetailInfo={{
                      title: '其他涨粉',
                      momNumber: stepsData?.momOtherNewFollowerCountRate,
                      currentNumber: `${formatNum(stepsData?.otherNewFollowerCount)}`,
                      previousNumber: `${formatNum(stepsData?.momOtherNewFollowerCount)}`,
                    }}
                  />
                </div>
              </div>
            </div>

            <Divider type="vertical" style={{ height: '100%' }} />

            <div className={styles.card}>
              <div className={styles.title}>
                线索量
                <Tooltip title="统计周期内获得的总线索量">
                  <QuestionCircleOutlined className="ml-1" />
                </Tooltip>
              </div>
              <div className={styles.number}>{formatNum(stepsData?.clueCount)}</div>
              <MomData
                momNumber={stepsData?.momClueCountRate}
                fontSize="14px"
                marginInline="10px"
                momDetailInfo={{
                  title: '线索量',
                  momNumber: stepsData?.momClueCountRate,
                  currentNumber: `${formatNum(stepsData?.clueCount)}`,
                  previousNumber: `${formatNum(stepsData?.momClueCount)}`,
                }}
              />
              <div className={styles.detailInfo}>
                <div className={styles.description}>
                  <span className="color-[#777] text-xs">
                    直播线索
                    <Tooltip title="统计周期内通过直播获得的去重线索数">
                      <QuestionCircleOutlined className="ml-1" />
                    </Tooltip>{' '}
                    :
                  </span>
                  <span className="data">{formatNum(stepsData?.liveClueCount)}</span>
                  <MomData
                    momNumber={stepsData?.momLiveClueCountRate}
                    momDetailInfo={{
                      title: '直播线索',
                      momNumber: stepsData?.momLiveClueCountRate,
                      currentNumber: `${formatNum(stepsData?.liveClueCount)}`,
                      previousNumber: `${formatNum(stepsData?.momLiveClueCount)}`,
                    }}
                  />
                  {stepsData?.liveClueConversionRate && (
                    <Popover
                      content={
                        <ConversionInfo
                          topText={`直播观看人次${formatNum(stepsData?.liveViewTime)}`}
                          bottomText={`直播线索量${formatNum(stepsData?.liveClueCount)}`}
                          conversionRate={stepsData?.liveClueConversionRate}
                          momConversionRate={stepsData?.momLiveClueConversionRate}
                        />
                      }
                      placement="topLeft"
                      title="直播线索转化率"
                    >
                      <div className={styles.conversionRate}>
                        转化率: {stepsData?.liveClueConversionRate}%
                      </div>
                    </Popover>
                  )}
                </div>
                <div className={styles.description}>
                  <span className="color-[#777] text-xs">
                    作品线索
                    <Tooltip title="统计周期内通过短视频获得的去重线索数">
                      <QuestionCircleOutlined className="ml-1" />
                    </Tooltip>{' '}
                    :
                  </span>
                  <span className="data">{formatNum(stepsData?.postClueCount)}</span>
                  <MomData
                    momNumber={stepsData?.momPostClueCountRate}
                    momDetailInfo={{
                      title: '作品线索',
                      momNumber: stepsData?.momPostClueCountRate,
                      currentNumber: `${formatNum(stepsData?.postClueCount)}`,
                      previousNumber: `${formatNum(stepsData?.momPostClueCount)}`,
                    }}
                  />
                  {stepsData?.postClueConversionRate && (
                    <Popover
                      content={
                        <ConversionInfo
                          topText={`作品播放量${formatNum(stepsData?.postPlayCount)}`}
                          bottomText={`作品线索量${formatNum(stepsData?.postClueCount)}`}
                          conversionRate={stepsData?.postClueConversionRate}
                          momConversionRate={stepsData?.momPostClueConversionRate}
                        />
                      }
                      placement="topLeft"
                      title="作品线索转化率"
                    >
                      <div className={styles.conversionRate}>
                        转化率: {stepsData?.postClueConversionRate}%
                      </div>
                    </Popover>
                  )}
                </div>
                <div className={styles.description}>
                  <span className="color-[#777] text-xs">
                    私信线索
                    <Tooltip title="统计周期内通过私信获得的去重线索数">
                      <QuestionCircleOutlined className="ml-1" />
                    </Tooltip>{' '}
                    :
                  </span>
                  <span className="data">{formatNum(stepsData?.douyinChatClueCount)}</span>
                  <MomData
                    momNumber={stepsData?.momDouyinChatClueCountRate}
                    momDetailInfo={{
                      title: '私信线索',
                      momNumber: stepsData?.momDouyinChatClueCountRate,
                      currentNumber: `${formatNum(stepsData?.douyinChatClueCount)}`,
                      previousNumber: `${formatNum(stepsData?.momDouyinChatClueCount)}`,
                    }}
                  />
                  {stepsData?.postClueConversionRate && (
                    <Popover
                      content={
                        <ConversionInfo
                          topText={`私信开口人数${formatNum(stepsData?.douyinChatUserCount)}`}
                          bottomText={`私信线索量${formatNum(stepsData?.douyinChatClueCount)}`}
                          conversionRate={stepsData?.douyinChatClueConversionRate}
                          momConversionRate={stepsData?.momDouyinChatClueConversionRate}
                        />
                      }
                      placement="topLeft"
                      title="私信线索转化率"
                    >
                      <div className={styles.conversionRate}>
                        转化率: {stepsData?.douyinChatClueConversionRate}%
                      </div>
                    </Popover>
                  )}
                </div>
                <div className={styles.description}>
                  <span className="color-[#777] text-xs">
                    其他线索
                    <Tooltip title="统计周期内，除了直播、作品、私信之外的途径获得的去重线索数">
                      <QuestionCircleOutlined className="ml-1" />
                    </Tooltip>{' '}
                    :
                  </span>
                  <span className="data">{formatNum(stepsData?.otherClueCount)}</span>
                  <MomData
                    momNumber={stepsData?.momOtherClueCountRate}
                    momDetailInfo={{
                      title: '其他线索',
                      momNumber: stepsData?.momOtherClueCountRate,
                      currentNumber: `${formatNum(stepsData?.otherClueCount)}`,
                      previousNumber: `${formatNum(stepsData?.momOtherClueCount)}`,
                    }}
                  />
                </div>
              </div>
            </div>
          </Skeleton>
        </div>
      </Flex>
    </div>
  );
};

export default StepsCard;
