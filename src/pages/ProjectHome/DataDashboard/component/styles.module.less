.stepTitle {
  width: 173px;
  height: 40px;
  line-height: 40px;
  border-radius: 20px;
  margin: 0 auto;
  color: #fff;
  font-weight: 500;
}

.stepsSubTitle {
  width: 72px;
  height: 24px;
  background: #ffffff;
  border: 1px solid #e2e2e2;
  border-radius: 20px;
  text-align: center;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 45%;
    left: 0px;
    transform: translateX(-100%);
    width: var(--left-width, 0px);
    border: 0;
    border-top: 2px dashed var(--left-color, #a2a9b6);
  }

  &::after {
    content: '';
    position: absolute;
    top: 45%;
    right: 0px;
    transform: translateX(100%);
    width: var(--right-width, 0px);
    border: 0;
    border-top: 2px dashed var(--right-color, #a2a9b6);
  }

  span {
    font-size: 12px;
  }
}

.cardContainer {
  display: flex;
  justify-content: space-between;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  margin: 20px 0;
  transition: transform 0.3s ease-in-out;

  &:hover {
    transform: scale(1.1);
  }
}

.card {
  flex: 1;
  border-radius: 10px;
}

.title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-inline: 10px;
  gap: 5px;
}

.description {
  display: flex;
  align-items: center;
  justify-content: left;
  margin-inline: 10px;
  gap: 10px;
  flex-wrap: wrap;

  .title {
    font-size: 14px;
    color: #777;

    span {
      margin-left: 2px;
      transform: translateY(1px);
    }
  }

  .data {
    font-size: 16px;
    color: #0e1015;
    font-weight: 500;
  }
}

.number {
  font-size: 26px;
  color: #000;
  font-weight: 500;
  margin-inline: 10px;
}

.detailInfo {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-block: 20px;
}

.conversionRate {
  color: #1e5eff;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
}
