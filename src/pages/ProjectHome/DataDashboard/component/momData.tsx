import { Flex, Popover } from 'antd';
import styled from 'styled-components';
import { CaretDownOutlined, CaretUpOutlined } from '@ant-design/icons';
import MomDetailInfo from './momDetailInfo';

const Rise = styled.div`
  font-family: OPPOSans;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 1px;
`;

const Decline = styled.div`
  font-family: OPPOSans;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 1px;
`;

type MomDataProps = {
  momNumber?: string;
  fontSize?: string;
  marginInline?: string;
  momDetailInfo?: {
    title?: string;
    momNumber?: string;
    currentNumber?: string;
    previousNumber?: string;
  };
};

const MomData = (props: MomDataProps) => {
  const { momNumber, fontSize = '12px', marginInline, momDetailInfo } = props;

  if (!momNumber) return null;

  return (
    <Popover content={<MomDetailInfo data={momDetailInfo} />} title={momDetailInfo?.title}>
      <Flex vertical style={{ marginInline }}>
        {Number(momNumber) >= 0 ? (
          <Rise style={{ fontSize: fontSize, lineHeight: fontSize, color: '#f54848' }}>
            {momNumber}%
            <CaretUpOutlined style={{ marginLeft: '3px' }} />
          </Rise>
        ) : (
          <Decline style={{ fontSize: fontSize, lineHeight: fontSize, color: '#30b824' }}>
            {momNumber}%
            <CaretDownOutlined style={{ marginLeft: '3px' }} />
          </Decline>
        )}
      </Flex>
    </Popover>
  );
};

export default MomData;
