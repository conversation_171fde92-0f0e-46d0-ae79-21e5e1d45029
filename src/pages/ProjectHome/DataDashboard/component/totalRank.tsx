import {
  BusinessContributionList,
  BusinessContributionOverview,
  BusinessContributionTop,
  DateType,
  FieldList,
} from '@/services/business';
import { pieColor } from '@/utils/commonStyle';
import { useRequest } from '@umijs/max';
import { Col, Row, Segmented } from 'antd';
import ReactECharts from 'echarts-for-react';
import BlackTable from './blackTable';
import ContributionTable from './contributionTable';
import DataDetailsTable from './dataDetailsTable';
import RedTable from './redTable';
import { PlatForm } from '@/utils/platform';
import { useDarkMode } from '@/hooks/useDarkMode';
import { PieChartOutlined, TableOutlined } from '@ant-design/icons';
import { useState } from 'react';

type TotalRankProps = {
  showDrawer: (fieldList: FieldList[]) => void;
  fieldName?: string;
  projectId?: string;
  dateType?: DateType;
  rangeTime?: (string | undefined)[];
  teamCodeList?: string[];
  tagIdList?: string[];
  fieldId?: number;
  liveBehaviorViolateFG?: boolean;
  anchorImageState?: boolean;
  platform: PlatForm;
};

const TotalRank = (props: TotalRankProps) => {
  const {
    showDrawer,
    fieldName,
    projectId,
    dateType,
    rangeTime,
    teamCodeList,
    tagIdList,
    fieldId,
    liveBehaviorViolateFG,
    anchorImageState,
    platform,
  } = props;
  const { isDarkMode } = useDarkMode();
  const [viewMode, setViewMode] = useState<'chart' | 'table'>('chart');

  const { data: totalRankPie, loading: totalRankPieLoading } = useRequest(
    () =>
      BusinessContributionOverview({
        projectId,
        dateType,
        startDate: rangeTime?.[0],
        endDate: rangeTime?.[1],
        teamCodeList,
        tagIdList,
        type: 1,
        fieldId,
        platform,
      }),
    {
      ready: !!dateType && !!projectId && !!fieldId && !!platform,
      refreshDeps: [dateType, projectId, rangeTime, teamCodeList, tagIdList, fieldId, platform],
    },
  );

  const filterRankPie = totalRankPie?.map((item) => ({ name: item.name, value: item.percent }));

  const pieOption = {
    grid: {
      containLabel: true,
      top: 10,
      bottom: 10,
      left: 10,
      right: 10,
    },
    series: [
      {
        type: 'pie',
        radius: ['40%', '60%'],
        center: ['50%', '50%'],
        color: pieColor,
        label: {
          show: true,
          position: 'outside',
          formatter: '{b|{b}}  {d|{d}%}',
          lineHeight: 14,
          rich: {
            b: {
              color: '#95979C',
              fontSize: 11,
            },
            d: {
              color: isDarkMode ? '#E6E8EC' : '#0E1015',
              fontSize: 12,
            },
          },
        },
        labelLine: {
          show: true,
          length: 8,
          length2: 8,
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 14,
            fontWeight: 'bold',
          },
        },
        data: filterRankPie,
      },
    ],
  };

  const { data: contributionList, loading: contributionListLoading } = useRequest(
    () =>
      BusinessContributionList({
        projectId,
        dateType,
        startDate: rangeTime?.[0],
        endDate: rangeTime?.[1],
        teamCodeList,
        tagIdList,
        type: 1,
        fieldId,
        platform,
      }),
    {
      ready: !!dateType && !!projectId && !!fieldId && !!platform,
      refreshDeps: [dateType, projectId, rangeTime, teamCodeList, tagIdList, fieldId, platform],
    },
  );

  const { data: contributionRank, loading: contributionRankLoading } = useRequest(
    () =>
      BusinessContributionTop({
        projectId,
        dateType,
        startDate: rangeTime?.[0],
        endDate: rangeTime?.[1],
        teamCodeList,
        tagIdList,
        type: 1,
        fieldId,
        platform,
      }),
    {
      ready: !!dateType && !!projectId && !!fieldId && !!platform,
      refreshDeps: [dateType, projectId, rangeTime, teamCodeList, tagIdList, fieldId, platform],
    },
  );

  const redTableData = contributionRank?.topUpList;
  const blackTableData = contributionRank?.topDownList;

  return (
    <>
      <div className="mt-2.5 flex gap-6">
        <div className="flex-1">
          <div className="min-h-[300px] rounded-lg border bg-white px-[20px] pb-[10px] pt-[10px]">
            <div className="flex items-center justify-between">
              <div className="text-base font-bold">贡献分布情况</div>
              <Segmented
                options={[
                  {
                    label: <PieChartOutlined />,
                    value: 'chart',
                  },
                  {
                    label: <TableOutlined />,
                    value: 'table',
                  },
                ]}
                value={viewMode}
                onChange={(value) => setViewMode(value as 'chart' | 'table')}
              />
            </div>

            {viewMode === 'chart' ? (
              <ReactECharts
                option={pieOption}
                notMerge={true}
                key="total-pie-charts"
                showLoading={totalRankPieLoading}
              />
            ) : (
              <ContributionTable
                fieldName={fieldName}
                type="total"
                data={contributionList}
                loading={contributionListLoading}
              />
            )}
          </div>
        </div>
        <div className="flex-1">
          <RedTable
            fieldName={fieldName}
            showDrawer={showDrawer}
            type="total"
            data={redTableData}
            loading={contributionRankLoading}
          />
        </div>
        <div className="flex-1">
          <BlackTable
            fieldName={fieldName}
            showDrawer={showDrawer}
            type="total"
            data={blackTableData}
            loading={contributionRankLoading}
          />
        </div>
      </div>
      <Row>
        <Col span={24}>
          <DataDetailsTable
            type={1}
            projectId={projectId}
            dateType={dateType}
            rangeTime={rangeTime}
            teamCodeList={teamCodeList}
            tagIdList={tagIdList}
            fieldId={fieldId}
            fieldName={fieldName}
            liveBehaviorViolateFG={liveBehaviorViolateFG}
            anchorImageState={anchorImageState}
            platform={platform}
            storageKey={`TotalRank-DataDetailsTable`}
          />
        </Col>
      </Row>
    </>
  );
};

export default TotalRank;
