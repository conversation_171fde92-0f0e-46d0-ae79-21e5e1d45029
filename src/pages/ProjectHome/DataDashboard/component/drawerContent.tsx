import DynamicTree from '@/components/dynamicTree';
import TagsSelect from '@/components/tags/tagsSelect';
import TimeFilterDataDashboard from '@/components/timeFilterDataDashboard';
import useProjectId from '@/hooks/useProjectId';
import { BusinessOverview, BusinessTips, DateType, FieldList } from '@/services/business';
import { PlatForm } from '@/utils/platform';
import { getTimeByTypeBusiness } from '@/utils/time';
import { useRequest } from '@umijs/max';
import { Alert, Flex } from 'antd';
import { useEffect, useState } from 'react';
import { renderBusinessTips } from './businessTips';
import DataLineChart from './dataLineChart';
import StepsCard from './stepsCard';

type DrawerContentProps = {
  fieldList: FieldList[];
  drawerRangTime?: string[];
  drawerRadioValue?: DateType;
  drawerTagIdList?: string[];
  liveBehaviorViolateFG?: boolean;
  afkState?: boolean;
  anchorImageState?: boolean;
  platform: PlatForm;
};

const DrawerContent = (props: DrawerContentProps) => {
  const {
    fieldList,
    drawerRangTime,
    drawerRadioValue,
    drawerTagIdList,
    liveBehaviorViolateFG,
    afkState,
    anchorImageState,
    platform,
  } = props;
  const projectId = useProjectId();
  const [treeValue, setTreeValue] = useState<string[] | undefined>(undefined);
  const [radioValue, setRadioValue] = useState<DateType | undefined>(
    drawerRadioValue || DateType.LastWeek,
  );
  const [rangeTime, setRangeTime] = useState<string[]>(
    drawerRangTime || getTimeByTypeBusiness(DateType.LastWeek, projectId),
  );
  const [tagIdList, setTagIdList] = useState<string[]>([]);

  useEffect(() => {
    if (drawerRangTime) setRangeTime(drawerRangTime);
  }, [drawerRangTime]);

  useEffect(() => {
    if (drawerTagIdList) setTagIdList(drawerTagIdList);
  }, [drawerTagIdList]);

  useEffect(() => {
    if (drawerRadioValue) setRadioValue(drawerRadioValue);
  }, [drawerRadioValue]);

  const { data: stepsData, loading: stepsDataLoading } = useRequest(
    () =>
      BusinessOverview({
        projectId,
        dateType: radioValue,
        startDate: rangeTime?.[0],
        endDate: rangeTime?.[1],
        teamCodeList: treeValue,
        tagIdList,
        fieldList,
        platform,
      }),
    {
      ready: !!radioValue && !!projectId && !!fieldList && !!platform,
      refreshDeps: [radioValue, projectId, rangeTime, treeValue, tagIdList, fieldList, platform],
    },
  );

  const { data: businessTips } = useRequest(
    () =>
      BusinessTips({
        projectId,
        dateType: radioValue,
        startDate: rangeTime?.[0],
        endDate: rangeTime?.[1],
        teamCodeList: treeValue,
        tagIdList,
        fieldList,
        platform,
      }),
    {
      ready: !!radioValue && !!projectId && !!fieldList && !!platform,
      refreshDeps: [radioValue, projectId, rangeTime, treeValue, tagIdList, fieldList, platform],
    },
  );

  const onTagIdListChange = (value: any) => {
    setTagIdList(value);
  };

  return (
    <>
      <Flex>
        <DynamicTree
          value={treeValue}
          setValue={setTreeValue}
          style={{ width: '200px' }}
          nodeTitle={fieldList[0].value}
        />
        <TagsSelect
          style={{ width: '200px', marginLeft: '10px' }}
          value={tagIdList}
          onChange={onTagIdListChange}
        />
        <TimeFilterDataDashboard
          radioValue={radioValue}
          setRadioValue={setRadioValue}
          setRangeTime={setRangeTime}
          style={{ marginLeft: 'auto' }}
          firstShowRangeTime={drawerRangTime}
          projectId={projectId}
        />
      </Flex>

      {businessTips && businessTips?.length > 0 && (
        <Alert
          style={{ marginBlock: '10px' }}
          message={renderBusinessTips(rangeTime, businessTips)}
          type="info"
          showIcon
          closable
        />
      )}

      <StepsCard
        stepsData={stepsData}
        loading={stepsDataLoading}
        liveBehaviorViolateFG={liveBehaviorViolateFG}
        afkState={afkState}
        anchorImageState={anchorImageState}
      />

      <DataLineChart
        isInDrawer
        rangeTime={rangeTime}
        projectId={projectId}
        dateType={radioValue}
        teamCodeList={treeValue}
        tagIdList={tagIdList}
        fieldList={fieldList}
        platform={platform}
      />
    </>
  );
};

export default DrawerContent;
