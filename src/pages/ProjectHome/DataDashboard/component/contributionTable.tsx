import React from 'react';
import { ConfigProvider, Table } from 'antd';
import type { TableColumnsType } from 'antd';
import { ContributionListResult } from '@/services/business';
import { formatSecond } from '@/utils/time';
import { formatNum } from '@/utils/common';
import { RankBgMap } from '@/utils/commonStyle';

type ContributionTableProps = {
  type: 'total' | 'single';
  fieldName?: string;
  typeSelectValueName?: string;
  data?: ContributionListResult[];
  loading?: boolean;
};

const ContributionTable = (props: ContributionTableProps) => {
  const { type, fieldName, typeSelectValueName, data, loading } = props;

  const totalRankColumns: TableColumnsType<ContributionListResult> = [
    {
      title: '总和贡献排名',
      dataIndex: 'rank',
      align: 'center',
      width: 100,
      render: (_: any, record: any) => {
        return RankBgMap[record.rank] || <div>{record.rank}</div>;
      },
    },
    {
      title: `${fieldName}`,
      dataIndex: 'name',
      align: 'center',
    },
    {
      title: '综合贡献占比',
      dataIndex: 'percent',
      align: 'center',
      render: (value) => `${value}%`,
    },
  ];

  const singleRankColumns: TableColumnsType<ContributionListResult> = [
    {
      title: '贡献排名',
      dataIndex: 'rank',
      align: 'center',
      width: 100,
      render: (_: any, record: any) => {
        return RankBgMap[record.rank] || <div>{record.rank}</div>;
      },
    },
    {
      title: `${fieldName}`,
      dataIndex: 'name',
      align: 'center',
    },
    {
      title: `${typeSelectValueName}`,
      dataIndex: 'value',
      align: 'center',
      render: (value) =>
        typeSelectValueName === '直播时长' ? formatSecond(value) : formatNum(value),
    },
    {
      title: '贡献占比',
      dataIndex: 'percent',
      align: 'center',
      render: (value) => `${value}%`,
    },
  ];

  return (
    <div style={{ borderRadius: '4px', maxWidth: '600px' }}>
      <ConfigProvider
        theme={{
          components: {
            Table: {
              headerBorderRadius: 0,
            },
          },
        }}
      >
        <Table
          columns={type === 'total' ? totalRankColumns : singleRankColumns}
          size="middle"
          rowKey="name"
          loading={loading}
          dataSource={data}
          pagination={false}
          scroll={{ y: 240 }}
          style={{ marginTop: '10px' }}
        />
      </ConfigProvider>
    </div>
  );
};

export default ContributionTable;
