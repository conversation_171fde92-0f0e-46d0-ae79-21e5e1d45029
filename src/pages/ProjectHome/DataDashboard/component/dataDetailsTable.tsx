import ExportButton from '@/components/exportButton';
import { ColumnsState, ProColumns, ProTable } from '@ant-design/pro-components';
import { customPaginationRender } from '../../style';
import {
  BusinessChineseName,
  BusinessDataType,
  BusinessDetail,
  BusinessDetailExport,
  BusinessDetailResult,
  DateType,
  ModuleType,
} from '@/services/business';
import { useEffect, useMemo, useRef, useState } from 'react';
import MomData from '@/components/momData';
import { formatSecond } from '@/utils/time';
import { formatNum } from '@/utils/common';
import { usePollingExport } from '@/hooks/usePollingExport';
import { message } from 'antd';
import { RankBgMap } from '@/utils/commonStyle';
import { PlatForm } from '@/utils/platform';
import { proTableOptionsConfig } from '@/utils/proTableConfig';

const defaultColumnsState = {
  [BusinessDataType.WorksPublished]: {
    show: true,
  },
  [BusinessDataType.WorksInteractions]: {
    show: true,
  },
  [BusinessDataType.WorksViewsIncrement]: {
    show: false,
  },
  [BusinessDataType.WorksLikesIncrement]: {
    show: false,
  },
  [BusinessDataType.WorksCommentsIncrement]: {
    show: false,
  },
  [BusinessDataType.WorksSharesIncrement]: {
    show: false,
  },
  [BusinessDataType.LiveSessions]: {
    show: true,
  },
  [BusinessDataType.LiveDuration]: {
    show: false,
  },
  [BusinessDataType.LiveViewers]: {
    show: false,
  },
  [BusinessDataType.LiveViewerTimes]: {
    show: true,
  },
  [BusinessDataType.LiveDanmuCount]: {
    show: false,
  },
  [BusinessDataType.LiveLikes]: {
    show: false,
  },
  [BusinessDataType.IdleLiveSessions]: {
    show: false,
  },
  [BusinessDataType.SensitivityViolations]: {
    show: false,
  },
  [BusinessDataType.BehavioralViolations]: {
    show: false,
  },
  // [BusinessDataType.FollowersTotal]: {
  //   show: true,
  // },
  [BusinessDataType.FollowersIncrement]: {
    show: true,
  },
  // [BusinessDataType.AverageFollowersTotal]: {
  //   show: false,
  // },
  [BusinessDataType.AverageFollowersIncrement]: {
    show: false,
  },
  [BusinessDataType.MessagesSent]: {
    show: true,
  },
  [BusinessDataType.MessagesReceived]: {
    show: false,
  },
  [BusinessDataType.MessagesSentTotal]: {
    show: false,
  },
  [BusinessDataType.RepliesIn3Min]: {
    show: false,
  },
  [BusinessDataType.RepliesNotIn3Min]: {
    show: false,
  },
  [BusinessDataType.AverageMessagesSent]: {
    show: false,
  },
  [BusinessDataType.LeadsGenerated]: {
    show: true,
  },
  [BusinessDataType.LeadsFromLive]: {
    show: false,
  },
  [BusinessDataType.LeadsFromWorks]: {
    show: false,
  },
  [BusinessDataType.LeadsMessages]: {
    show: false,
  },
  [BusinessDataType.LeadsFromOther]: {
    show: false,
  },
  [BusinessDataType.LeadsAverage]: {
    show: false,
  },
};

type DataDetailsTableProps = {
  type: number;
  moduleType?: ModuleType;
  dataIndexType?: BusinessDataType;
  projectId?: string;
  dateType?: DateType;
  rangeTime?: (string | undefined)[];
  teamCodeList?: string[];
  tagIdList?: string[];
  fieldId?: number;
  fieldName?: string;
  liveBehaviorViolateFG?: boolean;
  anchorImageState?: boolean;
  platform: PlatForm;
  storageKey?: string;
};

const DataDetailsTable = (props: DataDetailsTableProps) => {
  const {
    type,
    moduleType,
    dataIndexType,
    projectId,
    dateType,
    rangeTime,
    teamCodeList,
    tagIdList,
    fieldId,
    fieldName,
    liveBehaviorViolateFG,
    anchorImageState,
    platform,
    storageKey,
  } = props;

  const defaultColumns: ProColumns<any>[] = useMemo(
    () => [
      {
        title: '排名',
        dataIndex: 'rank',
        width: 100,
        align: 'center',
        fixed: 'left',
        key: 'rank',

        render: (_: any, record: any) => {
          return RankBgMap[record.rank] || <div>{record.rank}</div>;
        },
      },
      {
        title: `${fieldName}`,
        dataIndex: 'name',
        align: 'center',
        fixed: 'left',
        key: 'name',
      },
    ],
    [fieldName],
  );

  const [columns, setColumns] = useState<ProColumns<any>[]>(defaultColumns);
  const firstAddColumns = useRef(false);
  const [columnsStateMap, setColumnsStateMap] = useState<Record<string, ColumnsState>>(
    type === 1 ? defaultColumnsState : {},
  );
  const { pollingExport, percent, pollingLoading } = usePollingExport();

  useEffect(() => {
    if (fieldName) {
      setColumns(defaultColumns);
    }
  }, [defaultColumns, fieldName]);

  useEffect(() => {
    setColumns(defaultColumns);
    firstAddColumns.current = false;
  }, [defaultColumns, moduleType]);

  // 用来对单指标的情况进行表头标题高亮与排序到前
  useEffect(() => {
    if (dataIndexType && columns) {
      const updatedColumns = columns
        .sort((a, b) => {
          if (a.dataIndex === dataIndexType) {
            return -1; // Move the column with dataIndex === dataIndexType to the front
          } else if (b.dataIndex === dataIndexType) {
            return 1;
          } else {
            return 0;
          }
        })
        .map((item) => {
          if (item.dataIndex === dataIndexType) {
            return {
              ...item,
              className: 'highlight-table-header',
            };
          } else {
            return {
              ...item,
              className: '',
            };
          }
        });
      setColumns(updatedColumns);
    }
  }, [dataIndexType]);

  const handleExport = async () => {
    const res = await BusinessDetailExport({
      projectId,
      dateType,
      startDate: rangeTime?.[0],
      endDate: rangeTime?.[1],
      teamCodeList,
      tagIdList,
      type,
      dataIndexType,
      fieldId,
      moduleType,
      platform,
    });
    if (res.data) {
      pollingExport({ taskId: res.data });
    } else {
      message.error('导出失败');
    }
  };

  return (
    <ProTable
      rowKey={(record) => record.name + record.rank + type + moduleType + dataIndexType}
      ghost
      scroll={{ x: 'max-content' }}
      tableClassName="custom-table"
      columns={columns}
      search={false}
      headerTitle="数据明细"
      columnsState={{
        value: columnsStateMap,
        onChange: setColumnsStateMap,
        persistenceKey: storageKey,
        persistenceType: 'localStorage',
      }}
      options={{ ...proTableOptionsConfig }}
      params={{
        projectId,
        dateType,
        startDate: rangeTime?.[0],
        endDate: rangeTime?.[1],
        teamCodeList,
        tagIdList,
        type,
        dataIndexType,
        fieldId,
        moduleType,
        platform,
      }}
      postData={(data: BusinessDetailResult[]) => {
        if (!firstAddColumns.current && data.length > 0) {
          const { dataIndexDetailList } = data[0];
          if (dataIndexDetailList?.length) {
            dataIndexDetailList?.map((item) => {
              const { dataIndexType: dataIndexTypeData } = item;
              if (
                !liveBehaviorViolateFG &&
                dataIndexTypeData === BusinessDataType.BehavioralViolations
              ) {
                return;
              }
              if (
                !anchorImageState &&
                dataIndexTypeData === BusinessDataType.AnchorImageViolation
              ) {
                return;
              }
              setColumns((prev) => [
                ...prev,
                {
                  title: BusinessChineseName[dataIndexTypeData],
                  dataIndex: dataIndexTypeData,
                  key: dataIndexTypeData,
                  align: 'center',
                  className: dataIndexTypeData === dataIndexType ? 'highlight-table-header' : '',
                  render: (_, record) => {
                    return (
                      <>
                        <span>
                          {dataIndexTypeData === BusinessDataType.LiveDuration
                            ? formatSecond(record[dataIndexTypeData])
                            : formatNum(record[dataIndexTypeData])}
                        </span>
                        <MomData rate={record[`${dataIndexTypeData}-MomRate`]} />
                      </>
                    );
                  },
                },
              ]);
            });
            firstAddColumns.current = true;
          }
        }
        return data?.map((businessDetailItem) => {
          const { name, rank, dataIndexDetailList } = businessDetailItem;
          const result = {
            name,
            rank,
            ...(dataIndexDetailList?.reduce(
              (acc: { [key: string]: any }, { dataIndexType, value, momRate }) => {
                acc[dataIndexType] = value;
                acc[`${dataIndexType}-MomRate`] = momRate;
                return acc;
              },
              {} as { [key: string]: any },
            ) || {}),
          };
          return result;
        });
      }}
      request={async (params) => {
        if (!fieldId || !dateType || !type) {
          return {
            data: [],
            success: true,
            total: 0,
          };
        }
        const result = await BusinessDetail(params);
        return {
          data: result.data,
          success: result.code === 0,
          total: result.data?.length,
        };
      }}
      toolBarRender={() => [
        <ExportButton
          exportFn={() => handleExport()}
          loading={pollingLoading}
          percent={percent}
          key="export"
        />,
      ]}
      pagination={{
        defaultPageSize: 10,
        hideOnSinglePage: true,
        showTotal: (total, range) => `${range[0]}-${range[1]} 共 ${total} 条`,
        itemRender: customPaginationRender,
      }}
    />
  );
};

export default DataDetailsTable;
