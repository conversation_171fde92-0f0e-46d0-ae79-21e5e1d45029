import useTabKeySearchParams from '@/hooks/useTabKeySearchParams';
import { PageContainer } from '@ant-design/pro-components';
import { Tabs, TabsProps } from 'antd';
import Remind from './component/remind';
import PlatformSwitch from '@/components/platformSwitch';
import BreadCrumbSimple from '@/components/ui/breadcrumbSimple';

const RemindDashboard = () => {
  const [activeKey, onTabChange] = useTabKeySearchParams('qualityremind');

  const items: TabsProps['items'] = [
    {
      label: '质检实时提醒',
      key: 'qualityremind',
      children: <Remind type="quality" />,
    },
    {
      label: '私信实时提醒',
      key: 'chatremind',
      children: <Remind type="chat" />,
    },
  ];

  return (
    <PageContainer
      title={<BreadCrumbSimple breadcrumbs={['提醒管理', '实时提醒']} />}
      extra={<PlatformSwitch />}
    >
      <Tabs activeKey={activeKey} onChange={onTabChange} items={items} className="horizontal-tab" />
    </PageContainer>
  );
};

export default RemindDashboard;
