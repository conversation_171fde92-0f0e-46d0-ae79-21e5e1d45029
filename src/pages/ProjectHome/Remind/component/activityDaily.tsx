import ExportButton from '@/components/exportButton';
import useProjectId from '@/hooks/useProjectId';
import { ActivityDailyItem, ExportActivityDaily, GetActivityDaily } from '@/services/daily';
import { proTableRequestAdapter } from '@/utils';
import { STICKY_OFFSETHEADER } from '@/utils/common';
import { ProCard, ProColumns, ProTable } from '@ant-design/pro-components';
import dayjs from 'dayjs';
import saveAs from 'file-saver';
import { customPaginationRender } from '../../style';

type ActivityDailyProps = {
  date: string;
};

const columns: ProColumns<ActivityDailyItem>[] = [
  {
    title: '活动昵称',
    dataIndex: 'activityName',
    align: 'center',
  },
  {
    title: '60分以下的账号数',
    dataIndex: 'sixtyPointDownAccountCount',
    align: 'center',
  },
  {
    title: '60分以下的团队数',
    dataIndex: 'sixtyPointDownTeamCount',
    align: 'center',
  },
  {
    title: '60分以上的账号数(含 60分)',
    dataIndex: 'sixtyPointUpAccountCount',
    align: 'center',
  },
  {
    title: '60分以上团队数(含 60分)',
    dataIndex: 'sixtyPointUpTeamCount',
    align: 'center',
  },
];

const ActivityDaily = (props: ActivityDailyProps) => {
  const { date } = props;
  const projectId = useProjectId();

  const handleExportRank = async () => {
    const resBlob = await ExportActivityDaily({ projectId, date });
    saveAs(resBlob, `${dayjs().format('YYYY-MM-DD')}.xls`);
  };

  return (
    <ProCard>
      <ProTable<ActivityDailyItem>
        columns={columns}
        params={{
          projectId,
          date,
        }}
        ghost
        sticky={{ offsetHeader: STICKY_OFFSETHEADER }}
        tableClassName="custom-table"
        scroll={{ x: 'max-content' }}
        request={(params, sorter, filter) => {
          return proTableRequestAdapter(params, sorter, filter, GetActivityDaily);
        }}
        search={false}
        toolBarRender={() => [<ExportButton exportFn={handleExportRank} key="export" />]}
        rowKey="teamName"
        pagination={{
          defaultPageSize: 10,
          showTotal: (total, range) => `${range[0]}-${range[1]} 共 ${total} 条`,
          itemRender: customPaginationRender,
          showSizeChanger: true,
        }}
        dateFormatter="string"
      />
    </ProCard>
  );
};

export default ActivityDaily;
