import ExportButton from '@/components/exportButton';
import useProjectId from '@/hooks/useProjectId';
import { ChatDailyItem, ExportChatDaily, GetChatDailyPage } from '@/services/daily';
import { proTableRequestAdapter } from '@/utils';
import { STICKY_OFFSETHEADER } from '@/utils/common';
import { ProCard, ProColumns, ProTable } from '@ant-design/pro-components';
import dayjs from 'dayjs';
import saveAs from 'file-saver';
import React from 'react';
import { customPaginationRender } from '../../style';

type ChatDailyProps = {
  date: (string | undefined)[];
};

const YesterdayTip: React.FC<{ title: string }> = ({ title }) => {
  return (
    <>
      <span style={{ textWrap: 'nowrap' }}>{title}</span>
      <br />
      (截止至昨天)
    </>
  );
};

const columns: ProColumns<ChatDailyItem>[] = [
  {
    title: '门店简称',
    dataIndex: 'teamName',
    align: 'center',
  },
  {
    title: '账号名称',
    dataIndex: 'nickname',
    align: 'center',
  },
  {
    title: '账号',
    dataIndex: 'showAccountId',
    align: 'center',
  },
  {
    title: '昨日会话人数',
    dataIndex: 'chatRound',
    align: 'center',
  },
  {
    title: '昨日接收消息数',
    dataIndex: 'receiveMsgCount',
    align: 'center',
  },
  {
    title: '昨日回复消息数',
    dataIndex: 'replayMsgCount',
    align: 'center',
  },
  {
    title: '昨日被提醒次数',
    dataIndex: 'notifyCount',
    align: 'center',
  },
  {
    title: '昨日被提醒次数',
    dataIndex: 'notifyCount',
    align: 'center',
  },
  {
    title: <YesterdayTip title="当周累计会话人数" />,
    dataIndex: 'weekChatRoundCount',
    align: 'center',
    width: 150,
  },
  {
    title: <YesterdayTip title="当周接收消息数" />,
    dataIndex: 'weekReceiveMsgCount',
    align: 'center',
  },
  {
    title: <YesterdayTip title="当周回复消息数" />,
    dataIndex: 'weekReplayMsgCount',
    align: 'center',
  },
  {
    title: <YesterdayTip title="当周被提醒次数" />,
    dataIndex: 'weekNotifyCount',
    align: 'center',
  },
  {
    title: <YesterdayTip title="当月累计会话人数" />,
    dataIndex: 'monthChatRoundCount',
    align: 'center',
  },
  {
    title: <YesterdayTip title="当月接收消息数" />,
    dataIndex: 'monthReceiveMsgCount',
    align: 'center',
  },
  {
    title: <YesterdayTip title="当月回复消息数" />,
    dataIndex: 'monthReplayMsgCount',
    align: 'center',
  },
  {
    title: <YesterdayTip title="当月被提醒次数" />,
    dataIndex: 'monthNotifyCount',
    align: 'center',
  },
];

const ChatDaily = (props: ChatDailyProps) => {
  const { date } = props;
  const projectId = useProjectId();

  const handleExport = async () => {
    const resBlob = await ExportChatDaily({ projectId, date });
    saveAs(resBlob, `${dayjs().format('YYYY-MM-DD')}.xls`);
  };

  return (
    <ProCard>
      <ProTable<ChatDailyItem>
        columns={columns}
        params={{
          projectId,
          date,
        }}
        ghost
        sticky={{ offsetHeader: STICKY_OFFSETHEADER }}
        tableClassName="custom-table"
        scroll={{ x: 'max-content' }}
        request={(params, sorter, filter) => {
          return proTableRequestAdapter(params, sorter, filter, GetChatDailyPage);
        }}
        search={false}
        toolBarRender={() => [<ExportButton exportFn={handleExport} key="export" />]}
        rowKey="teamName"
        pagination={{
          defaultPageSize: 10,
          showTotal: (total, range) => `${range[0]}-${range[1]} 共 ${total} 条`,
          itemRender: customPaginationRender,
          showSizeChanger: true,
        }}
        dateFormatter="string"
      />
    </ProCard>
  );
};

export default ChatDaily;
