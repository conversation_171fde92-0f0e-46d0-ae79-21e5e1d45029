import ExportButton from '@/components/exportButton';
import QualityTypeSelect from '@/components/qualityTypeSelect';
import TimeFilter from '@/components/ui/timeFilter';
import useLiveAfkFG from '@/hooks/fg/useLiveAfkFG';
import { usePollingExport } from '@/hooks/usePollingExport';
import useProjectId from '@/hooks/useProjectId';
import { QualityTargetType } from '@/services/constants';
import {
  ChatNotifyItem,
  ExportChatRecord,
  ExportQualityRecord,
  GetChatNotify,
  GetNotifyOverview,
  GetNotifyOverviewRes,
  GetQualityNotify,
  QualityNotifyItem,
} from '@/services/notify';
import { TagSpan } from '@/utils/commonStyle';
import { getTimeByType } from '@/utils/time';
import { ProCard } from '@ant-design/pro-components';
import { useAsyncEffect } from 'ahooks';
import { Empty, Flex, message, Select, Skeleton, Space, Timeline } from 'antd';
import { useAtomValue } from 'jotai';
import { useEffect, useRef, useState } from 'react';
import InfiniteScroll from 'react-infinite-scroll-component';
import styled from 'styled-components';
import { selectPlatformAtom } from '../../atom';
import { useParams } from '@umijs/max';

const TimelineItem = styled.div`
  padding: 10px 17px;
  border-radius: 8px;
  font-weight: 400;
  margin-block: 8px;
  letter-spacing: 1px;
  line-height: 25px;
`;

type QualityRemindProps = {
  type: 'quality' | 'chat';
};

const TimeLineHideScrollBarDiv = styled.div`
  overflow-y: auto;
  max-height: calc(100vh - 30vh - 56px);
  &::-webkit-scrollbar {
    display: none;
  }
  margin-top: 20px;
  padding-block: 10px;
`;

const notifyMap: Record<number, string> = {
  1: '3min未回复',
};

const qualityRuleMap: Record<number, string> = {
  1: '标题文字',
  2: '语音',
  3: '封面',
  4: '画面',
};

const qualityTypeMap: Record<number, string> = {
  1: '触发质检类型',
  2: '触发疑似空播挂播违规',
  3: '主播形象违规',
};

const renderQualityTag = (level?: number) => {
  switch (level) {
    case 1:
      return (
        <TagSpan $bgColor="#fff2f0" $textColor="#ff4d4f">
          严重空播挂播
        </TagSpan>
      );
    case 2:
      return (
        <TagSpan $bgColor="#fbebe2" $textColor="#FF7533">
          轻微空播挂播
        </TagSpan>
      );
    case 3:
      return (
        <TagSpan $bgColor="#dfe7fd" $textColor="#1E5EFF">
          正常
        </TagSpan>
      );
    default:
      return <></>;
  }
};

const QualityContent = styled.span`
  color: #64666b;
  font-size: 12px;

  .highlight {
    color: black;
    background: yellow;
  }
`;

const Remind = (props: QualityRemindProps) => {
  const projectId = useProjectId();
  const { industryType } = useParams();
  const { type } = props;
  const [qualityData, setQualityData] = useState<QualityNotifyItem[] | ChatNotifyItem[]>([]);
  const [notifyOverview, setNotifyOverview] = useState<GetNotifyOverviewRes>();
  const [rangeTime, setRangeTime] = useState<(string | undefined)[]>(getTimeByType('today'));
  // 滚动触底加载相关的state
  const pageRef = useRef<number>(1);
  const [total, setTotal] = useState<number>(0);
  const [loading, setLoading] = useState(false);
  const [qualityCategoryTypeList, setQualityCategoryTypeList] = useState<string[] | undefined>();
  const [liveAfkLevel, setLiveAfkLevel] = useState<string[] | undefined>();
  const { pollingExport, percent, pollingLoading } = usePollingExport();
  const afkState = useLiveAfkFG(projectId);
  const platform = useAtomValue(selectPlatformAtom);

  const handleExport = async () => {
    const exportFn = type === 'quality' ? ExportQualityRecord : ExportChatRecord;
    const [notifyStartDate, notifyEndDate] = rangeTime;
    const res = await exportFn({
      projectId,
      notifyStartDate,
      notifyEndDate,
      platform,
    });
    if (res.data) {
      pollingExport({ taskId: res.data });
    } else {
      message.error('导出失败,请重试');
    }
  };

  const renderQualityContent = (content: string, mark?: string) => {
    if (!mark) return content;
    const searchRegex = new RegExp(mark, 'gi');
    const showContent = content.replace(searchRegex, '<span class="highlight">$&</span>');
    return showContent;
  };

  const timeLineRender = qualityData?.map((item) => {
    if (type === 'quality') {
      const qualityItem = item as QualityNotifyItem;
      const monitorArr = qualityItem?.violationNameStr
        ? qualityItem?.violationNameStr.split(',')
        : [];
      return {
        children: (
          <div style={{ marginTop: '10px' }}>
            <span style={{ color: '#95979C' }}>{qualityItem.notifyTime}</span>
            <TimelineItem style={{ backgroundColor: '#1e5eff05' }}>
              <div>
                {qualityItem.teamName}在
                <a
                  href={
                    qualityItem.targetType === QualityTargetType.LIVE
                      ? `${window.location.origin}/live/detail/${qualityItem.targetId}/${platform}/${industryType}`
                      : `${window.location.origin}/post/detail/${qualityItem.targetId}/${platform}/${industryType}`
                  }
                  target="_blank"
                  rel="noreferrer"
                >
                  {qualityItem.title}
                </a>
                {qualityItem.targetType === QualityTargetType.LIVE ? '这场直播中' : '这部作品中'}，
                {qualityRuleMap[qualityItem.qualityRuleType]}
                {qualityTypeMap[qualityItem?.type]}
                <Flex wrap="nowrap" gap={5} style={{ display: 'inline-flex', marginLeft: '10px' }}>
                  {monitorArr.length > 0 &&
                    monitorArr.map((item) => (
                      <TagSpan key={item} $bgColor="#fbebe2" $textColor="#FF7533">
                        {item}
                      </TagSpan>
                    ))}
                </Flex>
                {renderQualityTag(qualityItem.liveAfkLevel)}
              </div>
              {qualityItem.content && (
                <QualityContent>
                  {qualityItem.type === 1 ? '违规内容' : '空播时长'}:
                  <span
                    dangerouslySetInnerHTML={{
                      __html: renderQualityContent(qualityItem.content, qualityItem?.mark),
                    }}
                  />
                </QualityContent>
              )}
            </TimelineItem>
          </div>
        ),
      };
    } else if (type === 'chat') {
      const chatItem = item as ChatNotifyItem;
      return {
        children: (
          <div style={{ marginTop: '10px' }}>
            <span style={{ color: '#95979C' }}>{chatItem.notifyTime}</span>
            <TimelineItem style={{ backgroundColor: '#1e5eff05' }}>
              <div>
                {chatItem.nickname}, 用户{chatItem.chatUserNickname} 发送了私信, 超过
                {notifyMap[chatItem.notifyChatType]}
              </div>
              <span style={{ color: '#64666B', fontSize: '12px' }}>
                私信内容: {chatItem.content}
              </span>
            </TimelineItem>
          </div>
        ),
      };
    } else {
      return {};
    }
  });

  const fetchNotify = async () => {
    let res;
    const [notifyStartDate, notifyEndDate] = rangeTime;
    const page = pageRef.current;
    if (type === 'quality') {
      res = await GetQualityNotify({
        projectId,
        notifyStartDate,
        notifyEndDate,
        size: 10,
        page,
        qualityCategoryTypeList,
        liveAfkLevel,
        platform,
      });
    } else if (type === 'chat') {
      res = await GetChatNotify({
        projectId,
        notifyStartDate,
        notifyEndDate,
        size: 10,
        page,
        platform,
      });
    }
    const data = res?.data?.items;
    const total = res?.data?.total;
    setTotal(total || 0);
    if (data && data.length > 0) {
      // @ts-ignore
      setQualityData((prevData) => [...prevData, ...data]);
      setLoading(false);
      pageRef.current += 1;
    } else {
      setQualityData([]);
      setLoading(false);
      pageRef.current = 1;
    }
  };

  useAsyncEffect(async () => {
    const [startDate, endDate] = rangeTime;
    const res = await GetNotifyOverview({
      projectId,
      type: type === 'quality' ? 1 : 2,
      startDate,
      endDate,
      platform,
    });
    setNotifyOverview(res.data);
  }, [rangeTime, platform]);

  useEffect(() => {
    // rangeTime改变的话不新增page
    pageRef.current = 1;
    setQualityData([]);
    fetchNotify();
  }, [rangeTime, qualityCategoryTypeList, liveAfkLevel, platform]);

  const loadMoreData = () => {
    if (loading) {
      return;
    }
    setLoading(true);
    fetchNotify();
  };

  return (
    <ProCard style={{ minWidth: 1000 }}>
      <div style={{ marginBottom: '16px' }}>
        提醒总数: {notifyOverview?.notifyCount || 0}, 发送成功数:{' '}
        {notifyOverview?.notifySuccessCount || 0}, 发送失败数:{' '}
        {notifyOverview?.notifyFailCount || 0}
      </div>
      <Space>
        <TimeFilter value={rangeTime} onChange={(value) => setRangeTime(value)} showToday />
        {type === 'quality' && (
          <Flex gap={10}>
            <QualityTypeSelect
              projectId={projectId}
              value={qualityCategoryTypeList}
              onChange={(value) => setQualityCategoryTypeList(value)}
              style={{ width: '200px' }}
            />
            {afkState && (
              <Select
                allowClear
                style={{ width: '200px' }}
                placeholder="是否疑似空挂播"
                value={liveAfkLevel}
                onChange={(value) => {
                  setLiveAfkLevel(value);
                }}
                options={[
                  { label: '严重空挂播', value: 1 },
                  { label: '轻微空挂播', value: 2 },
                ]}
              />
            )}
          </Flex>
        )}
        <ExportButton
          exportFn={handleExport}
          style={{ float: 'right' }}
          loading={pollingLoading}
          percent={percent}
          key="export"
        />
      </Space>
      {qualityData.length > 0 ? (
        <TimeLineHideScrollBarDiv id="scrollableDiv">
          <InfiniteScroll
            dataLength={qualityData.length}
            hasMore={qualityData.length < total}
            next={loadMoreData}
            loader={<Skeleton paragraph={{ rows: 1 }} active />}
            endMessage={
              qualityData && qualityData.length > 0 ? (
                <Empty description={<span>已全部展示</span>} />
              ) : null
            }
            scrollableTarget="scrollableDiv"
            height={`calc(100vh - 30vh - 56px)`}
          >
            <Timeline items={timeLineRender} />
          </InfiniteScroll>
        </TimeLineHideScrollBarDiv>
      ) : (
        <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
      )}
    </ProCard>
  );
};

export default Remind;
