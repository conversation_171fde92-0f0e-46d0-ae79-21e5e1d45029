import LiveQualityReport from '@/components/defaultRemindComponent/liveQualityReport';
import PostQualityReport from '@/components/defaultRemindComponent/postQualityReport';
import PlatformSwitch from '@/components/platformSwitch';
import TimeFilterByType from '@/components/timFilterByType.tsx';
import BreadCrumbSimple from '@/components/ui/breadcrumbSimple';
import useProjectId from '@/hooks/useProjectId';
import useTabKeySearchParams from '@/hooks/useTabKeySearchParams';
import { XIAOPENG } from '@/utils/constant';
import { PageContainer } from '@ant-design/pro-components';
import { Tabs, TabsProps } from 'antd';
import { useState } from 'react';
import DailyReport from './component/dailyReport';

type TimeFilterByType = {
  rangeTime: string[];
  radioValue: 'day' | 'week' | 'month' | null;
};

const RemindDaily = () => {
  const projectId = useProjectId();
  const [activeKey, onTabChange] = useTabKeySearchParams(
    projectId === XIAOPENG ? 'quality-daily' : 'live-quality-daily',
  );
  const [timeFilterByType, setTimeFilterByType] = useState<TimeFilterByType>({
    rangeTime: [],
    radioValue: null,
  });

  const handleTimeFilterChange = (filter: TimeFilterByType) => {
    setTimeFilterByType(filter);
  };

  const XIAOPENGItems: TabsProps['items'] = [
    {
      label: '质检日报',
      key: 'quality-daily',
      children: (
        <DailyReport
          rangeTime={timeFilterByType.rangeTime}
          dataType={timeFilterByType.radioValue}
          type="quality"
        />
      ),
    },
    {
      label: '私信日报',
      key: 'chat-daily',
      children: (
        <DailyReport
          rangeTime={timeFilterByType.rangeTime}
          dataType={timeFilterByType.radioValue}
          type="chat"
        />
      ),
    },
    // {
    //   label: '激励政策日报',
    //   key: 'activitydaily',
    //   children: <ActivityDaily date={date} />,
    // },
  ];

  // 虽然产品说是以后这个是通用的但大概率新项目接入又要定制 暂时命名default吧
  const defaultItems: TabsProps['items'] = [
    {
      label: '直播质检日报',
      key: 'live-quality-daily',
      children: (
        // 基本上是从DailyReport复制出来改造的 不再封装了 定制化需求太多 估计后面还会变
        <LiveQualityReport
          rangeTime={timeFilterByType.rangeTime}
          dataType={timeFilterByType.radioValue}
          projectId={projectId}
        />
      ),
    },
    {
      label: '作品质检日报',
      key: 'post-quality-daily',
      children: (
        // 基本上是从DailyReport复制出来改造的 不再封装了 定制化需求太多 估计后面还会变
        <PostQualityReport
          rangeTime={timeFilterByType.rangeTime}
          dataType={timeFilterByType.radioValue}
          projectId={projectId}
        />
      ),
    },
    {
      label: '私信日报',
      key: 'chat-daily',
      children: (
        <DailyReport
          rangeTime={timeFilterByType.rangeTime}
          dataType={timeFilterByType.radioValue}
          type="chat"
        />
      ),
    },
  ];

  return (
    <PageContainer
      title={<BreadCrumbSimple breadcrumbs={['提醒管理', '每日日报']} />}
      extra={<PlatformSwitch />}
    >
      <Tabs
        activeKey={activeKey}
        onChange={onTabChange}
        items={projectId === XIAOPENG ? XIAOPENGItems : defaultItems}
        tabBarExtraContent={<TimeFilterByType onTimeFilterChange={handleTimeFilterChange} />}
        className="horizontal-tab"
      />
    </PageContainer>
  );
};

export default RemindDaily;
