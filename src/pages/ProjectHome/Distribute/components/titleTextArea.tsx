import { SvgIcon } from '@/components/SvgIcon';
import { Button, Dropdown, Flex, Input, Popconfirm } from 'antd';
import { TextAreaRef } from 'antd/es/input/TextArea';
import { useRef, useState } from 'react';
import styles from '../index.module.less';

const SUG_LIST = [
  {
    cha_name: '热点',
    view_count: 307183753188,
    cid: '1589032799848451',
    group_id: '6537448892210894094',
    tag: 0,
  },
  {
    cha_name: '热点新闻事件',
    view_count: 143331907068,
    cid: '1692231302224896',
    group_id: '6573989188750824708',
    tag: 0,
  },
  {
    cha_name: '热点小助手',
    view_count: 38011907626,
    cid: '1657574141484040',
    group_id: '6665583118696584460',
    tag: 0,
  },
  {
    cha_name: '热点知多少',
    view_count: 23978013354,
    cid: '1740664216996871',
    group_id: '6773228809698153735',
    tag: 0,
  },
  {
    cha_name: '热点话题',
    view_count: 10096781394,
    cid: '1608853136664590',
    group_id: '6527568132737864973',
    tag: 0,
  },
  {
    cha_name: '热点追踪',
    view_count: 7997088836,
    cid: '1637590393856004',
    group_id: '6585122196555830535',
    tag: 0,
  },
  {
    cha_name: '热点60秒',
    view_count: 4806711613,
    cid: '1658931546763276',
    group_id: '6736545331648926984',
    tag: 1,
  },
  {
    cha_name: '热点宝',
    view_count: 4929912341,
    cid: '1705420896484494',
    group_id: '6735603555345896715',
    tag: 0,
  },
  {
    cha_name: '热点榜',
    view_count: 3224039904,
    cid: '1642555690137612',
    group_id: '6596535665863693582',
    tag: 0,
  },
  {
    cha_name: '热点新闻',
    view_count: 7104726876,
    cid: '1614898705062925',
    group_id: '6541372214913012996',
    tag: 0,
  },
  {
    cha_name: '热点助手',
    view_count: 1047211974,
    cid: '1660954592737284',
    group_id: '6663499515422577934',
    tag: 0,
  },
];

export const TitleTextArea = ({
  inputValue,
  showDelete,
  setInputValue,
  onDelete,
  onExtractTagBtnClick,
}: {
  showDelete: boolean;
  inputValue: string;
  setInputValue: (newVal: string) => void;
  onDelete: () => void;
  onExtractTagBtnClick: (tag: string[]) => void;
}) => {
  const [openSugList, setOpenSugList] = useState(false);
  // const [inputFocus, setInputFocus] = useState(false);
  // const [dropdownFocus, setDropdownFocus] = useState(false);
  const inputRef = useRef<TextAreaRef>(null);

  // useEffect(() => {
  //   if (!inputFocus && !dropdownFocus) {
  //     setOpenSugList(false);
  //   }
  // }, [inputFocus, dropdownFocus]);

  return (
    <div className="relative mb-2 h-[105px] w-[600px] rounded-[6px] border border-[#DCDCDC] p-2 pb-[26px] pr-[24px]">
      <Dropdown
        menu={{
          autoFocus: true,
          selectable: true,
          selectedKeys: [],
          defaultChecked: false,
          // onFocus: () => setDropdownFocus(true),
          // onBlur: () => setDropdownFocus(false),
          onSelect(e) {
            console.log(e);
            if (inputRef.current) {
              setInputValue(
                `${inputRef.current?.resizableTextArea?.textArea.value || ''}${e.key}\\`,
              );
            }
            setOpenSugList(false);
            setTimeout(() => {
              inputRef.current?.resizableTextArea?.textArea.focus();
            }, 0);
          },
          items: SUG_LIST.map((item) => ({
            label: (
              <Flex className="w-full" justify="space-between">
                <span>#{item.cha_name}</span>
                <span>
                  {item.view_count > 100000000
                    ? (item.view_count / 100000000).toFixed(1) + '亿'
                    : item.view_count}
                </span>
              </Flex>
            ),
            key: `${item.cha_name}`,
          })),
        }}
        open={openSugList}
        placement="bottomCenter"
        overlayClassName={styles.mention_options}
        autoFocus
      >
        <Input.TextArea
          ref={inputRef}
          bordered={false}
          placeholder="请输入标题"
          className="no-scrollbar p-0"
          style={{ height: 63.8 }}
          maxLength={200}
          // onFocus={() => setInputFocus(true)}
          // onBlur={() => setInputFocus(false)}
          value={inputValue}
          autoSize={{ maxRows: 2.9 }}
          onChange={(e) => {
            if (e.target.value.endsWith('#')) {
              setOpenSugList(true);
            } else if (openSugList) {
              setOpenSugList(false);
            }
            setInputValue(e.target.value);
          }}
        />
      </Dropdown>

      <Flex
        className="absolute bottom-0 w-[97%] text-[12px]"
        justify="space-between"
        align="center"
      >
        <div>
          <Button
            type="link"
            className="mr-3 p-0 text-[12px]"
            onClick={() => {
              setInputValue(`${inputValue}#`);
              setOpenSugList(true);
            }}
          >
            添加话题
          </Button>
          <Button
            type="link"
            className="p-0 text-[12px]"
            disabled={!inputValue.includes('#')}
            onClick={() => {
              const tag = inputValue.match(/#([^\\]*)\\/g);
              onExtractTagBtnClick?.(tag as string[]);
            }}
          >
            同步话题至所有标题
          </Button>
        </div>
        <span className="text-[#AEB0B5]">{inputValue.length}/200</span>
      </Flex>

      {showDelete && (
        <Popconfirm title="确认删除吗" onConfirm={onDelete}>
          <div className="absolute right-[8px] top-[8px]">
            <SvgIcon icon="local:outline/delete-outline" />
          </div>
        </Popconfirm>
      )}
    </div>
  );
};
