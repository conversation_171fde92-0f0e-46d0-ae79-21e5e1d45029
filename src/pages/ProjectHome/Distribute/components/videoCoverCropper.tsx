import { PlusOutlined, ReloadOutlined } from '@ant-design/icons';
import { <PERSON>ton, <PERSON>vider, <PERSON>lex, Modal, Slider, Spin, message } from 'antd';
import { useCallback, useEffect, useRef, useState } from 'react';
import { createGlobalStyle } from 'styled-components';
import Cropper, { ReactCropperElement } from 'react-cropper';
import 'cropperjs/dist/cropper.css';
import { IFormVideo } from '../typings';
import { useDebounceFn } from 'ahooks';
import SliderHandler from '@/assets/slider-handler.png';
import dayjs from 'dayjs';
import duration from 'dayjs/plugin/duration';
import { uploadBase64Image } from '@/utils/oss';

dayjs.extend(duration);

const CropperModalStyles = createGlobalStyle`
  .cropper-box-content {
    input {
      opacity: 0;
      width: 100%;
      height: 100%;
      left: 0;
      top: 0;
      position: absolute;
    }
  }
  
  .ant-slider-horizontal {
    padding-block: 0;
    margin: 0;
  }
  .ant-slider-handle::before,.ant-slider-handle::after {
    display: none;
  }
  
`;

const getEmptyImage = () => {
  const canvas = document.createElement('canvas');
  canvas.width = 9;
  canvas.height = 16;
  // const ctx = canvas.getContext('2d')
  return canvas.toDataURL();
};
export const VideoCoverCropper = (props: {
  video?: IFormVideo;
  onChange?: (value: string) => void;
  show: boolean;
  setShow: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
  const { video, onChange, show, setShow } = props;
  // const [isModalOpen, setShow] = useState(false);

  const videoRef = useRef<HTMLVideoElement | null>(null);
  const canvasRef = useRef<HTMLCanvasElement | null>(null);
  const cropperRef = useRef<ReactCropperElement>(null);

  /** 待截取的图像 */
  const [image, setImage] = useState<string | undefined>();
  const [customImage, setCustomImage] = useState(false);
  const [uploadingImage, setUploadingImage] = useState(false);
  /** 视频时长 */
  const [videoDuration, setVideoDuration] = useState(0);

  useEffect(() => {
    if (show) {
      videoRef.current?.dispatchEvent(new Event('loadedmetadata'));
    }
  }, [show]);

  const handleOk = useCallback(async () => {
    if (!videoRef.current) {
      return;
    }
    if (image) {
      onChange?.(image);
      if (typeof cropperRef.current?.cropper !== 'undefined') {
        cropperRef.current?.cropper
          .getCroppedCanvas({
            minWidth: 324,
            minHeight: 432,
          })
          ?.toBlob(async (blob) => {
            if (!blob) {
              message.error('裁剪框中没有内容');
              return;
            }
            setUploadingImage(true);
            const uploadRes = await uploadBase64Image({
              base64Data: blob,
              key: `/distribute/cover/${video?.uid}-cover.jpg`,
            });

            if (uploadRes.statusCode === 200) {
              const hitImageUrl = `https://${uploadRes.Location}`;

              onChange?.(hitImageUrl);
              setShow(false);
              setUploadingImage(false);
              return;
            }
            setUploadingImage(false);
            message.error('上传图片失败');
          });
      }
      return;
    }
  }, [image, onChange]);

  const handleCancel = useCallback(() => {
    setShow(false);
  }, []);

  const onInputImage = (e: any) => {
    e.preventDefault();
    let files;
    if (e.dataTransfer) {
      files = e.dataTransfer.files;
    } else if (e.target) {
      files = e.target.files;
    }
    if (!files || files.length === 0) {
      return;
    }
    if (files?.[0]?.size > 2 * 1024 * 1024) {
      message.error('图片大小不能超过2M');
      return;
    }
    const reader = new FileReader();
    reader.onload = () => {
      setCustomImage(true);
      setImage(reader.result as any);
    };
    reader.readAsDataURL(files[0]);
  };

  const { run: drawVideoCurrentFrame } = useDebounceFn(
    () => {
      if (!videoRef.current) {
        return;
      }
      const ctx = canvasRef.current?.getContext('2d');
      ctx?.drawImage(
        videoRef.current,
        0,
        0,
        videoRef.current?.videoWidth / 2,
        videoRef.current?.videoHeight / 2,
      );
      // console.log(canvasRef.current?.toDataURL());

      setImage(canvasRef.current?.toDataURL());
    },
    { wait: 1500 },
  );

  return (
    <div>
      <Modal
        title="设置封面"
        open={show}
        onCancel={() => setShow(false)}
        afterClose={() => {
          setImage(undefined);
          setCustomImage(false);
        }}
        footer={[
          <Flex
            key="footer"
            className={`w-full`}
            justify={customImage ? 'space-between' : 'flex-end'}
          >
            {customImage && (
              <Button
                className="float-left p-0 text-left"
                type="link"
                onClick={() => {
                  setCustomImage(false);
                  setImage(undefined);
                  drawVideoCurrentFrame();
                }}
              >
                <ReloadOutlined />
                重新选择
              </Button>
            )}
            <Flex className="float-right">
              <Button onClick={handleCancel} className="mr-2">
                取消
              </Button>
              <Button onClick={handleOk} type="primary" disabled={!image} loading={uploadingImage}>
                确认
              </Button>
            </Flex>
          </Flex>,
        ]}
        mask={false}
        width={555}
        bodyProps={{
          style: {
            padding: 0,
          },
        }}
        maskClosable={false}
      >
        <div className="cropper-box-content">
          <Divider />
          <div className="relative mb-3 mr-auto h-[265px] w-[515px] bg-[#F5F6FA] text-center">
            <video
              ref={videoRef}
              src={video?.url}
              style={{ opacity: image ? 0 : 1 }}
              className="video-content absolute left-1/2 h-full -translate-x-1/2"
              controls={false}
              crossOrigin="anonymous"
              onTimeUpdate={drawVideoCurrentFrame}
              onLoadedMetadata={() => {
                canvasRef.current = document.createElement('canvas');
                if (videoRef.current?.videoWidth && videoRef.current?.videoHeight) {
                  canvasRef.current.width = videoRef.current?.videoWidth / 2;
                  canvasRef.current.height = videoRef.current?.videoHeight / 2;
                  videoRef.current.currentTime = 0.1;
                }
                setVideoDuration(videoRef.current?.duration || 0);
                drawVideoCurrentFrame();
              }}
            />

            {/* image没生成好的时候转圈 展示video的内容 */}
            <Spin
              className="absolute left-1/2 top-1/2 z-10 -translate-x-1/2 -translate-y-1/2"
              spinning={!image}
            />

            <Cropper
              ref={cropperRef}
              style={{ width: '100%', height: 265, backgroundColor: 'black' }}
              // zoomTo={0.5}
              crossOrigin="anonymous"
              aspectRatio={3 / 4}
              src={image || getEmptyImage()}
              dragMode="move"
              viewMode={1}
              minCropBoxHeight={100}
              zoomable={false}
              minCanvasHeight={243}
              background={false}
              autoCropArea={1}
              checkOrientation={false} // https://github.com/fengyuanchen/cropperjs/issues/671
              guides={true}
            />
          </div>

          <Flex className="mt-6 w-full" justify="center">
            <label
              htmlFor="custom-cover"
              className="relative mr-4 block h-[64px] w-[64px] cursor-pointer bg-[#EBEDF2] text-center leading-[64px]"
            >
              <PlusOutlined size={20} />
              <input type="file" id="custom-cover" onChange={onInputImage} accept="image/*" />
            </label>

            {!customImage && (
              <Slider
                min={0}
                className="h-[64px] w-[288px]"
                max={videoDuration}
                tooltip={{
                  formatter(value) {
                    return dayjs.duration(value || 0, 'seconds').format('HH:mm:ss');
                  },
                }}
                step={0.001}
                styles={{
                  rail: {
                    height: 64,
                    backgroundColor: '#00000080',
                    borderRadius: 6,
                  },
                  track: {
                    height: '100%',
                    backgroundColor: '#00000080',
                    borderRadius: 6,
                  },
                  handle: {
                    background: `url(${SliderHandler})`,
                    backgroundSize: '100% auto',
                    backgroundPosition: 'center',
                    top: '50%',
                    transform: 'translate(-50%,-50%)',
                    width: 22,
                    height: 48,
                  },
                }}
                onChange={(v) => {
                  if (videoRef.current) {
                    videoRef.current.currentTime = v;
                  }
                  if (image) {
                    setImage(undefined);
                  }
                }}
              />
            )}
          </Flex>
        </div>
      </Modal>

      <CropperModalStyles />
    </div>
  );
};
