import { getDistributeAccountPage, PostDistributeAccountPageVO } from '@/services/distribute';
import { Empty, Spin } from 'antd';
import douyin1 from '@/assets/platformIcon/douyin.png';
import { CheckboxChangeEvent } from 'antd/es/checkbox';
import { useRequest } from 'ahooks';
import {
  ChangeEvent,
  ChangeEventHandler,
  memo,
  useCallback,
  useEffect,
  useMemo,
  useRef,
} from 'react';
import { CloseOutlined } from '@ant-design/icons';
import { IFormAccount } from '../typings';

const AccountCheckBoxItem = memo(
  ({
    name,
    accountId,
    onChange,
    nickname,
    area,
    checked,
  }: {
    checked: boolean;
    name?: string;
    nickname?: string;
    accountId?: string;
    area?: string;
    onChange?: ChangeEventHandler<HTMLInputElement>;
  }) => {
    console.log('item render', checked);

    return (
      <label htmlFor={accountId} className="flex items-center">
        <input id={accountId} checked={checked} type="checkbox" onChange={(e) => onChange?.(e)} />
        <div className="flex w-[500px] items-center">
          <div className="ml-1 flex-[1]">
            <img
              src={douyin1}
              className="pointer-events-none h-[21px] w-[21px] rounded-[6px] object-cover"
            />
          </div>
          <div title={name} className="flex-[12] overflow-hidden text-ellipsis text-nowrap">
            {name}
          </div>
          <div title={nickname} className="flex-[7] overflow-hidden text-ellipsis text-nowrap">
            {nickname}
          </div>
          <div title={area} className="flex-[4] overflow-hidden text-ellipsis text-nowrap">
            {area}
          </div>
        </div>
      </label>
    );
  },
  (prevProps, nextProps) => {
    return (
      prevProps.checked === nextProps.checked &&
      prevProps.accountId === nextProps.accountId &&
      prevProps.name === nextProps.name
    );
  },
);

export const AccountCheckBoxList = memo(
  ({
    onChange,
    projectId,
    treeValue,
    nickNameSearchKey,
    platform,
    type,
    selectingAccounts,
    setSelectingAccounts,
  }: {
    defaultSelectingAccount?: IFormAccount[];
    projectId?: string;
    treeValue?: string[];
    platform: number;
    type: number;
    nickNameSearchKey?: string;
    selectingAccounts: PostDistributeAccountPageVO[];
    setSelectingAccounts: React.Dispatch<React.SetStateAction<PostDistributeAccountPageVO[]>>;
    onChange?: (e: CheckboxChangeEvent, item: PostDistributeAccountPageVO) => void;
  }) => {
    const {
      data: accountRes,
      loading,
      mutate: mutateAccountRes,
    } = useRequest(
      () => {
        if (!projectId) {
          return Promise.resolve(null);
        }
        return getDistributeAccountPage({
          projectId,
          platform,
          type,
          page: 1,
          size: 9999,
          teamCodeList: treeValue,
          nickName: nickNameSearchKey,
        });
      },
      {
        refreshDeps: [projectId, onChange, type, platform, treeValue, nickNameSearchKey],
        onSuccess(res) {
          const data = res?.data || { items: [], total: 0 };
          data.items = data?.items?.map((item) => {
            const nickname = item.teamFields?.find((item) => item.fieldName == '门店简称');
            const area = item.teamFields?.find((item) => item.fieldName == '大区');
            const city = item.teamFields?.find((item) => item.fieldName == '城市');
            const province = item.teamFields?.find((item) => item.fieldName == '省份');
            return {
              ...item,
              shortName: nickname?.fieldValue,
              district: area?.fieldValue,
              city: city?.fieldValue,
              province: province?.fieldValue,
            };
          });

          mutateAccountRes({ code: 0, data });
        },
      },
    );

    const accountList = useMemo(() => {
      return accountRes?.data?.items || [];
    }, [accountRes]);

    const checkboxRef = useRef<HTMLInputElement>(null);

    const handleCheckAllChange = useCallback(
      (e: ChangeEvent<HTMLInputElement>) => {
        setSelectingAccounts((prev) =>
          e.target.checked
            ? [...prev, ...accountList]
            : prev.filter((item) => !accountList.some((a) => a.accountId == item.accountId)),
        );
      },
      [accountList, setSelectingAccounts],
    );

    useEffect(() => {
      if (!checkboxRef.current) {
        return;
      }
      const indeterminate =
        selectingAccounts?.length > 0 && selectingAccounts?.length < accountList?.length;
      const selectRowIds = selectingAccounts.map((item) => item.accountId);
      if (accountList.every((item) => selectRowIds.includes(item.accountId))) {
        checkboxRef.current.checked = true;
      } else {
        checkboxRef.current.checked = false;
      }
      checkboxRef.current.indeterminate = indeterminate;
    }, [selectingAccounts, accountList]);

    return (
      <Spin spinning={loading}>
        <div className="no-scrollbar mt-2 h-[360px] overflow-auto">
          {accountList.length ? (
            <>
              <label htmlFor="checkAll" className="flex items-center">
                <input
                  id="checkAll"
                  type="checkbox"
                  onChange={handleCheckAllChange}
                  ref={checkboxRef}
                />
                <span className="ml-[7px]">全选</span>
              </label>
              <div className="mb-2 h-full w-full items-center">
                {accountList?.map((item) => {
                  return (
                    <AccountCheckBoxItem
                      key={item.accountId}
                      name={item.nickname}
                      nickname={item.shortName}
                      area={item.district}
                      accountId={item.accountId}
                      checked={selectingAccounts.some((a) => a.accountId == item.accountId)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectingAccounts((prev) => [...prev, item]);
                        } else {
                          setSelectingAccounts((prev) =>
                            prev.filter((a) => a.accountId !== item.accountId),
                          );
                        }
                      }}
                    />
                  );
                })}
              </div>
            </>
          ) : (
            <div className="flex h-full w-full items-center justify-center">
              <Empty />
            </div>
          )}
        </div>
      </Spin>
    );
  },
  (prevProps, nextProps) => {
    return (
      prevProps.projectId === nextProps.projectId &&
      prevProps.treeValue === nextProps.treeValue &&
      prevProps.nickNameSearchKey === nextProps.nickNameSearchKey &&
      prevProps.platform === nextProps.platform &&
      prevProps.type === nextProps.type &&
      prevProps.selectingAccounts === nextProps.selectingAccounts
    );
  },
);

export const SelectingAccountItem = memo(
  ({ accountName, onDelete }: { accountName?: string; onDelete?: () => void }) => {
    console.log('selecting item render');

    return (
      <div className="relative mb-[6px] flex items-center rounded bg-[#EBEDF2] px-[6px] py-0">
        <img
          src={douyin1}
          className="pointer-events-none h-[20px] w-[20px] rounded-[6px] object-cover"
        />
        <span
          title={accountName || '-'}
          className="inline-block w-[196px] overflow-hidden text-ellipsis text-nowrap"
        >
          {accountName || '-'}
        </span>
        <CloseOutlined className="absolute right-[6px]" onClick={onDelete} />
      </div>
    );
  },
  (prevProps, nextProps) => {
    return prevProps.accountName === nextProps.accountName;
  },
);
