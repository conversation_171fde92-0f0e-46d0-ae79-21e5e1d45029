import { PlatForm } from '@/utils/platform';
import { Flex, Radio, Spin } from 'antd';
import { useEffect, useRef, useState } from 'react';
import douyin1 from '@/assets/platformPreview/douyin1.png';
import douyin2 from '@/assets/platformPreview/douyin2.png';
import VideoPause from '@/assets/video-pause.svg';

export const PlatFormPreview = ({
  url,
  cover,
}: {
  url?: string;
  platform: PlatForm;
  cover?: string;
}) => {
  /** 是否开始加载视频 不需要加载播放时只展示封面 */
  const [prepareVideo, setPrepareVideo] = useState(false);
  const [previewType, setPreviewType] = useState(1);
  const [pause, setPause] = useState(true);
  const videoRef = useRef<HTMLVideoElement | null>(null);
  const videoContainerRef = useRef<HTMLDivElement>(null);
  const [loadingVideo, setLoadingVideo] = useState(false);

  useEffect(() => {
    return () => {
      setPrepareVideo(false);
      setPause(true);
      if (videoRef.current) {
        videoContainerRef.current?.removeChild(videoRef.current);
        videoRef.current = null;
      }
    };
  }, [url]);

  useEffect(() => {
    if (videoRef.current) {
      videoRef.current?.pause();
      videoRef.current.style.display = previewType == 1 ? 'block' : 'none';
    }
  }, [previewType]);

  const handlePlayPause = () => {
    if (previewType == 2) {
      return;
    }

    const videoPause = videoRef.current?.paused;
    if (videoPause) {
      videoRef.current?.play();
    } else {
      videoRef.current?.pause();
    }
  };

  if (!url) {
    return null;
  }

  return (
    <Flex vertical justify="center" align="center" className="absolute left-[817px] top-0 mt-2">
      <Radio.Group
        optionType="button"
        value={previewType}
        onChange={(e) => setPreviewType(e.target.value)}
        options={[
          { label: '视频预览', value: 1 },
          {
            label: '主页封面',
            value: 2,
          },
        ]}
      />

      {url && (
        <div
          className="relative mt-4 h-[443px] w-[208px] rounded-lg bg-black bg-cover"
          // style={{ backgroundImage: `url(${douyin1})` }}
        >
          {
            <>
              {pause && prepareVideo && (
                <img
                  src={VideoPause}
                  width="48px"
                  height="48px"
                  style={{ display: previewType == 1 ? 'block' : 'none' }}
                  className="pointer-events-none absolute left-1/2 top-1/2 z-10 -translate-x-1/2 -translate-y-1/2 opacity-80"
                  onClick={handlePlayPause}
                />
              )}
              <Spin
                style={{ opacity: previewType == 1 ? 1 : 0 }}
                spinning={loadingVideo}
                className="absolute left-1/2 top-1/2 z-10 -translate-x-1/2 -translate-y-1/2 opacity-80"
              />
              <img
                src={douyin1}
                style={{ display: previewType == 1 ? 'block' : 'none' }}
                className="pointer-events-none absolute top-0 z-10 w-full object-cover"
                alt=""
              />

              <div
                ref={videoContainerRef}
                style={{ opacity: previewType == 1 ? 1 : 0 }}
                className="relative mt-[20px] flex h-[calc(100%-20px-41px)] w-full items-center justify-center bg-black"
              >
                {!prepareVideo && (
                  <img
                    src={cover}
                    className="w-full object-cover opacity-40"
                    onClick={() => {
                      videoRef.current = document.createElement('video');
                      videoRef.current.className = 'w-full';
                      videoRef.current.controls = false;
                      videoRef.current.onplay = () => setPause(false);
                      videoRef.current.onpause = () => setPause(true);
                      videoRef.current.oncanplay = () => setLoadingVideo(false);
                      videoRef.current.onclick = handlePlayPause;
                      videoContainerRef.current?.appendChild(videoRef.current);
                      videoRef.current.src = url;
                      setPrepareVideo(true);
                      setLoadingVideo(true);
                    }}
                  />
                )}
              </div>
            </>
          }

          <>
            <img
              src={douyin2}
              style={{ display: previewType == 2 ? 'block' : 'none' }}
              className="pointer-events-none absolute -left-[1px] -top-[1px] w-full object-cover"
              alt=""
            />
            <img
              src={cover}
              style={{ display: previewType == 2 ? 'block' : 'none' }}
              className="pointer-events-none absolute left-[6.7px] top-[53px] h-[128px] w-[96px] rounded-[8px] object-cover"
            />
          </>
        </div>
      )}
    </Flex>
  );
};
