import useProjectId from '@/hooks/useProjectId';
import { getTitlesByDify, TitleParams } from '@/services/distribute';
import { LeftOutlined, ReloadOutlined } from '@ant-design/icons';
import { useRequest } from 'ahooks';
import { Button, Divider, Empty, Flex, Form, Input, InputNumber, Modal } from 'antd';
import { useForm } from 'antd/es/form/Form';

export const AITitleModal = ({
  show,
  setShow,
  onConfirmAddTitle,
}: {
  show: boolean;
  setShow: (show: boolean) => void;
  onConfirmAddTitle: (titles: string[]) => void;
}) => {
  const [form] = useForm<TitleParams>();
  const projectId = useProjectId();

  const {
    run: getTitlesByDifyFn,
    loading,
    data: titlesRes,
    mutate: setTitles,
    refresh: refetchTitles,
  } = useRequest((values) => getTitlesByDify({ ...values, projectId }), {
    manual: true,
    refreshDeps: [projectId],
  });

  return (
    <>
      <a href="#" onClick={() => setShow(true)}>
        AI生成标题
      </a>

      <Modal
        title="AI生成标题"
        open={show}
        onCancel={() => setShow(false)}
        width={477}
        footer={[
          <Flex
            key="footer"
            className="w-full"
            justify={!titlesRes?.data ? 'flex-end' : 'space-between'}
          >
            <Flex>
              {titlesRes?.data && (
                <Button type="link" onClick={() => setTitles({ code: 0, data: undefined })}>
                  <LeftOutlined />
                  返回
                </Button>
              )}
              {titlesRes?.data && (
                <Button className="float-left p-0 text-left" type="link" onClick={refetchTitles}>
                  <ReloadOutlined />
                  重新生成
                </Button>
              )}
            </Flex>
            <Flex>
              <Button className="mr-2" onClick={() => setShow(false)}>
                取消
              </Button>
              <Button
                loading={loading}
                type="primary"
                onClick={() => {
                  if (!titlesRes?.data) {
                    form.submit();
                  } else {
                    onConfirmAddTitle(titlesRes.data);
                  }
                }}
              >
                {titlesRes?.data ? '确认添加' : '开始生成'}
              </Button>
            </Flex>
          </Flex>,
        ]}
      >
        <Divider type="horizontal" className="mb-5 mt-2" />

        <Form<TitleParams>
          form={form}
          labelCol={{ span: 5 }}
          style={{ display: titlesRes?.data ? 'none' : 'block' }}
          onFinish={async (values) => {
            getTitlesByDifyFn(values);
            // setTitles({ code: 0, data: ['123', '345'] });
          }}
        >
          <Form.Item label="标题案例" name="title" rules={[{ required: true }]}>
            <Input placeholder="请输入标题案例，我们会根据该案例来生成新标题" />
          </Form.Item>
          <Form.Item label="生成数量" name="count" rules={[{ required: true }]} initialValue={5}>
            <InputNumber min={1} precision={0} />
          </Form.Item>
        </Form>

        <div className="mh-[340px] no-scrollbar overflow-auto">
          {titlesRes?.data?.length ? (
            titlesRes?.data?.map((item, i) => {
              return (
                <>
                  <Flex key={item + i} className="font-[400 px-4 text-[14px]" vertical>
                    <span className="text-[#64666B]">标题{i + 1}</span>
                    <span className="#0E1015E5">{item}</span>
                  </Flex>
                  <Divider className="m-2" />
                </>
              );
            })
          ) : (
            <Empty description="没有数据" style={{ display: titlesRes?.data ? 'block' : 'none' }} />
          )}
        </div>
      </Modal>
    </>
  );
};
