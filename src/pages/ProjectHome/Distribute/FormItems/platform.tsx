import { PLATFORM_LIST } from '@/utils/platform';
import { Flex, Space } from 'antd';

export default function Platform({
  defaultValue,
  value = defaultValue,
  onChange,
  resetAccounts,
}: {
  value?: number;
  defaultValue?: number;
  onChange?: (value: number) => void;
  resetAccounts?: () => void;
}) {
  return (
    <Space>
      {PLATFORM_LIST.map((item) => (
        <Flex
          key={item.label}
          align="center"
          justify="center"
          onClick={() => {
            resetAccounts?.();
            onChange?.(item.value);
          }}
          className="h-[54px] w-[96px] rounded-[8px] text-center"
          style={{
            border: `1px solid ${value === item.value ? '#1E5EFF' : 'transparent'}`,
            backgroundColor: value === item.value ? '#1E5EFF1A' : '#F9FAFF',
          }}
        >
          <img
            src={item.icon}
            alt="icon"
            width={36}
            height={36}
            className="pointer-events-none mr-2"
          />
          <span>{item.label}</span>
        </Flex>
      ))}
    </Space>
  );
}
