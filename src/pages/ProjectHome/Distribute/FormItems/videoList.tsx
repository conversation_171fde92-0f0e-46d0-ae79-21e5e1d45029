import { cos, uploadBase64Image, uploadFile } from '@/utils/oss';
import {
  UploadProps,
  Upload,
  Button,
  Space,
  Flex,
  Progress,
  Spin,
  Popconfirm,
  Modal,
  Divider,
  message,
} from 'antd';
import { nanoid } from 'nanoid';
import PQueue from 'p-queue';
import { useEffect, useRef, useState } from 'react';
import { PlatFormPreview } from '../components/platFormPreview';
import { VideoCoverCropper } from '../components/videoCoverCropper';
import { importVideo } from '@/services/distribute';
import { EventEmitter } from 'ahooks/lib/useEventEmitter';
import { IDistributeForm, IFormVideo } from '../typings';
import { SvgIcon } from '@/components/SvgIcon';

const uploadVideoQueue = new PQueue({ concurrency: 3, autoStart: true });
const uploadingTaskIds: Record<string, string> = {};

export const VideoList = ({
  value,
  onChange,
  resetFormValue$,
}: {
  value?: IFormVideo[];
  onChange?: (value: IFormVideo[]) => void;
  resetFormValue$: EventEmitter<IDistributeForm>;
}) => {
  const videoRef = useRef<HTMLVideoElement>(document.createElement('video'));

  const [videos, setVideos] = useState<IFormVideo[]>(value || []);
  const [selectingVideoId, setSelectingVideoId] = useState<string>();
  const [editCoverVideo, setEditCoverVideo] = useState<IFormVideo>();
  const [showModal, setShowModal] = useState(false);
  const [uploadPause, setUploadPause] = useState(false);
  const [showImportModal, setShowImportModal] = useState(false);

  const selectingVideo = videos.find((item) => item.uid === selectingVideoId);

  resetFormValue$.useSubscription((data) => {
    if (data.videos && data.videos.length > 0) {
      setVideos(data.videos || []);
      getVideoDefaultCover(data.videos.filter((item) => !item.initCover));
    }
  });

  useEffect(() => {
    if (value) {
      getVideoDefaultCover(value.filter((item) => !item.initCover));
      setVideos((prev) => {
        if (prev.length === 0) {
          return value;
        } else {
          return prev;
        }
      });
    }
  }, [value]);

  const updateVideoState = (id: string, attrs: Partial<IFormVideo>) => {
    setVideos((prev) => {
      const values = prev.map((item) => {
        if (item.uid === id) {
          return {
            ...item,
            ...attrs,
          };
        }
        return item;
      });
      onChange?.(values);
      return values;
    });
  };

  const getVideoDefaultCover = async (videos: IFormVideo[]) => {
    videoRef.current.crossOrigin = 'anonymous';
    const canvas = document.createElement('canvas');
    // console.log('getcover', videos);

    for (const video of videos) {
      if (!video.objectUrl && !video.url) {
        continue;
      }
      if (video.initCover) {
        continue;
      }

      // console.log(video);
      videoRef.current.src = (video.objectUrl || video.url)!;

      await new Promise<void>((resolve) => {
        try {
          videoRef.current.onerror = () => {
            updateVideoState(video.uid, { error: 'error', initCover: 'error' });
            resolve();
          };
          videoRef.current.onloadeddata = () => {
            videoRef.current.currentTime = 0.1;
          };
          videoRef.current.ontimeupdate = async () => {
            if (videoRef.current?.videoWidth && videoRef.current.videoHeight) {
              canvas.width = videoRef.current?.videoWidth / 4;
              canvas.height = videoRef.current?.videoHeight / 4;
              const ctx = canvas.getContext('2d');

              ctx?.drawImage(
                videoRef.current,
                0,
                0,
                videoRef.current?.videoWidth / 4,
                videoRef.current?.videoHeight / 4,
              );
              console.log(canvas.toDataURL());

              canvas.toBlob(async (blob) => {
                const uploadRes = await uploadBase64Image({
                  base64Data: blob,
                  key: `/distribute/cover/${video?.uid}-cover.jpg`,
                });

                if (uploadRes.statusCode === 200) {
                  const hitImageUrl = `https://${uploadRes.Location}`;
                  updateVideoState(video.uid, { cover: hitImageUrl, initCover: hitImageUrl });
                }
              });

              updateVideoState(video.uid, { initCover: canvas.toDataURL() });
              setTimeout(() => {
                resolve();
              }, 100);
            }
          };
        } catch (e) {
          updateVideoState(video.uid, { error: 'error', initCover: 'error' });
          resolve();
        }
      });
    }
  };

  const props: UploadProps = {
    name: 'file',
    multiple: true,
    accept: '.xlsx,.mp4,.webm',
    showUploadList: false,
    customRequest: (options) => {
      options.onSuccess?.({}, options.file);
    },
    beforeUpload(file) {
      if (file.size > 1024 * 1024 * 1024 * 16) {
        // 没写错 产品要求16G
        message.error('视频最大16GB');
        return false;
      }
      return true;
    },
    onChange: async (info) => {
      // console.log(
      //   info,
      //   info.fileList.every((item) => item.status == 'done'),
      // );

      if (info.fileList.every((item) => item.status == 'done')) {
        const uploadVideos = info.fileList.filter((item) => item.type?.startsWith('video'));
        const excels = info.fileList.filter((item) => !item.type?.startsWith('video'));
        let objectVideos: IFormVideo[] = [];
        let httpVideos: IFormVideo[] = [];
        const xlsxVideoUrls = [];

        if (excels.length > 0) {
          for (const excel of excels) {
            if (excel?.originFileObj) {
              const res = await importVideo({ file: excel.originFileObj });
              xlsxVideoUrls.push(...(res.data?.filter((item) => !!item) || []));
            }
          }
        }
        if (uploadVideos.length + videos.length + xlsxVideoUrls.length > 50) {
          message.error('视频数量不能超过50个');
          setShowImportModal(false);
          return;
        }
        if (xlsxVideoUrls.length > 0) {
          httpVideos = xlsxVideoUrls.map((item, i) => {
            const videoName = `${i}-${item.split('/').pop()}`;
            return {
              uid: nanoid(),
              name: videoName,
              percent: 1,
              url: item,
            };
          });
          setVideos((prev) => {
            onChange?.([...prev, ...httpVideos]);
            return [...prev, ...httpVideos];
          });
        }
        if (uploadVideos.length > 0) {
          objectVideos = uploadVideos.map((item) => {
            uploadVideoQueue.add(() =>
              uploadFile({
                key: `/distribute/videos/${item.uid}${item.name}`,
                base64Data: item.originFileObj,
                onTaskReady(taskId) {
                  uploadingTaskIds[item.uid] = taskId;
                },
                onProgress(data) {
                  updateVideoState(item.uid, { percent: data.percent });
                },
                onFileFinish(err, data) {
                  uploadingTaskIds[item.uid] && delete uploadingTaskIds[item.uid];
                  updateVideoState(item.uid, { url: `https://${data.Location}`, percent: 1 });
                },
              }),
            );
            return {
              uid: item.uid,
              name: item.fileName || item.name,
              percent: 0,
              objectUrl: URL.createObjectURL(item.originFileObj as Blob),
            };
          });
          setVideos((prev) => {
            onChange?.([...prev, ...objectVideos]);
            return [...prev, ...objectVideos];
          });
        }

        setShowImportModal(false);
        getVideoDefaultCover([...objectVideos, ...httpVideos]);
      }
    },

    // onDrop(e) {
    //   console.log('Dropped files', e.dataTransfer.files);
    // },
  };

  const uploadingVideos = videos.filter((item) => item.percent < 1);

  return (
    <div>
      <span className="block h-[32px] w-[570px] text-[12px] leading-[32px] text-[#95979C]">
        已添加: <span className="text-[#1E5EFF]">{videos?.length - uploadingVideos.length}</span>
        {videos.length > 0 && (
          <a href="#" className="float-right text-[14px]" onClick={() => setShowImportModal(true)}>
            继续上传
          </a>
        )}
      </span>

      {uploadingVideos.length > 0 && (
        <div className="mb-2 w-[600px]">
          <Progress
            status="active"
            percent={Number((100 - (uploadingVideos.length / videos.length) * 100).toFixed(2))}
          />
          <Flex justify="space-between" align="center">
            <span className="text-[14px] text-[#0E1015]">
              上传中 {uploadingVideos.length} / {videos.length}
            </span>
            <Space>
              <span
                onClick={() => {
                  if (uploadPause) {
                    for (const key in uploadingTaskIds) {
                      cos.restartTask(uploadingTaskIds[key]);
                    }
                  } else {
                    for (const key in uploadingTaskIds) {
                      cos.pauseTask(uploadingTaskIds[key]);
                    }
                  }
                  setUploadPause((prev) => !prev);
                }}
              >
                {uploadPause ? '开始' : '暂停'}
              </span>
              <span
                onClick={() => {
                  for (const key in uploadingTaskIds) {
                    cos.cancelTask(uploadingTaskIds[key]);
                  }
                  setVideos((prev) => {
                    const completeVideos = prev.filter((item) => item.percent === 1);
                    onChange?.(completeVideos);
                    return prev.filter((item) => item.percent === 1);
                  });
                }}
              >
                取消
              </span>
            </Space>
          </Flex>
        </div>
      )}

      {!videos.length ? (
        <div className="h-[200px] w-[600px] pb-6">
          <Upload.Dragger {...props}>
            <SvgIcon
              icon="local:upload-cloud"
              fontSize={50}
              style={{ border: 'none', color: 'transparent' }}
            />
            <p>
              <span className="text-[#1E5EFF]">点击上传</span> / 拖拽到此区域
            </p>
          </Upload.Dragger>
          <p className="mt-1 w-[720px] text-xs text-[#95979C]">
            视频时长&lt;= 60分钟，大小&lt;=
            16GB，推荐上传720p以上分辨率，最高支持4k；支持常见视频格式，推荐使用mp4、webm
            <a href="/new-media-api/distribute/download-video-template" target="_blank">
              &nbsp;下载模版
            </a>
          </p>
        </div>
      ) : (
        <Flex wrap className="w-[600px]">
          {videos.map((item) => {
            const selecting = item.uid === selectingVideo?.uid;
            return (
              <div key={item.uid} className="relative mr-4 w-[180px]">
                <div onClick={() => setSelectingVideoId(item.uid)}>
                  <img
                    src={item.cover || item.initCover}
                    className="pointer-events-none h-[240px] w-[180px] rounded-[8px] border-[1.5px] border-solid object-cover"
                    style={{
                      borderColor: selecting ? '#1E5EFF' : 'transparent',
                    }}
                  />

                  {((!item.initCover && !item.cover) || item.error) && (
                    <Flex
                      justify="center"
                      align="center"
                      className="pointer-events-none absolute top-0 h-[240px] w-full rounded-[8px] bg-[#1E5EFF33]"
                    >
                      <Spin spinning={!item.error} />
                      {item.error && <span> 视频或封面加载失败</span>}
                    </Flex>
                  )}

                  {selecting && (
                    <div className="pointer-events-none absolute top-0 h-[240px] w-full rounded-[8px] bg-[#1E5EFF33]" />
                  )}

                  {item.initCover && item.percent < 1 && (
                    <Flex
                      justify="center"
                      align="center"
                      className="pointer-events-none absolute top-0 h-[240px] w-full rounded-[8px] bg-[#00000080]"
                    >
                      <Progress
                        trailColor="#fff"
                        format={(p) => <span className="text-white">{p?.toFixed(2)}%</span>}
                        percent={item.percent * 100}
                        className="w-[142px]"
                      />
                    </Flex>
                  )}
                </div>
                <span
                  title={item.name}
                  className="block max-w-[90%] overflow-hidden text-ellipsis text-nowrap"
                >
                  {item.name}
                </span>
                <Space align="center">
                  <Button
                    type="link"
                    className="p-0"
                    // disabled={item.initCover == 'error'}
                    onClick={() => {
                      setShowModal(true);
                      setEditCoverVideo(item);
                    }}
                  >
                    更换封面
                  </Button>
                  <span className="text-[#C7C9CE]">|</span>
                  <Popconfirm
                    title="确认删除吗"
                    onConfirm={() =>
                      setVideos((prev) => {
                        onChange?.(prev.filter((video) => video.uid != item.uid));
                        if (item.uid === selectingVideo?.uid) {
                          setSelectingVideoId(undefined);
                        }
                        return prev.filter((video) => video.uid != item.uid);
                      })
                    }
                  >
                    <Button type="link" className="p-0">
                      删除
                    </Button>
                  </Popconfirm>
                </Space>
              </div>
            );
          })}
        </Flex>
      )}

      <PlatFormPreview
        url={selectingVideo?.objectUrl || selectingVideo?.url}
        platform={1}
        cover={selectingVideo?.cover || selectingVideo?.initCover}
      />

      <VideoCoverCropper
        video={editCoverVideo}
        show={showModal}
        setShow={setShowModal}
        onChange={(newCover) => {
          if (newCover && editCoverVideo?.uid) {
            updateVideoState(editCoverVideo?.uid, { cover: newCover });
          }
        }}
      />

      <Modal
        title="上传视频"
        open={showImportModal}
        destroyOnHidden
        width={452}
        onCancel={() => setShowImportModal(false)}
        footer={null}
      >
        <Divider className="mb-5 mt-1 w-full" />

        <div className="h-[139px]">
          <Upload.Dragger {...props}>
            <SvgIcon
              icon="local:upload-cloud"
              fontSize={50}
              style={{ border: 'none', color: 'transparent' }}
            />

            <p className="text-[#64666B]">
              <span className="text-[#1E5EFF]">点击上传</span> / 拖拽到此区域
            </p>
          </Upload.Dragger>
        </div>
        <div className="mt-1 text-wrap text-[12px] text-[#95979C]">
          视频时长&lt;= 60分钟，大小&lt;=
          16GB，推荐上传720p以上分辨率，最高支持4k；支持常见视频格式，推荐使用mp4、webm{' '}
          <a href="/new-media-api/distribute/download-video-template" target="_blank">
            下载模版
          </a>
        </div>
      </Modal>
    </div>
  );
};
