import { Divider, Flex, Modal } from 'antd';
import { useState } from 'react';
import { TitleTextArea } from '../components/titleTextArea';
import { importTitle } from '@/services/distribute';
import { RcFile } from 'antd/es/upload';
import { AITitleModal } from '../components/aiTitleModal';
import { Upload, UploadProps } from 'antd/lib';
import { EventEmitter } from 'ahooks/lib/useEventEmitter';
import { IDistributeForm } from '../typings';
import { SvgIcon } from '@/components/SvgIcon';

const DEFAULT_TITLES = [''];
export const RedDotSpan = (text: string) => (
  <span>
    <span className="text-[red]">*</span>
    {text}
  </span>
);
export const Titles = ({
  value,
  onChange,
  resetFormValue$,
}: {
  value?: string[];
  onChange?: (value: string[]) => void;
  resetFormValue$: EventEmitter<IDistributeForm>;
}) => {
  const [titles, setTitles] = useState<string[]>(value || DEFAULT_TITLES);
  const [show, setShow] = useState(false);
  const [showImportModal, setShowImportModal] = useState(false);

  resetFormValue$.useSubscription((data) => {
    if (data.titles) {
      setTitles(data.titles);
    }
  });

  const updateTitleByIndex = (index: number, newValue: string) => {
    setTitles((prev) => {
      const newTitles = prev.map((item, i) => {
        if (i == index) {
          return newValue;
        }
        return item;
      });
      onChange?.(newTitles);
      return newTitles;
    });
  };

  const props: UploadProps = {
    name: 'file',
    multiple: true,
    accept: '.xlsx,',
    showUploadList: false,
    customRequest: (options) => {
      options.onSuccess?.({}, options.file);
    },
    // beforeUpload(file, fileList) {},
    onChange: async (info) => {
      // console.log(
      //   info,
      //   info.fileList.every((item) => item.status == 'done'),
      // );

      if (info.fileList.every((item) => item.status == 'done')) {
        const excels = info.fileList.filter((item) => !item.type?.endsWith('xlsx'));
        // let objectVideos: IFormVideo[] = [];
        // let httpVideos: IFormVideo[] = [];
        const arr: string[] = [];
        for (const excel of excels || []) {
          const res = await importTitle({ file: excel.originFileObj as RcFile });
          arr.push(...(res?.data || []));
        }
        console.log(arr);

        setTitles((prev) => {
          onChange?.([...prev, ...arr]);
          return [...prev, ...arr];
        });
        setShowImportModal(false);
      }
    },

    // onDrop(e) {
    //   console.log('Dropped files', e.dataTransfer.files);
    // },
  };

  return (
    <div className="w-[600px]">
      <Flex justify="space-between" align="center" className="h-[32px]">
        <span className="text-[12px] text-[#95979C]">
          已添加: <span className="text-[#1E5EFF]">{titles.length || 0}</span>
        </span>
        <Flex align="center">
          <a
            href="#"
            onClick={() => {
              setTitles((prev) => {
                onChange?.([...prev, '']);
                return [...prev, ''];
              });
            }}
          >
            添加标题
          </a>
          <Divider type="vertical" className="w-[1px]" />
          <AITitleModal
            show={show}
            setShow={setShow}
            onConfirmAddTitle={(t) => {
              setTitles((prev) => {
                onChange?.([...prev, ...t]);
                return [...prev, ...t];
              });
              setShow(false);
            }}
          />
          <Divider type="vertical" />

          <a href="#" onClick={() => setShowImportModal(true)}>
            批量导入
          </a>
        </Flex>
      </Flex>

      {titles?.map((title, i) => {
        return (
          <TitleTextArea
            key={i}
            showDelete={titles.length > 1}
            inputValue={title}
            setInputValue={(val) => updateTitleByIndex(i, val)}
            onDelete={() => {
              setTitles((prev) => {
                onChange?.(prev.filter((_, index) => i !== index));
                return prev.filter((_, index) => i !== index);
              });
            }}
            onExtractTagBtnClick={(tags) => {
              setTitles((prev) => {
                const newTitles = prev.map((previousTitle, index) => {
                  if (index === i) {
                    return previousTitle;
                  } else {
                    return `${previousTitle}\n${tags.join('')}`;
                  }
                });
                onChange?.(newTitles);
                return newTitles;
              });
            }}
          />
        );
      })}

      <span className="text-[12px] text-[#95979C]">
        {RedDotSpan(' 请在每个话题结尾添加反斜杠"\\" 否则无法识别；')}
        <br />
        至少需添加一个标题，若添加标题数小于视频数，则多出来的视频随机匹配标题
      </span>

      <Modal
        title="批量添加标题"
        open={showImportModal}
        width={452}
        destroyOnHidden
        onCancel={() => setShowImportModal(false)}
        footer={null}
      >
        <Divider className="mb-5 mt-1 w-full" />

        <div className="h-[139px]">
          <Upload.Dragger {...props}>
            <SvgIcon
              icon="local:upload-cloud"
              fontSize={50}
              style={{ border: 'none', color: 'transparent' }}
            />
            <p className="text-[#64666B]">
              <span className="text-[#1E5EFF]">点击上传</span> / 拖拽到此区域
            </p>
          </Upload.Dragger>
        </div>
        <div className="mt-1 text-wrap text-[12px] text-[#95979C]">
          支持xlsx格式上传{' '}
          <a href="/new-media-api/distribute/download-title-template" target="_blank">
            下载模版
          </a>
        </div>
      </Modal>
    </div>
  );
};
