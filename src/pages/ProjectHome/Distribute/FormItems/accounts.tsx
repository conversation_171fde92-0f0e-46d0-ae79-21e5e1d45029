import DynamicTree from '@/components/dynamicTree';
import useProjectId from '@/hooks/useProjectId';
import { PostDistributeAccountPageVO } from '@/services/distribute';
import { Alert, Divider, Flex, Input, message, Modal, Popconfirm } from 'antd';

import { useCallback, useState } from 'react';

import { AccountCheckBoxList, SelectingAccountItem } from '../components/accountCheckBoxItem';
import { CheckboxChangeEvent } from 'antd/es/checkbox';
import { IDistributeForm, IFormAccount } from '../typings';
import { EventEmitter } from 'ahooks/lib/useEventEmitter';

export const Accounts = ({
  value,
  onChange,
  getVideoCount,
  type,
  platform,
  resetFormValue$,
}: {
  getVideoCount: () => number;
  platform: number;
  type: number;
  value?: IFormAccount[];
  onChange?: (value: IFormAccount[]) => void;
  resetFormValue$: EventEmitter<IDistributeForm>;
}) => {
  const [show, setShow] = useState(false);
  const [treeValue, setTreeValue] = useState<string[] | undefined>();
  const [selectingAccounts, setSelectingAccounts] = useState<PostDistributeAccountPageVO[]>(
    value || [],
  );

  const [nickNameSearchKey, setNicknameSearchKey] = useState<string>();
  const projectId = useProjectId();

  resetFormValue$.useSubscription((data) => {
    if (data.accounts) {
      setSelectingAccounts(data.accounts || []);
    }
  });

  const handleAccountItemChange = useCallback(
    (e: CheckboxChangeEvent, item: PostDistributeAccountPageVO) => {
      if (e.target.checked) {
        setSelectingAccounts((prev) => [...prev, item]);
      } else {
        setSelectingAccounts((prev) => prev.filter((a) => a.accountId !== item.accountId));
      }
    },
    [],
  );

  const handleDeleteAccountItem = (item: PostDistributeAccountPageVO, setFormValues?: boolean) => {
    setSelectingAccounts((prev) => {
      const list = prev.filter((a) => a.accountId !== item.accountId);
      if (setFormValues) {
        onChange?.(list);
      }
      return list;
    });
  };

  return (
    <>
      <Flex className="h-[32px] w-[600px]" justify="space-between" align="center">
        <span className="text-[12px] text-[#95979C]">
          已添加：
          <span className="text-[#1E5EFF]">{value?.length || 0}</span>
        </span>
        <a href="#" onClick={() => setShow(true)}>
          添加
        </a>
      </Flex>

      <div className="w-[250px]">
        {value?.map((item) => (
          <SelectingAccountItem
            key={item.accountId}
            accountName={item.nickname}
            onDelete={() => {
              handleDeleteAccountItem(item, true);
            }}
          />
        ))}
        {/* {value && value?.length > 5 && <span>...</span>} */}
        <span className="text-[12px] text-[#95979C]">所选择的账号数量需小于视频数量</span>
      </div>

      <Modal
        open={show}
        onCancel={() => {
          if (selectingAccounts.length > 0) {
            Modal.confirm({
              title: '提示',
              content: '存在账号被选择，是否确认关闭该页面',
              onOk() {
                setShow(false);
              },
            });
          } else {
            setShow(false);
          }
        }}
        title="发布账号"
        width={866}
        onOk={() => {
          if (getVideoCount() < selectingAccounts.length) {
            message.error('所要添加的账号数量不得大于视频数量，请进行调整');
            return;
          }
          onChange?.(
            selectingAccounts.map((a) => ({
              accountId: a.accountId,
              nickname: a.nickname,
              province: a.province,
              city: a.city,
              district: a.district,
              avatar: a.avatar,
            })),
          );
          setShow(false);
        }}
      >
        <Divider type="horizontal" className="mb-5 mt-2" />
        <Alert
          className="m-auto mb-4 h-[54px] w-full"
          message={
            type === 1
              ? '当前发布模式选择为平台自动发布，账号列表仅展现当前项目已授权且支持发布的账号'
              : '当前发布模式选择为任务派发，账号列表内显示当前项目所有已录入的账号'
          }
          type="info"
          showIcon
        />
        <Flex>
          <Flex flex={2}>
            <div>
              <Flex>
                <DynamicTree
                  style={{ width: '200px', marginRight: 8 }}
                  value={treeValue}
                  setValue={setTreeValue}
                  triggerOnDropdownClose={true}
                  projectId={projectId}
                />
                <Input.Search
                  onSearch={(v) => setNicknameSearchKey(v)}
                  placeholder="请输入账号名称搜索"
                  style={{ width: '200px' }}
                />
              </Flex>
              <AccountCheckBoxList
                defaultSelectingAccount={value}
                platform={platform}
                type={type}
                projectId={projectId}
                treeValue={treeValue}
                nickNameSearchKey={nickNameSearchKey}
                onChange={handleAccountItemChange}
                selectingAccounts={selectingAccounts}
                setSelectingAccounts={setSelectingAccounts}
              />
            </div>
          </Flex>

          <Divider type="vertical" className="h-[400px]" />
          <Flex flex={1} vertical className="text[12px]">
            <Flex justify="space-between" className="text-[#989898]">
              <span>已选{selectingAccounts.length}项</span>
              <Popconfirm title="确定清空吗" onConfirm={() => setSelectingAccounts([])}>
                <span>清空</span>
              </Popconfirm>
            </Flex>
            <div className="no-scrollbar mt-2 h-[360px] overflow-auto">
              {selectingAccounts.map((item) => {
                return (
                  <SelectingAccountItem
                    key={item.accountId}
                    accountName={item.nickname}
                    onDelete={() => handleDeleteAccountItem(item)}
                  />
                );
              })}
            </div>
          </Flex>
        </Flex>
      </Modal>
    </>
  );
};
