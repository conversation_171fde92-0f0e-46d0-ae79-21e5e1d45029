export interface IDistributeForm {
  videos?: IFormVideo[];
  platform?: number;
  type?: number;
  matchType?: number;
  titles?: string[];
  accounts?: IFormAccount[];
  publishType?: number;
  publishTime?: string;
  intervalTime?: number;
}

export interface IFormVideo {
  uid: string;
  name: string;
  percent: number;
  cover?: string;
  url?: string;
  /** 本地选择视频时的objectUrl 不会入库保存 */
  objectUrl?: string;
  /** initCover每次重新生成 不会入库保存 */
  initCover?: string;
  error?: string;
}

export interface IFormAccount {
  accountId?: string;
  nickname?: string;
  province?: string;
  city?: string;
  district?: string;
  avatar?: string;
}
