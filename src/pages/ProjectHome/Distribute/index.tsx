import { <PERSON>Container } from '@ant-design/pro-components';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  DatePicker,
  Flex,
  Form,
  InputNumber,
  message,
  Modal,
  Popconfirm,
  Radio,
  Space,
} from 'antd';

import {
  createDistribute,
  distributePost,
  getByDistributionInfoById,
  getLatestDistribution,
  updateDistribute,
} from '@/services/distribute';
import useProjectId from '@/hooks/useProjectId';
import { useNavigate, useParams, useRequest, useSearchParams } from '@umijs/max';
import { LeftOutlined } from '@ant-design/icons';
import Platform from './FormItems/platform';
import { useCallback, useEffect, useState } from 'react';
import { useBlocker } from '@/hooks/useBlock';
import { RedDotSpan, Titles } from './FormItems/titles';
import dayjs from 'dayjs';
import { Accounts } from './FormItems/accounts';

import { range } from 'lodash-es';
import { useEventEmitter } from 'ahooks';
import { IDistributeForm } from './typings';
import { VideoList } from './FormItems';
import BreadCrumbSimple from '@/components/ui/breadcrumbSimple';
import styles from './index.module.less';

export default function Distribute() {
  const [form] = Form.useForm<IDistributeForm>();

  const projectId = useProjectId();
  const { projectKey, industryType = '1' } = useParams();
  const [searchParams] = useSearchParams();
  const { id, copyId } = Object.fromEntries(searchParams.entries());
  const navigate = useNavigate();
  const [isDistribute, setIsDistribute] = useState(false);

  const resetFormValue$ = useEventEmitter<IDistributeForm>();

  useRequest(
    () => {
      if (!projectId || (!id && !copyId)) {
        return Promise.resolve(undefined);
      }
      return getByDistributionInfoById({ distributionId: id || copyId, projectId });
    },
    {
      ready: (!!id || !!copyId) && !!projectId,
      refreshDeps: [id, projectId, copyId],
      onSuccess(res) {
        const configs = JSON.parse(res);
        setFormFieldsValues(configs);
        form.setFieldsValue({
          publishTime: dayjs(configs.publishTime) as unknown as string,
          intervalTime: configs.intervalTime,
        });
      },
    },
  );

  const {
    data: lastDistribute,
    mutate: mutateLatestDistribute,
    refresh: refreshLatestDistribute,
  } = useRequest(
    () => {
      if (projectId && !copyId) {
        return getLatestDistribution({ projectId });
      }
    },
    { refreshDeps: [projectId, copyId], ready: !id && !copyId && !!projectId },
  );

  const saveConfig = useCallback(
    async (event?: any) => {
      event?.preventDefault();

      const config = form.getFieldsValue();
      // 把没有上传完成的过滤掉 同时把objectUrl去掉
      config.videos = config.videos
        ?.filter((item) => item.percent >= 1)
        .map((item) => ({
          uid: item.uid,
          name: item.name,
          percent: item.percent,
          cover: item.cover,
          url: item.url,
          initCover: item.initCover?.includes(
            'https://media-1307444343.cos.ap-guangzhou.myqcloud.com',
          )
            ? item.initCover
            : undefined,
        }));

      if (config.publishTime) {
        config.publishTime = dayjs(config.publishTime).format('YYYY-MM-DD HH:mm:ss');
      }

      if (projectId) {
        console.log(config);
        const currentId = lastDistribute?.id || id;
        const saveFn = currentId ? updateDistribute : createDistribute;

        await saveFn({
          id: currentId,
          projectId,
          config: JSON.stringify(config),
        });
        refreshLatestDistribute();
      }
    },
    [form, projectId, lastDistribute?.id, id, refreshLatestDistribute],
  );

  useEffect(() => {
    window.addEventListener('beforeunload', saveConfig);
    return () => {
      window.removeEventListener('beforeunload', saveConfig);
    };
  }, [saveConfig]);

  useBlocker(
    useCallback(
      async (tx: any) => {
        console.log(tx);

        if (tx.location.state === 'commit') {
          tx.retry();
          return;
        }
        await saveConfig().finally(() => {
          if (window.confirm('离开此网站？ 未上传完成的视频和封面将丢失')) tx.retry();
        });
      },
      [saveConfig],
    ),
  );

  const setFormFieldsValues = (values: Partial<IDistributeForm>) => {
    form.setFieldsValue(values);
    resetFormValue$.emit(values);
  };

  return (
    <div className={styles.distribute_wrapper}>
      <PageContainer
        title={<BreadCrumbSimple breadcrumbs={['矩阵发布', '发布日志', '发布视频']} />}
        childrenContentStyle={{ paddingBottom: 10 }}
      >
        <div className="relative rounded-md bg-[#fff] pb-5">
          <div className="mb-5 flex h-[46px] items-center border-b border-[#EBEDF2]">
            <span
              className="ml-8 text-sm font-medium"
              onClick={() => navigate(`/project/${projectKey}/${industryType}/distribute/log`)}
            >
              <LeftOutlined className="mr-1" />
              发布日志
            </span>
          </div>
          {lastDistribute?.id && lastDistribute?.config && (
            <Alert
              className="m-auto h-[54px] w-[95%]"
              message="你还有上次未发布的编辑内容，是否继续编辑？"
              type="info"
              showIcon
              action={
                <Space>
                  <Popconfirm
                    title="当前内容将被覆盖"
                    onConfirm={() => {
                      if (!lastDistribute?.config) {
                        return;
                      }
                      try {
                        const configs = JSON.parse(lastDistribute?.config);
                        setFormFieldsValues(configs);
                        form.setFieldsValue({
                          publishTime: dayjs(configs.publishTime) as unknown as string,
                          intervalTime: configs.intervalTime,
                        });
                        mutateLatestDistribute({ ...lastDistribute, config: undefined });
                      } catch {
                        message.error('恢复失败');
                      }
                    }}
                  >
                    <Button type="link">继续编辑</Button>
                  </Popconfirm>
                  <Button
                    type="link"
                    onClick={async () => {
                      if (projectId && lastDistribute?.id) {
                        // await deleteDistribute({ projectId, distributionId: lastDistribute?.id });
                      }
                      mutateLatestDistribute(undefined);
                    }}
                  >
                    放弃
                  </Button>
                </Space>
              }
            />
          )}

          <Form<IDistributeForm>
            form={form}
            colon={false}
            requiredMark={false}
            labelCol={{ span: 2 }}
            onFinish={async (values) => {
              console.log(values);
              if (!values.videos?.every((item) => item.url && item.percent >= 1)) {
                message.info('存在没上传完的视频');
                return;
              } else {
                // 把没有上传完成的过滤掉 同时把objectUrl去掉
                values.videos = values.videos
                  ?.filter((item) => item.percent >= 1)
                  .map((item) => ({
                    uid: item.uid,
                    name: item.name,
                    percent: item.percent,
                    cover: item.cover,
                    url: item.url,
                    initCover: item.initCover?.includes(
                      'https://media-1307444343.cos.ap-guangzhou.myqcloud.com',
                    )
                      ? item.initCover
                      : undefined,
                  }));

                if (values.publishTime) {
                  values.publishTime = dayjs(values.publishTime).format('YYYY-MM-DD HH:mm:ss');
                }

                let newId: string | undefined = lastDistribute?.id || id;
                if (!newId) {
                  const res = await createDistribute({
                    projectId,
                    config: JSON.stringify(values),
                  });
                  newId = res.data;
                }
                if (newId) {
                  setIsDistribute(true);
                  const res = await distributePost({
                    id: newId,
                    projectId,
                    config: JSON.stringify(values),
                  }).catch(() => {
                    setIsDistribute(false);
                  });
                  if (res?.code == 0) {
                    navigate(`/project/${projectKey}/distribute/log`, { state: 'commit' });
                  } else {
                    setIsDistribute(false);
                  }
                }
              }
            }}
          >
            <Form.Item label={RedDotSpan('添加视频')} name="videos" rules={[{ required: true }]}>
              <VideoList resetFormValue$={resetFormValue$} />
            </Form.Item>

            <Form.Item
              label={RedDotSpan('发布平台')}
              name="platform"
              rules={[{ required: true }]}
              initialValue={1}
            >
              <Platform
                defaultValue={1}
                resetAccounts={() => setFormFieldsValues({ accounts: [] })}
              />
            </Form.Item>

            <Form.Item label={RedDotSpan('发布模式')}>
              <Flex vertical className="w-[599px] bg-[#F9FAFF] p-4">
                <Form.Item name="type" noStyle rules={[{ required: true }]} initialValue={1}>
                  <Radio.Group
                    className="mb-6"
                    onChange={() => {
                      setFormFieldsValues({ accounts: [] });
                    }}
                    options={[
                      { label: '平台自动发布', value: 1 },
                      { label: '任务派发', value: 2 },
                    ]}
                  />
                </Form.Item>
                <span className="-translate-y-4 text-[12px] text-[#95979C]">
                  平台自动分发：通过乾坤圈系统将视频发布至抖音，无需用户自行操作。
                </span>
                <Form.Item name="matchType" noStyle rules={[{ required: true }]} initialValue={1}>
                  <Radio.Group
                    className="mb-1"
                    options={[
                      { label: '一个账号对应一个视频', value: 1 },
                      { label: '一个账号对应多个且不重复的视频', value: 2 },
                    ]}
                  />
                </Form.Item>
              </Flex>
            </Form.Item>

            <Form.Item label={RedDotSpan('发布设置')} className="m-0" />

            <Form.Item noStyle dependencies={['type']}>
              {({ getFieldValue }) => {
                return (
                  getFieldValue('type') == 1 && (
                    <Form.Item
                      label="内容标题"
                      className="custom-form-label"
                      name="titles"
                      rules={[
                        {
                          validateTrigger: ['onSubmit'],
                          validator: (_, value) => {
                            if (!value || value?.length == 0) {
                              return Promise.reject(new Error('请输入标题'));
                            }
                            if (value?.some((item: string) => !item)) {
                              return Promise.reject(new Error('存在空标题'));
                            }
                            return Promise.resolve();
                          },
                        },
                      ]}
                    >
                      <Titles resetFormValue$={resetFormValue$} />
                    </Form.Item>
                  )
                );
              }}
            </Form.Item>

            <Form.Item noStyle dependencies={['type', 'videos', 'platform']}>
              {({ getFieldValue }) => (
                <Form.Item
                  label="发布账号"
                  className="custom-form-label"
                  name="accounts"
                  rules={[
                    { required: true },
                    {
                      validator: (_, value) => {
                        if (value?.length > getFieldValue('videos')?.length) {
                          return Promise.reject(new Error('发布账号数量不能大于视频数量'));
                        }
                        return Promise.resolve();
                      },
                    },
                  ]}
                >
                  <Accounts
                    getVideoCount={() => getFieldValue('videos')?.length || 0}
                    type={getFieldValue('type')}
                    platform={getFieldValue('platform')}
                    resetFormValue$={resetFormValue$}
                  />
                </Form.Item>
              )}
            </Form.Item>

            <Form.Item label={RedDotSpan('发布时间')}>
              <Flex vertical className="w-[599px] bg-[#F9FAFF] p-4" gap={8}>
                <Form.Item noStyle name="publishType" rules={[{ required: true }]} initialValue={1}>
                  <Radio.Group
                    options={[
                      { label: '立即发布', value: 1 },
                      { label: '定时发布', value: 2 },
                    ]}
                  />
                </Form.Item>

                <Space>
                  <Form.Item noStyle dependencies={['matchType']}>
                    {({ getFieldValue }) =>
                      getFieldValue('matchType') == 2 && (
                        <Flex align="center">
                          单账号发布视频间隔&nbsp;&nbsp;
                          <Form.Item noStyle name="intervalTime" initialValue={5}>
                            <InputNumber defaultValue={5} min={1} max={60} precision={0} />
                          </Form.Item>
                        </Flex>
                      )
                    }
                  </Form.Item>

                  <Form.Item noStyle dependencies={['publishType']}>
                    {({ getFieldValue }) =>
                      getFieldValue('publishType') == 2 && (
                        <Flex align="center">
                          发布时间&nbsp;&nbsp;
                          <Form.Item
                            noStyle
                            name="publishTime"
                            rules={[
                              {
                                validator: (_, value) => {
                                  if (!value) {
                                    return Promise.resolve();
                                  } else {
                                    if (value < dayjs().add(3, 'hour')) {
                                      return Promise.reject(
                                        new Error('发布时间只能选择当前时间三小时后'),
                                      );
                                    } else {
                                      return Promise.resolve();
                                    }
                                  }
                                },
                              },
                            ]}
                          >
                            <DatePicker
                              showTime={{ format: 'HH:mm' }}
                              className="w-[200px]"
                              disabledDate={(current) =>
                                current && current < dayjs().startOf('day')
                              }
                              disabledTime={(current) => {
                                // 只允许选择三小时后的时间
                                const isSelectingToday = current.date() == dayjs().date();
                                const canTodaySelect = dayjs().add(3, 'hour');
                                return {
                                  disabledHours: () =>
                                    isSelectingToday
                                      ? range(0, 24).slice(0, canTodaySelect?.hour())
                                      : [],
                                  disabledMinutes: () =>
                                    isSelectingToday
                                      ? range(0, 60).slice(0, canTodaySelect?.minute())
                                      : [],
                                };
                              }}
                            />
                          </Form.Item>
                        </Flex>
                      )
                    }
                  </Form.Item>
                </Space>
              </Flex>
            </Form.Item>
          </Form>
        </div>
      </PageContainer>
      <div
        key="submit"
        className="sticky bottom-0 left-0 flex h-[73px] w-full items-center justify-end border-t border-solid border-[#EBEDF2] bg-white/[0.6] p-[24px] backdrop-blur"
      >
        <Button
          className="mr-3"
          onClick={async () => {
            const config = form.getFieldsValue();
            if (config.videos?.some((item) => item.percent != 1)) {
              await new Promise<void>((resolve, reject) => {
                Modal.confirm({
                  title: '提示',
                  content: '未上传完成的视频和封面将丢失',
                  onCancel() {
                    reject();
                  },
                  onOk() {
                    resolve();
                  },
                });
              });
            }
            // await saveConfig().finally(() => {
            navigate(`/project/${projectKey}/distribute/log`);
            // });
          }}
        >
          取消
        </Button>
        <Button type="primary" disabled={isDistribute} onClick={() => form.submit()}>
          提交
        </Button>
      </div>
    </div>
  );
}
