import { Flex } from 'antd';
import { CSSProperties } from 'react';

interface PropsType {
  status: 'success' | 'fail' | 'default' | 'processing';
  children: string;
}

const StatusTag = ({ status, children }: PropsType) => {
  const style: CSSProperties = {
    width: 72,
    height: 24,
    borderRadius: 4,
    boxSizing: 'border-box',
    fontSize: 14,
    color: '#09090B',
    background: '#f1f4f9',
  };

  if (status === 'success') {
    style.color = '#0FA964';
    style.background = '#0FA9641A';
  } else if (status === 'fail') {
    style.color = '#E71919';
    style.background = '#E719191A';
  } else if (status === 'processing') {
    style.color = '#1E5EFF';
    style.background = '#1E80FF1A';
  }

  return (
    <Flex justify="center" align="center" style={style}>
      {children}
    </Flex>
  );
};

export default StatusTag;
