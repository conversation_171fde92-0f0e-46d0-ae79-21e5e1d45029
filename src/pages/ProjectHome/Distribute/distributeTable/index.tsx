import { useState, useRef } from 'react';
import {
  PageContainer,
  ProTable,
  ProFieldValueType,
  ProCard,
  ActionType,
  ProColumns,
} from '@ant-design/pro-components';
import { Link, useParams } from '@umijs/max';
import { Button, DatePicker, Space, Flex, message } from 'antd';
import dayjs from 'dayjs';
import { DownOutlined, UpOutlined } from '@ant-design/icons';
import FolderIcon from '@/assets/folder.png';
import {
  deleteDistribute,
  SpiderAccountPostDistributeVO,
  getDistributeList,
} from '@/services/distribute';
import DistributeVideoScrollList from './distributeVideoScrollList';
import StatusTag from './statusTag';
import { proTableRequestAdapter } from '@/utils';
import useProjectId from '@/hooks/useProjectId';
import styled from 'styled-components';
import { customPaginationRender } from '@/pages/ProjectHome/style/index';
import { proTableSearchConfig } from '@/utils/proTableConfig';
import BreadCrumbSimple from '@/components/ui/breadcrumbSimple';

const DistributeProTable = styled(ProTable<SpiderAccountPostDistributeVO>)`
  .ant-table-expanded-row.ant-table-expanded-row-level-1 > .ant-table-cell {
    padding-left: 0 !important;
  }
  .ant-pro-query-filter-row {
    margin-bottom: 17px;
  }
`;

export const PLATFORM_MAP: { [key: number]: string } = {
  1: '抖音',
  4: '视频号',
  6: '小红书',
};

export const DISTRIBUTE_TYPES: { [key: number]: string } = {
  1: '视频发布',
  2: '任务派发',
};

export const STATUS_VALUE_ENUM = {
  0: {
    text: '待发布',
  },
  1: {
    text: '发布中',
  },
  2: {
    text: '发布成功',
  },
  3: {
    text: '发布失败',
  },
  4: {
    text: '未发布',
  },
};

export default function DistributeTable() {
  const { projectKey, industryType = '1' } = useParams();
  const projectId = useProjectId();

  const [dateArea, setDateArea] = useState<string[]>([
    dayjs().subtract(30, 'day').format('YYYY-MM-DD'),
    dayjs().format('YYYY-MM-DD'),
  ]);
  const [status, setStatus] = useState<number | undefined>();
  const [currentDistributeDetailId, setCurrentDistributeDetailId] = useState<string | null>(null); // 当前查看分发详情id

  const actionRef = useRef<ActionType>();

  const [messageApi, contextHolder] = message.useMessage();

  /**
   * 筛选日期
   */
  const onChangeRange = (_: any, dateString: string[]) => {
    setDateArea(dateString);
  };

  /**
   * 删除任务
   */
  const onDeleteTask = async (id: string) => {
    if (projectId) {
      const res = await deleteDistribute({
        projectId,
        distributionId: id,
      });

      if (res.code === 0 && res.data) {
        messageApi.success('删除成功');

        if (actionRef.current) {
          actionRef.current.reload();
        }
      } else {
        messageApi.error(res.msg || '删除失败');
      }
    }
  };

  const tableColumns: ProColumns[] = [
    {
      title: '视频',
      hideInSearch: true,
      dataIndex: 'videoCount',
      width: 100,
      render(_: any, record: SpiderAccountPostDistributeVO) {
        return (
          <Space size="small">
            <img src={FolderIcon} style={{ width: 30 }} alt="" />
            <Flex
              justify="center"
              align="center"
              style={{
                height: 24,
                width: 24,
                backgroundColor: '#f8f9ff',
                color: '#1E5EFF',
                fontSize: 12,
                borderRadius: '50%',
              }}
            >
              {record.videoCount || 0}
            </Flex>
          </Space>
        );
      },
    },
    {
      title: '账号信息',
      hideInSearch: true,
      dataIndex: 'videoCount',
      width: 150,
      render(_: any, record: SpiderAccountPostDistributeVO) {
        const avatars = record.avatars;
        const count = avatars.length;
        const maxShowCount = 5;
        const surplus = count - maxShowCount;
        return (
          <Flex>
            {avatars.slice(0, maxShowCount).map((item) => {
              return (
                <img
                  key={item}
                  src={item}
                  alt=""
                  style={{ width: 24, height: 24, borderRadius: '50%', marginLeft: -5 }}
                />
              );
            })}
            {surplus > 0 && (
              <Flex
                justify="center"
                align="center"
                style={{
                  minWidth: 24,
                  height: 24,
                  padding: '0 4px',
                  marginLeft: -5,
                  backgroundColor: '#f8f9ff',
                  borderRadius: 12,
                  color: '#1E5EFF',
                  fontSize: 12,
                }}
              >
                +{surplus}
              </Flex>
            )}
          </Flex>
        );
      },
    },
    {
      title: '门店简称',
      hideInSearch: true,
      dataIndex: 'storeName',
      width: 150,
      render() {
        return '-';
      },
    },
    {
      title: '所属大区',
      hideInSearch: true,
      dataIndex: 'region',
      width: 100,
      render() {
        return '-';
      },
    },
    {
      title: '平台',
      hideInSearch: true,
      dataIndex: 'platform',
      width: 90,
      valueEnum: PLATFORM_MAP,
    },
    {
      title: '发布类型',
      hideInSearch: true,
      dataIndex: 'type',
      width: 90,
      valueEnum: DISTRIBUTE_TYPES,
    },
    {
      title: '状态',
      width: 100,
      hideInSearch: true,
      dataIndex: 'status',
      render(_: any, record: SpiderAccountPostDistributeVO) {
        const status = record.status;
        if (typeof status === 'number') {
          const statusText = STATUS_VALUE_ENUM[status]?.text;

          const tagStatus = {
            0: 'default',
            1: 'processing',
            2: 'success',
            3: 'fail',
            4: 'default',
          }[status] as 'success' | 'fail' | 'default' | 'processing';

          return <StatusTag status={tagStatus}>{statusText}</StatusTag>;
        } else {
          return '-';
        }
      },
    },
    {
      title: '执行开始时间',
      width: 160,
      hideInSearch: true,
      dataIndex: 'taskStartTime',
    },
    {
      title: '执行结束时间',
      width: 160,
      hideInSearch: true,
      dataIndex: 'taskEndTime',
    },
    {
      title: '操作',
      hideInSearch: true,
      dataIndex: 'operation',
      width: 166,
      // fixed: 'right',
      render(_: any, record: SpiderAccountPostDistributeVO) {
        return [
          <Button size="small" key="delete" type="link" onClick={() => onDeleteTask(record.id)}>
            删除
          </Button>,
          record.status === 4 ? (
            <Link to={`/project/${projectKey}/${industryType}/distribute?id=${record.id}`}>
              <Button size="small" key="edit" type="link">
                编辑
              </Button>
            </Link>
          ) : (
            <Link to={`/project/${projectKey}/${industryType}/distribute?copyId=${record.id}`}>
              <Button size="small" key="edit" type="link">
                复制并编辑
              </Button>
            </Link>
          ),
        ];
      },
    },
  ];

  const filterColumns: ProColumns[] = [
    {
      title: '时间区间',
      dataIndex: 'timeArea',
      hideInTable: true,
      formItemProps: {
        label: null,
      },
      renderFormItem: () => {
        return (
          <DatePicker.RangePicker
            onChange={onChangeRange}
            // disabledDate={disabledDate}
            allowClear={false}
            defaultValue={[dayjs(dateArea[0]), dayjs(dateArea[1])]}
          />
        );
      },
    },
    {
      title: '状态筛选',
      dataIndex: 'status',
      hideInTable: true,
      formItemProps: {
        label: null,
      },
      valueType: 'select' as ProFieldValueType,
      valueEnum: STATUS_VALUE_ENUM,
      fieldProps: {
        placeholder: '筛选状态',
        onChange: setStatus,
      },
    },
  ];

  return (
    <PageContainer
      title={<BreadCrumbSimple breadcrumbs={['矩阵发布', '发布日志']} />}
      style={{ position: 'relative' }}
    >
      <ProCard>
        <DistributeProTable
          actionRef={actionRef}
          scroll={{ x: 'max-content' }}
          expandable={{
            expandIcon: (props: { record: SpiderAccountPostDistributeVO }) => {
              const record = props.record;
              const recordId = record.id;

              if (record.status === 4) {
                return null;
              }
              if (currentDistributeDetailId === recordId) {
                return <UpOutlined onClick={() => setCurrentDistributeDetailId(null)} />;
              } else {
                return <DownOutlined onClick={() => setCurrentDistributeDetailId(recordId)} />;
              }
            },
            expandedRowKeys: currentDistributeDetailId ? [currentDistributeDetailId] : [],
            expandedRowRender: (record: SpiderAccountPostDistributeVO) => {
              return <DistributeVideoScrollList distributionId={record.id} />;
            },
          }}
          columns={[...tableColumns, ...filterColumns]}
          request={(params, sorter, filter) => {
            return proTableRequestAdapter(params, sorter, filter, getDistributeList);
          }}
          params={{
            projectId,
            startTime: dateArea[0],
            endTime: dateArea[1],
            status: status,
          }}
          toolBarRender={false}
          rowKey="id"
          tableClassName="custom-table expand-row-bgn-none"
          search={{
            ...proTableSearchConfig,
            style: { marginBottom: 10 },
            optionRender: () => {
              return [
                <Button type="primary" key="distribute">
                  <Link to={`/project/${projectKey}/${industryType}/distribute`}>发布视频</Link>
                </Button>,
              ];
            },
          }}
          ghost
          pagination={{
            defaultPageSize: 10,
            showTotal: (total, range) => `${range[0]}-${range[1]} 共 ${total} 条`,
            itemRender: customPaginationRender,
            showSizeChanger: true,
          }}
        />
      </ProCard>

      {contextHolder}
    </PageContainer>
  );
}
