import { useState, useEffect, useRef } from 'react';
import { useRequest } from 'ahooks';
import { Table, Avatar, Popover } from 'antd';
import { ColumnsType } from 'antd/es/table';
import XGVideoCard from '@/components/xgVideoCard';
import StatusTag from './statusTag';
import { getDistributeDetailList, DistributeVideoItem } from '@/services/distribute';
import BlueVip from '@/assets/blue-vip.png';
import { PLATFORM_MAP, DISTRIBUTE_TYPES, STATUS_VALUE_ENUM } from './index';
import styled from 'styled-components';
import useProjectId from '@/hooks/useProjectId';

const DistributeVideoScrollListWrap = styled(Table<DistributeVideoItem>)`
  .ant-table {
    margin-inline: 0 -8px !important;
  }
`;

interface PropsType {
  distributionId: string;
}

const DistributeVideoScrollList = ({ distributionId }: PropsType) => {
  const REQUEST_SIZE = 30;

  const projectId = useProjectId();

  const currentPageRef = useRef<number>(1);
  const hasMoreRef = useRef<boolean>(true);

  const [distributeVideoData, setDistributeVideoData] = useState<DistributeVideoItem[]>([]);
  const { loading, run, cancel } = useRequest(getDistributeDetailList, {
    defaultParams: [
      {
        distributionId,
        projectId: projectId!,
        page: currentPageRef.current,
        size: REQUEST_SIZE,
      },
    ],
    manual: projectId ? false : true,
    throttleWait: 300,
    onSuccess(res, params) {
      if (res.code === 0 && res.data) {
        const data = res.data;
        const items = data.items;

        if (items) {
          setDistributeVideoData((preData) => {
            return preData.concat(items);
          });
        }

        if (params[0].page) {
          currentPageRef.current = params[0].page;
        }

        if (!items || !items.length || distributeVideoData.length + items.length >= data.total) {
          hasMoreRef.current = false;
        }
      } else {
        hasMoreRef.current = false;
      }
    },
  });

  useEffect(() => {
    return () => {
      cancel();
    };
  }, []);

  const column: ColumnsType<DistributeVideoItem> = [
    {
      title: '',
      dataIndex: 'id',
      width: 48,
      align: 'center',
      render(_: any, _1: any, index: number) {
        return index + 1;
      },
    },
    {
      title: '视频',
      dataIndex: 'videoUrl',
      width: 100,
      render(_: any, record: DistributeVideoItem) {
        return (
          <div className="flex h-[80px] w-[60px] items-center justify-center">
            <XGVideoCard
              playableVideoUrl={record.videoUrl}
              videoUrl={record.videoUrl}
              coverUrl={record.coverUrl}
              width={60}
              height={80}
              videoWidth={315}
              videoHeight={562}
              noShowTag={true}
            />
          </div>
        );
      },
    },
    {
      title: '账号信息',
      dataIndex: 'nickname',
      width: 150,
      render(_: any, record: DistributeVideoItem) {
        return (
          <div className="flex w-full items-center overflow-hidden">
            <Avatar size="small" src={record.avatar} />
            <Popover content={record.nickname}>
              <span
                title={record.nickname}
                className="inline-block max-w-24 overflow-hidden text-ellipsis text-nowrap"
              >
                {record.nickname}
              </span>
            </Popover>
            {record.blueVipFlag === 1 ? <img src={BlueVip} width={18} /> : null}
          </div>
        );
      },
    },
    {
      title: '门店简称',
      dataIndex: 'storeName',
      width: 150,
      render(_: any, record: DistributeVideoItem) {
        const teamFields = record.teamFields;
        const storeNameField = teamFields.find((item) => item.fieldName === '门店简称');

        if (storeNameField) {
          return storeNameField.fieldValue;
        } else {
          return '-';
        }
      },
    },
    {
      title: '所属大区',
      dataIndex: 'region',
      width: 100,
      render(_: any, record: DistributeVideoItem) {
        const teamFields = record.teamFields;
        const areaField = teamFields.find((item) => item.fieldName === '大区');

        if (areaField) {
          return areaField.fieldValue;
        } else {
          return '-';
        }
      },
    },
    {
      title: '平台',
      dataIndex: 'platform',
      width: 90,
      render(_: any, record: DistributeVideoItem) {
        return PLATFORM_MAP[record.platform];
      },
    },
    {
      title: '发布类型',
      dataIndex: 'type',
      width: 90,
      render(_: any, record: DistributeVideoItem) {
        return DISTRIBUTE_TYPES[record.type];
      },
    },
    {
      title: '状态',
      width: 100,
      dataIndex: 'status',
      render(_: any, record: DistributeVideoItem) {
        const status = record.status;
        if (typeof status === 'number') {
          const statusText = STATUS_VALUE_ENUM[status].text;

          const tagStatus = {
            0: 'default',
            1: 'processing',
            2: 'success',
            3: 'fail',
            4: 'default',
          }[status] as 'success' | 'fail' | 'default' | 'processing';

          return <StatusTag status={tagStatus}>{statusText}</StatusTag>;
        } else {
          return '-';
        }
      },
    },
    {
      title: '执行开始时间',
      width: 160,
      dataIndex: 'taskStartTime',
    },
    {
      title: '执行结束时间',
      width: 160,
      dataIndex: 'taskEndTime',
    },
    {
      title: '操作',
      dataIndex: 'operation',
      width: 150,
      render() {
        return null;
      },
    },
  ];

  const onListScroll = (e: React.UIEvent<HTMLDivElement, UIEvent>) => {
    if (!hasMoreRef.current || loading) {
      return;
    }

    const target = e.target as HTMLDivElement;

    if (target.scrollHeight - target.scrollTop <= target.offsetHeight + 200) {
      if (projectId) {
        run({
          projectId,
          distributionId,
          page: currentPageRef.current + 1,
          size: REQUEST_SIZE,
        });
      }
    }
  };

  return (
    <div>
      <DistributeVideoScrollListWrap
        showHeader={false}
        columns={column}
        dataSource={distributeVideoData}
        pagination={false}
        scroll={{ y: 105 * 4.5 }}
        onScroll={onListScroll}
        loading={loading && !distributeVideoData.length}
        rowKey="id"
      />
    </div>
  );
};

export default DistributeVideoScrollList;
