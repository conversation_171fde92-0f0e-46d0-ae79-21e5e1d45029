import useProjectId from '@/hooks/useProjectId';
import { ExportTeamList, TeamSettingItem, TeamSettingPage } from '@/services/project';
import { proTableRequestAdapter } from '@/utils';
import { formatNum, initDataFilter, STICKY_OFFSETHEADER, transferParams } from '@/utils/common';
import {
  proTableOptionsConfig,
  proTablePaginationConfig,
  proTableSearchConfig,
} from '@/utils/proTableConfig';
import type {
  ActionType,
  ColumnsState,
  ProColumns,
  ProFormInstance,
} from '@ant-design/pro-components';
import { PageContainer, ProCard, ProTable } from '@ant-design/pro-components';
import { message, Select, Space } from 'antd';

import DataFilter from '@/components/dataFilter';
import DynamicTree from '@/components/dynamicTree';
import ExportButton from '@/components/exportButton';
import TimeFilter from '@/components/ui/timeFilter';
import useGetTeamFieldList from '@/hooks/useFields';
import { renderCustomColumns } from '@/components/renderCustomColumns';
import { formatSecond, getTimeByType } from '@/utils/time';
import { useAsyncEffect, useSetState } from 'ahooks';
import { useEffect, useRef, useState } from 'react';
import useLiveAfkFG from '@/hooks/fg/useLiveAfkFG';
import { usePollingExport } from '@/hooks/usePollingExport';
import { omit } from 'lodash-es';
import { useAtomValue } from 'jotai';
import { selectPlatformAtom } from '../atom';
import BreadCrumbSimple from '@/components/ui/breadcrumbSimple';
import AggregateQueryInput from '@/components/aggregateQueryInput';
import { getSumColumnsWidth } from '@/utils/table';
import { GetTagGroups, TagsType } from '@/services/tag';
import { CommonTagGroup } from '@/services/typings';
import PlatformSwitch from '@/components/platformSwitch';

const TeamSetting = () => {
  const projectId = useProjectId();
  const actionRef = useRef<ActionType>();
  const formRef = useRef<ProFormInstance>();
  const filedIdArrRef = useRef<string[]>([]);
  const firstAddColumns = useRef(false);
  const [rangeTime, setRangeTime] = useState<(string | undefined)[]>(getTimeByType('day'));
  const [dataFilterState, setDataFilterState] = useSetState(initDataFilter);
  const teamFieldListRef = useGetTeamFieldList(projectId);
  const paramsRef = useRef<any>({});
  const { pollingExport, percent, pollingLoading } = usePollingExport();
  const platform = useAtomValue(selectPlatformAtom);
  const options = [
    { value: 'liveAfkDuration', label: '空播时长' },
    { value: 'liveAfkRate', label: '空播率' },
  ];
  const tagResRef = useRef<CommonTagGroup[] | null | undefined>(null);
  const afkState = useLiveAfkFG(projectId);

  useAsyncEffect(async () => {
    const res = await GetTagGroups(TagsType.accountTag, { projectId });
    tagResRef.current = res.data;
  }, []);

  const defaultColumns: ProColumns<TeamSettingItem>[] = [
    {
      dataIndex: 'teamCode',
      hideInSearch: true,
      hideInTable: true,
    },
    {
      title: '聚合查询',
      dataIndex: 'aggregateQuery',
      hideInTable: true,
      formItemProps: {
        label: null,
      },
      search: {
        transform: (value: string[]) => {
          if (value.length > 0) {
            if (value[0] === 'liveAfkDuration') {
              return {
                [value[0]]: value[1] ? Number(value[1]) * 60 : undefined,
              };
            } else if (value[0] === 'liveAfkRate') {
              return {
                [value[0]]: value[1] ? Number(value[1]) / 100 : undefined,
              };
            } else {
              return {
                [value[0]]: value[1],
              };
            }
          }
        },
      },
      renderFormItem: () => {
        return <AggregateQueryInput selectOptions={options} />;
      },
    },
    {
      title: '动态维度筛选',
      dataIndex: 'teamCodeList',
      hideInTable: true,
      formItemProps: {
        label: null,
      },
      renderFormItem: () => {
        return <DynamicTree />;
      },
    },
    {
      title: '门店编码',
      dataIndex: 'teamCode',
      align: 'right',
      width: 90,
      hideInSearch: true,
    },
    {
      title: '账号标签',
      dataIndex: 'tagIds',
      align: 'left',
      width: 150,
      fieldProps: {
        placeholder: '账号标签',
      },
      formItemProps: {
        label: null,
      },
      ellipsis: true,
      hideInTable: true,
      renderFormItem: () => {
        return (
          <Select
            mode="multiple"
            allowClear
            maxTagCount="responsive"
            options={tagResRef?.current?.map((item) => {
              return {
                label: item?.name,
                options: item?.tags.map((tag) => {
                  return {
                    label: tag?.name,
                    value: tag?.id,
                  };
                }),
              };
            })}
          />
        );
      },
    },
    {
      title: '账号数量',
      dataIndex: 'accountCount',
      align: 'right',
      width: 90,
      hideInSearch: true,
      render: (text, record) => formatNum(record.accountCount),
    },
    {
      title: '粉丝增量',
      dataIndex: 'followerGrowth',
      align: 'right',
      width: 90,
      hideInSearch: true,
      render: (text, record) => formatNum(record.followerGrowth),
      sorter: true,
    },
    {
      title: '粉丝总数',
      dataIndex: 'followerCount',
      align: 'right',
      width: 90,
      hideInSearch: true,
      render: (text, record) => formatNum(record.followerCount),
      sorter: true,
    },
    {
      title: '作品总数',
      dataIndex: 'postCount',
      align: 'right',
      width: 90,
      hideInSearch: true,
      render: (text, record) => formatNum(record.postCount),
      sorter: true,
    },
    {
      title: '作品发布数',
      dataIndex: 'postPublishCount',
      align: 'right',
      width: 110,
      hideInSearch: true,
      render: (text, record) => formatNum(record.postPublishCount),
      sorter: true,
    },
    {
      title: '作品播放量',
      dataIndex: 'postPlayCount',
      align: 'right',
      width: 110,
      hideInSearch: true,
      render: (text, record) => formatNum(record.postPlayCount),
      sorter: true,
    },
    {
      title: '作品点赞数',
      dataIndex: 'postDiggCount',
      align: 'right',
      width: 110,
      hideInSearch: true,
      render: (text, record) => formatNum(record.postDiggCount),
      sorter: true,
    },
    {
      title: '作品评论数',
      dataIndex: 'postCommentCount',
      align: 'right',
      width: 110,
      hideInSearch: true,
      render: (text, record) => formatNum(record.postCommentCount),
      sorter: true,
    },
    {
      title: '作品收藏数',
      dataIndex: 'postCollectCount',
      align: 'right',
      width: 110,
      hideInSearch: true,
      render: (text, record) => formatNum(record.postCollectCount),
    },
    {
      title: '作品分享数',
      dataIndex: 'postShareCount',
      align: 'right',
      width: 110,
      hideInSearch: true,
      render: (text, record) => formatNum(record.postShareCount),
      sorter: true,
    },
    {
      title: '直播场次',
      dataIndex: 'liveCount',
      align: 'right',
      width: 90,
      hideInSearch: true,
      render: (text, record) => formatNum(record.liveCount),
      sorter: true,
    },
    {
      title: '直播时长',
      dataIndex: 'liveDuration',
      align: 'right',
      width: 150,
      hideInSearch: true,
      render: (text, record) => formatSecond(record.liveDuration),
      sorter: true,
    },
    {
      title: '检测时长',
      dataIndex: 'liveAfkCheckDuration',
      align: 'right',
      tooltip: '用于检测空播挂播的片段时长',
      width: 150,
      hideInSearch: true,
      render: (text, record) => formatSecond(record.liveAfkCheckDuration),
    },
    {
      title: '空播时长',
      dataIndex: 'liveAfkDuration',
      align: 'right',
      tooltip: '画面无变化且讲解内容和直播无关的时长',
      width: 150,
      hideInSearch: true,
      render: (text, record) => formatSecond(record.liveAfkDuration, '未检测'),
      sorter: true,
    },
    {
      title: '空播率',
      dataIndex: 'liveAfkRate',
      align: 'right',
      tooltip: '空播时长/实际直播时长，由于实际直播时长需要次日更新，所以空播率也需要次日更新',
      width: 150,
      hideInSearch: true,
      render: (text, record) => {
        return (
          <span>
            {record.liveAfkRate
              ? (Number(record.liveAfkRate) * 100).toFixed(2) + '%'
              : record.liveAfkDuration
                ? '未判定'
                : '未检测'}
          </span>
        );
      },
    },
    {
      title: '轻微空播挂播场次',
      dataIndex: 'liveMinorAfkCount',
      align: 'right',
      tooltip: '轻微空播挂播场次指20%≤空播率＜50%的直播场次',
      width: 180,
      hideInSearch: true,
      sorter: true,
    },
    {
      title: '严重空播挂播场次',
      dataIndex: 'liveSeriousAfkCount',
      align: 'right',
      tooltip: '严重空播挂播场次指空播率≥50%的直播场次',
      width: 180,
      hideInSearch: true,
      sorter: true,
    },
    {
      title: '直播观看人次',
      dataIndex: 'liveViewTime',
      align: 'right',
      width: 120,
      hideInSearch: true,
      render: (text, record) => formatNum(record.liveViewTime),
      sorter: true,
    },
    {
      title: '直播观看人数',
      dataIndex: 'liveViewCount',
      align: 'right',
      width: 120,
      hideInSearch: true,
      render: (text, record) => formatNum(record.liveViewCount),
      sorter: true,
    },
    {
      title: '直播点赞次数',
      dataIndex: 'liveDiggCount',
      align: 'right',
      width: 120,
      hideInSearch: true,
      render: (text, record) => formatNum(record.liveDiggCount),
      sorter: true,
    },
    {
      title: '直播评论量',
      dataIndex: 'liveCommentCount',
      align: 'right',
      width: 110,
      hideInSearch: true,
      render: (text, record) => formatNum(record.liveCommentCount),
      sorter: true,
    },
    {
      title: '直播粉丝增量',
      dataIndex: 'liveFollowerGrowth',
      align: 'right',
      width: 120,
      hideInSearch: true,
      render: (text, record) => formatNum(record.liveFollowerGrowth),
      sorter: true,
    },
    {
      title: '线索总数',
      dataIndex: 'clueCount',
      align: 'right',
      width: 120,
      hideInSearch: true,
      render: (text, record) => formatNum(record.clueCount),
      sorter: true,
    },
  ];
  const [columns, setColumns] = useState<ProColumns<TeamSettingItem>[]>(defaultColumns);
  const [columnsStateMap, setColumnsStateMap] = useState<Record<string, ColumnsState>>({
    teamCode: {
      show: false,
    },
  });
  const [isEmptyTable, setIsEmptyTable] = useState(false);

  useEffect(() => {
    // 空挂播相关的列
    const afkColumns = [
      'liveAfkCheckDuration',
      'liveAfkDuration',
      'liveAfkRate',
      'liveMinorAfkCount',
      'liveSeriousAfkCount',
      'aggregateQuery', //聚合查询的options都是空挂播相关所以直接隐藏掉
    ];
    // 这里条件的原因是下面ProTable的postData会修改columns列 要保证排在动态列设置后再修改一次 避免修改失效
    if (firstAddColumns.current) {
      const newColumns = columns.map((item) => {
        if (afkColumns.includes(item.dataIndex as string)) {
          return {
            ...item,
            hidden: !afkState,
            // search 会有一些自定义 transform 配置 所以需要继承下来
            search: afkState ? item.search : false,
          };
        }
        return item;
      }) as ProColumns<TeamSettingItem>[];
      setColumns(newColumns);
    }
  }, [firstAddColumns.current, afkState]);

  const handleExport = async () => {
    const omitPageParams = omit(paramsRef.current, ['pageSize', 'current']);
    const res = await ExportTeamList({
      ...omitPageParams,
    });
    if (res.data) {
      pollingExport({ taskId: res.data });
    } else {
      message.error('导出失败,请重试');
    }
  };

  return (
    <PageContainer title={<BreadCrumbSimple breadcrumbs={['平台管理', '团队列表']} />}>
      <div className="rounded-lg bg-white px-4 py-2">
        <Space>
          <TimeFilter value={rangeTime} onChange={(value) => setRangeTime(value)} />
          <PlatformSwitch />
        </Space>
      </div>
      <ProCard style={{ marginTop: '16px' }}>
        <ProTable<TeamSettingItem>
          columns={columns}
          actionRef={actionRef}
          formRef={formRef}
          ghost
          sticky={{ offsetHeader: STICKY_OFFSETHEADER }}
          tableClassName="custom-table"
          scroll={{ x: isEmptyTable ? getSumColumnsWidth(columns) : 'max-content' }}
          params={{
            projectId,
            startTime: rangeTime[0],
            endTime: rangeTime[1],
            postPlayCount: dataFilterState.videoFilter ? dataFilterState.postPlayCount : undefined,
            liveDuration: dataFilterState.liveFilter
              ? Number(dataFilterState.liveDuration) * 60
              : undefined,
            platform,
          }}
          onReset={() => {
            setDataFilterState(initDataFilter);
          }}
          postData={(data: TeamSettingItem[]) => {
            // 判断表格数据是否为空
            data.length === 0 ? setIsEmptyTable(true) : setIsEmptyTable(false);
            if (!firstAddColumns.current && data.length > 0) {
              renderCustomColumns(teamFieldListRef.current, true).then(
                ({ customColumns, fieldIdArr }) => {
                  filedIdArrRef.current = fieldIdArr;
                  const newColumns = [
                    ...columns,
                    ...customColumns,
                  ] as ProColumns<TeamSettingItem>[];
                  setColumns(newColumns);
                },
              );
              firstAddColumns.current = true;
            }
            // 将teamFields内的字段都抽出来
            const formatData = data.map((item) => {
              const res = { ...item };
              const teamFields = res.teamFieldList;
              if (teamFields) {
                teamFields.forEach((field) => {
                  (res as any)[field.fieldId] = field.value;
                });
              }
              return res;
            });
            return formatData;
          }}
          beforeSearchSubmit={(params) => {
            const fieldList = transferParams(params, filedIdArrRef.current);
            if (fieldList.length > 0) {
              return { ...params, fieldList };
            }
            return params;
          }}
          request={(params, sorter, filter) => {
            paramsRef.current = params;
            return proTableRequestAdapter(params, sorter, filter, TeamSettingPage);
          }}
          search={{ ...proTableSearchConfig }}
          options={{ ...proTableOptionsConfig }}
          pagination={{ ...proTablePaginationConfig }}
          headerTitle={
            <Space>
              <DataFilter
                dataFilterState={dataFilterState}
                setDataFilterState={setDataFilterState}
              />
            </Space>
          }
          toolBarRender={() => [
            <ExportButton
              exportFn={handleExport}
              loading={pollingLoading}
              percent={percent}
              key="export"
            />,
          ]}
          rowKey="teamCode"
          columnsState={{
            value: columnsStateMap,
            onChange: setColumnsStateMap,
            persistenceKey: 'TeamSetting-Table-Columns',
            persistenceType: 'localStorage',
          }}
          dateFormatter="string"
        />
      </ProCard>
    </PageContainer>
  );
};

export default TeamSetting;
