.livingMapContainer {
  position: relative;

  background: repeat
    url(https://media-1307444343.cos.ap-guangzhou.myqcloud.com/project_icon/map-bg-light%20%281%29.jpeg);

  &.dark-bg {
    background: repeat
      url(https://media-1307444343.cos.ap-guangzhou.myqcloud.com/project_icon/map-bg-dark%20%281%29.png);

    :global {
      .livingStoreBoardWideText {
        color: #ffffffc7;
      }

      .livingStoreBoardShallowText {
        color: #ffffffc7;
      }

      .livingStoreBoardXShallowText {
        color: #ffffff45;
      }
    }
  }
}

.livingStoreBoardWideText {
  color: #1c2131;
}

.livingStoreBoardShallowText {
  color: #696666;
}

.livingStoreBoardXShallowText {
  color: #a1a5b0;
}

.livingStoreBoardBlueText {
  color: #1e5eff;
}

.livingStoreBoardSmallText {
  font-size: 10px;
}

.livingStoreBoardMiddleText {
  font-size: 12px;
}

.livingStoreBoardLargeText {
  font-size: 14px;
}

.livingStoreBoardXLargeText {
  font-size: 16px;
  line-height: 20px;
}

.livingStoreBoardLink {
  cursor: pointer;
  transition: color 0.3s;

  &:hover {
    color: #1e5eff !important;
  }
}

.livingStoreListWrap {
  max-height: 358px;
  margin-top: 8px;
  overflow-y: auto;

  &::-webkit-scrollbar {
    display: none;
  }

  .livingStoreItem {
    cursor: pointer;
    transition: background-color 0.3s;

    &:hover {
      background-color: #1e5eff;

      .livingStoreBoardBlueText,
      .livingStoreBoardShallowText {
        color: #fff;
      }
    }
  }
}
