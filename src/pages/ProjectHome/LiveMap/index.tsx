import { useCallback, useEffect, useMemo, useRef, useState } from 'react';

import * as echarts from 'echarts';
import { ECharts } from 'echarts';
import ReactECharts from 'echarts-for-react';
import CityLatLng from './city-lat-lng';

import useEchartsMap from '@/hooks/useEchartsMap';
import useProjectId from '@/hooks/useProjectId';

import {
  ContainerHeight,
  FieldValues,
  formatFieldValuesToList,
  GeoLightOption,
  getLivingMaxMin,
  getStoreMarkScatterSeriesOption,
  LiveNumVisualMapColor,
  LiveNumVisualMapText,
  TooltipOption,
  transferLiveInfoList2MapData,
} from './live-map-config';

import LivingListBoard from './components/LivingListBoard';
import LivingStoreBoard from './components/LivingStoreBoard';

import { QueryLiveMapList } from '@/services/project';
import { LiveInfo, SimpleTeamFieldItem } from '@/services/typings';

import { message } from 'antd';

import { debounce } from 'lodash-es';

import { useUpdateEffect } from 'ahooks';
import styles from './style.module.less';
import { cn } from '@/lib/utils';

const LiveMap = () => {
  const projectId = useProjectId();
  const chinaMapRegistered = useEchartsMap('china'); // 注册中国地图

  const [echartsOptions, setEchartsOptions] = useState(); // 地图配置
  const [currentStore, setCurrentStore] = useState<LiveInfo | undefined>(); // 当前选中门店
  const [loadingListCount, setLoadingListCount] = useState<number>(0); // 正在加载列表
  const [liveMapList, setLiveMapList] = useState<LiveInfo[]>(); // 直播列表
  const [storeCount, setStoreCount] = useState<number>(); // 直播门店总数
  const [fieldValues, setFieldValues] = useState<FieldValues>(); // 搜索组件字段对应的值
  const [treeValue, setTreeValue] = useState<string[] | undefined>(undefined); // 动态维度搜索
  const [isDarkMode, setIsDarkMode] = useState(localStorage.getItem('darkMode') === 'true');

  const fieldValuesRef = useRef<FieldValues>();
  const currentLivingEndRef = useRef<boolean>(false); // 当前直播已结束
  const eChartInstanceRef = useRef<ECharts>(); // echart实例

  // 添加监听器以响应暗黑模式变化
  useEffect(() => {
    const handleStorageChange = () => {
      setIsDarkMode(localStorage.getItem('darkMode') === 'true');
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, []);

  // 地图数据
  const mapData = useMemo(() => {
    if (liveMapList) {
      const data = transferLiveInfoList2MapData(liveMapList);
      return data;
    }
  }, [liveMapList]);

  // 加载直播门店列表数据
  const fetchLiveMapData = (fieldList?: SimpleTeamFieldItem[], treeValue?: string[]) => {
    if (projectId) {
      setLoadingListCount((c) => c + 1);
      QueryLiveMapList({
        projectId,
        fieldList,
        teamCodeList: treeValue,
      })
        .then((res) => {
          if (res.code === 0) {
            const data = res.data;
            if (data) {
              setLiveMapList(data.liveInfoList);
              setStoreCount(data.storeCount);

              setCurrentStore((currentStore) => {
                // 当前观看的直播结束 自动切换到人数最多的直播
                if (currentLivingEndRef.current) {
                  const mostHotLiving = data.liveInfoList[0];

                  if (mostHotLiving) {
                    return mostHotLiving;
                  }
                } else if (currentStore) {
                  // 更新当前直播门店数据
                  const currentStoreKey = currentStore.accountId + currentStore.platform;
                  let newStore: LiveInfo | undefined;

                  if (data.liveInfoList) {
                    data.liveInfoList.some((infoItem) => {
                      const infoKey = infoItem.accountId + infoItem.platform;

                      if (infoKey === currentStoreKey) {
                        newStore = infoItem;
                        return true;
                      }
                    });
                  }

                  if (newStore) {
                    return newStore;
                  }
                }

                return currentStore;
              });
            } else {
              setLiveMapList([]);
              setStoreCount(0);
            }

            message.success('直播列表已刷新');
          } else {
            message.error(res.msg || '获取直播列表失败');
          }
        })
        .finally(() => {
          setLoadingListCount((c) => c - 1);
        });
    }
  };

  // 节流获取直播门店列表数据
  const debouncedFetchLiveMapData = useCallback(
    debounce((fieldList?: SimpleTeamFieldItem[], treeValue?: string[]) => {
      fetchLiveMapData(fieldList, treeValue);
    }, 700),
    [],
  );

  useEffect(() => {
    currentLivingEndRef.current = false;
  }, [currentStore]);

  useEffect(() => {
    fetchLiveMapData();
  }, []);

  useUpdateEffect(() => {
    fieldValuesRef.current = fieldValues;
    debouncedFetchLiveMapData(formatFieldValuesToList(fieldValues), treeValue);
  }, [fieldValues, treeValue]);

  const initEchartsOption = () => {
    const options: any = {
      geo: GeoLightOption,
      tooltip: TooltipOption,
    };

    setEchartsOptions(options);
  };

  const setMapDataToEchart = () => {
    if (eChartInstanceRef.current && mapData) {
      const options: any = {
        visualMap: [
          {
            id: 'echart-map-visual-map',
            type: 'continuous',
            min: getLivingMaxMin(mapData)[0],
            max: getLivingMaxMin(mapData)[1],
            text: LiveNumVisualMapText,
            inRange: {
              color: LiveNumVisualMapColor,
            },
          },
        ],
        series: [
          {
            id: 'map-data',
            name: '各省正在直播数量',
            type: 'map',
            map: 'china',
            geoIndex: 0,
            label: {
              show: true,
            },
            data: mapData,
          },
        ],
      };
      eChartInstanceRef.current.setOption(options);
    }
  };

  // 标记直播门播店位置
  const markLivingStore = (currentStore: LiveInfo) => {
    if (eChartInstanceRef.current) {
      const cityName = currentStore.city;

      // 根据城市名字查找对应位置
      let cityPosition;

      if (cityName) {
        Object.keys(CityLatLng).some((item) => {
          if (cityName.indexOf(item) >= 0) {
            cityPosition = CityLatLng[item as keyof typeof CityLatLng];
            return true;
          }
        });
      }

      if (cityPosition) {
        const scatterSeriesOption = getStoreMarkScatterSeriesOption({
          name: currentStore.nickname,
          label: currentStore.fullName
            ? `${currentStore.nickname}\n${currentStore.fullName}`
            : currentStore.nickname,
          cityPosition,
        });
        eChartInstanceRef.current.setOption({
          // @ts-ignore TODO
          series: scatterSeriesOption,
        });
      }
    }
  };

  useEffect(() => {
    if (currentStore) {
      markLivingStore(currentStore);
    }
  }, [currentStore]);

  // 地图加载完成时配置地图
  useEffect(() => {
    if (chinaMapRegistered) {
      initEchartsOption();
    }
  }, [chinaMapRegistered]);

  // 直播列表数据改变时重新配置地图
  useEffect(() => {
    // TODO 临时解决方案
    setTimeout(() => {
      setMapDataToEchart();
    }, 1000);
  }, [liveMapList, eChartInstanceRef.current]);

  // 没有选中门店时 默认选中第一个
  useEffect(() => {
    if (liveMapList && liveMapList.length && !currentStore) {
      setCurrentStore(liveMapList[0]);
    }
  }, [liveMapList]);

  // 每10分钟刷新直播列表
  useEffect(() => {
    let timer = setInterval(
      () => {
        fetchLiveMapData(formatFieldValuesToList(fieldValuesRef.current));
      },
      1000 * 60 * 10,
    );

    return () => {
      clearInterval(timer);
      // @ts-ignore
      timer = null;
    };
  }, []);

  return (
    <div
      className={cn(
        styles.livingMapContainer,
        isDarkMode && styles['dark-bg'],
        'transition-colors duration-300',
      )}
      style={{
        height: ContainerHeight,
      }}
    >
      {echartsOptions && (
        <ReactECharts
          echarts={echarts}
          style={{ height: ContainerHeight }}
          option={echartsOptions}
          onChartReady={(chartInstance) => (eChartInstanceRef.current = chartInstance)}
        />
      )}

      {/* 左上角直播列表看板 */}
      <LivingListBoard
        loadingList={loadingListCount > 0}
        refreshList={() => {
          fetchLiveMapData(formatFieldValuesToList(fieldValues));
        }}
        selectedStore={currentStore}
        onSelectStore={setCurrentStore}
        liveMapList={liveMapList}
        storeCount={storeCount}
        fieldValues={fieldValues}
        setFieldValues={setFieldValues}
        treeValue={treeValue}
        setTreeValue={setTreeValue}
      />

      {/* 右边直播门店看板 */}
      {currentStore && (
        <LivingStoreBoard
          currentStore={currentStore}
          onLivingEnd={() => (currentLivingEndRef.current = true)}
        />
      )}
    </div>
  );
};

export default LiveMap;
