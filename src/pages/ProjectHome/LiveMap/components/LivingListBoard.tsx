import { useEffect, useMemo, useState } from 'react';

import { ProCard } from '@ant-design/pro-components';
import { Button, Card, Col, Empty, Flex, Form, Input, Row, Space } from 'antd';

import {
  ClockCircleOutlined,
  DownOutlined,
  SearchOutlined,
  SyncOutlined,
  UpOutlined,
} from '@ant-design/icons';

import useProjectId from '@/hooks/useProjectId';

import { QueryTeamListFields } from '@/services/project';
import { LiveInfo, ProjectTeamFieldItem } from '@/services/typings';

import { FieldValues } from '../live-map-config';

import dayjs from 'dayjs';

import DynamicTree from '@/components/dynamicTree';
import { BizType } from '@/utils/common';
import styles from '../style.module.less';

interface PropsType {
  loadingList: boolean; // 正在加载列表
  refreshList: () => void; // 刷新列表
  selectedStore?: LiveInfo; // 选择的门店
  onSelectStore: (info: LiveInfo | undefined) => void; // 选择门店
  liveMapList?: LiveInfo[]; // 直播列表
  storeCount?: number; // 直播门店总数
  fieldValues?: FieldValues; // 搜索组件字段对应的值
  setFieldValues: (fieldValues: FieldValues) => void;
  treeValue: string[] | undefined;
  setTreeValue: React.Dispatch<React.SetStateAction<string[] | undefined>>;
}

const LivingListBoard = ({
  loadingList,
  selectedStore,
  liveMapList,
  fieldValues,
  setFieldValues,
  refreshList,
  onSelectStore,
  treeValue,
  setTreeValue,
}: PropsType) => {
  const projectId = useProjectId();

  const [showList, setShowList] = useState<boolean>(true); // 展示直播列表

  const [fieldList, setFieldList] = useState<ProjectTeamFieldItem[]>(); // 字段列表

  const [collapse, setCollapse] = useState<boolean>(true); // 折叠

  // 加载到新列表的时间
  const listTime = useMemo<string>(() => {
    return dayjs().format('YYYY/MM/DD HH:mm:ss');
  }, [liveMapList]);

  // 表单值改变 防抖刷新列表
  const onFieldValuesChange = (values: FieldValues) => {
    setFieldValues(values);
  };

  // 过滤出展示的字段
  const showFieldList = useMemo(() => {
    if (fieldList) {
      return fieldList.filter((item) => item.showFlag && item.bizType === BizType.ORDINARY);
    }
  }, [fieldList]);

  // 请求搜索字段
  useEffect(() => {
    if (projectId) {
      QueryTeamListFields({ projectId }).then((res) => {
        if (res.code === 0) {
          setFieldList(res.data);
        }
      });
    }
  }, []);

  useEffect(() => {
    if (liveMapList && liveMapList.length > 0) {
      const firstLive = liveMapList[0];
      if (firstLive) {
        onSelectStore(firstLive);
      }
    } else {
      onSelectStore(undefined);
    }
  }, [liveMapList]);

  return (
    <div style={{ position: 'absolute', zIndex: 1, top: 30, left: 30 }}>
      <Space>
        <Card
          size="small"
          className={[styles.livingStoreBoardMiddleText, styles.livingStoreBoardWideText].join(' ')}
          style={{ height: 32, border: 'none' }}
          bodyStyle={{ padding: 8 }}
        >
          <Space size="small">
            <span>正在直播的账号</span>
            <span className={styles.livingStoreBoardBlueText}>{liveMapList?.length || 0}</span>
            {showList ? (
              <UpOutlined style={{ cursor: 'pointer' }} onClick={() => setShowList(false)} />
            ) : (
              <DownOutlined style={{ cursor: 'pointer' }} onClick={() => setShowList(true)} />
            )}
          </Space>
        </Card>
        <Button
          className={styles.livingStoreBoardMiddleText}
          style={{ border: 'none' }}
          onClick={() => !loadingList && refreshList()}
        >
          <SyncOutlined spin={loadingList} />
          {loadingList ? '正在刷新...' : null}
        </Button>
        {/* <Switch
          checkedChildren="深"
          unCheckedChildren="浅"
          defaultChecked={themeMode === 'dark' ? true : false}
          onChange={(e) => {
            setThemeMode(e ? 'dark' : 'light');
          }}
        /> */}
      </Space>

      {showList && (
        <ProCard
          style={{ marginTop: 8, padding: 0, minWidth: 250 }}
          split="horizontal"
          direction="column"
        >
          <ProCard size="small">
            <Flex
              align="center"
              className={[styles.livingStoreBoardWideText, styles.livingStoreBoardLargeText].join(
                ' ',
              )}
            >
              <img
                src="https://media-**********.cos.ap-guangzhou.myqcloud.com/project_icon/Group%20563.png"
                alt=""
                style={{ width: 10, height: 10, marginRight: 4 }}
              />
              正在直播
            </Flex>
            <Flex
              align="center"
              className={[
                styles.livingStoreBoardXShallowText,
                styles.livingStoreBoardSmallText,
              ].join(' ')}
            >
              <ClockCircleOutlined
                className={styles.livingStoreBoardBlueText}
                style={{ marginRight: 4 }}
              />
              {listTime}
            </Flex>

            <div style={{ marginTop: 8 }}>
              <DynamicTree
                value={treeValue}
                setValue={setTreeValue}
                style={{ width: 182, height: 28, marginBottom: '8px', fontSize: '12px' }}
              />
              <Button
                type="link"
                className={styles.livingStoreBoardMiddleText}
                onClick={() => setCollapse(!collapse)}
              >
                {collapse ? '展开' : '收起'}
              </Button>
              {!collapse && (
                <QueryForm
                  fieldList={showFieldList || []}
                  onValuesChange={onFieldValuesChange}
                  initialValues={fieldValues}
                />
              )}
            </div>
          </ProCard>

          <ProCard size="small" direction="column">
            <Row>
              <Col
                span={16}
                className={[
                  styles.livingStoreBoardMiddleText,
                  styles.livingStoreBoardXShallowText,
                ].join(' ')}
              >
                正在直播账号
              </Col>
              <Col
                span={8}
                className={[
                  styles.livingStoreBoardMiddleText,
                  styles.livingStoreBoardXShallowText,
                ].join(' ')}
                style={{ textAlign: 'center' }}
              >
                直播观看人数
              </Col>
            </Row>
            <div className={styles.livingStoreListWrap}>
              {liveMapList && liveMapList.length ? (
                <Flex vertical={true} gap={8}>
                  {liveMapList.map((item, index) => {
                    const isSelected =
                      selectedStore &&
                      item.accountId === selectedStore.accountId &&
                      item.livePullUrl === selectedStore.livePullUrl;
                    return (
                      <Row
                        key={item.livePullUrl}
                        className={styles.livingStoreItem}
                        onClick={() => {
                          onSelectStore(item);
                        }}
                      >
                        <Col
                          span={16}
                          className={[
                            styles.livingStoreBoardMiddleText,
                            'single-line',
                            isSelected
                              ? styles.livingStoreBoardBlueText
                              : styles.livingStoreBoardShallowText,
                          ].join(' ')}
                        >
                          {index < 3 ? (
                            <img
                              src="https://media-**********.cos.ap-guangzhou.myqcloud.com/project_icon/Vector.png"
                              style={{ width: 10, marginRight: 4 }}
                            />
                          ) : (
                            <span style={{ marginRight: 4 }}>{index + 1}</span>
                          )}
                          {item.nickname}
                        </Col>
                        <Col
                          span={8}
                          className={[
                            styles.livingStoreBoardMiddleText,
                            isSelected
                              ? styles.livingStoreBoardBlueText
                              : styles.livingStoreBoardShallowText,
                          ].join(' ')}
                          style={{ textAlign: 'center' }}
                        >
                          {item.watchCount || 0}人
                        </Col>
                      </Row>
                    );
                  })}
                </Flex>
              ) : (
                <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
              )}
            </div>
          </ProCard>
        </ProCard>
      )}
    </div>
  );
};

const QueryForm = ({
  fieldList,
  initialValues,
  onValuesChange,
}: {
  fieldList: ProjectTeamFieldItem[];
  initialValues?: FieldValues;
  onValuesChange: (values: FieldValues) => void;
}) => {
  return (
    <div>
      <Form
        onValuesChange={(_: any, allValues: FieldValues) => {
          onValuesChange(allValues);
        }}
        initialValues={initialValues}
        component={false}
      >
        <Flex vertical={true} gap={8}>
          {fieldList.map((item) => {
            if (!item.showFlag) {
              return null;
            }
            return (
              <Flex align="center" key={item.id}>
                <Form.Item name={item.id} style={{ marginBottom: 0 }}>
                  <Input
                    placeholder={`请输入${item.fieldName}`}
                    prefix={<SearchOutlined className={styles.livingStoreBoardShallowText} />}
                    className={styles.livingStoreBoardMiddleText}
                    style={{ width: 182, height: 28 }}
                    allowClear
                  />
                </Form.Item>
              </Flex>
            );
          })}
        </Flex>
      </Form>
    </div>
  );
};

export default LivingListBoard;
