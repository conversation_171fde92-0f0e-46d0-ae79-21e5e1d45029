import { lazy, Suspense, useCallback, useEffect, useMemo, useRef, useState } from 'react';

import { RightOutlined } from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';
import { Flex, message, Spin } from 'antd';
import { LiveInfo } from '@/services/typings';

import styles from '../style.module.less';
import Player from 'xgplayer';
import 'xgplayer/dist/index.min.css';
import { useParams } from '@umijs/max';

const LiveInfoCountStat = [
  {
    imgSrc: 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/project_icon/play.png',
    label: '直播观看人数',
    key: 'watchCount' as const,
  },
  {
    imgSrc: 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/project_icon/chart-bar.png',
    label: '总弹幕条数',
    key: 'dmCount' as const,
  },
  {
    imgSrc: 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/project_icon/thumb-up.png',
    label: '总点赞次数',
    key: 'diggCount' as const,
  },
];

interface PropsType {
  currentStore: LiveInfo;
  onLivingEnd: () => void;
}

const XGPlayer = lazy(() => import('@/components/xgplayer'));

const LivingStoreBoard = ({ currentStore, onLivingEnd }: PropsType) => {
  const { industryType } = useParams();
  const videoPlayerRef = useRef<Player | null>(null);
  const [autoPlay, setAutoPlay] = useState<boolean>(false);

  useEffect(() => {
    setAutoPlay(true);
  }, []);

  const onPlayerError = (type: string) => {
    if (type === 'network') {
      message.error('该直播已结束');
      onLivingEnd();
    } else {
      message.error('连接超时，请刷新重试');
    }
  };

  const onPlayerEnded = () => {
    message.warning('该直播已结束');
    onLivingEnd();
  };

  const onVideoReady = useCallback((player: Player) => {
    videoPlayerRef.current = player;

    const cfg = localStorage.getItem('video-card-preview-volume');
    if (cfg) {
      try {
        const { volume, muted } = JSON.parse(cfg);
        player.volume = volume;
        player.muted = muted;
      } catch (err) {
        console.log(err);
      }
    }
  }, []);

  const options = useMemo(() => {
    if (!currentStore.livePullUrl) {
      return null;
    }
    return {
      width: 346,
      height: (346 * 16) / 9,
      url: currentStore.livePullUrl,
      autoplay: autoPlay,
    };
  }, [currentStore.livePullUrl, autoPlay]);

  return (
    <div style={{ position: 'absolute', zIndex: 1, top: '5%', right: 15, width: 346 }}>
      <ProCard style={{ overflow: 'hidden' }} bodyStyle={{ padding: 0 }} direction="column">
        <ProCard size="small">
          <img
            src={currentStore.avatar}
            alt="品牌logo"
            style={{ height: 32, borderRadius: '50%' }}
          />
          <span
            onClick={() =>
              window.open(
                `/live/detail/${currentStore.roomId}/${currentStore.platform}/${industryType}`,
              )
            }
            className={[
              styles.livingStoreBoardLink,
              styles.livingStoreBoardWideText,
              styles.livingStoreBoardLargeText,
            ].join(' ')}
            style={{ marginLeft: 8 }}
          >
            {currentStore.nickname} <RightOutlined />
          </span>
        </ProCard>

        <ProCard size="small" bodyStyle={{ paddingTop: 8 }}>
          <Flex justify="space-between" align="center">
            {LiveInfoCountStat.map((item) => (
              <Flex align="center" key={item.key}>
                <img src={item.imgSrc} style={{ width: 24 }} />
                <div style={{ marginLeft: 4 }}>
                  <div
                    className={[
                      styles.livingStoreBoardXLargeText,
                      styles.livingStoreBoardWideText,
                    ].join(' ')}
                  >
                    {currentStore[item.key] || 0}
                  </div>
                  <div
                    className={[
                      styles.livingStoreBoardXShallowText,
                      styles.livingStoreBoardSmallText,
                    ].join(' ')}
                  >
                    {item.label}
                  </div>
                </div>
              </Flex>
            ))}
          </Flex>
        </ProCard>

        <ProCard ghost>
          <Suspense fallback={<Spin />}>
            <XGPlayer
              url={currentStore.livePullUrl}
              playerOptions={options}
              isLive={true}
              type="flv"
              onPlayerReady={onVideoReady}
              onError={onPlayerError}
              onEnded={onPlayerEnded}
            />
          </Suspense>
        </ProCard>
      </ProCard>
    </div>
  );
};

export default LivingStoreBoard;
