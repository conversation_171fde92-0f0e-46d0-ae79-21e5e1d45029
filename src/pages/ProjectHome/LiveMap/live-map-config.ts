import { LiveInfo, SimpleTeamFieldItem } from '@/services/typings';

// 容器高度
export const ContainerHeight = 'calc(100vh - 56px)';

// 直播门店密集度文本
export const LiveNumVisualMapText = ['直播门店密集', '直播门店稀疏'];

// 直播门店密集度渐变颜色
export const LiveNumVisualMapColor = ['#ABC8E8', '#1C61AD'];
export const LiveNumVisualMapColorDark = ['#647494', '#16294F'];

// 地图基础配置
export const GeoCommonOption = {
  id: 'geo-option',
  map: 'china',
  top: '30%',
  left: '30%',
  roam: true,
  zoom: 1.7,
  scaleLimit: {
    min: 1,
    max: 2.5,
  },
  regions: [
    { name: '河北省', label: { offset: [0, 20] } },
    { name: '香港特别行政区', label: { offset: [10, 0], formatter: '香港' } },
    { name: '澳门特别行政区', label: { offset: [-10, 0], formatter: '澳门' } },
  ],
};

// 地图浅色配置
export const GeoLightOption = {
  ...GeoCommonOption,
  label: {
    show: true,
    color: '#373838',
  },
  itemStyle: {
    borderColor: '#fff',
    borderType: 'dashed',
    areaColor: '#ABC8E8',
    // borderDashOffset: 100,
    // shadowColor: 'rgba(0, 0, 0, 0.1)',
    // shadowBlur: 3,
  },
};

// 地图深色配置
export const GeoDarkOption = {
  ...GeoCommonOption,
  label: {
    show: true,
    color: '#ffffffc7',
  },
  itemStyle: {
    borderColor: '#ffffff45',
    borderType: 'dashed',
    borderDashOffset: 100,
  },
};

// 文本提示配置
export const TooltipOption = {
  trigger: 'item',
  formatter: function (params: any) {
    // @ts-ignore
    if (params.name === '南海诸岛') {
      return '';
    }

    // @ts-ignore
    return params.name + '<br/>正在直播门店数量: ' + params.value;
  },
};

// 深色模式下标注文本颜色
export const VisualMapTextStyleDark = {
  color: '#ffffffc7',
};

// 门店定位标点基础配置
export const StoreMarkScatterOption = {
  id: 'store-mark',
  type: 'scatter',
  coordinateSystem: 'geo',
  tooltip: {
    show: false,
  },
  label: {
    show: true,
    lineHeight: 16,
    position: 'right',
  },
  symbol:
    'path://M3.19683 3.18377C4.60334 1.78552 6.51098 1 8.50008 1C10.4892 1 12.3968 1.78552 13.8033 3.18377C15.2098 4.58201 16 6.47843 16 8.45584C16 10.4333 15.2098 12.3297 13.8033 13.7279L8.50008 19L3.19683 13.7279C2.50036 13.0356 1.94788 12.2137 1.57094 11.3091C1.19401 10.4045 1 9.43497 1 8.45584C1 7.47672 1.19401 6.50717 1.57094 5.60259C1.94788 4.698 2.50036 3.87608 3.19683 3.18377V3.18377ZM8.50008 10.586C9.06837 10.586 9.61338 10.3616 10.0152 9.96208C10.4171 9.5626 10.6428 9.02079 10.6428 8.45584C10.6428 7.8909 10.4171 7.34909 10.0152 6.94961C9.61338 6.55014 9.06837 6.32571 8.50008 6.32571C7.93179 6.32571 7.38678 6.55014 6.98494 6.94961C6.58311 7.34909 6.35735 7.8909 6.35735 8.45584C6.35735 9.02079 6.58311 9.5626 6.98494 9.96208C7.38678 10.3616 7.93179 10.586 8.50008 10.586Z',
  symbolKeepAspect: true,
  symbolSize: 16,
  colorBy: 'data',
  itemStyle: {
    color: '#00BC8B',
    borderColor: '#fff',
  },
  geoIndex: 0,
  zlevel: 9,
};

const AllProvince = [
  '黑龙江省',
  '吉林省',
  '辽宁省',
  '内蒙古自治区',
  '新疆维吾尔自治区',
  '西藏自治区',
  '云南省',
  '广西壮族自治区',
  '广东省',
  '海南省',
  '福建省',
  '江西省',
  '湖南省',
  '贵州省',
  '四川省',
  '重庆市',
  '湖北省',
  '安徽省',
  '浙江省',
  '上海市',
  '江苏省',
  '山东省',
  '河南省',
  '陕西省',
  '宁夏回族自治区',
  '甘肃省',
  '青海省',
  '河北省',
  '天津市',
  '北京市',
  '山西省',
  '台湾省',
  '香港特别行政区',
  '澳门特别行政区',
];

// 获取直播门店最高值 和 最低值
export const getLivingMaxMin = (data: { name: string; value: number }[]): [number, number] => {
  let min = 0;
  let max = 0;

  data.forEach((item) => {
    if (typeof item.value === 'number') {
      if (item.value < min) {
        min = item.value;
      }

      if (item.value > max) {
        max = item.value;
      }
    }
  });

  return [min, max];
};

export interface FieldValues {
  [field: string]: string;
}

export interface MapData {
  name: string;
  value: number;
}

// 处理维度参数成列表
export const formatFieldValuesToList = (
  fieldValues?: FieldValues,
): SimpleTeamFieldItem[] | undefined => {
  if (fieldValues) {
    const list: SimpleTeamFieldItem[] = [];

    for (const key in fieldValues) {
      const value = fieldValues[key];
      if (value) {
        list.push({
          fieldId: Number(key),
          value,
        });
      }
    }

    return list;
  }
};

// 接口数据转成地图数据
export const transferLiveInfoList2MapData = (liveInfoList: LiveInfo[]): MapData[] => {
  const preProvinceWatchCount = new Map();
  let provinceName: string;

  liveInfoList.map((item) => {
    AllProvince.some((provinceItem) => {
      if (provinceItem.indexOf(item.province) >= 0) {
        provinceName = provinceItem;
        return true;
      }
    });

    const oldCount = preProvinceWatchCount.get(provinceName) || 0;
    const newCount = oldCount + 1;
    preProvinceWatchCount.set(provinceName, newCount);
  });

  const mapData: MapData[] = AllProvince.map((item) => {
    return {
      name: item,
      value: preProvinceWatchCount.get(item) || 0,
    };
  });

  return mapData;
};

// 获取门店定位点series配置
export const getStoreMarkScatterSeriesOption = ({
  name,
  label,
  cityPosition,
}: {
  name: string;
  label?: string;
  cityPosition: [number, number];
}) => {
  const option = {
    ...StoreMarkScatterOption,
    name,
    label: {
      ...StoreMarkScatterOption.label,
      formatter: label,
    },
    data: [
      {
        itemStyle: StoreMarkScatterOption.itemStyle,
        value: cityPosition,
      },
    ],
  };

  return option;
};
