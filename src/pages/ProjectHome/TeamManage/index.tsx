import DynamicTree from '@/components/dynamicTree';
import ExportButton from '@/components/exportButton';
import useDouyinAuthLink from '@/hooks/useDouyinAuthLinks';
import useGetTeamFieldList from '@/hooks/useFields';
import useProjectId from '@/hooks/useProjectId';
import { ExportTeam, GetProjectTeamInfo, TeamInfoItem } from '@/services/team';
import { proTableRequestAdapter } from '@/utils';
import { handleExport, STICKY_OFFSETHEADER, transferParams } from '@/utils/common';
import {
  proTableOptionsConfig,
  proTablePaginationConfig,
  proTableSearchConfig,
} from '@/utils/proTableConfig';
import { renderCustomColumns } from '@/components/renderCustomColumns';
import {
  PageContainer,
  ProCard,
  ProColumns,
  ProFormInstance,
  ProTable,
} from '@ant-design/pro-components';
import { Button, Flex, message, Tooltip } from 'antd';
import copy from 'copy-to-clipboard';
import { useCallback, useRef, useState } from 'react';
import TeamInfoModal from './teamInfo';
import { Dealer } from './components/dealer';

const TeamManage = () => {
  const projectId = useProjectId();

  const firstAddColumns = useRef(false);
  const filedIdArrRef = useRef<string[]>([]);
  const [openModal, setOpenModal] = useState(false);
  const [teamId, setTeamId] = useState('');
  const [teamInfos, setTeamInfos] = useState<TeamInfoItem[]>();
  const [customColumns, setCustomColumns] = useState<ProColumns<TeamInfoItem>[]>([]);
  const formRef = useRef<ProFormInstance>();
  const douyinAuthLinks = useDouyinAuthLink(teamInfos, projectId);
  const teamFieldListRef = useGetTeamFieldList(projectId);
  const paramsRef = useRef<any>({});

  const handleShowDetails = (id: string) => {
    setOpenModal(true);
    setTeamId(id);
  };

  const onDataSourceChange = (data?: TeamInfoItem[]) => {
    setTeamInfos(data);
  };

  // 复制抖音授权链接
  const copyDouyinAuthLink = useCallback((link?: string) => {
    if (link) {
      const copySuccess = copy(link);

      if (copySuccess) {
        message.success('授权链接已复制');
      } else {
        message.error('复制失败，请再试一次');
      }
    }
  }, []);

  const defaultColumns: ProColumns<TeamInfoItem>[] = [
    {
      dataIndex: 'id',
      hideInTable: true,
      hideInSearch: true,
    },
    {
      title: '动态维度筛选',
      dataIndex: 'teamCodeList',
      hideInTable: true,
      formItemProps: {
        label: null,
      },
      renderFormItem: () => {
        return <DynamicTree />;
      },
    },
    {
      title: '团队编码',
      dataIndex: 'teamCode',
      width: 100,
      fieldProps: {
        placeholder: '团队编码',
      },
      formItemProps: {
        label: null,
      },
      align: 'left',
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      width: 180,
      hideInSearch: true,
      align: 'left',
    },
    //  账号状态 1：正常 2：有失效 3：待授权 4: 无账号
    {
      title: '状态',
      dataIndex: 'status',
      width: 100,
      formItemProps: {
        label: null,
      },
      fieldProps: {
        placeholder: '状态',
      },
      valueEnum: {
        1: {
          text: '正常',
          color: 'green',
        },
        2: {
          text: '有失效',
          color: 'red',
        },
        3: {
          text: '待授权',
          color: 'gray',
        },
        4: {
          text: '待录入',
          color: 'blue',
        },
      },
      align: 'left',
    },
    {
      title: '抖音账号数量',
      width: 100,
      dataIndex: 'douyinAccountCount',
      hideInSearch: true,
      align: 'right',
    },
    {
      title: '小红书账号数量',
      width: 120,
      dataIndex: 'xiaoHongShuAccountCount',
      hideInSearch: true,
      align: 'right',
    },
    {
      title: '快手账号数量',
      width: 100,
      dataIndex: 'kuaishowAccountCount',
      hideInSearch: true,
      align: 'right',
    },
    {
      title: '视频号账号数量',
      width: 120,
      dataIndex: 'wxVideoAccountCount',
      ellipsis: true,
      hideInSearch: true,
      align: 'right',
    },
    {
      title: '懂车帝账号数量',
      width: 120,
      dataIndex: 'dongchediAccountCount',
      ellipsis: true,
      hideInSearch: true,
      align: 'right',
    },
    {
      title: 'Bilibili账号数量',
      width: 120,
      dataIndex: 'bilibiliAccountCount',
      hideInSearch: true,
      align: 'right',
    },
    {
      title: '汽车之家账号数量',
      width: 120,
      dataIndex: 'qczzAccountCount',
      hideInSearch: true,
      align: 'right',
    },
    {
      title: '微博账号数量',
      width: 120,
      dataIndex: 'weiboAccountCount',
      hideInSearch: true,
      align: 'right',
    },
    {
      title: '微信公众号账号数量',
      width: 120,
      dataIndex: 'wxPublicAccountCount',
      hideInSearch: true,
      align: 'right',
    },
    {
      title: '易车账号数量',
      width: 120,
      dataIndex: 'yicheAccountCount',
      hideInSearch: true,
      align: 'right',
    },
    {
      title: '操作',
      width: 140,
      key: 'option',
      valueType: 'option',
      fixed: 'right',
      align: 'left',
      render: (_, record) => {
        return (
          <Flex justify="flex-start" style={{ marginLeft: '-15px' }}>
            <Tooltip title={douyinAuthLinks[record.teamCode]}>
              <Button
                type="link"
                onClick={() => copyDouyinAuthLink(douyinAuthLinks[record.teamCode])}
              >
                复制授权链接
              </Button>
            </Tooltip>
            <Button type="link" onClick={() => handleShowDetails(record.id)}>
              详情
            </Button>
            <Dealer teamCode={record.teamCode} teamFields={record.teamFields} />
          </Flex>
        );
      },
    },
  ];

  const columns = [...defaultColumns, ...customColumns];

  return (
    <PageContainer title={'团队管理'}>
      <ProCard>
        <ProTable
          onDataSourceChange={onDataSourceChange}
          columns={columns}
          params={{ projectId }}
          scroll={{ x: 'max-content' }}
          formRef={formRef}
          rowKey="id"
          ghost
          sticky={{ offsetHeader: STICKY_OFFSETHEADER }}
          tableClassName="custom-table"
          search={{ ...proTableSearchConfig }}
          options={{ ...proTableOptionsConfig }}
          pagination={{ ...proTablePaginationConfig }}
          toolBarRender={() => [
            <ExportButton
              exportFn={() => handleExport(ExportTeam, paramsRef.current)}
              key="export"
            />,
          ]}
          postData={(data: TeamInfoItem[]) => {
            if (!firstAddColumns.current && data.length > 0) {
              renderCustomColumns(teamFieldListRef.current, true).then(
                ({ customColumns, fieldIdArr }) => {
                  filedIdArrRef.current = fieldIdArr;
                  setCustomColumns(customColumns as ProColumns<TeamInfoItem>[]);
                },
              );
              firstAddColumns.current = true;
            }
            // 将teamFields内的字段都抽出来
            const formatData = data.map((item) => {
              const res = { ...item };
              const teamFields = res.teamFields;
              if (teamFields) {
                teamFields.forEach((field) => {
                  (res as any)[field.fieldId] = field.fieldValue;
                });
              }
              return res;
            });
            return formatData;
          }}
          beforeSearchSubmit={(params) => {
            const fieldList = transferParams(params, filedIdArrRef.current);
            if (fieldList.length > 0) {
              return { ...params, fieldList };
            }
            return params;
          }}
          request={(params, sorter, filter) => {
            paramsRef.current = params;
            return proTableRequestAdapter(params, sorter, filter, GetProjectTeamInfo);
          }}
        />
      </ProCard>
      <TeamInfoModal isModalOpen={openModal} setOpenModal={setOpenModal} teamId={teamId} />
    </PageContainer>
  );
};

export default TeamManage;
