import { TeamField } from '@/services/team';
import { <PERSON><PERSON>, Modal } from 'antd';
import Cookies from 'js-cookie';
import { useMemo, useState } from 'react';

/**
 * 经销商域名
 * new-media-dealer-dev.xiaofeilun.cn
 * new-media-dealer.xiaofeilun.cn
 */
const isDev = process.env.NODE_ENV === 'development';
const domain = window.location.host;
let dealerUrl = '';
if (isDev || domain.includes('dev')) {
  dealerUrl = 'https://new-media-dealer-dev.xiaofeilun.cn/dealer/mobile/personal';
} else if (!domain.includes('dev') && !isDev) {
  dealerUrl = 'https://new-media-dealer.xiaofeilun.cn/dealer/mobile/personal';
}

type DealerProps = {
  teamCode: string;
  teamFields: TeamField[];
  triggerName?: string;
};

// 本地开发打开这个组件会提示未登录原因是：后端的 set-cookie 设置不到 localhost 的所以更新到 dev 后再验证
export function Dealer(props: DealerProps) {
  const token = Cookies.get('new-media-token');
  const { teamCode, teamFields, triggerName } = props;
  const [isModalOpen, setIsModalOpen] = useState(false);

  const storeName = useMemo(() => {
    return teamFields?.find((field) => field.fieldName === '门店简称')?.fieldValue || teamCode;
  }, [teamCode, teamFields]);

  const showModal = () => {
    setIsModalOpen(true);
  };

  const handleOk = () => {
    setIsModalOpen(false);
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };

  const iframeUrl = `${dealerUrl}/?token=${token}&source=iframe&teamCode=${teamCode}`;

  return (
    <>
      <Button type="link" onClick={showModal}>
        {triggerName || '查看门店'}
      </Button>
      <Modal
        title={`门店：${storeName}`}
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        footer={null}
      >
        <iframe
          src={iframeUrl}
          frameBorder={0}
          style={{
            width: '100%',
            height: '100%',
            minHeight: '700px',
          }}
        />
      </Modal>
    </>
  );
}
