import { SvgIcon } from '@/components/SvgIcon';
import { Button } from '@/components/ui/button';
import { formatNum } from '@/utils/common';
import { PaginationProps, Radio, Tabs } from 'antd';
import { isNull } from 'lodash-es';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import styled from 'styled-components';

export const TipDiv = styled.div`
  font-size: 0.75rem;
  line-height: 1.25rem;
  margin: 0.375rem 0rem;
`;

export const customPaginationRender: PaginationProps['itemRender'] = (_, type, originalElement) => {
  if (type === 'prev') {
    return (
      <Button variant="outline" style={{ height: '2rem', width: '2rem', padding: '0rem' }}>
        <ChevronLeft width={18} height={18} />
      </Button>
    );
  }
  if (type === 'next') {
    return (
      <Button
        variant="outline"
        style={{ height: '2rem', width: '2rem', padding: '0rem', marginRight: '.625rem' }}
      >
        <ChevronRight width={18} height={18} />
      </Button>
    );
  }
  return originalElement;
};

export const ButtonTabs = styled(Tabs)`
  &.tabs-line > .ant-tabs-nav {
    height: 2rem;
    margin-bottom: 0rem;
  }

  &.tabs-line > .ant-tabs-content-holder > .ant-tabs-content > .ant-tabs-tabpane > .ant-layout {
    height: 100%;
  }
  &.ant-tabs > .ant-tabs-nav::before {
    border-bottom: 0;
  }
  &.ant-tabs > .ant-tabs-nav > .ant-tabs-nav-wrap > .ant-tabs-nav-list .ant-tabs-tab {
    width: 5.375rem;
    height: 2rem;
    margin: 1.25rem 0 0 0;
    padding: 0;
    border: 0.0625rem solid #3d4350;
    border-right: 0;
    border-left: 0;
    border-radius: 0 0 0 0;
  }
  .ant-tabs-tab .ant-tabs-tab-btn {
    margin: 0 auto;
    color: #8f9093;
  }
  &.ant-tabs > .ant-tabs-nav > .ant-tabs-nav-wrap > .ant-tabs-nav-list .ant-tabs-tab-active {
    background-color: #3870ff;
    div {
      color: #fff;
    }
  }
  &.ant-tabs > .ant-tabs-nav > .ant-tabs-nav-wrap > .ant-tabs-nav-list .ant-tabs-tab:first-of-type {
    border-right: 0.0625rem solid #3d4350;
    border-left: 0.0625rem solid #3d4350;
    border-radius: 0.375rem 0 0 0.375rem !important;
  }

  &.ant-tabs > .ant-tabs-nav > .ant-tabs-nav-wrap > .ant-tabs-nav-list div:nth-last-child(2) {
    border-right: 0.0625rem solid #3d4350;
    border-left: 0.0625rem solid #3d4350;
    border-radius: 0 0.375rem 0.375rem 0 !important;
  }
  .ant-tabs-nav-list {
    border-radius: 0.375rem;
  }

  #showContentCheckbox {
    position: absolute;
    top: 2.1875rem;
    left: 18.75rem;
    white-space: nowrap;
  }
  #showHitState {
    position: absolute;
    top: 2.1875rem;
    right: 1rem;
    white-space: nowrap;
  }
`;

export const ButtonRadioGroup = styled(Radio.Group)`
  .ant-radio-button-wrapper {
    width: 4.5625rem;
    height: 1.8125rem;
    margin-left: 0.625rem;
    line-height: 1.8125rem;
    text-align: center;
    border-inline-start-width: 0.0625rem;
  }
  .ant-radio-button-wrapper:not(:first-child)::before {
    display: none;
  }
  .ant-radio-button-wrapper,
  .ant-radio-button-wrapper:last-child,
  .ant-radio-button-wrapper:first-child {
    border-radius: 0.25rem;
  }
  .ant-radio-button-wrapper {
  }
`;

export const RateRise = styled.div`
  min-width: 6.125rem;
  height: 1.5rem;
  border-radius: 0.25rem;
  font-family: OPPOSans;
  font-size: 0.75rem;
  font-style: normal;
  font-weight: 400;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.0625rem;
  padding: 0.3125rem 0.5rem;
`;

export const RateDecline = styled.div`
  min-width: 6.125rem;
  height: 1.5rem;
  border-radius: 0.25rem;
  font-family: OPPOSans;
  font-size: 0.75rem;
  font-style: normal;
  font-weight: 400;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.0625rem;
  padding: 0.3125rem 0.5rem;
`;

export const Rise = styled.div`
  font-family: OPPOSans;
  font-size: 0.75rem;
  font-style: normal;
  font-weight: 400;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.0625rem;
  width: 3.125rem;
`;

export const Decline = styled.div`
  font-family: OPPOSans;
  font-size: 0.75rem;
  font-style: normal;
  font-weight: 400;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.0625rem;
  width: 3.125rem;
`;

export const renderTips = (value: number, tipsValue?: string, rate?: number) => {
  const renderRate = () => {
    if (!isNull(rate) && rate) {
      if (rate >= 0) {
        return (
          <RateRise style={{ background: '#fbf0f0', color: '#f54848' }}>
            <SvgIcon icon={'local:outline/rate-rise'} y={2} />
            环比增加{rate}%
          </RateRise>
        );
      } else {
        return (
          <RateDecline style={{ background: '#f0f8ee', color: '#30b824' }}>
            <SvgIcon icon={'local:outline/rate-decline'} y={2} />
            环比减少{rate}%
          </RateDecline>
        );
      }
    } else {
      return null;
    }
  };
  return (
    <div>
      <div>{!isNull(value) ? formatNum(value) : '-'}</div>
      <TipDiv style={{ color: '#666' }}>{tipsValue}</TipDiv>
      {renderRate()}
    </div>
  );
};
