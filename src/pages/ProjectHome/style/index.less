.custom-table-search {
  margin-left: -3px;
  form {
    padding: 0 !important;
  }
  .ant-form-item-label {
    display: none;
  }
  .ant-btn {
    padding: 4px 13px;
  }
  @media (min-width: 1201px) and (max-width: 1570px) {
    .ant-btn {
      padding: 4px 10px;
    }
  }
  .ant-space-horizontal {
    gap: 7px !important;
  }

  margin-block-end: 0px !important;
}

.custom-table {
  .ant-table-sticky-scroll-bar {
    display: none;
  }
  .ant-table-container table > thead > tr:first-child > th.highlight-table-header {
    color: #1e5eff !important;
  }
  &.ant-table-wrapper .ant-table-thead > tr > th {
    background: #fafcfe;
    white-space: nowrap;
  }
  .ant-pagination-total-text {
    height: 32px !important;
    line-height: 32px !important;
  }
  .ant-pagination-disabled {
    div {
      color: #d6d6d6 !important;
      border-color: #d6d6d6 !important;
    }

    div:hover {
      color: #d6d6d6 !important;
      background-color: unset !important;
      border-color: #d6d6d6 !important;
    }
  }

  .ant-pagination-jump-next-custom-icon {
    height: 32px !important;
    line-height: 32px !important;
  }

  .ant-pagination-item {
    min-width: 32px !important;
    height: 32px !important;
    font-family: OPPOSans;
    line-height: 32px !important;
    background-color: #e8edfb !important;
    margin-inline: 8px !important;

    &.ant-pagination-item-active {
      line-height: 32px;
      background-color: #2160f9 !important;
      border: none;

      a {
        color: #fff;
        font-weight: 400;
        font-size: 14px;
        font-style: normal;
      }
    }
  }

  .ant-pagination-options {
    .ant-select {
      height: 32px !important;
    }
    .ant-pagination-options-size-changer {
      height: 34px;
    }
  }

  .ant-table-body {
    &::-webkit-scrollbar {
      // display: none;
      opacity: 0;
    }
  }

  :global {
    .ant-table-container .ant-table-cell {
      white-space: nowrap;
    }
  }

  &.expand-row-bgn-none.ant-table-wrapper tr.ant-table-expanded-row > td {
    background: none;
  }

  // 解决 scroll x: max-content的时候空数据空白列的问题
  .live-info {
    width: 336px;
  }

  .video-info {
    width: 336px;
  }

  .afkLevel-info {
    width: 300px;
  }

  .table-90-col {
    width: 90px;
  }

  .table-120-col {
    width: 120px;
  }

  .table-150-col {
    width: 150px;
  }
  .table-200-col {
    width: 200px;
  }

  .table-300-col {
    width: 300px;
  }
  .col-150-text-ellipsis {
    max-width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  &.expand-row-bgn-none.ant-table-wrapper tr.ant-table-expanded-row > td {
    background: none;
  }

  .custom-table-row {
    .ant-table-cell {
      padding: 0 !important;
    }
  }

  .ant-table-header.fix-init-width table {
    width: auto !important;
  }
}
.custom-pagination {
  display: flex;
  align-items: center;
  justify-content: end;

  .ant-pagination-total-text {
    margin-top: 5px;
  }
  .ant-pagination-disabled {
    div {
      color: #d6d6d6 !important;
      border-color: #d6d6d6 !important;
    }

    div:hover {
      color: #d6d6d6 !important;
      background-color: unset !important;
      border-color: #d6d6d6 !important;
    }
  }

  .ant-pagination-jump-next-custom-icon {
    height: 32px !important;
    line-height: 32px !important;
  }

  .ant-pagination-item {
    min-width: 32px !important;
    height: 32px !important;
    font-family: OPPOSans;
    line-height: 32px !important;
    background-color: #e8edfb !important;
    margin-inline: 8px !important;

    &.ant-pagination-item-active {
      line-height: 32px;
      background-color: #2160f9 !important;
      border: none;
      a {
        color: #fff;
        font-weight: 400;
        font-size: 14px;
        font-style: normal;
      }
    }
  }

  .ant-pagination-options {
    margin: 0;

    .ant-pagination-options-size-changer {
      height: 34px;
    }
  }

  .ant-pagination-prev {
    margin: 0;
  }
}

.horizontal-tab {
  .ant-tabs-nav {
    height: 51px;
    padding: 0 24px;
    background: #fff;
    border-radius: 6px;
  }

  .ant-tabs-nav-list {
    .ant-tabs-tab:hover {
      color: #1e5eff;
    }

    .ant-tabs-tab-active {
      .ant-tabs-tab-btn[aria-selected='true'] {
        color: #1e5eff;
        font-size: 14px;
        font-family: OPPOSans;
        text-shadow: none;
      }
    }
    .ant-tabs-ink-bar {
      height: 4px;
      background: #1e5eff;
    }
  }
}

.custom-button {
  color: #4096ff;
  border-color: #4096ff;
}

.account-select-search {
  .ant-pro-query-filter-collapse-button {
    text-wrap: nowrap;
  }
}

.activity-user-list {
  .ant-pro-list-row.ant-pro-list-row-selected {
    background-color: unset;
  }

  // 列表过长优化
  .ant-checkbox-checked .ant-checkbox-inner::after {
    transition: none;
  }

  .ant-checkbox-checked::after {
    animation: none;
  }
}

.customer-list {
  .ant-list-header {
    padding-block: 0;
  }

  .ant-list-items {
    overflow-y: auto;
    max-height: 268px; // 300px - header 部分
  }

  .ant-list-item {
    padding-inline: 0;
  }
}

// 客服设置的表格样式微调
.custom-setting-table {
  .ant-pro-table-list-toolbar-container {
    padding: 0px 18px 3px 5px;
  }
}

.col-150-text-ellipsis {
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.ant-table-thead {
  .ant-table-cell {
    background-color: #fbfcfe !important;
  }
}
