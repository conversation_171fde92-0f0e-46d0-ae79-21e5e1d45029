import BlueVip from '@/assets/blue-vip.png';
import FailImg from '@/assets/fail-img.png';
import DefaultAvatar from '@/assets/default-avatar.png';
import FallBackImg from '@/assets/fallback-img.png';
import NoAnchorImg from '@/assets/no-anchorImg.jpeg';
import AggregateQueryInput from '@/components/aggregateQueryInput';
import AnchorImageViolationInfo from '@/components/anchorImageInfo';
import DataCard from '@/components/dataCard';
import DynamicTree from '@/components/dynamicTree';
import ExInfoDetail from '@/components/exInfo';
import ExportButton from '@/components/exportButton';
import FilterDropdownRadio from '@/components/filterDropdownRadio';
import QualityTypeSelect from '@/components/qualityTypeSelect';
import { renderCustomColumns } from '@/components/renderCustomColumns';
import TagsSelect from '@/components/tags/tagsSelect';
import TagsShow from '@/components/tags/tagsShow';
import PlatformSwitch from '@/components/platformSwitch';
import TimeFilter from '@/components/ui/timeFilter';
import XGVideoCard from '@/components/xgVideoCard';
import useAnchorImageFG from '@/hooks/fg/useAnchorImageFG';
import useLiveAfkFG from '@/hooks/fg/useLiveAfkFG';
import useGetTeamFieldList from '@/hooks/useFields';
import { usePollingExport } from '@/hooks/usePollingExport';
import useProjectId from '@/hooks/useProjectId';
import { AccountPlatformEnum, PlatForm } from '@/utils/platform';
import { GetDataCardTrend } from '@/services/data-card';
import { ExportLiveQuality, LiveQualityItem, LiveQualityPage } from '@/services/quality';
import { proTableRequestAdapter } from '@/utils';
import { formatNum, STICKY_OFFSETHEADER, transferParams } from '@/utils/common';
import { HideScrollBarRow, TagSpan } from '@/utils/commonStyle';
import {
  proTableOptionsConfig,
  proTablePaginationConfig,
  proTableSearchConfig,
} from '@/utils/proTableConfig';
import { disabledDate, formatSecond, getTimeByType } from '@/utils/time';
import {
  DownloadOutlined,
  FilterFilled,
  InfoCircleOutlined,
  LeftOutlined,
  RightOutlined,
} from '@ant-design/icons';
import {
  ActionType,
  ColumnsState,
  ProCard,
  ProColumns,
  ProFormInstance,
  ProTable,
} from '@ant-design/pro-components';
import { FileTextIcon } from '@radix-ui/react-icons';
import { useParams, useRequest } from '@umijs/max';
import { Avatar, Button, Col, Flex, Image, message, Popover, Radio, Space, Tooltip } from 'antd';
import { FilterDropdownProps } from 'antd/es/table/interface';
import dayjs from 'dayjs';
import saveAs from 'file-saver';
import { AnimatePresence, motion } from 'framer-motion';
import { useAtom } from 'jotai';
import { omit } from 'lodash-es';
import { useEffect, useRef, useState } from 'react';
import { selectPlatformAtom } from '../../atom';
import { useGetState } from 'ahooks';
import { getSumColumnsWidth } from '@/utils/table';
import { Copy } from 'lucide-react';
import copy from 'copy-to-clipboard';
import { useHorizontalDragScroll } from '@/hooks/useHorizontalDragScroll';

type HitRuleTimes = {
  hitRuleStartTime: string;
  hitRuleEndTime: string;
};

const videoType = (liveData: LiveQualityItem) => {
  if (liveData?.liveStatus === 0) {
    return 'flv';
  } else if (liveData?.isPullUrl === 0) {
    return 'mp4';
  } else {
    return 'm3u8';
  }
};

const videoUrl = (liveData: LiveQualityItem) => {
  if (liveData.liveStatus === 0) {
    return liveData?.livePullUrl;
  } else if (liveData.isPullUrl === 0) {
    return liveData?.videoUrl;
  } else {
    return liveData?.replayUrls?.[0];
  }
};

const LiveInspection = () => {
  const projectId = useProjectId();
  const { industryType } = useParams();
  const [platform, setPlatform] = useAtom(selectPlatformAtom);
  const options = [
    { value: 'nickname', label: '账号名称' },
    { value: 'liveTitle', label: '直播标题' },
    { value: 'roomId', label: '直播间ID' },
  ];
  const [qualityStatus, setQualityStatus, getQualityStatus] = useGetState<number | undefined>();
  const [liveStatus, setLiveStatus, getLiveStatus] = useGetState<number | undefined>();
  const [recordStatus, setRecordStatus, getRecordStatus] = useGetState<number | undefined>();

  const renderDownloadVideo = (record: LiveQualityItem) => {
    const replayUrlsArr = record.replayUrls;

    const handleDownload = () => {
      if (record.isPullUrl === 0) {
        const url = record.videoUrl;
        if (url) {
          saveAs(url, `video.m3u8`);
        }
      } else if (replayUrlsArr && replayUrlsArr?.length > 0 && record.cleanFlag == 0) {
        for (const [index, url] of replayUrlsArr.entries()) {
          saveAs(url, `video-${index}.m3u8`);
        }
      }
    };

    return (
      <div onClick={handleDownload} className="flex items-center gap-[2px] hover:cursor-pointer">
        <DownloadOutlined />
        下载视频
      </div>
    );
  };

  const defaultColumns: ProColumns<LiveQualityItem>[] = [
    {
      title: '聚合查询',
      dataIndex: 'aggregateQuery',
      hideInTable: true,
      formItemProps: {
        label: null,
      },
      search: {
        transform: (value: string[]) => {
          if (value.length > 0) {
            return {
              [value[0]]: value[1],
            };
          }
        },
      },
      renderFormItem: () => {
        return <AggregateQueryInput selectOptions={options} />;
      },
    },
    {
      title: '动态维度筛选',
      dataIndex: 'teamCodeList',
      hideInTable: true,
      formItemProps: {
        label: null,
      },
      renderFormItem: () => {
        return <DynamicTree />;
      },
    },
    {
      title: '直播信息',
      dataIndex: 'liveInfo',
      hideInSearch: true,
      fixed: 'left',
      align: 'left',
      className: 'live-info',
      filterIcon: () => (
        <FilterFilled style={{ color: getLiveStatus() !== undefined ? '#3B77F6' : undefined }} />
      ),
      filterDropdown: (props: FilterDropdownProps) => {
        return (
          <FilterDropdownRadio
            title="直播状态"
            options={[
              { label: '直播中', value: 0 },
              { label: '已结束', value: 1 },
            ]}
            filterDropdownProps={props}
            setValueChange={setLiveStatus}
          />
        );
      },
      render: (text, record) => {
        const currentVideoType = videoType(record);
        const currentVideoUrl = videoUrl(record);
        return (
          <Flex justify="flex-start" align="stretch" style={{ minWidth: 336 }}>
            <XGVideoCard
              coverUrl={record.cover || FailImg}
              videoUrl={currentVideoUrl}
              playableVideoUrl={currentVideoUrl}
              videoWidth={315}
              videoHeight={562}
              width={81}
              height={108}
              isLive={Number(record.liveStatus) === 0}
              type={currentVideoType}
              noShowTag={true}
              showBottomTag
            />
            <Flex vertical justify="space-between" gap={4} style={{ marginLeft: '8px' }}>
              <div className="line-clamp-2 font-medium text-primary">{record.roomTitle}</div>
              <div className="text-xs font-normal text-black/60">
                {record.liveStartTime}~{record.liveEndTime}
              </div>
              {record.liveStatus === 1 && (
                <div className="text-xs font-normal text-black/60">
                  历时{formatSecond(record.duration)}
                </div>
              )}
              <Space size="large" className="text-sm font-normal text-[#1e5eff]">
                {renderDownloadVideo(record)}
                <div
                  onClick={() => {
                    window.open(
                      `${window.location.origin}/live/detail/${record.roomId}/${record.platform}/${industryType}`,
                    );
                  }}
                  className="flex items-center gap-[2px] hover:cursor-pointer"
                >
                  <FileTextIcon /> 直播复盘
                </div>
              </Space>
            </Flex>
          </Flex>
        );
      },
    },
    {
      title: '账号信息',
      dataIndex: 'nickname',
      width: 300,
      fixed: 'left',
      align: 'left',
      hideInSearch: true,
      render: (text, record) => {
        return (
          <Flex vertical gap={5} style={{ minWidth: 300 }}>
            <Space size="small">
              <Avatar
                size="small"
                src={record.avatar || DefaultAvatar}
                style={{ width: '16px', height: '16px' }}
              />
              <span>{record.nickname}</span>
              {record.blueVipFlag ? <img src={BlueVip} width={18} /> : null}
            </Space>
            <Space>
              <div className="text-xs font-normal text-black/60">
                {[AccountPlatformEnum[platform]?.text]}号: {record.showAccountId}
              </div>
              <Tooltip title="复制账号ID">
                <Copy
                  size={13}
                  className="mt-1 hover:cursor-pointer"
                  onClick={() => {
                    if (record.showAccountId) {
                      copy(record.showAccountId);
                      message.success('账号ID已复制到剪切板');
                    }
                  }}
                />
              </Tooltip>
            </Space>
            <div>{record.tags && <TagsShow tags={record.tags} />}</div>
          </Flex>
        );
      },
    },
    {
      dataIndex: 'roomId',
      hideInSearch: true,
      align: 'center',
      title: '直播间ID',
    },
    {
      title: '录制状态',
      dataIndex: 'recordStatus',
      width: 120,
      valueEnum: {
        1: { text: '未录制' },
        2: { text: '录制中' },
        3: { text: '录制结束' },
      },
      hideInSearch: true,
      align: 'left',
      filterIcon: () => (
        <FilterFilled style={{ color: getRecordStatus() !== undefined ? '#3B77F6' : undefined }} />
      ),
      filterDropdown: (props: FilterDropdownProps) => {
        return (
          <FilterDropdownRadio
            title="录制状态"
            options={[
              { label: '录制中', value: 2 },
              { label: '录制结束', value: 3 },
            ]}
            filterDropdownProps={props}
            setValueChange={setRecordStatus}
          />
        );
      },
    },
    {
      title: '质检状态',
      dataIndex: 'qualityStatus',
      width: 120,
      valueEnum: {
        0: { text: '分析中' },
        1: { text: '已结束' },
      },
      hideInSearch: true,
      align: 'left',
      filterIcon: () => (
        <FilterFilled style={{ color: getQualityStatus() !== undefined ? '#3B77F6' : undefined }} />
      ),
      filterDropdown: (props: FilterDropdownProps) => {
        return (
          <FilterDropdownRadio
            title="质检状态"
            options={[
              { label: '分析中', value: 0 },
              { label: '已结束', value: 1 },
            ]}
            filterDropdownProps={props}
            setValueChange={setQualityStatus}
          />
        );
      },
    },
    {
      title: '账号标签',
      dataIndex: 'tagIds',
      fieldProps: {
        placeholder: '账号标签',
      },
      formItemProps: {
        label: null,
      },
      hideInTable: true,
      renderFormItem: () => {
        return <TagsSelect />;
      },
    },
    {
      title: '质检类型筛选',
      dataIndex: 'qualityCategoryTypeList',
      hidden: true,
      renderFormItem: () => <QualityTypeSelect projectId={projectId} />,
    },
    {
      title: '质检类型',
      dataIndex: 'monitoringNameStr',
      width: 300,
      align: 'left',
      hideInSearch: true,
      render: (_, record) => {
        const { monitoringNameInfoList, anchorImageViolationInfoList } = record;
        if (
          (monitoringNameInfoList && monitoringNameInfoList?.length > 0) ||
          (anchorImageViolationInfoList && anchorImageViolationInfoList?.length > 0)
        ) {
          return (
            <>
              <Flex wrap="wrap" gap={5} justify="flex-start">
                {monitoringNameInfoList?.map(({ monitoringName, monitoringNameId }) => (
                  <Popover
                    key={monitoringName}
                    content={
                      <ExInfoDetail
                        targetType={1}
                        platform={record.platform}
                        projectId={projectId}
                        monitoringNameId={monitoringNameId}
                        targetId={record.roomId}
                      />
                    }
                    title={monitoringName}
                  >
                    <TagSpan $bgColor="#fbebe2" $textColor="#FF7533">
                      {monitoringName}
                    </TagSpan>
                  </Popover>
                ))}
                {anchorImageViolationInfoList?.map(
                  ({ anchorImageViolationTypeName, anchorImageViolationType }) => (
                    <Popover
                      key={anchorImageViolationTypeName}
                      content={
                        <AnchorImageViolationInfo
                          projectId={projectId}
                          platform={record.platform}
                          type={anchorImageViolationType}
                          roomId={record.roomId}
                        />
                      }
                      title={anchorImageViolationTypeName}
                    >
                      <TagSpan $bgColor="#fbebe2" $textColor="#FF7533">
                        {anchorImageViolationTypeName}
                      </TagSpan>
                    </Popover>
                  ),
                )}
              </Flex>
            </>
          );
        } else {
          return '-';
        }
      },
    },
    {
      title: '主播形象',
      dataIndex: 'anchorImage',
      align: 'left',
      width: 150,
      search: false,
      render: (_, record) => {
        switch (record.anchorImageStatus) {
          case 1:
            return '未识别';
          case 2:
            return '识别中';
          case 3:
            return (
              <Image
                width={60}
                height={80}
                style={{ borderRadius: '4px', objectFit: 'cover' }}
                src={record.anchorImage}
                fallback={FallBackImg}
              />
            );
          case 4:
            return (
              <Image
                width={60}
                height={80}
                style={{ borderRadius: '4px', objectFit: 'cover' }}
                src={NoAnchorImg}
              />
            );
        }
      },
    },
    {
      title: '空挂播',
      dataIndex: 'afkLevel',
      width: 130,
      align: 'left',
      tooltip:
        '空播率≥20%以上判定为疑似空播挂播，其中空播率≥50%为严重空播挂播，20%≤空播率＜50%为轻微空播挂播。次日更新判断结果',
      fieldProps: {
        placeholder: '是否疑似空挂播',
        mode: 'multiple',
      },
      formItemProps: {
        label: null,
      },
      valueType: 'select',
      valueEnum: {
        1: '严重空挂播',
        2: '轻微空挂播',
        3: '正常',
        4: '未判定',
      },
      search: {
        transform: (value: number[]) => {
          return {
            afkLevelList: value,
          };
        },
      },
      render: (text, record) => {
        const { afkLevel } = record;
        switch (afkLevel) {
          case 1:
            return (
              <TagSpan $bgColor="#fff2f0" $textColor="#ff4d4f">
                {text}
              </TagSpan>
            );
          case 2:
            return (
              <TagSpan $bgColor="#fbebe2" $textColor="#FF7533">
                {text}
              </TagSpan>
            );
          case 3:
            return (
              <TagSpan $bgColor="#dfe7fd" $textColor="#1E5EFF">
                {text}
              </TagSpan>
            );
          case 4:
            return (
              <TagSpan $bgColor="#ebeced" $textColor="#474a4e">
                {text}
              </TagSpan>
            );
        }
      },
    },
    {
      title: '检测时长',
      dataIndex: 'liveAfkCheckDuration',
      align: 'right',
      tooltip: '用于检测空播挂播的片段时长',
      width: 150,
      hideInSearch: true,
      render: (text, record) => formatSecond(record.liveAfkCheckDuration),
    },
    {
      title: '空播时长',
      dataIndex: 'liveAfkDuration',
      align: 'right',
      tooltip: '画面无变化且讲解内容和直播无关的时长',
      width: 150,
      hideInSearch: true,
      render: (text, record) => formatSecond(record.liveAfkDuration, '未检测'),
      sorter: true,
    },
    {
      title: '空播率',
      dataIndex: 'liveAfkRate',
      align: 'right',
      tooltip: '空播时长/实际直播时长，由于实际直播时长需要次日更新，所以空播率也需要次日更新',
      width: 150,
      hideInSearch: true,
      render: (text, record) => {
        return (
          <span>
            {record.liveAfkRate
              ? (Number(record.liveAfkRate) * 100).toFixed(2) + '%'
              : record.liveAfkDuration
                ? '未判定'
                : '未检测'}
          </span>
        );
      },
    },
    {
      title: '直播时长',
      dataIndex: 'duration',
      align: 'right',
      width: 150,
      hideInSearch: true,
      render: (text, record) => {
        if (record.liveStatus == 0) {
          return '-';
        } else {
          return formatSecond(record.duration);
        }
      },
    },

    {
      title: '观看人次',
      dataIndex: 'viewTime',
      width: 80,
      align: 'right',
      hideInSearch: true,
      render: (text, record) => formatNum(record.viewTime),
    },
    {
      title: '点赞次数',
      dataIndex: 'diggCount',
      width: 80,
      align: 'right',
      hideInSearch: true,
      render: (text, record) => formatNum(record.diggCount),
    },
  ];

  const actionRef = useRef<ActionType>();
  const formRef = useRef<ProFormInstance>();
  const firstAddColumns = useRef(false);
  const filedIdArrRef = useRef<string[]>([]);
  const [columns, setColumns] = useState<ProColumns<LiveQualityItem>[]>(defaultColumns);
  const [hitRuleTimes, setHitRuleTimes] = useState<HitRuleTimes | undefined>(undefined);
  const [radioValue, setRadioValue] = useState<number | null>(null);
  const teamFieldListRef = useGetTeamFieldList(projectId);
  const [columnsStateMap, setColumnsStateMap] = useState<Record<string, ColumnsState>>({
    roomId: {
      show: false,
    },
  });
  const paramsRef = useRef<any>({});
  const { pollingExport, percent, pollingLoading } = usePollingExport();
  const afkState = useLiveAfkFG(projectId);
  const anchorImageState = useAnchorImageFG(projectId);
  const [rangeTime, setRangeTime] = useState<(string | undefined)[]>(getTimeByType('today'));
  const timeFilterRef = useRef<{ value: string } | null>({ value: '' });
  const [showLeftBtn, setShowLeftBtn] = useState(false);
  const scrollContainerRef = useHorizontalDragScroll<HTMLDivElement>();
  const [isEmptyTable, setIsEmptyTable] = useState(false);

  useEffect(() => {
    const afkColumns = ['afkLevel', 'liveAfkCheckDuration', 'liveAfkDuration', 'liveAfkRate'];
    const anchorImageColumns = ['anchorImage'];
    // 这里条件的原因是下面ProTable的postData会修改columns列 要保证排在动态列设置后再修改一次 避免修改失效
    if (firstAddColumns.current) {
      const newColumns = columns.map((item) => {
        if (afkColumns.includes(item.dataIndex as string)) {
          return {
            ...item,
            hidden: !afkState,
            // search 会有一些自定义 transform 配置 所以需要继承下来
            search: afkState ? item.search : false,
          };
        }
        if (anchorImageColumns.includes(item.dataIndex as string)) {
          return {
            ...item,
            hidden: !anchorImageState,
          };
        }
        return item;
      }) as ProColumns<LiveQualityItem>[];
      setColumns(newColumns);
    }
  }, [firstAddColumns.current, afkState, anchorImageState]);

  const onChangeHitRuleTimes = (minuteTime: number) => {
    const hitRuleStartTime = dayjs().subtract(minuteTime, 'minute').format('YYYY-MM-DD HH:mm:ss');
    const hitRuleEndTime = dayjs().format('YYYY-MM-DD HH:mm:ss');
    setRadioValue(minuteTime);
    setHitRuleTimes({ hitRuleEndTime, hitRuleStartTime });
  };

  const radioClick = (value: number) => {
    if (radioValue === value) {
      setRadioValue(null);
      setHitRuleTimes(undefined);
      actionRef.current?.reload();
    }
  };

  // 导出数据量大特殊处理
  const handleExportLiveQuality = async () => {
    const omitPageParams = omit(paramsRef.current, ['pageSize', 'current']);
    const res = await ExportLiveQuality({
      ...omitPageParams,
    });
    if (res.data) {
      pollingExport({ taskId: res.data });
    } else {
      message.error('导出失败,请重试');
    }
  };

  const requestFn = (type: number) =>
    GetDataCardTrend({
      projectId,
      platform,
      startDate: rangeTime[0],
      endDate: rangeTime[1],
      type,
    });

  const { data: liveCountNumData, loading: liveCountNumDataLoading } = useRequest(
    () => requestFn(501),
    {
      refreshDeps: [rangeTime, platform],
    },
  );

  const { data: liveViolationNumData, loading: liveViolationNumDataLoading } = useRequest(
    () => requestFn(502),
    {
      refreshDeps: [rangeTime, platform],
    },
  );

  const { data: liveViolationRateNumData, loading: liveViolationRateNumDataLoading } = useRequest(
    () => requestFn(503),
    {
      refreshDeps: [rangeTime, platform],
    },
  );

  const { data: afkTimeData, loading: afkTimeDataLoading } = useRequest(() => requestFn(504), {
    refreshDeps: [rangeTime, platform],
  });

  const { data: afkRateData, loading: afkRateDataLoading } = useRequest(() => requestFn(505), {
    refreshDeps: [rangeTime, platform],
  });

  const handleScroll = () => {
    if (scrollContainerRef.current) {
      const { scrollLeft } = scrollContainerRef.current;
      setShowLeftBtn(scrollLeft >= 30);
    }
  };

  useEffect(() => {
    const container = scrollContainerRef.current;
    if (container) {
      container.addEventListener('scroll', handleScroll);
    }

    return () => {
      // 清理滚动监听
      if (container) {
        container.removeEventListener('scroll', handleScroll);
      }
    };
  }, []);

  const scroll = (width: number) => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: width, behavior: 'smooth' });
    }
  };

  useEffect(() => {
    if (timeFilterRef.current?.value !== 'today') {
      setRadioValue(null);
      setHitRuleTimes(undefined);
    }
  }, [timeFilterRef.current?.value]);

  useEffect(() => {
    setColumns(defaultColumns);
    firstAddColumns.current = false;
  }, [platform]);

  // 设置默认平台为抖音
  useEffect(() => {
    setPlatform(PlatForm.Douyin);
  }, [setPlatform]);

  return (
    <>
      <div className="rounded-lg bg-white px-4 py-2">
        <Space>
          <TimeFilter
            showToday
            value={rangeTime}
            onChange={(value) => setRangeTime(value)}
            rangeDisable={disabledDate}
            ref={timeFilterRef}
          />
          <PlatformSwitch onlyDouyin />
        </Space>
      </div>
      <Flex justify="center" align="center">
        <AnimatePresence>
          {showLeftBtn && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.5 }}
              style={{
                zIndex: '1000',
              }}
            >
              <Button shape="circle" icon={<LeftOutlined />} onClick={() => scroll(-300)} />
            </motion.div>
          )}
        </AnimatePresence>
        <HideScrollBarRow
          gutter={{ xs: 8, sm: 16, md: 24, lg: 32 }}
          ref={scrollContainerRef}
          className="py-4"
          wrap={false}
        >
          <Col xs={8} sm={8} md={7} lg={7} xl={8} xxl={5} span={5}>
            <DataCard
              title="总直播场次"
              data={liveCountNumData}
              loading={liveCountNumDataLoading}
            />
          </Col>
          <Col xs={8} sm={8} md={7} lg={7} xl={8} xxl={5} span={5}>
            <DataCard
              title="违规直播场次"
              data={liveViolationNumData}
              loading={liveViolationNumDataLoading}
            />
          </Col>
          <Col xs={8} sm={8} md={7} lg={7} xl={8} xxl={5} span={5}>
            <DataCard
              title="总违规率"
              isPercentage
              data={liveViolationRateNumData}
              loading={liveViolationRateNumDataLoading}
            />
          </Col>
          <Col xs={8} sm={8} md={7} lg={7} xl={8} xxl={5} span={5}>
            <DataCard
              title="总空播率"
              isPercentage
              data={afkRateData}
              loading={afkRateDataLoading}
            />
          </Col>
          <Col xs={8} sm={8} md={7} lg={7} xl={8} xxl={5} span={5}>
            <DataCard title="空播时长" isTimeType data={afkTimeData} loading={afkTimeDataLoading} />
          </Col>
        </HideScrollBarRow>
        <Button
          shape="circle"
          icon={<RightOutlined />}
          style={{
            zIndex: '1000',
          }}
          onClick={() => scroll(300)}
        />
      </Flex>
      <ProCard>
        <ProTable<LiveQualityItem>
          columns={columns}
          actionRef={actionRef}
          formRef={formRef}
          params={{
            projectId,
            ...hitRuleTimes,
            liveStartTime: dayjs(rangeTime[0]).format('YYYY-MM-DD 00:00:00'),
            liveEndTime: dayjs(rangeTime[1]).format('YYYY-MM-DD 23:59:59'),
            qualityStatus,
            liveStatus,
            recordStatus,
            platform,
          }}
          tableClassName="custom-table"
          scroll={{ x: isEmptyTable ? getSumColumnsWidth(columns) : 'max-content' }}
          ghost
          sticky={{ offsetHeader: STICKY_OFFSETHEADER }}
          postData={(data: LiveQualityItem[]) => {
            // 判断表格数据是否为空
            data.length === 0 ? setIsEmptyTable(true) : setIsEmptyTable(false);
            if (!firstAddColumns.current && data.length > 0) {
              renderCustomColumns(teamFieldListRef.current).then(
                ({ customColumns, fieldIdArr }) => {
                  filedIdArrRef.current = fieldIdArr;
                  const newColumns = [
                    ...columns,
                    ...customColumns,
                  ] as ProColumns<LiveQualityItem>[];
                  setColumns(newColumns);
                },
              );
              firstAddColumns.current = true;
            }
            // 将teamFields内的字段都抽出来
            const formatData = data.map((item) => {
              const res = { ...item };
              const teamFields = res.teamFieldList;
              if (teamFields) {
                teamFields.forEach((field) => {
                  (res as any)[field.fieldId] = field.value;
                });
              }
              return res;
            });
            return formatData;
          }}
          beforeSearchSubmit={(params) => {
            const fieldList = transferParams(params, filedIdArrRef.current);
            if (fieldList.length > 0) {
              return { ...params, fieldList };
            }
            return params;
          }}
          request={(params, sorter, filter) => {
            paramsRef.current = params;
            return proTableRequestAdapter(params, sorter, filter, LiveQualityPage);
          }}
          search={{ ...proTableSearchConfig }}
          options={{ ...proTableOptionsConfig }}
          pagination={{ ...proTablePaginationConfig, defaultPageSize: 15 }}
          rowKey="roomId"
          dateFormatter="string"
          columnsState={{
            value: columnsStateMap,
            onChange: setColumnsStateMap,
            persistenceKey: 'LiveQuality-Table-Columns',
            persistenceType: 'localStorage',
          }}
          toolBarRender={() => [
            <ExportButton
              exportFn={() => handleExportLiveQuality()}
              loading={pollingLoading}
              percent={percent}
              key="export"
            />,
          ]}
          toolbar={{
            title: (
              <>
                {timeFilterRef?.current?.value === 'today' ? (
                  <Space>
                    <Radio.Group
                      value={radioValue}
                      onChange={(e) => onChangeHitRuleTimes(e.target.value)}
                      buttonStyle="solid"
                    >
                      <Radio.Button value={10} onClick={() => radioClick(10)}>
                        近10分钟
                      </Radio.Button>
                      <Radio.Button value={30} onClick={() => radioClick(30)}>
                        近半小时
                      </Radio.Button>
                      <Radio.Button value={60} onClick={() => radioClick(60)}>
                        近1小时
                      </Radio.Button>
                      <Radio.Button value={60 * 24} onClick={() => radioClick(60 * 24)}>
                        近1天内
                      </Radio.Button>
                    </Radio.Group>
                    <Tooltip title="命中规则时间">
                      <InfoCircleOutlined />
                    </Tooltip>
                  </Space>
                ) : null}
              </>
            ),
          }}
        />
      </ProCard>
    </>
  );
};

export default LiveInspection;
