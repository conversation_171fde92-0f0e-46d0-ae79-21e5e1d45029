import { PageContainer } from '@ant-design/pro-components';
import type { TabsProps } from 'antd';
import { Tabs } from 'antd';

import useTabKeySearchParams from '@/hooks/useTabKeySearchParams';
import useProjectId from '@/hooks/useProjectId';
import EditWordModal from '@/components/editWordModal';
import LiveInspection from './component/basicData';
import QualityVisualData from '@/components/qualityVisual/qualityVisualData';
import BreadCrumbSimple from '@/components/ui/breadcrumbSimple';

const AccountSetting = () => {
  const projectId = useProjectId();
  const [activeKey, onTabChange] = useTabKeySearchParams('basicdata');

  const items: TabsProps['items'] = [
    {
      label: '基础数据',
      key: 'basicdata',
      children: <LiveInspection />,
    },
    {
      label: '质检可视化',
      key: 'visualdata',
      children: <QualityVisualData projectId={projectId} type="live" />,
    },
  ];

  return (
    <PageContainer
      title={<BreadCrumbSimple breadcrumbs={['质检管理', '直播质检']} />}
      header={{
        extra: [<EditWordModal key={'editModal'} projectId={projectId} />],
      }}
    >
      <Tabs activeKey={activeKey} onChange={onTabChange} items={items} className="horizontal-tab" />
    </PageContainer>
  );
};

export default AccountSetting;
