import TimeFilter from '@/components/ui/timeFilter';
import useProjectId from '@/hooks/useProjectId';
import {
  GetTeamOverviewData,
  GetTeamOverviewPage,
  ProjectTeamOverviewAggregationListVO,
} from '@/services/team';
import { proTableRequestAdapter } from '@/utils';
import { BizType, STICKY_OFFSETHEADER } from '@/utils/common';
import { proTableOptionsConfig, proTablePaginationConfig } from '@/utils/proTableConfig';
import { getTimeByType } from '@/utils/time';
import { PageContainer, ProCard, ProColumns, ProTable } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Flex, Space } from 'antd';
import { isNil } from 'lodash-es';
import { useState } from 'react';
type AlignType = 'start' | 'end' | 'left' | 'right' | 'center' | 'justify' | 'match-parent';
const TeamOverview = () => {
  const projectId = useProjectId();

  const [rangeTime, setRangeTime] = useState<(string | undefined)[]>(getTimeByType('day'));

  const { data } = useRequest(() => GetTeamOverviewData({ projectId }));

  const cols = data
    ?.map((item) => {
      if (item?.description && ['3mins回复率', '作品完播率'].includes(item?.description)) {
        return {
          title: item?.description,
          dataIndex: item?.fieldName,
          order: item?.sort,
          hideInTable: item?.showFlag === 0,
          align: 'right' as AlignType,
          width: (item?.description?.length || 10) * 20,
          renderText(text: number) {
            return <span>{isNil(text) ? '-' : `${(text * 100).toFixed(2)}%`}</span>;
          },
        };
      }

      return {
        title: item?.description,
        dataIndex: item?.fieldName,
        order: item?.sort,
        hideInTable: item?.showFlag === 0,
        align: item?.description?.includes('时间') ? 'left' : ('right' as AlignType),

        width: item?.description?.includes('时间') ? 220 : (item.description?.length || 10) * 20,
      };
    })
    .sort((a, b) => a.order! - b.order!);

  const columns: ProColumns<ProjectTeamOverviewAggregationListVO>[] = [
    {
      title: '门店',
      dataIndex: 'id',
      width: 200,
      align: 'left' as AlignType,
      hideInTable: false,
      render(text, record) {
        const value = record?.teamFieldList?.find(
          (item) => item?.bizType == BizType.FINALLYHIERACHY,
        )?.value;
        return value;
      },
    },
    ...(cols || []),
  ];

  return (
    <PageContainer title={'团队总览'}>
      <ProCard>
        <Flex justify="space-between" style={{ marginBottom: '20px' }}>
          <Space>
            <TimeFilter
              value={rangeTime}
              onChange={(value) => {
                setRangeTime(value);
              }}
            />
          </Space>
        </Flex>

        <ProTable
          className="bg-white"
          columns={columns}
          params={{ projectId, startTime: rangeTime[0], endTime: rangeTime[1], platform: 1 }}
          scroll={{ x: 'max-content' }}
          rowKey="id"
          ghost
          sticky={{ offsetHeader: STICKY_OFFSETHEADER }}
          tableClassName="custom-table"
          search={false}
          toolBarRender={false}
          options={proTableOptionsConfig}
          pagination={proTablePaginationConfig}
          request={(params, sorter, filter) => {
            return proTableRequestAdapter(params, sorter, filter, GetTeamOverviewPage);
          }}
        />
      </ProCard>
    </PageContainer>
  );
};

export default TeamOverview;
