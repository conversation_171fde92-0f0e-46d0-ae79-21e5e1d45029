import BreadCrumbSimple from '@/components/ui/breadcrumbSimple';
import useProjectId from '@/hooks/useProjectId';
import { PageContainer } from '@ant-design/pro-components';
import { Button, Tabs, type TabsProps } from 'antd';
import BasicData from './component/basicData';
import VisualData from './component/visualData';

const ClueDocking = () => {
  const projectId = useProjectId();

  const items: TabsProps['items'] = [
    {
      label: '基础数据',
      key: 'basicdata',
      children: <BasicData projectId={projectId} />,
    },
    {
      label: '可视化数据',
      key: 'visualdata',
      children: <VisualData projectId={projectId} />,
    },
  ];

  return (
    <PageContainer
      title={<BreadCrumbSimple breadcrumbs={['线索管理', '线索对接']} />}
      extra={
        <Button
          type="link"
          onClick={() => {
            window.open('https://hj81r4bz4v.feishu.cn/docx/H5RcdsT7roYMV4xqYh3cH57Pndd');
          }}
        >
          配置推送规则教程
        </Button>
      }
    >
      <Tabs defaultActiveKey="1" items={items} className="horizontal-tab" />
    </PageContainer>
  );
};

export default ClueDocking;
