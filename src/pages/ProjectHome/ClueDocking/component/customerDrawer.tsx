import { SvgIcon } from '@/components/SvgIcon';
import { IDynamicsData, QueryCustomerDynamics, QueryLeadsDetail } from '@/services/clue';
import { FullscreenOutlined, UserOutlined } from '@ant-design/icons';
import { Avatar, Drawer, message, Space, Tabs, TabsProps, Timeline } from 'antd';
import copy from 'copy-to-clipboard';
import { useEffect, useState } from 'react';
import styled from 'styled-components';
import CustomerInfoForm, { Title } from './customerInfoForm';
import { useRequest } from '@umijs/max';

const TimelineItem = styled.div`
  padding: 10px 18px;
  border-radius: 8px;
  margin: 0px 5px;
  font-weight: 400;
  letter-spacing: 1px;
  line-height: 25px;
`;

const DrawerHeader = styled.div`
  display: flex;
  padding: 20px;
  border-radius: 8px;
  width: 100%;
  .customer-name {
    max-width: 250px;
    overflow: hidden;
    font-size: 14px;
    font-family: OPPOSans;
    line-height: 22px;
    white-space: nowrap;
    text-align: left;
    text-overflow: ellipsis;
  }
  .copy-icon {
    margin-left: 4px;
    color: #1e5eff;
    font-size: 9px;
  }
  .customer-id {
    max-width: 250px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
`;
enum ActionTypeMap {
  '其他',
  '短视频',
  '直播',
  '企业主页',
  '私信',
  '订阅文章',
  '群聊',
  '独立店铺',
  '搜索服务卡',
}
enum ClueTypeMap {
  '字节-表单提交',
  '字节-在线咨询',
  '字节-智能电话',
  '字节-网页回呼',
  '字节-卡券',
  '字节-抽奖',
}

const CustomInfoDrawer = ({
  isDrawOpen,
  setDrawOpen,
  projectId,
  title,
  clueId,
}: {
  isDrawOpen: boolean;
  setDrawOpen: (value: boolean) => void;
  projectId?: string;
  title: string;
  clueId?: string;
}) => {
  const [width, setWidth] = useState<string>('448px');
  const [customerDynamics, setCustomerDynamics] = useState<IDynamicsData[]>([]);
  const handleDrawerWidth = () => {
    width === '448px' ? setWidth('100vw') : setWidth('448px');
  };

  const onClose = () => {
    setDrawOpen(false);
  };

  const { data: customerInfo, run: queryCustomInfo } = useRequest(
    () => QueryLeadsDetail({ projectId, clueId }),
    {
      refreshDeps: [projectId, clueId],
      manual: true,
    },
  );

  useEffect(() => {
    if (isDrawOpen) {
      queryCustomInfo();
    }
  }, [isDrawOpen]);

  useEffect(() => {
    const getCustomerDynamics = async () => {
      if (!projectId || !customerInfo?.clueId) return;
      const res = await QueryCustomerDynamics({
        projectId,
        clueId: customerInfo?.clueId,
      });
      if (res.code === 0) {
        setCustomerDynamics(res.data || []);
      }
    };
    getCustomerDynamics();
  }, [projectId, customerInfo]);

  const dynamicItems =
    customerDynamics && customerDynamics.length
      ? customerDynamics.map((item) => ({
          children: (
            <TimelineItem style={{ backgroundColor: '#1e5eff05' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <span style={{ color: '#989898' }}>抖音留资</span>
                <span style={{ color: '#989898' }}>{item.clueCreateTime}</span>
              </div>
              <div style={{ lineHeight: '25px' }}>
                在抖音账号[{item.referDyName}]通过[{ActionTypeMap[item.actionType]}]中的[
                {ClueTypeMap[item.clueType]}]留资
              </div>
              <span style={{ color: '#696666' }}>联系方式:{item.telephone}</span>
            </TimelineItem>
          ),
        }))
      : [];

  const items: TabsProps['items'] = [
    {
      label: '客户概况',
      key: 'detail',
      children: customerInfo && <CustomerInfoForm customerInfo={customerInfo} />,
    },
    {
      label: '客户动态',
      key: 'dynamics',
      children: (
        <div style={{ margin: '10px 0' }}>
          <Title style={{ color: '#151515', borderColor: '#1e5eff' }}>客户动态</Title>
          <Timeline style={{ paddingTop: '10px' }} items={[...dynamicItems]} />
        </div>
      ),
    },
  ];
  return (
    <Drawer
      placement="right"
      onClose={onClose}
      title={title}
      open={isDrawOpen}
      width={width}
      destroyOnHidden
      extra={
        <Space>
          <FullscreenOutlined onClick={handleDrawerWidth} />
        </Space>
      }
    >
      <DrawerHeader style={{ backgroundColor: '#1e5eff05' }}>
        <Avatar style={{ backgroundColor: '#1677ff' }} size={42} icon={<UserOutlined />} />
        <div style={{ marginLeft: '12px' }}>
          <div
            onClick={() => {
              copy(customerInfo?.name || '');
              message.success('复制成功');
            }}
          >
            <span className="customer-name">{customerInfo?.name}</span>
            <SvgIcon x={4} icon="local:outline/copy-filled" />
          </div>
          <div className="customer-id" style={{ color: '#696666' }}>
            {customerInfo?.clueId}
          </div>
        </div>
      </DrawerHeader>
      <Tabs defaultActiveKey="1" items={items} />
    </Drawer>
  );
};

export default CustomInfoDrawer;
