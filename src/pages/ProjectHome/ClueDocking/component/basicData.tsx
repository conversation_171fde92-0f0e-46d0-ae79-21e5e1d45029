import ExportButton from '@/components/exportButton';
import { usePollingExport } from '@/hooks/usePollingExport';
import {
  ActionTypeEnum,
  ChannelSourceEnum,
  ClueInfo,
  ClueTypeEnum,
  ExportLeads,
  QueryLeadsPage,
} from '@/services/clue';
import { AccountPlatformEnum } from '@/utils/platform';
import { proTableRequestAdapter } from '@/utils';
import { STICKY_OFFSETHEADER } from '@/utils/common';
import {
  proTableOptionsConfig,
  proTablePaginationConfig,
  proTableSearchConfig,
} from '@/utils/proTableConfig';
import { getSumColumnsWidth } from '@/utils/table';
import type { ActionType, ProColumns, ProFormInstance } from '@ant-design/pro-components';
import { ProCard, ProTable } from '@ant-design/pro-components';
import { Cascader, DatePickerProps, Flex, message, Space } from 'antd';
import dayjs from 'dayjs';
import duration from 'dayjs/plugin/duration';
import { omit } from 'lodash-es';
import { useRef, useState } from 'react';
import CustomInfoDrawer from './customerDrawer';
dayjs.extend(duration);

const maxDays = 31;

// 禁用不超过一个月
const disabledDate: DatePickerProps['disabledDate'] = (current, { from }) => {
  if (from) {
    // 检查选择的日期范围是否超过 **maxDays** 天
    const diffInDays = Math.abs(current.diff(from, 'days'));
    if (diffInDays >= maxDays) {
      return true;
    }
  }

  const today = dayjs().startOf('day');
  return current && current >= today;
};

const BasicData = (props: { projectId?: string }) => {
  const { projectId } = props;
  const [clueId, setClueId] = useState<string>();
  const [isDrawOpen, setDrawOpen] = useState(false);
  const actionRef = useRef<ActionType>();
  const formRef = useRef<ProFormInstance>();
  const paramsRef = useRef<any>({});
  const { pollingExport, pollingLoading, percent } = usePollingExport();
  const [isEmptyTable, setIsEmptyTable] = useState(false);

  const handleExport = async () => {
    const omitPageParams = omit(paramsRef.current, ['pageSize', 'current']);
    const res = await ExportLeads({
      ...omitPageParams,
    });
    if (res.data) {
      pollingExport({ taskId: res.data });
    } else {
      message.error('导出失败,请重试');
    }
  };

  const columns: ProColumns<ClueInfo>[] = [
    {
      dataIndex: 'clueId',
      hideInSearch: true,
      hideInTable: true,
    },
    {
      title: '姓名',
      dataIndex: 'name',
      align: 'left',
      formItemProps: {
        label: null,
      },
      fieldProps: {
        placeholder: '请输入客户名称',
      },
    },
    {
      title: '来源经销商',
      dataIndex: 'sourceCommerce',
      align: 'left',
      formItemProps: {
        label: null,
      },
      width: '120px',
      hideInSearch: true,
    },
    {
      title: '留资平台',
      dataIndex: 'platform',
      width: 90,
      valueType: 'select',
      align: 'left',
      valueEnum: AccountPlatformEnum,
      fieldProps: {
        placeholder: '留资平台',
      },
      formItemProps: {
        label: null,
      },
    },
    {
      title: '首次线索渠道',
      dataIndex: 'clueSource',
      align: 'left',
      search: {
        transform: (values: [string, number][]) => ({
          clueSource: values.map((value) => value.slice(-1)[0]).toString(),
        }),
      },
      fieldProps: {
        showCheckedStrategy: Cascader.SHOW_CHILD,
        expandTrigger: 'hover',
        placeholder: '首次线索渠道',
        options: [
          {
            text: '抖音',
            value: 'douyin',
            children: [
              { text: '字节-橙子建站', value: ChannelSourceEnum['字节-橙子建站'] },
              { text: '其他渠道-外部导入', value: ChannelSourceEnum['其他渠道-外部导入'] },
              { text: '字节-抖音企业号', value: ChannelSourceEnum['抖音企业号'] },
              { text: '字节-巨量线索', value: ChannelSourceEnum['巨量线索'] },
              { text: '字节-云店', value: ChannelSourceEnum['字节-云店'] },
              { text: '字节-星图', value: ChannelSourceEnum['字节-星图'] },
              { text: '字节-获客宝', value: ChannelSourceEnum['字节-获客宝'] },
              { text: '字节-住小帮', value: ChannelSourceEnum['字节-住小帮'] },
            ],
          },
        ],
        fieldNames: {
          children: 'children',
          label: 'text',
        },
        maxTagTextLength: 5,
        maxTagCount: 2,
        showSearch: true,
        multiple: true,
      },
      valueType: 'cascader',
      formItemProps: {
        label: null,
      },
    },
    {
      title: '首次互动类型',
      dataIndex: 'actionType',
      valueType: 'select',
      width: '120px',
      align: 'left',
      valueEnum: {
        [ActionTypeEnum.其他]: { text: '其他' },
        [ActionTypeEnum.短视频]: { text: '短视频' },
        [ActionTypeEnum.直播]: { text: '直播' },
        [ActionTypeEnum.企业主页]: { text: '企业主页' },
        [ActionTypeEnum.私信]: { text: '私信' },
        [ActionTypeEnum.订阅文章]: { text: '订阅文章' },
        [ActionTypeEnum.群聊]: { text: '群聊' },
        [ActionTypeEnum.独立店铺]: { text: '独立店铺' },
        [ActionTypeEnum.搜索服务卡]: { text: '搜索服务卡' },
      },
      search: {
        transform: (value) => ({
          actionType: value,
        }),
      },
      fieldProps: {
        placeholder: '首次互动类型',
      },
      formItemProps: {
        label: null,
      },
    },
    {
      title: '首次线索类型',
      dataIndex: 'clueType',
      valueType: 'cascader',
      width: 120,
      align: 'left',
      fieldProps: {
        placeholder: '首次线索类型',
        showCheckedStrategy: Cascader.SHOW_CHILD,
        expandTrigger: 'hover',
        options: [
          {
            text: '抖音',
            value: 'douyin',
            children: [
              {
                value: ClueTypeEnum.表单提交,
                text: '字节-表单提交',
              },
              {
                value: ClueTypeEnum.在线咨询,
                text: '字节-在线咨询',
              },
              {
                value: ClueTypeEnum.智能电话,
                text: '字节-智能电话',
              },
              {
                value: ClueTypeEnum.网页回呼,
                text: '字节-网页回呼',
              },
              {
                value: ClueTypeEnum.卡券,
                text: '字节-卡券',
              },
              {
                value: ClueTypeEnum.抽奖,
                text: '字节-抽奖',
              },
            ],
          },
        ],
        fieldNames: {
          children: 'children',
          label: 'text',
        },
        maxTagTextLength: 5,
        maxTagCount: 2,
        showSearch: true,
        multiple: true,
      },

      search: {
        transform: (values: [string, number][]) => ({
          clueType: values.map((value) => value.slice(-1)[0]).toString(),
        }),
      },
      formItemProps: {
        label: null,
      },
    },
    {
      title: '电话',
      dataIndex: 'telephone',
      align: 'left',
      width: 100,
      hideInSearch: true,
    },
    {
      title: '微信',
      width: 80,
      dataIndex: 'weixin',
      align: 'left',
      hideInSearch: true,
    },
    {
      title: '手动填写地域',
      dataIndex: 'address',
      width: 120,
      align: 'left',
      hideInSearch: true,
      render: (_, record) => (
        <Flex vertical={true} align="flex-start">
          <span>
            {record.provinceName} - {record.cityName}
          </span>
        </Flex>
      ),
    },
    {
      valueType: 'dateRange',
      hideInTable: true,
      fieldProps: {
        placeholder: ['留资时间'],
        disabledDate: disabledDate,
      },
      formItemProps: {
        label: null,
      },
      initialValue: [dayjs().subtract(1, 'day').startOf('day'), dayjs()],
      search: {
        transform: (value) => ({
          startDate: value[0],
          endDate: value[1],
        }),
      },
    },
    {
      title: '留资时间',
      dataIndex: 'createTime',
      align: 'left',
      fixed: 'right',
      hideInSearch: true,
    },
    {
      title: '操作',
      dataIndex: 'options',
      align: 'left',
      hideInSearch: true,
      hideInTable: false,
      fixed: 'right',
      width: '100px',
      render: (text, record) => {
        return (
          <Space>
            <a
              onClick={() => {
                setClueId(record.clueId);
                setDrawOpen(true);
              }}
            >
              详情
            </a>
          </Space>
        );
      },
    },
  ];

  return (
    <ProCard>
      <ProTable<ClueInfo>
        columns={columns}
        actionRef={actionRef}
        formRef={formRef}
        ghost
        sticky={{ offsetHeader: STICKY_OFFSETHEADER }}
        params={{ projectId }}
        tableClassName="custom-table"
        scroll={{ x: isEmptyTable ? getSumColumnsWidth(columns) : 'max-content' }}
        postData={(data: ClueInfo[]) => {
          // 判断表格数据是否为空
          data.length === 0 ? setIsEmptyTable(true) : setIsEmptyTable(false);
          return data;
        }}
        request={async (params, sorter, filter) => {
          paramsRef.current = params;
          return proTableRequestAdapter(params, sorter, filter, QueryLeadsPage);
        }}
        beforeSearchSubmit={(params) => {
          params?.clueSource?.split(',').includes('1')
            ? (params.clueSource = params.clueSource.concat(',', '0'))
            : params.clueSource;
          return {
            ...params,
          };
        }}
        search={{ ...proTableSearchConfig }}
        options={{ ...proTableOptionsConfig }}
        pagination={{ ...proTablePaginationConfig }}
        toolBarRender={() => [
          <ExportButton
            exportFn={handleExport}
            loading={pollingLoading}
            percent={percent}
            key="export"
          />,
        ]}
        onRow={(record) => {
          return {
            onClick: () => {
              if (isDrawOpen) {
                setClueId(record.clueId);
              }
            },
          };
        }}
        rowKey="clueId"
        dateFormatter="string"
        columnsState={{
          persistenceKey: 'ClueDocking-Table-Columns',
          persistenceType: 'localStorage',
        }}
      />
      <CustomInfoDrawer
        title="客户详情"
        isDrawOpen={isDrawOpen}
        setDrawOpen={setDrawOpen}
        projectId={projectId}
        clueId={clueId}
      />
    </ProCard>
  );
};
export default BasicData;
