import { LeadsDouyinDetail } from '@/services/clue';
import { AccountPlatformEnum } from '@/utils/platform';
import { ProForm, ProFormSelect, ProFormText } from '@ant-design/pro-components';
import { Flex } from 'antd';
import { useEffect, useState } from 'react';
import styled from 'styled-components';

export const Title = styled.div`
  width: 100%;
  height: 14px;
  line-height: 14px;
  padding-left: 6px;
  font-weight: 700;
  margin-bottom: 12px;
  margin-top: 5vh;
  border-left: 2px solid;
  &:first-child {
    margin-top: 6px;
  }
`;
const CustomerProForm = styled(ProForm)`
  .ant-form-item {
    margin-bottom: 12px;
    .ant-form-item-control-input {
      min-height: 18px;
    }
    .ant-form-item-label {
      padding: 0;
      > label {
        color: rgba(105, 102, 102, 1);
        font-size: 12px;
      }
    }
  }
`;
const CustomerInfoForm = ({ customerInfo }: { customerInfo: LeadsDouyinDetail }) => {
  const [formKey, setFormKey] = useState(0);

  useEffect(() => {
    setFormKey((prevKey) => prevKey + 1); // 每次更新
  }, [customerInfo]);
  return (
    <CustomerProForm
      key={formKey}
      initialValues={customerInfo}
      layout={'vertical'}
      className="clue-form"
      grid={true}
      rowProps={{
        gutter: [0, 0],
      }}
      submitter={false}
    >
      <Title style={{ color: '#151515', borderColor: '#1e5eff' }}>账户信息</Title>
      <ProFormText
        labelCol={{ style: { color: 'red !important' } }}
        colProps={{ md: 12, xl: 12 }}
        name="clueId"
        label="客户编码"
        readonly
      />
      <ProFormText colProps={{ md: 12, xl: 12 }} name="referDyName" label="来源账号" readonly />
      <ProFormText colProps={{ md: 12, xl: 12 }} name="name" label="客户信息" readonly />
      <ProFormSelect
        colProps={{ md: 12, xl: 12 }}
        name="platform"
        label="留资平台"
        valueEnum={AccountPlatformEnum}
        readonly
      />
      <Title style={{ color: '#151515', borderColor: '#1e5eff' }}>用户信息</Title>
      <ProFormText colProps={{ md: 12, xl: 12 }} name="name" label="姓名" readonly />
      <ProFormText colProps={{ md: 12, xl: 12 }} name="telephone" label="手机" readonly />
      <ProFormText colProps={{ md: 12, xl: 12 }} name="weixin" label="微信" readonly />
      <ProFormText colProps={{ md: 12, xl: 12 }} name="age" label="年龄" readonly />
      <Title style={{ color: '#151515', borderColor: '#1e5eff' }}>线索情况</Title>
      <ProFormSelect
        colProps={{ md: 12, xl: 12 }}
        name="clueSource"
        label="首次线索渠道"
        valueEnum={{
          0: { text: '字节-橙子建站' },
          1: { text: '字节-橙子建站' },
          2: { text: '其他渠道-外部导入' },
          5: { text: '字节-抖音企业号' },
          7: { text: '字节-巨量线索' },
          8: { text: '字节-云店' },
          9: { text: '字节-星图' },
          10: { text: '字节-获客宝' },
          11: { text: '字节-住小帮' },
          20: { text: '其他' },
          21: { text: '外部导入' },
          22: { text: '广告创意' },
          23: { text: '蹊径落地页' },
          24: { text: '一叶智能' },
          25: { text: '微信原生推广页' },
          26: { text: '讯达小程序' },
          27: { text: '互动落地页' },
          28: { text: '教培通' },
          29: { text: '优居' },
          30: { text: '亦芯' },
          31: { text: '艾推' },
          32: { text: 'CMS落地页' },
        }}
        readonly
      />
      <ProFormSelect
        colProps={{ md: 12, xl: 12 }}
        name="actionType"
        label="首次互动类型"
        valueEnum={{
          0: { text: '其他' },
          1: { text: '短视频' },
          2: { text: '直播' },
          3: { text: '企业主页' },
          4: { text: '私信' },
          5: { text: '订阅文章' },
          6: { text: '群聊' },
          7: { text: '独立店铺' },
          8: { text: '搜索服务卡' },
          20: { text: '其他' },
          21: { text: '外部导入' },
          22: { text: '广告创意' },
          23: { text: '蹊径落地页' },
          24: { text: '一叶智能' },
          25: { text: '微信原生推广页' },
          26: { text: '讯达小程序' },
          27: { text: '互动落地页' },
          28: { text: '教培通' },
          29: { text: '优居' },
          30: { text: '亦芯' },
          31: { text: '艾推' },
          32: { text: 'CMS落地页' },
        }}
        readonly
      />
      <ProFormSelect
        colProps={{ md: 12, xl: 12 }}
        name="clueType"
        label="首次线索类型"
        valueEnum={{
          0: { text: '字节-表单提交' },
          1: { text: '字节-在线咨询' },
          2: { text: '字节-智能电话' },
          3: { text: '字节-网页回呼' },
          4: { text: '字节-卡券' },
          5: { text: '字节-抽奖' },
          20: {
            text: '表单预约',
          },
          21: {
            text: '在线咨询',
          },
          22: {
            text: '普通电话',
          },
          23: {
            text: '智能电话',
          },
          24: {
            text: '发券',
          },
          25: {
            text: '智能咨询',
          },
          26: {
            text: '抽奖',
          },
          27: {
            text: '落地⻚点击',
          },
          28: {
            text: '⼀键授权',
          },
          29: {
            text: '加企业微信',
          },
          30: {
            text: '公众号关注',
          },
          31: {
            text: '个⼈微信加授权',
          },
        }}
        readonly
      />
      <ProFormText colProps={{ md: 12, xl: 12 }} label="手动填写地域">
        <Flex>
          <ProFormText name="provinceName" noStyle colProps={{ md: 4, xl: 4 }} readonly />
          <ProFormText name="cityName" noStyle colProps={{ md: 4, xl: 4 }} readonly />
        </Flex>
      </ProFormText>

      <ProFormText colProps={{ md: 12, xl: 12 }} name="createTime" label="留资时间" readonly />
    </CustomerProForm>
  );
};

export default CustomerInfoForm;
