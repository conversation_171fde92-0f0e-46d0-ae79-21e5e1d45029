import { ITopSource, QueryTopSource } from '@/services/clue';
import {
  CustomDateRadioButton,
  CustomDateRadioGroup,
  echartDefaultColor,
} from '@/utils/commonStyle';
import { disabledDate, getTimes } from '@/utils/time';
import { ProCard } from '@ant-design/pro-components';
import { DatePicker, Flex, RadioChangeEvent } from 'antd';
import { RangePickerProps } from 'antd/es/date-picker';
import dayjs from 'dayjs';
import ReactECharts from 'echarts-for-react';
import React, { useEffect, useMemo, useState } from 'react';

const { RangePicker } = DatePicker;

interface VisualDataProps {
  projectId?: string;
}

const VisualData = (props: VisualDataProps) => {
  const { projectId } = props;

  const [activeKey, setActiveKey] = useState<React.Key>(1);
  const [[startTime, endTime], setRangeTime] = useState<string[]>(getTimes(1));
  const [topTenData, setTopTenData] = useState<ITopSource[]>([]);
  const percentageData = useMemo(() => {
    const total = topTenData.reduce((sum, item) => sum + item.userCount, 0);
    return topTenData.map((item) => ((item.userCount / total) * 100).toFixed(2));
  }, [topTenData]);

  useEffect(() => {
    if (startTime && endTime) {
      const getTopTenData = async () => {
        const { data } = await QueryTopSource({
          projectId,
          startDate: startTime,
          endDate: endTime,
        });
        setTopTenData(data ? data : []);
      };
      getTopTenData();
    }
  }, [projectId, startTime, endTime]);

  const options = {
    color: echartDefaultColor,
    xAxis: {
      // max: 'dataMax',
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
        label: {
          precision: 0, // 省略小数点
        },
      },
      formatter: function (params: any) {
        const dataIndex = params[0].dataIndex;
        const value = params[0].value;
        const percentage = percentageData[dataIndex];
        return `线索数: ${value}<br>占比:(${percentage}%)`;
      },
    },
    yAxis: {
      type: 'category',
      data: topTenData.map((item) => item.referDyName),
      inverse: true,

      axisLabel: {
        color: '#989898',
        lineHeight: 20,
        width: 120, // 设置标签宽度为 120 像素
        overflow: 'break', // 设置为 break 自动换行
        rich: {
          multiline: {
            align: 'left',
          },
        },
        formatter: function (value: any) {
          return `{multiline|${value}}`;
        },
      },
    },
    series: [
      {
        name: '线索数',
        type: 'bar',
        data: topTenData.map((item, index) => ({
          value: item.userCount,
          percentage: 50,
          itemStyle: {
            color: `rgba(30, 94, 255, ${1 - 0.1 * index})`,
            borderType: 'dashed',
          },
        })),
        label: {
          show: true,
          position: 'right',
        },
      },
    ],
    legend: {
      show: false,
    },
  };
  const onChangeRange = (date: RangePickerProps['value'], dateString: string[]) => {
    setRangeTime(dateString);
    setActiveKey('0');
  };

  const onChangeRangeBtn = (e: RadioChangeEvent) => {
    const value = e.target.value;
    setActiveKey(value as string);
    const range = getTimes(Number(value));
    setRangeTime(range);
  };

  return (
    <ProCard bodyStyle={{ padding: 0, height: '70vh' }}>
      <Flex justify="space-between" style={{ padding: '16px 24px 0 24px' }}>
        <h3>数据概况</h3>

        <div>
          <CustomDateRadioGroup value={activeKey} buttonStyle="solid" onChange={onChangeRangeBtn}>
            <CustomDateRadioButton value={1}>昨天</CustomDateRadioButton>
            <CustomDateRadioButton value={6}>近7天</CustomDateRadioButton>
            <CustomDateRadioButton value={14}>近15天</CustomDateRadioButton>
          </CustomDateRadioGroup>

          <RangePicker
            disabledDate={disabledDate}
            onChange={onChangeRange}
            style={{ marginLeft: '10px' }}
            allowClear={false}
            defaultValue={[dayjs(startTime), dayjs(endTime)]}
            value={[startTime ? dayjs(startTime) : null, endTime ? dayjs(endTime) : null]}
          />
        </div>
      </Flex>
      <h4 style={{ marginLeft: '24px' }}>来源账号TOP10</h4>

      <ReactECharts
        option={options}
        notMerge={true}
        style={{ width: '100%', height: '95%', marginTop: '-30px' }}
      />
    </ProCard>
  );
};

export default VisualData;
