:global(.ant-drawer .ant-drawer-body) {
  padding: 0 5px !important;
}
:global(.ant-drawer .ant-drawer-close) {
  position: absolute;
  right: -2px !important;
}
.add_keyword_modal {
  :global {
    .ant-collapse,
    .ant-collapse > .ant-collapse-item:last-child,
    .ant-collapse .ant-collapse-item:last-child > .ant-collapse-content,
    .ant-collapse > .ant-collapse-item:last-child > .ant-collapse-header {
      border-radius: 0;
      border: none;
    }

    .ant-collapse > .ant-collapse-item:last-child > .ant-collapse-header {
      border-top: 1px solid #f1f1f1;
    }
    .ant-collapse .ant-collapse-item:last-child > .ant-collapse-content {
      border-bottom: 1px solid #f1f1f1;
    }
    .ant-select-multiple .ant-select-selection-overflow-item {
      align-self: flex-start;
    }
    .ant-select-multiple .ant-select-selection-overflow {
      justify-content: flex-start;
      align-items: flex-start;
    }
    .ant-select:not(.ant-select-customize-input) .ant-select-selector input {
      height: 28px !important;
    }
    .ant-select .ant-select-arrow {
      top: 15%;
    }
    .ant-select-multiple .ant-select-selector {
      border-radius: 2px;
      overflow-y: auto;
      overflow-x: hidden;
      padding: 2px;
    }
    .ant-select-multiple .ant-select-selection-placeholder {
      top: 110%;
      white-space: pre-wrap;
    }
  }
}
