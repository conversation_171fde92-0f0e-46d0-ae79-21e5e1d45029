import { BatchAddObjectTags } from '@/services/tag';
import { CommonTagGroup } from '@/services/typings';
import { useRequest } from 'ahooks';
import { Button, Empty, Input, message, Modal, Tag } from 'antd';
import { PropsWithChildren, useState } from 'react';

export const TagSelectModal = (
  props: PropsWithChildren<{
    userIds: (string | number | undefined)[];
    tagGroups: CommonTagGroup[];
    onKeywordGroupsChange?: () => void;
    type?: number;
  }>,
) => {
  const { userIds, tagGroups, onKeywordGroupsChange, type } = props;

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [searchKey, setSearchKey] = useState('');

  const tagGroupFilter = searchKey
    ? tagGroups.filter((tagGroup) => tagGroup.tags.some((tag) => tag.name.includes(searchKey)))
    : tagGroups;

  const [selectedTagIds, setSelectedTagIds] = useState<number[]>([]);

  const { run: batchAddObjectTags } = useRequest(BatchAddObjectTags, {
    manual: true,
    onSuccess: (data) => {
      if (data.code === 0) {
        message.success('添加成功');
        onKeywordGroupsChange?.();
      } else {
        message.error(data.msg);
      }
    },
  });

  const showModal = () => {
    if (userIds.length > 0) {
      setIsModalOpen(true);
    } else {
      message.error('请选择至少一项');
    }
  };

  const handleOk = () => {
    setIsModalOpen(false);
    batchAddObjectTags({
      type: type || 2,
      objectIds: userIds.filter((item) => !!item).map((item) => String(item)),
      tagIds: selectedTagIds,
    });
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };

  const handleChange = (tag: number, checked: boolean) => {
    const nextSelectedTags = checked
      ? [...selectedTagIds, tag]
      : selectedTagIds.filter((t) => t !== tag);

    setSelectedTagIds(nextSelectedTags);
  };

  return (
    <>
      <div onClick={showModal}>{props.children || <Button>批量添加标签</Button>}</div>
      <Modal
        title={userIds.length > 1 ? '批量添加标签' : '添加标签'}
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        destroyOnHidden
        afterClose={() => setSelectedTagIds([])}
      >
        <Input
          placeholder="输入关键词检索标签和标签组"
          onChange={(e) => setSearchKey(e.target.value)}
        />

        <div style={{ marginTop: 10, maxHeight: 220, overflow: 'auto' }}>
          {tagGroupFilter.length ? (
            tagGroupFilter.map((group) => {
              return (
                <div key={group.id} style={{ marginBottom: 15 }}>
                  <span>{group.name}</span>
                  <div>
                    {group.tags.map((tag) => (
                      <Tag.CheckableTag
                        key={tag.id}
                        style={{ border: '1px solid #DBDBDB' }}
                        checked={selectedTagIds.includes(tag.id)}
                        onChange={(checked) => handleChange(tag.id, checked)}
                      >
                        {tag.name}
                      </Tag.CheckableTag>
                    ))}
                  </div>
                </div>
              );
            })
          ) : (
            <Empty />
          )}
        </div>
      </Modal>
    </>
  );
};
