import { Chat<PERSON><PERSON><PERSON>VO } from '@/services/customer';
import { UserOutlined } from '@ant-design/icons';
import { Avatar, Empty } from 'antd';
import classNames from 'classnames';
import dayjs from 'dayjs';
import styled from 'styled-components';

const ChatBox = styled.div`
  display: flex;
  flex-direction: column;
  height: calc(100% - 58px);
  width: 100%;
  padding: 20px;
  overflow: auto;
  &::-webkit-scrollbar {
    width: 4px;
    height: 4px;
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    width: 1px;
    height: 2px;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 10px;
  }

  &::-webkit-scrollbar-thumb:hover {
    height: 2px;
    border-radius: 10px;
  }
`;
const DateBox = styled.div`
  width: 100%;
  display: flex;
  padding: 20px;
  justify-content: center;
  font-size: 12px;
`;
const MessageItemBox = styled.div<{ $selecting: boolean }>`
  display: flex;
  flex-direction: ${(props) => (props?.$selecting ? 'row-reverse' : 'row')};
  gap: 15px;
  margin-bottom: 10px;
  .message {
    display: flex;
    flex-direction: column;
    .message-time {
      font-size: 12px;
      line-height: 20px;
      text-align: ${(props) => (props?.$selecting ? 'right' : 'left')};
    }
    .message-content {
      max-width: 30vw;
      padding: 8px;
      font-size: 12px;
      font-family: OPPOSans;
      line-height: 25px;
      border-radius: 4px;
    }
  }
`;

const MessageList = ({
  messageList,
  customerId,
}: {
  messageList: ChatHistoryVO[];
  customerId: string;
}) => {
  return (
    <ChatBox>
      {messageList?.length ? (
        <div>
          {messageList?.map((message, index) => {
            // 检查当前消息与前一个消息的时间是否同天
            const isSameDay =
              index > 0 && dayjs(message?.sendAt).isSame(messageList?.[index - 1]?.sendAt, 'day');
            return (
              <div key={message?.id}>
                {!isSameDay && (
                  <DateBox> {dayjs(message?.sendAt).format('YYYY年MM月DD日')}</DateBox>
                )}

                <MessageItemBox key={message?.id} $selecting={message?.sender !== customerId}>
                  <Avatar size={36} gap={2} src={message?.senderInfo?.avatar || <UserOutlined />} />

                  {customerId && (
                    <div className="message">
                      <div
                        className={classNames('message-content text-[#696666]', {
                          'bg-[#1E5EFF14]': message?.sender !== customerId,
                          'bg-[#FAFAFA]': message?.sender === customerId,
                        })}
                      >
                        {message?.content?.message_type &&
                        ['text', 'other'].includes(message?.content?.message_type) ? (
                          // 文字
                          <span>{message?.content?.text}</span>
                        ) : message?.content?.resource_url ? (
                          // TODO 图片
                          <img
                            src={message?.content?.resource_url}
                            style={{ maxWidth: 300, maxHeight: 200 }}
                          />
                        ) : (
                          // TODO 其他
                          '[其他消息]'
                        )}
                      </div>
                      <div className="message-time text-[#989898]">
                        {dayjs(message?.sendAt).format('HH:mm:ss')}
                      </div>
                    </div>
                  )}
                </MessageItemBox>
              </div>
            );
          })}
        </div>
      ) : (
        <Empty />
      )}
    </ChatBox>
  );
};
export default MessageList;
