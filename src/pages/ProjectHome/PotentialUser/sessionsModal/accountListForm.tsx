import { SimpleTeamFieldItem } from '@/services/typings';
import FieldSelect from '@/components/fieldSelect';
import { SearchOutlined } from '@ant-design/icons';
import { Button, Flex, Form, Input } from 'antd';
import { useEffect, useState } from 'react';
import { FormFieldItem } from '.';

export const AccountListForm = ({
  fieldList,
  defaultNickname,
  onValuesChange,
  collapseStateChange,
}: {
  fieldList: FormFieldItem[];
  defaultNickname?: string;
  onValuesChange: (values: { nickname?: string; fieldList: SimpleTeamFieldItem[] }) => void;
  collapseStateChange?: (s: boolean, expandedItemNum: number) => void;
}) => {
  const [collapse, setCollapse] = useState<boolean>(true); // 折叠
  const [form] = Form.useForm();
  const initialValues: Record<number, any> = {};
  fieldList.forEach((item) => {
    if (item.showFlag) {
      initialValues[item?.fieldId] = item?.value;
    }
  });
  useEffect(() => {
    const province = fieldList?.find((item) => item?.name === '经销商编码');
    if (province) {
      form.setFieldsValue({
        [province.fieldId]: province?.value,
      });
      onValuesChange?.({ fieldList: [province], nickname: defaultNickname });
    }
  }, [defaultNickname]);

  useEffect(() => {
    const expandedItemNum = collapse ? 0 : fieldList.filter((item) => item?.showFlag).length;
    collapseStateChange?.(collapse, expandedItemNum);
  }, [collapse]);

  return (
    <div>
      <Form
        form={form}
        onValuesChange={(_: any, allValues: Record<string, string> & { nickname?: string }) => {
          const nickname = allValues?.nickname;
          if (allValues?.nickname) {
            delete allValues?.nickname;
          }
          onValuesChange({
            nickname,
            fieldList: Object.keys(allValues)
              .filter((key) => !!allValues?.[key])
              .map((key) => {
                return {
                  fieldId: parseInt(key),
                  value: allValues[key],
                };
              }),
          });
        }}
        component={false}
      >
        <Form.Item name="nickname" style={{ marginTop: 8, marginBottom: 0 }}>
          <Flex>
            <Input
              placeholder="请输入账号名称、用户昵称"
              defaultValue={defaultNickname}
              style={{ height: 30 }}
              prefix={<SearchOutlined />}
            />
            <Button type="link" onClick={() => setCollapse(!collapse)}>
              {collapse ? '展开' : '收起'}
            </Button>
          </Flex>
        </Form.Item>
        <Form.Item noStyle>
          {fieldList.map((item) => {
            if (!item?.showFlag) {
              return null;
            }

            return (
              <Flex
                align="center"
                key={item?.fieldId}
                style={{ width: '100%', display: collapse ? 'none' : 'block' }}
              >
                <Form.Item
                  name={item?.fieldId}
                  style={{ width: 195, height: 30, marginTop: 8, marginBottom: 0 }}
                >
                  <FieldSelect fieldId={item?.fieldId} name={item?.name} />
                </Form.Item>
              </Flex>
            );
          })}
        </Form.Item>
      </Form>
    </div>
  );
};
