import { Collapse } from 'antd';
import styled from 'styled-components';
export const ConversationWrapper = styled.div`
  max-height: 500px;
  overflow: auto;
  &::-webkit-scrollbar {
    /* display: none; */
    width: 1px;
    height: 1px;
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    width: 1px;
    height: 2px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 10px;
  }
`;
export const SessionModalPanel = styled.div`
  display: flex;
  // 72 banner 56 header 24 margin 32 padding
  height: calc(100vh - 72px - 58px - 24px - 32px);
  margin: auto;
  width: 100%;
  border-radius: 6px;
`;
export const AccountList = styled.div<{ $collapseNum: number }>`
  width: 286px;
  height: 100%;
  box-sizing: border-box;
  flex-shrink: 0;
  .header {
    box-sizing: border-box;
    width: 100%;
    height: 58px;
    padding: 0 16px;
    font-weight: 600;
    font-size: 16px;
    line-height: 58px;
  }
  .list {
    width: 100%;
    height: calc(100% - 58px);
    padding: 0 14px 0 16px;
    font-size: 12px;
    line-height: 44px;
    user-select: none;
    .account-scroll {
      height: calc(100% - 70px - ${(props) => (props.$collapseNum || 0) * 38}px);
      padding-top: 10px;
      overflow-y: auto;
      /* 滚动条样式 */
      &::-webkit-scrollbar {
        width: 4px;
        height: 4px;
        background: transparent;
      }

      &::-webkit-scrollbar-thumb {
        width: 1px;
        height: 2px;
        background: rgba(0, 0, 0, 0.1);
        border-radius: 10px;
      }

      &::-webkit-scrollbar-thumb:hover {
        height: 2px;
        border-radius: 10px;
      }
      /* 滚动条样式 */
    }

    .agent-info {
      display: flex;
      align-items: center;
      width: 95%;
      margin-bottom: 5px;
      font-weight: 600;
      .nick-name {
        display: inline-block;
        width: 180px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .avatar {
        width: 24px;
        height: 24px;
        margin-right: 8px;
      }
      .num {
        float: right;
      }
    }

    .customer-info {
      display: flex;
      align-items: center;
      justify-content: space-around;
      width: 98%;
      height: 55px;
      font-size: 10px;
      border-radius: 4px;

      span {
        display: block;
        user-select: none;
      }

      .avatar {
        flex-shrink: 0;
        width: 34px;
        height: 34px;
      }
      .msg {
        width: 150px;
        height: 32px;
        margin-left: 7px;
        .name {
          width: 110px;
          overflow: hidden;
          font-size: 12px;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        .latest-msg {
          width: 120px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
      .date {
        display: flex;
        align-items: flex-start;
        width: 48px;
        height: 32px;
      }
    }
  }
`;

export const CustomCollapse = styled(Collapse)`
  &.ant-collapse-borderless > .ant-collapse-item {
    /* margin-top: 10px; */
  }
  &.ant-collapse > .ant-collapse-item > .ant-collapse-header {
    padding: 0 !important;
  }
  &.ant-collapse .ant-collapse-content > .ant-collapse-content-box {
    padding: 0;
  }
`;
