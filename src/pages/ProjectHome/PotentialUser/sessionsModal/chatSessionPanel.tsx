import {
  AgentVO,
  ChatHistoryVO,
  ChatSearchQO,
  ConversationVO,
  getChatHistoryRequest,
} from '@/services/customer';
import { GetTagGroups, TagsType } from '@/services/tag';
import {
  LoadingOutlined,
  PlusCircleOutlined,
  SearchOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';
import { useRequest } from 'ahooks';
import { Avatar, Button, DatePicker, Flex } from 'antd';
import Search from 'antd/es/input/Search';
import { useEffect, useReducer, useState } from 'react';
import styled, { createGlobalStyle } from 'styled-components';
import { TagSelectModal } from '../tagModal';
import MessageList from './messageList';

const { RangePicker } = DatePicker;
const HeaderLeft = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  .customer-name {
    max-width: 8vw;
    margin-left: 10px;
    overflow: hidden;
    font-size: 14px;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
`;
const ChatSessionPanelStyle = createGlobalStyle`
  .ant-input-group-addon {
    display: none !important;
  }
  .ant-input-affix-wrapper {
    border-start-end-radius: 6px !important;
    border-end-end-radius: 6px !important;
  }
`;

const ChatSessionPanel = ({
  projectId,
  selectingCustomer,
  selectingAgent,
}: {
  projectId: string;
  selectingCustomer: ConversationVO;
  selectingAgent: AgentVO;
}) => {
  const [messageList, setMessageList] = useState<ChatHistoryVO[]>([]);
  const [loading, setLoading] = useState(false);
  const { data: tagRes } = useRequest(() => GetTagGroups(TagsType.customTag, { projectId }));

  const [params, dispatch] = useReducer(
    (
      state: ChatSearchQO,
      action: {
        payload: any;
      },
    ) => ({
      ...state,
      ...action.payload,
    }),
    {
      projectId,
      interactiveType: 3,
      sender: selectingCustomer?.sender,
      accountUserId: selectingAgent?.accountUserId,
      begDate: '',
      endDate: '',
      content: '',
      size: 5000,
      page: 0,
    },
  );

  useEffect(() => {
    dispatch({
      payload: {
        projectId,
        sender: selectingCustomer?.sender,
        accountUserId: selectingAgent?.accountUserId,
      },
    });
  }, [selectingAgent?.accountUserId, selectingCustomer, projectId]);

  useEffect(() => {
    const getMessageList = async () => {
      setLoading(true);
      const res = await getChatHistoryRequest(params);

      if (res.code === 0) {
        setLoading(false);
        setMessageList(res?.data?.items || []);
      }
    };
    getMessageList();
  }, [params]);

  return (
    <ProCard
      bodyStyle={{ padding: '24px 0px' }}
      headStyle={{ height: '58px' }}
      title={
        <HeaderLeft>
          <Avatar
            style={{ backgroundColor: '#1677ff', border: 'none' }}
            size={36}
            gap={2}
            src={selectingCustomer?.avatar || <UserOutlined />}
          />
          <div className="customer-name">{selectingCustomer?.name}</div>
        </HeaderLeft>
      }
      headerBordered
      extra={
        <Flex gap={10}>
          <Search
            placeholder="搜索聊天内容"
            addonAfter={null}
            prefix={<SearchOutlined />}
            onSearch={(value) => {
              dispatch({ payload: { content: value } });
            }}
            onBlur={(e) => dispatch({ payload: { content: e.target.value } })}
            enterButton
            style={{ maxWidth: '12vw' }}
          />
          <RangePicker
            style={{ maxWidth: '15vw' }}
            onChange={(values, formatString: [string, string]) => {
              dispatch({
                payload: {
                  begDate: formatString[0] ? `${formatString[0]} 00:00:00` : '',
                  endDate: formatString[1] ? `${formatString[1]} 23:59:59` : '',
                },
              });
            }}
            placeholder={['统计时间', '']}
          />
          {selectingCustomer?.sender && tagRes?.data && (
            <TagSelectModal userIds={[selectingCustomer?.sender]} tagGroups={tagRes?.data}>
              <Button>
                <PlusCircleOutlined />
                添加标签
              </Button>
            </TagSelectModal>
          )}
        </Flex>
      }
    >
      {loading ? (
        <Flex align="center" justify="center">
          <LoadingOutlined />
        </Flex>
      ) : (
        !!selectingCustomer?.sender && (
          <MessageList messageList={messageList} customerId={selectingCustomer?.sender} />
        )
      )}

      <ChatSessionPanelStyle />
    </ProCard>
  );
};
export default ChatSessionPanel;
