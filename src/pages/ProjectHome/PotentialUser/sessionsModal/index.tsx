import { AgentVO, ConversationVO, getAgentList } from '@/services/customer';
import { ArrowLeftOutlined, DownOutlined, LoadingOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import { history, useParams } from '@umijs/max';
import { useDebounce, useRequest } from 'ahooks';
import { Avatar, CollapseProps, Divider, Empty, Flex, Spin } from 'antd';
import { useCallback, useEffect, useReducer, useState } from 'react';

import useLocationWithState from '@/hooks/useLocationWithState';
import { SimpleTeamFieldItem } from '@/services/typings';
import InfiniteScroll from 'react-infinite-scroll-component';
import { AccountListForm } from './accountListForm';
import ChatSessionPanel from './chatSessionPanel';
import { ConversationBox } from './conversationBox';
import { AccountList, CustomCollapse, SessionModalPanel } from './styles';
interface SessionModalState {
  projectId: string;
  accountUserId: string;
  selectingConversation: ConversationVO;
  selectingAgent: AgentVO;
}
export type FormFieldItem = SimpleTeamFieldItem & { name: string; showFlag: number };
const SessionsModal = () => {
  const [searchFormParams, setSearchFormParams] = useState<{
    nickname?: string;
    fieldList: SimpleTeamFieldItem[];
  }>();
  const [expandAccountUserIds, setExpandAccountUserIds] = useState<string[]>([]);
  const [expandedFormItemNum, setExpandedItemNum] = useState(0);

  const [accountListAcc, setAccountListAcc] = useState<AgentVO[]>([]);
  const [page, setPage] = useState(1);

  const debouncedSearchParams = useDebounce(searchFormParams, { wait: 700 });
  const routeParams = useParams();
  const location = useLocationWithState<{ teamFieldList: FormFieldItem[] }>();
  const [state, dispatch] = useReducer(
    (
      state: SessionModalState,
      action: {
        payload: any;
      },
    ) => ({
      ...state,
      ...action.payload,
    }),
    {
      projectId: '',
      accountUserId: '',
      selectingConversationId: '',
      selectingConversation: '',
      selectingAgent: '',
      name: '',
    },
  );

  useEffect(() => {
    const { projectId, accountUserId, platformUniqueId, name } = routeParams;
    if (projectId && accountUserId && platformUniqueId) {
      // const params = decodeURIComponent(routeParams.uid).split(PARAMS_SEPARATOR);
      const decodeAccountUserId = decodeURIComponent(accountUserId);
      const decodeName = decodeURIComponent(name || '');
      const decodeSelectingConversationId = decodeURIComponent(platformUniqueId);
      dispatch({
        payload: {
          projectId: parseInt(projectId),
          accountUserId: decodeAccountUserId,
          selectingConversationId: decodeSelectingConversationId,
          selectingAgent: { accountUserId: decodeAccountUserId },
          name: decodeName || null,
        },
      });
      setExpandAccountUserIds([decodeAccountUserId]);
    }
  }, []);

  const { data: accountListRes, loading } = useRequest(
    () => {
      if (state.projectId && debouncedSearchParams) {
        return getAgentList(
          { size: 15, page },
          {
            projectId: state?.projectId,
            ...debouncedSearchParams,
          },
        );
      }
      return Promise.reject();
    },
    {
      refreshDeps: [state?.projectId, debouncedSearchParams, page],
      onSuccess(data) {
        if (page === 1) {
          setAccountListAcc(data?.data?.items || []);
        } else {
          setAccountListAcc((prev) => [...prev, ...(data?.data?.items || [])]);
        }
      },
    },
  );

  const agentList = accountListRes?.data?.items;

  const panelStyle: React.CSSProperties = {
    marginBottom: 7,
    background: '',
    borderRadius: 0,
    border: 'none',
  };
  const handleConversationSelect = useCallback(
    (customer: ConversationVO, agent: AgentVO) => {
      dispatch({
        payload: {
          selectingAgent: agent,
          selectingConversation: customer,
          selectingConversationId: customer?.sender,
        },
      });
    },
    [dispatch],
  );

  const items: CollapseProps['items'] = accountListAcc?.map((agent) => {
    return {
      label: (
        <div className="agent-info">
          <Avatar className="avatar" src={agent?.avatar} />
          <span className="nick-name" title={agent?.nickname}>
            {agent?.nickname}
          </span>
          {/* <span className="num">{10 || agent.accountUsers?.length}</span> */}
        </div>
      ),
      key: agent?.accountUserId,
      style: panelStyle,
      children: (
        <ConversationBox
          agent={agent}
          name={searchFormParams?.nickname}
          expandAccountUserIds={expandAccountUserIds}
          selectingConversationId={state?.selectingConversationId}
          handleConversationSelect={handleConversationSelect}
        />
      ),
    };
  });

  const backToPotentialUserListTab3 = () => {
    const routePath = `/project/${routeParams?.projectKey}/clue/potential-user?activeTab=3`;
    history.push(routePath);
  };

  const handleFormValueChange = useCallback(
    (val: { nickname?: string | undefined; fieldList: SimpleTeamFieldItem[] }) => {
      setSearchFormParams(val);
      setPage(1);
    },
    [],
  );

  const handleCollapseStateChange = useCallback((_: boolean, num: number) => {
    setExpandedItemNum(num);
  }, []);

  return (
    <>
      {
        <PageContainer
          title={
            <span>
              <ArrowLeftOutlined style={{ marginRight: 5 }} onClick={backToPotentialUserListTab3} />
              高潜用户/对话流
            </span>
          }
        >
          <SessionModalPanel style={{ background: '#fff' }}>
            <AccountList $collapseNum={expandedFormItemNum} style={{ border: '1px solid #f1f1f1' }}>
              <div className="header border-b border-[#f1f1f1]">企业账号列表</div>
              <div className="list">
                {location.state?.teamFieldList && (
                  <Spin spinning={loading}>
                    <AccountListForm
                      fieldList={location.state?.teamFieldList}
                      defaultNickname={routeParams?.name}
                      onValuesChange={handleFormValueChange}
                      collapseStateChange={handleCollapseStateChange}
                    />
                  </Spin>
                )}
                <Divider style={{ marginTop: 10, marginBottom: 0 }} />
                <div className="account-scroll" id="account-scroll-div">
                  <InfiniteScroll
                    dataLength={accountListAcc?.length}
                    style={{ overflow: 'hidden' }}
                    next={() => setPage((prev) => prev + 1)}
                    hasMore={accountListAcc?.length < parseInt(accountListRes?.data?.total || '0')}
                    loader={
                      <Flex justify="center">
                        <LoadingOutlined />
                      </Flex>
                    }
                    endMessage={
                      accountListAcc.length === parseInt(accountListRes?.data?.total || '0') ? (
                        <Divider plain style={{ color: '#d1d2d3' }}>
                          已全部展示
                        </Divider>
                      ) : null
                    }
                    scrollableTarget="account-scroll-div"
                  >
                    {state.accountUserId && agentList?.length ? (
                      <CustomCollapse
                        bordered={false}
                        defaultActiveKey={[state?.accountUserId]}
                        expandIcon={({ isActive }) => (
                          <DownOutlined style={{ marginTop: 5 }} rotate={isActive ? 0 : -90} />
                        )}
                        onChange={(expandAccountUserIds: string | string[]) =>
                          setExpandAccountUserIds(expandAccountUserIds as string[])
                        }
                        style={{ background: '#fff' }}
                        items={items}
                      />
                    ) : (
                      <Empty />
                    )}
                  </InfiniteScroll>
                </div>
              </div>
            </AccountList>
            {state?.selectingConversation?.sender && state?.selectingAgent && state?.projectId ? (
              <ChatSessionPanel
                projectId={state?.projectId}
                selectingCustomer={state?.selectingConversation}
                selectingAgent={state?.selectingAgent}
              />
            ) : (
              <Flex style={{ width: '100%' }} vertical={true}>
                <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description="请选择账号" />
              </Flex>
            )}
          </SessionModalPanel>
        </PageContainer>
      }
    </>
  );
};

export default SessionsModal;
