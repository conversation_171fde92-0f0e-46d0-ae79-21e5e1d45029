import { AgentVO, ConversationVO, getConversationList } from '@/services/customer';
import { LoadingOutlined } from '@ant-design/icons';
import { usePrevious, useRequest } from 'ahooks';
import { Avatar, Empty, Flex } from 'antd';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import InfiniteScroll from 'react-infinite-scroll-component';
import { ConversationWrapper } from './styles';
import classNames from 'classnames';

export const ConversationBox = (props: {
  agent: AgentVO;
  selectingConversationId?: string;
  handleConversationSelect: (conversation: ConversationVO, agent: AgentVO) => void;
  expandAccountUserIds: string[];
  name?: string;
}) => {
  const { agent, selectingConversationId, handleConversationSelect, expandAccountUserIds, name } =
    props;
  const previousExpandAccountUserIds = usePrevious(expandAccountUserIds);
  const [page, setPage] = useState(1);
  const [conversationListAcc, setConversationListAcc] = useState<ConversationVO[]>([]);

  const {
    data: conversationRes,
    loading: fetchingConversation,
    mutate,
  } = useRequest(
    () => {
      if (
        agent?.accountUserId &&
        expandAccountUserIds?.includes(agent?.accountUserId) &&
        !previousExpandAccountUserIds?.includes(agent?.accountUserId)
      ) {
        return getConversationList({
          size: 15,
          page: page,
          accountUserId: agent?.accountUserId,
          orderBy: 'latestSendAt',
          name,
        });
      }
      return Promise.reject();
    },
    {
      refreshDeps: [expandAccountUserIds, previousExpandAccountUserIds, page],
      onSuccess(data) {
        if (page === 1) {
          setConversationListAcc(data?.data?.items || []);
        } else if (data.data?.items?.length) {
          setConversationListAcc((prev) => [...prev, ...(data?.data?.items || [])]);
        }
      },
      onError() {
        // @ts-ignore
        mutate({ data: { items: [] } });
      },
    },
  );

  useEffect(() => {
    const target = conversationListAcc?.find((item) => item?.sender === selectingConversationId);
    if (target) {
      handleConversationSelect(target, agent);
    }
  }, [selectingConversationId, conversationListAcc]);

  const conversationList = conversationRes?.data?.items;

  const scrollBoxId = `conversation-scroll-div-${agent?.accountUserId}`;
  return conversationListAcc && conversationListAcc?.length > 0 ? (
    <ConversationWrapper id={scrollBoxId}>
      <InfiniteScroll
        dataLength={conversationListAcc?.length}
        style={{ overflow: 'hidden' }}
        next={() => {
          console.log(page + 1);
          setPage((prev) => prev + 1);
        }}
        hasMore={
          !!(
            conversationListAcc?.length < parseInt(conversationRes?.data?.total || '0') &&
            conversationList?.length
          )
        }
        loader={
          <Flex justify="center">
            <LoadingOutlined />
          </Flex>
        }
        endMessage={null}
        scrollableTarget={scrollBoxId}
      >
        {conversationListAcc?.map((conversation) => {
          const date = dayjs(conversation?.latestSendAt);
          const dateFormat = conversation?.latestSendAt ? date.format('MM月DD日') : '未知时间';
          return (
            <div
              onClick={() => handleConversationSelect?.(conversation, agent)}
              key={conversation?.sender}
              className={classNames('customer-info text-[#696666]', {
                'bg-[#1e5eff14]': selectingConversationId === conversation?.sender,
              })}
            >
              <Avatar className="avatar" src={conversation?.avatar} />
              <div className="msg">
                <span className="name text-[#000]" title={conversation?.name}>
                  {conversation?.name}
                </span>
                <span className="latest-msg">[{conversation?.lateMessage || '...'}]</span>
              </div>
              <div className="date">
                <span>{dateFormat}</span>
              </div>
            </div>
          );
        })}
      </InfiniteScroll>
    </ConversationWrapper>
  ) : (
    <div style={{ textAlign: 'center' }}>
      {fetchingConversation ? <LoadingOutlined /> : <Empty />}
    </div>
  );
};
