import BreadCrumbSimple from '@/components/ui/breadcrumbSimple';
import useProjectId from '@/hooks/useProjectId';
import { getKeywordList } from '@/services/keyword';
import { PageContainer } from '@ant-design/pro-components';
import { useLocation } from '@umijs/max';
import { useRequest } from 'ahooks';
import { Tabs } from 'antd';
import { useState } from 'react';
import { AddKeyWordModal } from './addKeyWordModal';
import { UserList } from './userList';

const PotentialUser = () => {
  const location = useLocation();
  const defaultActiveTabMatchRes = location.search.match(/\?activeTab=(\d)$/);
  const defaultActiveTab = defaultActiveTabMatchRes?.[1] || '1';

  const [activeTab, setActiveTab] = useState(defaultActiveTab);
  const projectId = useProjectId();

  const { data: keyWordListRes, refresh: refreshKeywordGroup } = useRequest(() =>
    getKeywordList({ projectId: projectId }),
  );

  const onChange = (activeKey: string) => {
    setActiveTab(activeKey);
  };
  const items = [
    {
      key: '1',
      label: '作品评论互动',
    },
    {
      key: '2',
      label: '直播弹幕互动',
    },
    {
      key: '3',
      label: '私信互动',
    },
  ];

  return (
    <PageContainer
      title={<BreadCrumbSimple breadcrumbs={['线索管理', '高潜用户']} />}
      extra={
        <AddKeyWordModal
          keywordGroups={keyWordListRes?.data?.tags}
          onKeywordGroupsChange={refreshKeywordGroup}
        />
      }
    >
      {projectId && (
        <>
          <Tabs
            defaultActiveKey={defaultActiveTab}
            items={items}
            onChange={onChange}
            className="horizontal-tab"
          />

          <UserList
            activeTab={activeTab}
            projectId={projectId}
            keywordGroups={keyWordListRes?.data?.tags || []}
          />
        </>
      )}
    </PageContainer>
  );
};

export default PotentialUser;
