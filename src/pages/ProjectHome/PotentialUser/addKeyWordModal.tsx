import { SvgIcon } from '@/components/SvgIcon';
import useProjectId from '@/hooks/useProjectId';
import { addBulletKeyword, Tags } from '@/services/keyword';
import { PlusCircleOutlined, QuestionCircleFilled } from '@ant-design/icons';
import { DrawerForm } from '@ant-design/pro-components';
import { useRequest } from 'ahooks';
import { Button, Collapse, Form, Input, message, Select, Tooltip } from 'antd';
import styles from './index.module.less';

const DEFAULT_KEYWORD = [
  {
    label: '很喜欢',
    value: '很喜欢',
  },
  {
    label: '感兴趣',
    value: '感兴趣',
  },
  {
    label: '非常棒',
    value: '非常棒',
  },

  {
    label: '好极了',
    value: '好极了',
  },
];
const INIT_KEYWORD_GROUP = {
  name: '高意向用户',
  words: [],
};
export const AddKeyWordModal = ({
  keywordGroups,
  onKeywordGroupsChange,
}: {
  keywordGroups?: Tags[];
  onKeywordGroupsChange: () => void;
}) => {
  const [form] = Form.useForm<{ name: string; company: string }>();
  const projectId = useProjectId();

  const { run: addBulletKeywordRequest } = useRequest(addBulletKeyword, {
    manual: true,
    onSuccess() {
      message.success('添加成功');
      onKeywordGroupsChange();
    },
  });

  return (
    <DrawerForm<{
      tags: Tags[];
    }>
      className={styles.add_keyword_modal}
      title={
        <span>
          新建关键词&nbsp;
          <Tooltip title="修改关键词组和关键词后，只对未来的数据产生影响，历史数据不再重新标记">
            <QuestionCircleFilled />
          </Tooltip>
        </span>
      }
      width={448}
      form={form}
      trigger={<Button type="link">弹幕关键词</Button>}
      autoFocusFirstInput
      drawerProps={{
        destroyOnHidden: true,
        mask: false,
      }}
      submitTimeout={2000}
      onFinish={async (values) => {
        if (projectId) {
          addBulletKeywordRequest({
            projectId,
            tags: values.tags,
          });
        }
        return true;
      }}
      layout="inline"
      initialValues={{ tags: [...(keywordGroups || [INIT_KEYWORD_GROUP])] }}
    >
      <Form.List name="tags">
        {(fields, { add, remove }) => {
          return (
            <div style={{ width: '100%' }}>
              {fields.map(({ key, name }, i) => (
                <Collapse
                  size="small"
                  key={key}
                  collapsible="icon"
                  defaultActiveKey={`${key}_item`}
                  style={{ background: '#fff' }}
                  expandIconPosition="end"
                  items={[
                    {
                      key: `${key}_item`,
                      extra: i !== 0 && (
                        <SvgIcon
                          icon="local:outline/delete-outline"
                          y={4}
                          onClick={() => remove(name)}
                        />
                      ),
                      label: (
                        <Form.Item
                          initialValue={`关键词组${i}`}
                          name={[name, 'name']}
                          rules={[{ required: true, message: '关键词组不能为空' }]}
                          noStyle
                        >
                          <Input
                            size="small"
                            disabled={i === 0}
                            variant="borderless"
                            maxLength={20}
                          />
                        </Form.Item>
                      ),
                      children: (
                        <Form.Item
                          initialValue={['很喜欢']}
                          name={[name, 'words']}
                          label="关键词"
                          rules={[
                            {
                              type: 'array',
                              defaultField: {
                                validator(_, value) {
                                  if (!value || value.length === 0) {
                                    return Promise.reject('*关键词不可为空');
                                  }
                                  const AllChars = value
                                    .trim()
                                    .split('')
                                    .filter((char: string) => /[\u4e00-\u9fa5\w]/.test(char));

                                  return AllChars.length >= 2
                                    ? Promise.resolve()
                                    : Promise.reject('*每个词需要大于等于2个字 不包括标点符号');
                                },
                              },
                            },
                            {
                              required: true,
                              message: '关键词不可为空',
                            },
                          ]}
                        >
                          <Select
                            mode="tags"
                            style={{ maxWidth: '246px', height: '96px' }}
                            placeholder="请输入关键词名称，多个关键词按回车添加/自动按英文逗号分隔"
                            options={DEFAULT_KEYWORD}
                            tokenSeparators={[',']}
                            autoFocus
                          />
                        </Form.Item>
                      ),
                    },
                  ]}
                />
              ))}
              <div
                style={{ marginLeft: 15, marginTop: 14, color: '#2160F9' }}
                onClick={() => add()}
              >
                {<PlusCircleOutlined />}&nbsp;添加关键词组
              </div>
            </div>
          );
        }}
      </Form.List>
    </DrawerForm>
  );
};
