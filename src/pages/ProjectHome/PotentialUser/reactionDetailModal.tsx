import { SvgIcon } from '@/components/SvgIcon';
import { PlatForm } from '@/utils/platform';
import { Chat<PERSON><PERSON>oryVO, Customer, getChatHistoryRequest } from '@/services/customer';
import { CommonTagGroup } from '@/services/typings';
import { FullscreenOutlined, SearchOutlined } from '@ant-design/icons';
import { useInViewport, usePrevious, useRequest } from 'ahooks';
import {
  Avatar,
  Button,
  Drawer,
  Empty,
  Flex,
  Input,
  message,
  Popover,
  Space,
  Timeline,
} from 'antd';
import copy from 'copy-to-clipboard';
import { useEffect, useRef, useState } from 'react';
import { styled } from 'styled-components';
import { TagSelectModal } from './tagModal';

export const PANEL_LABELS: Record<
  string,
  {
    work: string;
    comment: string;
    detailLabel: string;
    interactiveNum: string;
    commentsNum: string;
    keywordsNum: string;
    firstCommentTime: string;
    latestCommentTime: string;
  }
> = {
  1: {
    work: '作品',
    comment: '评论',
    detailLabel: '作品评论互动',
    interactiveNum: '互动作品数',
    commentsNum: '评论数',
    keywordsNum: '关键词评论数',
    firstCommentTime: '首次评论时间',
    latestCommentTime: '最新评论时间',
  },
  2: {
    work: '直播',
    comment: '弹幕',
    detailLabel: '直播弹幕互动',
    interactiveNum: '互动直播场次',
    commentsNum: '弹幕数',
    keywordsNum: '关键词弹幕数',
    firstCommentTime: '首次弹幕时间',
    latestCommentTime: '最新弹幕时间',
  },
  3: {
    work: '直播',
    comment: '弹幕',
    detailLabel: '私信',
    interactiveNum: '接收消息数',
    commentsNum: '回复消息数',
    keywordsNum: '3min回复率',
    firstCommentTime: '首次评论时间',
    latestCommentTime: '最新评论时间',
  },
};

const DrawerContentWrapper = styled.div`
  padding: 10px;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 57px - 48px);
  .ant-input-group-addon {
    display: none;
  }
  .ant-input-affix-wrapper {
    border-start-end-radius: 6px !important;
    border-end-end-radius: 6px !important;
  }
  .reaction-label {
    height: 24px;
    padding-left: 4px;
    font-weight: 500;
    font-size: 14px;
    line-height: 24px;
  }
  .reaction-label::before {
    display: block;
    float: left;
    width: 2px;
    height: 16px;
    transform: translate(-4px, 3px);
    content: '';
  }
`;
const TimelineItem = styled.div`
  padding-top: 3px;
  border-radius: 8px;
  margin: 10px 0;
  font-size: 12px;

  .content {
    padding: 10px;
    background-color: rgba(30, 94, 255, 0.02);

    .comment,
    .title,
    .link {
      display: block;
      overflow: hidden;
      vertical-align: baseline;
    }
    .comment {
      color: #696666;
      font-family: ABeeZee;
    }
    .title {
      font-size: 14px;
    }

    .tag {
      display: inline-block;
      height: 16px;
      margin-right: 5px;
      padding: 0 2px;
      overflow: hidden;
      font-size: 10px;
      font-family: Inter;
      line-height: 16px;
      text-align: center;
      text-overflow: ellipsis;
      border: 1px solid #000;
      border-radius: 3px;
      transform: translateY(4px);
    }
  }

  .title {
    color: #000;
    font-weight: 600;
  }
`;
const DrawerHeader = styled.div`
  padding: 12px;
  border-radius: 8px;
  background-color: #1e5eff05;
  width: 100%;
  display: flex;
  .user-info {
    display: flex;
    flex-shrink: 0;
    align-content: space-between;
    width: 40%;
    white-space: nowrap;
    transform: translateX(-5px);
    .name-tag {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      width: calc(100% - 40px);
      height: 100%;
    }
    .name {
      max-width: 100%;
      margin-top: 4px;
      overflow: hidden;
      color: #000;
      font-weight: 400;
      font-size: 14px;
      font-family: OPPOSans;
      text-overflow: ellipsis;
    }
    .avatar {
      display: block;
      flex-shrink: 0;
      width: 44px;
      height: 44px;
      margin-right: 11px;
    }
    .tags {
      display: flex;
    }
    .tag {
      display: block;
      max-width: 40%;
      height: 18px;
      margin-right: 2px;
      padding: 0 4px;
      overflow: hidden;
      font-weight: 400;
      font-size: 12px;
      font-family: Open Sans;
      line-height: 18px;
      text-align: center;
      text-overflow: ellipsis;
      border: 1px solid #000;
      border-radius: 3px;
      &.tag-color {
        color: #fda633;
        border-color: #fda633;
      }
      &.keyword-color {
        color: #1e5eff;
        border-color: #1e5eff;
      }
    }
  }
  .user-reaction-data {
    display: flex;
    justify-content: space-between;
    width: 65%;
    div {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }
    span {
      display: block;
      text-align: center;
    }
    .num {
      margin-bottom: 3px;
      font-weight: 600;
      font-size: 20px;
    }
    .num-label {
      color: #989898;
      font-size: 12px;
    }
  }
`;
const PLATFORM_LABEL: Record<number, string> = {
  [PlatForm.Douyin]: '抖音',
  [PlatForm.KuaiShou]: '快手',
  [PlatForm.DongCheDi]: '懂车帝',
  [PlatForm.WXVideo]: '视频号',
  '-1': '未知平台',
};
export const ReactionDetailModal = (props: {
  isDrawOpen: boolean;
  setDrawOpen: (value: boolean) => void;
  activeTab: number;
  projectId: number;
  reactionLabel: string;
  tags: CommonTagGroup[];
  title: string;
  user: Customer;
  onClose?: () => void;
  onKeywordGroupsChange?: () => void;
}) => {
  const {
    isDrawOpen,
    setDrawOpen,
    title,
    onClose: onDrawerClose,
    user,
    projectId,
    activeTab,
    reactionLabel,
    tags,
  } = props;

  const [width, setWidth] = useState<string>('448px');
  const [searchKey, setSearchKey] = useState('');
  const previousSearchKey = usePrevious(searchKey);
  const [page, setPage] = useState(0);
  const [chatList, setChatList] = useState<ChatHistoryVO[]>([]);
  const bottomDOM = useRef<HTMLDivElement | null>(null);
  const loadAllData = useRef(false);
  const [inViewport] = useInViewport(bottomDOM, { threshold: 0.8 });

  const { data: chatHistoryRes } = useRequest(
    () => {
      if (user.platformUniqueId && isDrawOpen) {
        return getChatHistoryRequest({
          page: page,
          size: 15,
          projectId,
          interactiveType: activeTab,
          content: searchKey,
          sender: user?.platformUniqueId,
        });
      }
      return Promise.reject();
    },
    { refreshDeps: [activeTab, searchKey, user?.platformUniqueId, page, isDrawOpen] },
  );

  useEffect(() => {
    setChatList((prev) => {
      const total = chatHistoryRes?.data?.total || '0';
      const finfish =
        (chatHistoryRes?.data?.items?.length || 0) + prev.length >= parseInt(total) ||
        chatHistoryRes?.data?.items?.length === 0;

      if (finfish && bottomDOM.current) {
        bottomDOM.current.innerHTML = '没有更多数据';
        loadAllData.current = true;
      }
      if (searchKey !== previousSearchKey && page === 1) {
        return chatHistoryRes?.data?.items || [];
      }
      return [...prev, ...(chatHistoryRes?.data?.items || [])];
    });
  }, [chatHistoryRes?.data?.items, chatHistoryRes?.data?.items?.length]);

  useEffect(() => {
    if (inViewport && !loadAllData.current) {
      setPage((prev) => prev + 1);
    }
  }, [inViewport]);

  const handleDrawerWidth = () => {
    width === '448px' ? setWidth('100vw') : setWidth('448px');
  };

  const onClose = () => {
    setDrawOpen(false);
    onDrawerClose?.();
  };

  const timelineItems =
    chatList && chatList.length > 0
      ? chatList?.map((item) => {
          const hitKeyword = item.tags && item.tags?.length > 0;
          const work = PANEL_LABELS[activeTab]['work'];
          const comment = PANEL_LABELS[activeTab]['comment'];
          return {
            color: hitKeyword ? 'red' : '#1E5EFF',
            children: (
              <TimelineItem className="color-[#989898]">
                <span className="time">{item.sendAt}</span>

                <div className="content">
                  <span className="title">
                    给{PLATFORM_LABEL[item.platform || -1]}账号&nbsp;[
                    {item?.receiverInfo?.name || item?.receiver}]&nbsp;的{work}：
                    <Popover
                      content={
                        item?.linkInfo?.cover ? (
                          <img src={item?.linkInfo?.cover} style={{ height: 250 }} />
                        ) : null
                      }
                    >
                      <a target="_blank" href={item?.linkInfo?.url} rel="noreferrer">
                        [{(item?.linkInfo?.title || item?.linkInfo?.url)?.slice(0, 22)}...]
                      </a>
                    </Popover>
                  </span>

                  <span className="comment">
                    {`发送${comment}: ${item?.content} `}

                    {item?.tags?.map((keyword) => (
                      <span
                        className="tag"
                        style={{ color: '#5AC262', borderColor: '#5AC262' }}
                        key={keyword?.name}
                      >
                        {keyword?.name}
                      </span>
                    ))}
                  </span>
                </div>
              </TimelineItem>
            ),
          };
        })
      : [];

  const handleSearch = (value: string) => {
    setSearchKey(value);
    setPage(1);
  };
  const handleCopyId = (value: string) => {
    copy(value);
    message.success('复制成功');
  };
  return (
    <>
      {user && (
        <Drawer
          maskClosable={true}
          mask={false}
          placement="right"
          onClose={onClose}
          title={title}
          open={isDrawOpen}
          width={width}
          destroyOnHidden
          extra={
            <Space className="mr-3">
              <FullscreenOutlined onClick={handleDrawerWidth} />
            </Space>
          }
        >
          <DrawerContentWrapper>
            <DrawerHeader>
              <div className="user-info">
                <Avatar className="avatar" src={user.avatar} />
                <div className="name-tag">
                  <div
                    onClick={() => handleCopyId(String(user.id))}
                    className="name"
                    title={user.name}
                  >
                    <span>{user.name} </span>
                    <SvgIcon icon="local:outline/copy-filled" y={2} />
                  </div>
                  <div className="tags">
                    {(user?.keywordTags?.length || 0) > 0 && (
                      <span
                        className="tag border-[#1e5eff] text-[#1e5eff]"
                        title={user.keywordTags?.[0]?.name}
                      >
                        {user.keywordTags?.[0]?.name}
                      </span>
                    )}
                    {user?.tags?.length > 0 && (
                      <span className="tag tag-color">{user.tags?.[0]?.name}</span>
                    )}
                  </div>
                </div>
              </div>
              <div className="user-reaction-data">
                <div>
                  <span className="num">{user?.interactiveNum || 0}</span>
                  <span className="num-label">{PANEL_LABELS[activeTab]['interactiveNum']}</span>
                </div>
                <div>
                  <span className="num">{user?.commentsNum || 0}</span>
                  <span className="num-label">{PANEL_LABELS[activeTab]['commentsNum']}</span>
                </div>
                <div>
                  <span className="num">{user?.keywordsNum || 0}</span>
                  <span className="num-label">{PANEL_LABELS[activeTab]['keywordsNum']}</span>
                </div>
              </div>
            </DrawerHeader>

            <Input.Search
              type="search"
              style={{ width: 224, margin: '15px 0 18px 0', borderRadius: 2 }}
              onSearch={handleSearch}
              prefix={<SearchOutlined style={{ color: '#DBDBDB' }} />}
              addonAfter={null}
              placeholder={`请输入${PANEL_LABELS[activeTab]['comment']}内容`}
            />

            <Flex justify="space-between" align="center">
              <div className="reaction-label before:bg-[#1e5eff]">{reactionLabel}</div>
              {user.platformUniqueId && (
                <TagSelectModal
                  userIds={[user.platformUniqueId]}
                  tagGroups={tags}
                  onKeywordGroupsChange={() => props.onKeywordGroupsChange?.()}
                >
                  <Button size="small">添加标签</Button>
                </TagSelectModal>
              )}
            </Flex>

            <div>
              <div
                style={{
                  margin: '5px 0',
                  height: 'calc(100vh - 255px)',
                  overflow: 'auto',
                }}
              >
                {timelineItems.length > 0 ? <Timeline items={timelineItems} /> : <Empty />}

                {chatList.length >= 14 && (
                  <div ref={bottomDOM} style={{ textAlign: 'center', color: '#989898' }}>
                    加载中...
                  </div>
                )}
              </div>
            </div>
          </DrawerContentWrapper>
        </Drawer>
      )}
    </>
  );
};
