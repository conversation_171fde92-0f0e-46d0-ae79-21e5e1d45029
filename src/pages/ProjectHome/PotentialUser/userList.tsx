import DynamicTree from '@/components/dynamicTree';
import useGetTeamFieldList from '@/hooks/useFields';
import { Customer, exportCustomerList, getCustomerList } from '@/services/customer';
import { Tags } from '@/services/keyword';
import { GetTagGroups, TagsType } from '@/services/tag';
import { proTableRequestAdapterParamsAndData } from '@/utils';
import { STICKY_OFFSETHEADER, transferParams } from '@/utils/common';
import {
  proTableOptionsConfig,
  proTablePaginationConfig,
  proTableSearchConfig,
} from '@/utils/proTableConfig';
import { renderCustomColumns } from '@/components/renderCustomColumns';
import {
  ActionType,
  ProCard,
  ProColumns,
  ProFormInstance,
  ProTable,
} from '@ant-design/pro-components';
import { Link, useParams } from '@umijs/max';
import { usePrevious, useRequest } from 'ahooks';
import { Avatar, message, Select, Space, Tag, Tooltip } from 'antd';

import ExportButton from '@/components/exportButton';
import copy from 'copy-to-clipboard';
import { useEffect, useRef, useState } from 'react';
import { styled } from 'styled-components';
import { PANEL_LABELS, ReactionDetailModal } from './reactionDetailModal';
import { TagSelectModal } from './tagModal';
import { concatURIComponent } from '@/utils/url';
import { usePollingExport } from '@/hooks/usePollingExport';
import { omit } from 'lodash-es';
import dayjs from 'dayjs';
import DefaultAvatar from '@/assets/default-avatar.png';
import { getSumColumnsWidth } from '@/utils/table';

export const UserTag = styled.span`
  display: inline-block;
  padding: 4px 8px;
  margin-right: 4px;
  overflow: hidden;
  font-size: 12px;
  line-height: 16px;
  text-align: center;
  text-overflow: ellipsis;
  border: 1px solid;
  border-radius: 3px;
  border: none;
`;

export const UserList = (props: {
  activeTab: string;
  projectId: string;
  keywordGroups: Tags[];
}) => {
  const [isReactionDetailModalOpen, setIsReactionDetailModalOpen] = useState(false);
  const [selectingUser, setSelectingUser] = useState<Customer>();
  const [selectRow, setSelectRow] = useState<Customer[]>([]);
  const [customColumns, setCustomColumns] = useState<ProColumns<Customer>[]>([]);

  const { activeTab, projectId, keywordGroups } = props;
  const teamFieldListRef = useGetTeamFieldList(projectId);

  const previousActiveTab = usePrevious(activeTab);
  const formRef = useRef<ProFormInstance>();
  const userTableRef = useRef<ActionType>();
  const pageInfo = useRef({
    page: 1,
    size: 20,
  });
  const routeParams = useParams();

  const filedIdArrRef = useRef<string[]>([]);
  const { data: tagRes } = useRequest(() => GetTagGroups(TagsType.customTag, { projectId }));
  const { pollingExport, percent, pollingLoading } = usePollingExport();
  const [isEmptyTable, setIsEmptyTable] = useState(false);

  const userTypeArr = keywordGroups
    .filter((item) => item.name)
    ?.map((keyword) => {
      return {
        text: keyword?.name,
        keywordGroup: keyword?.name,
      };
    });

  useEffect(() => {
    userTableRef.current?.reload();
    userTableRef.current?.clearSelected?.();
    setSelectRow([]);
  }, [activeTab]);

  const defaultColumns: ProColumns<Customer>[] = [
    {
      title: '动态维度筛选',
      dataIndex: 'teamCodeList',
      hideInTable: true,
      formItemProps: {
        label: null,
      },
      renderFormItem: () => {
        return <DynamicTree />;
      },
    },
    {
      title: '客户信息',
      dataIndex: 'name',
      width: 180,
      align: 'left',
      render: (_, record) => (
        <Tooltip title={`[${record.name}] 复制用户ID [${record.id}]`}>
          <div
            style={{ display: 'flex', alignItems: 'flex-start' }}
            onClick={() => {
              copy(String(record.id));
              message.success('复制成功');
            }}
          >
            <Avatar src={record.avatar || DefaultAvatar} style={{ marginRight: 5 }} />
            <span
              style={{
                display: 'inline-block',
                maxWidth: 100,
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
              }}
            >
              {record.name}
            </span>
          </div>
        </Tooltip>
      ),
    },
    {
      title: '用户类型',
      dataIndex: 'keywordGroup',
      align: 'left',
      renderFormItem() {
        return (
          <Select
            allowClear
            options={userTypeArr?.map((item) => {
              return {
                label: item.text,
                value: item.text,
              };
            })}
          />
        );
      },
      render(_, rec) {
        return (
          <Tooltip
            color="#fff"
            title={
              <div>
                {rec?.keywordTags?.map((item) => (
                  <Tag key={item.name}>{item.name}</Tag>
                ))}
              </div>
            }
          >
            {rec?.keywordTags?.slice(0, 2).map((item) => (
              <Tag key={item.name}>{item.name}</Tag>
            ))}
          </Tooltip>
        );
      },
      width: 150,
    },
    {
      title: '用户标签',
      dataIndex: 'tagIds',
      width: 150,
      align: 'left',
      render(_, rec) {
        return (
          <Tooltip
            color="#fff"
            title={
              <div>
                {rec?.tags?.map((item) => (
                  <UserTag
                    style={{ color: '#5b5c61', backgroundColor: '#f3f5fa', borderColor: '#000' }}
                    key={item.id}
                  >
                    {item.name}
                  </UserTag>
                ))}
              </div>
            }
          >
            {rec?.tags?.slice(0, 4).map((item) => (
              <UserTag
                style={{ color: '#5b5c61', backgroundColor: '#f3f5fa', borderColor: '#000' }}
                key={item.id}
              >
                {item.name}
              </UserTag>
            ))}
          </Tooltip>
        );
      },
      renderFormItem() {
        return (
          <Select
            mode="multiple"
            allowClear
            options={tagRes?.data?.map((item) => {
              return {
                label: item?.name,
                options: item?.tags.map((tag) => {
                  return {
                    label: tag?.name,
                    value: tag?.id,
                  };
                }),
              };
            })}
          />
        );
      },
    },
    {
      title: '来源平台',
      dataIndex: 'platform',
      valueType: 'select',
      width: 100,
      align: 'left',
      valueEnum: {
        // @ts-ignore
        1: { text: '抖音', platform: '1' },
        4: { text: '视频号', platform: '4' },
        6: { text: '小红书', platform: '6' },
      },
    },
    {
      search: false,
      title: PANEL_LABELS[activeTab]['interactiveNum'],
      width: 100,
      dataIndex: 'interactiveNum',
      align: 'right',
    },

    {
      title: PANEL_LABELS[activeTab]['commentsNum'],
      width: 100,
      search: false,
      dataIndex: 'commentsNum',
      align: 'right',
    },
    {
      title: PANEL_LABELS[activeTab]['keywordsNum'],
      width: 120,
      align: 'right',
      search: false,
      dataIndex: activeTab === '3' ? 'replyRate' : 'keywordsNum',
      render(_, rec) {
        const isRate = typeof rec.replyRate === 'number';
        if (activeTab === '3' && isRate) {
          return `${isRate ? (rec.replyRate! * 100).toFixed(1) + '%' : '-'}`;
        } else {
          return `${rec.keywordsNum || '-'} `;
        }
      },
    },
    {
      title: PANEL_LABELS[activeTab]['firstCommentTime'],
      dataIndex: 'firstCommentTime',
      valueType: 'dateTimeRange',
      align: 'left',
      fieldProps: {
        placeholder: [PANEL_LABELS[activeTab]['firstCommentTime'], ''],
      },
      render(_, record) {
        return <>{record.firstCommentTime}</>;
      },
      search: {
        transform: (value) => {
          return {
            firstCommentTimeBeg: value[0],
            firstCommentTimeEnd: value[1],
          };
        },
      },
    },
    {
      title: PANEL_LABELS[activeTab]['latestCommentTime'],
      dataIndex: 'latestCommentTime',
      // width: 150,
      align: 'left',
      fieldProps: {
        placeholder: [PANEL_LABELS[activeTab]['latestCommentTime'], ''],
      },
      valueType: 'dateTimeRange',
      render(_, record) {
        return <>{record.latestCommentTime}</>;
      },
      search: {
        transform: (value) => {
          return {
            latestCommentTimeBeg: value[0],
            latestCommentTimeEnd: value[1],
          };
        },
      },
    },
    {
      title: '操作',
      dataIndex: 'type',
      valueType: 'option',
      align: 'left',
      fixed: 'right',
      width: 125,
      render: (text, record) => [
        <a
          key={`${record.id}_detail`}
          onClick={() => {
            if (activeTab !== '3') {
              setSelectingUser(record);
              setIsReactionDetailModalOpen(true);
            }
          }}
        >
          {activeTab === '3' ? (
            <Link
              key={record.id}
              to={concatURIComponent([
                `project`,
                routeParams.projectKey,
                routeParams.industryType,
                'clue',
                projectId,
                record?.accountUserId,
                record?.platformUniqueId,
                record.name,
                'session-list',
              ])}
              state={{
                teamFieldList: record.teamFieldList,
              }}
            >
              对话流
            </Link>
          ) : (
            '详情'
          )}
        </a>,
        <TagSelectModal
          tagGroups={tagRes?.data || []}
          key={`${record.id}_batch_add_tag`}
          userIds={
            selectRow.length > 0
              ? selectRow.map((item) => item.platformUniqueId)
              : [record.platformUniqueId]
          }
          onKeywordGroupsChange={() => userTableRef.current?.reload()}
        >
          <a>添加标签</a>
        </TagSelectModal>,
      ],
      search: false,
    },
  ];

  const handleUserListExport = async (form?: ProFormInstance<any>) => {
    const params = form?.getFieldsValue();
    const firstCommentTimeBeg = params.firstCommentTime?.[0];
    const firstCommentTimeEnd = params.firstCommentTime?.[1];
    const latestCommentTimeBeg = params.latestCommentTime?.[0];
    const latestCommentTimeEnd = params.latestCommentTime?.[1];
    params.firstCommentTime && delete params.firstCommentTime;
    params.latestCommentTime && delete params.latestCommentTime;
    const fieldList = transferParams(params, filedIdArrRef.current) || [];
    const paramsFormat = {
      ...params,
      fieldList: fieldList.filter((item) => item.fieldId && item.value),
      interactiveType: parseInt(props.activeTab),
      projectId,
      firstCommentTimeBeg: firstCommentTimeBeg
        ? dayjs(firstCommentTimeBeg).format('YYYY-MM-DD HH:mm:ss')
        : undefined,
      firstCommentTimeEnd: firstCommentTimeEnd
        ? dayjs(firstCommentTimeEnd).format('YYYY-MM-DD HH:mm:ss')
        : undefined,
      latestCommentTimeBeg: latestCommentTimeBeg
        ? dayjs(latestCommentTimeBeg).format('YYYY-MM-DD HH:mm:ss')
        : undefined,
      latestCommentTimeEnd: latestCommentTimeEnd
        ? dayjs(latestCommentTimeEnd).format('YYYY-MM-DD HH:mm:ss')
        : undefined,
    };
    const omitPageParams = omit(paramsFormat, ['pageSize', 'current']);
    const res = await exportCustomerList({ ...omitPageParams });
    if (res.data) {
      pollingExport({ taskId: res.data });
    } else {
      message.error('导出失败,请重试');
    }
  };

  const columns: ProColumns<Customer>[] = [...defaultColumns, ...customColumns].map((item) => {
    return {
      fieldProps: {
        placeholder: item.title,
      },
      align: ['name'].includes(item.dataIndex as string) ? 'left' : 'center',
      ...item,
    };
  });

  return (
    <ProCard>
      <ProTable<Customer, Customer>
        ghost
        sticky={{ offsetHeader: STICKY_OFFSETHEADER }}
        columns={columns}
        actionRef={userTableRef}
        rowKey={(rec) => rec.id}
        scroll={{ x: isEmptyTable ? getSumColumnsWidth(columns) : 'max-content' }}
        formRef={formRef}
        tableClassName="custom-table"
        search={{ ...proTableSearchConfig }}
        options={{ ...proTableOptionsConfig }}
        pagination={{ ...proTablePaginationConfig }}
        rowSelection={{
          type: 'checkbox',
          onChange: (_, selectedRows) => {
            setSelectRow(selectedRows);
          },
        }}
        tableAlertRender={({ selectedRowKeys }) => {
          return (
            <Space size={24}>
              <span>已选 {selectedRowKeys.length} 项</span>
              <TagSelectModal
                tagGroups={tagRes?.data || []}
                key={`batch_add_tag`}
                userIds={selectRow.map((item) => item.platformUniqueId)}
                onKeywordGroupsChange={() => userTableRef.current?.reload()}
              >
                <a>添加标签</a>
              </TagSelectModal>
            </Space>
          );
        }}
        toolBarRender={() => [
          <ExportButton
            exportFn={() => handleUserListExport(formRef.current)}
            loading={pollingLoading}
            percent={percent}
            key="export"
          />,
        ]}
        postData={(data: Customer[]) => {
          // 判断表格数据是否为空
          data.length === 0 ? setIsEmptyTable(true) : setIsEmptyTable(false);
          const switchTab =
            (previousActiveTab && previousActiveTab !== activeTab) || !previousActiveTab;
          if (switchTab && data.length > 0) {
            renderCustomColumns(teamFieldListRef.current).then(({ customColumns, fieldIdArr }) => {
              filedIdArrRef.current = fieldIdArr;
              // @ts-ignore
              setCustomColumns(customColumns);
            });
          }
          // 将teamFields内的字段都抽出来
          const formatData = data.map((item) => {
            const res = { ...item };
            const teamFields = res.teamFieldList;
            if (teamFields) {
              teamFields.forEach((field) => {
                (res as any)[field.fieldId] = field.value;
              });
            }
            return res;
          });
          return formatData;
        }}
        beforeSearchSubmit={(params) => {
          const fieldList = transferParams(params, filedIdArrRef.current);
          if (fieldList.length > 0) {
            return { ...params, fieldList };
          }
          return params;
        }}
        request={(formParams, sorter) => {
          pageInfo.current = {
            page: formParams.current || 1,
            size: formParams.pageSize || 20,
          };
          const params = {
            page: formParams.current || 1,
            size: formParams.pageSize || 20,
            ...formParams,
            interactiveType: parseInt(props.activeTab),
            projectId: parseInt(projectId),
          };
          return proTableRequestAdapterParamsAndData(params, sorter, getCustomerList);
        }}
      />

      {projectId && selectingUser && (
        <ReactionDetailModal
          isDrawOpen={isReactionDetailModalOpen}
          setDrawOpen={setIsReactionDetailModalOpen}
          activeTab={parseInt(activeTab)}
          title="客户详情"
          reactionLabel={PANEL_LABELS[activeTab]['detailLabel']}
          user={selectingUser}
          projectId={parseInt(projectId)}
          onClose={() => setSelectingUser(undefined)}
          tags={tagRes?.data || []}
          onKeywordGroupsChange={() => userTableRef.current?.reload()}
        />
      )}
    </ProCard>
  );
};
