import {
  Copy<PERSON>obot,
  <PERSON>reateRobot,
  NotifyConfigList,
  NotifyRobotListItem,
  UpdateNotifyRobot,
} from '@/services/notify';
import { ModalForm, ProFormRadio, ProFormText } from '@ant-design/pro-components';
import { message } from 'antd';
import { RuleObject } from 'antd/es/form';
import { StoreValue } from 'antd/es/form/interface';

interface NoticeModalFormProps {
  noticeList: NotifyRobotListItem[] | NotifyConfigList[];
  initValue?: NotifyRobotListItem | NotifyConfigList;
  trigger?: JSX.Element;
  projectId?: string;
  refresh?: () => void;
  type: 1 | 2 | 3;
  fieldId?: string;
  fieldValue?: React.ReactNode;
  copyNotifyRobotId?: number;
  actionType?: number;
  showChangeActionType?: boolean;
}

enum NoticeMode {
  FEISHU = 1,
  DINGDING = 2,
  QIYEWEIXIN = 3,
}

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 18 },
};

type NoticeFormValues = {
  robotName: string;
  webhook: string;
  bindType: number;
  actionType?: number;
};

const NoticeModalForm = (props: NoticeModalFormProps) => {
  const {
    trigger,
    noticeList,
    initValue,
    projectId,
    refresh,
    type,
    fieldId,
    fieldValue,
    copyNotifyRobotId,
    actionType = 1,
    showChangeActionType = false,
  } = props;

  const handleWebhookValidator = (_: RuleObject, value: StoreValue) => {
    if (value === undefined || !value.trim()) {
      return Promise.reject('请输入请求地址');
    }
    if (initValue && initValue.webhook === value) {
      return Promise.resolve();
    } else if (noticeList.some((item) => item.webhook === value)) {
      return Promise.reject('提示: 请求地址重复');
    }
    return Promise.resolve();
  };

  const handleNameValidate = (_: RuleObject, value: StoreValue) => {
    if (!value) {
      return Promise.reject('请输入名称');
    } else if (value?.length > 20) {
      return Promise.reject('限制20个字符');
    }
    return Promise.resolve();
  };

  return (
    <ModalForm<NoticeFormValues>
      trigger={trigger}
      autoFocusFirstInput
      modalProps={{
        destroyOnHidden: true,
        maskClosable: true,
      }}
      {...formItemLayout}
      width="800px"
      layout={'horizontal'}
      onFinish={async (values) => {
        const actionTypeValue = values.actionType || actionType;
        if (initValue) {
          const res = await UpdateNotifyRobot({ ...values, id: initValue.id, fieldId, fieldValue });
          res.code === 0 ? message.success('更新成功') : message.error('更新失败');
          await refresh?.();
        } else {
          if (copyNotifyRobotId) {
            const res = await CopyRobot({
              ...values,
              projectId,
              type,
              fieldId,
              fieldValue,
              copyNotifyRobotId,
              actionType: actionTypeValue,
            });
            res.code === 0 ? message.success('创建副本成功') : message.error('创建副本失败');
          } else {
            const res = await CreateRobot({
              ...values,
              projectId,
              type,
              fieldId,
              fieldValue,
              actionType: actionTypeValue,
            });
            res.code === 0 ? message.success('创建成功') : message.error('创建失败');
          }
          await refresh?.();
        }
        return true;
      }}
    >
      <ProFormText
        width="md"
        name="robotName"
        label="名称"
        placeholder="请输入名称"
        initialValue={initValue?.robotName}
        rules={[{ required: true, validator: handleNameValidate }]}
      />
      <ProFormRadio.Group
        name="bindType"
        label="通知方式"
        initialValue={initValue?.bindType}
        rules={[{ required: true, message: '请选择通知方式' }]}
        options={[
          {
            label: '飞书',
            value: NoticeMode.FEISHU,
          },
          {
            label: '钉钉',
            value: NoticeMode.DINGDING,
          },
          {
            label: '企业微信',
            value: NoticeMode.QIYEWEIXIN,
          },
        ]}
      />
      {showChangeActionType && (
        <ProFormRadio.Group
          name="actionType"
          label="作用类型"
          initialValue={initValue?.actionType}
          rules={[{ required: true, message: '请选择通知方式' }]}
          options={[
            {
              label: '实时提醒',
              value: 1,
            },
            {
              label: '日报',
              value: 2,
            },
          ]}
        />
      )}
      <ProFormText
        width="md"
        name="webhook"
        label="请求地址"
        initialValue={initValue?.webhook}
        placeholder="请输入请求地址"
        rules={[{ required: true, validator: handleWebhookValidator }]} // 校验请求地址是否已经存在
      />
    </ModalForm>
  );
};

export default NoticeModalForm;
