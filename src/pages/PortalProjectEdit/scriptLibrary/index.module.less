.card {
  background-color: #fff;
  width: 100%;
  padding: 12px;
  border-radius: 8px;
  border: 1px solid #fff;
  position: relative;
  transition: all 0.3s;

  &:hover {
    border: 1px solid #1e5eff;
    transition: all 0.3s;
    cursor: pointer;
  }
  .title {
    font-size: 18px;
    color: #0e1015;
    white-space: nowrap;
    font-weight: 500;
  }
  .subtitle {
    font-size: 16px;
    color: #0e1015;
  }
  .truncate {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .date {
    font-size: 14px;
    color: #64666b;
  }
  .content {
    padding-top: 16px;
    font-size: 14px;
    word-break: break-all;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2; /* 这里是超出几行省略 */
    overflow: hidden;
  }
  .footer {
    padding-top: 16px;
    border-top: 1px solid #d3d5db;
    font-size: 14px;
    color: #64666b;
    display: flex;
    justify-content: space-between;
    margin-top: 16px;
  }
}

.list {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  overflow-y: auto;
  margin-top: 10px;
  height: calc(100vh - 250px);

  .list-item {
    background-color: #fff;
    width: 100%;
    padding: 12px;
    border-radius: 8px;
    border: 1px solid #fff;
    position: relative;
    &:hover {
      border: 1px solid #1e5eff;
      transition: all 0.3s;
      cursor: pointer;
    }
  }
}
