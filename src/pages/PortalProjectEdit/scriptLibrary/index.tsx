import {
  Critic,
  deleteOutline,
  getCarList,
  getOutlineList,
  saveCar,
  saveOutline,
} from '@/services/ai-live/outline';
import { useParams, useQuery } from '@umijs/max';
import type { InputRef } from 'antd';
import { ProFormDependency, ProFormSelect } from '@ant-design/pro-components';
import type { PaginationProps, RadioChangeEvent } from 'antd';
import {
  App,
  Button,
  Col,
  Divider,
  Empty,
  Flex,
  Form,
  Input,
  message,
  Modal,
  Pagination,
  Popconfirm,
  Radio,
  Row,
  Select,
  Space,
  Tooltip,
} from 'antd';
import dayjs from 'dayjs';
import { useMemo, useRef, useState } from 'react';
import { parseJSONToMarkdown, parseMarkdownToJSON } from './parseMarkdownToJSON';

import { calculateReadingTime } from './calculateReadingTime';
import { SvgIcon } from '@/components/SvgIcon';

import { CheckOutlined, PlusCircleOutlined } from '@ant-design/icons';
import { RichTextTextArea } from '@/components/RichTextTextArea';
import styled from './index.module.less';

function useLiveOutlineList(params: { page: number; size: number }) {
  const { projectId } = useParams();
  const { data, error, isFetching, refetch } = useQuery({
    queryFn: () => getOutlineList({ page: params.page, size: params.size, projectId }),
    queryKey: [`live-outline-list`, params.page, params.size, projectId],
  });
  return { data: data?.items, error, loading: isFetching, total: data?.total, refetch };
}
function useCarList() {
  const { projectId } = useParams();
  const { data, error, isFetching, refetch } = useQuery({
    queryFn: () => getCarList({ projectId }),
    queryKey: ['car-list', projectId],
    enabled: !!projectId,
  });
  return {
    data: data?.map((item) => ({
      label: item.carName,
      value: item.carId,
    })),
    error,
    loading: isFetching,
    refetch,
  };
}

type EditFormType = {
  carId: string;
  text: string;
  outlineName: string;
  name: string;
};
type NewFormType = {
  outlineName: string;
  carId: string;
  text: string;
  critic: string;
};
export function ScriptLibrary() {
  const [flitterParams, setFlitterParams] = useState('all');
  const [currentScript, setCurrentScript] = useState<Critic | null>(null);

  const { projectId } = useParams();
  const { data: carList, refetch: refetchCarList } = useCarList();

  const [carType, setCarType] = useState<string | null>(null);
  const [queryParam, setQueryParm] = useState({ page: 1, size: 10 });
  const { data: liveOutlineList, total, refetch } = useLiveOutlineList(queryParam);
  const [showAddCarInput, setShowAddCarInput] = useState(false);

  const onChange: PaginationProps['onChange'] = (pageNumber) => {
    setQueryParm((pre) => ({ ...pre, page: pageNumber }));
  };
  const onShowSizeChange: PaginationProps['onShowSizeChange'] = (current, pageSize) => {
    setQueryParm((pre) => ({ ...pre, size: pageSize }));
  };
  const [editForm] = Form.useForm<EditFormType>();

  const [newForm] = Form.useForm<NewFormType>();
  const [name, setName] = useState('');
  const inputRef = useRef<InputRef>(null);
  const [newModalOpen, setNewModalOpen] = useState(false);
  const [editModalOpen, setEditModalOpen] = useState(false);
  const onNameChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setName(event.target.value);
  };

  const addItem = async (e: React.MouseEvent<HTMLButtonElement | HTMLAnchorElement>) => {
    e.preventDefault();
    if (!projectId) return;
    await saveCar({ type: name, projectId: projectId });
    await refetchCarList();
    setShowAddCarInput(false);
    message.success('添加成功');
    setName('');
  };
  const newCarId = Form.useWatch('carId', newForm);
  const newOutlineName = Form.useWatch('outlineName', newForm);

  const newFormDisabled = useMemo(() => {
    const carTypeItem = liveOutlineList?.find((item) => item.carId === newCarId);

    const critics = carTypeItem?.critics.filter((item) => item.critic === '优惠政策');

    if (critics?.length && newOutlineName === '优惠政策') {
      return { disabled: true, text: '该车型已有优惠政策内容' };
    }
    return { disabled: false, text: '保存' };
  }, [liveOutlineList, newCarId, newOutlineName]);

  return (
    <App>
      <div className="flex">
        <div
          style={{
            flex: '60%',
            padding: '20px',
            paddingTop: '0',
            height: 'calc(100vh - 250px)',
            overflowY: 'auto',
            position: 'relative',
            scrollbarWidth: 'thin',
          }}
        >
          <Flex
            justify="space-between"
            style={{
              position: 'sticky',
              zIndex: 999,
              top: '0',
              backgroundColor: '#FBFBFB',
              padding: '10px ',
              borderRadius: '8px',
            }}
          >
            <Radio.Group
              optionType="button"
              defaultValue="all"
              value={flitterParams}
              onChange={(e: RadioChangeEvent) => {
                setFlitterParams(e.target.value);
              }}
              options={[
                { label: '全部', value: 'all' },
                { label: '车型介绍', value: '车型介绍' },
                { label: '优惠政策', value: '优惠政策' },
              ]}
            />
            <Button
              type="primary"
              onClick={() => {
                newForm.resetFields();
                setNewModalOpen(true);
              }}
            >
              新增脚本
            </Button>
          </Flex>
          <Space direction="vertical" size="middle" style={{ display: 'flex' }}>
            {liveOutlineList?.length === 0 && (
              <Empty
                style={{
                  marginTop: '150px',
                }}
                description={<div>暂无数据</div>}
              />
            )}
            {liveOutlineList
              ?.filter((outline) => outline.critics.length > 0)
              ?.map((item) => (
                <Space
                  direction="vertical"
                  size="middle"
                  style={{ display: 'flex' }}
                  key={item.carType}
                >
                  <div
                    style={{
                      fontSize: '18px',
                      color: '#0E1015',
                    }}
                  >
                    车型：{item.carType}
                  </div>
                  <div>
                    <Row gutter={[16, 8]}>
                      {item.critics
                        .filter((i) => {
                          if (flitterParams === 'all') {
                            return true;
                          }
                          if (flitterParams === '车型介绍') {
                            return i.critic != '优惠政策';
                          }
                          if (flitterParams === '优惠政策') {
                            return i.critic === '优惠政策';
                          }
                        })
                        .map((critic, index) => (
                          <Col span={8} key={index}>
                            <div
                              className={styled.card}
                              style={{
                                border:
                                  currentScript?.id === critic.id
                                    ? '1px solid #1E5EFF'
                                    : '1px solid #fff',
                              }}
                              onClick={() => {
                                if (currentScript?.id === critic.id) {
                                  return;
                                }
                                editForm.setFieldValue('name', critic.critic);
                                // @ts-ignore
                                editForm.setFieldValue('carType', item.carType);
                                editForm.setFieldValue(
                                  'text',
                                  parseJSONToMarkdown(critic?.outline[0].caption),
                                );
                                editForm.setFieldValue(
                                  // @ts-ignore
                                  'type',
                                  critic.critic === '优惠政策' ? '优惠政策' : '车型介绍',
                                );
                                setCurrentScript(critic);
                                setCarType(item.carType);
                              }}
                            >
                              <div>
                                {critic.critic !== '优惠政策' && (
                                  <span className="title">车型介绍</span>
                                )}
                                <Tooltip title={critic.critic}>
                                  <span
                                    className="subtitle"
                                    style={{
                                      fontWeight: critic.critic === '优惠政策' ? '500' : 'normal',
                                      marginLeft: critic.critic === '优惠政策' ? '0' : '8px',
                                    }}
                                  >
                                    {critic.critic}
                                  </span>
                                </Tooltip>
                              </div>
                              <div className="content">
                                <span
                                  style={{
                                    color: '#0E1015',
                                  }}
                                />
                                环节内容：
                                <Tooltip
                                  title={critic.outline[0].caption
                                    ?.map((caption) => caption.subtitle)
                                    .join('、')}
                                >
                                  <span
                                    style={{
                                      color: '#64666B',
                                    }}
                                  >
                                    {critic.outline[0].caption
                                      ?.map((caption) => caption.subtitle)
                                      .join('、')}
                                  </span>
                                </Tooltip>
                              </div>
                              <div className="footer">
                                <span key={0} className="truncate">
                                  {dayjs(critic.create_time).format('YYYY-MM-DD')}
                                </span>
                                <span key={1} className="truncate">
                                  {critic.outline.reduce((acc, cur) => acc + cur.word_count, 0)}字
                                </span>
                              </div>
                            </div>
                          </Col>
                        ))}
                    </Row>
                  </div>
                </Space>
              ))}
          </Space>

          {liveOutlineList?.length !== 0 && (
            <div
              style={{
                display: 'flex',
                justifyContent: 'end',
                marginTop: '20px',
              }}
            >
              <Pagination
                onChange={onChange}
                onShowSizeChange={onShowSizeChange}
                defaultCurrent={queryParam.page}
                total={total}
                pageSize={queryParam.size}
                showSizeChanger={true}
              />
            </div>
          )}
        </div>

        <div
          style={{
            flex: '40%',
            padding: '20px',
            borderLeft: '1px solid #D3D5DB',
          }}
        >
          {currentScript ? (
            <div>
              <Flex justify="space-between" align="center">
                <Space>
                  <span
                    style={{
                      fontSize: '18px',
                    }}
                  >
                    {carType}
                  </span>
                  <span
                    style={{
                      fontSize: '16px',
                    }}
                  >
                    {currentScript.critic}
                  </span>
                </Space>

                <Space>
                  <Button
                    type="default"
                    onClick={() => {
                      editForm.setFieldValue('name', currentScript.critic);
                      editForm.setFieldValue('carId', currentScript.carId);
                      editForm.setFieldValue(
                        'text',
                        parseJSONToMarkdown(currentScript?.outline[0].caption),
                      );
                      editForm.setFieldValue(
                        'outlineName',
                        currentScript.critic === '优惠政策' ? '优惠政策' : '车型介绍',
                      );
                      setEditModalOpen(true);
                    }}
                  >
                    编辑
                  </Button>
                  <Popconfirm
                    title="删除"
                    onConfirm={async () => {
                      if (currentScript.critic !== '优惠政策') {
                        const carTypeItem = liveOutlineList?.find(
                          (item) => item.carType === carType,
                        );
                        const critics = carTypeItem?.critics.filter(
                          (item) => item.critic !== '优惠政策',
                        );
                        if (critics?.length === 1) {
                          message.error('内容不可删除');
                          return;
                        }
                      }

                      await deleteOutline({
                        carId: currentScript.carId,
                        critic: currentScript.critic,
                      });
                      await refetch();
                      setCurrentScript(null);
                      message.success('删除成功');
                    }}
                  >
                    <Button type="default">删除</Button>
                  </Popconfirm>
                </Space>
              </Flex>
              {/* selected preview */}
              <div className={styled.list}>
                {currentScript.outline[0].caption.map((item) => (
                  <div key={item.captionId} className="list-item">
                    <p>
                      <span
                        style={{
                          color: '#0E1015',
                        }}
                      >
                        {item.subtitle}
                      </span>
                      <span
                        style={{
                          color: '#64666B',
                          marginLeft: '5px',
                        }}
                      >
                        {calculateReadingTime(item.text)} min
                      </span>
                    </p>
                    <p
                      style={{
                        color: '#64666B',
                        fontSize: '14px',
                      }}
                      dangerouslySetInnerHTML={{ __html: item.text }}
                    />
                    <div
                      style={{
                        position: 'absolute',
                        right: '10px',
                        top: '10px',
                        cursor: 'pointer',
                      }}
                      onClick={(event) => {
                        event.stopPropagation();
                        event.preventDefault();
                        setEditModalOpen(true);
                        editForm.setFieldValue('name', currentScript.critic);
                        editForm.setFieldValue('carId', carType);
                        editForm.setFieldValue(
                          'text',
                          parseJSONToMarkdown(currentScript?.outline[0].caption),
                        );
                        editForm.setFieldValue(
                          'outlineName',
                          currentScript.critic === '优惠政策' ? '优惠政策' : '车型介绍',
                        );
                      }}
                    >
                      <SvgIcon
                        icon="local:outline/edit"
                        width="20"
                        height="20"
                        stroke="1"
                        color="#1E5EFF"
                      />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <Empty
              style={{
                marginTop: '150px',
              }}
              description={<div>请在左侧选择脚本内容</div>}
            />
          )}
        </div>
      </div>

      {/* 新增 Modal */}
      <Modal
        open={newModalOpen}
        footer={null}
        title="新增脚本"
        onCancel={() => {
          setNewModalOpen(false);
        }}
        width={1200}
      >
        <Form<NewFormType>
          form={newForm}
          onFinish={async (values) => {
            const postData = {
              critic: values?.critic,
              carId: values?.carId,
              outline: [
                {
                  caption: parseMarkdownToJSON(values.text),
                  outlineName: values?.outlineName,
                },
              ],
            };
            // @ts-ignore
            await saveOutline(postData);
            await refetch();
            setShowAddCarInput(false);
            setNewModalOpen(false);
            message.success('提交成功');
            return true;
          }}
        >
          <div
            className="flex"
            style={{
              gap: '20px',
            }}
          >
            <div
              style={{
                flex: '60%',
                display: 'flex',
                flexDirection: 'column',
              }}
            >
              <Space>
                <Form.Item name="outlineName" label="脚本类型" rules={[{ required: true }]}>
                  <Select
                    placeholder="请选择脚本类型"
                    style={{ width: '200px' }}
                    options={[
                      { label: '车型介绍', value: '车型介绍' },
                      { label: '优惠政策', value: '优惠政策' },
                    ]}
                  />
                </Form.Item>
                <ProFormSelect
                  width="sm"
                  name="carId"
                  label="车型"
                  rules={[{ required: true }]}
                  params={{
                    projectId,
                  }}
                  style={{ width: '200px' }}
                  fieldProps={{
                    dropdownRender(menu) {
                      return (
                        <>
                          {menu}
                          <Divider style={{ margin: '8px 0' }} />
                          <Space>
                            {showAddCarInput && (
                              <Input
                                placeholder="输入要添加的车型"
                                ref={inputRef}
                                value={name}
                                onChange={onNameChange}
                                onKeyDown={(e) => e.stopPropagation()}
                                addonAfter={<CheckOutlined onClick={addItem} />}
                              />
                            )}
                            <Button
                              type="text"
                              icon={<PlusCircleOutlined color="#2160F9" />}
                              onClick={() => {
                                setShowAddCarInput(true);
                              }}
                              style={{
                                width: '100%',
                                color: '#2160F9',
                              }}
                            >
                              添加车型
                            </Button>
                          </Space>
                        </>
                      );
                    },
                    options: carList,
                  }}
                />

                {/* <ProFormDependency name={['outlineName']}>
                  {({ outlineName }) => {
                    if (outlineName === '优惠政策') return null;
                    return <ProFormText name="critic" label="脚本名称" />;
                  }}
                </ProFormDependency> */}
              </Space>
              <Form.Item
                noStyle
                shouldUpdate={(prevValues, currentValues) =>
                  prevValues.gender !== currentValues.gender
                }
              >
                {({ getFieldValue }) =>
                  getFieldValue('outlineName') === '优惠政策' ? null : (
                    <Form.Item name="critic" label="脚本名称" rules={[{ required: true }]}>
                      <Input />
                    </Form.Item>
                  )
                }
              </Form.Item>

              <Form.Item
                layout="vertical"
                label="新建脚本"
                name="text"
                rules={[{ required: true }]}
                labelCol={{ span: 24 }}
                style={{ height: '500px' }}
              >
                <RichTextTextArea placeholder={`# 无标题\n\n无内容`} />
              </Form.Item>
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'end',
                  marginTop: '20px',
                }}
              >
                <Button
                  onClick={() => {
                    newForm.submit();
                  }}
                  type="primary"
                  disabled={newFormDisabled.disabled}
                  style={{
                    justifySelf: 'center',
                  }}
                >
                  {newFormDisabled.text}
                </Button>
              </div>
            </div>
            <Divider
              type="vertical"
              style={{
                height: '600px',
                backgroundColor: '#D3D5DB',
              }}
            />
            <div
              style={{
                flex: '40%',
              }}
            >
              <ProFormDependency name={['critic']}>
                {({ critic }) => {
                  if (critic === '优惠政策') return null;
                  return (
                    <div
                      style={{
                        fontSize: '16px',
                      }}
                    >
                      {critic}
                    </div>
                  );
                }}
              </ProFormDependency>
              <ProFormDependency name={['text']}>
                {({ text }) => {
                  const json = parseMarkdownToJSON(text);
                  if (!text)
                    return (
                      <Empty
                        style={{
                          marginTop: '150px',
                        }}
                        description={<div>请在左侧编辑脚本内容</div>}
                      />
                    );
                  if (json.length === 0) {
                    return (
                      <Empty
                        style={{
                          marginTop: '150px',
                        }}
                        description={<div>请输入符合格式的文本，否则无法预览</div>}
                      />
                    );
                  }

                  return (
                    <div
                      className={styled.list}
                      style={{
                        height: '600px',
                        overflowY: 'auto',
                      }}
                    >
                      {json.map((item) => (
                        <div
                          key={item.captionId}
                          className="list-item"
                          style={{
                            backgroundColor: '#F5F6FA',
                            height: 'fit-content',
                          }}
                        >
                          <p>
                            <span
                              style={{
                                color: '#0E1015',
                              }}
                            >
                              {item.subtitle}
                            </span>
                            <span
                              style={{
                                color: '#64666B',
                                marginLeft: '5px',
                              }}
                            >
                              {calculateReadingTime(item.text)} min
                            </span>
                          </p>
                          <p
                            style={{
                              color: '#64666B',
                              fontSize: '14px',
                            }}
                            dangerouslySetInnerHTML={{
                              __html: item.text,
                            }}
                          />
                        </div>
                      ))}
                    </div>
                  );
                }}
              </ProFormDependency>
            </div>
          </div>
        </Form>
      </Modal>
      {/* 编辑 Modal */}
      <Modal
        open={editModalOpen}
        footer={null}
        title="编辑脚本"
        onCancel={() => {
          setEditModalOpen(false);
        }}
        width={1200}
      >
        <Form<EditFormType>
          form={editForm}
          onFinish={async (values) => {
            const postData = {
              id: currentScript?.id,
              critic: values?.name,
              carId: values?.carId,
              outline: [
                {
                  caption: parseMarkdownToJSON(values.text),
                  outlineName: values?.outlineName,
                },
              ],
            };

            try {
              // @ts-ignore
              const res = await saveOutline(postData);
              if (res.msg) {
                message.error(res.msg);
                return;
              }
              setCurrentScript(res);
              await refetch();
              setShowAddCarInput(false);
              setEditModalOpen(false);
              message.success('提交成功');
            } catch (error) {
              message.error(JSON.stringify(error));
            }
          }}
        >
          <div
            className="flex"
            style={{
              gap: '20px',
            }}
          >
            <div
              style={{
                flex: '60%',
                display: 'flex',
                flexDirection: 'column',
              }}
            >
              <Form.Item
                noStyle
                shouldUpdate={(prevValues, currentValues) =>
                  prevValues.gender !== currentValues.gender
                }
              >
                {({ getFieldValue }) => {
                  return (
                    <Row
                      style={{
                        gap: '32px',
                        margin: '20px 0',
                        paddingLeft: '10px',
                      }}
                    >
                      <Row>
                        <label>脚本类型：</label>
                        <div>{getFieldValue('type')}</div>
                      </Row>

                      <Space>
                        <label>车型：</label>
                        <div>{getFieldValue('carType')}</div>
                      </Space>
                    </Row>
                  );
                }}
              </Form.Item>
              <Form.Item name="name" hidden>
                <Input />
              </Form.Item>
              <Form.Item name="carId" hidden>
                <Input />
              </Form.Item>
              <Form.Item name="outlineName" hidden>
                <Input />
              </Form.Item>
              <Form.Item
                noStyle
                shouldUpdate={(prevValues, currentValues) =>
                  prevValues.gender !== currentValues.gender
                }
              >
                {({ getFieldValue }) =>
                  getFieldValue('outlineName') === '优惠政策' ? (
                    <Form.Item name="outlineName" hidden>
                      <Input />
                    </Form.Item>
                  ) : (
                    <Form.Item name="name" label="脚本名称" rules={[{ required: true }]}>
                      <Input />
                    </Form.Item>
                  )
                }
              </Form.Item>
              <Form.Item
                layout="vertical"
                label="新建脚本"
                name="text"
                rules={[{ required: true }]}
                labelCol={{ span: 24 }}
                style={{ height: '500px' }}
              >
                <RichTextTextArea placeholder={`# 无标题\n\n无内容`} />
              </Form.Item>
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'end',
                  marginTop: '20px',
                }}
              >
                <Button
                  onClick={() => {
                    editForm.submit();
                  }}
                  type="primary"
                  style={{
                    justifySelf: 'center',
                  }}
                >
                  保存
                </Button>
              </div>
            </div>
            <Divider
              type="vertical"
              style={{
                height: '600px',
                backgroundColor: '#D3D5DB',
              }}
            />
            <div
              style={{
                flex: '40%',
              }}
            >
              <ProFormDependency name={['critic']}>
                {({ critic }) => {
                  if (critic === '优惠政策') return null;
                  return (
                    <div
                      style={{
                        fontSize: '16px',
                      }}
                    >
                      {critic}
                    </div>
                  );
                }}
              </ProFormDependency>
              <ProFormDependency name={['text']}>
                {({ text }) => {
                  const json = parseMarkdownToJSON(text);
                  if (!text)
                    return (
                      <Empty
                        style={{
                          marginTop: '150px',
                        }}
                        description={<div>请在左侧编辑脚本内容</div>}
                      />
                    );
                  if (json.length === 0) {
                    return (
                      <Empty
                        style={{
                          marginTop: '150px',
                        }}
                        description={<div>请输入符合格式的文本，否则无法预览</div>}
                      />
                    );
                  }

                  return (
                    <div
                      className={styled.list}
                      style={{
                        height: '600px',
                        overflowY: 'auto',
                      }}
                    >
                      {json.map((item) => (
                        <div
                          key={item.captionId}
                          className="list-item"
                          style={{
                            backgroundColor: '#F5F6FA',
                            height: 'fit-content',
                          }}
                        >
                          <p>
                            <span
                              style={{
                                color: '#0E1015',
                              }}
                            >
                              {item.subtitle}
                            </span>
                            <span
                              style={{
                                color: '#64666B',
                                marginLeft: '5px',
                              }}
                            >
                              {calculateReadingTime(item.text)} min
                            </span>
                          </p>
                          <p
                            style={{
                              color: '#64666B',
                              fontSize: '14px',
                            }}
                            dangerouslySetInnerHTML={{
                              __html: item.text,
                            }}
                          />
                        </div>
                      ))}
                    </div>
                  );
                }}
              </ProFormDependency>
            </div>
          </div>
        </Form>
      </Modal>
    </App>
  );
}
