import { Caption } from '@/services/ai-live/outline';

export function parseMarkdownToJSON(markdown: string): Caption[] {
  // 切分字符串为多个部分，每个部分包括标题和可能的正文
  if (!markdown) return [];
  const sections = markdown.split(/(?=# )/g);
  const result = [];

  for (const section of sections) {
    if (section.trim() === '') continue; // 跳过空段落

    // 使用正则表达式来查找标题和可能的正文
    const match = section.match(/# (.+)(?:\n([\s\S]+))?/);
    if (match) {
      const title = match[1].trim();
      const content = match[2] ? match[2].trim().replace(/\n/g, '<br>') : '';
      result.push({ subtitle: title, text: content });
    }
  }

  return result;
}

export function parseJSONToMarkdown(json: Caption[] | undefined) {
  if (!json) return [];
  return json
    .map((section) => {
      const title = `# ${section.subtitle}`;
      const content = section.text.replace(/<br>/g, '\n');
      return `${title}\n\n${content}`;
    })
    .join('\n\n');
}
