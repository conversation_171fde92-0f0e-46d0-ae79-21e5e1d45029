import useTabKeySearchParams from '@/hooks/useTabKeySearchParams';
import { ConfigProvider, Flex, Tabs, TabsProps } from 'antd';
import { useParams } from '@umijs/max';
import AuthMembers from './AuthMember';
import BasicInfo from './BasicInfo';
import QualityRule from './QualityRule';
import RemindSetting from './RemindSetting';
import TeamList from './TeamList';
import { ScriptLibrary } from './scriptLibrary';
import { AccountManager } from './AccountManager';
import { useRequest } from 'ahooks';
import { GetProjectInfo } from '@/services/project';
import AIToolsManager from './AIToolsManager';
import CustomManager from './CustomManager';
import styles from './index.module.less';

const PortalProjectEdit = () => {
  const [activeKey, onTabChange] = useTabKeySearchParams('basicInfo');
  const { projectId } = useParams();

  const { data: projectInfoRes } = useRequest(
    () => {
      if (!projectId) {
        return Promise.reject();
      }
      return GetProjectInfo({ projectId });
    },
    { refreshDeps: [projectId] },
  );

  const projectInfo = projectInfoRes?.data;

  const items: TabsProps['items'] = [
    {
      key: 'basicInfo',
      label: '基本信息',
      children: <BasicInfo projectInfo={projectInfo} />,
    },
    {
      key: 'authMembers',
      label: '授权成员',
      children: <AuthMembers />,
    },
    {
      key: 'teamList',
      label: '团队列表',
      children: <TeamList />,
    },
    {
      key: 'qualityInspect',
      label: '配置开关',
      children: <QualityRule />,
    },
    {
      key: 'remindSetting',
      label: '通知设置',
      children: <RemindSetting />,
    },
    {
      key: 'scriptLibrary',
      label: '脚本库',
      children: <ScriptLibrary />,
    },
    {
      key: 'accountManager',
      label: '云控账号',
      children: <AccountManager />,
    },
    {
      key: 'aiTools',
      label: 'AI工具',
      children: <AIToolsManager />,
    },
    {
      key: 'custom-people',
      label: '客服团队',
      children: <CustomManager />,
    },
  ];

  return (
    <ConfigProvider
      theme={{
        token: {
          colorPrimary: '#1E5EFF',
        },
        components: {
          Button: {
            colorPrimary: '#1E5EFF',
          },
          Radio: {
            buttonSolidCheckedBg: '#1E5EFF',
            buttonSolidCheckedActiveBg: '#1E5EFF',
            buttonSolidCheckedHoverBg: '#487bfc',
          },
          Switch: {
            colorPrimary: '#1E5EFF',
          },
          Input: {
            colorBorder: '#D3D5DA',
          },
          Select: {
            colorBorder: '#D3D5DA',
          },
          DatePicker: {
            colorBorder: '#D3D5DA',
          },
        },
      }}
    >
      <div className={styles.portal_project_edit_wrapper}>
        {/* 项目图标和名称 */}
        {projectInfo && (
          <Flex
            align="center"
            style={{ backgroundColor: '#fff', padding: '16px 16px 0 16px', borderRadius: 4 }}
          >
            <img src={projectInfo?.iconUrl} alt="" className="mr-[8px] h-[24px] w-[24px]" />
            <span style={{ fontSize: 18, color: '#0E1015', fontWeight: 500 }}>
              {projectInfo?.name}
            </span>
          </Flex>
        )}
        <Tabs activeKey={activeKey} onChange={onTabChange} items={items} destroyOnHidden />
      </div>
    </ConfigProvider>
  );
};

export default PortalProjectEdit;
