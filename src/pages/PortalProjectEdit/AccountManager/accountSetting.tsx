import { ProColumns, ProTable, ActionType, ProFormInstance } from '@ant-design/pro-components';
import { Button, Flex, message, Modal, Select, Space, Tooltip } from 'antd';
import { ReactNode, useEffect, useRef, useState } from 'react';
import '@/pages/ProjectHome/style/index.less';
import {
  SpiderAccountPageVO,
  deleteSpiderAccount,
  getSpiderAccounts,
  updateCaptureStatus,
} from '@/services/spider-account';
import { useParams } from '@umijs/max';
import DynamicTree from '@/components/dynamicTree';
import { QRCodeModal } from './qrCodeModal';
import useGetTeamFieldList from '@/hooks/useFields';
import { IPSelectModal } from './ipSelectModal';
import dayjs from 'dayjs';
import { renderCustomColumns } from '@/components/renderCustomColumns';
import { PlatForm } from '@/utils/platform';
import { PlusCircleOutlined } from '@ant-design/icons';
import { PLATFORM_MAP } from '@/pages/ProjectHome/Distribute/distributeTable';
import copy from 'copy-to-clipboard';

export const AccountSetting = ({
  setActiveKey,
  setDefaultAccountIdParams,
}: {
  setActiveKey: any;
  setDefaultAccountIdParams: any;
}) => {
  const formRef = useRef<ProFormInstance>();
  const selectedRowsRef = useRef<SpiderAccountPageVO[]>([]);
  const [formParams, setFormParams] = useState<{
    /** 平台：1抖音，4视频号，6小红书 */
    platform?: number;
    teamCodeList?: string[];
    authorizationtStatus?: number;
  }>({
    teamCodeList: [] as string[],
    platform: undefined,
    authorizationtStatus: undefined,
  });
  const { projectId } = useParams();
  const tableAction = useRef<ActionType>();
  const [customColumns, setCustomColumns] = useState<ProColumns<SpiderAccountPageVO>[]>([]);
  const teamFieldListRef = useGetTeamFieldList(projectId);
  const filedIdArrRef = useRef<string[]>([]);
  const provinceKey = useRef<string | number>();
  const cityKey = useRef<string | number>();
  const [show, setShow] = useState(false);

  const [isEmptyTable, setIsEmptyTable] = useState(false);

  const handleChangeCaptureFrequency = async (v: string, onCleanSelected: () => void) => {
    await updateCaptureStatus({
      accountIds: selectedRowsRef.current.map((item) => item.accountId),
      frequency: parseInt(v),
    });
    onCleanSelected();
    tableAction.current?.reload();
  };

  const defaultColumns: ProColumns<SpiderAccountPageVO>[] = [
    {
      title: '账号名称',
      dataIndex: 'nickname',
      align: 'left',
      search: false,
      className: 'col-150-text-ellipsis ',
      width: 200,
      render(_, rec) {
        return (
          <div className="flex items-center gap-2">
            <span title={rec.nickname}>{rec.nickname}</span>
            <Tooltip title="复制账号ID">
              <Button
                type="default"
                size="small"
                onClick={() => {
                  if (rec.accountId) {
                    copy(rec.accountId);
                    message.success('已复制到剪切板');
                  }
                }}
              >
                ID
              </Button>
            </Tooltip>
          </div>
        );
      },
    },
    {
      title: '平台',
      dataIndex: 'platform',
      search: false,
      width: 100,
      render(_, rec) {
        let platformName = rec.platform ? PLATFORM_MAP[rec.platform] : '';
        if (rec.platform == PlatForm.WXVideo) {
          platformName = rec.agencyFlag ? '视频号机构号' : '视频号助手';
        }
        return rec.platform ? <span title={platformName}>{platformName}</span> : '-';
      },
    },
    {
      title: '账号状态',
      width: 150,
      align: 'center',
      render(_, rec) {
        const AUTH_DICT: Record<number, ReactNode> = {
          1: <span>已授权</span>,
          2: (
            <span className="text-[#1E5EFF]" onClick={() => setShow(true)}>
              重新授权
            </span>
          ),
        };
        const expiredTime = dayjs(rec.authorizationtTime).add(15, 'day');
        const remainDays = expiredTime.diff(dayjs(), 'days');

        return (
          <div style={{ fontSize: 13 }}>
            {AUTH_DICT[rec.authorizationtStatus as number] || '-'}
            <br />
            {rec.authorizationtStatus === 1 && remainDays > 0 && `(预计${remainDays}天后失效)`}
          </div>
        );
      },
    },
    {
      title: '团队编码',
      dataIndex: 'teamCode',
      align: 'center',
      width: 100,
    },
    {
      title: '代理IP',
      align: 'center',
      width: 150,
      render: (_, rec) => {
        return (
          <span>
            {rec.proxy}-{rec.city}
          </span>
        );
      },
    },
    {
      title: '数据抓取频次',
      dataIndex: 'frequency',
      width: 160,
      align: 'center',
      valueEnum: {
        0: '禁止',
        1: '每小时',
        2: '每天',
      },
    },
    {
      title: '上次抓取',
      dataIndex: 'lastCaptureTime',
      align: 'center',
      width: 180,
      render(_, rec) {
        const STATUS_DICT: Record<number, string> = {
          0: '失败',
          1: '正常',
        };
        const lastCaptureTime = rec.lastCaptureTime;
        const mins = dayjs().diff(lastCaptureTime, 'minutes');
        const hours = dayjs().diff(lastCaptureTime, 'hours');
        const days = dayjs().diff(lastCaptureTime, 'days');
        let timeSpanStr = '-';
        if (hours >= 1) {
          timeSpanStr = `${hours}小时前`;
          if (hours >= 24) {
            timeSpanStr = `${days}天前`;
          }
        } else {
          timeSpanStr = `${mins}分钟前`;
        }

        return (
          <span>
            <Flex align="center" justify="center">
              {STATUS_DICT[rec.captureStatus as number] && (
                <div
                  style={{
                    width: 8,
                    height: 8,
                    borderRadius: 8,
                    backgroundColor: rec.captureStatus === 1 ? '#30B824' : '#FF3E3E',
                  }}
                />
              )}
              &nbsp;
              {STATUS_DICT[rec.captureStatus as number] || '-'}
              {STATUS_DICT[rec.captureStatus as number] && <span>({timeSpanStr})</span>}
            </Flex>
          </span>
        );
      },
    },
    {
      title: '修改时间',
      dataIndex: 'updateTime',
      valueType: 'dateTime',
      align: 'center',
      width: 200,
    },

    {
      title: '操作',
      key: 'option',
      width: 120,
      valueType: 'option',
      fixed: 'right',

      render: (_, rec) => [
        <a
          key="link"
          onClick={() => {
            setActiveKey('log-detail');
            setDefaultAccountIdParams(rec.accountId);
          }}
        >
          日志
        </a>,
        <a
          key="warn"
          onClick={async () => {
            Modal.confirm({
              title: '确认删除吗？',
              content: '确定删除该账号吗？',
              onOk: async () => {
                await deleteSpiderAccount([rec.accountId]);
                message.success('删除成功');
                tableAction.current?.reload();
              },
            });
          }}
        >
          删除
        </a>,
      ],
    },
  ];

  const columns: ProColumns<SpiderAccountPageVO>[] = [...defaultColumns, ...customColumns];

  const handleScanned = () => {
    message.success('授权成功');
    tableAction.current?.reload();
  };

  useEffect(() => {
    tableAction.current?.reset?.();
  }, [projectId]);

  return (
    <div>
      <ProTable<SpiderAccountPageVO>
        formRef={formRef}
        className="no-scrollbar"
        sticky={{ offsetHeader: -10 }}
        columns={columns}
        actionRef={tableAction}
        params={{ formParams, projectId }}
        pagination={{
          pageSizeOptions: [20, 50, 100],
          defaultPageSize: 20,
          showSizeChanger: true,
        }}
        request={async (params) => {
          if (!params.projectId) {
            return { success: false };
          }
          const res = await getSpiderAccounts({
            ...params.formParams,
            page: params.current,
            size: params.pageSize,
            projectId: params.projectId,
          });

          return {
            data: res?.data?.items,
            success: res?.code === 0,
            total: res?.data?.total,
            page: params.current,
            pageSize: params.pageSize,
          };
        }}
        postData={(data: SpiderAccountPageVO[]) => {
          // console.log(teamFieldListRef);
          data.length === 0 ? setIsEmptyTable(true) : setIsEmptyTable(false);

          if (data.length > 0) {
            renderCustomColumns(teamFieldListRef.current).then(({ customColumns, fieldIdArr }) => {
              filedIdArrRef.current = fieldIdArr;
              customColumns.forEach((item) => {
                if (item.title === '省份') {
                  provinceKey.current = item.dataIndex as number;
                }
                if (item.title === '城市') {
                  cityKey.current = item.dataIndex as number;
                }
              });
              setCustomColumns(customColumns as ProColumns<SpiderAccountPageVO>[]);
            });
          }
          const formatData = data.map((item) => {
            const teamFields = item.teamFields;
            const extraAttrs: Record<string, string | undefined> = {};
            teamFields?.forEach((item) => {
              extraAttrs[item.fieldId] = item.fieldValue;
            });
            return {
              ...item,
              ...extraAttrs,
            };
          });
          return formatData;
        }}
        scroll={{ x: isEmptyTable ? 1500 : 'max-content' }}
        toolbar={{
          title: (
            <Space>
              <DynamicTree
                style={{ width: 150 }}
                projectId={projectId}
                value={formParams.teamCodeList}
                setValue={(value) =>
                  // @ts-ignore
                  setFormParams((prev) => ({ ...prev, teamCodeList: value || [] }))
                }
              />

              <Select
                key="平台搜索"
                style={{ width: 150 }}
                options={[
                  { label: '抖音', value: 1 },
                  { label: '视频号', value: 4 },
                  { label: '小红书', value: 6 },
                ]}
                allowClear
                placeholder="平台搜索"
                onClear={() => {
                  setFormParams((prev) => ({ ...prev, platform: undefined }));
                }}
                onSelect={(v) => {
                  setFormParams((prev) => ({ ...prev, platform: v }));
                }}
              />

              <Select
                key="账号状态"
                options={[
                  { label: '已授权', value: 1 },
                  { label: '已失效', value: 2 },
                ]}
                allowClear
                onClear={() => {
                  setFormParams((prev) => ({ ...prev, authorizationtStatus: undefined }));
                }}
                onSelect={(v) => {
                  setFormParams((prev) => ({ ...prev, authorizationtStatus: v }));
                }}
                style={{ width: 150 }}
                placeholder="账号状态"
              />
            </Space>
          ),
          actions: [
            <Button
              type="primary"
              key="add"
              className="rounded bg-[#1E5Eff]"
              onClick={() => setShow(true)}
            >
              <PlusCircleOutlined />
              添加账号
            </Button>,
          ],
        }}
        rowSelection={{
          type: 'checkbox',
          onChange: (_, selectedRows) => {
            selectedRowsRef.current = selectedRows;
          },
        }}
        rowKey="accountId"
        tableAlertOptionRender={({ onCleanSelected }) => (
          <Space>
            <Button type="link">
              抓取频次&nbsp;
              <Select
                size="small"
                bordered={false}
                defaultValue="选择"
                style={{ width: 105 }}
                onSelect={(v) => handleChangeCaptureFrequency(v, onCleanSelected)}
                options={[
                  { value: 0, label: '禁止' },
                  { value: 1, label: '每小时' },
                  { value: 2, label: '每天' },
                ]}
              />
            </Button>
            <IPSelectModal
              selectingRows={selectedRowsRef.current}
              provinceKey={provinceKey.current}
              cityKey={cityKey.current}
              closeSelected={onCleanSelected}
              afterSubmit={() => tableAction.current?.reload()}
            />
            <Button
              type="link"
              onClick={() => {
                Modal.confirm({
                  title: '确认删除吗？',
                  content: '确定删除该账号吗？',
                  onOk: async () => {
                    await deleteSpiderAccount(
                      selectedRowsRef.current.map((item) => item.accountId),
                    );
                    message.success('删除成功');
                    tableAction.current?.reload();
                    onCleanSelected?.();
                  },
                });
              }}
            >
              删除
            </Button>
            <Button type="link" onClick={onCleanSelected}>
              关闭
            </Button>
          </Space>
        )}
        search={false}
      />

      <QRCodeModal key="add" onScannedSuccess={handleScanned} show={show} setShow={setShow} />
    </div>
  );
};
