import { Button, Flex, Input, InputRef, Modal, Spin } from 'antd';
import { Dispatch, useEffect, useRef, useState } from 'react';
import {
  AuthStatus,
  closeAuth,
  createQrCode,
  queryAuthStatus,
  requestCaptcha,
  sendCaptcha,
} from '@/services/spider-account';
import { useRequest } from 'ahooks';
import { RedoOutlined } from '@ant-design/icons';
import { PlatForm } from '@/utils/platform';
import IconDouYin from '@/assets/platformIcon/douyin.png';
import IconShiPinHao from '@/assets/platformIcon/wxvideo.png';
import IconXiaoHongShu from '@/assets/platformIcon/xiaohongshu.png';
import useProjectId from '@/hooks/useProjectId';
const WX_CHANNELS_OFFICIAL = 40;
const PLATFORM_LIST = [
  { label: '抖音', value: PlatForm.Douyin, icon: IconDouYin },
  { label: '视频号助手', value: PlatForm.WXVideo, icon: IconShiPinHao },
  { label: '视频号机构号', value: WX_CHANNELS_OFFICIAL, icon: IconShiPinHao },
  { label: '小红书', value: PlatForm.XiaoHongShu, icon: IconXiaoHongShu },
];
export const QRCodeModal = ({
  onScannedSuccess,
  show,
  setShow,
}: {
  onScannedSuccess?: () => void;
  show: boolean;
  setShow: Dispatch<React.SetStateAction<boolean>>;
}) => {
  const [platform, setPlatform] = useState<number>(1);
  const [captchaPhoneNumber, setCaptchaPhoneNumber] = useState<string | null>(null);
  const [requestCaptchaConfirm, setRequestCaptchaConfirm] = useState(false);
  const captchaInputInputRef = useRef<InputRef>(null);
  const qrCodeUuid = useRef<string>();
  const projectId = useProjectId();

  const {
    data: QRCodeRes,
    run: fetchQRCodeId,
    loading: loadingQRCode,
    mutate: setQRCodeRes,
  } = useRequest(
    () => {
      if (!projectId) {
        return Promise.resolve(undefined);
      }
      return createQrCode({
        platform: platform == WX_CHANNELS_OFFICIAL ? PlatForm.WXVideo : platform,
        isAgency: platform == WX_CHANNELS_OFFICIAL,
        projectId,
      });
    },
    {
      refreshDeps: [platform, projectId],
      manual: true,
      onSuccess(res) {
        qrCodeUuid.current = res?.data;
        setTimeout(startPollingAuthStatus, 0);
      },
    },
  );

  const {
    data: authStatusRes,
    run: startPollingAuthStatus,
    cancel: stopPollingAuthStatus,
    mutate: setAuthStatusRes,
  } = useRequest(
    () => {
      if (!qrCodeUuid.current) {
        return Promise.reject();
      }
      return queryAuthStatus({
        uuid: qrCodeUuid.current,
      });
    },
    {
      refreshDeps: [qrCodeUuid.current],
      pollingInterval: 1000,
      manual: true,
      onSuccess(res) {
        if (res.data?.status === AuthStatus.授权成功) {
          // 已授权
          stopPollingAuthStatus();
          onScannedSuccess?.();
          setTimeout(() => {
            setShow(false);
          }, 1000);
        }
        if (res.data?.status === AuthStatus.二维码过期) {
          // 已过期
          stopPollingAuthStatus();
        }
        if (res.data?.status == AuthStatus.需要验证码) {
          // 出现验证码 需要验证
          setCaptchaPhoneNumber(res.data?.phoneNumber || null);
        }
      },
    },
  );

  useEffect(() => {
    /** 关闭标签页时关闭授权 */
    const handleTabClose = (event: any) => {
      event.preventDefault();
      if (qrCodeUuid.current) {
        closeAuth({ uuid: qrCodeUuid.current });
      }
    };

    window.addEventListener('beforeunload', handleTabClose);

    return () => {
      window.removeEventListener('beforeunload', handleTabClose);
      if (qrCodeUuid.current) {
        closeAuth({ uuid: qrCodeUuid.current });
      }
    };
  }, []);

  const { run: sendCaptchaFn, loading: sendingCaptcha } = useRequest(sendCaptcha, { manual: true });

  const handleConfirmCaptcha = () => {
    const val = captchaInputInputRef.current?.input?.value;
    if (!val || !qrCodeUuid.current) {
      return;
    }

    sendCaptchaFn({ uuid: qrCodeUuid.current, code: val });
    setCaptchaPhoneNumber(null);
  };

  const handleRequestCaptcha = async () => {
    if (!qrCodeUuid.current) {
      return;
    }
    setRequestCaptchaConfirm(true);
    await requestCaptcha({ uuid: qrCodeUuid.current });
  };

  const captchaBox = (
    <Flex vertical align="center" className="h-full w-full">
      <span className="inline-block w-full text-center">
        为保证账号安全，请输入当前手机{captchaPhoneNumber}收到的验证码
      </span>
      <Input.Search
        className={`mt-3 w-[85%]`}
        placeholder="请输入验证码"
        onSearch={handleRequestCaptcha}
        enterButton={
          <Button disabled={requestCaptchaConfirm} type="primary">
            {requestCaptchaConfirm ? '短信已发送' : '获取验证码'}
          </Button>
        }
        ref={captchaInputInputRef}
        disabled={sendingCaptcha}
      />
      <Button className="mt-3 w-[85%]" loading={sendingCaptcha} onClick={handleConfirmCaptcha}>
        验证
      </Button>
    </Flex>
  );

  const qrCodeBox =
    // 二维码没请求成功 或者 发送了验证码后 展示loading
    QRCodeRes?.data ? (
      <div style={{ padding: '16px 12px', textAlign: 'center' }}>
        <Flex
          align="center"
          justify="center"
          className="relative left-[50%] translate-x-[-50%] text-center"
          style={{
            width: 250,
            height: 250,
            backgroundColor:
              !authStatusRes?.data?.qrCode ||
              [
                AuthStatus.需要验证码,
                AuthStatus.正在请求验证码,
                AuthStatus.正在验证验证码,
              ].includes(authStatusRes.data.status)
                ? '#00000013'
                : 'transparent',
          }}
        >
          <>
            {([AuthStatus.二维码过期, AuthStatus.授权关闭].includes(
              authStatusRes?.data?.status || AuthStatus.授权失败,
            ) ||
              authStatusRes?.data?.status == '') && (
              <Flex
                onClick={() => {
                  setRequestCaptchaConfirm(false);
                  setCaptchaPhoneNumber(null);
                  setQRCodeRes(undefined);
                  setAuthStatusRes(undefined);
                  fetchQRCodeId();
                }}
                className="z-9 absolute cursor-pointer bg-[#000] text-[#fff]"
              >
                {authStatusRes?.data?.status == AuthStatus.授权失败 && '授权超时 点击刷新'}
                {authStatusRes?.data?.status == AuthStatus.二维码过期 && '二维码已过期 点击刷新'}
                {authStatusRes?.data?.status == AuthStatus.授权关闭 && '授权关闭 点击刷新'}
                <RedoOutlined />
              </Flex>
            )}
            {authStatusRes?.data?.qrCode && (
              <img
                src={authStatusRes?.data?.qrCode}
                alt=""
                style={{
                  width: 250,
                  height: 250,
                  // 输入验证码后 二维码变灰一点
                  opacity: [
                    AuthStatus.需要验证码,
                    AuthStatus.正在请求验证码,
                    AuthStatus.正在验证验证码,
                  ].includes(authStatusRes.data.status)
                    ? 0.5
                    : 1,
                  background: 'black',
                }}
              />
            )}
          </>
          {(!authStatusRes?.data?.qrCode ||
            authStatusRes.data.status == AuthStatus.正在验证验证码) && (
            <Spin
              spinning={true}
              className="absolute left-[50%] top-[50%] translate-x-[-50%] translate-y-[-50%]"
            />
          )}
        </Flex>
      </div>
    ) : (
      <Spin
        spinning={
          loadingQRCode ||
          (authStatusRes?.data?.status === AuthStatus.二维码获取中 && !authStatusRes?.data.qrCode)
        }
      >
        <Flex justify="space-between" className="no-scrollbar w-full overflow-auto px-0 py-4">
          {PLATFORM_LIST.map((item) => (
            <Flex
              key={item.label}
              onClick={() => setPlatform(item.value)}
              align="center"
              justify="center"
              className="mr-3 h-[52px] min-w-[100px] flex-shrink-0 rounded-[8px] px-2 text-center"
              style={{
                border: `1px solid ${platform === item.value ? '#1E5EFF' : '#DBDBDB'}`,
                backgroundColor: platform === item.value ? '#1E5EFF1A' : '#fff',
              }}
            >
              <img
                src={item.icon}
                alt="icon"
                width={36}
                height={36}
                style={{ marginRight: 8, pointerEvents: 'none' }}
              />
              <span>{item.label}</span>
            </Flex>
          ))}
        </Flex>
      </Spin>
    );

  return (
    <>
      <Modal
        title="平台授权"
        width={468}
        maskClosable={false}
        afterClose={() => {
          stopPollingAuthStatus();
          if (qrCodeUuid.current) {
            closeAuth({ uuid: qrCodeUuid.current });
          }
          setRequestCaptchaConfirm(false);
          setCaptchaPhoneNumber(null);
          setQRCodeRes(undefined);
          setAuthStatusRes(undefined);
        }}
        open={show}
        onOk={() => fetchQRCodeId()}
        destroyOnHidden
        okButtonProps={{
          disabled: loadingQRCode,
        }}
        onCancel={() => {
          setShow(false);
        }}
        footer={captchaPhoneNumber || QRCodeRes?.data ? false : undefined}
      >
        {/* 出现验证码 隐藏二维码框 展示验证码框 */}
        {captchaPhoneNumber ? captchaBox : qrCodeBox}
      </Modal>
    </>
  );
};
