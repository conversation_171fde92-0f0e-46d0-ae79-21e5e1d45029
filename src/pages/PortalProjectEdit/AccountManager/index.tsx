import { Tabs, TabsProps } from 'antd';
import { AccountSetting } from './accountSetting';
import { ProxySetting } from './proxySetting';
import { LogDetail } from './logDetail';
import useTabKeySearchParams from '@/hooks/useTabKeySearchParams';
import { useState } from 'react';
import styles from './index.module.less';

export const AccountManager = () => {
  const [activeKey, onTabChange] = useTabKeySearchParams('account-setting', 'subActiveKey');
  const [defaultAccountIdParams, setDefaultAccountIdParams] = useState();

  const items: TabsProps['items'] = [
    {
      key: 'account-setting',
      label: '账号设置',
      children: (
        <AccountSetting
          setActiveKey={onTabChange}
          setDefaultAccountIdParams={setDefaultAccountIdParams}
        />
      ),
    },
    {
      key: 'proxy-setting',
      label: '代理设置',
      children: <ProxySetting />,
    },
    {
      key: 'log-detail',
      label: '日志详情',
      children: <LogDetail accountId={defaultAccountIdParams} />,
      destroyInactiveTabPane: true,
    },
  ];
  return (
    <div className={styles.account_manager_wrapper}>
      <Tabs
        activeKey={activeKey}
        items={items}
        onChange={(key) => {
          if (defaultAccountIdParams) {
            setDefaultAccountIdParams(undefined);
          }
          onTabChange(key);
        }}
      />
    </div>
  );
};
