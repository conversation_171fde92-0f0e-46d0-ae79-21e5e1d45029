.account_manager_wrapper {
  background-color: #fff;
  border-radius: 4px;
  min-height: calc(100vh - 186px);
  :global {
    .ant-tabs .ant-tabs-content-holder {
      padding: 0 16px 16px 16px;
    }
    .ant-tabs > .ant-tabs-nav {
      position: relative;
      &::after {
        width: calc(100% - 32px);
        height: 1.5px;
        background-color: #ebedf2;
        display: block;
        content: '';
        position: absolute;
        bottom: -10px;
        left: 16px;
      }
    }
    .ant-tabs .ant-tabs-tab {
      color: #64666b;
      padding: 0;
      margin-top: 15px;
    }

    .ant-tabs .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
      color: #ffffff !important;
      background-color: #1e5eff;
      border-radius: 4px;
      padding: 0 4px;
      height: 24px;
      line-height: 24px;
    }
    .ant-tabs-top > .ant-tabs-nav .ant-tabs-ink-bar {
      display: none;
    }
    .ant-pro-table-list-toolbar-container {
      .ant-select:not(.ant-select-customize-input) .ant-select-selector,
      .ant-select-single .ant-select-selector {
        border-radius: 2px;
        border-color: #ebedf2;
      }
    }
    .ant-pro-table-list-toolbar-right {
      div:nth-child(1) {
        order: 99;
      }
    }
    .ant-table-thead > tr > th {
      padding: 7px 12px;
      background-color: #f5f6fa;
    }
    .ant-table-wrapper .ant-table.ant-table-middle .ant-table-thead > tr > th {
      padding: 7px 12px;
    }
    .ant-pro-card .ant-pro-card-body {
      padding: 0;
    }
    .ant-pro-table-alert-container {
      padding-block: 5px;
      background-color: #fbfcfe;
    }
    .ant-pro-table-alert-info .ant-select-show-arrow .ant-select-selection-item {
      color: #1677ff;
    }

    .ant-pagination-options {
      order: -1;
    }
    .ant-pagination .ant-select-outlined:not(.ant-select-customize-input) .ant-select-selector {
      background-color: #f5f6fa;
      border-color: #ebedf2;
      height: 24px;
    }

    .ant-pagination .ant-pagination-item-active {
      width: 24px;
      height: 24px;
      line-height: 24px;
      background-color: #1e5eff;
      margin-right: 8px;
    }
    .ant-pagination .ant-pagination-item-active a {
      color: #fff;
    }
    .ant-pagination.ant-pagination-mini:not(.ant-pagination-disabled)
      .ant-pagination-item:not(.ant-pagination-item-active) {
      background-color: #f5f6fa;
      border-color: #ebedf2;
      margin-right: 8px;
    }
    .ant-table-wrapper .ant-table-pagination.ant-pagination {
      margin: 24px 0 16px 0;
    }
    .ant-pagination.ant-pagination-mini .ant-pagination-total-text {
      /* display: none; */
      order: -2;
    }

    .ant-input-outlined {
      background-color: #fff;
    }
    .ant-input-affix-wrapper {
      border-radius: 2px !important;
    }
    .ant-input-search
      > .ant-input-group
      > .ant-input-group-addon:last-child
      .ant-input-search-button {
      border-radius: 2px !important;
      background-color: #fff;
      color: #000;
      width: 30px;
      display: flex;
      justify-content: center;
      align-items: center;
      border: 1px solid #ebedf2;
    }
    .ant-btn-link,
    a {
      color: #1e5eff;
    }
    .ant-table-sticky-scroll-bar {
      display: none;
    }
  }
}
