import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { Space, Select, Input, Flex } from 'antd';
import { getSpiderLog, SpiderLogPageVO } from '@/services/spider-account';
import { useParams } from '@umijs/max';
import { useEffect, useRef, useState } from 'react';
import { PlatForm } from '@/utils/platform';
import { PLATFORM_MAP } from '@/pages/ProjectHome/Distribute/distributeTable';

const columns: ProColumns<SpiderLogPageVO>[] = [
  {
    title: '账号名称',
    dataIndex: 'nickname',
    align: 'center',
    search: false,
  },
  {
    title: '平台',
    dataIndex: 'platform',
    align: 'center',
    search: false,
    render(_, rec) {
      let platformName = rec.platform ? PLATFORM_MAP[rec.platform] : '';
      if (rec.platform == PlatForm.WXVideo) {
        platformName = rec.agencyFlag ? '视频号机构号' : '视频号助手';
      }
      return rec.platform ? <span title={platformName}>{platformName}</span> : '-';
    },
  },
  {
    title: '执行时间',
    dataIndex: 'taskStartTime',
    align: 'center',
    valueType: 'dateTime',
  },
  {
    title: '执行状态',
    align: 'center',
    dataIndex: 'status',
    valueEnum: {
      0: '待执行',
      1: '执行中',
      2: '执行完成',
    },
    width: 80,
  },
  {
    align: 'center',
    title: '类型',
    render: () => '数据抓取',
  },
  {
    title: '执行结果',
    align: 'center',
    width: 80,
    valueEnum: {
      1: '成功',
      0: '失败',
    },
    render(_, rec) {
      const STATUS_DICT: Record<number, string> = {
        1: '成功',
        0: '失败',
      };
      return STATUS_DICT[rec.resultStatus as number] ? (
        <Flex align="center" justify="center">
          <div
            style={{
              width: 8,
              height: 8,
              borderRadius: 8,
              backgroundColor: rec.resultStatus === 1 ? '#30B824' : '#FF3E3E',
            }}
          />
          &nbsp;
          <span>{STATUS_DICT[rec.resultStatus as number] || '-'}</span>
        </Flex>
      ) : (
        '-'
      );
    },
  },
];

export const LogDetail = ({ accountId }: { accountId?: string }) => {
  const { projectId } = useParams();
  const tableAction = useRef<ActionType>();
  const [formParams, setFormParams] = useState<{
    /** 平台：1抖音，5视频号，6小红书 */
    platform?: number;
    /** 账号名称 */
    nickname?: string;
    /** 账号id */
    accountId?: string;
  }>({ platform: undefined, nickname: undefined, accountId });

  useEffect(() => {
    setFormParams((prev) => ({ ...prev, accountId }));
  }, [accountId]);

  useEffect(() => {
    tableAction.current?.reset?.();
  }, [projectId]);

  return (
    <>
      <ProTable<SpiderLogPageVO>
        columns={columns}
        sticky={{ offsetHeader: -10 }}
        actionRef={tableAction}
        params={{ projectId, formParams }}
        request={async (params) => {
          if (!params.projectId) {
            return { success: false };
          }
          console.log(params);

          const res = await getSpiderLog({
            ...params.formParams,
            page: params.current,
            size: params.pageSize,
            projectId: params.projectId,
          });
          return {
            data: res.data?.items,
            success: res?.code === 0,
            total: res.data?.total,
            page: params.current,
            pageSize: params.pageSize,
          };
        }}
        rowKey="id"
        pagination={{
          pageSizeOptions: [20, 50, 100],
          defaultPageSize: 20,
          showSizeChanger: true,
        }}
        scroll={{ x: 'max-content' }}
        toolbar={{
          title: (
            <Space key="tool">
              <Select
                key="平台搜索"
                style={{ width: 150 }}
                options={[
                  { label: '抖音', value: 1 },
                  { label: '视频号', value: 4 },
                  { label: '小红书', value: 6 },
                ]}
                allowClear
                placeholder="平台搜索"
                onClear={() => {
                  setFormParams((prev) => ({ ...prev, platform: undefined }));
                }}
                onSelect={(v) => {
                  setFormParams((prev) => ({ ...prev, platform: v }));
                }}
              />

              <Input.Search
                style={{ width: 180 }}
                allowClear
                onSearch={(v) => {
                  setFormParams((prev) => ({ ...prev, nickname: v }));
                }}
                onChange={(v) => {
                  if (!v) {
                    setFormParams((prev) => ({ ...prev, nickname: undefined }));
                  }
                }}
                placeholder="账号名称"
              />

              <Input.Search
                key={accountId}
                style={{ width: 180 }}
                defaultValue={accountId}
                allowClear
                onSearch={(v) => {
                  setFormParams((prev) => ({ ...prev, accountId: v }));
                }}
                onChange={(v) => {
                  if (!v) {
                    setFormParams((prev) => ({ ...prev, accountId: undefined }));
                  }
                }}
                placeholder="账号id"
              />
            </Space>
          ),
        }}
        search={false}
      />
    </>
  );
};
