import { PlatForm } from '@/utils/platform';
import { get<PERSON>ro<PERSON><PERSON><PERSON>, SpiderAccountPageVO, updateAccountProxy } from '@/services/spider-account';
import {
  ModalForm,
  ProFormDependency,
  ProFormRadio,
  ProFormSelect,
} from '@ant-design/pro-components';
import { useParams } from '@umijs/max';
import { Button, Form, message } from 'antd';

export const IPSelectModal = ({
  selectingRows,
  provinceKey,
  cityKey,
  closeSelected,
  afterSubmit,
}: {
  selectingRows: SpiderAccountPageVO[];
  provinceKey?: number | string;
  cityKey?: number | string;
  closeSelected: () => void;
  afterSubmit?: () => void;
}) => {
  const [form] = Form.useForm<{
    rule: 1 | 0;
    proxyId: string;
  }>();
  const { projectId } = useParams();

  return (
    <>
      <ModalForm<{
        rule: 1 | 0;
        proxyId: string;
      }>
        title="修改代理IP"
        trigger={<Button type="link">修改代理IP</Button>}
        form={form}
        autoFocusFirstInput
        modalProps={{
          destroyOnHidden: true,
          maskClosable: false,
        }}
        width={468}
        submitTimeout={2000}
        onFinish={async (values) => {
          if (!projectId) {
            return;
          }
          if (values.rule === undefined) {
            return true;
          }
          if (values.rule === 1 && !values.proxyId) {
            message.error('请选择代理IP');
            return;
          }
          if (selectingRows.some((item) => item.platform == PlatForm.WXVideo)) {
            message.error('微信视频号不支持修改代理IP');
            return;
          }
          await updateAccountProxy({
            rule: values.rule,
            proxyId: values.proxyId,
            projectId: projectId,
            accountInfos: selectingRows.map((item) => ({
              accountId: item.accountId,
              city: item[cityKey as keyof SpiderAccountPageVO] as string | undefined,
              province: item[provinceKey as keyof SpiderAccountPageVO] as string | undefined,
            })),
          });
          closeSelected?.();
          afterSubmit?.();
          message.success('提交成功');
          return true;
        }}
        layout="horizontal"
        labelCol={{ span: 4 }}
        colon={false}
      >
        <ProFormRadio.Group
          name="rule"
          label="代理IP"
          options={[
            {
              label: '自动匹配',
              value: 0,
            },
            {
              label: '自定义',
              value: 1,
            },
          ]}
        />

        <ProFormDependency name={['rule']}>
          {({ rule }) => {
            if (rule === 1) {
              return (
                <ProFormSelect
                  label=" "
                  width="md"
                  name="proxyId"
                  request={async () => {
                    if (!projectId) {
                      return [];
                    }
                    const res = await getProxyList({
                      page: 1,
                      size: 999999,
                      projectId: projectId,
                    });
                    return (
                      res?.data?.items?.map((item) => ({
                        label: `${item.proxy}-${item.city}`,
                        value: item.id,
                      })) || []
                    );
                  }}
                />
              );
            }
          }}
        </ProFormDependency>
      </ModalForm>
    </>
  );
};
