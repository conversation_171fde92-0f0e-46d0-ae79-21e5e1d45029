import {
  deleteUsingIP,
  downloadProxyTemplate,
  exportProxy,
  getProxyList,
  SpiderProxyVO,
} from '@/services/spider-account';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { useParams } from '@umijs/max';
import { Space, Button, message, Upload, UploadProps, Modal } from 'antd';
import dayjs from 'dayjs';
import saveAs from 'file-saver';
import { useEffect, useRef, useState } from 'react';
import { IPSettingModal } from './ipSettingModal';
const PROXY_UPLOAD_MSG_KEY = 'upload-proxy-msg';
export const ProxySetting = () => {
  const [show, setShow] = useState(false);
  const [editingRow, setEditingRow] = useState<SpiderProxyVO>();
  const selectedRowsRef = useRef<SpiderProxyVO[]>([]);
  const tableAction = useRef<ActionType>();
  const { projectId } = useParams();

  const props: UploadProps = {
    name: 'file',
    accept: '.xlsx,.xls',
    action: '/new-media-api/spider-proxy/import',
    headers: {
      contentType: 'multipart/form-data',
    },
    showUploadList: false,
    data: {
      projectId,
    },
    // @ts-ignore
    onSuccess(res) {
      if (res.code == 0) {
        message.success({
          key: PROXY_UPLOAD_MSG_KEY,
          content: '导入成功',
        });
        tableAction.current?.reload();
      } else {
        message.error({
          key: PROXY_UPLOAD_MSG_KEY,
          content: res.msg || '文件导入失败',
        });
      }
    },
    onError() {
      message.error({
        key: PROXY_UPLOAD_MSG_KEY,
        content: '文件上传失败',
      });
    },
    onChange(info) {
      if (info.file.status !== 'uploading') {
        message.loading({
          key: PROXY_UPLOAD_MSG_KEY,
          content: '文件上传中',
        });
      }
      if (info.file.status === 'done') {
        message.loading({
          key: PROXY_UPLOAD_MSG_KEY,
          content: '文件导入中',
        });
      } else if (info.file.status === 'error') {
        message.error({
          key: PROXY_UPLOAD_MSG_KEY,
          content: '文件上传失败',
        });
      }
    },
  };

  const columns: ProColumns<SpiderProxyVO>[] = [
    {
      title: '代理IP',
      dataIndex: 'proxy',
      search: false,
      align: 'center',
    },
    {
      title: '代理地址',
      dataIndex: 'city',
      align: 'center',
      search: false,
      width: 110,
    },
    {
      title: 'IP类型',
      align: 'center',
      dataIndex: 'type',
      width: 80,
      valueEnum: {
        1: '独享',
        2: '共享',
      },
    },
    {
      align: 'center',
      title: '已分配账号数',
      dataIndex: 'accountCount',
      width: 110,
    },
    {
      title: '到期时间',
      align: 'center',
      dataIndex: 'expiresTime',
      valueType: 'dateTime',
    },
    {
      title: '操作',
      align: 'center',
      key: 'option',
      valueType: 'option',
      fixed: 'right',
      render: (_, rec) => [
        <Button
          type="link"
          key="show-edit"
          onClick={() => {
            setEditingRow(rec);
            setShow(true);
          }}
        >
          修改代理IP
        </Button>,
        <a
          key="warn"
          onClick={async () => {
            Modal.confirm({
              title: '确认删除吗？',
              content: '确定删除该IP吗？',
              onOk: async () => {
                if (!projectId) {
                  return;
                }
                await deleteUsingIP({ projectId, ids: [rec.id] });
                message.success('删除成功');
                tableAction.current?.reload();
              },
            });
          }}
        >
          删除
        </a>,
      ],
    },
  ];

  useEffect(() => {
    tableAction.current?.reset?.();
  }, [projectId]);

  return (
    <>
      <ProTable<SpiderProxyVO>
        columns={columns}
        sticky={{ offsetHeader: -10 }}
        params={{ projectId }}
        request={async (params, sorter, filter) => {
          if (!params.projectId) {
            return { success: false };
          }
          const res = await getProxyList({
            page: params.current,
            size: params.pageSize,
            projectId: params.projectId,
          });
          console.log(params, sorter, filter);
          return {
            data: res.data?.items,
            success: res?.code === 0,
            total: res.data?.total,
            page: params.current,
            pageSize: params.pageSize,
          };
        }}
        actionRef={tableAction}
        pagination={{
          pageSizeOptions: [20, 50, 100],
          defaultPageSize: 20,
          showSizeChanger: true,
        }}
        scroll={{ x: 'max-content' }}
        toolbar={{
          title: (
            <Space>
              <Upload {...props}>
                <Button type="default">批量导入数据</Button>
              </Upload>
              <Button
                type="default"
                onClick={async () => {
                  const resBlob = await downloadProxyTemplate({ projectId });
                  saveAs(resBlob, `${dayjs().format('导入模版 YYYYMMDDHHMM')}.xls`);
                }}
              >
                下载模版
              </Button>
            </Space>
          ),
          actions: [
            <Button
              key="primary"
              type="primary"
              onClick={async () => {
                if (!projectId) {
                  return;
                }
                const resBlob: any = await exportProxy({ projectId });
                saveAs(resBlob, `${dayjs().format('YYYYMMDDHHMMss')}.xls`);
              }}
            >
              导出所有
            </Button>,
          ],
        }}
        rowKey="id"
        rowSelection={{
          type: 'checkbox',
          onChange: (_, selectedRows) => {
            selectedRowsRef.current = selectedRows;
            console.log(selectedRows);
          },
        }}
        tableAlertOptionRender={({ onCleanSelected }) => (
          <Space>
            <Button
              type="link"
              onClick={() => {
                Modal.confirm({
                  title: '确认删除吗？',
                  content: '确定删除该IP吗？',
                  onOk: async () => {
                    if (!projectId) {
                      return;
                    }

                    await deleteUsingIP({
                      projectId,
                      ids: selectedRowsRef.current.map((item) => item.id),
                    });
                    message.success('删除成功');
                    tableAction.current?.reload();
                  },
                });
                tableAction.current?.reload();
              }}
            >
              删除
            </Button>
            <Button type="link" onClick={onCleanSelected}>
              关闭
            </Button>
          </Space>
        )}
        search={false}
      />
      <IPSettingModal
        initValue={editingRow}
        show={show}
        setShow={setShow}
        afterSubmit={() => tableAction.current?.reload()}
      />
    </>
  );
};
