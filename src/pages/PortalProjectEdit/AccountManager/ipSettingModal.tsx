import { SpiderProxyDTO, SpiderProxyVO, updateProxy } from '@/services/spider-account';
import {
  ModalForm,
  ProFormCascader,
  ProFormDatePicker,
  ProFormInstance,
  ProFormItem,
  ProFormSelect,
} from '@ant-design/pro-components';
import { useParams } from '@umijs/max';
import { Flex, Form, InputNumber, message } from 'antd';
import dayjs from 'dayjs';
import { provinceAndCityData } from 'element-china-area-data';
import { useEffect, useMemo, useRef } from 'react';

type IUpdateProxyForm = Omit<SpiderProxyDTO, 'id'>;
export const IPSettingModal = ({
  show,
  setShow,
  initValue,
  afterSubmit,
}: {
  show: boolean;
  setShow: any;
  initValue?: SpiderProxyVO;
  afterSubmit: () => void;
}) => {
  const [form] = Form.useForm<IUpdateProxyForm>();
  const formRef = useRef<ProFormInstance>();
  const { projectId } = useParams();

  const provinceAndCity = useMemo(() => {
    const municipality = ['北京市', '天津市', '上海市', '重庆市'];
    return provinceAndCityData.map((item) => {
      return {
        label: item.label,
        value: item.label,
        children: municipality.includes(item.label)
          ? []
          : item.children?.map((child) => {
              return {
                label: child.label,
                value: child.label,
              };
            }),
      };
    });
  }, []);

  useEffect(() => {
    if (initValue || show) {
      form.setFieldsValue(initValue || {});
    }
  }, [initValue, show]);

  return (
    <>
      <ModalForm<IUpdateProxyForm>
        title="修改代理IP"
        form={form}
        formRef={formRef}
        autoFocusFirstInput
        modalProps={{
          destroyOnHidden: true,
          maskClosable: false,
        }}
        open={show}
        onOpenChange={setShow}
        width={468}
        style={{ paddingRight: 20, paddingTop: 20 }}
        submitTimeout={2000}
        onFinish={async (values) => {
          if (!projectId || !initValue?.id) {
            message.error('缺少参数');
            return;
          }

          let city, province;
          if (Array.isArray(values.city) && values.city.length === 2) {
            city = values.city[1] || values.city[0];
            province = values.city[0];
          } else if (Array.isArray(values.city) && values.city.length === 1) {
            city = values.city[0];
            province = values.city[0];
          } else {
            city = values.city || initValue?.city;
            province = initValue.province;
          }

          await updateProxy({
            id: initValue?.id,
            projectId,
            proxy: values?.proxy || initValue?.proxy,
            type: values?.type || initValue?.type,
            city,
            province,
            expiresTime:
              dayjs(values?.expiresTime)?.format('YYYY-MM-DD HH:mm:ss') || initValue?.expiresTime,
          });

          afterSubmit?.();
          message.success('提交成功');
          return true;
        }}
        layout="horizontal"
        labelCol={{ span: 6, offset: 0 }}
        wrapperCol={{ span: 16 }}
      >
        <ProFormItem
          name="proxy"
          label="代理IP"
          required
          rules={[{ required: true, message: '这是必填项' }]}
        >
          <IPInputItem />
        </ProFormItem>

        <ProFormCascader
          name="city"
          key={initValue?.city}
          label="代理地址"
          required
          fieldProps={{ options: provinceAndCity, changeOnSelect: true }}
          rules={[{ required: true, message: '这是必填项' }]}
        />

        <ProFormSelect
          name="type"
          key={initValue?.type}
          label="IP类型"
          required
          rules={[{ required: true, message: '这是必填项' }]}
          options={[
            {
              label: '独享',
              value: 1,
            },
            {
              label: '共享',
              value: 2,
            },
          ]}
        />

        <ProFormDatePicker
          required
          key={initValue?.expiresTime}
          style={{ width: '100%' }}
          rules={[{ required: true, message: '这是必填项' }]}
          name="expiresTime"
          label="到期时间"
        />
      </ModalForm>
    </>
  );
};

interface IPInputItemProps {
  value?: string;
  onChange?: (v: string) => void;
}
const IPInputItem = ({ value, onChange }: IPInputItemProps) => {
  const input1 = useRef<HTMLInputElement>(null);
  const input2 = useRef<HTMLInputElement>(null);
  const input3 = useRef<HTMLInputElement>(null);
  const input4 = useRef<HTMLInputElement>(null);
  const input5 = useRef<HTMLInputElement>(null);

  const initValues = value?.split(':');
  initValues?.splice(0, 1, ...initValues[0].split('.'));

  const handleBlur = () => {
    if (
      input1.current?.value &&
      input2.current?.value &&
      input3.current?.value &&
      input4.current?.value
    ) {
      const ipStr = `${input1.current?.value || ''}.${input2.current?.value || ''}.${
        input3.current?.value || ''
      }.${input4.current?.value || ''}:${input5.current?.value || ''}`;
      console.log(ipStr);

      onChange?.(ipStr);
    }
  };

  return (
    <Flex align="end">
      <InputNumber
        ref={input1}
        controls={false}
        size="small"
        style={{ width: 40 }}
        defaultValue={initValues?.[0]}
        onChange={() => console.log(input1.current)}
        maxLength={3}
        onBlur={handleBlur}
      />
      <span>&nbsp;.&nbsp;</span>
      <InputNumber
        size="small"
        style={{ width: 40 }}
        ref={input2}
        defaultValue={initValues?.[1]}
        controls={false}
        onBlur={handleBlur}
        maxLength={3}
      />
      <span>&nbsp;.&nbsp;</span>
      <InputNumber
        size="small"
        ref={input3}
        style={{ width: 40 }}
        defaultValue={initValues?.[2]}
        onBlur={handleBlur}
        controls={false}
        maxLength={3}
      />
      <span>&nbsp;.&nbsp;</span>
      <InputNumber
        style={{ width: 40 }}
        size="small"
        ref={input4}
        onBlur={handleBlur}
        defaultValue={initValues?.[3]}
        controls={false}
        maxLength={3}
      />
      <span>&nbsp;:&nbsp;</span>
      <InputNumber
        style={{ width: 60 }}
        size="small"
        placeholder="端口"
        ref={input5}
        onBlur={handleBlur}
        defaultValue={initValues?.[4]}
        controls={false}
        maxLength={5}
      />
    </Flex>
  );
};
