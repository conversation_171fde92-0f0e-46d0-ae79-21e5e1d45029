import {
  CheckNotifyRobotBind,
  CheckNotifyRobotBindRes,
  DeleteBindRobot,
  DownloadNotifyRobotTemplate,
  ExportNotifyRobotBind,
  GetNotifyRobotBindStatus,
  GetNotifyRobotBindStatusRes,
  GetRobotBindRelation,
  ImportBindRobot,
  RobotBindRelationItem,
  UpdateNotifyRobotBind,
} from '@/services/notify';
import { proTableRequestAdapter, reloadTable } from '@/utils';
import { InboxOutlined } from '@ant-design/icons';
import {
  ActionType,
  ModalForm,
  ProColumns,
  ProFormText,
  ProTable,
} from '@ant-design/pro-components';
import { useAsyncEffect } from 'ahooks';
import { Button, Flex, message, Modal, Popconfirm, Space, Upload, UploadProps } from 'antd';
import saveAs from 'file-saver';
import { isUndefined } from 'lodash-es';
import { useRef, useState } from 'react';

type TeamRemindProps = {
  projectId?: string;
};

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 12 },
};

type CheckNotifyTableItem = {
  /**
   * 好友状态 1-已添加 2-未添加 3-已删除
   */
  friendStatus?: number;
  /**
   * 添加类型 1-新增 2-切换
   */
  importStatus?: number;
  /**
   * 团队识别码
   */
  teamIdentificationCode?: string;
  /**
   * 被提醒微信号
   */
  teamWechatId?: string;
};

const checkTableColumns: ProColumns[] = [
  {
    title: '团队识别码',
    dataIndex: 'teamIdentificationCode',
    align: 'center',
    hideInSearch: true,
  },
  {
    title: '被提醒微信号',
    dataIndex: 'teamWechatId',
    align: 'center',
    hideInSearch: true,
  },
  {
    title: '好友状态',
    dataIndex: 'friendStatus',
    align: 'center',
    valueEnum: {
      '1': '已添加',
      '2': '未添加',
      '3': '已删除',
    },
    valueType: 'select',
  },
  // {
  //   title: '添加类型',
  //   dataIndex: 'importStatus',
  //   align: 'center',
  //   valueEnum: {
  //     '1': '新增',
  //     '2': '切换',
  //   },
  //   valueType: 'select',
  // },
];

const { Dragger } = Upload;

type CheckEditTableType = {
  id?: string;
  projectId?: string;
  teamIdentificationCode: string;
  teamWechatId: string;
};

const TeamRemind = (props: TeamRemindProps) => {
  const { projectId } = props;
  const actionRef = useRef<ActionType>();
  const [checkImportData, setCheckImportData] = useState<boolean>(false);
  const [tempFile, setTempFile] = useState<File | Blob | null>(null);
  const [checkTableData, setCheckTableData] = useState<CheckNotifyTableItem[]>([]);
  const [checkTableDataShow, setCheckTableDataShow] = useState<CheckNotifyTableItem[]>([]);
  const [checkEditModalOpen, setCheckEditModalOpen] = useState(false);
  const [checkEditTableParams, setCheckEditTableParams] = useState<CheckEditTableType | null>();
  const [checkEditTableData, setCheckEditTableData] = useState<CheckNotifyRobotBindRes[]>([]);
  const [bindStatus, setBindStatus] = useState<GetNotifyRobotBindStatusRes>({});

  useAsyncEffect(async () => {
    if (checkEditModalOpen) {
      const res = await CheckNotifyRobotBind(checkEditTableParams);
      res.data && setCheckEditTableData([res.data]);
    }
  }, [checkEditTableParams, checkEditModalOpen]);

  useAsyncEffect(async () => {
    const res = await GetNotifyRobotBindStatus({ projectId });
    res.data && setBindStatus(res.data);
  }, []);

  const checkEditColumns: ProColumns<CheckNotifyRobotBindRes>[] = [
    {
      title: '微信号',
      dataIndex: 'teamWechatId',
    },
    {
      title: '团队识别码',
      dataIndex: 'teamIdentificationCode',
    },
    {
      title: '好友状态',
      dataIndex: 'friendStatus',
      valueEnum: {
        '1': '已添加',
        '2': '未添加',
        '3': '已删除',
      },
    },
    // {
    //   title: '添加类型',
    //   dataIndex: 'importStatus',
    //   valueEnum: {
    //     '1': '新增',
    //     '2': '切换',
    //   },
    // },
  ];

  const columns: ProColumns<RobotBindRelationItem>[] = [
    {
      dataIndex: 'id',
      hidden: true,
    },
    {
      title: '团队识别码',
      dataIndex: 'teamIdentificationCode',
      fixed: 'left',
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
    },
    {
      title: '被提醒微信号',
      dataIndex: 'teamWechatId',
    },
    {
      title: '好友状态',
      dataIndex: 'friendStatus',
      valueEnum: {
        '1': '已添加',
        '2': '未添加',
        '3': '已删除',
      },
    },
    {
      title: '机器人昵称',
      dataIndex: 'wechatRobotName',
    },
    {
      title: '机器人微信号',
      dataIndex: 'wechatRobotId',
    },
    {
      title: '添加机器人时间',
      dataIndex: 'bindTime',
    },
    {
      title: '操作',
      valueType: 'option',
      render: (text, record) => [
        <Space key="action">
          <ModalForm
            title="编辑"
            trigger={<a>编辑</a>}
            layout={'horizontal'}
            autoFocusFirstInput
            width="50%"
            {...formItemLayout}
            modalProps={{
              destroyOnHidden: true,
            }}
            initialValues={record}
            submitTimeout={2000}
            onFinish={async (values) => {
              const params = {
                projectId,
                id: record.id,
                ...values,
              };
              setCheckEditTableParams(params as CheckEditTableType);
              setCheckEditModalOpen(true);
              return true;
            }}
          >
            <ProFormText readonly name="teamIdentificationCode" label="团队识别码" />
            <ProFormText width="sm" name="teamWechatId" label="被提醒微信号" />
          </ModalForm>
          <Popconfirm
            title={'是否确认删除'}
            onConfirm={async () => {
              await DeleteBindRobot({
                projectId,
                id: record.id,
                teamIdentificationCode: record.teamIdentificationCode,
              });
              reloadTable(actionRef, 1);
            }}
            key={'delete'}
          >
            <a>删除</a>
          </Popconfirm>
        </Space>,
      ],
    },
  ];

  const importCheckProps: UploadProps = {
    name: 'file',
    accept: '.xlsx,.xls',
    maxCount: 1,
    action: `/new-media-api/notify/robot/wechat/bind/check-import`,
    data: {
      projectId,
    },
    headers: {
      contentType: 'multipart/form-data',
    },
    showUploadList: false,
    beforeUpload: (file) => {
      setTempFile(file);
    },
    onChange(info) {
      if (info.file.status === 'done') {
        if (info.file.response?.data) {
          setCheckTableData(info.file.response?.data);
          setCheckTableDataShow(info.file.response?.data);
          setCheckImportData(true);
        }
      } else if (info.file.status === 'error') {
        message.error(`${info.file.name} 文件导入失败`);
      }
    },
  };

  return (
    <>
      <ProTable
        ghost
        columns={columns}
        scroll={{ x: 'max-content' }}
        actionRef={actionRef}
        headerTitle={[
          <Space key="actions">
            <ModalForm
              title="导入账号信息确认"
              key="import-check"
              trigger={<Button type="primary">批量导入数据</Button>}
              width="80%"
              modalProps={{
                destroyOnHidden: true,
                centered: true,
                afterClose: () => {
                  setCheckImportData(false);
                },
              }}
              onFinish={async () => {
                if (tempFile) {
                  const formData = new FormData();
                  formData.append('file', tempFile);
                  const res = await ImportBindRobot({ projectId }, formData);
                  if (res.code !== 0) {
                    message.error(`文件导入失败 数据项格式可能不正确请检查; 错误原因: ${res.msg}`);
                  } else {
                    if (res.data.successFlag) {
                      message.success(`导入成功`);
                      actionRef.current?.reload();
                    } else {
                      message.error(
                        <span>
                          失败文件下载地址:
                          <a href={res.data.failFileUrl} download>
                            下载失败文件
                          </a>
                        </span>,
                      );
                    }
                  }
                }
                return true;
              }}
            >
              {checkImportData ? (
                <ProTable
                  columns={checkTableColumns}
                  dataSource={checkTableDataShow}
                  onSubmit={(params) => {
                    // const { friendStatus, importStatus } = params;
                    // const newCheckTableData = checkTableData.filter((item) => {
                    //   if (!isUndefined(friendStatus) && !isUndefined(importStatus)) {
                    //     return (
                    //       item.friendStatus == friendStatus && item.importStatus == importStatus
                    //     );
                    //   } else if (!isUndefined(friendStatus)) {
                    //     return item.friendStatus == friendStatus;
                    //   } else if (!isUndefined(importStatus)) {
                    //     return item.importStatus == importStatus;
                    //   } else {
                    //     return true;
                    //   }
                    // });
                    const { friendStatus } = params;
                    const newCheckTableData = checkTableData.filter((item) => {
                      if (!isUndefined(friendStatus)) {
                        return item.friendStatus == friendStatus;
                      } else {
                        return true;
                      }
                    });
                    setCheckTableDataShow(newCheckTableData);
                  }}
                  headerTitle={
                    <Flex vertical gap={10}>
                      <div>
                        好友状态:
                        <Space>
                          <span>
                            已添加{checkTableData.filter((item) => item.friendStatus == 1).length}{' '}
                            条
                          </span>
                          <span>
                            未添加{checkTableData.filter((item) => item.friendStatus == 2).length}{' '}
                            条
                          </span>
                          <span>
                            已删除{checkTableData.filter((item) => item.friendStatus == 3).length}{' '}
                            条
                          </span>
                        </Space>
                      </div>
                      {/* <div>
                        添加类型:
                        <Space>
                          <span>
                            新增{checkTableData.filter((item) => item.importStatus == 1).length} 条
                          </span>
                          <span>
                            切换{checkTableData.filter((item) => item.importStatus == 2).length} 条
                          </span>
                        </Space>
                      </div> */}
                    </Flex>
                  }
                  search={{ filterType: 'light' }}
                  rowKey="teamIdentificationCode"
                  options={false}
                  ghost
                  scroll={{ x: 'max-content' }}
                  size="small"
                  tableStyle={{
                    paddingTop: '30px',
                  }}
                  pagination={{
                    defaultPageSize: 10,
                  }}
                />
              ) : (
                <Dragger {...importCheckProps}>
                  <p className="ant-upload-drag-icon">
                    <InboxOutlined />
                  </p>
                  <p className="ant-upload-text">点击或拖拽上传文件</p>
                </Dragger>
              )}
            </ModalForm>
            <Button
              key="download-template"
              type="primary"
              onClick={async () => {
                const downloadBlob = await DownloadNotifyRobotTemplate();
                saveAs(downloadBlob, '导入模版.xlsx');
              }}
            >
              下载模版
            </Button>
            <Button
              type="primary"
              onClick={async () => {
                const resBlob = await ExportNotifyRobotBind({ projectId });
                saveAs(resBlob, '绑定关系.xls');
              }}
            >
              导出
            </Button>
            <Space>
              <span style={{ textWrap: 'nowrap' }}>
                已添加好友账号数: {bindStatus?.addCount} 个
              </span>
              <span style={{ textWrap: 'nowrap' }}>
                未添加好友账号数: {bindStatus?.unAddCount}个
              </span>
            </Space>
          </Space>,
        ]}
        search={false}
        rowKey="id"
        params={{ projectId }}
        pagination={{
          defaultPageSize: 10,
        }}
        request={(params, sorter, filter) => {
          return proTableRequestAdapter(params, sorter, filter, GetRobotBindRelation);
        }}
      />
      <Modal
        title="编辑账号信息确认"
        open={checkEditModalOpen}
        width="50%"
        onOk={async () => {
          await UpdateNotifyRobotBind(checkEditTableParams);
          setCheckEditModalOpen(false);
          actionRef.current?.reload();
        }}
        onCancel={() => setCheckEditModalOpen(false)}
      >
        <ProTable
          dataSource={checkEditTableData}
          search={false}
          rowKey="teamIdentificationCode"
          columns={checkEditColumns}
        />
      </Modal>
    </>
  );
};

export default TeamRemind;
