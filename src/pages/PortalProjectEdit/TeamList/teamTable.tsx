import {
  BatchDeleteTeam,
  DownLoadTemplate,
  ExportTeam,
  GetProjectTeamInfo,
  TeamInfoItem,
} from '@/services/team';
import copy from 'copy-to-clipboard';
import FieldSelect from '@/components/fieldSelect';
import { ImportProjectTeam } from '@/services/project';
import { proTableRequestAdapter } from '@/utils';
import { transferParams } from '@/utils/common';
import { douyinAuthPath } from '@/utils/const';
import {
  proTableOptionsConfig,
  proTablePaginationConfig,
  proTableSearchConfig,
} from '@/utils/proTableConfig';
import { InboxOutlined } from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ModalForm, ProTable } from '@ant-design/pro-components';
import { Button, message, Popconfirm, Space, Table, Tooltip, Upload, UploadProps } from 'antd';
import dayjs from 'dayjs';
import saveAs from 'file-saver';
import { isUndefined } from 'lodash-es';
import { useCallback, useRef, useState } from 'react';
import EditFieldValueModal from './editFieldValueModal';
import TeamInfoModal from './teamInfoModal';

type TeamListProps = {
  projectId?: string;
  actionRef: React.MutableRefObject<ActionType | undefined>;
};

type CheckTableItem = {
  serialNumber: number;
  importType: number;
};

type CheckImport = {
  /**
   * 导入类型 0：覆盖 1：新增
   */
  importType?: number;
  /**
   * 序号
   */
  serialNumber?: number;
  /**
   * 团队维度
   */
  teamFieldList?: {
    /**
     * 字段id
     */
    fieldId?: number;
    /**
     * 字段名称
     */
    fieldName?: string;
    /**
     * 字段值
     */
    fieldValue?: string;
  }[];
};

const { Dragger } = Upload;

const TeamTable = (props: TeamListProps) => {
  const { actionRef, projectId } = props;
  const [dataSource, setDataSource] = useState<TeamInfoItem[]>([]);
  const [openModal, setOpenModal] = useState(false);
  const [teamInfo, setTeamInfo] = useState<TeamInfoItem>();
  const [checkImportData, setCheckImportData] = useState<boolean>(false);
  const [tempFile, setTempFile] = useState<File | Blob | null>(null);
  const [checkTableData, setCheckTableData] = useState<CheckTableItem[]>([]);
  const [checkTableDataShow, setCheckTableDataShow] = useState<CheckTableItem[]>([]);
  const [columns, setColumns] = useState<ProColumns<TeamInfoItem>[]>([]);
  const [checkTableColumns, setCheckTableColumns] = useState<ProColumns<CheckTableItem>[]>([]);
  const filedIdArrRef = useRef<string[]>([]);

  const handleShowDetails = (record: TeamInfoItem) => {
    setOpenModal(true);
    setTeamInfo(record);
  };

  const getTeamDouyinLink = (teamCode: string, projectId?: string) => {
    const link: string =
      window.location.origin +
      douyinAuthPath +
      '?' +
      `teamCode=${teamCode}&beforeAuth=true&projectId=${projectId}`;

    return link;
  };

  // 复制抖音授权链接
  const copyDouyinAuthLink = useCallback((link?: string) => {
    if (link) {
      const copySuccess = copy(link);

      if (copySuccess) {
        message.success('授权链接已复制');
      } else {
        message.error('复制失败，请再试一次');
      }
    }
  }, []);

  const defaultColumns: ProColumns<TeamInfoItem>[] = [
    {
      title: '团队编码',
      dataIndex: 'teamCode',
      fixed: 'left',
      formItemProps: {
        label: null,
      },
      fieldProps: {
        placeholder: '请输入团队编码',
      },
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      width: 200,
      hideInSearch: true,
    },
    {
      title: '抖音账号数量',
      dataIndex: 'douyinAccountCount',
      align: 'center',
      hideInSearch: true,
    },
    {
      title: '小红书账号数量',
      width: 120,
      dataIndex: 'xiaoHongShuAccountCount',
      align: 'center',
      hideInSearch: true,
    },
    {
      title: '快手账号数量',
      dataIndex: 'kuaishowAccountCount',
      align: 'center',
      hideInSearch: true,
    },
    {
      title: '视频号账号数量',
      dataIndex: 'wxVideoAccountCount',
      align: 'center',
      hideInSearch: true,
    },
    {
      title: '懂车帝账号数量',
      dataIndex: 'dongchediAccountCount',
      align: 'center',
      hideInSearch: true,
    },
    {
      title: 'Bilibili账号数量',
      dataIndex: 'bilibiliAccountCount',
      align: 'center',
      hideInSearch: true,
    },
    {
      title: '汽车之家账号数量',
      dataIndex: 'qczzAccountCount',
      align: 'center',
      hideInSearch: true,
    },
    {
      title: '微博账号数量',
      dataIndex: 'weiboAccountCount',
      align: 'center',
      hideInSearch: true,
    },
    {
      title: '微信公众号账号数量',
      dataIndex: 'wxPublicAccountCount',
      align: 'center',
      hideInSearch: true,
    },
    {
      title: '易车账号数量',
      dataIndex: 'yicheAccountCount',
      align: 'center',
      hideInSearch: true,
    },
    {
      title: '操作',
      width: 180,
      key: 'option',
      valueType: 'option',
      fixed: 'right',
      render: (_, record) => {
        return (
          <Space>
            <Popconfirm
              key="delete"
              title="确定要删除吗？"
              onConfirm={async () => {
                await BatchDeleteTeam([record.id]);
                message.success('删除成功');
                actionRef.current?.reload();
              }}
            >
              <a>删除</a>
            </Popconfirm>

            <EditFieldValueModal
              projectId={projectId}
              teamId={record.id}
              reloadTable={actionRef.current?.reload}
            />
            <a onClick={() => handleShowDetails(record)}>详情</a>
            <Tooltip title={getTeamDouyinLink(record.teamCode, projectId)}>
              <a onClick={() => copyDouyinAuthLink(getTeamDouyinLink(record.teamCode, projectId))}>
                复制授权
              </a>
            </Tooltip>
          </Space>
        );
      },
    },
  ];

  const defaultCheckTableColumns: ProColumns<CheckTableItem>[] = [
    {
      title: '序号',
      dataIndex: 'serialNumber',
      fixed: 'left',
      align: 'center',
      hideInSearch: true,
    },
    {
      title: '导入类型',
      dataIndex: 'importType',
      fixed: 'right',
      align: 'center',
      valueEnum: {
        '0': '覆盖',
        '1': '新增',
      },
      valueType: 'select',
    },
  ];

  const importCheckProps: UploadProps = {
    name: 'file',
    accept: '.xlsx,.xls',
    maxCount: 1,
    action: `/new-media-api/project_team/check-import`,
    data: {
      projectId,
    },
    headers: {
      contentType: 'multipart/form-data',
    },
    showUploadList: false,
    beforeUpload: (file) => {
      setTempFile(file);
    },
    onChange(info) {
      const handleSuccess = (data: CheckImport[]) => {
        const { teamFieldList } = data[0];
        const customColumns: ProColumns<CheckTableItem>[] =
          teamFieldList?.map((item) => ({
            title: item.fieldName,
            dataIndex: item.fieldName,
            valueType: 'text',
            hideInSearch: true,
          })) || [];

        setCheckTableColumns([...defaultCheckTableColumns, ...customColumns]);

        const formatData = data.map((item) => {
          const res = { ...item };
          item.teamFieldList?.forEach((field) => {
            // @ts-ignore
            res[field.fieldName] = field.fieldValue;
          });
          return res;
        });

        setCheckTableData(formatData as CheckTableItem[]);
        setCheckTableDataShow(formatData as CheckTableItem[]);
        setCheckImportData(true);
      };

      if (info.file.status === 'done') {
        const respond = info.file.response;
        if (respond?.data?.length > 0) {
          handleSuccess(respond.data);
        } else if (respond.code !== 0) {
          message.error(`导入失败, 错误原因: ${respond.msg}`);
        }
      } else if (info.file.status === 'error') {
        message.error(`${info.file.name} 文件导入失败`);
      }
    },
  };

  const handleExport = async (projectId: string | undefined) => {
    const resBlob = await ExportTeam({ projectId });
    // TODO 导出失败提示
    saveAs(resBlob, `${dayjs().format('YYYYMMDDHHMM')}.xls`);
  };

  return (
    <>
      <ProTable<TeamInfoItem>
        columns={columns}
        rowSelection={{
          selections: [Table.SELECTION_ALL, Table.SELECTION_INVERT],
          type: 'checkbox',
        }}
        ghost
        actionRef={actionRef}
        scroll={{ x: 'max-content' }}
        search={{ ...proTableSearchConfig }}
        options={{ ...proTableOptionsConfig }}
        pagination={{ ...proTablePaginationConfig }}
        tableAlertRender={({ selectedRowKeys, onCleanSelected }) => {
          return (
            <Popconfirm
              key="delete"
              title="确定要批量删除吗？"
              onConfirm={async () => {
                await BatchDeleteTeam(selectedRowKeys);
                onCleanSelected();
                message.success('删除成功');
                actionRef.current?.reload();
              }}
            >
              <a>删除</a>
            </Popconfirm>
          );
        }}
        headerTitle={[
          <Space key="batch-import">
            <ModalForm
              title="导入账号信息确认"
              key="import-check"
              trigger={<Button type="primary">批量导入数据</Button>}
              width="80%"
              modalProps={{
                destroyOnHidden: true,
                centered: true,
                afterClose: () => {
                  setCheckImportData(false);
                },
              }}
              onFinish={async () => {
                if (tempFile) {
                  const formData = new FormData();
                  formData.append('file', tempFile);
                  const res = await ImportProjectTeam(formData, projectId);
                  if (res.code !== 0) {
                    message.error(`文件导入失败 数据项格式可能不正确请检查; 错误原因: ${res.msg}`);
                  } else {
                    if (res.data?.successFlag) {
                      message.success(`导入成功`);
                      actionRef.current?.reload();
                    } else {
                      message.error(
                        <span>
                          失败文件下载地址:
                          <a href={res.data?.failFileUrl} download>
                            下载失败文件
                          </a>
                        </span>,
                      );
                    }
                  }
                }
                return true;
              }}
            >
              {checkImportData ? (
                <ProTable
                  columns={checkTableColumns}
                  dataSource={checkTableDataShow}
                  onSubmit={(params) => {
                    const { importType } = params;
                    const newCheckTableData = checkTableData.filter((item) => {
                      if (!isUndefined(importType)) {
                        return item.importType == importType;
                      } else {
                        return true;
                      }
                    });
                    setCheckTableDataShow(newCheckTableData);
                  }}
                  headerTitle={
                    <Space>
                      <span>
                        新增数:{checkTableData.filter((item) => item.importType == 1).length}条
                      </span>
                      <span>
                        覆盖数:{checkTableData.filter((item) => item.importType == 0).length}条
                      </span>
                    </Space>
                  }
                  search={{ filterType: 'light' }}
                  rowKey="serialNumber"
                  options={false}
                  ghost
                  scroll={{ x: 'max-content' }}
                  size="small"
                  tableStyle={{
                    paddingTop: '30px',
                  }}
                  pagination={{
                    defaultPageSize: 10,
                  }}
                />
              ) : (
                <Dragger {...importCheckProps}>
                  <p className="ant-upload-drag-icon">
                    <InboxOutlined />
                  </p>
                  <p className="ant-upload-text">点击或拖拽上传文件</p>
                </Dragger>
              )}
            </ModalForm>
            <Button
              key="download-template"
              type="primary"
              onClick={async () => {
                const downloadBlob = await DownLoadTemplate({ projectId });
                saveAs(downloadBlob, '导入模版.xlsx');
              }}
            >
              下载模版
            </Button>
            <Button
              type="primary"
              onClick={() => {
                if (dataSource.length === 0) {
                  message.warning('团队列表为空');
                  return;
                }
                return handleExport(projectId);
              }}
            >
              导出
            </Button>
          </Space>,
        ]}
        rowKey="id"
        params={{ projectId }}
        postData={(data: TeamInfoItem[]) => {
          // 动态生成列
          const infoItem = data[0];
          filedIdArrRef.current = infoItem?.teamFields?.map((item) => item.fieldId) || [];
          const customColumns: ProColumns<TeamInfoItem>[] = [];
          if (infoItem?.teamFields) {
            infoItem.teamFields.forEach((item) => {
              customColumns.push({
                title: item.fieldName,
                dataIndex: item.fieldId,
                formItemProps: {
                  label: null,
                },
                renderFormItem: () => {
                  return <FieldSelect fieldId={item.fieldId} name={item.fieldName} />;
                },
              });
            });
          }
          const newColumns = [...defaultColumns, ...customColumns] as ProColumns<TeamInfoItem>[];
          setColumns(newColumns);
          // 生成表格数据, 由于fieldValueId新添加的都为null所以无法使用账号列表的那种方式
          const formatData = data.map((item) => {
            const res = { ...item };
            const teamFields = res.teamFields;
            if (teamFields) {
              teamFields.forEach((field) => {
                (res as any)[field.fieldId] = field.fieldValue;
              });
            }
            return res;
          });
          setDataSource(formatData);
          return formatData;
        }}
        beforeSearchSubmit={(params) => {
          const fieldList = transferParams(params, filedIdArrRef.current);
          if (fieldList.length > 0) {
            return { ...params, fieldList };
          }
          return params;
        }}
        request={(params, sorter, filter) => {
          return proTableRequestAdapter(params, sorter, filter, GetProjectTeamInfo);
        }}
      />
      <TeamInfoModal isModalOpen={openModal} setOpenModal={setOpenModal} teamInfo={teamInfo} />
    </>
  );
};

export default TeamTable;
