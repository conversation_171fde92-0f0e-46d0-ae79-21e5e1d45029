import { BatchCreateExternalUser, CreateExternalUser, ExportExternalUser } from '@/services/auth';
import { GetNotifyRobotList } from '@/services/notify';
import { GetExternalUserTreeData } from '@/services/team';
import { RedoOutlined } from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Button, Flex, Input, message, Space, Tree, TreeDataNode } from 'antd';
import { saveAs } from 'file-saver';
import { debounce, isEqual, memoize, uniqWith } from 'lodash-es';
import { useCallback, useEffect, useRef, useState } from 'react';
import { dataToTreeDataManager, TreeNode } from './dataToTreeDataManager';
import NoticeInfoModal from './noticeInfoModal';
import UserListModal from './userListModal';
import dayjs from 'dayjs';

const { Search } = Input;
type TreeCardProps = {
  projectId?: string;
};

// 大区type为2
const Type = 2;

const TreeCard = (props: TreeCardProps) => {
  const { projectId } = props;
  const [searchValue, setSearchValue] = useState('');
  const [treeData, setTreeData] = useState<TreeNode[]>([]);
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
  const [autoExpandParent, setAutoExpandParent] = useState(true);
  const memoizedConvertToTree = memoize(dataToTreeDataManager);
  const noMemberField = useRef<{ fieldId: string; dataScope: string }[]>([]);

  const { data: noticeList, refresh } = useRequest(
    () => GetNotifyRobotList({ projectId, type: Type }),
    {
      ready: !!projectId,
    },
  );

  const fetchExternalUserTreeData = async () => {
    // 先把记录无账号的ref清空
    noMemberField.current = [];
    const res = await GetExternalUserTreeData({ projectId });
    const data = res.data;
    if (data) {
      const convertTreeData = memoizedConvertToTree(data);
      setTreeData(convertTreeData);
    }
  };

  const setTreeTitle = useCallback(
    (item: TreeNode) => {
      const { title: treeTitle, users, isMaxLevel } = item;
      const filterScopeUser = users?.filter((item) => item.dataScope === treeTitle) || [];
      const filterScopeUserCount = filterScopeUser?.length || 0;

      if (filterScopeUserCount === 0) {
        noMemberField.current.push({
          fieldId: item.value,
          dataScope: String(item.title),
        });
      }

      const strTitle = item.title as string;
      const index = strTitle.indexOf(searchValue);
      const beforeStr = strTitle.substring(0, index);
      const afterStr = strTitle.slice(index + searchValue.length);

      const title =
        index > -1 ? (
          <span>
            {beforeStr}
            <span style={{ color: 'red' }}>{searchValue}</span>
            {afterStr}
          </span>
        ) : (
          <span>{strTitle}</span>
        );

      const fieldRelationRobot =
        noticeList?.filter((noticeItem) => {
          if (noticeItem.fieldId === item.value && noticeItem.fieldValue === item.title) {
            return noticeItem;
          }
        }) || [];

      return (
        <Flex gap={10}>
          <span>{title}</span>
          <Button
            onClick={async () => {
              const res = await CreateExternalUser({
                projectId,
                fieldId: item.value,
                dataScope: item.title,
              });
              if (res.code === 0) {
                message.success('生成成功');
                await fetchExternalUserTreeData();
              }
            }}
          >
            生成账号
          </Button>
          <UserListModal
            trigger={<a>查看成员{filterScopeUserCount > 0 ? `(${filterScopeUserCount})` : null}</a>}
            data={filterScopeUser}
            refresh={fetchExternalUserTreeData}
          />
          {isMaxLevel && (
            <NoticeInfoModal
              noticeList={fieldRelationRobot}
              projectId={projectId}
              refresh={refresh}
              fieldId={item.value}
              fieldValue={item.title}
            >
              <div>添加提醒机器人{`(${fieldRelationRobot.length})`}</div>
            </NoticeInfoModal>
          )}
        </Flex>
      );
    },
    [searchValue, noticeList, projectId],
  );

  const setTree = (module_data: TreeNode[]) => {
    return module_data.map((item: TreeNode) => {
      const _json = { ...item };
      _json.title = setTreeTitle(item);
      _json.children = item.children ? setTree(item.children) : [];
      return _json;
    });
  };

  const dataList: { key: React.Key; title: string }[] = [];
  const generateList = (data: TreeDataNode[]) => {
    for (let i = 0; i < data.length; i++) {
      const node = data[i];
      const { key } = node;
      dataList.push({ key, title: key as string });
      if (node.children) {
        generateList(node.children);
      }
    }
  };
  generateList(treeData);

  const getParentKey = (key: React.Key, tree: TreeDataNode[]): React.Key => {
    let parentKey: React.Key;
    for (let i = 0; i < tree.length; i++) {
      const node = tree[i];
      if (node.children) {
        if (node.children.some((item) => item.key === key)) {
          parentKey = node.key;
        } else if (getParentKey(key, node.children)) {
          parentKey = getParentKey(key, node.children);
        }
      }
    }
    return parentKey!;
  };

  const onChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    const newExpandedKeys = dataList
      .map((item) => {
        if (item.title.indexOf(value) > -1) {
          return getParentKey(item.key, treeData);
        }
        return null;
      })
      .filter((item, i, self): item is React.Key => !!(item && self.indexOf(item) === i));
    setExpandedKeys(newExpandedKeys);
    setSearchValue(value);
    setAutoExpandParent(true);
  };

  const onExpand = (newExpandedKeys: React.Key[]) => {
    setExpandedKeys(newExpandedKeys);
    setAutoExpandParent(false);
  };

  useEffect(() => {
    if (projectId) {
      fetchExternalUserTreeData();
    }
  }, [projectId]);

  const debounceFetchData = useCallback(
    debounce(() => {
      fetchExternalUserTreeData();
      message.success('刷新维度成功');
    }, 700),
    [],
  );

  return (
    <ProCard title="组织层架架构">
      {treeData.length > 0 ? (
        <>
          <Space style={{ marginBottom: 10 }}>
            <Button
              type="primary"
              onClick={async () => {
                // 去重 由于有的字段会出现在两个不同维度下
                const uniqNoMemberField = uniqWith(noMemberField.current, isEqual);
                const res = await BatchCreateExternalUser({
                  projectId,
                  batchInfoList: uniqNoMemberField,
                });
                if (res.code === 0) {
                  message.success('批量生成成功');
                  await fetchExternalUserTreeData();
                } else {
                  message.error('批量生成失败请重试');
                }
              }}
            >
              批量生成账号
            </Button>
            <Button
              type="primary"
              onClick={async () => {
                const resBlob = await ExportExternalUser({ projectId });
                saveAs(resBlob, `${dayjs().format('YYYYMMDDHHMM')}.xls`);
              }}
            >
              批量导出账号
            </Button>
            <RedoOutlined
              onClick={() => {
                debounceFetchData();
              }}
            />
          </Space>
          <Search style={{ marginBottom: 10 }} placeholder="Search" onChange={onChange} />
          <Tree
            treeData={setTree(treeData)}
            onExpand={onExpand}
            autoExpandParent={autoExpandParent}
            expandedKeys={expandedKeys}
            defaultExpandAll={true}
          />
        </>
      ) : (
        <div>暂未导入信息及分配层级</div>
      )}
    </ProCard>
  );
};

export default TreeCard;
