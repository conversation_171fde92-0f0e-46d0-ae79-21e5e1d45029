import { DeleteNotifyRobot, NotifyRobotListItem } from '@/services/notify';
import { DeleteOutlined } from '@ant-design/icons';
import { Button, Card, Modal } from 'antd';
import { PropsWithChildren, useState } from 'react';
import NoticeModalForm from '../noticeModal';

type NotifyModalProps = {
  noticeList?: NotifyRobotListItem[];
  projectId?: string;
  refresh?: () => void;
  fieldId: string;
  fieldValue: React.ReactNode;
};

const cardStyle: React.CSSProperties = {
  margin: '0 auto 20px',
};

const NoticeInfoModal = (props: PropsWithChildren<NotifyModalProps>) => {
  const { noticeList, projectId, refresh, fieldId, fieldValue } = props;
  const [isModalOpen, setIsModalOpen] = useState(false);

  return (
    <>
      <a onClick={() => setIsModalOpen(true)}>{props.children}</a>
      <Modal
        title={false}
        open={isModalOpen}
        onCancel={() => setIsModalOpen(false)}
        footer={null}
        width="40%"
        destroyOnHidden
        styles={{ body: { paddingBlock: '20px' } }}
      >
        <Card title="添加提醒机器人" style={cardStyle} bordered={false}>
          <>
            {noticeList &&
              noticeList.map((item, index) => {
                const handleDelete = async (id: number) => {
                  await DeleteNotifyRobot({ notifyRobotId: id });
                  await refresh?.();
                };
                return (
                  <div
                    style={{ display: 'flex', gap: '10px', marginBottom: '10px' }}
                    key={`notice-${index}`}
                  >
                    <NoticeModalForm
                      trigger={
                        <Button type="primary" style={{ flex: 1 }}>
                          {item.robotName}
                        </Button>
                      }
                      noticeList={noticeList}
                      initValue={item}
                      refresh={refresh}
                      type={2}
                      fieldId={fieldId}
                      fieldValue={fieldValue}
                      showChangeActionType
                    />
                    <Button
                      icon={<DeleteOutlined />}
                      style={{ width: '50px' }}
                      onClick={() => handleDelete(item.id)}
                    />
                  </div>
                );
              })}
            <NoticeModalForm
              trigger={<Button block>+添加通知</Button>}
              noticeList={noticeList || []}
              projectId={projectId}
              refresh={refresh}
              type={2}
              fieldId={fieldId}
              fieldValue={fieldValue}
              showChangeActionType
            />
          </>
        </Card>
      </Modal>
    </>
  );
};

export default NoticeInfoModal;
