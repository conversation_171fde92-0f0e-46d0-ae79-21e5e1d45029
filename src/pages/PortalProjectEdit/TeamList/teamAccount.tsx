import { PlatForm } from '@/utils/platform';
import {
  DownloadProjectTeamAccountTemplate,
  ExportProjectTeamAccount,
  GetProjectTeamAccountPage,
  ImportProjectTeamAccount,
  ImportProjectTeamAccountV2,
  ProjectTeamAccountItem,
} from '@/services/project-team';
import { proTableRequestAdapter } from '@/utils';
import { InboxOutlined } from '@ant-design/icons';
import { ActionType, ModalForm, ProColumns, ProTable } from '@ant-design/pro-components';
import { Button, message, Radio, Space, Upload, UploadProps } from 'antd';
import dayjs from 'dayjs';
import saveAs from 'file-saver';
import { isUndefined } from 'lodash-es';
import { useRef, useState } from 'react';

type TeamAccountProps = {
  projectId?: string;
};

type CheckTableItem = {
  /**
   * 抖音账号 id
   */
  accountId?: string;
  /**
   * 团队关联类型 0：切换 1：新增
   */
  importType?: number;
  /**
   * 序号
   */
  serialNumber?: number;
  /**
   * 外显 id
   */
  showAccountId?: string;
  /**
   * 爬虫录入状态 0：已录入 1：待录入
   */
  spiderStatus?: number;
  /**
   * 团队识别码
   */
  teamIdentificationCode?: string;
};

const { Dragger } = Upload;

const statusMap: Record<number, string> = {
  0: '未绑定',
  1: '正常',
  2: '失效',
};

const TeamAccount = (props: TeamAccountProps) => {
  const { projectId } = props;
  const actionRef = useRef<ActionType>();
  const [checkTableData, setCheckTableData] = useState<CheckTableItem[]>([]);
  const [checkTableDataShow, setCheckTableDataShow] = useState<CheckTableItem[]>([]);
  const [checkImportData, setCheckImportData] = useState<boolean>(false);
  const [tempFile, setTempFile] = useState<File | Blob | null>(null);
  const [platform, setPlatform] = useState<number>(PlatForm.Douyin);

  const handleExport = async (projectId: string | undefined) => {
    const resBlob = await ExportProjectTeamAccount({ projectId, platform });
    saveAs(resBlob, `${dayjs().format('YYYYMMDDHHMM')}.xls`);
  };

  const columns: ProColumns<ProjectTeamAccountItem>[] = [
    {
      title: '团队识别码',
      dataIndex: 'teamIdentificationCode',
      fixed: 'left',
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      align: 'center',
    },
    {
      title: '唯一用户 ID',
      dataIndex: 'accountId',
      align: 'center',
    },
    {
      title: '账号',
      dataIndex: 'showAccountId',
      align: 'center',
    },
    {
      title: '账号名称',
      dataIndex: 'nickname',
      align: 'center',
    },
    {
      title: '授权状态',
      dataIndex: 'status',
      align: 'center',
      valueEnum: {
        0: '未绑定',
        1: '正常',
        2: '失效',
      },
      render: (_, record) => {
        const statusKey = platform === PlatForm.Douyin ? record.status : record.hostingGrantStatus;
        return statusMap[statusKey as number] || '未知';
      },
    },
    {
      title: '授权账号时间',
      dataIndex: 'douyinApiGrantTime',
      align: 'center',
      render: (_, record) => {
        const showTime =
          platform === PlatForm.Douyin ? record.douyinApiGrantTime : record.hostingGrantTime;
        return showTime ? showTime : '-';
      },
    },
  ];

  const checkTableColumns: ProColumns<CheckTableItem>[] = [
    {
      title: '序号',
      dataIndex: 'serialNumber',
      fixed: 'left',
      align: 'center',
      hideInSearch: true,
    },
    {
      title: '团队识别码',
      dataIndex: 'teamIdentificationCode',
      align: 'center',
      hideInSearch: true,
    },
    {
      title: '账号ID',
      dataIndex: 'showAccountId',
      align: 'center',
      hideInSearch: true,
      render: (_, record) => {
        return platform === PlatForm.WXVideo ? record.showAccountId : record.accountId;
      },
    },
    {
      title: '爬虫录入',
      dataIndex: 'spiderStatus',
      align: 'center',
      valueEnum: {
        '0': '已录入',
        '1': '待录入',
      },
      valueType: 'select',
    },
    {
      title: '团队关联类型',
      dataIndex: 'importType',
      align: 'center',
      valueEnum: {
        '0': '切换',
        '1': '新增',
      },
      valueType: 'select',
    },
  ];

  const importCheckProps: UploadProps = {
    name: 'file',
    accept: '.xlsx,.xls',
    maxCount: 1,
    action:
      platform === PlatForm.WXVideo
        ? `/new-media-api/project-team-account/check-import`
        : `/new-media-api/project-team-account/check-import/v2`,
    data: {
      projectId,
      platform,
    },
    headers: {
      contentType: 'multipart/form-data',
    },
    showUploadList: false,
    beforeUpload: (file) => {
      setTempFile(file);
    },
    onChange(info) {
      if (info.file.status === 'done') {
        if (info.file.response?.data) {
          setCheckTableData(info.file.response?.data);
          setCheckTableDataShow(info.file.response?.data);
          setCheckImportData(true);
        }
      } else if (info.file.status === 'error') {
        message.error(`${info.file.name} 文件导入失败`);
      }
    },
  };

  return (
    <ProTable
      ghost
      columns={columns}
      scroll={{ x: 'max-content' }}
      actionRef={actionRef}
      rowKey="accountId"
      headerTitle={[
        <Space key="batch-import">
          <ModalForm
            title="导入账号信息确认"
            key="import-check"
            trigger={<Button type="primary">批量导入数据</Button>}
            width="80%"
            modalProps={{
              destroyOnHidden: true,
              centered: true,
              afterClose: () => {
                setCheckImportData(false);
              },
            }}
            onFinish={async () => {
              if (tempFile) {
                const formData = new FormData();
                formData.append('file', tempFile);
                const res =
                  platform === PlatForm.WXVideo
                    ? await ImportProjectTeamAccount(formData, projectId, platform)
                    : await ImportProjectTeamAccountV2(formData, projectId, platform);
                if (res.code !== 0) {
                  message.error(`文件导入失败 数据项格式可能不正确请检查; 错误原因: ${res.msg}`);
                } else {
                  if (res.data?.successFlag) {
                    message.success(`导入成功`);
                    actionRef.current?.reload();
                  } else {
                    message.error(
                      <span>
                        失败文件下载地址:
                        <a href={res.data?.failFileUrl} download>
                          下载失败文件
                        </a>
                      </span>,
                    );
                  }
                }
              }
              return true;
            }}
          >
            {checkImportData ? (
              <ProTable
                columns={checkTableColumns}
                dataSource={checkTableDataShow}
                onSubmit={(params) => {
                  const { spiderStatus, importType } = params;
                  const newCheckTableData = checkTableData.filter((item) => {
                    if (!isUndefined(spiderStatus) && !isUndefined(importType)) {
                      return item.spiderStatus == spiderStatus && item.importType == importType;
                    } else if (!isUndefined(spiderStatus)) {
                      return item.spiderStatus == spiderStatus;
                    } else if (!isUndefined(importType)) {
                      return item.importType == importType;
                    } else {
                      return true;
                    }
                  });
                  setCheckTableDataShow(newCheckTableData);
                }}
                headerTitle={
                  <Space>
                    <span>
                      爬虫已录入:{checkTableData.filter((item) => item.spiderStatus == 0).length}条
                    </span>
                    <span>
                      爬虫待录入:{checkTableData.filter((item) => item.spiderStatus == 1).length} 条
                    </span>
                    <span>
                      切换关联:{checkTableData.filter((item) => item.importType == 0).length} 条
                    </span>
                    <span>
                      新增关联:{checkTableData.filter((item) => item.importType == 1).length} 条
                    </span>
                  </Space>
                }
                search={{ filterType: 'light' }}
                rowKey="serialNumber"
                options={false}
                ghost
                scroll={{ x: 'max-content' }}
                size="small"
                tableStyle={{
                  paddingTop: '30px',
                }}
                pagination={{
                  defaultPageSize: 10,
                }}
              />
            ) : (
              <Dragger {...importCheckProps}>
                <p className="ant-upload-drag-icon">
                  <InboxOutlined />
                </p>
                <p className="ant-upload-text">点击或拖拽上传文件</p>
              </Dragger>
            )}
          </ModalForm>
          <Button
            key="download-template"
            type="primary"
            onClick={async () => {
              const downloadBlob = await DownloadProjectTeamAccountTemplate({ platform });
              saveAs(downloadBlob, '导入模版.xlsx');
            }}
          >
            下载模版
          </Button>
          <Button
            type="primary"
            onClick={() => {
              return handleExport(projectId);
            }}
          >
            导出
          </Button>
          <Space style={{ marginLeft: '5px' }}>
            平台:
            <Radio.Group
              options={[
                { label: '抖音', value: PlatForm.Douyin },
                { label: '小红书', value: PlatForm.XiaoHongShu },
                { label: '视频号', value: PlatForm.WXVideo },
                { label: '快手', value: PlatForm.KuaiShou },
                { label: 'B站', value: PlatForm.Bilibili },
                { label: '微博', value: PlatForm.Weibo },
                { label: '易车', value: PlatForm.YiChe },
                { label: '汽车之家', value: PlatForm.QiCheZhiJia },
                { label: '懂车帝', value: PlatForm.DongCheDi },
                { label: '微信公众号', value: PlatForm.WXPublic },
              ]}
              onChange={(value) => {
                setPlatform(value.target.value);
              }}
              value={platform}
            />
          </Space>
        </Space>,
      ]}
      search={false}
      params={{ projectId, platform }}
      pagination={{
        defaultPageSize: 10,
      }}
      request={(params, sorter, filter) => {
        return proTableRequestAdapter(params, sorter, filter, GetProjectTeamAccountPage);
      }}
    />
  );
};

export default TeamAccount;
