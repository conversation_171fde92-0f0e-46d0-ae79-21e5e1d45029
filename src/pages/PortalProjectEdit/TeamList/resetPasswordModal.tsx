import { ResetUserPassword } from '@/services/auth';
import { ModalForm, ProFormText } from '@ant-design/pro-components';
import { message } from 'antd';

const formItemLayout = {
  labelCol: { span: 8 },
  wrapperCol: { span: 12 },
};

type ResetPasswordProps = {
  userId: string;
  refresh?: () => void;
};

const ResetPasswordModal = (props: ResetPasswordProps) => {
  const { userId, refresh } = props;
  return (
    <ModalForm
      title="重置密码"
      layout="horizontal"
      width={500}
      {...formItemLayout}
      autoFocusFirstInput
      modalProps={{
        destroyOnHidden: true,
      }}
      trigger={<a>重置</a>}
      onFinish={async (values) => {
        const res = await ResetUserPassword({ ...values, userId });
        if (res.code === 0) {
          message.success('修改成功');
          refresh?.();
          return true;
        }
      }}
    >
      <ProFormText.Password
        name="newPassword"
        label="修改密码"
        placeholder="修改密码"
        rules={[{ required: true, message: '请输入修改密码' }]}
      />
      <ProFormText.Password
        name="confirmPassword"
        label="确认密码"
        placeholder="确认密码"
        rules={[
          ({ getFieldValue }) => ({
            validator(rule, value) {
              if (!value || getFieldValue('newPassword') === value) {
                return Promise.resolve();
              }
              return Promise.reject('两次密码输入不一致');
            },
          }),
        ]}
      />
    </ModalForm>
  );
};

export default ResetPasswordModal;
