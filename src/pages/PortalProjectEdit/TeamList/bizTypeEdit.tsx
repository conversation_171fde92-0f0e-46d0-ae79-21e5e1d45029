import { FiledTableItem } from '@/services/team';
import { message, Select } from 'antd';

type BizTypeEditProps = {
  value?: Pick<FiledTableItem, 'depth' | 'bizType'>;
  onChange?: (value?: Pick<FiledTableItem, 'depth' | 'bizType'>) => void;
  dataSource?: FiledTableItem[];
};

const BizTypeEdit = (props: BizTypeEditProps) => {
  const checkOnlyBizType = () => {
    return props?.dataSource?.some((item) => item.bizType === 1);
  };

  return (
    <>
      <Select
        size="small"
        placeholder="请选择维度类型"
        value={props?.value?.bizType}
        onChange={(value) => {
          const check = checkOnlyBizType();
          if (value === 1 && check) {
            message.error('唯一项不可多选');
            return;
          }
          props?.onChange?.({ ...props?.value, bizType: value });
        }}
        options={[
          { value: 0, label: '普通' },
          { value: 1, label: '最细层级(唯一)' },
          { value: 2, label: '层级' },
        ]}
      />
      {props?.value?.bizType === 2 && (
        <Select
          size="small"
          placeholder="请选择第几层级"
          value={props?.value?.depth}
          onChange={(value) => props?.onChange?.({ ...props?.value, depth: value })}
          options={[
            { value: 1, label: '第一层级' },
            { value: 2, label: '第二层级' },
            { value: 3, label: '第三层级' },
            { value: 4, label: '第四层级' },
            { value: 5, label: '第五层级' },
          ]}
        />
      )}
    </>
  );
};

export default BizTypeEdit;
