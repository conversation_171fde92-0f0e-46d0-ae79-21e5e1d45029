import { ProCard, type ActionType } from '@ant-design/pro-components';
import { useParams } from '@umijs/max';
import { Flex, Tabs, TabsProps } from 'antd';
import { useRef } from 'react';
import FieldTable from './fieldTable';
import TeamAccount from './teamAccount';
import TeamRemind from './teamRemind';
import TeamTable from './teamTable';
import TreeCard from './treeCard';
import { TeamSystemAccount } from './TeamSystemAccount';

// 他的这个表列名称是用户自定义的所以要动态生成列与数据
const TeamList = () => {
  const { projectId } = useParams();

  const actionRef = useRef<ActionType>();

  const items: TabsProps['items'] = [
    {
      key: 'teamInfo',
      label: '团队信息',
      children: <TeamTable projectId={projectId} actionRef={actionRef} />,
    },
    {
      key: 'accountInfo',
      label: '账号信息',
      children: <TeamAccount projectId={projectId} />,
    },
    {
      key: 'teamRemind',
      label: '提醒信息',
      children: <TeamRemind projectId={projectId} />,
    },
    {
      key: 'teamSystemAccount',
      label: '团队系统账号',
      children: <TeamSystemAccount projectId={projectId} actionRef={actionRef} />,
    },
  ];

  return (
    <Flex vertical gap={20}>
      <FieldTable key="fieldTable" actionRef={actionRef} projectId={projectId} />
      <ProCard>
        <Tabs defaultActiveKey="teamInfo" items={items} />
      </ProCard>
      <TreeCard projectId={projectId} />
    </Flex>
  );
};

export default TeamList;
