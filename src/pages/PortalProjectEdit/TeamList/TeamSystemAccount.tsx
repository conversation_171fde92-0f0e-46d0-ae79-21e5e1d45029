import DynamicTree from '@/components/dynamicTree';
import { usePollingExport } from '@/hooks/usePollingExport';
import {
  DistributorExport,
  DistributorPasswordCreate,
  DistributorResetPassword,
  DistributorSendAccount,
  GetPortalUserDistributor,
  ITeamFieldList,
  PortalUserDistributor,
} from '@/services/team';
import { proTableRequestAdapter } from '@/utils';
import {
  ProTable,
  ProColumns,
  ActionType,
  ModalForm,
  ProFormText,
} from '@ant-design/pro-components';
import { Table, Popconfirm, message, Space, Button } from 'antd';
import { useState } from 'react';

export function TeamSystemAccount(props: {
  projectId?: string;
  actionRef: React.MutableRefObject<ActionType | undefined>;
}) {
  const { actionRef, projectId } = props;
  const [columns, setColumns] = useState<ProColumns<PortalUserDistributor>[]>([]);
  const [treeValue, setTreeValue] = useState<string[] | undefined>(undefined);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };

  const defaultColumns: ProColumns<PortalUserDistributor>[] = [
    {
      title: '团队编码',
      dataIndex: 'teamCode',
      fixed: 'left',
      renderFormItem: () => {
        return <DynamicTree value={treeValue} setValue={setTreeValue} projectId={projectId} />;
      },
    },
    {
      title: '团队登录账号',
      dataIndex: 'distributorAccountId',
      width: 200,
      search: false,
    },
    {
      title: '团队初始密码',
      dataIndex: 'distributorDefaultPassword',
      width: 200,
      search: false,
    },
    {
      title: '发送密码状态',
      dataIndex: 'sendStatus',
      width: 200,
      valueType: 'select',
      valueEnum: {
        1: { text: '成功' },
        2: { text: '失败' },
      },
    },
    {
      title: '账号是否存在',
      dataIndex: 'existStatus',
      hideInTable: true,
      valueType: 'select',
      valueEnum: {
        1: { text: '存在' },
        2: { text: '不存在' },
      },
    },
    {
      title: '操作',
      width: 180,
      key: 'option',
      valueType: 'option',
      fixed: 'right',
      render: (_, record) => {
        return (
          <Space>
            <ModalForm
              title="重置密码"
              layout="horizontal"
              width={500}
              autoFocusFirstInput
              modalProps={{
                destroyOnHidden: true,
              }}
              trigger={<Button type="link">重置密码</Button>}
              onFinish={async (values) => {
                const res = await DistributorResetPassword({
                  userId: record.id,
                  newPassword: values.newPassword,
                  confirmPassword: values.confirmPassword,
                });
                if (res.code === 0) {
                  message.success('重置成功');
                  return true;
                }
              }}
            >
              <ProFormText.Password
                name="newPassword"
                label="修改密码"
                placeholder="修改密码"
                rules={[{ required: true, message: '请输入修改密码' }]}
              />
              <ProFormText.Password
                name="confirmPassword"
                label="确认密码"
                placeholder="确认密码"
                rules={[
                  ({ getFieldValue }) => ({
                    validator(rule, value) {
                      if (!value || getFieldValue('newPassword') === value) {
                        return Promise.resolve();
                      }
                      return Promise.reject('两次密码输入不一致');
                    },
                  }),
                ]}
              />
            </ModalForm>
            <Popconfirm
              key="delete"
              title="确定要重新发送帐密吗？"
              onConfirm={async () => {
                const response = await DistributorSendAccount({
                  projectId: projectId as string,
                  teamCodeList: [record.teamCode],
                });
                if (response.code === 0) {
                  console.log(response.data);
                  if (response.data?.length === 0) {
                    message.success('重新发送帐密成功');
                  } else {
                    message.error(`团队 ${response.data?.join('/')} 发送失败`);
                  }
                }
                actionRef.current?.reload();
              }}
            >
              <Button type="link">重新发送帐密</Button>
            </Popconfirm>
          </Space>
        );
      },
    },
  ];
  const { pollingExport, percent, pollingLoading } = usePollingExport();
  const handelExport = async () => {
    const res = await DistributorExport({
      projectId: Number(projectId),
    });
    if (res.data) {
      pollingExport({ taskId: res.data });
    } else {
      message.error('导出失败,请重试');
    }
  };

  return (
    <>
      <ProTable<PortalUserDistributor>
        columns={columns}
        rowSelection={{
          selections: [Table.SELECTION_ALL, Table.SELECTION_INVERT],
          type: 'checkbox',
          selectedRowKeys,
          onChange: onSelectChange,
          preserveSelectedRowKeys: true,
        }}
        ghost
        rowKey={'teamCode'}
        actionRef={actionRef}
        scroll={{ x: 'max-content' }}
        search={{
          labelWidth: 'auto',
        }}
        onReset={() => {
          setTreeValue(undefined);
        }}
        headerTitle={[
          <Space key="batch-import">
            <Popconfirm
              key="delete"
              title="确定要批量生成系统账号吗？"
              onConfirm={async () => {
                const response = await DistributorPasswordCreate({
                  projectId: projectId as string,
                  teamCodeList: selectedRowKeys as string[],
                });
                actionRef.current?.reload();
                if (response.code === 0) {
                  message.success('生成成功');
                }
              }}
            >
              <Button type="link">批量生成系统账号</Button>
            </Popconfirm>
            <Button
              key="download-template"
              type="link"
              onClick={handelExport}
              disabled={pollingLoading}
            >
              批量下载系统账号
              {pollingLoading && <span>{percent}/100%</span>}
            </Button>
            <Popconfirm
              key="send-password"
              title="确定要发送团队帐密吗？"
              onConfirm={async () => {
                const response = await DistributorSendAccount({
                  projectId: projectId as string,
                  teamCodeList: selectedRowKeys as string[],
                });
                if (response.code === 0) {
                  if (response.data?.length === 0) {
                    message.success('发送成功');
                  } else {
                    message.error(`团队 ${response.data?.join('、')} 发送失败`);
                  }
                }
                actionRef.current?.reload();
              }}
            >
              <Button type="link">发送团队帐密</Button>
            </Popconfirm>
          </Space>,
        ]}
        params={{ projectId, teamCodeList: treeValue }}
        postData={(data: PortalUserDistributor[]) => {
          // 动态生成列
          const infoItem = data[0];
          const customColumns: ProColumns<ITeamFieldList>[] = [];
          if (infoItem?.teamFieldList) {
            infoItem.teamFieldList.forEach((item) => {
              customColumns.push({
                title: item.name,
                dataIndex: item.name,
                valueType: 'text',
                search: false,
              });
            });
          }
          const newColumns = [
            ...defaultColumns,
            ...customColumns,
          ] as ProColumns<PortalUserDistributor>[];
          setColumns(newColumns);
          // 生成表格数据, 由于fieldValueId新添加的都为null所以无法使用账号列表的那种方式
          const formatData = data.map((item) => {
            const res = { ...item };
            const teamFields = res.teamFieldList;
            if (teamFields) {
              teamFields.forEach((field) => {
                (res as any)[field.name] = field.value;
              });
            }
            return res;
          });

          return formatData;
        }}
        request={(params, sorter, filter) => {
          return proTableRequestAdapter(params, sorter, filter, GetPortalUserDistributor);
        }}
        pagination={{
          defaultPageSize: 10,
        }}
      />
    </>
  );
}
