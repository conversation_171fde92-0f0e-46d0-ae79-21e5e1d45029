import { AddField } from '@/services/team';
import {
  ActionType,
  ModalForm,
  ProFormDependency,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { Button } from 'antd';
import { FiledType } from './fieldTable';

interface AddFieldModalProps {
  text: string;
  projectId: string | undefined;
  fieldType: FiledType;
  actionRef: React.MutableRefObject<ActionType | undefined>;
  tableActionRef?: React.MutableRefObject<ActionType | undefined>;
}

const AddFieldModal = (props: AddFieldModalProps) => {
  const { text, projectId, fieldType, actionRef, tableActionRef } = props;
  return (
    <ModalForm<{
      filedname: string;
      bizType?: number;
      depth?: number;
    }>
      title={text}
      layout={'horizontal'}
      width={500}
      modalProps={{
        destroyOnHidden: true,
        centered: true,
      }}
      trigger={<Button>{text}</Button>}
      onFinish={async (values) => {
        let filedName;
        const defaultArr = ['defaultWar', 'defaultProvince', 'defaultCity', 'defaultStore'];
        if (defaultArr.includes(values.filedname)) {
          switch (values.filedname) {
            case 'defaultWar':
              filedName = '战区';
              break;
            case 'defaultProvince':
              filedName = '省份';
              break;
            case 'defaultCity':
              filedName = '城市';
              break;
            case 'defaultStore':
              filedName = '门店';
          }
        } else {
          filedName = values.filedname;
        }
        const addFiledValue = {
          projectId,
          fieldName: filedName,
          type: fieldType,
          showFlag: 1,
          sort: 0,
          bizType: values.bizType,
          depth: values.depth,
        };
        // @ts-ignore
        await AddField(addFiledValue);
        actionRef.current?.reload();
        tableActionRef?.current?.reload();
        return true;
      }}
    >
      {fieldType === FiledType.Custom ? (
        <ProFormText
          width="md"
          name="filedname"
          label="维度名称"
          placeholder="请输入"
          rules={[{ required: true, message: '请输入维度名称' }]}
        />
      ) : (
        <ProFormSelect
          name="filedname"
          label="维度名称"
          valueEnum={{
            defaultWar: '战区',
            defaultProvince: '省份',
            defaultCity: '城市',
            defaultStore: '门店',
          }}
          placeholder="请选择默认维度"
          rules={[{ required: true, message: '请输入维度名称' }]}
        />
      )}
      <ProFormSelect
        width="md"
        name="bizType"
        label="维度类型"
        placeholder="请选择维度类型"
        valueEnum={{
          0: '普通',
          2: '层级',
          1: '最细层级(唯一)',
        }}
        rules={[{ required: true, message: '请选择维度名称' }]}
      />
      <ProFormDependency name={['bizType']}>
        {({ bizType }) => {
          if (bizType == 2) {
            return (
              <ProFormSelect
                valueEnum={{
                  1: '第一层级',
                  2: '第二层级',
                  3: '第三层级',
                  4: '第四层级',
                  5: '第五层级',
                }}
                width="md"
                name="depth"
                placeholder="请选择第几层级"
                label="第几层级"
                rules={[{ required: true, message: '请选择第几层级' }]}
              />
            );
          }
        }}
      </ProFormDependency>
    </ModalForm>
  );
};

export default AddFieldModal;
