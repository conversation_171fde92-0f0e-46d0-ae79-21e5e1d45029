import { DeletePortalUser } from '@/services/auth';
import { User } from '@/services/team';
import { ModalForm } from '@ant-design/pro-components';
import { Space } from 'antd';
import Table, { ColumnsType } from 'antd/es/table';
import ResetPasswordModal from './resetPasswordModal';

type UserModalProps = {
  trigger: JSX.Element;
  data?: User[];
  refresh?: () => void;
};

const UserListModal = (props: UserModalProps) => {
  const { trigger, data, refresh } = props;

  const columns: ColumnsType<User> = [
    {
      dataIndex: 'id',
    },
    {
      title: '账号',
      dataIndex: 'userName',
      align: 'center',
    },
    {
      title: '初始密码',
      dataIndex: 'defaultPassword',
      align: 'center',
    },
    {
      title: '归属架构',
      dataIndex: 'dataScope',
      align: 'center',
    },
    {
      title: '生成时间',
      dataIndex: 'createTime',
      align: 'center',
    },
    {
      title: '操作',
      dataIndex: 'option',
      align: 'center',
      render: (text, record) => {
        return (
          <Space>
            <a
              onClick={async () => {
                await DeletePortalUser([record.id]);
                refresh?.();
              }}
            >
              删除
            </a>
            <ResetPasswordModal userId={record.id} refresh={refresh} />
          </Space>
        );
      },
    },
  ];

  return (
    <ModalForm title="查看成员" trigger={trigger} submitter={false}>
      <Table columns={columns} dataSource={data} pagination={false} />
    </ModalForm>
  );
};

export default UserListModal;
