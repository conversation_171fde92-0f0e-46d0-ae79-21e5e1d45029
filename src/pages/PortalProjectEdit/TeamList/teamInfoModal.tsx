import { PlatForm } from '@/utils/platform';
import { GetTeamInfo, TeamInfo, TeamInfoItem } from '@/services/team';
import { useAsyncEffect } from 'ahooks';
import { Badge, Modal, Space, Table } from 'antd';
import { ColumnsType } from 'antd/es/table';
import { useState } from 'react';

interface PropsType {
  isModalOpen: boolean;
  teamInfo?: TeamInfoItem;
  setOpenModal: (open: boolean) => void;
}

const TeamInfoModal = (props: PropsType) => {
  const { isModalOpen, teamInfo, setOpenModal } = props;
  const [data, setData] = useState<TeamInfo[]>([]);

  const columns: ColumnsType<TeamInfo> = [
    {
      title: '平台',
      dataIndex: 'platform',
      align: 'center',
      render(value) {
        switch (value) {
          case PlatForm.Douyin:
            return '抖音';
          case PlatForm.KuaiShou:
            return '快手';
          case PlatForm.DongCheDi:
            return '懂车帝';
          case PlatForm.WXVideo:
            return '视频号';
          case PlatForm.XiaoHongShu:
            return '小红书';
          case PlatForm.Bilibili:
            return 'B站';
          case PlatForm.Weibo:
            return '微博';
          case PlatForm.YiChe:
            return '易车';
          case PlatForm.QiCheZhiJia:
            return '汽车之家';
          default:
            return '';
        }
      },
    },
    {
      title: '账号',
      dataIndex: 'showAccountId',
      align: 'center',
    },
    {
      title: '账号名称',
      dataIndex: 'nickname',
      align: 'center',
    },
    {
      title: '状态',
      dataIndex: 'status',
      align: 'center',
      render: (_, record) => {
        const statusKey =
          record.platform === PlatForm.Douyin ? record.status : record.hostingGrantStatus;
        switch (statusKey) {
          case 0:
            return (
              <Space>
                <Badge color="gray" text="待授权" />
              </Space>
            );
          case 1:
            return (
              <Space>
                <Badge color="green" text="正常" />
              </Space>
            );
          case 2:
            return (
              <Space>
                <Badge color="red" text="已失效" />
              </Space>
            );
          default:
            return '-';
        }
      },
    },
    {
      title: '爬虫录入时间',
      dataIndex: 'createTime',
      align: 'center',
    },
    {
      title: '账号授权时间',
      dataIndex: 'granDateTime',
      align: 'center',
      render: (_, record) => {
        const showTime =
          record.platform === PlatForm.Douyin ? record.grantDateTime : record.hostingGrantTime;
        return showTime ? showTime : '-';
      },
    },
  ];

  useAsyncEffect(async () => {
    if (isModalOpen && teamInfo) {
      setData([]);
      const res = await GetTeamInfo({ teamId: teamInfo.id });
      const data = res.data;
      data && setData(data);
    }
  }, [isModalOpen, teamInfo]);

  return (
    <Modal
      title="详情"
      width={1200}
      open={isModalOpen}
      maskClosable
      footer
      keyboard
      destroyOnHidden
      onCancel={() => {
        setOpenModal(false);
      }}
    >
      <Table columns={columns} dataSource={data} pagination={false} />
    </Modal>
  );
};

export default TeamInfoModal;
