import { Team<PERSON><PERSON>, TeamFieldList, UpdateTeamFieldValueV2 } from '@/services/team';
import { ModalForm, ProFormSelect, ProFormText } from '@ant-design/pro-components';
import { pcaTextArr } from 'element-china-area-data';
import { useEffect, useRef } from 'react';
import { useImmer } from 'use-immer';

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 12 },
};

export interface EditFieldValueModalProps {
  projectId: string | undefined;
  teamId: string;
  reloadTable?: () => void;
}

const defaultField = ['项目名称', '团队识别码'];

const EditFieldValueModal = (props: EditFieldValueModalProps) => {
  const { projectId, teamId, reloadTable } = props;
  const citySelectRef = useRef<{ value: string; lable: string }[] | undefined>([
    {
      value: '请先选择省份',
      lable: '请先选择省份',
    },
  ]);

  const [filedState, updateFiledState] = useImmer<TeamField[]>([]);

  const renderCity = (province: string) => {
    const city = pcaTextArr.find((item) => item.value === province)?.children;
    const citySelectValue = city?.map((item) => ({ value: item.value, lable: item.label }));
    citySelectRef.current = citySelectValue;
  };

  const renderTeamField = (fieldList: TeamField[] | undefined) => {
    if (!fieldList) return <></>;
    return fieldList.map((item) => {
      if (item.type === 1) {
        switch (item.fieldName) {
          case '省份':
            renderCity(item.fieldValue);
            return (
              <ProFormSelect
                name={item.fieldName}
                label={item.fieldName}
                options={pcaTextArr.map((item) => ({ value: item.value, lable: item.label }))}
                fieldProps={{
                  onChange: (value: string) => {
                    renderCity(value);
                    updateFiledState((draft) => {
                      const item = draft.find((x) => x.fieldName === '省份');
                      if (item) {
                        item.fieldValue = value;
                      }
                    });
                  },
                  defaultValue: item.fieldValue,
                }}
              />
            );
          case '城市':
            return (
              <ProFormSelect
                name={item.fieldName}
                label={item.fieldName}
                options={citySelectRef.current}
                fieldProps={{
                  onChange: (value: string) => {
                    updateFiledState((draft) => {
                      const item = draft.find((x) => x.fieldName === '城市');
                      if (item) {
                        item.fieldValue = value;
                      }
                    });
                  },
                  defaultValue: item.fieldValue,
                }}
              />
            );
          case '战区':
            return (
              <ProFormSelect
                name={item.fieldName}
                label={item.fieldName}
                options={[
                  { label: '东区', value: '东区' }, // 后端需要的是中文
                  { label: '南区', value: '南区' },
                  { label: '西区', value: '西区' },
                  { label: '北区', value: '北区' },
                ]}
                fieldProps={{
                  onChange: (value: string) => {
                    updateFiledState((draft) => {
                      const item = draft.find((x) => x.fieldName === '战区');
                      if (item) {
                        item.fieldValue = value;
                      }
                    });
                  },
                  defaultValue: item.fieldValue,
                }}
              />
            );
        }
      } else {
        return (
          <ProFormText
            key={'text'}
            name={item.fieldName}
            label={item.fieldName}
            fieldProps={{
              onChange: (e) => {
                const filedName = item.fieldName;
                updateFiledState((draft) => {
                  const item = draft.find((x) => x.fieldName === filedName);
                  if (item) {
                    item.fieldValue = e.target.value;
                  }
                });
              },
              defaultValue: item.fieldValue,
            }}
          />
        );
      }
    });
  };

  useEffect(() => {
    if (filedState.length > 0) {
      renderTeamField(filedState);
    }
  }, [filedState]);

  return (
    <ModalForm
      title="编辑"
      trigger={<a>编辑</a>}
      layout={'horizontal'}
      autoFocusFirstInput
      width="50%"
      {...formItemLayout}
      modalProps={{
        destroyOnHidden: true,
      }}
      onOpenChange={async (open: boolean) => {
        if (open) {
          const result = await TeamFieldList({ projectId, teamId });
          const fieldList = result?.data;
          const orderMap: { [key: string]: number } = {
            战区: 0,
            省份: 1,
            城市: 2,
          };
          // 去除默认不可修改的两项
          const filterDefault = fieldList?.filter((item) => !defaultField.includes(item.fieldName));
          // 纠正排序
          const sortFiledState = filterDefault?.sort((a, b) => {
            const orderA = orderMap[a.fieldName];
            const orderB = orderMap[b.fieldName];
            if (orderA !== undefined && orderB !== undefined) {
              return orderA - orderB;
            }
            if (orderA !== undefined) return -1;
            if (orderB !== undefined) return 1;
            return 0;
          });
          updateFiledState(sortFiledState || []);
        }
      }}
      onFinish={async () => {
        const updateValue = filedState.map(({ fieldId, fieldValue }) => ({
          fieldId,
          fieldValue,
        }));
        await UpdateTeamFieldValueV2({ projectId, teamId }, updateValue);
        await reloadTable?.();
        return true;
      }}
    >
      {renderTeamField(filedState)}
    </ModalForm>
  );
};

export default EditFieldValueModal;
