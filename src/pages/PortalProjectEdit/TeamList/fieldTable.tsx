import { DeleteField, FiledTableItem, GetPageField, UpdateField } from '@/services/team';
import { proTableRequestAdapter } from '@/utils';
import { ActionType, ProCard, ProColumns, ProTable } from '@ant-design/pro-components';

import { Flex, FormInstance, message, Popconfirm, Space, Switch } from 'antd';
import { useRef, useState } from 'react';
import AddFieldModal from './addFieldModal';

import { BizType } from '@/utils/common';
import { MenuOutlined } from '@ant-design/icons';
import type { DragEndEvent } from '@dnd-kit/core';
import { DndContext } from '@dnd-kit/core';
import { restrictToVerticalAxis } from '@dnd-kit/modifiers';
import {
  arrayMove,
  SortableContext,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import React from 'react';
import BizTypeEdit from './bizTypeEdit';

export enum FiledType {
  Custom = 0,
  Default = 1,
}

interface FieldTableProps {
  actionRef: React.MutableRefObject<ActionType | undefined>;
  projectId?: string;
}

const defaultField = [9999, 9998];

interface RowProps extends React.HTMLAttributes<HTMLTableRowElement> {
  'data-row-key': string;
}

const Row = ({ children, ...props }: RowProps) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    setActivatorNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({
    id: props['data-row-key'],
  });

  const style: React.CSSProperties = {
    ...props.style,
    transform: CSS.Transform.toString(transform && { ...transform, scaleY: 1 }),
    transition,
    ...(isDragging && transform ? { position: 'relative', zIndex: 9999 } : {}),
  };

  return (
    <tr {...props} ref={setNodeRef} style={style} {...attributes}>
      {React.Children.map(children, (child) => {
        const sort = (child as React.ReactElement).props.record?.sort || 0;
        if ((child as React.ReactElement).key === 'sort' && !defaultField.includes(sort)) {
          return React.cloneElement(child as React.ReactElement, {
            children: (
              <MenuOutlined
                ref={setActivatorNodeRef}
                style={{ touchAction: 'none', cursor: 'move' }}
                {...listeners}
              />
            ),
          });
        }
        return child;
      })}
    </tr>
  );
};

const renderHierarchy = (data: number | undefined) => {
  switch (data) {
    case 1:
      return '第一层级';
    case 2:
      return '第二层级';
    case 3:
      return '第三层级';
    case 4:
      return '第四层级';
    case 5:
      return '第五层级';
    default:
      return '';
  }
};

const FieldTable = (props: FieldTableProps) => {
  const { actionRef: tableActionRef, projectId } = props;
  const actionRef = useRef<ActionType>();
  const [dataSource, setDataSource] = useState<FiledTableItem[]>([]);
  const formRef = useRef<FormInstance>();

  const columns: ProColumns<FiledTableItem>[] = [
    {
      key: 'sort',
    },
    {
      dataIndex: 'id',
      hideInTable: true,
      editable: false,
    },
    {
      title: '维度名称',
      dataIndex: 'fieldName',
      align: 'center',
    },
    {
      title: '维度类型',
      dataIndex: 'type',
      align: 'center',
      editable: false,
      render: (_, record) => {
        if (defaultField.includes(record.sort)) {
          return <></>;
        } else {
          return <span>{record.type === FiledType.Custom ? '自定义' : '默认'}</span>;
        }
      },
    },
    {
      title: '层级类型',
      dataIndex: 'biz',
      align: 'center',
      valueEnum: {
        0: '普通',
        2: '层级',
        1: '最细层级(唯一)',
      },
      render: (_, record) => {
        if (defaultField.includes(record.sort)) {
          return <></>;
        } else if (record.bizType === BizType.ORDINARY) {
          return '普通';
        } else if (record.bizType === BizType.HIERACHY) {
          return <span>层级 {renderHierarchy(record.depth)}</span>;
        } else if (record.bizType === BizType.FINALLYHIERACHY) {
          return '最细层级(唯一)';
        }
      },
      renderFormItem: () => {
        return <BizTypeEdit dataSource={dataSource} />;
      },
    },
    {
      title: '数据表',
      dataIndex: 'showFlag',
      align: 'center',
      editable: false,
      render: (_, record) => {
        if (defaultField.includes(record.sort)) {
          return <></>;
        } else {
          return (
            <Switch
              checked={Boolean(record.showFlag)}
              onChange={async (checked) => {
                await UpdateField([{ ...record, showFlag: Number(checked) }]);
                actionRef.current?.reload();
              }}
              checkedChildren="显示"
              unCheckedChildren="隐藏"
            />
          );
        }
      },
    },
    {
      title: '操作',
      valueType: 'option',
      align: 'center',
      render: (text, record, _, action) => {
        if (defaultField.includes(record.sort)) {
          return <></>;
        } else {
          return (
            <Space>
              {record.bizType !== 0 ? (
                <Popconfirm
                  key="editable"
                  title="修改层级将影响层级架构下生成的账号，请确认是否修改"
                  onConfirm={() => record.id && action?.startEditable?.(record.id)}
                >
                  <a>编辑</a>
                </Popconfirm>
              ) : (
                <a onClick={() => record.id && action?.startEditable?.(record.id)}>编辑</a>
              )}

              <Popconfirm
                key="delete"
                title="修改层级将影响层级架构下生成的账号,确定要删除该维度吗？"
                onConfirm={async () => {
                  await DeleteField([record.id]);
                  message.success('删除成功');
                  actionRef.current?.reload();
                  tableActionRef.current?.reload();
                }}
              >
                <a>删除</a>
              </Popconfirm>
            </Space>
          );
        }
      },
    },
  ];

  const onDragEnd = async ({ active, over }: DragEndEvent) => {
    if (active.id !== over?.id) {
      const activeIndex = dataSource.findIndex((i) => i.id === active.id);
      const overIndex = dataSource.findIndex((i) => i.id === over?.id);
      const moveResult = arrayMove(dataSource, activeIndex, overIndex);
      setDataSource(moveResult);
      const cloneDataSource: FiledTableItem[] = [...moveResult];
      for (let i = 2; i < cloneDataSource.length; i++) {
        cloneDataSource[i].sort = cloneDataSource.length - i;
      }
      // 去除前两项
      const updateDataSource = cloneDataSource.filter((item) => !defaultField.includes(item.sort));
      await UpdateField(updateDataSource);
    }
  };

  return (
    <ProCard>
      <Flex vertical style={{ color: 'red', fontSize: '12px' }}>
        <span>*排组织层级架构必须要有1个【最细层级（唯一）】类型，否则影响组织架构生成</span>
        <span>
          *排组织层级架构【层级】类型必须要按照顺序，不能够跳跃层级，否则影响组织架构生成。
        </span>
        <span>例如：必须要按第一层级、第二层级、第三层级...，依次类推。</span>
      </Flex>
      <DndContext modifiers={[restrictToVerticalAxis]} onDragEnd={onDragEnd}>
        <SortableContext items={dataSource.map((i) => i.id)} strategy={verticalListSortingStrategy}>
          <ProTable
            search={false}
            columns={columns}
            formRef={formRef}
            options={false}
            pagination={false}
            params={{ projectId }}
            actionRef={actionRef}
            dataSource={dataSource}
            postData={(data: FiledTableItem[]) => setDataSource(data)}
            request={(params, sorter, filter) => {
              return proTableRequestAdapter(params, sorter, filter, GetPageField);
            }}
            components={{
              body: {
                row: Row,
              },
            }}
            editable={{
              type: 'single',
              onSave: async (key, row) => {
                const biz = row.biz;
                const updateData = {
                  ...row,
                  ...biz,
                };
                delete updateData.index;
                delete updateData.biz;
                await UpdateField([updateData]);
                actionRef.current?.reload();
                tableActionRef.current?.reload();
              },
            }}
            rowKey="id"
            toolBarRender={() => [
              <AddFieldModal
                key={'add-custom'}
                text={'添加自定义维度'}
                fieldType={FiledType.Custom}
                actionRef={actionRef}
                projectId={projectId}
                tableActionRef={tableActionRef}
              />,
              <AddFieldModal
                key={'add-default'}
                text={'添加默认维度'}
                fieldType={FiledType.Default}
                actionRef={actionRef}
                projectId={projectId}
                tableActionRef={tableActionRef}
              />,
            ]}
          />
        </SortableContext>
      </DndContext>
    </ProCard>
  );
};

export default FieldTable;
