import { DepthList<PERSON>ith<PERSON>ser, TreeDataWithUser, User } from '@/services/team';
import { ReactNode } from 'react';

export type TreeNode = {
  title: string | ReactNode;
  value: string;
  key: string;
  children?: TreeNode[];
  users?: User[];
  isMaxLevel?: boolean;
};

let treeData: TreeNode[] = [];
const firstSet = new Set();
const secondSet = new Set();
const thirdSet = new Set();
const fourthSet = new Set();
const fifthSet = new Set();
let maxDepth: number;

function getLevelSet(depth: number) {
  switch (depth) {
    case 0:
      return firstSet;
    case 1:
      return secondSet;
    case 2:
      return thirdSet;
    case 3:
      return fourthSet;
    case 4:
      return fifthSet;
  }
}

function findNodePush(
  levels: string[],
  depth: number,
  key: string,
  name: string,
  fieldId: string,
  users: User[],
) {
  let parentNode: TreeNode | undefined;
  let findNode: TreeNode | undefined;
  levels.forEach((level, index) => {
    if (index === 0) {
      findNode = treeData.find((item) => item.title === level);
    } else {
      findNode = parentNode?.children?.find((item) => item.title === level);
    }
    if (findNode) {
      // 找到节点记录父节点
      parentNode = findNode;
    }
  });

  if (!findNode && depth === 0) {
    treeData.push({
      title: name,
      value: fieldId,
      key: key,
      children: [],
      users,
      isMaxLevel: true,
    });
    return;
  }
  if (!findNode && depth < maxDepth && depth > 0 && !getLevelSet(depth)?.has(key)) {
    parentNode?.children?.push({
      title: name,
      value: fieldId,
      key: key,
      children: [],
      users,
    });
    return;
  }
}

function transRowData(item: DepthListWithUser) {
  const rowData = item.projectTeamFieldValueDTOList;
  const portalUserExternalList = item.portalUserExternalList;

  const levels = new Array(maxDepth + 1).fill('');

  rowData.forEach((item) => {
    if (item.bizType === 2) {
      levels[item.depth - 1] = item.fieldValue;
      const filterLevels = levels.filter(Boolean);
      const key = filterLevels.join('-');
      if (!getLevelSet(item.depth - 1)?.has(key)) {
        findNodePush(
          filterLevels,
          item.depth - 1,
          key,
          item.fieldValue,
          String(item.fieldId),
          portalUserExternalList,
        );
        getLevelSet(item.depth - 1)?.add(key);
      }
    }
  });
}

export function dataToTreeDataManager(data: TreeDataWithUser) {
  // 先排好序 最大层在上面 最底层在最后
  firstSet.clear();
  secondSet.clear();
  thirdSet.clear();
  fourthSet.clear();
  fifthSet.clear();
  treeData = [];

  maxDepth = data.maxDepth;
  data.depthList.forEach((item) => {
    item.projectTeamFieldValueDTOList.sort((a, b) => {
      if (a.bizType === 1 && b.bizType !== 1) {
        return 1;
      } else if (a.bizType !== 1 && b.bizType === 1) {
        return -1;
      } else if (a.bizType === 1 && b.bizType === 1) {
        return 0;
      } else {
        return a.depth - b.depth;
      }
    });
    transRowData(item);
  });

  return treeData;
}
