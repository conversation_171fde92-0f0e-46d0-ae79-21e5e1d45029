import {
  QualityMonitorWordSave,
  QualityMonitorWordUpdate,
  SceneTypeEnum,
} from '@/services/quality';
import { checkChineseComma } from '@/utils/common';
import { ModalForm, ProFormSelect, ProFormText, ProFormTextArea } from '@ant-design/pro-components';
import { Button, message } from 'antd';
import { MonitorTable } from '.';

type QualityModalProps = {
  sceneType: SceneTypeEnum;
  projectId: string | undefined;
  isEdit?: boolean;
  refresh?: () => void;
  data?: MonitorTable;
};

const QualityModal = (props: QualityModalProps) => {
  const { sceneType, projectId, isEdit, refresh, data } = props;

  return (
    <ModalForm<{
      id?: string;
      name: string;
      suspectedWord?: string;
      sensitiveWord?: string;
      description?: string;
    }>
      title={isEdit ? '编辑' : '添加规则'}
      trigger={
        isEdit ? (
          <a>编辑</a>
        ) : (
          <Button type="primary" key="add">
            添加
          </Button>
        )
      }
      modalProps={{
        destroyOnHidden: true,
      }}
      autoFocusFirstInput
      onFinish={async (values) => {
        if (sceneType === SceneTypeEnum.TEXT) {
          const { suspectedWord, sensitiveWord } = values;
          if (suspectedWord && sensitiveWord) {
            const illegalWordsArr = suspectedWord.split(',');
            const sensitiveWordsArr = sensitiveWord.split(',');
            const isRepeat = illegalWordsArr.some((item: string) =>
              sensitiveWordsArr.includes(item),
            );
            if (isRepeat) {
              message.error('疑似词与敏感词不能重复');
              return;
            }
          }
        }
        const submitSaveData = {
          projectId,
          sceneType,
          ...values,
          id: isEdit ? data?.id : undefined,
        };

        if (isEdit) {
          await QualityMonitorWordUpdate(submitSaveData);
        } else {
          await QualityMonitorWordSave(submitSaveData);
        }
        refresh?.();
        return true;
      }}
    >
      <ProFormSelect
        name="standardType"
        label="规范类型"
        required
        valueEnum={{
          1: '品牌',
          2: '平台',
        }}
        initialValue={data?.standardType?.toString()}
      />
      <ProFormText
        name="name"
        label="分类"
        required
        fieldProps={{ maxLength: 10 }}
        initialValue={data?.name}
      />
      {(sceneType === SceneTypeEnum.TEXT || sceneType === SceneTypeEnum.COMMENT) && (
        <ProFormTextArea
          name="suspectedWord"
          label="疑似词"
          placeholder="多个监控词用英文逗号隔开"
          tooltip="多个监控词用英文逗号隔开"
          initialValue={data?.suspectedWord}
          rules={[{ validator: checkChineseComma }]}
        />
      )}
      {sceneType === SceneTypeEnum.TEXT && (
        <ProFormTextArea
          name="sensitiveWord"
          label="敏感词"
          placeholder="多个监控词用英文逗号隔开"
          tooltip="多个监控词用英文逗号隔开"
          initialValue={data?.sensitiveWord}
          rules={[{ validator: checkChineseComma }]}
        />
      )}
      {sceneType === SceneTypeEnum.BEHAVIOR && (
        <ProFormTextArea name="description" label="说明" initialValue={data?.description} />
      )}
    </ModalForm>
  );
};

export default QualityModal;
