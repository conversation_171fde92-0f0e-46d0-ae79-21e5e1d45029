import { FunctionCode, UpdateSystemFunction } from '@/services/system';
import { Switch } from 'antd';

type AfkTableProps = {
  projectId?: string;
  afkFunctionState?: boolean;
  refreshSystemFunction?: () => void;
};

const AfkTable = (props: AfkTableProps) => {
  const { projectId, afkFunctionState, refreshSystemFunction } = props;

  return (
    <div
      style={{
        fontSize: '16px',
        fontWeight: '500',
        marginBlock: '10px',
      }}
    >
      空挂播识别
      <Switch
        style={{ marginLeft: '10px' }}
        value={afkFunctionState}
        onChange={async (value) => {
          await UpdateSystemFunction({
            projectId,
            functionCode: FunctionCode.EmptyBroadcastRecognition,
            isDisabled: value ? 0 : 1,
          });
          refreshSystemFunction?.();
        }}
      />
    </div>
  );
};

export default AfkTable;
