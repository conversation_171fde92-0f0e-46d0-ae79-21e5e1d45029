import {
  BatchUpdateAnchorImageSwitch,
  GetAnchorImageList,
  QualityLiveAnchorImage,
  UpdateAnchorImageDescription,
  UpdateAnchorImageSwitch,
} from '@/services/quality';
import { FunctionCode, UpdateSystemFunction } from '@/services/system';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { Checkbox, Space, Switch } from 'antd';
import { CheckboxChangeEvent } from 'antd/es/checkbox';
import { useRef, useState } from 'react';

type AnchorImageTableProps = {
  projectId?: string;
  enableOcr?: boolean;
  refreshSystemFunction?: () => void;
};

const AnchorImageTable = (props: AnchorImageTableProps) => {
  const { projectId, enableOcr, refreshSystemFunction } = props;
  const actionRef = useRef<ActionType>();
  const [anchorImageSwitchState, setAnchorImageSwitchState] = useState(false);

  const onChangeAnchorImage = async (checked: boolean) => {
    await BatchUpdateAnchorImageSwitch({ projectId, isDisabled: checked ? 0 : 1 });
    actionRef.current?.reload();
  };

  const onChangeAnchorOcrState = async (e: CheckboxChangeEvent) => {
    await UpdateSystemFunction({
      projectId,
      functionCode: FunctionCode.AnchorImageRecognition,
      isDisabled: e.target.checked ? 0 : 1,
    });
    refreshSystemFunction?.();
  };

  const anchorImageColumns: ProColumns<QualityLiveAnchorImage>[] = [
    {
      title: '分类',
      dataIndex: 'name',
      align: 'center',
      width: 100,
      editable: false,
    },
    {
      title: '添加类型',
      dataIndex: '',
      align: 'center',
      width: 100,
      editable: false,
      render: () => <>默认</>,
    },
    {
      title: '说明',
      dataIndex: 'description',
      align: 'center',
    },
    {
      title: '操作',
      valueType: 'option',
      hideInSearch: true,
      align: 'center',
      fixed: 'right',
      width: 180,
      render: (_, record, index, action) => {
        return (
          <Space>
            <a
              key="editable"
              onClick={() => {
                action?.startEditable?.(record.id);
              }}
            >
              编辑
            </a>
            <Switch
              value={!record.isDisabled}
              onChange={async (checked) => {
                await UpdateAnchorImageSwitch({
                  id: record.id,
                  isDisabled: checked ? 0 : 1,
                  projectId,
                });
                action?.reload();
              }}
            />
          </Space>
        );
      },
    },
  ];

  return (
    <ProTable
      headerTitle={
        <Space>
          <div style={{ height: '32px', lineHeight: '32px' }}>主播形象</div>
          <Switch value={anchorImageSwitchState} onChange={onChangeAnchorImage} />
          <Checkbox
            checked={enableOcr}
            onChange={onChangeAnchorOcrState}
            style={{ marginLeft: '10px' }}
          />
          <div>画面OCR识别</div>
        </Space>
      }
      columns={anchorImageColumns}
      rowKey="id"
      search={false}
      options={false}
      ghost
      editable={{
        type: 'single',
        onSave: async (key, row) => {
          const { id, description, standardType } = row;
          await UpdateAnchorImageDescription({
            projectId,
            id,
            description,
            standardType,
          });
          actionRef.current?.reload();
        },
        actionRender: (row, config, defaultDom) => {
          return [defaultDom.save, defaultDom.cancel];
        },
      }}
      actionRef={actionRef}
      params={{ projectId }}
      postData={(data: QualityLiveAnchorImage[]) => {
        const anchorImageSwitchState = data?.some((item) => item.isDisabled === 0);
        setAnchorImageSwitchState(anchorImageSwitchState);
        return data;
      }}
      request={async (params) => {
        const result = await GetAnchorImageList({ projectId: params.projectId });
        return {
          data: result.data,
          success: result.code === 0,
          total: result.data?.length,
        };
      }}
      pagination={{
        defaultPageSize: 10,
        hideOnSinglePage: true,
      }}
    />
  );
};

export default AnchorImageTable;
