import {
  BatchUpdateMonitorWordStatus,
  QualityMonitorWordDelete,
  SceneTypeEnum,
  UpdateQualityMonitorSwitch,
} from '@/services/quality';
import { FunctionCode, UpdateSystemFunction } from '@/services/system';
import { InfoCircleOutlined } from '@ant-design/icons';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import { Space, Popconfirm, message, Switch, Tooltip, Checkbox } from 'antd';
import { CheckboxChangeEvent } from 'antd/es/checkbox';
import { MonitorTable } from '.';
import QualityModal from './qualityModal';

type BehaviorTableProps = {
  projectId?: string;
  dataSourceData?: any[];
  loading?: boolean;
  refresh?: () => void;
  enableOcr?: boolean;
  refreshSystemFunction?: () => void;
};

const BehaviorTable = (props: BehaviorTableProps) => {
  const { projectId, dataSourceData, loading, refresh, enableOcr, refreshSystemFunction } = props;

  const behaviorSwitchState = dataSourceData?.some((item) => item.isDisabled === 0);

  const onChangeBehaviorSwitch = async (checked: boolean) => {
    await BatchUpdateMonitorWordStatus({
      projectId,
      isDisabled: checked ? 0 : 1,
      sceneType: SceneTypeEnum.BEHAVIOR,
    });
    refresh?.();
  };

  const onChangeLiveOcrSwitch = async (e: CheckboxChangeEvent) => {
    await UpdateSystemFunction({
      projectId,
      functionCode: FunctionCode.LiveOCRRecognition,
      isDisabled: e.target.checked ? 0 : 1,
    });
    refreshSystemFunction?.();
  };

  const behaviorColumns: ProColumns<MonitorTable>[] = [
    {
      title: '规范类型',
      dataIndex: 'standardType',
      align: 'center',
      width: 100,
      valueType: 'select',
      valueEnum: {
        1: '品牌',
        2: '平台',
      },
    },
    {
      title: '分类',
      dataIndex: 'name',
      align: 'center',
      width: 130,
    },
    {
      title: '添加类型',
      dataIndex: 'type',
      align: 'center',
      width: 100,
      valueEnum: {
        1: '默认',
        2: '自定义',
      },
    },
    {
      title: '说明',
      dataIndex: 'description',
      align: 'center',
    },
    {
      title: '操作',
      valueType: 'option',
      hideInSearch: true,
      align: 'center',
      fixed: 'right',
      width: 180,
      render: (_, record) => {
        return (
          <Space>
            {record.type === 2 && (
              <Popconfirm
                title={'是否确认删除'}
                onConfirm={async () => {
                  const res = await QualityMonitorWordDelete({ id: record.id });
                  res.code === 0 ? message.success('删除成功') : message.error('删除失败');
                  refresh?.();
                }}
                key={'delete'}
              >
                <a>删除</a>
              </Popconfirm>
            )}
            <QualityModal
              isEdit={true}
              sceneType={SceneTypeEnum.BEHAVIOR}
              projectId={record.projectId}
              data={record}
              refresh={refresh}
            />
            <Switch
              value={!record.isDisabled}
              onChange={async (checked) => {
                await UpdateQualityMonitorSwitch({
                  id: record.id,
                  isDisabled: checked ? 0 : 1,
                  projectId,
                });
                refresh?.();
              }}
            />
          </Space>
        );
      },
    },
  ];

  return (
    <ProTable
      headerTitle={
        <Space style={{ marginRight: '10px' }}>
          <div>行为标识</div>
          <Switch value={behaviorSwitchState} onChange={onChangeBehaviorSwitch} />
          <Tooltip title="行为标识类型，系统不进行识别，需要进行人工质检。此处配置用于人工质检选择分类使用">
            <InfoCircleOutlined />
          </Tooltip>
          <Checkbox
            checked={enableOcr}
            onChange={onChangeLiveOcrSwitch}
            style={{ marginLeft: '10px' }}
          />
          <div>画面OCR识别</div>
        </Space>
      }
      columns={behaviorColumns}
      rowKey="name"
      search={false}
      options={false}
      ghost
      loading={loading}
      dataSource={dataSourceData}
      toolBarRender={() => [
        <QualityModal
          key={'add'}
          sceneType={SceneTypeEnum.BEHAVIOR}
          projectId={projectId}
          refresh={refresh}
        />,
      ]}
      pagination={{
        defaultPageSize: 10,
        hideOnSinglePage: true,
      }}
    />
  );
};

export default BehaviorTable;
