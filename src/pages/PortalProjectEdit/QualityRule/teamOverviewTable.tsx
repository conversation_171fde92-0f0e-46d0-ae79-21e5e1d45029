import {
  GetTeamOverviewData,
  ProjectTeamOverviewFieldVO,
  UpdateTeamOverviewData,
} from '@/services/team';
import { useSortable } from '@dnd-kit/sortable';
import { useRequest } from 'ahooks';
import { Button, Checkbox, Spin } from 'antd';
import { useEffect, useRef, useState } from 'react';
import { CSS } from '@dnd-kit/utilities';

import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { isNil } from 'lodash-es';
import { CloseOutlined, LockOutlined, UnorderedListOutlined } from '@ant-design/icons';

const columnKeys1 = ['门店', '时间', '服务分', '3mins回复率', '总消耗', '线索总数', '单条线索成本'];
const columnKeys2 = [
  '作品发布总数',
  '投流作品数',
  '粉丝净增长',
  '作品互动数',
  '作品播放数',
  '作品平均播放数',
  '作品点赞数',
  '作品分享数',
  '作品评论数',
  '作品完播率',
  '作品广告消耗',
  '作品线索数',
  '作品线索成本',
  '引导私信线索量',
];

export const TeamOverviewTable = ({ show, projectId }: { show: boolean; projectId?: string }) => {
  const [isEdit, setIsEdit] = useState(false);
  const [editVersion, setEditVersion] = useState(0);
  const [columnData, setColumnData] = useState<ProjectTeamOverviewFieldVO[]>();
  const columnOptions = useRef<ProjectTeamOverviewFieldVO[]>();
  const orderWrapper = useRef<HTMLDivElement>(null);

  const { refresh, loading } = useRequest(() => GetTeamOverviewData({ projectId }), {
    onSuccess(res) {
      setColumnData(
        res.data?.filter((item) => !!item.showFlag).sort((a, b) => a.sort! - b.sort!) || [],
      );
      setEditVersion(editVersion + 1);
      columnOptions.current = res.data;
    },
    refreshDeps: [projectId],
  });

  useEffect(() => {
    setIsEdit(false);
  }, [projectId]);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 5,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    }),
  );

  function handleDragEnd(event: DragEndEvent) {
    if (!isEdit) {
      setIsEdit(true);
    }
    const { active, over } = event;

    if (active.id !== over?.id) {
      setColumnData((items) => {
        const oldIndex = items?.findIndex((item) => item.id == active.id);
        const newIndex = items?.findIndex((item) => item.id == over?.id);
        if (items && !isNil(oldIndex) && !isNil(newIndex)) {
          return arrayMove(items, oldIndex, newIndex);
        }
      });
    }
  }

  return show && projectId ? (
    <Spin spinning={loading}>
      <div className="mt-4 flex w-full pb-28">
        <div className="flex-shrink-0 basis-[75%]">
          <div className="mb-3 w-full bg-[#f9f9f9] p-2 font-semibold">基础指标</div>
          <div className="flex flex-wrap px-2" key={editVersion + 'a'}>
            <div className="mb-3 min-w-[150px]">
              <Checkbox disabled defaultChecked={true}>
                门店
              </Checkbox>
            </div>
            {columnOptions.current
              ?.filter((item) => columnKeys1.includes(item.description || ''))
              .map((item) => (
                <div
                  key={item.id}
                  className="mb-3 min-w-[150px]"
                  style={{ order: item.description?.length }}
                >
                  <Checkbox
                    defaultChecked={!!item.showFlag || false}
                    onChange={(e) => {
                      if (!isEdit) {
                        setIsEdit(true);
                      }
                      if (e.target.checked) {
                        setColumnData((items) => {
                          if (items) {
                            items.unshift(item);

                            return [...items];
                          }
                        });
                        if (orderWrapper.current) {
                          orderWrapper.current.scrollTop = 0;
                        }
                      } else {
                        setColumnData((items) => {
                          if (items) {
                            return items.filter((i) => i.id != item.id);
                          }
                        });
                      }
                    }}
                  >
                    {item.description}
                  </Checkbox>
                </div>
              ))}
          </div>

          <div className="mb-3 w-full bg-[#f9f9f9] p-2 font-semibold">作品指标</div>
          <div className="flex flex-wrap px-2" key={editVersion + 'b'}>
            {columnOptions.current
              ?.filter((item) => columnKeys2.includes(item.description || ''))
              .map((item) => (
                <div
                  key={item.id}
                  className="mb-3 min-w-[150px]"
                  style={{ order: item.description?.length }}
                >
                  <Checkbox
                    defaultChecked={!!item.showFlag || false}
                    onChange={(e) => {
                      if (!isEdit) {
                        setIsEdit(true);
                      }
                      if (e.target.checked) {
                        setColumnData((items) => {
                          if (items) {
                            items.unshift(item);
                            return [...items];
                          }
                        });
                        if (orderWrapper.current) {
                          orderWrapper.current.scrollTop = 0;
                        }
                      } else {
                        setColumnData((items) => {
                          if (items) {
                            return items.filter((i) => i.id != item.id);
                          }
                        });
                      }
                    }}
                  >
                    {item.description}
                  </Checkbox>
                </div>
              ))}
          </div>

          <div className="mb-3 w-full bg-[#f9f9f9] p-2 font-semibold">直播指标</div>
          <div className="flex flex-wrap px-2" key={editVersion + 'c'}>
            {columnOptions.current
              ?.filter((item) => ![...columnKeys1, ...columnKeys2].includes(item.description || ''))
              .map((item) => (
                <div
                  key={item.id}
                  className="mb-3 min-w-[150px]"
                  style={{ order: item.description?.length }}
                >
                  <Checkbox
                    defaultChecked={!!item.showFlag || false}
                    onChange={(e) => {
                      if (!isEdit) {
                        setIsEdit(true);
                      }
                      if (e.target.checked) {
                        setColumnData((items) => {
                          if (items) {
                            items.unshift(item);
                            return [...items];
                          }
                        });
                        if (orderWrapper.current) {
                          orderWrapper.current.scrollTop = 0;
                        }
                      } else {
                        setColumnData((items) => {
                          if (items) {
                            return items.filter((i) => i.id != item.id);
                          }
                        });
                      }
                    }}
                  >
                    {item.description}
                  </Checkbox>
                </div>
              ))}
          </div>
        </div>

        <div className="absolute right-0 h-full w-[24%] flex-shrink-0">
          <div className="w-full bg-[#f9f9f9] p-2 font-semibold">已添加</div>
          <div className="px-4 py-3 text-center text-gray-400">以下指标顺序横向展示至管理端</div>
          <div
            className="flex flex-col overflow-y-auto overflow-x-hidden scroll-smooth px-4"
            style={{ height: 'calc(100% - 150px)' }}
            ref={orderWrapper}
          >
            <div className="mb-2 flex w-full justify-between bg-[#d7d7d7] px-3 py-2">
              <div className="flex items-center">
                <LockOutlined className="mr-1 text-lg" /> 门店
              </div>
            </div>
            {columnData && (
              <DndContext
                sensors={sensors}
                collisionDetection={closestCenter}
                onDragEnd={handleDragEnd}
              >
                <SortableContext items={columnData} strategy={verticalListSortingStrategy}>
                  {columnData.map((item) => (
                    <SortableItem key={item.id} id={item.id}>
                      <div className="mb-2 flex w-full justify-between bg-[#d7d7d7] px-3 py-2">
                        <div className="flex items-center">
                          <UnorderedListOutlined className="mr-2" />
                          {item.description}
                        </div>
                        <div
                          className="w-3"
                          onClick={() => {
                            setColumnData((items) => {
                              return items?.filter((c) => c.id != item.id);
                            });
                            setIsEdit(true);
                            for (const c of columnOptions.current || []) {
                              if (c.id == item.id) {
                                c.showFlag = 0;
                              }
                            }
                            setEditVersion(editVersion + 1);
                          }}
                        >
                          <CloseOutlined />
                        </div>
                      </div>
                    </SortableItem>
                  ))}
                </SortableContext>
              </DndContext>
            )}
          </div>

          {isEdit && (
            <div className="mt-4 flex justify-end gap-2 text-end">
              <Button
                onClick={() => {
                  setIsEdit(false);
                  refresh();
                }}
              >
                取消
              </Button>
              <Button
                type="primary"
                onClick={async () => {
                  const updateList =
                    columnData?.map((c, i) => ({
                      id: c.id,
                      sort: i,
                      showFlag: 1,
                    })) || [];

                  for (const c of columnOptions.current || []) {
                    if (updateList?.some((item) => item.id == c.id)) {
                      continue;
                    } else {
                      updateList?.push({
                        id: c.id,
                        sort: updateList.length,
                        showFlag: 0,
                      });
                    }
                  }

                  await UpdateTeamOverviewData({
                    projectId: projectId,
                    teamOverviewFieldInfoList: updateList,
                  });
                  setIsEdit(false);
                  refresh();
                }}
              >
                确定
              </Button>
            </div>
          )}
        </div>
      </div>
    </Spin>
  ) : null;
};

export function SortableItem(props: { id: string; children?: React.ReactNode }) {
  const { attributes, listeners, setNodeRef, transform, transition } = useSortable({
    id: props.id,
  });

  const style: React.CSSProperties = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  return (
    <div ref={setNodeRef} style={style} {...attributes} {...listeners}>
      {props.children}
    </div>
  );
}
