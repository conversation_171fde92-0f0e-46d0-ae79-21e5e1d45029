import {
  BatchUpdateMonitorWordStatus,
  QualityMonitorWordDelete,
  QualityRuleList,
  QualityRuleUpdate,
  SceneTypeEnum,
  UpdateQualityMonitorSwitch,
} from '@/services/quality';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Checkbox, Space, Popconfirm, message, Switch } from 'antd';
import { CheckboxChangeEvent } from 'antd/es/checkbox';
import { MonitorTable } from '.';
import QualityModal from './qualityModal';

type TextTableProps = {
  projectId?: string;
  dataSourceData?: any[];
  loading?: boolean;
  refresh?: () => void;
};

const TextTable = (props: TextTableProps) => {
  const { projectId, dataSourceData, loading, refresh } = props;

  const { data: ruleListData, refresh: refreshRuleListData } = useRequest(() =>
    QualityRuleList({ projectId }),
  );

  const textSwitchState = dataSourceData?.some((item) => item.isDisabled === 0);

  const onChangeTextSwitch = async (checked: boolean) => {
    await BatchUpdateMonitorWordStatus({
      projectId,
      isDisabled: checked ? 0 : 1,
      sceneType: SceneTypeEnum.TEXT,
    });
    refresh?.();
  };

  const textColumns: ProColumns<MonitorTable>[] = [
    {
      title: '规范类型',
      dataIndex: 'standardType',
      align: 'center',
      width: 100,
      valueType: 'select',
      valueEnum: {
        1: '品牌',
        2: '平台',
      },
    },
    {
      title: '分类',
      dataIndex: 'name',
      align: 'center',
      width: 130,
    },
    {
      title: '添加类型',
      dataIndex: 'type',
      align: 'center',
      width: 100,
      valueEnum: {
        1: '默认',
        2: '自定义',
      },
    },
    {
      title: '疑似词',
      dataIndex: 'suspectedWord',
      align: 'center',
      ellipsis: true,
      tooltip: '疑似词只做高亮标识，需要人工进行二次复检',
    },
    {
      title: '敏感词',
      dataIndex: 'sensitiveWord',
      align: 'center',
      ellipsis: true,
      tooltip: '敏感词为最终确认违规类型，直接进行标记确认为违规',
    },
    {
      title: '操作',
      valueType: 'option',
      hideInSearch: true,
      align: 'center',
      fixed: 'right',
      width: 180,
      render: (_, record) => {
        return (
          <Space>
            {record.type === 2 && (
              <Popconfirm
                title={'是否确认删除，删除后，历史已标记和确认的违规内容也会跟随删除且无法找回。'}
                onConfirm={async () => {
                  const res = await QualityMonitorWordDelete({ id: record.id });
                  res.code === 0 ? message.success('删除成功') : message.error('删除失败');
                  refresh?.();
                }}
                key={'delete'}
              >
                <a>删除</a>
              </Popconfirm>
            )}
            <QualityModal
              isEdit={true}
              sceneType={SceneTypeEnum.TEXT}
              projectId={projectId}
              data={record}
              refresh={refresh}
            />
            <Switch
              value={!record.isDisabled}
              onChange={async (checked) => {
                await UpdateQualityMonitorSwitch({
                  id: record.id,
                  isDisabled: checked ? 0 : 1,
                  projectId,
                });
                refresh?.();
              }}
            />
          </Space>
        );
      },
    },
  ];

  return (
    <ProTable
      params={{ projectId }}
      headerTitle={
        <Space style={{ marginRight: '10px' }}>
          <div>文字标识</div>
          <div style={{ marginLeft: '10px' }}>
            {ruleListData?.map((item) => (
              <Checkbox
                key={item.id}
                checked={item.status === 1}
                onChange={async (e: CheckboxChangeEvent) => {
                  const checkedStatue = e.target.checked;
                  await QualityRuleUpdate({
                    id: item.id,
                    status: checkedStatue ? 1 : 0,
                    projectId,
                  });
                  refreshRuleListData();
                }}
              >
                {item.name}
              </Checkbox>
            ))}
          </div>
          <Switch value={textSwitchState} onChange={onChangeTextSwitch} />
        </Space>
      }
      tooltip="文字标识：标题、语音中涉及到的文字会自动识别。疑似词只做高亮标识，需要人工进行二次复检。敏感词为最终确认违规类型，系统直接进行标记确认为违规"
      columns={textColumns}
      rowKey="name"
      search={false}
      options={false}
      ghost
      loading={loading}
      dataSource={dataSourceData}
      toolBarRender={() => [
        <QualityModal
          key={'add'}
          sceneType={SceneTypeEnum.TEXT}
          projectId={projectId}
          refresh={refresh}
        />,
      ]}
      pagination={{
        defaultPageSize: 10,
        hideOnSinglePage: true,
      }}
    />
  );
};

export default TextTable;
