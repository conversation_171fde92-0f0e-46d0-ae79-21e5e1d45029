import { SWITCHER_DICT } from '@/hooks/fg/useProjectFeature';
import { FunctionCode, SystemFunctionResult, UpdateSystemFunction } from '@/services/system';
import { clearCache } from 'ahooks';
import { Switch } from 'antd';

type BarrageAnalysisTableProps = {
  projectId?: string;
  featureDict?: Record<FunctionCode, SystemFunctionResult> | undefined;
  cacheKey: string;
  refresh: () => void;
};

/** 功能控制开关 */
const ProjectFeatureSwitchers = (props: BarrageAnalysisTableProps) => {
  const { projectId, featureDict, cacheKey, refresh } = props;

  return (
    <div className="flex flex-col">
      {featureDict &&
        Object.entries(featureDict).map(([key, item]) => {
          if (Object.keys(SWITCHER_DICT).includes(key)) {
            return (
              <div
                key={key}
                style={{
                  order: key === FunctionCode.TeamOverview ? 99 : undefined,
                  fontSize: '16px',
                  fontWeight: '500',
                  marginBlock: '10px',
                }}
              >
                {item.functionName}
                <Switch
                  style={{ marginLeft: '10px' }}
                  key={`${projectId}-${key}-${item.isDisabled}`}
                  defaultChecked={!item.isDisabled}
                  onChange={async (value) => {
                    await UpdateSystemFunction({
                      projectId,
                      functionCode: key as FunctionCode,
                      isDisabled: value ? 0 : 1,
                    });
                    clearCache(cacheKey);
                    refresh();
                  }}
                />
              </div>
            );
          }
        })}
    </div>
  );
};

export default ProjectFeatureSwitchers;
