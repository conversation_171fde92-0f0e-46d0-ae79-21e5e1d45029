import { QualityMonitorWordList, SceneTypeEnum } from '@/services/quality';
import { FunctionCode, GetSystemFunction } from '@/services/system';
import { ProCard } from '@ant-design/pro-components';
import { useParams, useRequest } from '@umijs/max';
import AfkTable from './afkTable';
import AnchorImageTable from './anchorImageTable';
import BehaviorTable from './behaviorTable';
import CommentTable from './commentTable';
import TextTable from './textTable';
import ProjectFeatureSwitchers from './projectFeatureSwitchers';
import useProjectFeature from '@/hooks/fg/useProjectFeature';
import { TeamOverviewTable } from './teamOverviewTable';
import { Spin } from 'antd';

export type MonitorTable = {
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 创建人
   */
  creator?: string;
  /**
   * 说明
   */
  description?: string;
  /**
   * 主键 id
   */
  id: number | string;
  /**
   * 违规词库
   */
  suspectedWord?: string;
  /**
   * 修改人
   */
  modifier?: string;
  /**
   * 分类-中文名
   */
  name?: string;
  /**
   * 项目id
   */
  projectId?: string;
  /**
   * 场景类型 1-文字标识 2-行为标识 3-弹幕标识
   */
  sceneType?: number;
  /**
   * 敏感词库
   */
  sensitiveWord?: string;
  type?: number;
  /**
   * 更新时间
   */
  updateTime?: string;
  /**
   * 是否禁用 0-正常 1-禁用
   */
  isDisabled?: number;
  /**
   * 规范类型
   */
  standardType?: number;
};

const QualityRule = () => {
  const { projectId } = useParams();

  const {
    data: monitorWordData,
    loading,
    refresh,
  } = useRequest(
    () => {
      if (!projectId) {
        return Promise.reject();
      }
      return QualityMonitorWordList({ projectId });
    },
    {
      refreshDeps: [projectId],
      ready: !!projectId,
    },
  );

  const {
    featureDict,
    cacheKey,
    refresh: refreshProjectFeature,
    isLoading,
  } = useProjectFeature({
    projectId,
  });

  const conversionMonitorWordData = monitorWordData?.map((item) => {
    const suspectedWord = item.suspectedWord?.join(',');
    const sensitiveWord = item.sensitiveWord?.join(',');
    return {
      ...item,
      suspectedWord,
      sensitiveWord,
    };
  });

  const textTableDataSource = conversionMonitorWordData?.filter(
    (item) => item.sceneType === SceneTypeEnum.TEXT,
  );

  const beHaviorTableDataSource = conversionMonitorWordData?.filter(
    (item) => item.sceneType === SceneTypeEnum.BEHAVIOR,
  );

  const commentTableDataSource = conversionMonitorWordData?.filter(
    (item) => item.sceneType === SceneTypeEnum.COMMENT,
  );

  const { data: systemFunctionData, refresh: refreshSystemFunction } = useRequest(
    () => GetSystemFunction({ projectId }),
    {
      ready: !!projectId,
    },
  );

  const liveOcrState = systemFunctionData?.find(
    (item) => item.functionCode === FunctionCode.LiveOCRRecognition,
  )?.isDisabled;

  const anchorOcrState = systemFunctionData?.find(
    (item) => item.functionCode === FunctionCode.AnchorImageRecognition,
  )?.isDisabled;

  const afkFunctionState = systemFunctionData?.find(
    (item) => item.functionCode === FunctionCode.EmptyBroadcastRecognition,
  )?.isDisabled;

  return (
    <ProCard>
      <TextTable
        projectId={projectId}
        dataSourceData={textTableDataSource}
        loading={loading}
        refresh={refresh}
      />

      <BehaviorTable
        projectId={projectId}
        dataSourceData={beHaviorTableDataSource}
        loading={loading}
        refresh={refresh}
        enableOcr={!liveOcrState}
        refreshSystemFunction={refreshSystemFunction}
      />

      <CommentTable
        projectId={projectId}
        dataSourceData={commentTableDataSource}
        loading={loading}
        refresh={refresh}
      />

      <AnchorImageTable
        projectId={projectId}
        enableOcr={!anchorOcrState}
        refreshSystemFunction={refreshSystemFunction}
      />

      {/* <PriceTable projectId={projectId} /> */}

      <AfkTable
        projectId={projectId}
        afkFunctionState={!afkFunctionState}
        refreshSystemFunction={refreshSystemFunction}
      />

      <ProjectFeatureSwitchers
        projectId={projectId}
        featureDict={featureDict}
        cacheKey={cacheKey}
        refresh={refreshProjectFeature}
      />

      {featureDict?.[FunctionCode.TeamOverview] && (
        <Spin spinning={isLoading}>
          <TeamOverviewTable
            show={!featureDict?.[FunctionCode.TeamOverview]?.isDisabled}
            projectId={projectId}
          />
        </Spin>
      )}
    </ProCard>
  );
};

export default QualityRule;
