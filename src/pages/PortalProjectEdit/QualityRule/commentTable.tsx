import {
  BatchUpdateMonitorWordStatus,
  QualityMonitorWordDelete,
  SceneTypeEnum,
  UpdateQualityMonitorSwitch,
} from '@/services/quality';
import { ProColumns, ProTable } from '@ant-design/pro-components';
import { Space, Popconfirm, message, Switch } from 'antd';
import { MonitorTable } from '.';
import QualityModal from './qualityModal';

type CommentTableProps = {
  projectId?: string;
  dataSourceData?: any[];
  loading?: boolean;
  refresh?: () => void;
};

const CommentTable = (props: CommentTableProps) => {
  const { projectId, dataSourceData, loading, refresh } = props;

  const commentSwitchState = dataSourceData?.some((item) => item.isDisabled === 0);

  const onChangeCommentSwitch = async (checked: boolean) => {
    await BatchUpdateMonitorWordStatus({
      projectId,
      isDisabled: checked ? 0 : 1,
      sceneType: SceneTypeEnum.COMMENT,
    });
    refresh?.();
  };

  const commentColumns: ProColumns<MonitorTable>[] = [
    {
      title: '规范类型',
      dataIndex: 'standardType',
      align: 'center',
      width: 100,
      valueType: 'select',
      valueEnum: {
        1: '品牌',
        2: '平台',
      },
    },
    {
      title: '分类',
      dataIndex: 'name',
      align: 'center',
      width: 130,
    },
    {
      title: '添加类型',
      dataIndex: 'type',
      align: 'center',
      width: 100,
      valueEnum: {
        1: '默认',
        2: '自定义',
      },
    },
    {
      title: '疑似词',
      dataIndex: 'suspectedWord',
      align: 'center',
      tooltip: '疑似词只做高亮标识，需要人工进行二次复检',
    },
    {
      title: '操作',
      valueType: 'option',
      hideInSearch: true,
      align: 'center',
      fixed: 'right',
      width: 180,
      render: (_, record) => {
        return (
          <Space>
            {record.type === 2 && (
              <Popconfirm
                title={'是否确认删除'}
                onConfirm={async () => {
                  const res = await QualityMonitorWordDelete({ id: record.id });
                  res.code === 0 ? message.success('删除成功') : message.error('删除失败');
                  refresh?.();
                }}
                key={'delete'}
              >
                <a>删除</a>
              </Popconfirm>
            )}
            <QualityModal
              isEdit={true}
              sceneType={SceneTypeEnum.COMMENT}
              projectId={record.projectId}
              data={record}
              refresh={refresh}
            />
            <Switch
              value={!record.isDisabled}
              onChange={async (checked) => {
                await UpdateQualityMonitorSwitch({
                  id: record.id,
                  isDisabled: checked ? 0 : 1,
                  projectId,
                });
                refresh?.();
              }}
            />
          </Space>
        );
      },
    },
  ];

  return (
    <ProTable
      headerTitle={
        <Space style={{ marginRight: '10px' }}>
          <div>弹幕标识</div>
          <Switch value={commentSwitchState} onChange={onChangeCommentSwitch} />
        </Space>
      }
      tooltip="弹幕词系统会自动进行识别，但只做高亮提示，不会最终确认为违规，需要人工进行二次校验"
      columns={commentColumns}
      rowKey="name"
      search={false}
      options={false}
      ghost
      loading={loading}
      dataSource={dataSourceData}
      toolBarRender={() => [
        <QualityModal
          key={'add'}
          sceneType={SceneTypeEnum.COMMENT}
          projectId={projectId}
          refresh={refresh}
        />,
      ]}
      pagination={{
        defaultPageSize: 10,
        hideOnSinglePage: true,
      }}
    />
  );
};

export default CommentTable;
