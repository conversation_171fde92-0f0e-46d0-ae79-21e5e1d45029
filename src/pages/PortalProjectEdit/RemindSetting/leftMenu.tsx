import { DeleteNotifyRobot, GetNotifyConfigList, NotifyConfigList } from '@/services/notify';
import { CopyOutlined, DeleteOutlined, EditOutlined } from '@ant-design/icons';
import { useParams, useRequest } from '@umijs/max';
import { Button, GetProp, Menu, MenuProps, Tooltip } from 'antd';
import { useState } from 'react';
import NoticeModalForm from '../noticeModal';

type MenuItemSelect = GetProp<MenuProps, 'onSelect'>;

// 总部type为3
const Type = 3;

const renderMenuActions = (
  data: NotifyConfigList,
  activeKey?: string,
  refresh?: () => void,
  projectId?: string,
  noticeConfigList?: NotifyConfigList[],
  actionType?: number,
  setActiveKey?: (value: string) => void,
  onChange?: (value: string) => void,
) => {
  return (
    <>
      {data.robotName}
      {data.id.toString() === activeKey && (
        <div style={{ float: 'right' }}>
          {data.notifyType === 2 && (
            <NoticeModalForm
              trigger={
                <Tooltip title="创建副本">
                  <CopyOutlined />
                </Tooltip>
              }
              noticeList={noticeConfigList || []}
              refresh={refresh}
              type={Type}
              projectId={projectId}
              copyNotifyRobotId={data.id}
              actionType={actionType}
            />
          )}
          {/* TODO 次版本不能修改个人微信 */}
          {data.notifyType === 2 && (
            <NoticeModalForm
              trigger={
                <Tooltip title="编辑修改">
                  <EditOutlined />
                </Tooltip>
              }
              noticeList={noticeConfigList || []}
              refresh={refresh}
              type={Type}
              projectId={projectId}
              initValue={noticeConfigList?.find((item) => item.id.toString() === activeKey)}
              actionType={actionType}
            />
          )}
          {data.objectType === 2 && (
            <Tooltip title="删除">
              <DeleteOutlined
                onClick={async () => {
                  const currentIndex = noticeConfigList?.findIndex((item) => item.id === data.id);
                  await DeleteNotifyRobot({ notifyRobotId: data.id });
                  refresh?.();
                  if (!noticeConfigList || noticeConfigList.length === 0) return;
                  const nextItem =
                    currentIndex !== undefined && currentIndex > 0
                      ? noticeConfigList[currentIndex - 1]
                      : noticeConfigList[0];
                  const newActiveKey = nextItem.id.toString();
                  setActiveKey?.(newActiveKey);
                  onChange?.(newActiveKey);
                }}
              />
            </Tooltip>
          )}
        </div>
      )}
    </>
  );
};

type LeftMenuProps = {
  onChange: (value: string) => void;
  actionType: number;
};

export enum DefaultMenuId {
  DefaultUserWeiXin = -1,
  DefaultOfficeGroup = -2,
}

const LeftMenu = (props: LeftMenuProps) => {
  const { onChange, actionType } = props;
  const { projectId } = useParams();
  const [activeKey, setActiveKey] = useState<string>();
  const [isFirstSetActiveKey, setIsFirstSetActiveKey] = useState<boolean>(false);

  const { data: noticeConfigList, refresh: refreshNoticeConfigList } = useRequest(
    () => {
      if (!projectId) {
        return Promise.reject();
      }
      return GetNotifyConfigList({ projectId, actionType });
    },
    {
      onSuccess: (res) => {
        // 首次接口成功后设置activeKey为第一个的notifyRobotId
        if (res && res.length > 0 && !isFirstSetActiveKey) {
          const firstNotifyRobotId = res[0].id.toString();
          setActiveKey(firstNotifyRobotId);
          onChange?.(firstNotifyRobotId);
          setIsFirstSetActiveKey(true);
        }
      },
      refreshDeps: [projectId],
      ready: !!projectId,
    },
  );

  const menuSelect: MenuItemSelect = ({ key }) => {
    setActiveKey(key);
    onChange?.(key);
  };

  const renderMenuItems = (noticeConfigList: NotifyConfigList[]) => {
    return noticeConfigList.map((item) => ({
      key: item.id,
      label: renderMenuActions(
        item,
        activeKey,
        refreshNoticeConfigList,
        projectId,
        noticeConfigList,
        actionType,
        setActiveKey,
        onChange,
      ),
    }));
  };

  return (
    <>
      <NoticeModalForm
        trigger={
          <Button type="primary" style={{ margin: '10px' }}>
            添加推送
          </Button>
        }
        noticeList={noticeConfigList || []}
        refresh={refreshNoticeConfigList}
        type={Type}
        projectId={projectId}
        actionType={actionType}
      />
      <Menu
        selectedKeys={[activeKey || DefaultMenuId.DefaultUserWeiXin.toString()]}
        mode="inline"
        items={renderMenuItems(noticeConfigList || [])}
        onSelect={menuSelect}
      />
    </>
  );
};

export default LeftMenu;
