import { ProCard } from '@ant-design/pro-components';
import { useState } from 'react';
import LeftMenu, { DefaultMenuId } from '../leftMenu';
import RightTable from './rightTable';

const DailyRemindSetting = () => {
  const [notifyRobotId, setNotifyRobotId] = useState<string>();

  const mergedData = [
    {
      id: 'quality_daily_report',
      remindName: '质检日报链接',
      bizModel: '质检',
    },
    {
      id: 'chat_daily_report',
      remindName: '私信日报链接',
      bizModel: '私信',
    },
    ...(Number(notifyRobotId) === DefaultMenuId.DefaultUserWeiXin
      ? [
          {
            id: 'activity_daily_report',
            remindName: '激励活动链接',
            bizModel: '激励活动',
          },
        ]
      : []),
    {
      id: 'daily_summary_report',
      remindName: '日报小结海报',
      bizModel: '质检+私信',
    },
  ];

  const onChangeNotifyRobotId = (value: string) => {
    setNotifyRobotId(value);
  };

  return (
    <ProCard split="vertical">
      <ProCard colSpan="300px" ghost>
        <LeftMenu onChange={onChangeNotifyRobotId} actionType={2} />
      </ProCard>
      <ProCard>
        <RightTable robotId={notifyRobotId} mergedData={mergedData} actionType={2} />
      </ProCard>
    </ProCard>
  );
};

export default DailyRemindSetting;
