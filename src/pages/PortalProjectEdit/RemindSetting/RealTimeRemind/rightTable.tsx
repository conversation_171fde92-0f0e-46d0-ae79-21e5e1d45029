import {
  ConfigItem,
  GetNotifyConfigQuery,
  NotifyConfigData,
  UpdateNotifyConfig,
  UpdateNotifyPushTime,
} from '@/services/notify';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { useParams, useRequest } from '@umijs/max';
import { InputNumber, message, Select, Space, Switch, TimePicker } from 'antd';
import { RangePickerProps } from 'antd/es/date-picker';
import dayjs from 'dayjs';
import { Key, useRef, useState } from 'react';
import { MergeDataType } from '.';
import { DefaultMenuId } from '../leftMenu';

type RightTableProps = {
  robotId?: string;
  mergedData?: MergeDataType[];
  actionType: number;
};

type NotifyTableData = MergeDataType & ConfigItem;

// 将configItemList的数组结构转化成键为code,其余数据为配置项的形式,这样mergedData中就可以直接通过code获取到配置项
const transformNotifyConfigData = (data?: NotifyConfigData) => {
  if (!data?.configItemList) {
    return {};
  }
  return data?.configItemList?.reduce((prev, curr) => {
    prev[curr.code as string] = curr;
    return prev;
  }, {} as Record<string, ConfigItem>);
};

const RightTable = (props: RightTableProps) => {
  const { robotId, mergedData, actionType } = props;
  const { projectId } = useParams();
  const actionRef = useRef<ActionType>();
  const [selectedRowKeys, setSelectedRowKeys] = useState<Key[]>([]);
  const isDefaultMenuId =
    Number(robotId) === DefaultMenuId.DefaultUserWeiXin ||
    Number(robotId) === DefaultMenuId.DefaultOfficeGroup;
  const notifyType = Number(robotId) === DefaultMenuId.DefaultUserWeiXin ? 1 : 2;
  const objectType = isDefaultMenuId ? 1 : 2;

  const { data: notifyConfigData, refresh: refreshNotifyConfig } = useRequest(
    () => {
      if (!projectId) {
        return Promise.reject();
      }
      return GetNotifyConfigQuery({
        projectId,
        id: robotId,
        notifyType,
        objectType,
        actionType,
      });
    },
    {
      refreshDeps: [projectId, robotId],
      ready: !!projectId,
    },
  );

  const notifyConfigCodeKeyMap = transformNotifyConfigData(notifyConfigData);

  // 后端不填默认值 前端来填
  const mergeDataSource = mergedData?.map((item) => {
    const configItem = notifyConfigCodeKeyMap[item.id];
    if (configItem) {
      return {
        ...item,
        ...configItem,
      };
    } else {
      return {
        ...item,
        isDisable: 1,
        intervalType: 1,
        pushCoefficient: 1,
      };
    }
  }) as NotifyTableData[];

  const updateConfig = async (record: NotifyTableData, updatedFields: Partial<NotifyTableData>) => {
    const { id, isDisable, intervalType, pushCoefficient } = record;
    const configItem = {
      code: id,
      isDisable: updatedFields.isDisable ?? isDisable,
      intervalType: updatedFields.intervalType ?? intervalType,
      pushCoefficient: updatedFields.pushCoefficient ?? pushCoefficient,
    };

    const updateConfigData = {
      id: robotId,
      projectId,
      notifyType,
      objectType,
      actionType,
      configItemList: [configItem],
    };
    await UpdateNotifyConfig(updateConfigData);
    refreshNotifyConfig();
  };

  const handleSwitchChange = async (record: NotifyTableData) => {
    await updateConfig(record, {
      isDisable: record.isDisable === 0 ? 1 : 0,
    });
  };

  const handleIntervalTypeChange = async (record: NotifyTableData, changeValue: string) => {
    await updateConfig(record, {
      intervalType: Number(changeValue),
    });
  };

  const handlePushCoefficientChange = async (
    record: NotifyTableData,
    changeValue: number | null,
  ) => {
    if (changeValue) {
      await updateConfig(record, {
        pushCoefficient: changeValue,
      });
    }
  };

  const columns: ProColumns<NotifyTableData>[] = [
    {
      dataIndex: 'id',
      hidden: true,
    },
    {
      title: '提醒名称',
      dataIndex: 'remindName',
      align: 'center',
    },
    {
      title: '提醒类型',
      dataIndex: 'remindType',
      align: 'center',
    },
    {
      title: '业务模块',
      dataIndex: 'bizModel',
      align: 'center',
    },
    {
      title: '提醒频率',
      dataIndex: 'intervalType',
      align: 'center',
      valueEnum: {
        1: '实时',
        2: '每小时',
        3: '每天',
      },
      render: (_, record) => {
        return (
          <Select
            style={{ width: 120 }}
            onChange={(value) => handleIntervalTypeChange(record, value)}
            defaultValue={'1'}
            value={String(record.intervalType)}
            options={[
              { value: '1', label: '实时' },
              { value: '2', label: '每小时' },
              { value: '3', label: '每天' },
            ]}
          />
        );
      },
    },
    {
      title: '推送系数',
      dataIndex: 'pushCoefficient',
      align: 'center',
      render: (_, record) => {
        if (record.intervalType === 1) {
          return (
            <Space>
              第
              <InputNumber
                min={1}
                max={3}
                defaultValue={1}
                style={{ width: 35 }}
                controls={false}
                value={record.pushCoefficient}
                onChange={(value) => handlePushCoefficientChange(record, value)}
              />
              套
            </Space>
          );
        } else {
          return '-';
        }
      },
    },
    {
      title: '是否启用',
      align: 'center',
      dataIndex: 'isDisable',
      render: (_, record) => {
        return (
          <Switch checked={record.isDisable === 0} onChange={() => handleSwitchChange(record)} />
        );
      },
    },
  ];

  const handleTimeChange = async (date: RangePickerProps['value'], dateString: string[]) => {
    await UpdateNotifyPushTime({
      id: robotId,
      pushStartTime: dateString[0],
      pushEndTime: dateString[1],
      notifyType,
      objectType,
      projectId,
      actionType,
    });
    refreshNotifyConfig();
  };

  // 批量操作
  const batchUpdateConfig = async (
    selectRowKeys: Key[],
    mergeDataSource: NotifyTableData[],
    configUpdates: (item: NotifyTableData) => Partial<ConfigItem>,
  ) => {
    if (selectRowKeys.length === 0) {
      message.error('当前未勾选任何行');
      return;
    }
    const ids = new Set(selectRowKeys);
    const filterChangeData = mergeDataSource.filter((item) => ids.has(item.id));
    const updateConfigItem = filterChangeData.map(configUpdates);
    const updateConfigData = {
      id: robotId,
      projectId,
      notifyType,
      objectType,
      actionType,
      configItemList: updateConfigItem,
    };
    await UpdateNotifyConfig(updateConfigData);
    refreshNotifyConfig();
  };

  const handleBatchSwitch = async (
    selectRowKeys: Key[],
    mergeDataSource: NotifyTableData[],
    enable: boolean,
  ) => {
    await batchUpdateConfig(selectRowKeys, mergeDataSource, (item) => ({
      code: item.id,
      intervalType: item.intervalType,
      isDisable: enable ? 0 : 1,
      pushCoefficient: item.pushCoefficient,
    }));
  };

  const handleBatchIntervalTypeChange = async (
    selectRowKeys: Key[],
    mergeDataSource: NotifyTableData[],
    changeIntervalType: number,
  ) => {
    await batchUpdateConfig(selectRowKeys, mergeDataSource, (item) => ({
      code: item.id,
      intervalType: changeIntervalType,
      isDisable: item.isDisable,
      pushCoefficient: item.pushCoefficient,
    }));
  };

  if (!robotId) return null;

  return (
    <ProTable<NotifyTableData>
      columns={columns}
      rowKey="id"
      headerTitle={
        <Space>
          <div style={{ fontSize: 14, fontWeight: '500' }}>提醒时间段:</div>
          <TimePicker.RangePicker
            allowClear={false}
            value={[
              dayjs(notifyConfigData?.pushStartTime, 'HH:mm:ss'),
              dayjs(notifyConfigData?.pushEndTime, 'HH:mm:ss'),
            ]}
            needConfirm
            onChange={handleTimeChange}
          />
        </Space>
      }
      dataSource={mergeDataSource}
      actionRef={actionRef}
      rowSelection={{
        alwaysShowAlert: true,
        onChange: (selectedRowKeys) => {
          setSelectedRowKeys(selectedRowKeys);
        },
      }}
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      tableAlertRender={({ selectedRowKeys, selectedRows, onCleanSelected }) => {
        return (
          <Space size={16} style={{ width: '200px' }}>
            已选 {selectedRowKeys.length} 项<a onClick={onCleanSelected}>取消选择</a>
          </Space>
        );
      }}
      tableAlertOptionRender={() => {
        return (
          <Space size={16} wrap>
            <Space>
              提醒频率:
              <a onClick={() => handleBatchIntervalTypeChange(selectedRowKeys, mergeDataSource, 1)}>
                实时
              </a>
              <a onClick={() => handleBatchIntervalTypeChange(selectedRowKeys, mergeDataSource, 2)}>
                每小时
              </a>
              <a onClick={() => handleBatchIntervalTypeChange(selectedRowKeys, mergeDataSource, 3)}>
                每天
              </a>
            </Space>
            <Space>
              是否启用:
              <a onClick={() => handleBatchSwitch(selectedRowKeys, mergeDataSource, true)}>
                全部启用
              </a>
              <a onClick={() => handleBatchSwitch(selectedRowKeys, mergeDataSource, false)}>
                全部禁用
              </a>
            </Space>
          </Space>
        );
      }}
      scroll={{ x: 'max-content' }}
      options={false}
      search={false}
      pagination={false}
    />
  );
};

export default RightTable;
