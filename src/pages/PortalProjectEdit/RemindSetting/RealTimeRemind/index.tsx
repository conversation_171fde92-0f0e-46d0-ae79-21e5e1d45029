import {
  GetAnchorImageViolationSelect,
  QualityMonitorWordList,
  SceneTypeEnum,
} from '@/services/quality';
import { ProCard } from '@ant-design/pro-components';
import { useParams } from '@umijs/max';
import { useAsyncEffect } from 'ahooks';
import { useState } from 'react';
import LeftMenu from '../leftMenu';
import RightTable from './rightTable';

const remindNameMap: Record<SceneTypeEnum, string> = {
  [SceneTypeEnum.TEXT]: '文字标识',
  [SceneTypeEnum.BEHAVIOR]: '行为标识',
  [SceneTypeEnum.COMMENT]: '弹幕标识',
};

export type MergeDataType = {
  id: number | string;
  remindName: string;
  remindType: string;
  bizModel: string;
};

const RealTimeRemindSetting = () => {
  const { projectId } = useParams();
  const [notifyRobotId, setNotifyRobotId] = useState<string>();
  const [mergedData, setMergedData] = useState<MergeDataType[]>();

  useAsyncEffect(async () => {
    if (!projectId) return;
    const [monitorWordData, anchorImageData] = await Promise.all([
      QualityMonitorWordList({ projectId }).then((res) => res.data),
      GetAnchorImageViolationSelect({ projectId }).then((res) => res.data),
    ]);

    if (monitorWordData && anchorImageData) {
      const formatMonitorWordData = monitorWordData
        .filter(({ sceneType }) => sceneType !== SceneTypeEnum.COMMENT)
        .map(({ id, sceneType, name }) => ({
          id,
          remindName: remindNameMap[sceneType],
          remindType: name,
          bizModel: '质检',
        }));
      const formatAnchorImageData = anchorImageData.map(({ value, name }) => ({
        id: value,
        remindName: '主播形象',
        remindType: name,
        bizModel: '质检',
      }));

      setMergedData([
        ...formatMonitorWordData,
        ...formatAnchorImageData,
        {
          id: 'quality_live_afk',
          remindName: '空挂播识别',
          remindType: '空挂播',
          bizModel: '质检',
        },
        {
          id: 'chat_reminder',
          remindName: '私信实时提醒',
          remindType: '私信提醒',
          bizModel: '私信',
        },
      ]);
    }
  }, [projectId]);

  const onChangeNotifyRobotId = (value: string) => {
    setNotifyRobotId(value);
  };

  return (
    <ProCard split="vertical">
      <ProCard colSpan="300px" ghost>
        <LeftMenu onChange={onChangeNotifyRobotId} actionType={1} />
      </ProCard>
      <ProCard>
        <RightTable robotId={notifyRobotId} mergedData={mergedData} actionType={1} />
      </ProCard>
    </ProCard>
  );
};

export default RealTimeRemindSetting;
