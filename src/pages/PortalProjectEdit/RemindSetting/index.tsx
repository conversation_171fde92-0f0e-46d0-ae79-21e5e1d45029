import { Tabs, TabsProps } from 'antd';
import DailyRemindSetting from './DailyRemind';
import RealTimeRemindSetting from './RealTimeRemind';

const items: TabsProps['items'] = [
  {
    key: 'realtime-remind',
    label: '实时提醒',
    children: <RealTimeRemindSetting />,
  },
  {
    key: 'daily-remind',
    label: '日报',
    children: <DailyRemindSetting />,
  },
];

const RemindSetting = () => {
  return <Tabs defaultActiveKey="realtime-remind" items={items} />;
};

export default RemindSetting;
