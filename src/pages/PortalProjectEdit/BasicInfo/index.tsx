import { spiderSyncUserTags } from '@/models/store';
import ImageUpload, { buildUploadFileFn } from '@/pages/PortalProjectList/ImageUpload';
import CommonTagSelectionModal from '@/pages/TagList/Components/selectModal';
import { PlatForm } from '@/utils/platform';
import {
  DeleteProjectCompetition,
  ProjectInfo,
  QueryProjectCompetitionTag,
  UpdateProject,
} from '@/services/project';
import { proTableRequestAdapter } from '@/utils';
import { InfoCircleOutlined } from '@ant-design/icons';
import {
  ActionType,
  EditableFormInstance,
  EditableProTable,
  FooterToolbar,
  ProCard,
  ProColumns,
  ProForm,
  ProFormCheckbox,
  ProFormDigit,
  ProFormInstance,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { useParams } from '@umijs/max';

import {
  Button,
  Col,
  Flex,
  Form,
  Image,
  message,
  Popconfirm,
  Row,
  Space,
  Tag,
  Tooltip,
  Spin,
} from 'antd';

import { useAtomValue } from 'jotai';
import { pick } from 'lodash-es';
import { useEffect, useRef, useState } from 'react';
import { IndustryType } from '@/utils/const';

type FormItemData = {
  competitionDate: any;
  iconUrl: string;
};

const BasicInfo = ({ projectInfo }: { projectInfo?: ProjectInfo }) => {
  const { tagId, projectId } = useParams();
  const formRef = useRef<ProFormInstance>();
  const actionRef = useRef<ActionType>();
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);

  const [showTagSelectModal, setShowTagSelectModal] = useState(false);
  const tagGroups = useAtomValue(spiderSyncUserTags);
  const editorFormRef = useRef<EditableFormInstance>();

  useEffect(() => {
    if (formRef.current?.setFieldsValue && projectInfo) {
      formRef.current?.setFieldsValue({ ...projectInfo });
      return;
    }
  }, [projectInfo]);

  const competitionTable: ProColumns<any>[] = [
    {
      title: '竞品ID',
      dataIndex: 'competitionId',
      hideInSearch: true,
      hideInTable: true,
    },
    {
      title: '竞品名称',
      dataIndex: 'competitionName',
      hideInSearch: true,
      hideInTable: false,
      formItemProps: () => {
        return {
          rules: [{ required: true, message: '此项为必填项' }],
        };
      },
    },
    {
      title: '标签ID',
      dataIndex: 'tagId',
      hideInSearch: true,
      hideInTable: true,
    },
    {
      title: '竞品爬虫标签',
      dataIndex: 'name',
      hideInSearch: false,
      hideInTable: false,
      render: (text) => {
        return <Tag>{text}</Tag>;
      },
      renderFormItem: (item: any) => {
        return (
          <CommonTagSelectionModal
            mode="single"
            visible={showTagSelectModal}
            setVisible={setShowTagSelectModal}
            data={tagGroups}
            value={item}
            onFinish={(value) => {
              editorFormRef.current?.setRowData?.(item.entry.competitionId, {
                name: value[0].name,
                tagId: value[0].id,
              });
            }}
          />
        );
      },
    },
    {
      title: '爬虫账号数',
      dataIndex: 'spiderCount',
      hideInSearch: true,
      hideInTable: false,
      editable: false,
    },
    {
      title: '上传图标',
      dataIndex: 'icon',
      hideInSearch: true,
      hideInTable: false,
      render: (_, record) => {
        if (record.icon) {
          return <Image src={record.icon} width={100} height={100} />;
        }
      },
      renderFormItem: () => {
        return (
          <ImageUpload
            customRequest={async (req) => {
              await buildUploadFileFn()(req, formRef, 'icon');
            }}
          />
        );
      },
    },
    {
      title: '操作',
      valueType: 'option',
      hideInSearch: true,
      hideInTable: false,
      render: (text, record, _, action) => [
        <Space key="action">
          <Popconfirm
            title={'是否确认删除'}
            onConfirm={async () => {
              await DeleteProjectCompetition([record.competitionId]);
              message.success('删除成功');
              await action?.reload();
            }}
            key={'delete'}
          >
            <a>删除</a>
          </Popconfirm>
          <a
            onClick={() => {
              action?.startEditable?.(record.competitionId);
            }}
          >
            编辑项目
          </a>
        </Space>,
      ],
    },
  ];
  if (!projectId) {
    return (
      <div style={{ width: '100%', textAlign: 'center' }}>
        <Spin />
      </div>
    );
  }
  return (
    <ProForm<FormItemData>
      formRef={formRef}
      layout="inline"
      onFinish={async (values) => {
        const competitionTagList = values.competitionDate.map((item: any) =>
          pick(item, ['competitionName', 'tagId', 'icon']),
        );
        const updateValue = {
          ...values,
          projectId,
          tagId,
          competitionTagList,
        };
        delete updateValue.competitionDate;
        // @ts-ignore
        const res = await UpdateProject(updateValue);
        if (res.code === 0) {
          message.success('更新成功');
        } else {
          message.error('更新失败');
        }
      }}
      submitter={{
        render: (_, dom) => (
          <FooterToolbar
            style={{
              right: 0,
              width: '100%',
            }}
          >
            {dom}
          </FooterToolbar>
        ),
      }}
    >
      <ProCard title="基本信息" bordered style={{ marginBottom: '20px' }}>
        <Form.Item name="iconUrl" label="上传项目图片" rules={[{ required: true }]}>
          <ImageUpload
            customRequest={async (req) => {
              await buildUploadFileFn()(req, formRef, 'iconUrl');
            }}
          />
        </Form.Item>
        <Row gutter={[16, 16]} style={{ marginTop: '20px' }}>
          <Col xs={24} sm={12} md={12} lg={6} xl={6}>
            <ProFormText
              name="name"
              label="项目名称"
              placeholder="请输入"
              rules={[{ required: true, message: '请输入项目名称' }]}
              fieldProps={{ style: { width: '100%' } }}
            />
          </Col>
          <Col xs={24} sm={12} md={12} lg={6} xl={6}>
            <ProFormText
              name="projectKey"
              label="项目标识"
              placeholder="请输入"
              rules={[{ required: true, message: '请输入项目标识' }]}
              fieldProps={{ style: { width: '100%' } }}
            />
          </Col>
          <Col xs={24} sm={12} md={12} lg={6} xl={6}>
            <ProFormTextArea
              name="description"
              label="项目描述"
              placeholder="请输入"
              fieldProps={{ style: { width: '100%' } }}
            />
          </Col>
          <Col xs={24} sm={12} md={12} lg={6} xl={6}>
            <ProFormSelect
              name="industryType"
              label="行业类型"
              readonly
              tooltip="行业类型不可修改"
              fieldProps={{ style: { width: '100%' } }}
              valueEnum={{
                [IndustryType.CAR]: '汽车行业',
                [IndustryType.E_COMMERCE]: '电商行业',
              }}
            />
          </Col>
        </Row>
        <Row gutter={[16, 16]} style={{ marginTop: '20px' }}>
          <Col xs={24} sm={12} md={12} lg={6} xl={6}>
            <Flex align="center" gap={8}>
              <ProFormDigit
                label="视频存储周期≥"
                name="storageLife"
                width="xs"
                min={0}
                rules={[{ required: true, message: '请输入视频存储周期' }]}
                fieldProps={{
                  addonAfter: '天',
                }}
              />
              <Tooltip title="指直播视频、作品视频、高光视频的存储周期，达到该周期将自动删除。">
                <InfoCircleOutlined />
              </Tooltip>
            </Flex>
          </Col>
          <Col xs={24} sm={12} md={12} lg={6} xl={6}>
            <div className="flex flex-nowrap items-center gap-2">
              <ProFormDigit
                label="直播观看人次≥"
                name="liveHighlightViewThreshold"
                width="xs"
                min={0}
                rules={[{ required: true, message: '请输入直播观看人数' }]}
                fieldProps={{
                  addonAfter: '人',
                }}
              />
              <div className="text-nowrap">才生成高光视频</div>
              <Tooltip title="高光门槛指生成高光视频需要达到的观看人次。">
                <InfoCircleOutlined />
              </Tooltip>
            </div>
          </Col>
          <Col xs={24} sm={12} md={12} lg={6} xl={6} />
          <Col xs={24} sm={12} md={12} lg={6} xl={6} />
        </Row>
      </ProCard>

      <ProCard title="平台列表" bordered style={{ marginBottom: '20px' }}>
        <ProFormCheckbox.Group
          name="platformList"
          layout="horizontal"
          options={[
            { label: '抖音', value: PlatForm.Douyin, disabled: true },
            { label: '小红书', value: PlatForm.XiaoHongShu },
            { label: '视频号', value: PlatForm.WXVideo },
            { label: '快手', value: PlatForm.KuaiShou },
            { label: 'B站', value: PlatForm.Bilibili },
            { label: '微博', value: PlatForm.Weibo },
            // { label: 'TikTok', value: PlatForm.TikTok },
            { label: '易车', value: PlatForm.YiChe },
            { label: '汽车之家', value: PlatForm.QiCheZhiJia },
            { label: '懂车帝', value: PlatForm.DongCheDi },
            { label: '微信公众号', value: PlatForm.WXPublic },
          ]}
        />
      </ProCard>

      <ProCard title="竞品列表" bordered style={{ marginBottom: '20px' }}>
        <Space style={{ marginBottom: '20px' }}>
          <Button
            type="primary"
            onClick={() => {
              actionRef.current?.addEditRecord?.({
                competitionId: (Math.random() * 1000000).toFixed(0),
                name: '请选择',
              });
            }}
          >
            添加
          </Button>
        </Space>
        <Form.Item name="competitionDate">
          <EditableProTable
            columns={competitionTable}
            actionRef={actionRef}
            editableFormRef={editorFormRef}
            recordCreatorProps={false}
            rowKey="competitionId"
            toolBarRender={false}
            bordered
            search={false}
            params={{
              projectId,
            }}
            pagination={{
              pageSize: 100,
              showTotal: (total) => `共 ${total} 条`,
            }}
            request={async (params, sorter, filter) => {
              return proTableRequestAdapter(params, sorter, filter, QueryProjectCompetitionTag);
            }}
            editable={{
              type: 'single',
              editableKeys,
              form: editorFormRef.current,
              onSave: async (key, row) => {
                if (row.name === '请选择') {
                  message.error('请选择一个标签');
                  return Promise.reject();
                }
              },
              onChange: setEditableRowKeys,
              actionRender: (row, config, dom) => [dom.save, dom.cancel],
            }}
          />
        </Form.Item>
      </ProCard>
    </ProForm>
  );
};

export default BasicInfo;
