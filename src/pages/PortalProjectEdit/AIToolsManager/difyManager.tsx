import { useParams } from '@umijs/max';
import type { ProColumns, EditableFormInstance } from '@ant-design/pro-components';
import { EditableProTable } from '@ant-design/pro-components';
import { Button, Modal, Form, message, Input, Popconfirm, Space } from 'antd';
import { useState, useRef } from 'react';
import { useRequest } from 'ahooks';
import {
  GetDifyConfigList,
  PostDifyConfigSave,
  PostDifyConfigUpdate,
  PostDifyConfigDelete,
  type ProjectDifyConfig,
  GetDifyMenuTree,
} from '@/services/dify';
import { ProFormTreeSelect } from '@ant-design/pro-components';
import styled from 'styled-components';

const DifyTable = styled.div`
  .dify-table .ant-pro-card-body {
    padding-block-start: unset !important;
  }
`;

export default function DifyManager() {
  const { projectId } = useParams();
  const [modalVisible, setModalVisible] = useState(false);
  const [, setSelectedMenuId] = useState<number | null>(null);
  const [editableKeys, setEditableKeys] = useState<React.Key[]>([]);
  const [form] = Form.useForm();
  const editableFormRef = useRef<EditableFormInstance<ProjectDifyConfig>>();

  const { data: configList, refresh } = useRequest(() => GetDifyConfigList({ projectId }), {
    refreshDeps: [projectId],
  });

  const handleAdd = () => {
    setModalVisible(true);
  };

  const handleSave = async (_: any, row: ProjectDifyConfig) => {
    try {
      await PostDifyConfigUpdate({
        projectId: Number(projectId),
        code: row.code as 'private-message-assistant' | 'private-message-intention-judgment',
        apiKey: row.apiKey,
        knowledgeId: row.appType === 2 ? row.knowledgeId : undefined,
      });
      message.success('更新成功');
      refresh();
      return true;
    } catch (error) {
      message.error('更新失败');
      return false;
    }
  };

  const handleDelete = async (record: ProjectDifyConfig) => {
    try {
      await PostDifyConfigDelete({
        projectId: Number(projectId),
        code: record.code as 'private-message-assistant' | 'private-message-intention-judgment',
      });
      message.success('删除成功');
      refresh();
    } catch (error) {
      message.error('删除失败');
    }
  };

  const handleModalOk = async () => {
    const values = await form.validateFields();
    const menuId = values.menuId || null;

    try {
      const menuTreeData = await GetDifyMenuTree();
      const selectedMenus =
        menuTreeData.data?.flatMap(
          (item) => item.children?.filter((child) => menuId === child.id) || [],
        ) || [];

      const promises = selectedMenus.map((menu) =>
        PostDifyConfigSave({
          projectId: Number(projectId),
          code: menu.code!,
          apiKey: values.apiKey,
          knowledgeId: values.knowledgeId,
        }),
      );
      await Promise.all(promises);

      message.success('配置保存成功');
      refresh();
      setModalVisible(false);
      form.resetFields();
      setSelectedMenuId(null);
    } catch (error) {
      message.error('配置保存失败');
    }
  };

  const columns: ProColumns<ProjectDifyConfig>[] = [
    {
      title: '功能模块',
      dataIndex: 'topMenuName',
      readonly: true,
      width: 100,
    },
    {
      title: '子功能模块',
      dataIndex: 'name',
      readonly: true,
      width: 100,
    },
    {
      title: '类型',
      dataIndex: 'appType',
      valueType: 'select',
      width: 100,
      formItemProps: {
        rules: [{ required: true, message: '请选择类型' }],
      },
      readonly: true,
      valueEnum: new Map([
        [1, '工作流'],
        [2, '聊天助手'],
      ]),
    },
    {
      title: '工作流/聊天助手密钥',
      dataIndex: 'apiKey',
      width: 300,
      formItemProps: {
        rules: [{ required: true, message: '请输入密钥' }],
      },
      render: (_, record) => record.apiKey,
    },
    {
      title: '知识库key',
      dataIndex: 'knowledgeId',
      width: 300,
      render: (_, record) => {
        if (record.appType === 1) {
          return <span className="text-gray-400">-</span>;
        }
        return <span>{record.knowledgeId}</span>;
      },
      formItemProps: (form, { entity }) => {
        return {
          rules: [{ required: false }],
          hidden: entity?.appType === 1,
        };
      },
      renderFormItem: (_, { record }) => {
        if (record?.appType === 1) {
          return <span className="text-gray-400">-</span>;
        }
        return <Input placeholder="请输入知识库key" />;
      },
    },
    {
      title: '操作',
      width: 100,
      valueType: 'option',
      render: (_, record) => (
        <Space>
          <a
            onClick={() => {
              setEditableKeys([record.id!]);
            }}
          >
            编辑
          </a>
          <Popconfirm
            key="delete"
            title="确定删除该配置吗？"
            onConfirm={() => handleDelete(record)}
          >
            <a className="text-red-500">删除</a>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <DifyTable>
      <EditableProTable<ProjectDifyConfig>
        columns={columns}
        value={configList?.data}
        className="dify-table"
        rowKey="id"
        options={false}
        search={false}
        pagination={false}
        dateFormatter="string"
        headerTitle="Dify配置"
        recordCreatorProps={false}
        editableFormRef={editableFormRef}
        editable={{
          type: 'single',
          editableKeys,
          onSave: handleSave,
          onChange: setEditableKeys,
          actionRender: (row, config, defaultDom) => {
            return [defaultDom.save, defaultDom.cancel];
          },
        }}
        toolBarRender={() => [
          <Button key="add" type="primary" onClick={handleAdd}>
            新增配置模块
          </Button>,
        ]}
      />

      <Modal
        title="新增配置模块"
        open={modalVisible}
        onOk={handleModalOk}
        onCancel={() => {
          setModalVisible(false);
          form.resetFields();
          setSelectedMenuId(null);
        }}
        width={600}
      >
        <Form form={form} layout="vertical">
          <ProFormTreeSelect
            name="menuId"
            label="选择功能模块"
            placeholder="请选择功能模块"
            allowClear
            secondary
            rules={[{ required: true, message: '请选择功能模块' }]}
            request={async () => {
              const res = await GetDifyMenuTree();
              return (res.data || []).map((item) => ({
                title: item.name,
                value: item.id,
                disabled: true,
                children: item.children?.map((child) => ({
                  title: child.name,
                  value: child.id,
                })),
              }));
            }}
            fieldProps={{
              onChange: (value) => {
                setSelectedMenuId(value);
              },
              treeDefaultExpandAll: true,
            }}
          />
          <Form.Item
            name="apiKey"
            label="工作流/聊天助手密钥"
            rules={[{ required: true, message: '请输入密钥' }]}
          >
            <Input placeholder="请输入密钥" />
          </Form.Item>
          <Form.Item
            name="knowledgeId"
            label="知识库key"
            extra="为空则使用默认知识库key，仅聊天助手类型可用"
          >
            <Input placeholder="请输入知识库key" />
          </Form.Item>
        </Form>
      </Modal>
    </DifyTable>
  );
}
