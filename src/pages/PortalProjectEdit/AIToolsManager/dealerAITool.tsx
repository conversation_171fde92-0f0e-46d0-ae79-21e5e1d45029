import useProjectFeature from '@/hooks/fg/useProjectFeature';
import { FunctionCode, UpdateSystemFunction } from '@/services/system';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { useParams } from '@umijs/max';
import { clearCache } from 'ahooks';
import { Space, Switch } from 'antd';
import { Key, useRef, useState } from 'react';

type AIToolsManagerTable = {
  modular: string;
  explain: string;
  isDisable: boolean;
  key: FunctionCode;
};

export default function DealerAITool() {
  const { projectId } = useParams();
  const actionRef = useRef<ActionType>();
  const [selectedRowKeys, setSelectedRowKeys] = useState<Key[]>([]);
  const { featureEnableDict, isLoading, cacheKey, refresh } = useProjectFeature({ projectId });

  const defaultDataSource = [
    {
      modular: 'AI 留资',
      explain:
        '直播间挂载小雪花留资组件，引导用户点击小雪花留资，可以精准获取客户的手机号、意向车系及意向购车地信息，提升线索质量。',
      key: FunctionCode.AIClue,
    },
    {
      modular: 'AI 助播',
      explain:
        'AI助播可以根据直播间数据与互动行为，AI为主播提供应对策略。AI实时监测分析弹幕内容，提炼观众的兴趣热点。实时展示直播间各个维度数据。',
      key: FunctionCode.AIAssistant,
    },
    {
      modular: 'AI 短视频',
      explain: 'AI短视频可以快速选题，提高视频质量下限，可以根据矩阵优质模板，进行一键剪辑。',
      key: FunctionCode.AIShortVideo,
    },
  ];

  const renderDataSource = defaultDataSource?.map((item) => {
    return {
      ...item,
      isDisable: featureEnableDict?.[item.key] || false,
    };
  });

  const handleSwitchChange = async (functionCode: FunctionCode, isDisable: boolean) => {
    await UpdateSystemFunction({
      projectId,
      functionCode,
      isDisabled: isDisable ? 1 : 0,
    });
    clearCache(cacheKey);
    refresh();
  };

  const columns: ProColumns<AIToolsManagerTable>[] = [
    {
      dataIndex: 'key',
      hideInTable: true,
    },
    {
      title: '模块',
      dataIndex: 'modular',
      align: 'center',
    },
    {
      title: '功能说明',
      dataIndex: 'explain',
      align: 'center',
    },
    {
      title: '操作',
      align: 'center',
      width: 100,
      dataIndex: 'isDisable',
      render: (_, record) => {
        return (
          <Switch
            checked={record.isDisable}
            onChange={() => handleSwitchChange(record.key, record.isDisable)}
          />
        );
      },
    },
  ];

  const handleBatchSwitch = async (selectRowKeys: FunctionCode[], enable: boolean) => {
    for (const key of selectRowKeys) {
      await UpdateSystemFunction({
        projectId,
        functionCode: key,
        isDisabled: enable ? 0 : 1,
      });
    }
    clearCache(cacheKey);
    refresh();
  };
  return (
    <ProTable<AIToolsManagerTable>
      columns={columns}
      rowKey="key"
      loading={isLoading}
      dataSource={renderDataSource}
      actionRef={actionRef}
      headerTitle="门店端 AI 工具"
      rowSelection={{
        alwaysShowAlert: true,
        onChange: (selectedRowKeys) => {
          setSelectedRowKeys(selectedRowKeys);
        },
      }}
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      tableAlertRender={({ selectedRowKeys, selectedRows, onCleanSelected }) => {
        return (
          <Space size={16} style={{ width: '200px' }}>
            已选 {selectedRowKeys.length} 项<a onClick={onCleanSelected}>取消选择</a>
          </Space>
        );
      }}
      tableAlertOptionRender={() => {
        return (
          <Space>
            是否启用:
            <a onClick={() => handleBatchSwitch(selectedRowKeys as FunctionCode[], true)}>
              全部启用
            </a>
            <a onClick={() => handleBatchSwitch(selectedRowKeys as FunctionCode[], false)}>
              全部禁用
            </a>
          </Space>
        );
      }}
      scroll={{ x: 'max-content' }}
      options={false}
      search={false}
      pagination={false}
    />
  );
}
