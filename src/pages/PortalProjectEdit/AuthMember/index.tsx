import { QueryPortalUsersForPagination } from '@/services/auth';
import { AuthorizeUser } from '@/services/project';
import { PortalUser } from '@/services/typings';
import { proTableRequestAdapter } from '@/utils';
import { ProCard, ProColumns, ProTable } from '@ant-design/pro-components';
import { useParams } from '@umijs/max';
import { useState } from 'react';

const AuthMembers = () => {
  const { projectId } = useParams();
  const [authUserIdList, setAuthUserIdList] = useState<number[]>([]);

  const userColumns: Array<ProColumns> = [
    {
      title: '序号',
      dataIndex: 'id',
      hideInSearch: true,
      hideInTable: false,
    },
    {
      title: '姓名',
      dataIndex: 'name',
      hideInSearch: false,
      hideInTable: false,
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      hideInSearch: true,
      hideInTable: false,
    },
  ];

  return (
    <ProCard title="授权成员" bordered style={{ marginBottom: '20px' }}>
      <ProTable
        columns={userColumns}
        rowKey="id"
        toolBarRender={false}
        bordered
        postData={(data: PortalUser[]) => {
          const selectedId = data
            .map((item) => {
              if (item.projectIds.includes(projectId)) {
                return item.id;
              }
            })
            .filter(Boolean);
          // 选中已授权的用户
          setAuthUserIdList(selectedId as number[]);
          return data;
        }}
        search={{
          labelWidth: 'auto',
        }}
        request={async (params, sorter, filter) => {
          return proTableRequestAdapter(params, sorter, filter, QueryPortalUsersForPagination);
        }}
        rowSelection={{
          selectedRowKeys: authUserIdList,
          columnTitle: ' ',
          onSelect: async (record, selected, selectedRows) => {
            const selectedId = selectedRows.map((item: any) => item.id);
            setAuthUserIdList(selectedId);
            await AuthorizeUser({
              projectId,
              userId: record.id,
              authorizeStatus: selected ? 1 : 0,
            });
          },
        }}
      />
    </ProCard>
  );
};

export default AuthMembers;
