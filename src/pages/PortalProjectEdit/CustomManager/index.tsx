import { Avatar, Button, List, message, Modal } from 'antd';
import DefaultAvatar from '@/assets/default-avatar.png';
import { ProCard } from '@ant-design/pro-components';
import CustomerAddModal from './customerAddModal';
import { useParams, useRequest } from '@umijs/max';
import { ManualTeamDelete, ManualTeamList, ManualTeamSave } from '@/services/douyin-im/manual-team';
import { DouyinImManualTeam } from '@/services/douyin-im/manual-config';
import { MinusCircleOutlined } from '@ant-design/icons';

export default function CustomManager() {
  const { projectId } = useParams();

  const { data: teamList, refresh } = useRequest(() => ManualTeamList({ projectId }), {
    refreshDeps: [projectId],
  });

  const handleModalOk = async (values: number[]) => {
    await ManualTeamSave({
      projectId,
      sysUserIdList: values,
    });
    message.success('添加成功');
    refresh();
  };

  const handleRemove = async (sysUserId?: number) => {
    if (!sysUserId) {
      message.error('请选择要移除的客服');
      return;
    }
    Modal.confirm({
      title: '移除客服',
      content:
        '是否确认移除？移除后该客服所接待的用户将分配给【客户团队】中当日接待客户数最少的客服。',
      okText: '确认',
      cancelText: '取消',
      closable: true,
      maskClosable: true,
      onOk: async () => {
        const res = await ManualTeamDelete({ projectId, sysUserId });
        if (res.code === 0) {
          message.success('移除成功');
          refresh();
        } else {
          message.error('移除失败');
        }
      },
    });
  };

  return (
    <ProCard>
      <List<DouyinImManualTeam>
        dataSource={teamList}
        split={false}
        size="small"
        header={
          <div className="sticky top-0 z-10 flex justify-between bg-white px-5">
            <div className="flex items-center text-new-media-gray-600">
              已添加: <span className="text-new-media-blue-900">{teamList?.length}</span>
            </div>
            <CustomerAddModal onOk={handleModalOk} projectId={projectId} />
          </div>
        }
        renderItem={(item, index) => (
          <List.Item
            key={item.sysUserId}
            className="hover:bg-gray-50"
            actions={[
              <Button
                danger
                icon={<MinusCircleOutlined />}
                key="remove"
                onClick={() => handleRemove(item.sysUserId)}
              >
                删除
              </Button>,
            ]}
          >
            <List.Item.Meta
              avatar={<Avatar size={40} src={DefaultAvatar} />}
              title={`${index + 1}号客服（账号名称: ${item.name}）`}
              description={item.email}
            />
          </List.Item>
        )}
      />
    </ProCard>
  );
}
