import { DouyinImManualSysUserVO, ManualTeamSysUserPage } from '@/services/douyin-im/manual-team';
import { proTableRequestAdapter } from '@/utils';
import { ModalForm, ProColumns, ProTable } from '@ant-design/pro-components';
import { Button, Table } from 'antd';
import { useState } from 'react';

type CustomerAddModalProps = {
  onOk: (values: any) => void;
  projectId?: string;
};

export type CustomerServiceMember = {
  id: string;
  name: string;
  account: string;
  maxConnections: number;
};

const CustomerAddModal = ({ onOk, projectId }: CustomerAddModalProps) => {
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    setSelectedRowKeys(newSelectedRowKeys);
  };

  const columns: ProColumns<DouyinImManualSysUserVO>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      hideInSearch: true,
    },
    {
      title: '姓名',
      dataIndex: 'name',
      search: {
        transform: (value) => ({
          keyword: value,
        }),
      },
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      hideInSearch: true,
    },
  ];

  return (
    <ModalForm<Partial<CustomerServiceMember>>
      title={
        <>
          <div>添加客服</div>
          <div className="my-2 text-sm text-red-500">
            *成员的角色需要在【系统管理】-【角色管理】-【功能模块】-【人工客服】拥有所有权限才会出现在下列选择列表
          </div>
        </>
      }
      layout="horizontal"
      labelAlign="left"
      trigger={<Button type="link">添加客服人员</Button>}
      onFinish={async () => {
        onOk(selectedRowKeys);
        return true;
      }}
      modalProps={{
        destroyOnHidden: true,
      }}
      width={800}
    >
      <ProTable<DouyinImManualSysUserVO>
        headerTitle={null}
        options={false}
        size="small"
        ghost
        params={{
          projectId,
        }}
        style={{ marginTop: '15px' }}
        search={{
          labelWidth: 'auto',
          span: {
            xs: 24,
            sm: 24,
            md: 12,
            lg: 12,
            xl: 6,
            xxl: 6,
          },
          searchGutter: 20,
        }}
        rowKey="id"
        postData={(data: DouyinImManualSysUserVO[]) => {
          const selectedId = data
            .map((item) => {
              if (item.selected) {
                return item.id;
              }
            })
            .filter(Boolean);
          setSelectedRowKeys(selectedId as number[]);
          return data;
        }}
        rowSelection={{
          selections: [Table.SELECTION_ALL, Table.SELECTION_INVERT],
          type: 'checkbox',
          selectedRowKeys,
          onChange: onSelectChange,
          preserveSelectedRowKeys: true,
          getCheckboxProps: (record) => ({
            disabled: record.selected,
          }),
        }}
        request={(params, sorter, filter) => {
          return proTableRequestAdapter(params, sorter, filter, ManualTeamSysUserPage);
        }}
        columns={columns}
        pagination={{
          defaultPageSize: 10,
          showTotal: (total, range) => `${range[0]}-${range[1]} 共 ${total} 条`,
          showSizeChanger: true,
        }}
      />
    </ModalForm>
  );
};

export default CustomerAddModal;
