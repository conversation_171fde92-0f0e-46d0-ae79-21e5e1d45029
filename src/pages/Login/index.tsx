/**
 * login page
 */
import logo from '@/assets/logo.png';
import NewMediaBanner from '@/components/banner';
import { ILogin, Login } from '@/services/auth';
import { GetAllProject } from '@/services/project';
import { fetchProjectInfo } from '@/utils/common';
import { useModel, history, useRequest } from '@umijs/max';
import { Button, Form, Input } from 'antd';
import { flushSync } from 'react-dom';
import styles from './index.module.less';

export default function LoginPage() {
  const { initialState, setInitialState } = useModel('@@initialState');

  const { run: fetchProjectList } = useRequest(() => GetAllProject(), {
    cacheKey: 'GetAllProject',
    cacheTime: 10 * 60 * 1000, // 10 分钟
    refreshDeps: [],
    manual: true,
    onSuccess: (projectData) => {
      if (projectData) {
        localStorage.setItem('projectList', JSON.stringify(projectData));
      }
    },
  });

  const fetchUserInfo = async () => {
    const userInfo = await initialState?.fetchUserInfo?.();
    if (userInfo) {
      flushSync(() => {
        // @ts-ignore
        setInitialState((s) => {
          return {
            ...s,
            currentUser: userInfo,
          };
        });
      });
    }
  };

  const onSubmit = async (values: ILogin) => {
    const res = await Login(values);
    if (res.code === 0) {
      await fetchUserInfo();
      const projectData = await fetchProjectInfo();

      // @ts-ignore
      setInitialState((s) => {
        return {
          ...s,
          projectData,
        };
      });

      const urlParams = new URL(window.location.href).searchParams;
      // console.log(urlParams.get('redirect'));
      let redirectUrl = urlParams.get('redirect');
      if (!redirectUrl) {
        // 如果可访问项目只有一个的话直接进入
        const projectList = await fetchProjectList();
        if (projectList?.length === 1) {
          redirectUrl = `/project/${projectList[0].projectKey}/${projectList[0].industryType || '1'}/overview/data-dashboard`;
        } else {
          redirectUrl = '/project-list';
        }
      }

      if (redirectUrl.indexOf('/clip-portal') > -1) {
        location.href = location.origin + redirectUrl;
      }
      // 这里使用history切换路由是为了重新执行init的拉取初始数据逻辑 getInitialState
      history.replace(redirectUrl);
    }
  };

  return (
    <div className={styles.container}>
      <div className={styles.imageBg}>
        <div
          style={{
            width: '600px',
            height: '600px',
            position: 'relative',
          }}
        >
          <NewMediaBanner />
        </div>
      </div>
      <div className={styles.content}>
        <div className={styles.main}>
          <div
            style={{
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'start',
              alignItems: 'center',
            }}
          >
            <img alt="logo" className={styles.logo} height={25} src={logo} />
            <span style={{ marginLeft: 5, fontSize: 15, fontWeight: 500 }}>新媒体监控平台</span>
          </div>

          <Form
            name="basic"
            onFinish={onSubmit}
            autoComplete="off"
            style={{
              padding: '30px 0px',
              textAlign: 'center',
              overflow: 'hidden',
            }}
          >
            <Form.Item name="account" rules={[{ required: true, message: '请输入邮箱!' }]}>
              <Input placeholder="请输入邮箱" />
            </Form.Item>

            <Form.Item name="password" rules={[{ required: true, message: '请输入登录密码!' }]}>
              <Input.Password placeholder="请输入登录密码" />
            </Form.Item>
            <Form.Item>
              <Button type="primary" htmlType="submit" block>
                登录
              </Button>
            </Form.Item>
          </Form>
        </div>
      </div>
    </div>
  );
}
