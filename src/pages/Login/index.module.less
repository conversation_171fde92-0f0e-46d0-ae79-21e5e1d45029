.container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  overflow: auto;
  background: linear-gradient(1deg, #e8efff -14.02%, #fff 111.72%);
  .imageBg {
    display: flex;
    flex: 1;
    align-items: center;
    justify-content: center;
    height: 100vh;
  }

  .content {
    width: 400px;
  }
}

.content {
  flex: 1;
  padding: 20px;
}

@media only screen and (max-width: 800px) {
  .container {
    background-color: #f2f6ff;
    .imageBg {
      display: none;
    }
  }
}
.main {
  width: 400px;
  margin: 0 auto;
  padding: 20px 36px;
  background-color: #fff;
  border-radius: 6px;
  box-shadow: 1px 1px 12px 1px rgba(33, 33, 33, 0.025);
  @media screen and (max-width: 576px) {
    width: 95%;
    max-width: 328px;
  }

  .placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 328px;
    height: 400px;
  }

  .qrcodeContainer {
    display: flex;
    justify-content: center;
  }
}
