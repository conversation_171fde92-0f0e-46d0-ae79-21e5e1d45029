/**
 * 标签管理
 */
import { DeleteOutlined, EditOutlined, PlusOutlined } from '@ant-design/icons';
import React, { useEffect, useRef, useState } from 'react';

import { spiderSyncUserTags, spiderSyncUserTagsReloader } from '@/models/store';
import { CreateTagGroup, DeleteTagGroup, EditTagGroup } from '@/services/tag';
import { CommonTagGroup } from '@/services/typings';
import { useAccess } from '@umijs/max';
import { Button, Empty, FormInstance, message, Modal, Space, Tag } from 'antd';
import { useAtomValue, useSetAtom } from 'jotai';
import CreateModal from './Components/createModal';
import { PageContainer, ProCard } from '@ant-design/pro-components';
import { INDUSTRY_TAG_COLOR_MAP } from '@/utils/const';
import { IndustryType } from '@/utils/const';
import { useIndustryTagGroups } from '@/hooks/request/use-industry-tag-groups';

const TagList: React.FC = () => {
  const type = 1;
  const [currentTagGroup, setCurrentTagGroup] = useState<CommonTagGroup>();
  const [createModalVisible, setCreateModalVisible] = useState<boolean>(false);
  const [editModalVisible, setEditModalVisible] = useState<boolean>(false);
  const createModalFormRef = useRef<FormInstance>();
  const access = useAccess();

  const tagGroups = useAtomValue(spiderSyncUserTags);
  const reloadSpiderSyncUserTags = useSetAtom(spiderSyncUserTagsReloader);
  const { refresh: refreshIndustryTagGroups } = useIndustryTagGroups();

  useEffect(() => {
    reloadSpiderSyncUserTags();

    return () => {
      // 退出标签管理页时刷新行业标签组 为了在作品分析拿到的行业标签筛选项数据是最新的
      refreshIndustryTagGroups();
    };
  }, []);

  return (
    <PageContainer
      extra={[
        <Button
          // disabled={!}
          key="create"
          type="primary"
          icon={<PlusOutlined style={{ fontSize: 16, verticalAlign: '-3px' }} />}
          onClick={() => {
            if (!access.canWriteMonitor) {
              message.warning('权限不足');
              return;
            }
            setCreateModalVisible(true);
          }}
        >
          新建标签
        </Button>,
      ]}
    >
      <div>
        {(tagGroups === null || tagGroups?.length === 0) && (
          <Empty style={{ marginTop: 36, marginBottom: 12 }} image={Empty.PRESENTED_IMAGE_SIMPLE} />
        )}
        <ProCard ghost gutter={[0, 16]} direction="column">
          {tagGroups?.map((tagGroup) => (
            <ProCard
              title={
                <div className="flex items-center">
                  {tagGroup.name}
                  <Tag
                    className="ml-3"
                    bordered={false}
                    color={INDUSTRY_TAG_COLOR_MAP[tagGroup.industryType || IndustryType.OTHER]}
                  >
                    {tagGroup.industryType === IndustryType.CAR
                      ? '汽车行业'
                      : tagGroup.industryType === IndustryType.E_COMMERCE
                        ? '电商行业'
                        : '其他'}
                  </Tag>
                </div>
              }
              key={tagGroup.id}
              extra={
                <Space>
                  <Button
                    disabled={!access.canWriteMonitor}
                    icon={<EditOutlined />}
                    type="text"
                    onClick={() => {
                      createModalFormRef.current?.setFieldsValue(tagGroup);
                      setCurrentTagGroup(tagGroup);
                      setEditModalVisible(true);
                    }}
                  >
                    修改
                  </Button>
                  <Button
                    disabled={!access.canWriteMonitor}
                    icon={<DeleteOutlined />}
                    type="text"
                    onClick={() => {
                      Modal.confirm({
                        title: `删除标签分组`,
                        content: `是否确认删除「${tagGroup.name}」分组？`,
                        okText: '删除',
                        okType: 'danger',
                        cancelText: '取消',
                        onOk() {
                          DeleteTagGroup(tagGroup.id).then((res) => {
                            if (res.code === 0) {
                              reloadSpiderSyncUserTags();
                            }
                          });
                        },
                      });
                    }}
                  >
                    删除
                  </Button>
                </Space>
              }
            >
              <Space direction={'horizontal'} wrap={true}>
                {tagGroup.tags?.map((item) => <Tag key={item.id}>{item.name}</Tag>)}
              </Space>
            </ProCard>
          ))}
        </ProCard>
      </div>

      <CreateModal
        type="create"
        setVisible={setCreateModalVisible}
        visible={createModalVisible}
        initialValues={{ tags: [{ name: '' }] }}
        formRef={createModalFormRef}
        onSubmitCreate={async (values: any) => {
          console.log(values);
          const res = await CreateTagGroup(type, values);
          if (res.code === 0) {
            setEditModalVisible(false);
            reloadSpiderSyncUserTags();
          }

          return res.code === 0;
        }}
      />

      <CreateModal
        type="edit"
        setVisible={setEditModalVisible}
        visible={editModalVisible}
        initialValues={currentTagGroup}
        formRef={createModalFormRef}
        onSubmitEdit={async (values: any) => {
          console.log(values);
          if (!currentTagGroup) {
            return false;
          }

          const res = await EditTagGroup(currentTagGroup?.id, values);
          if (res.code === 0) {
            setEditModalVisible(false);
            reloadSpiderSyncUserTags();
          }

          return res.code === 0;
        }}
      />
    </PageContainer>
  );
};

export default TagList;
