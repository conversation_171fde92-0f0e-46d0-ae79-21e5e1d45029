import type { FormInstance } from 'antd';
import { Modal, Space, Tooltip } from 'antd';
import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useRef, useState } from 'react';

import { CopyOutlined, DeleteOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import type { FormListFieldData, FormListOperation } from 'antd/lib/form/FormList';
import { keyBy } from 'lodash-es';
import {
  ProForm,
  ProFormList,
  ProFormText,
  ModalFormProps,
  ProFormSelect,
} from '@ant-design/pro-components';
import { IndustryType } from '@/utils/const';
import { CommonTag } from '@/services/common';

type TagEditForm = {
  name: string;
  tags: { id?: number; name: string }[];
  industryType?: IndustryType;
};

export type CreateModalFormProps = Omit<
  ModalFormProps,
  'onFinish' | 'visible' | 'initialValues'
> & {
  type: 'create' | 'edit';
  visible: boolean;
  setVisible: Dispatch<SetStateAction<boolean>>;
  onSubmitCreate?: (params: TagGroupCreateParam) => Promise<boolean>;
  onSubmitEdit?: (params: TagGroupEditParam) => Promise<boolean>;
  initialValues?: { id?: number } & Partial<TagEditForm>;
};

interface TagGroupCreateParam {
  name: string;
  addTagNames?: string[];
  industryType?: IndustryType;
}

interface TagGroupEditParam {
  name?: string;
  addTagNames?: string[];
  changeTagNames?: Record<number, string>;
  deleteTagIds?: number[];
}

const CreateModal: React.FC<CreateModalFormProps> = (props) => {
  const [deleteTagIds, setDeleteTagIds] = useState<number[]>([]);
  const formRef = useRef<FormInstance>();

  useEffect(() => {
    setDeleteTagIds([]);
  }, [props.initialValues]);

  return (
    <>
      <Modal
        title={props.type === 'create' ? '新建标签组' : '修改标签组'}
        width={568}
        open={props.visible}
        onOk={() => {
          console.log(formRef.current);
          formRef.current?.submit();
        }}
        onCancel={() => {
          props.setVisible(false);
        }}
        destroyOnHidden
      >
        <ProForm<TagEditForm>
          submitter={{
            render: false,
          }}
          initialValues={{
            ...props.initialValues,
            industryType: props.initialValues?.industryType
              ? props.initialValues.industryType
              : IndustryType.OTHER,
          }}
          formRef={formRef}
          layout="horizontal"
          labelAlign="left"
          onFinish={async (values) => {
            if (props.type === 'create') {
              const params: TagGroupCreateParam = {
                name: values.name,
                addTagNames: values.tags.map((i) => i.name) || [],
                industryType: values.industryType,
              };
              if (props.onSubmitCreate && (await props.onSubmitCreate(params))) {
                props.setVisible(false);
              }
            } else if (props.type === 'edit') {
              const changeTagNames: Record<number, string> = {};
              const oldTags = keyBy(
                props.initialValues?.tags?.filter((i) => i.id && i.name),
                (i) => i.id,
              ) as Record<number, Partial<CommonTag>>;
              values.tags.forEach((i) => {
                if (i.id && oldTags[i.id] && i.name !== oldTags[i.id].name) {
                  changeTagNames[i.id] = i.name;
                }
              });
              const params: TagGroupEditParam = {
                name: values.name,
                addTagNames: values.tags.filter((i) => !i.id).map((i) => i.name),
                changeTagNames,
                deleteTagIds,
              };

              if (props.onSubmitEdit && (await props.onSubmitEdit(params))) {
                props.setVisible(false);
              }
            }
          }}
        >
          <ProFormText
            name="name"
            label="标签组名称"
            width="md"
            placeholder="请输入标签组名称"
            rules={[{ required: true, message: '标签组名称必填' }]}
          />
          <ProFormSelect
            name="industryType"
            label="标签组分类"
            width="md"
            initialValue={0}
            disabled={props.type === 'edit'}
            transform={(value) => {
              return {
                industryType: value ? value : undefined,
              };
            }}
            options={[
              {
                label: '其他',
                value: IndustryType.OTHER,
              },
              {
                label: '汽车行业',
                value: IndustryType.CAR,
              },
              {
                label: '电商行业',
                value: IndustryType.E_COMMERCE,
              },
            ]}
            addonAfter={
              <Tooltip title="当前标签组，用于项目行业分类。例如：“作品分析”需要在项目中拆出分类。">
                <QuestionCircleOutlined className="cursor-help" />
              </Tooltip>
            }
            fieldProps={{ allowClear: false }}
            required
          />
          <ProFormList
            label="子标签名称"
            name="tags"
            actionRender={(field: FormListFieldData, action: FormListOperation) => {
              const currentKey = field.name;
              return [
                <Space key={'actions'} style={{ marginLeft: 16 }}>
                  <Tooltip key={'copy'} title="复制">
                    <CopyOutlined
                      className={'ant-pro-form-list-action-icon'}
                      onClick={() => {
                        action.add(formRef.current?.getFieldValue('tags')[currentKey]);
                      }}
                    />
                  </Tooltip>
                  <Tooltip key={'remove'} title="删除">
                    <DeleteOutlined
                      className={'ant-pro-form-list-action-icon'}
                      onClick={() => {
                        if (formRef.current?.getFieldValue('tags')[currentKey]?.id) {
                          setDeleteTagIds([
                            ...deleteTagIds,
                            formRef.current?.getFieldValue('tags')[currentKey].id,
                          ]);
                        }
                        action.remove(currentKey);
                      }}
                    />
                  </Tooltip>
                </Space>,
              ];
            }}
            creatorButtonProps={{
              type: 'default',
              style: { width: '128px' },
              position: 'bottom',
              creatorButtonText: '添加标签',
            }}
            creatorRecord={{
              name: '',
            }}
            rules={[
              {
                required: true,
                message: '请添加标签',
                validator(rule, value, callback) {
                  if (Array.isArray(value) && value.length === 0) {
                    callback('');
                    return;
                  }

                  callback();
                },
              },
              {
                required: true,
                // message: '存在重复标签',
                validator(rule, value, callback) {
                  if (Array.isArray(value) && value.length > 0) {
                    const items = value;
                    for (let i = 0; i < items.length; i++) {
                      for (let j = 0; j < i; j++) {
                        if (items[i].name === items[j].name) {
                          callback(
                            `第${j + 1}个和第${i + 1}个标签的名字重复, 都是${items[i].name}`,
                          );
                          return;
                        }
                      }
                    }
                  }

                  callback();
                },
              },
            ]}
          >
            <ProFormText
              name="name"
              width={'sm'}
              fieldProps={{
                allowClear: false,
              }}
              placeholder="请输入标签名称"
              rules={[
                {
                  required: true,
                  message: '标签名称必填',
                },
              ]}
            />
          </ProFormList>
        </ProForm>
      </Modal>
    </>
  );
};

export default CreateModal;
