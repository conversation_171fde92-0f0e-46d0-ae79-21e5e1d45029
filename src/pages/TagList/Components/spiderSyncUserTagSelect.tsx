import { CommonTagGroup } from '@/services/typings';
import { Select, Tag } from 'antd';
import type { SelectProps } from 'antd/lib/select';
import { keyBy } from 'lodash-es';
import React, { useState } from 'react';
import CommonTagSelectionModal from './selectModal';
import { CommonTag } from '@/services/common';

type PropsType = {
  onChange?: (value: any[]) => void; // 设置受控属性
  value?: any[]; // 受控属性，组件选中值
  data?: CommonTagGroup[];
} & SelectProps<any>;

const SpiderSyncUserTagSelect: React.FC<PropsType> = (props) => {
  const { value, onChange, data, ...rest } = props;
  const [showTagSelectModal, setShowTagSelectModal] = useState(false);

  const allTags: CommonTag[] = [];
  data &&
    data.forEach((tagGroup) => {
      tagGroup?.tags?.forEach((tag) => {
        allTags.push(tag);
      });
    });
  const allTagMap = keyBy<CommonTag>(allTags, 'id') as Record<number, CommonTag>;

  return (
    <>
      <Select
        {...rest}
        mode="multiple"
        placeholder={rest.placeholder || '请选择标签'}
        allowClear={true}
        value={value}
        open={false}
        onClear={() => {
          // @ts-ignore
          onChange([]);
        }}
        onClick={() => {
          setShowTagSelectModal(!showTagSelectModal);
        }}
        tagRender={(tagProps) => {
          // @ts-ignore
          const selectedTag: GroupChatTag = allTagMap[tagProps.value];
          if (selectedTag) {
            return (
              <Tag
                closable
                onClose={() => {
                  onChange?.(value.filter((extID: string) => extID !== selectedTag?.id));
                }}
              >
                {selectedTag?.name}
              </Tag>
            );
          }

          return <></>;
        }}
      />

      <CommonTagSelectionModal
        mode="multiple"
        visible={showTagSelectModal}
        setVisible={setShowTagSelectModal}
        defaultValues={
          value?.map((id: number) => allTagMap[id]).filter((i: CommonTag | undefined) => !!i) || []
        }
        onFinish={(values) => {
          onChange?.(values.map((i) => i.id));
        }}
        data={data}
      />
    </>
  );
};

export default SpiderSyncUserTagSelect;
