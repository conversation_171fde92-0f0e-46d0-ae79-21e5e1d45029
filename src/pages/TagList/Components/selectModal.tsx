/**
 * 通用标签弹出层
 */
import { CommonTag } from '@/services/common';
import { CommonTagGroup } from '@/services/typings';
import { ModalForm, ProCard } from '@ant-design/pro-components';
import { Empty, Input, message, ModalProps, Row, Space, Tag } from 'antd';
import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useState } from 'react';

export type FormParams = {
  ids: string[];
};

export type TSelectionProps = ModalProps & {
  mode?: 'multiple' | 'single';
  visible: boolean;
  setVisible: Dispatch<SetStateAction<boolean>>;
  onFinish?: (selectedTags: CommonTag[], params: FormParams) => void;
  value?: string;
  defaultValues?: CommonTag[];
  data?: CommonTagGroup[];
};

const CommonTagSelectionModal: React.FC<TSelectionProps> = ({
  visible,
  setVisible,
  onFinish,
  value,
  defaultValues,
  width,
  mode,
  data,
  ...rest
}) => {
  const [tagGroups, setTagGroups] = useState<CommonTagGroup[]>([]);
  const [keyword, setKeyword] = useState<string>('');
  const [selectedDatas, setSelectedData] = useState<CommonTag[]>(defaultValues || []);
  const [triggerValue, setTriggerValue] = useState<string | undefined>(value);

  useEffect(() => {
    setSelectedData(defaultValues || []);
    setKeyword('');
  }, [defaultValues, visible]);

  const handleFilter = () => {
    if (!data) {
      return;
    }
    const filteredTagGroups = data.filter((tagGroup) => {
      if (keyword.trim() === '') {
        return true;
      }
      const matchedTags = tagGroup.tags?.filter((tag) => {
        if (tag.name.includes(keyword)) {
          return true;
        }
        return false;
      });
      if (matchedTags && matchedTags.length > 0) {
        return true;
      }
      if (tagGroup.name?.includes(keyword)) {
        return true;
      }
      return false;
    });
    const filterTags = filteredTagGroups.map((item) => {
      const tags = item.tags?.filter((item) => {
        if (item.name && item.name.includes(keyword)) {
          return true;
        }
        return false;
      });
      return { ...item, tags: tags };
    });
    setTagGroups(filterTags);
  };

  useEffect(() => {
    handleFilter();
  }, [data, keyword]);

  const handleChange = (isSelected: boolean, item: CommonTag) => {
    if (!item?.id) {
      return;
    }

    if (mode !== 'multiple') {
      setSelectedData([item]);
      return;
    }

    if (isSelected) {
      setSelectedData(selectedDatas.filter((selected) => selected.id !== item?.id));
    } else {
      setSelectedData([...selectedDatas, item]);
    }
  };

  return (
    <ModalForm
      {...rest}
      width={width || 620}
      modalProps={{ zIndex: 1001, destroyOnHidden: true }}
      trigger={triggerValue ? <Tag>{triggerValue}</Tag> : undefined}
      open={visible}
      layout={'horizontal'}
      onOpenChange={setVisible}
      onFinish={async (values: FormParams) => {
        const params = { ...values };
        // @ts-ignore
        params.ids = selectedDatas.map((label) => label.id) || [];
        if (mode === 'single' && params.ids.length === 0) {
          message.warning('请至少选择一个标签');
          return;
        }

        if (onFinish) {
          onFinish(selectedDatas, params);

          if (triggerValue) {
            setTriggerValue(selectedDatas[0]?.name);
          }

          setVisible(false);
        }
      }}
      title="选择标签"
    >
      <>
        <Row style={{ marginBottom: 10 }}>
          <Input
            allowClear={true}
            placeholder={'输入关键词搜索标签和标签组'}
            value={keyword}
            onChange={(e) => {
              setKeyword(e.currentTarget.value);
            }}
          />
        </Row>
        <ProCard ghost gutter={[0, 16]} direction="column">
          {tagGroups.length === 0 && (
            <Empty
              style={{ marginTop: 36, marginBottom: 36 }}
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            />
          )}
          {tagGroups.map((tagGroup) => (
            <ProCard size="small" title={tagGroup.name} key={tagGroup.id} ghost>
              <Space direction={'horizontal'} wrap={true}>
                {tagGroup.tags?.map((tag) => {
                  const isSelected = selectedDatas.map((selected) => selected.id)?.includes(tag.id);

                  return (
                    <Tag
                      style={{ cursor: 'pointer' }}
                      color={isSelected ? 'blue' : 'default'}
                      key={tag.id}
                      onClick={() => {
                        if (!tag?.id) {
                          return;
                        }

                        handleChange(isSelected, tag);
                      }}
                    >
                      {tag.name}
                    </Tag>
                  );
                })}
              </Space>
            </ProCard>
          ))}
        </ProCard>
      </>
    </ModalForm>
  );
};

export default CommonTagSelectionModal;
