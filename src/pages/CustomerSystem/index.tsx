import { PageContainer } from '@ant-design/pro-components';
import { ConfigProvider, message } from 'antd';
import ChatContent, { ChatContentRef } from './component/chatContent';
import UserInfo from './component/userInfo';
import UserList from './component/userList';
import { useLocalStorageState, usePrevious } from 'ahooks';
import { useEffect, useRef, useState } from 'react';
import { useSetAtom } from 'jotai';
import {
  sseMessageDataAtom,
  sseConversationDataAtom,
  sseStatusAtom,
  sseErrorAtom,
  sseMessageEventAtom,
  sseConversationEventAtom,
} from './atoms/sseAtom';

export default function CustomerSystem() {
  const [projectId] = useLocalStorageState<string | null>('customer_system_project_id', {
    defaultValue: null,
    listenStorageChange: true,
  });
  const previousProjectId = usePrevious(projectId);
  const eventSourceRef = useRef<EventSource | null>(null);
  const chatContentRef = useRef<ChatContentRef>(null);

  // 设置 jotai 状态的函数
  const setMessageData = useSetAtom(sseMessageDataAtom);
  const setConversationData = useSetAtom(sseConversationDataAtom);
  const setStatus = useSetAtom(sseStatusAtom);
  const setError = useSetAtom(sseErrorAtom);
  const setMessageEvent = useSetAtom(sseMessageEventAtom);
  const setConversationEvent = useSetAtom(sseConversationEventAtom);

  // 状态管理
  const [status, setLocalStatus] = useState<'CONNECTING' | 'OPEN' | 'CLOSED'>('CLOSED');
  const [error, setLocalError] = useState<Error | null>(null);

  const retryCountRef = useRef(0);
  const maxRetries = 3; // 最大重试次数
  const retryInterval = 3000; // 重试间隔时间（毫秒）

  // 创建和管理 EventSource 连接
  const createEventSource = () => {
    if (!projectId) return;

    try {
      const url = `/new-media-api/douyin/im/manual/chat/sse/connect?projectId=${projectId}`;
      const es = new EventSource(url);
      eventSourceRef.current = es;
      setLocalStatus('CONNECTING');

      es.onopen = () => {
        setLocalStatus('OPEN');
        setLocalError(null);
      };

      es.onerror = () => {
        const error = new Error('SSE 连接错误');
        setLocalError(error);
        setLocalStatus('CLOSED');

        // 添加重连逻辑
        if (retryCountRef.current < maxRetries) {
          message.warning(
            `连接断开，${retryInterval / 1000}秒后进行第${retryCountRef.current + 1}次重连...`,
          );
          setTimeout(() => {
            retryCountRef.current += 1;
            closeEventSource();
            createEventSource();
          }, retryInterval);
        } else {
          message.error('重连次数已达上限，请手动刷新页面重试');
        }
      };

      es.addEventListener('chatMessage', (event) => {
        console.log('hjy ~ chatMessage ~ event:', event);
        setMessageEvent('chatMessage');
        setMessageData(event.data);
        chatContentRef.current?.refreshValidData();
      });

      es.addEventListener('conversation', (event) => {
        console.log('hjy ~ conversation ~ event:', event);
        setConversationEvent('conversation');
        setConversationData(event.data);
      });
    } catch (err) {
      setLocalError(err as Error);
      setLocalStatus('CLOSED');
    }
  };

  // 关闭 EventSource 连接
  const closeEventSource = () => {
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
      eventSourceRef.current = null;
      setLocalStatus('CLOSED');
    }
  };

  // 更新全局状态
  useEffect(() => {
    setStatus(status);
  }, [status, error, setStatus, setError]);

  // 处理项目 ID 变化
  useEffect(() => {
    if (projectId && projectId !== previousProjectId) {
      closeEventSource();
      createEventSource();
    } else if (!projectId) {
      closeEventSource();
    }
  }, [projectId, previousProjectId]);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      closeEventSource();
    };
  }, []);

  return (
    <ConfigProvider
      theme={{
        token: {
          colorPrimary: '#1E5EFF',
        },
      }}
    >
      <PageContainer
        pageHeaderRender={() => <></>}
        className="min-w-[1215px] bg-white"
        style={{
          height: 'calc(100vh - 56px)',
        }}
        token={{
          paddingInlinePageContainerContent: 0,
          paddingBlockPageContainerContent: 0,
        }}
      >
        <div
          className="flex"
          style={{
            height: 'calc(100vh - 56px)',
          }}
        >
          {/* 左侧会话列表 */}
          <div className="h-full w-[280px] border-r border-gray-100 bg-white">
            {/* 用户信息 */}
            <UserList />
          </div>

          {/* 主要聊天区域 */}
          <div className="flex flex-1 flex-col bg-white">
            <ChatContent ref={chatContentRef} />
          </div>

          {/* 右侧用户信息面板 */}
          <div className="w-[300px] border-l border-gray-100 bg-white">
            <UserInfo />
          </div>
        </div>
      </PageContainer>
    </ConfigProvider>
  );
}
