import { atom } from 'jotai';

// 存储 event:message 类型的 SSE 数据
export const sseMessageDataAtom = atom<string | null>(null);
export const sseMessageEventAtom = atom<string | null>(null);

// 存储 event:conversation 类型的 SSE 数据
export const sseConversationDataAtom = atom<string | null>(null);
export const sseConversationEventAtom = atom<string | null>(null);

// 存储 SSE 连接状态
export const sseStatusAtom = atom<string | null>(null);

// 存储 SSE 错误信息
export const sseErrorAtom = atom<Event | null>(null);

// 最后一条发送的消息 ID
export const lastSendMessageIdAtom = atom<string | undefined | null>(null);
