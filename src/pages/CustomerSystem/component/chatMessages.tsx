import { Bubble } from '@ant-design/x';
import { Avatar, But<PERSON>, Spin } from 'antd';
import { useAtom, useAtomValue } from 'jotai';
import { useEffect, useRef, useImperativeHandle, forwardRef } from 'react';
import { selectedConversationAtom } from '../atoms/userAtom';
import { useInfiniteQuery } from '@umijs/max';
import {
  GetChatPage,
  ChatMessage,
  PostChatMessageRead,
  PostChatReplyRecommend,
} from '@/services/douyin-im/chat';
import { useLocalStorageState, useInViewport, useThrottleFn } from 'ahooks';
import { cn } from '@/lib/utils';
import { lastSendMessageIdAtom, sseMessageDataAtom, sseMessageEventAtom } from '../atoms/sseAtom';
import defaultAvatar from '@/assets/default-avatar.png';
import { renderMessageInChat } from './utils';
import AIButton from '@/assets/ai-button.png';
import { nanoid } from 'nanoid';

export type ChatMessageType = ChatMessage & {
  // 消息发送方向 1-客户发送 2-客服发送
  direction: number;
  // 图片URL（当消息类型为图片时）
  imageUrl?: string;
  // 留资卡片标题（当消息类型为留资卡片时）
  title?: string;
  // 留资组件列表（当消息类型为留资卡片时）
  componentList?: number[];
};

type ChatMessagesProps = {
  realtimeMessages: ChatMessageType[];
  setRealtimeMessages: (messages: any) => void;
  setAIReplyInText: (value: string, loading?: boolean) => void;
};

export default forwardRef<ChatMessagesRef, ChatMessagesProps>(
  ({ realtimeMessages, setRealtimeMessages, setAIReplyInText }, ref) => {
    const selectedUser = useAtomValue(selectedConversationAtom);
    const [projectId] = useLocalStorageState<string | null>('customer_system_project_id', {
      listenStorageChange: true,
    });
    const loadMoreRef = useRef<HTMLDivElement>(null);
    const [inViewport] = useInViewport(loadMoreRef);
    const messagesEndRef = useRef<HTMLDivElement>(null);
    // SSE 数据
    const messageData = useAtomValue(sseMessageDataAtom);
    const lastSendMessageId = useAtomValue(lastSendMessageIdAtom);
    const [messageEvent, setMessageEvent] = useAtom(sseMessageEventAtom);

    const { data, fetchNextPage, hasNextPage, isFetchingNextPage, isLoading } = useInfiniteQuery({
      queryKey: ['chatMessages', selectedUser?.conversationId, projectId],
      queryFn: async ({ pageParam = 1 }) => {
        if (!projectId || !selectedUser?.conversationId) {
          throw new Error('缺少必要参数');
        }

        const response = await GetChatPage({
          projectId,
          conversationId: selectedUser.conversationId,
          page: pageParam,
          size: 20,
        });

        return {
          list: response.data?.items || [],
          current: pageParam,
          total: response.data?.total || 0,
        };
      },
      enabled: !!projectId && !!selectedUser?.conversationId,
      getNextPageParam: (lastPage) => {
        const hasMore = lastPage.list.length > 0 && lastPage.current * 20 < lastPage.total;
        return hasMore ? lastPage.current + 1 : undefined;
      },
      refetchOnWindowFocus: false,
    });

    // 处理会话消息的函数
    const updateMessage = (data: string) => {
      try {
        const messageSSEData = JSON.parse(data) as ChatMessage;
        console.log('hjy==messageSSEData=', messageSSEData);
        // 检查是否是当前选中的会话，处理和最后一条消息不同的消息避免出现消息重复
        // src/pages/CustomerSystem/component/chatContent.tsx:79 发送消息会立即上屏体验更好，但 sse 成功后会推送回来，所以这里使用lastSendMessageId来避免重复
        if (
          selectedUser?.conversationId === messageSSEData.conversationId &&
          messageSSEData.serverMessageId !== lastSendMessageId
        ) {
          // 创建新消息对象
          const newMessage: ChatMessageType = {
            ...messageSSEData,
            serverMessageId: messageSSEData.serverMessageId || `sse-${Date.now()}`,
            direction: messageSSEData.messageEvent === 0 ? 2 : 1,
          };

          // 添加到实时消息列表
          setRealtimeMessages((prev: ChatMessageType[]) => [newMessage, ...prev]);

          // 滚动到底部
          setTimeout(() => {
            messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
          }, 100);

          // 通知后端消息已读
          if (projectId && selectedUser?.conversationId) {
            PostChatMessageRead({
              conversationId: selectedUser.conversationId,
              projectId,
            });
          }
        }
      } catch (error) {
        console.error('处理会话消息失败:', error, '原始数据:', data);
      }
    };

    // 处理 SSE 消息更新
    useEffect(() => {
      if (!messageData || !messageEvent) return;
      updateMessage(messageData);
      setMessageEvent(null);
    }, [messageData, messageEvent, selectedUser, projectId]);

    // 监听视图，当加载更多元素进入视图时，加载下一页
    useEffect(() => {
      if (inViewport && hasNextPage && !isFetchingNextPage) {
        fetchNextPage();
      }
    }, [inViewport, fetchNextPage, hasNextPage, isFetchingNextPage]);

    // 首次加载时滚动到底部
    useEffect(() => {
      if (!isLoading && messagesEndRef.current) {
        messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
      }
    }, [isLoading]);

    // 当选择的用户变化时，清空实时消息
    useEffect(() => {
      setRealtimeMessages([]);
    }, [selectedUser?.conversationId]);

    // 获取所有消息并转换为 MessageType 类型
    const messages: ChatMessageType[] = [
      ...realtimeMessages,
      ...(data?.pages.flatMap((page) =>
        page.list.map((item: any) => ({
          ...item,
          direction: item.messageEvent === 0 ? 2 : 1, // 0: 发送(客服) 1: 接收(客户)
        })),
      ) || []),
    ];

    console.log('hjy==messages=', messages);

    // 添加一个滚动到底部的方法
    const scrollToBottom = () => {
      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    };

    // 将 scrollToBottom 方法通过 ref 暴露给父组件
    useImperativeHandle(ref, () => ({
      scrollToBottom,
    }));

    useEffect(() => {
      scrollToBottom();
    }, [messagesEndRef.current]);

    const { run: throttleHandleAIButtonClick } = useThrottleFn(
      async (message: ChatMessageType) => {
        if (!selectedUser?.conversationId || !projectId || !message.serverMessageId) return;

        // 设置 loading 状态
        setAIReplyInText('', true);

        try {
          const res = await PostChatReplyRecommend({
            conversationId: selectedUser.conversationId,
            projectId,
            serverMessageId: message.serverMessageId,
          });

          if (res.data?.replyRecommendContent) {
            setAIReplyInText(res.data.replyRecommendContent, false);
          }
        } catch (error) {
          // 发生错误时清除 loading 状态
          setAIReplyInText('', false);
          console.error('AI 回复推荐请求失败:', error);
        }
      },
      { wait: 5000 },
    );

    // 如果没有选择用户或正在加载中
    if (!selectedUser) {
      return null;
    }

    return (
      <div
        className="no-scrollbar flex flex-1 flex-col-reverse overflow-y-auto p-4"
        style={{ overflowAnchor: 'none' }}
      >
        {/* 用于自动滚动到底部的参考元素 */}
        <div ref={messagesEndRef} />

        {/* 消息列表 */}
        <div className="flex flex-1 flex-col-reverse gap-4">
          {/* 空白占位元素，用于消息少时将消息撑到底部 */}
          <div className="block flex-shrink flex-grow" />

          {messages.map((message) => (
            <div key={`${message.serverMessageId}-${nanoid()}`} className="group">
              {message.direction === 1 ? (
                // 客户发送的消息
                <div className="flex items-end">
                  <Bubble
                    content={renderMessageInChat(message)}
                    placement="start"
                    avatar={
                      <Avatar
                        src={selectedUser.customerInfo?.avatar || <img src={defaultAvatar} />}
                      />
                    }
                  />
                  <Button
                    icon={<img src={AIButton} alt="AI" className="h-[18px] w-[18px]" />}
                    onClick={() => throttleHandleAIButtonClick(message)}
                    size="small"
                    className="ml-2 p-1"
                  />
                </div>
              ) : (
                // 客服发送的消息
                <Bubble
                  content={renderMessageInChat(message)}
                  placement="end"
                  avatar={<Avatar src={<img src={defaultAvatar} />} />}
                />
              )}
              {/* 修改时间显示的样式 */}
              <div
                className={cn(
                  'mt-1 text-xs text-gray-400 opacity-0 transition-opacity duration-200 group-hover:opacity-100',
                  message.direction === 1 ? 'text-left' : 'text-right',
                  message.direction === 1 ? 'ml-12' : 'mr-12',
                )}
              >
                {message.messageCreateTime
                  ? new Date(message.messageCreateTime).toLocaleString()
                  : ''}
              </div>
            </div>
          ))}
        </div>

        {/* 添加没有更多消息的提示 */}
        {!hasNextPage && messages.length > 0 && (
          <div className="flex justify-center py-2 text-sm text-gray-400">没有更多消息了</div>
        )}

        {/* 加载状态 */}
        {isFetchingNextPage && (
          <div className="flex justify-center py-2">
            <Spin size="small" />
          </div>
        )}

        {/* 加载更多的参考点 */}
        <div ref={loadMoreRef} className="h-1 w-full" />

        {/* 无消息提示 */}
        {!isLoading && messages.length === 0 && (
          <div className="flex h-full items-center justify-center text-gray-400">暂无聊天记录</div>
        )}
      </div>
    );
  },
);

// 添加类型定义
export type ChatMessagesRef = {
  scrollToBottom: () => void;
};
