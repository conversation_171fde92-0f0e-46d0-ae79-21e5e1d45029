import defaultAvatar from '@/assets/default-avatar.png';
import { cn } from '@/lib/utils';
import {
  GetChatDataStatistics,
  GetChatValid,
  PostChatSend,
  SendType,
} from '@/services/douyin-im/chat';
import { MessageType, UpdateConversationStarStatus } from '@/services/douyin-im/conversation';
import { StarFilled, StarOutlined } from '@ant-design/icons';
import { useQueryClient, useRequest } from '@umijs/max';
import { useLocalStorageState } from 'ahooks';
import { Avatar, Button, message, Tooltip } from 'antd';
import { useAtomValue, useSetAtom } from 'jotai';
import { forwardRef, useImperativeHandle, useRef, useState } from 'react';
import { customerTypeAtom, retentionStatusAtom, selectedConversationAtom } from '../atoms/userAtom';
import ChatMessages, { ChatMessagesRef, ChatMessageType } from './chatMessages'; // 导入新的聊天记录组件
import QuickReply from './toolbar/quickReply';
import UploadButton from './toolbar/uploadButton';
import BusinessCard from './toolbar/businessCard';
import EmojiPicker from './toolbar/emojiPicker';
import { lastSendMessageIdAtom } from '../atoms/sseAtom';
import { useDarkMode } from '@/hooks/useDarkMode';

// 添加一个新的类型定义用于发送消息
type SendMessageParams = {
  content: string;
  messageType: MessageType;
  sendType: SendType;
  extraParams?: Record<string, any>;
};

// 添加 URL 检测的正则表达式
const URL_REGEX = /(https?:\/\/[^\s]+)|(www\.[^\s]+)/;

export type ChatContentRef = {
  refreshValidData: () => void;
};

const ChatContent = (props: any, ref: React.Ref<ChatContentRef>) => {
  const queryClient = useQueryClient();
  const { isDarkMode } = useDarkMode();
  const selectedUser = useAtomValue(selectedConversationAtom);
  const setSelectedConversation = useSetAtom(selectedConversationAtom);
  const [text, setText] = useState('');
  const [projectId] = useLocalStorageState<string | null>('customer_system_project_id', {
    listenStorageChange: true,
  });
  const customerType = useAtomValue(customerTypeAtom);
  const retentionStatus = useAtomValue(retentionStatusAtom);
  const [realtimeMessages, setRealtimeMessages] = useState<ChatMessageType[]>([]);
  const chatMessagesRef = useRef<ChatMessagesRef>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const [isAIReplyLoading, setIsAIReplyLoading] = useState(false);
  // 添加 URL 检测状态
  const [hasUrl, setHasUrl] = useState(false);
  const setLastSendMessageId = useSetAtom(lastSendMessageIdAtom);
  // 添加输入法组合状态 中文输入法按下 enter 会触发发送
  const [isComposing, setIsComposing] = useState(false);

  const { data: validData, refresh: refreshValidData } = useRequest(
    () => {
      if (!projectId || !selectedUser?.conversationId) return Promise.reject();
      return GetChatValid({
        conversationId: selectedUser.conversationId,
        projectId,
      });
    },
    {
      refreshDeps: [projectId, selectedUser?.conversationId],
      ready: !!projectId && !!selectedUser?.conversationId,
    },
  );

  // 有新消息推送的时候也触发一次 暴露给父组件
  useImperativeHandle(ref, () => ({
    refreshValidData,
  }));

  const { data: chatDataStatistics } = useRequest(
    () => {
      if (!projectId) return Promise.reject();
      return GetChatDataStatistics({ projectId });
    },
    {
      ready: !!projectId,
      refreshDeps: [projectId],
    },
  );

  // 添加统一的消息发送处理函数
  const handleSendMessage = async ({
    content,
    messageType,
    sendType,
    extraParams = {},
  }: SendMessageParams) => {
    if (!projectId || !selectedUser?.conversationId) return;

    // 先创建本地消息对象
    const localMessage: ChatMessageType = {
      serverMessageId: `local-${Date.now()}`,
      conversationId: selectedUser.conversationId,
      content,
      messageType,
      messageEvent: 0,
      direction: 2,
      messageCreateTime: new Date().toISOString(),
    };

    // 立即更新本地消息列表
    setRealtimeMessages((prev) => [localMessage, ...prev]);

    // 确保在状态更新后执行滚动
    Promise.resolve().then(() => {
      chatMessagesRef.current?.scrollToBottom();
    });

    try {
      // 发送消息到服务器
      const res = await PostChatSend({
        projectId,
        sendType,
        conversationId: selectedUser.conversationId,
        ...extraParams,
      });

      setLastSendMessageId(res.data?.serverMessageId);

      refreshValidData();
    } catch (error) {
      message.error('发送失败');
      // 从消息列表中移除失败的本地消息
      setRealtimeMessages((prev) =>
        prev.filter((msg) => msg.serverMessageId !== localMessage.serverMessageId),
      );
    }
  };

  // 修改文本输入处理函数
  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newText = e.target.value;
    setText(newText);

    // 检测文本中是否包含 URL
    const containsUrl = URL_REGEX.test(newText);
    setHasUrl(containsUrl);
  };

  // 修改发送处理函数
  const handleSend = async () => {
    if (text.trim() && !hasUrl) {
      setText('');
      await handleSendMessage({
        content: text,
        messageType: MessageType.Text,
        sendType: SendType.TEXT,
        extraParams: { text },
      });
    }
  };

  const handleKeyDown = (
    e: React.KeyboardEvent<HTMLTextAreaElement>,
    sendStatus?: boolean,
    messageInfo?: string,
  ) => {
    // 如果正在输入法组合输入中，不处理任何按键事件
    if (isComposing) {
      return;
    }

    if (e.key === 'Enter') {
      // Shift + Enter 或 Alt + Enter 换行
      if (e.altKey || e.shiftKey) {
        e.preventDefault(); // 阻止默认行为
        const textarea = e.currentTarget;
        const start = textarea.selectionStart;
        const end = textarea.selectionEnd;

        // 在光标位置插入换行符
        setText((prevText) => prevText.substring(0, start) + '\n' + prevText.substring(end));

        // 使用 requestAnimationFrame 确保在下一帧设置光标位置
        requestAnimationFrame(() => {
          textarea.selectionStart = textarea.selectionEnd = start + 1;
        });
        return;
      }
      // 普通 Enter 发送消息
      e.preventDefault();
      if (hasUrl) {
        message.error('不支持 URL 格式');
        return;
      }
      if (sendStatus) {
        handleSend();
      } else {
        message.error('发送失败: ' + messageInfo);
      }
    }
  };

  const handleStar = async (starStatus: number) => {
    if (!projectId || !selectedUser?.conversationId) return;
    await UpdateConversationStarStatus({
      conversationId: selectedUser.conversationId,
      projectId,
      starStatus,
    });

    // 直接本地更新选中的会话
    setSelectedConversation({
      ...selectedUser,
      starStatus,
    });

    // 更新会话列表中的数据
    queryClient.setQueryData(
      ['conversations', projectId, customerType, retentionStatus],
      (oldData: any) => {
        if (!oldData?.pages?.length) return oldData;

        const updatedPages = [...oldData.pages];

        // 找到并移除当前会话
        let targetConversation: any;
        updatedPages.forEach((page) => {
          const index = page.data.findIndex(
            (conversation: any) => conversation.conversationId === selectedUser.conversationId,
          );
          if (index !== -1) {
            targetConversation = {
              ...page.data[index],
              starStatus,
            };
            page.data.splice(index, 1);
          }
        });

        if (targetConversation) {
          const firstPage = updatedPages[0].data;
          // 找到最后一个星标会话的位置
          const lastStarredIndex = firstPage.findIndex((item: any) => item.starStatus !== 1);

          // 如果找不到非星标会话,说明全是星标,添加到末尾
          // 否则插入到第一个非星标会话的位置
          if (lastStarredIndex === -1) {
            firstPage.push(targetConversation);
          } else {
            firstPage.splice(lastStarredIndex, 0, targetConversation);
          }
        }

        return {
          ...oldData,
          pages: updatedPages,
        };
      },
    );
  };

  const setAIReplyInText = (value: string, loading?: boolean) => {
    if (loading !== undefined) {
      setIsAIReplyLoading(loading);
    }
    setText(value);
    textareaRef.current?.focus();
  };

  const handleUploadSuccess = async (imageUrl: string) => {
    await handleSendMessage({
      content: imageUrl,
      messageType: MessageType.Image,
      sendType: SendType.IMAGE,
      extraParams: { imageUrl },
    });
  };

  const handleEmojiSelect = (emoji: string) => {
    const textarea = textareaRef.current;
    if (!textarea) {
      setText((prev) => prev + emoji);
      return;
    }

    // 先获取当前光标位置
    const start = textarea.selectionStart ?? 0;
    const end = textarea.selectionEnd ?? 0;

    // 使用函数式更新确保拿到最新的 text 值
    setText((prevText) => {
      const newText = prevText.slice(0, start) + emoji + prevText.slice(end);

      // 使用 requestAnimationFrame 确保在下一帧更新光标位置
      requestAnimationFrame(() => {
        textarea.focus();
        const newPosition = start + emoji.length;
        textarea.setSelectionRange(newPosition, newPosition);
      });

      return newText;
    });
  };

  const handleBusinessCardSubmit = async (values: {
    imageUrl: string;
    title: string;
    componentList: number[];
  }) => {
    await handleSendMessage({
      content: JSON.stringify(values),
      messageType: MessageType.RetainConsultCard,
      sendType: SendType.RETAIN_CONSULT_CARD,
      extraParams: values,
    });
  };

  if (!selectedUser) {
    return (
      <div className="flex h-full items-center justify-center text-gray-400">
        请选择一个联系人开始聊天
      </div>
    );
  }

  return (
    <>
      {/* 顶部信息栏 */}
      <div className="flex items-center justify-between border-b border-gray-100 px-6 py-5">
        <div className="flex items-center gap-16">
          <div className="flex flex-col items-start gap-1">
            <div className="text-new-media-gray-600">接待中客户</div>
            <div className="text-base font-medium">{chatDataStatistics?.receptionCount ?? '-'}</div>
          </div>
          <div className="flex flex-col items-start gap-1">
            <div className="text-new-media-gray-600">今日接待数</div>
            <div className="text-base font-medium">
              {chatDataStatistics?.totalReceptionCount ?? '-'}
            </div>
          </div>
          <div className="flex flex-col items-start gap-1">
            <div className="text-new-media-gray-600">留资数</div>
            <div className="text-base font-medium">{chatDataStatistics?.leadsCount ?? '-'}</div>
          </div>
        </div>
      </div>
      <div className="flex w-full items-center justify-between gap-4 px-4 py-4">
        <div className="flex items-center gap-2">
          <Avatar src={selectedUser.customerInfo?.avatar || <img src={defaultAvatar} />} />
          <div className="ml-1 flex flex-col items-start gap-1">
            <div className="font-medium">{selectedUser.customerInfo?.nickname}</div>
          </div>
        </div>
        {selectedUser.starStatus ? (
          <StarFilled
            className="text-yellow-400"
            style={{ fontSize: 20 }}
            onClick={() => handleStar(0)}
          />
        ) : (
          <StarOutlined
            className="text-gray-400"
            style={{ fontSize: 20 }}
            onClick={() => handleStar(1)}
          />
        )}
      </div>

      <div className="flex flex-1 flex-col overflow-hidden">
        {/* 聊天记录呈现区域 */}
        <ChatMessages
          ref={chatMessagesRef}
          realtimeMessages={realtimeMessages}
          setRealtimeMessages={setRealtimeMessages}
          setAIReplyInText={setAIReplyInText}
        />

        {/* 底部发送消息区域 - 固定在底部 */}
        <div className="relative flex h-56 flex-shrink-0 flex-col items-start border-t border-gray-100 bg-white px-4 pb-4">
          <div className="flex w-full gap-2 py-2">
            <EmojiPicker onEmojiSelect={handleEmojiSelect} />
            <BusinessCard onSubmit={handleBusinessCardSubmit} />
            <UploadButton onUploadSuccess={handleUploadSuccess} />
            <QuickReply setText={setText} textareaRef={textareaRef} />
          </div>
          <textarea
            className={cn(
              'w-full resize-none rounded-lg border-none pb-3 pt-1 focus:outline-none',
              hasUrl && 'text-red-500',
              isDarkMode && 'bg-[#202023]',
            )}
            rows={7}
            placeholder={
              isAIReplyLoading
                ? 'AI 正在思考中...'
                : '回复内容，按Enter发送，按Shift+Enter或Alt+Enter换行'
            }
            value={text}
            onChange={handleTextChange}
            onKeyDown={(e) => handleKeyDown(e, validData?.sendStatus, validData?.message)}
            onCompositionStart={() => setIsComposing(true)}
            onCompositionEnd={() => setIsComposing(false)}
            autoFocus
            ref={textareaRef}
          />

          {hasUrl ? (
            <div className="absolute bottom-6 right-6 text-red-500">不支持 URL 格式</div>
          ) : isAIReplyLoading ? (
            <div className="absolute bottom-6 right-6 flex items-center gap-2 rounded-lg bg-gray-100 px-4 py-2">
              <div
                className="h-4 w-4 animate-spin rounded-sm bg-blue-600"
                style={{ animationDuration: '3s' }}
              />
              <span className="text-sm text-gray-500">AI 思考中...</span>
            </div>
          ) : validData?.sendStatus ? (
            <Button
              type="primary"
              className="absolute bottom-6 right-6"
              onClick={handleSend}
              disabled={hasUrl}
            >
              发送
            </Button>
          ) : (
            <Tooltip title={validData?.message}>
              <Button
                type="primary"
                className="absolute bottom-6 right-6"
                onClick={handleSend}
                disabled={!validData?.sendStatus || hasUrl}
              >
                发送
              </Button>
            </Tooltip>
          )}
        </div>
      </div>
    </>
  );
};

export default forwardRef(ChatContent);
