import { CloseOutlined } from '@ant-design/icons';
import {
  ProForm,
  ProFormDatePicker,
  ProFormDigit,
  ProFormInstance,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { Avatar, message, Popover, Tooltip } from 'antd';
import { useAtomValue } from 'jotai';
import { selectedConversationAtom } from '../atoms/userAtom';
import { useRef, useState } from 'react';

import {
  PrivacyMessageCustomerQuery,
  PrivacyMessageCustomerUpdate,
} from '@/services/douyin-im/privacy-message';
import { useRequest } from '@umijs/max';
import { useLocalStorageState } from 'ahooks';
import defaultAvatar from '@/assets/default-avatar.png';
import TagsManager from './tagsManager';
import { UpdateObjectTags, TagsType } from '@/services/tag';

type UserInfoProps = {
  previewConversationId?: string;
  previewProjectId?: string;
  previewMode?: boolean;
};

export default function UserInfo({
  previewConversationId,
  previewProjectId,
  previewMode,
}: UserInfoProps) {
  const selectedConversation = useAtomValue(selectedConversationAtom);
  const conversationId = previewMode ? previewConversationId : selectedConversation?.conversationId;
  const [storedProjectId] = useLocalStorageState<string | null>('customer_system_project_id', {
    listenStorageChange: true,
  });
  const projectId = previewMode ? previewProjectId : storedProjectId;
  const formRef = useRef<ProFormInstance>(null);
  // 表单聚焦正在输入的时候跳过轮询
  const [isFormFocused, setIsFormFocused] = useState(false);
  const [popoverVisible, setPopoverVisible] = useState(false);

  // 获取用户详情
  const {
    data: customerInfo,
    loading,
    refresh: refreshCustomerInfo,
  } = useRequest(
    () => {
      if (!conversationId || !projectId) return Promise.reject();
      return PrivacyMessageCustomerQuery({
        conversationId,
        projectId,
      });
    },
    {
      refreshDeps: [conversationId],
      ready: !!conversationId && !!projectId && !isFormFocused,
      pollingInterval: 1 * 60 * 1000,
      onSuccess: (data) => {
        if (formRef.current) {
          formRef.current.setFieldsValue(data);
        }
      },
    },
  );

  const handleTagsUpdate = () => {
    refreshCustomerInfo();
  };

  const handleSubmit = async (values: any) => {
    if (!conversationId || !projectId) return false;
    try {
      await PrivacyMessageCustomerUpdate({
        ...values,
        conversationId,
        projectId,
        gender: values.gender || 0,
      });
      return true;
    } catch (error) {
      message.error('更新失败');
      return false;
    }
  };

  const handleDeleteTag = (tagId?: number, tagIds?: (number | undefined)[]) => {
    UpdateObjectTags({
      type: TagsType.customTag,
      objectId: customerInfo?.customerOpenId || '',
      tagIds: tagIds?.filter((t) => t !== tagId) as number[],
    })
      .then(() => {
        handleTagsUpdate();
      })
      .catch(() => {
        message.error('删除标签失败');
      });
  };

  if (!selectedConversation && !previewMode) {
    return (
      <div className="flex h-full items-center justify-center text-gray-400">
        请选择一个联系人查看详情
      </div>
    );
  }

  return (
    <div className="flex h-full flex-col pb-16">
      {/* 用户基本信息展示区 */}
      <div className="p-4">
        <div className="flex items-center gap-3">
          <Avatar
            size={36}
            src={customerInfo?.douyinAvatar || <img src={defaultAvatar} />}
            crossOrigin="anonymous"
          />
          <div className="flex min-w-0 flex-1 items-start overflow-hidden">
            <div className="flex w-full min-w-0 flex-col items-start gap-1">
              <h4 className="text-lg font-medium">{customerInfo?.douyinNickname}</h4>
              <Tooltip title={customerInfo?.customerOpenId}>
                <span className="w-full truncate text-gray-500">
                  用户ID: {customerInfo?.customerOpenId}
                </span>
              </Tooltip>
            </div>
          </div>
        </div>
        <div className="my-2">
          <div className="flex flex-wrap gap-2">
            {customerInfo?.tags?.map((tag) => (
              <div
                key={tag.id}
                className="rounded bg-new-media-blue-200 px-2 py-[2px] text-new-media-blue-900"
              >
                {tag.name}
                <CloseOutlined
                  className="ml-1 cursor-pointer"
                  style={{ fontSize: 10 }}
                  onClick={() => {
                    handleDeleteTag(tag.id, customerInfo?.tags?.map((t) => t.id) || []);
                  }}
                />
              </div>
            ))}
            <Popover
              trigger="click"
              content={
                <TagsManager
                  selectedIds={customerInfo?.tags?.map((tag) => tag.id) || []}
                  userId={customerInfo?.customerOpenId}
                  handleTagsUpdate={handleTagsUpdate}
                  projectId={projectId}
                  onCancel={() => setPopoverVisible(false)}
                />
              }
              open={popoverVisible}
              onOpenChange={setPopoverVisible}
            >
              <div className="flex cursor-pointer items-center rounded bg-gray-100 px-2 py-[2px] text-gray-500 hover:bg-gray-200">
                <span className="mr-1">+</span>
                添加
              </div>
            </Popover>
          </div>
        </div>
        <div className="mt-3 space-y-2 text-new-media-gray-600">
          <div>来源: {customerInfo?.imEnterUserType ? '广告' : '自然流量'}</div>
          <div>计划名称: {customerInfo?.imEnterAdName || '--'}</div>
          <div>创意 id: {customerInfo?.imEnterCreativeId || '--'}</div>
          <div>广告主名称: {customerInfo?.imEnterAdvName || '--'}</div>
          <div>广告主 id: {customerInfo?.imEnterAdvId || '--'}</div>
        </div>
      </div>

      {/* 分割线 */}
      <div className="h-[1px] w-full bg-gray-100" />

      {/* 表单区域 */}
      <div className="no-scrollbar min-h-0 flex-1 overflow-hidden overflow-y-auto p-4">
        <ProForm
          autoFocusFirstInput={false}
          onFinish={handleSubmit}
          layout="horizontal"
          labelAlign="left"
          title="详细信息备注"
          loading={loading}
          formRef={formRef}
          submitter={false}
          onFocus={() => setIsFormFocused(true)}
          onBlur={() => setIsFormFocused(false)}
        >
          <ProFormText
            name="remark"
            label="备注"
            placeholder="请输入相关信息"
            fieldProps={{
              maxLength: 50,
              onBlur: () => formRef.current?.submit(),
              onPressEnter: () => formRef.current?.submit(),
            }}
          />
          <ProFormText
            name="name"
            label="姓名"
            placeholder="请输入姓名"
            fieldProps={{
              maxLength: 5,
              onBlur: () => formRef.current?.submit(),
              onPressEnter: () => formRef.current?.submit(),
            }}
          />
          <ProFormText
            name="phone"
            label="电话"
            placeholder="请输入电话"
            fieldProps={{
              onBlur: () => formRef.current?.submit(),
              onPressEnter: () => formRef.current?.submit(),
            }}
          />
          <ProFormSelect
            name="gender"
            label="性别"
            options={[
              { label: '男', value: 1 },
              { label: '女', value: 2 },
              { label: '未知', value: 0 },
            ]}
            fieldProps={{
              onBlur: () => formRef.current?.submit(),
              onSelect: () => formRef.current?.submit(),
            }}
          />
          <ProFormText
            name="region"
            label="地区"
            placeholder="请输入地区"
            fieldProps={{
              onBlur: () => formRef.current?.submit(),
              onPressEnter: () => formRef.current?.submit(),
            }}
          />
          <ProFormText
            name="address"
            label="地址"
            placeholder="请输入地址"
            fieldProps={{
              maxLength: 50,
              onBlur: () => formRef.current?.submit(),
              onPressEnter: () => formRef.current?.submit(),
            }}
          />
          <ProFormDigit
            name="age"
            label="年龄"
            placeholder="请输入年龄"
            fieldProps={{
              onBlur: () => formRef.current?.submit(),
              onPressEnter: () => formRef.current?.submit(),
            }}
          />
          <ProFormDatePicker
            name="birthday"
            label="生日"
            placeholder="请输入生日"
            width={210}
            fieldProps={{
              onBlur: () => formRef.current?.submit(),
              onChange: () => formRef.current?.submit(),
            }}
          />
          <ProFormText
            name="wechat"
            label="微信"
            placeholder="请输入微信"
            fieldProps={{
              onBlur: () => formRef.current?.submit(),
              onPressEnter: () => formRef.current?.submit(),
            }}
          />
          <ProFormText
            name="intentionCarModel"
            label="意向车型"
            placeholder="请输入意向车型"
            fieldProps={{
              maxLength: 10,
              onBlur: () => formRef.current?.submit(),
              onPressEnter: () => formRef.current?.submit(),
            }}
          />
        </ProForm>
      </div>
    </div>
  );
}
