import useProjectKey from '@/hooks/useProjectKey';
import { GetTagGroups, TagsType, UpdateObjectTags } from '@/services/tag';
import { SearchOutlined } from '@ant-design/icons';
import { useRequest } from '@umijs/max';
import { Button, Checkbox, Empty, Input, message, Space } from 'antd';
import { useEffect, useState } from 'react';

type TagsModalProps = {
  selectedIds: (number | undefined)[];
  userId?: string;
  handleTagsUpdate: () => void;
  projectId?: string | null;
  onCancel?: () => void;
};

export default function TagsManager(props: TagsModalProps) {
  const { selectedIds, userId, handleTagsUpdate, projectId, onCancel } = props;
  const [searchKey, setSearchKey] = useState('');
  const [tempSelectedTagIds, setTempSelectedTagIds] = useState<(number | undefined)[]>(selectedIds);
  const projectKey = useProjectKey(projectId);

  const { data: tagGroups } = useRequest(
    () => {
      if (!projectId) return Promise.reject();
      return GetTagGroups(TagsType.customTag, {
        projectId,
      });
    },
    {
      refreshDeps: [projectId],
    },
  );

  const tagGroupFilter = searchKey
    ? tagGroups?.filter((tagGroup) => tagGroup.tags.some((tag) => tag.name.includes(searchKey)))
    : tagGroups;

  const handleChange = (tag: number, checked: boolean) => {
    const nextSelectedTags = checked
      ? [...tempSelectedTagIds, tag]
      : tempSelectedTagIds.filter((t) => t !== tag);
    setTempSelectedTagIds(nextSelectedTags);
  };

  const handleConfirm = async () => {
    if (!userId) return;
    try {
      await UpdateObjectTags({
        type: TagsType.customTag,
        objectId: userId,
        tagIds: tempSelectedTagIds.filter((id) => id !== undefined) as number[],
      });
      handleTagsUpdate();
      onCancel?.();
    } catch (error) {
      message.error('修改标签失败');
    }
  };

  const handleManageTags = () => {
    if (!projectKey) return;
    window.open(`${window.location.origin}/project/${projectKey}/clue/tags`, '_blank');
  };

  useEffect(() => {
    setTempSelectedTagIds(selectedIds);
  }, [selectedIds]);

  return (
    <div className="w-[300px]">
      <Input
        placeholder="输入关键词检索标签和标签组"
        onChange={(e) => setSearchKey(e.target.value)}
        prefix={<SearchOutlined className="text-gray-400" />}
        className="mb-3"
      />

      <div className="max-h-[300px] overflow-y-auto">
        {tagGroupFilter?.length ? (
          tagGroupFilter.map((group) => (
            <div key={group.id} className="mb-4">
              <div className="mb-2 text-sm text-gray-500">{group.name}</div>
              <div className="flex flex-wrap gap-2">
                {group.tags.map((tag) => (
                  <div key={tag.id}>
                    <Checkbox
                      checked={tempSelectedTagIds.includes(tag.id)}
                      onChange={(e) => handleChange(tag.id, e.target.checked)}
                    >
                      <div className="flex cursor-pointer items-center rounded bg-gray-100 px-2 py-[2px] text-gray-500 hover:bg-gray-200">
                        {tag.name}
                      </div>
                    </Checkbox>
                  </div>
                ))}
              </div>
            </div>
          ))
        ) : (
          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
        )}
      </div>

      <div className="mt-3 flex items-center justify-between border-t border-gray-100 pt-3">
        <Button type="link" onClick={handleManageTags} className="p-0">
          管理标签
        </Button>
        <Space>
          <Button onClick={onCancel}>取消</Button>
          <Button type="primary" onClick={handleConfirm}>
            确定
          </Button>
        </Space>
      </div>
    </div>
  );
}
