import { SvgIcon } from '@/components/SvgIcon';
import { Upload, message } from 'antd';
import type { UploadProps } from 'antd';
import { useState } from 'react';
import { uploadImage } from '@/utils/oss';
import { cn } from '@/lib/utils';

// 添加 props 类型定义
type UploadButtonProps = {
  onUploadSuccess?: (imageUrl: string) => void;
};

export default function UploadButton({ onUploadSuccess }: UploadButtonProps) {
  const [isUploading, setIsUploading] = useState(false);

  const beforeUpload = (file: File) => {
    // 检查文件大小（小于 10MB）
    const isLt10M = file.size / 1024 / 1024 < 10;
    if (!isLt10M) {
      message.error('图片必须小于 10MB！');
      return Upload.LIST_IGNORE;
    }
    return true;
  };

  const customRequest: UploadProps['customRequest'] = async (options) => {
    const { file, onSuccess, onError } = options;

    if (!(file instanceof File)) {
      return;
    }

    try {
      setIsUploading(true);
      const key = `customer-system/images/${Date.now()}-${file.name}`;

      const result = await uploadImage({
        file,
        key,
      });

      const imageUrl = `https://media-1307444343.cos.ap-guangzhou.myqcloud.com/${key}`;

      onUploadSuccess?.(imageUrl);
      onSuccess?.(result);
    } catch (error) {
      message.error('上传失败，请重试！');
      onError?.(error as Error);
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <Upload
      accept=".jpg,.jpeg,.png,.bmp,.webp"
      showUploadList={false}
      beforeUpload={beforeUpload}
      customRequest={customRequest}
      disabled={isUploading}
      multiple // 启用多选
    >
      <SvgIcon
        icon="local:outline/upload-button"
        width="25"
        height="25"
        x={2}
        className={cn(
          'cursor-pointer transition-colors duration-200',
          isUploading ? 'text-blue-700' : 'hover:text-blue-500',
          { 'opacity-50': isUploading },
        )}
      />
    </Upload>
  );
}
