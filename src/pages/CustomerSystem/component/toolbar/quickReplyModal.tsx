import { cn } from '@/lib/utils';
import {
  QuickReplyData,
  QuickReplyDelete,
  QuickReplyGroupData,
  QuickReplyGroupDelete,
  QuickReplyGroupList,
  QuickReplyGroupSave,
  QuickReplyGroupUpdate,
  QuickReplySave,
  ReplyData,
  QuickReplyUpdate,
} from '@/services/douyin-im/quick-reply';
import { DeleteOutlined, EditOutlined } from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Button, Input, message, Popconfirm, Tooltip } from 'antd';
import { CirclePlus } from 'lucide-react';
import React, { useRef, useState, useEffect } from 'react';

const hoverTime = 1000;

// 添加props类型定义
type QuickReplyModalProps = {
  onSelectReply?: (content: string) => void;
};

const QuickReplyModal = ({ onSelectReply }: QuickReplyModalProps) => {
  const modalRef = useRef<HTMLDivElement>(null);
  const [isAddingGroup, setIsAddingGroup] = useState(false);
  const [selectedGroupId, setSelectedGroupId] = useState<number | null>(null);
  const [editingGroupId, setEditingGroupId] = useState<number | null>(null);
  const [newGroupName, setNewGroupName] = useState('');

  const [replyId, setReplyId] = useState<number | null>(null);
  const [isAddingReply, setIsAddingReply] = useState(false);
  const [newReplyName, setNewReplyName] = useState('');

  // 添加悬停状态
  const [hoveredGroupId, setHoveredGroupId] = useState<number | null>(null);
  const [showGroupActions, setShowGroupActions] = useState(false);
  const [hoveredReplyId, setHoveredReplyId] = useState<number | null>(null);
  const [showReplyActions, setShowReplyActions] = useState(false);

  // 悬停计时器引用
  const groupHoverTimerRef = useRef<NodeJS.Timeout | null>(null);
  const replyHoverTimerRef = useRef<NodeJS.Timeout | null>(null);

  // 添加一个状态来控制是否始终显示操作按钮
  const [alwaysShowActions, setAlwaysShowActions] = useState(false);

  // 回复编辑状态
  const [editingReplyId, setEditingReplyId] = useState<number | null>(null);

  const { data: groupsData = [], refresh: refreshGroups } = useRequest(QuickReplyGroupList, {
    onSuccess: (data) => {
      if (data && data.length > 0) {
        setSelectedGroupId(data[0].id);
      }
    },
  });

  const { data: replyData, refresh: refreshReply } = useRequest(
    () => {
      if (!selectedGroupId) return Promise.reject();
      return QuickReplyData({ groupId: selectedGroupId, page: 1, size: 999 });
    },
    {
      ready: !!selectedGroupId,
      refreshDeps: [selectedGroupId],
    },
  );

  // ====== 管理分组 =======

  const handleDeleteGroup = async (id: number) => {
    if (groupsData && groupsData.length <= 1) {
      message.warning('请至少保留一个分组');
      return;
    }
    await QuickReplyGroupDelete({
      id,
    });
    setSelectedGroupId(null);
    refreshGroups();
  };

  const handleEditGroup = async (id: number, groupName: string) => {
    await QuickReplyGroupUpdate({
      id,
      groupName,
    });
    setEditingGroupId(null);
    refreshGroups();
  };

  const handleAddGroup = () => {
    setIsAddingGroup(true);
  };

  const handleConfirmAddGroup = async (e?: React.MouseEvent) => {
    e?.stopPropagation();
    if (newGroupName.trim()) {
      await QuickReplyGroupSave({
        groupName: newGroupName.trim(),
      });
      setIsAddingGroup(false);
      setNewGroupName('');
      refreshGroups();
    }
  };

  const handleCancelAddGroup = (e?: React.MouseEvent) => {
    e?.stopPropagation();
    setIsAddingGroup(false);
    setNewGroupName('');
  };

  // ====== 管理回复 ======

  const handleDeleteReply = async (id: number) => {
    await QuickReplyDelete([id]);
    refreshReply();
  };

  const handleConfirmAddReply = async (e?: React.MouseEvent) => {
    e?.stopPropagation();
    if (newReplyName.trim() && selectedGroupId) {
      await QuickReplySave({
        groupId: selectedGroupId,
        content: newReplyName.trim(),
      });
      setIsAddingReply(false);
      setNewReplyName('');
      refreshReply();
    }
  };

  const handleCancelAddReply = (e?: React.MouseEvent) => {
    e?.stopPropagation();
    setIsAddingReply(false);
    setNewReplyName('');
  };

  const handleAddReply = () => {
    setIsAddingReply(true);
  };

  // 处理分组悬停
  const handleGroupMouseEnter = (groupId: number) => {
    setHoveredGroupId(groupId);
    if (selectedGroupId === groupId) {
      // 清除之前的计时器
      if (groupHoverTimerRef.current) {
        clearTimeout(groupHoverTimerRef.current);
        groupHoverTimerRef.current = null;
      }
      // 设置新的计时器
      groupHoverTimerRef.current = setTimeout(() => {
        setShowGroupActions(true);
      }, hoverTime);
    }
  };

  const handleGroupMouseLeave = () => {
    // 清除计时器
    if (groupHoverTimerRef.current) {
      clearTimeout(groupHoverTimerRef.current);
      groupHoverTimerRef.current = null;
    }
    // 重置状态
    setHoveredGroupId(null);
    setShowGroupActions(false);
  };

  // 处理回复悬停
  const handleReplyMouseEnter = (replyId: number) => {
    setHoveredReplyId(replyId);
    // 清除之前的计时器
    if (replyHoverTimerRef.current) {
      clearTimeout(replyHoverTimerRef.current);
      replyHoverTimerRef.current = null;
    }
    // 设置新的计时器
    replyHoverTimerRef.current = setTimeout(() => {
      setShowReplyActions(true);
    }, hoverTime);
  };

  const handleReplyMouseLeave = () => {
    // 清除计时器
    if (replyHoverTimerRef.current) {
      clearTimeout(replyHoverTimerRef.current);
      replyHoverTimerRef.current = null;
    }
    // 重置状态
    setHoveredReplyId(null);
    setShowReplyActions(false);
  };

  // 组件卸载时清除计时器
  useEffect(() => {
    return () => {
      if (groupHoverTimerRef.current) {
        clearTimeout(groupHoverTimerRef.current);
      }
      if (replyHoverTimerRef.current) {
        clearTimeout(replyHoverTimerRef.current);
      }
    };
  }, []);

  // 处理选择回复
  const handleSelectReply = (content: string) => {
    if (onSelectReply) {
      onSelectReply(content);
    }
  };

  // 添加编辑回复的处理函数
  const handleEditReply = async (id: number, content: string, groupId: number) => {
    await QuickReplyUpdate({
      id,
      groupId,
      content,
    });
    setEditingReplyId(null);
    refreshReply();
  };

  const renderMenuItem = (group: QuickReplyGroupData) => {
    if (editingGroupId === group.id) {
      return (
        <Input
          autoFocus
          defaultValue={group.groupName}
          onBlur={(e) => handleEditGroup(group.id, e.target.value)}
          onPressEnter={(e) => handleEditGroup(group.id, (e.target as HTMLInputElement).value)}
          onClick={(e) => e.stopPropagation()}
          placeholder="输入名称后按enter确认"
          style={{ height: '42px ' }}
        />
      );
    }

    return (
      <div
        key={group.id}
        className={cn(
          'mx-1 my-1 cursor-pointer rounded-md px-3 py-2.5 transition-colors',
          selectedGroupId === group.id
            ? 'bg-[#EAEFFD] text-primary hover:bg-[#EAEFFD]'
            : 'bg-transparent text-[#1f2329] hover:bg-[#f5f8ff]',
        )}
        onClick={() => setSelectedGroupId(group.id)}
        onMouseEnter={() => handleGroupMouseEnter(group.id)}
        onMouseLeave={handleGroupMouseLeave}
      >
        <div className="flex items-center justify-between">
          <span>{group.groupName}</span>
          {selectedGroupId === group.id &&
            ((hoveredGroupId === group.id && showGroupActions) || alwaysShowActions) && (
              <div className="flex gap-3">
                <Tooltip title="编辑分组名称">
                  <EditOutlined
                    onClick={(e) => {
                      e.stopPropagation();
                      setEditingGroupId(group.id);
                    }}
                  />
                </Tooltip>
                <Popconfirm
                  title="确定要删除吗？"
                  onConfirm={() => handleDeleteGroup(group.id)}
                  okText="确定"
                  cancelText="取消"
                >
                  <Tooltip title="删除分组">
                    <DeleteOutlined />
                  </Tooltip>
                </Popconfirm>
              </div>
            )}
        </div>
      </div>
    );
  };

  const renderReplyItem = (replyData: ReplyData) => {
    if (editingReplyId === replyData.id) {
      return (
        <div className="m-1">
          <Input
            autoFocus
            defaultValue={replyData.content}
            onBlur={(e) => handleEditReply(replyData.id, e.target.value, replyData.groupId)}
            onPressEnter={(e) =>
              handleEditReply(replyData.id, (e.target as HTMLInputElement).value, replyData.groupId)
            }
            onClick={(e) => e.stopPropagation()}
            placeholder="输入内容后按enter确认"
            maxLength={300}
          />
        </div>
      );
    }

    return (
      <div
        key={replyData.id}
        className={cn(
          'mx-1 my-1 cursor-pointer rounded-md px-3 py-2.5 transition-colors',
          replyId === replyData.id
            ? 'bg-new-media-blue-100 text-primary hover:bg-[#EAEFFD]'
            : 'bg-transparent text-[#1f2329] hover:bg-[#f5f8ff]',
        )}
        onClick={() => {
          if (replyId === replyData.id) {
            handleSelectReply(replyData.content);
          } else {
            setReplyId(replyData.id);
          }
        }}
        onDoubleClick={() => {
          handleSelectReply(replyData.content);
        }}
        onMouseEnter={() => handleReplyMouseEnter(replyData.id)}
        onMouseLeave={handleReplyMouseLeave}
      >
        <div className="flex items-center justify-between">
          <span>{replyData.content}</span>
          {((replyId === replyData.id && hoveredReplyId === replyData.id && showReplyActions) ||
            alwaysShowActions) && (
            <div className="flex gap-3">
              <Tooltip title="编辑回复内容">
                <EditOutlined
                  onClick={(e) => {
                    e.stopPropagation();
                    setEditingReplyId(replyData.id);
                  }}
                />
              </Tooltip>
              <Popconfirm
                title="确定要删除吗？"
                onConfirm={(e) => {
                  e?.stopPropagation();
                  handleDeleteReply(replyData.id);
                }}
                okText="确定"
                cancelText="取消"
                onOpenChange={(open, e) => {
                  e?.stopPropagation();
                }}
              >
                <Tooltip title="删除回复">
                  <DeleteOutlined onClick={(e) => e.stopPropagation()} />
                </Tooltip>
              </Popconfirm>
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div ref={modalRef} className="h-[433px] w-[690px] rounded-lg bg-white shadow-lg">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <h2 className="p py-3 pl-4 pr-3 text-sm font-bold">快捷回复</h2>
          <div className="text-xs text-gray-500">提示:将鼠标悬停1秒可显示编辑和删除按钮</div>
        </div>
        <Button
          type="link"
          size="small"
          onClick={() => setAlwaysShowActions(!alwaysShowActions)}
          className="mr-4 text-xs"
        >
          {alwaysShowActions ? '隐藏操作按钮' : '始终显示操作按钮'}
        </Button>
      </div>
      <div className="h-px bg-gray-200" />
      <div className="h-[390px]">
        <ProCard ghost split="vertical" className="h-full">
          <ProCard
            colSpan="250px"
            ghost
            style={{
              paddingTop: '5px',
              height: '100%',
            }}
            bodyStyle={{
              display: 'flex',
              flexDirection: 'column',
            }}
          >
            <div className="no-scrollbar flex-1 overflow-y-auto">
              {groupsData.map((group) => renderMenuItem(group))}
              {isAddingGroup && (
                <div className="m-1">
                  <Input
                    autoFocus
                    value={newGroupName}
                    onChange={(e) => setNewGroupName(e.target.value)}
                    onPressEnter={() => handleConfirmAddGroup()}
                    onBlur={() => handleCancelAddGroup()}
                    maxLength={10}
                    placeholder="输入名称后按enter确认"
                  />
                </div>
              )}
            </div>
            <Button
              type="link"
              icon={<CirclePlus size={14} />}
              className="mb-2 justify-start"
              onClick={handleAddGroup}
            >
              添加分组
            </Button>
          </ProCard>
          <ProCard
            ghost
            style={{ height: '100%', paddingTop: '5px' }}
            bodyStyle={{ display: 'flex', flexDirection: 'column' }}
          >
            <div className="no-scrollbar flex-1 overflow-y-auto">
              {replyData?.items?.map((item) => renderReplyItem(item))}
              {isAddingReply && (
                <div className="m-1">
                  <Input
                    autoFocus
                    value={newReplyName}
                    onChange={(e) => setNewReplyName(e.target.value)}
                    onPressEnter={() => handleConfirmAddReply()}
                    onBlur={() => handleCancelAddReply()}
                    maxLength={300}
                    placeholder="输入名称后按enter确认"
                  />
                </div>
              )}
            </div>
            <Button
              type="link"
              icon={<CirclePlus size={14} />}
              className="mb-2 justify-start"
              onClick={handleAddReply}
            >
              添加回复
            </Button>
          </ProCard>
        </ProCard>
      </div>
    </div>
  );
};

export default QuickReplyModal;
