import { Popover } from 'antd';
import QuickReplyModal from './quickReplyModal';
import { useState } from 'react';
import { SvgIcon } from '@/components/SvgIcon';
import { cn } from '@/lib/utils';

type QuickReplyProps = {
  setText: (text: string) => void;
  textareaRef: React.RefObject<HTMLTextAreaElement>;
};

export default function QuickReply({ setText, textareaRef }: QuickReplyProps) {
  const [isQuickReplyVisible, setQuickReplyVisible] = useState(false);

  // 处理快捷回复选择
  const handleQuickReplySelect = (content: string) => {
    setText(content);
    setQuickReplyVisible(false); // 选择后关闭弹窗
    textareaRef.current?.focus();
  };

  return (
    <Popover
      content={<QuickReplyModal onSelectReply={handleQuickReplySelect} />}
      title={null}
      trigger="click"
      placement="topLeft"
      overlayInnerStyle={{ padding: 0 }}
      open={isQuickReplyVisible}
      onOpenChange={(visible) => setQuickReplyVisible(visible)}
    >
      <SvgIcon
        icon="local:outline/quick-reply"
        width="23"
        height="23"
        y={2}
        className={cn('hover:text-blue-500', isQuickReplyVisible && 'text-blue-700')} // 根据状态改变颜色
        onClick={() => {
          setQuickReplyVisible(!isQuickReplyVisible);
        }} // 点击时显示快捷回复
      />
    </Popover>
  );
}
