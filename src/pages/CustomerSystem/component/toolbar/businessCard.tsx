import { useState } from 'react';
import { Popover } from 'antd';
import { SvgIcon } from '@/components/SvgIcon';
import BusinessCardForm from './businessCardForm';
import { cn } from '@/lib/utils';

type BusinessCardProps = {
  onSubmit: (values: any) => void;
};

export default function BusinessCard({ onSubmit }: BusinessCardProps) {
  const [visible, setVisible] = useState(false);

  const handleSubmit = (values: any) => {
    onSubmit(values);
    setVisible(false);
  };

  const handleCancel = () => {
    setVisible(false);
  };

  return (
    <Popover
      content={<BusinessCardForm onSubmit={handleSubmit} onCancel={handleCancel} />}
      destroyTooltipOnHide
      trigger="click"
      open={visible}
      onOpenChange={setVisible}
      placement="topRight"
      overlayClassName="business-card-popover"
    >
      <SvgIcon
        icon="local:outline/business-card"
        width="24"
        height="24"
        y={2}
        className={cn('cursor-pointer', 'hover:text-blue-500', visible && 'text-blue-700')}
      />
    </Popover>
  );
}
