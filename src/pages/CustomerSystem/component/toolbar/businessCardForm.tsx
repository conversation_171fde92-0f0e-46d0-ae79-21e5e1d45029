import { Form, Input, Button, Select, message } from 'antd';
import { useState } from 'react';
import { cn } from '@/lib/utils';
import { uploadImage } from '@/utils/oss';

type BusinessCardFormProps = {
  onSubmit: (values: { imageUrl: string; title: string; componentList: number[] }) => void;
  onCancel: () => void;
};

export default function BusinessCardForm({ onSubmit, onCancel }: BusinessCardFormProps) {
  const [form] = Form.useForm();
  const [imageUrl, setImageUrl] = useState<string>();
  const [imageFile, setImageFile] = useState<File>();

  // 处理图片选择
  const handleUpload = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        // 验证文件大小（例如最大 5MB）
        if (file.size > 5 * 1024 * 1024) {
          message.error('图片大小不能超过 5MB');
          return;
        }

        // 创建本地预览 URL
        const localUrl = URL.createObjectURL(file);
        setImageUrl(localUrl);
        setImageFile(file);
        form.setFieldValue('imageUrl', localUrl);
      }
    };
    input.click();
  };

  // 处理表单提交
  const handleSubmit = async (values: any) => {
    try {
      if (imageFile) {
        const key = `customer-system/business/images/${Date.now()}-${imageFile.name}`;
        const result = await uploadImage({ file: imageFile, key });
        // 添加 https 前缀到图片 URL
        values.imageUrl = `https://${result.Location}`;
      }
      onSubmit(values);
    } catch (error) {
      message.error('图片上传失败');
      console.error('Upload error:', error);
    }
  };

  return (
    <div className="w-[400px] p-4">
      <Form form={form} layout="vertical" onFinish={handleSubmit}>
        <Form.Item
          label="上传图片"
          name="imageUrl"
          required
          className="mb-4"
          rules={[{ required: true, message: '请上传图片' }]}
        >
          <div
            onClick={handleUpload}
            className={cn(
              'h-24 w-24 rounded-lg border-2 border-dashed border-gray-300',
              'flex cursor-pointer items-center justify-center',
              'hover:border-blue-500',
            )}
          >
            {imageUrl ? (
              <img src={imageUrl} alt="card" className="h-full w-full rounded-lg object-cover" />
            ) : (
              <div className="text-center">
                <div className="text-3xl">+</div>
                <div>点击上传</div>
              </div>
            )}
          </div>
        </Form.Item>

        <Form.Item
          label="卡片标题"
          name="title"
          required
          rules={[{ required: true, message: '请输入卡片标题' }]}
        >
          <Input placeholder="请输入" />
        </Form.Item>

        <Form.Item
          label="卡片填写框"
          name="componentList"
          required
          rules={[{ required: true, message: '至少需要1个' }]}
        >
          <Select
            mode="multiple"
            placeholder="请选择卡片填写框"
            options={[
              { label: '姓名', value: 1 },
              { label: '手机号', value: 2 },
              { label: '城市', value: 3 },
            ]}
          />
        </Form.Item>

        <div className="mt-4 flex justify-end gap-2">
          <Button
            onClick={() => {
              setImageUrl(undefined);
              setImageFile(undefined);
              onCancel();
            }}
          >
            取消
          </Button>
          <Button type="primary" htmlType="submit">
            发送
          </Button>
        </div>
      </Form>
    </div>
  );
}
