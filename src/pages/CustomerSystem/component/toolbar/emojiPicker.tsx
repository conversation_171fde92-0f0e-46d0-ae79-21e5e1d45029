import { useState } from 'react';
import { SvgIcon } from '@/components/SvgIcon';
import Picker from '@emoji-mart/react';
import { cn } from '@/lib/utils';
import { Popover, Spin } from 'antd';
import { useRequest } from 'ahooks';

type EmojiPickerProps = {
  onEmojiSelect?: (emoji: string) => void;
};

const EMOJI_DATA_URL =
  'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emoji-data/emoji-data.json';

// Picker 直接配置 locale={zh} 会触发请求国外 cdn 上的 json 还是会慢一下所以直接复制出来做本地中文
const i18n = {
  search: '搜索',
  search_no_results_1: '哦不！',
  search_no_results_2: '没有找到相关表情',
  pick: '选择一个表情…',
  add_custom: '添加自定义表情',
  categories: {
    activity: '活动',
    custom: '自定义',
    flags: '旗帜',
    foods: '食物与饮品',
    frequent: '最近使用',
    nature: '动物与自然',
    objects: '物品',
    people: '表情与角色',
    places: '旅行与景点',
    search: '搜索结果',
    symbols: '符号',
  },
  skins: {
    choose: '选择默认肤色',
    '1': '默认',
    '2': '白色',
    '3': '偏白',
    '4': '中等',
    '5': '偏黑',
    '6': '黑色',
  },
};

export default function EmojiPicker({ onEmojiSelect }: EmojiPickerProps) {
  const [isOpen, setIsOpen] = useState(false);

  const { data: emojiData } = useRequest(
    async () => {
      const response = await fetch(EMOJI_DATA_URL);
      return response.json();
    },
    {
      cacheKey: 'emojiData', // 缓存数据
      staleTime: 1000 * 60 * 60, // 1小时内不重新请求
    },
  );

  const handleEmojiSelect = (emoji: any) => {
    onEmojiSelect?.(emoji.native);
    setIsOpen(false);
  };

  return (
    <Popover
      open={isOpen}
      onOpenChange={setIsOpen}
      trigger="click"
      placement="topLeft"
      content={
        <div className="min-h-[200px] min-w-[320px]">
          {emojiData ? (
            <Picker
              data={emojiData}
              onEmojiSelect={handleEmojiSelect}
              previewPosition="none"
              skinTonePosition="none"
              searchPosition="none"
              maxFrequentRows={0}
              perLine={8}
              theme="light"
              set="native"
              height={200}
              i18n={i18n}
            />
          ) : (
            <div className="flex h-[200px] items-center justify-center">
              <Spin tip="加载中..." />
            </div>
          )}
        </div>
      }
    >
      <SvgIcon
        icon="local:outline/emoji"
        width="24"
        height="24"
        y={2}
        className={cn(
          'cursor-pointer transition-colors duration-200 hover:text-blue-500',
          isOpen && 'text-blue-700',
        )}
      />
    </Popover>
  );
}
