import { LastMessageOverview, MessageType } from '@/services/douyin-im/conversation';
import { parseDouyinEmojiToReactNode } from '@/utils/douyinEmoji';
import { ChatMessageType } from './chatMessages';

// 渲染在聊天界面消息内容的函数
export const renderMessageInChat = (message: ChatMessageType) => {
  switch (message.messageType) {
    case MessageType.Text:
      return (
        <div className="max-w-md whitespace-pre-wrap break-words">
          {parseDouyinEmojiToReactNode(message.content)}
        </div>
      );
    // 获取不到图片地址抖音官方未开放
    case MessageType.Image:
      return '[图片消息]';
    case MessageType.UserLocalImage:
      return '[图片消息]';
    case MessageType.UserLocalVideo:
      return '[视频消息]';
    case MessageType.Video:
      return '[视频消息]';
    case MessageType.Emoji:
      return '[动画表情消息]';
    case MessageType.RetainConsultCard:
      return '[留资卡片消息]';
    case MessageType.Other:
      return '[其他消息]';
    default:
      return '[其他消息]';
  }
};

// 渲染在左侧会话列表展示的消息
export const renderMessageInUserList = (message?: LastMessageOverview) => {
  if (!message) return '未知';
  switch (message.messageType) {
    case MessageType.Text:
      return message.content;
    case MessageType.Image:
      return '[图片消息]';
    case MessageType.UserLocalImage:
      return '[图片消息]';
    case MessageType.UserLocalVideo:
      return '[视频消息]';
    case MessageType.Video:
      return '[视频消息]';
    case MessageType.Emoji:
      return '[动画表情消息]';
    case MessageType.RetainConsultCard:
      return '[留资卡片消息]';
    case MessageType.Other:
      return '[其他消息]';
    default:
      return message.content;
  }
};
