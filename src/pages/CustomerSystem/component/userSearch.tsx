import { useEffect } from 'react';
import { CloseOutlined } from '@ant-design/icons';
import { useDebounce } from 'ahooks';
import {
  SearchConversation,
  UpdateConversationActiveTime,
} from '@/services/douyin-im/conversation';
import type { DouyinImManualConversation } from '@/services/douyin-im/conversation';
import { useRequest } from '@umijs/max';

type UserSearchProps = {
  searchValue: string;
  onClose: () => void;
  onSelect: (user: DouyinImManualConversation) => void;
  projectId: number;
};

export default function UserSearch(props: UserSearchProps) {
  const { searchValue: initialSearchValue, onClose, onSelect, projectId } = props;
  const debouncedSearchValue = useDebounce(initialSearchValue, { wait: 500 });

  const { data: users, loading } = useRequest(
    async () => {
      if (!debouncedSearchValue) {
        return Promise.reject();
      }
      return SearchConversation({
        customerNickname: debouncedSearchValue,
        projectId,
        page: 1,
        size: 999,
      });
    },
    {
      refreshDeps: [debouncedSearchValue, projectId],
    },
  );

  const userList = users?.items || [];

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [onClose]);

  const handleSelect = async (user: DouyinImManualConversation) => {
    if (!user.conversationId) {
      return;
    }
    onSelect(user);
    await UpdateConversationActiveTime({
      conversationId: user.conversationId,
      projectId,
    });
    onClose();
  };

  return (
    <div className="relative mx-auto h-[500px] w-[300px] rounded-lg bg-white p-4 shadow-lg">
      {/* 头部 */}
      <div className="mb-4 flex items-center justify-between">
        <div className="text-sm">已筛选出{userList.length}个会话</div>
        <CloseOutlined
          className="cursor-pointer text-gray-400 hover:text-gray-600"
          onClick={onClose}
        />
      </div>

      {/* 用户列表 */}
      <div className="space-y-4 overflow-y-auto" style={{ height: 'calc(100% - 65px)' }}>
        {loading ? (
          <div className="text-center text-gray-500">搜索中...</div>
        ) : userList.length > 0 ? (
          userList.map((user) => (
            <div
              key={user.conversationId}
              className="flex cursor-pointer items-center rounded p-2 hover:bg-gray-100"
              onClick={() => handleSelect(user)}
            >
              <div className="mr-3 h-10 w-10 overflow-hidden rounded-full">
                {user.customerInfo?.avatar ? (
                  <img
                    src={user.customerInfo.avatar}
                    alt={user.customerInfo.nickname}
                    className="h-full w-full object-cover"
                  />
                ) : (
                  <div className="flex h-full w-full items-center justify-center bg-gray-200">
                    {user.customerInfo?.nickname?.[0] || '?'}
                  </div>
                )}
              </div>
              <div className="min-w-0 flex-1 truncate">
                <div className="font-medium">{user.customerInfo?.nickname || '未知用户'}</div>
                <div className="w-full truncate text-sm text-gray-500">
                  {user.customerInfo?.openId}
                </div>
              </div>
            </div>
          ))
        ) : (
          <div className="text-center text-gray-500">
            {debouncedSearchValue ? '未找到相关用户' : '请输入关键词搜索'}
          </div>
        )}
      </div>

      {/* 底部提示 */}
      <div className="absolute bottom-4 left-0 right-0 text-center text-sm text-gray-400">
        只能通过键盘ESC或者右上角×隐藏当前面板
      </div>
    </div>
  );
}
