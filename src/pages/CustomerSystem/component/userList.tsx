import { cn } from '@/lib/utils';
import { PostChatMessageRead } from '@/services/douyin-im/chat';
import {
  ConversationPageParams,
  DouyinImManualConversation,
  GetConversationPage,
} from '@/services/douyin-im/conversation';
import { GetAllProject } from '@/services/project';
import { SearchOutlined, StarFilled, InfoCircleOutlined } from '@ant-design/icons';
import { history, useInfiniteQuery, useQueryClient, useRequest, useSearchParams } from '@umijs/max';
import { useInViewport, useLocalStorageState, useMap } from 'ahooks';
import { Avatar, Badge, Input, Popover, Select, Tag, Tooltip } from 'antd';
import dayjs from 'dayjs';
import { useAtom, useAtomValue } from 'jotai';
import React, { useEffect, useRef, useState } from 'react';
import { sseConversationDataAtom, sseConversationEventAtom } from '../atoms/sseAtom';
import { customerTypeAtom, retentionStatusAtom, selectedConversationAtom } from '../atoms/userAtom';
import UserSearch from './userSearch';
import { renderMessageInUserList } from './utils';
import defaultAvatar from '@/assets/default-avatar.png';
import { useDarkMode } from '@/hooks/useDarkMode';

export default function UserList() {
  const queryClient = useQueryClient();
  const { isDarkMode } = useDarkMode();
  const [selectedConversation, setSelectedConversation] = useAtom(selectedConversationAtom);
  const [inputValue, setInputValue] = useState('');
  const [visible, setVisible] = useState(false);
  const loadMoreRef = useRef<HTMLDivElement>(null);
  const [inViewport] = useInViewport(loadMoreRef);
  const [searchParams] = useSearchParams();
  const urlProjectId = searchParams.get('projectId');
  const [projectId, setProjectId] = useLocalStorageState<string | null>(
    'customer_system_project_id',
    {
      defaultValue: null,
      listenStorageChange: true,
    },
  );
  const [customerType, setCustomerType] = useAtom(customerTypeAtom);
  const [retentionStatus, setRetentionStatus] = useAtom(retentionStatusAtom);
  // SSE 数据
  const conversationData = useAtomValue(sseConversationDataAtom);
  const [conversationEvent, setConversationEvent] = useAtom(sseConversationEventAtom);

  const { data: projectList } = useRequest(GetAllProject, {
    onSuccess: (data) => {
      // 本地没有项目 ID 且 URL 没有项目 ID 时，设置第一个项目
      if (data?.length && !projectId && !urlProjectId) {
        setProjectId(data[0].id);
      }
    },
  });

  const projectOptions = projectList?.map((item) => ({
    label: item.name,
    value: item.id,
  }));

  const [conversationMap, { set: setConversationMap, get: getConversation }] = useMap<
    string,
    DouyinImManualConversation
  >();

  const {
    data: conversationList,
    isLoading,
    isFetchingNextPage,
    hasNextPage,
    fetchNextPage,
  } = useInfiniteQuery({
    queryKey: ['conversations', projectId, customerType, retentionStatus],
    queryFn: async ({ pageParam }) => {
      if (!projectId) {
        return Promise.resolve({
          cursor: 0,
          data: [],
          hasMore: false,
        });
      }
      const params: ConversationPageParams = {
        projectId,
        limit: 50,
        cursor: pageParam,
        customerType,
        retentionStatus,
      };
      const response = await GetConversationPage(params);
      return response.data;
    },
    select: (data) => {
      const updatedPages = data.pages.map((page) => {
        const updatedData =
          page?.data?.map((conversation: DouyinImManualConversation) => {
            if (!conversation.conversationId) return conversation;

            const existingConversation = getConversation(conversation.conversationId);

            // 如果已存在会话，合并现有数据和新数据，优先使用接口返回的未读数量
            if (existingConversation) {
              const mergedConversation = {
                ...existingConversation,
                ...conversation,
              };
              return mergedConversation;
            }

            return conversation;
          }) || [];

        return {
          ...page,
          data: updatedData,
        };
      });

      return {
        pages: updatedPages,
        pageParams: data.pageParams,
      };
    },
    getNextPageParam: (lastPage) => (lastPage?.hasMore ? lastPage?.cursor : undefined),
    initialData: {
      pages: [],
      pageParams: [],
    },
  });

  // 添加一个 useEffect 来处理 Map 的更新 放在 select 里面 setMap 会死循环
  useEffect(() => {
    conversationList?.pages.forEach((page) => {
      page?.data?.forEach((conversation: DouyinImManualConversation) => {
        if (conversation.conversationId) {
          setConversationMap(conversation.conversationId, conversation);
        }
      });
    });
  }, [conversationList?.pages, setConversationMap]);

  // 修改滚动加载效果
  useEffect(() => {
    if (inViewport && hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [inViewport, hasNextPage, isFetchingNextPage, fetchNextPage]);

  // 处理 URL 参数优先级
  useEffect(() => {
    if (urlProjectId) {
      setProjectId(urlProjectId);
    }
  }, [urlProjectId, setProjectId]);

  // 收到推送的消息更新会话列表
  const updateConversation = (data: string) => {
    const conversationSSEData: DouyinImManualConversation = JSON.parse(data);

    const { conversationId } = conversationSSEData;
    if (!conversationId) return;

    queryClient.setQueryData(
      ['conversations', projectId, customerType, retentionStatus],
      (oldData: any) => {
        if (!oldData?.pages?.length) return oldData;

        const existingConversation = getConversation(conversationId);
        const updatedPages = [...oldData.pages];

        if (existingConversation) {
          // 如果是当前打开的会话，不增加未读数
          const updatedConversation = {
            ...existingConversation,
            ...conversationSSEData,
            // 如果是当前选中的会话，保持未读数为0
            unReadCount:
              selectedConversation?.conversationId === conversationId
                ? 0
                : conversationSSEData.unReadCount || 0,
          };

          setConversationMap(conversationId, updatedConversation);

          updatedPages.forEach((page) => {
            const index = page.data.findIndex(
              (item: DouyinImManualConversation) =>
                item.conversationId === conversationSSEData.conversationId,
            );
            if (index !== -1) {
              page.data.splice(index, 1);
            }
          });

          // 找到第一个非星标会话的位置
          const firstNonStarredIndex = updatedPages[0].data.findIndex(
            (item: DouyinImManualConversation) => item.starStatus !== 1,
          );

          // 如果找到非星标会话，将会话插入到该位置，否则插入到列表最前面
          if (firstNonStarredIndex !== -1) {
            updatedPages[0].data.splice(firstNonStarredIndex, 0, updatedConversation);
          } else {
            updatedPages[0].data.unshift(updatedConversation);
          }
        } else {
          const newConversation = {
            conversationId: conversationSSEData.conversationId,
            ...conversationSSEData,
            // 如果是当前选中的会话，保持未读数为0
            unReadCount:
              selectedConversation?.conversationId === conversationId
                ? 0
                : conversationSSEData.unReadCount || 0,
          };

          setConversationMap(conversationId, newConversation);

          // 找到第一个非星标会话的位置
          const firstNonStarredIndex = updatedPages[0].data.findIndex(
            (item: DouyinImManualConversation) => item.starStatus !== 1,
          );

          // 如果找到非星标会话，将会话插入到该位置，否则插入到列表最前面
          if (firstNonStarredIndex !== -1) {
            updatedPages[0].data.splice(firstNonStarredIndex, 0, newConversation);
          } else {
            updatedPages[0].data.unshift(newConversation);
          }
        }

        return {
          ...oldData,
          pages: updatedPages,
        };
      },
    );
  };

  // 处理 SSE 消息更新
  useEffect(() => {
    if (!conversationData || !conversationEvent) return;
    try {
      updateConversation(conversationData);
      setConversationEvent(null);
    } catch (error) {
      console.error('处理 SSE 消息失败:', error);
    }
  }, [conversationData, conversationEvent]);

  const handleClose = () => {
    setVisible(false);
  };

  const handleSearchSelect = (conversation: DouyinImManualConversation) => {
    setSelectedConversation(conversation);
    setVisible(false);

    // 将选中的会话移动到列表最前面
    if (conversation.conversationId && projectId) {
      queryClient.setQueryData(
        ['conversations', projectId, customerType, retentionStatus],
        (oldData: any) => {
          if (!oldData?.pages?.length) return oldData;

          const updatedPages = [...oldData.pages];

          // 在所有页面中查找并移除该会话
          let foundConversation: DouyinImManualConversation | undefined;
          updatedPages.forEach((page) => {
            const index = page.data.findIndex(
              (item: DouyinImManualConversation) =>
                item.conversationId === conversation.conversationId,
            );
            if (index !== -1) {
              foundConversation = page.data.splice(index, 1)[0];
            }
          });

          // 如果在现有列表中找到了会话，使用它，否则使用传入的会话
          const conversationToAdd = foundConversation || conversation;

          // 找到第一个非星标会话的位置
          const firstNonStarredIndex = updatedPages[0].data.findIndex(
            (item: DouyinImManualConversation) => item.starStatus !== 1,
          );

          // 如果找到非星标会话，将会话插入到该位置，否则插入到列表最前面
          if (firstNonStarredIndex !== -1) {
            updatedPages[0].data.splice(firstNonStarredIndex, 0, conversationToAdd);
          } else {
            updatedPages[0].data.unshift(conversationToAdd);
          }

          // 更新会话映射
          if (conversation.conversationId) {
            setConversationMap(conversation.conversationId, conversationToAdd);
          }

          return {
            ...oldData,
            pages: updatedPages,
          };
        },
      );

      // 标记消息为已读
      PostChatMessageRead({
        conversationId: conversation.conversationId,
        projectId,
      });
    }
  };

  const handleSelectConversation = async (conversation: DouyinImManualConversation) => {
    setSelectedConversation(conversation);
    if (conversation.conversationId && projectId) {
      await PostChatMessageRead({
        conversationId: conversation.conversationId,
        projectId,
      });

      // 本地清除未读小红点
      queryClient.setQueryData(
        ['conversations', projectId, customerType, retentionStatus],
        (oldData: any) => {
          if (!oldData?.pages?.length) return oldData;

          const updatedPages = [...oldData.pages];

          // 在所有页面中查找并更新该会话的未读数
          updatedPages.forEach((page) => {
            const index = page.data.findIndex(
              (item: DouyinImManualConversation) =>
                item.conversationId === conversation.conversationId,
            );
            if (index !== -1) {
              page.data[index] = {
                ...page.data[index],
                unReadCount: 0,
              };
            }
          });

          // 更新会话映射
          if (conversation.conversationId) {
            const existingConversation = getConversation(conversation.conversationId);
            if (existingConversation) {
              setConversationMap(conversation.conversationId, {
                ...existingConversation,
                unReadCount: 0,
              });
            }
          }

          return {
            ...oldData,
            pages: updatedPages,
          };
        },
      );
    }
  };

  // 添加一个格式化消息时间的函数
  const formatMessageTime = (time?: string) => {
    if (!time) return '';
    const messageDate = dayjs(time);
    const isToday = messageDate.isSame(dayjs(), 'day');
    return isToday
      ? messageDate.format('HH:mm') // 今天的时间格式
      : messageDate.format('YYYY-MM-DD'); // 其他日期格式
  };

  // 添加一个函数来检查是否有重复的 customerInfo
  const hasDuplicateCustomerInfo = (conversation: DouyinImManualConversation) => {
    if (!conversation.customerInfo?.nickname) return false;

    // 检查是否有其他会话具有相同的 customerInfo.nickname
    let count = 0;
    conversationMap.forEach((item) => {
      if (item.customerInfo?.nickname === conversation.customerInfo?.nickname) {
        count++;
      }
    });

    return count > 1;
  };

  return (
    <div className="flex h-full flex-col">
      <div className="px-5 py-3">
        <Select
          showSearch
          placeholder="选择项目"
          options={projectOptions}
          allowClear
          value={projectId}
          onChange={(value) => {
            setProjectId(value);
            if (value) {
              history.replace(`?projectId=${value}`);
            } else {
              history.replace('');
            }
          }}
          style={{ width: '100%' }}
          filterOption={(input, option) =>
            (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
          }
          optionFilterProp="label"
        />
        <Popover
          content={
            <UserSearch
              searchValue={inputValue}
              onClose={handleClose}
              onSelect={handleSearchSelect}
              projectId={Number(projectId)}
            />
          }
          placement="bottomLeft"
          title={null}
          trigger="click"
          open={visible}
          overlayInnerStyle={{ padding: 0 }}
        >
          <Input
            placeholder="联系人"
            style={{ marginBlock: '10px' }}
            prefix={<SearchOutlined className="text-new-media-gray-600" />}
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onClick={() => setVisible(true)}
          />
        </Popover>
        <div className="flex justify-between gap-2">
          <Select
            placeholder="全部状态"
            options={[
              { label: '已留资', value: 1 },
              { label: '未留资', value: 0 },
            ]}
            style={{ width: 'calc(50% - 4px)' }}
            value={retentionStatus}
            onChange={(value) => setRetentionStatus(value)}
            onClear={() => setRetentionStatus(undefined)}
            allowClear
          />
          <Select
            placeholder="全部用户"
            options={[
              { label: '自然用户', value: 1 },
              { label: '投流用户', value: 2 },
            ]}
            value={customerType}
            onChange={(value) => setCustomerType(value)}
            onClear={() => setCustomerType(undefined)}
            style={{ width: 'calc(50% - 4px)' }}
            allowClear
          />
        </div>
      </div>

      {/* 分割线 */}
      <div className="h-[1px] w-full bg-gray-100" />

      {/* 平铺的用户列表 */}
      <div className="no-scrollbar flex-1 overflow-y-auto">
        {!projectId ? (
          <div className="flex h-full -translate-y-16 items-center justify-center text-gray-400">
            请选择项目
          </div>
        ) : isLoading ? (
          <div className="flex justify-center py-4">加载中...</div>
        ) : (
          <>
            {conversationList?.pages?.map((page, i) => (
              <React.Fragment key={i}>
                {page?.data?.map((conversation: DouyinImManualConversation) => (
                  <div
                    key={conversation.conversationId}
                    className={cn(
                      'flex cursor-pointer items-start gap-3 px-4 py-3 hover:bg-new-media-gray-50',
                      selectedConversation?.conversationId === conversation.conversationId &&
                        (isDarkMode ? 'bg-[#2F3030]' : 'bg-new-media-blue-100'),
                    )}
                    onClick={() => handleSelectConversation(conversation)}
                  >
                    <Badge count={conversation.unReadCount} size="small">
                      <Avatar
                        src={conversation.customerInfo?.avatar || <img src={defaultAvatar} />}
                        size={40}
                      />
                    </Badge>
                    <div className="flex flex-1 flex-col gap-1">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2 overflow-hidden">
                          <div className="max-w-[115px] truncate font-medium">
                            {conversation.customerInfo?.nickname}
                          </div>
                          {hasDuplicateCustomerInfo(conversation) &&
                            conversation.merchantInfo?.nickname && (
                              <Tooltip title={conversation.merchantInfo.nickname}>
                                <InfoCircleOutlined className="text-blue-400" />
                              </Tooltip>
                            )}
                          {conversation.starStatus === 1 && (
                            <StarFilled className="text-yellow-400" />
                          )}
                        </div>
                        <div>
                          {Boolean(conversation.retentionStatus) && (
                            <Tag color="green" className="text-xs" style={{ marginInlineEnd: 0 }}>
                              已留资
                            </Tag>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="max-w-[115px] truncate text-xs text-gray-400">
                          {renderMessageInUserList(conversation.lastMessageOverview)}
                        </div>
                        <div className="text-xs text-gray-400">
                          {formatMessageTime(conversation.lastMessageOverview?.messageCreateTime)}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </React.Fragment>
            ))}
            <div ref={loadMoreRef} className="h-4" />
            {isFetchingNextPage && <div className="flex justify-center py-4">加载更多...</div>}
          </>
        )}
      </div>
    </div>
  );
}
