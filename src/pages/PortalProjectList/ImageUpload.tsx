import { uploadBase64Image } from '@/utils/oss';
import { CloseCircleOutlined, LoadingOutlined, PlusCircleFilled } from '@ant-design/icons';
import { ProFormInstance } from '@ant-design/pro-components';
import { Button, Upload, Image, ImageProps, message, UploadProps, GetProp } from 'antd';
import React, { Dispatch, SetStateAction, useState } from 'react';
import styled from 'styled-components';

type UploadRequestOption =
  GetProp<UploadProps, 'customRequest'> extends (options: infer R) => void ? R : never;

export type ImageUploaderProps = {
  previewImageProps?: ImageProps;
  placeholder?: string;
  value?: string;
  onChange?: Dispatch<SetStateAction<string>>;
} & UploadProps;

const ImageContainer = styled.div`
  display: inline-block;
  position: relative;
  .ant-btn {
    position: absolute;
    top: -15px;
    right: -15px;
    display: none;
  }
  &:hover {
    .ant-btn {
      display: block;
    }
  }
`;

export const buildUploadFileFn = () => {
  return async (
    req: UploadRequestOption,
    ref: React.MutableRefObject<ProFormInstance | undefined>,
    inputName: string,
  ) => {
    const file = req.file as File;
    if (!file.name) {
      message.error('非法参数');
      return;
    }
    const hide = message.loading('上传中');
    try {
      const uploadRes = await uploadBase64Image({
        base64Data: file,
        key: `/project_icon/${file.name}`,
      });
      hide();
      if (uploadRes.statusCode === 200) {
        const hitImageUrl = `https://${uploadRes.Location}`;
        if (ref) {
          ref.current?.setFieldsValue({ [inputName]: hitImageUrl });

          req.onSuccess?.(hitImageUrl);
          return;
        } else {
          req.onSuccess?.(hitImageUrl);
        }
        return hitImageUrl;
      }
      message.error('上传图片失败');
    } catch (e) {
      console.error(e);
      message.error('上传图片失败');
    }
  };
};

const ImageUpload: React.FC<ImageUploaderProps> = (props) => {
  const { value, onChange, previewImageProps, placeholder = '上传图片', ...restProps } = props;
  const [loading, setLoading] = useState(false);

  return (
    <Upload
      accept={'.jpg,.png,.jpeg'}
      listType="picture-card"
      showUploadList={false}
      beforeUpload={(file) => {
        if (!['image/jpeg', 'image/png', 'image/jpg'].includes(file.type)) {
          message.error('只能上传jpg和png格式');
          return false;
        }
        if (file.size / 1024 / 1024 > 20) {
          message.error('图片最大20M');
          return false;
        }
        return true;
      }}
      onChange={(info) => {
        if (info.file.status === 'uploading') {
          setLoading(true);
        }
        setLoading(false);
        if (info.file.status === 'done' && onChange) {
          onChange(info.file.response);
          setLoading(false);
        }
      }}
      {...restProps}
    >
      {value && (
        <ImageContainer>
          <Image preview={false} src={value} {...previewImageProps} />
          <Button
            icon={<CloseCircleOutlined />}
            type="text"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              if (onChange) {
                onChange('');
              }
              setLoading(false);
            }}
          />
        </ImageContainer>
      )}
      {!value && (
        <div>
          <div>
            {loading ? <LoadingOutlined /> : <PlusCircleFilled />}
            <div>{placeholder}</div>
          </div>
        </div>
      )}
    </Upload>
  );
};

export default ImageUpload;
