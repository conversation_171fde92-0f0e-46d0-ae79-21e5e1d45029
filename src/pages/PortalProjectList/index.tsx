import { CreateProject, DeleteProject, QueryProjectList } from '@/services/project';
import { IndustryType } from '@/utils/const';
import {
  ModalForm,
  PageContainer,
  ProFormInstance,
  ProFormSelect,
  ProFormText,
  ProFormTextArea,
  ProLayout,
} from '@ant-design/pro-components';
import { history, useNavigate, useParams, useRequest } from '@umijs/max';
import { Button, Dropdown, Flex, Form, message, Modal } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import PortalProjectEdit from '../PortalProjectEdit';
import { waitTime } from '../ProjectHome/Business/incentiveForm';
import ImageUpload, { buildUploadFileFn } from './ImageUpload';
import { MoreOutlined } from '@ant-design/icons';
// import { isTestEnvironment } from '@/utils/common';
import styles from './index.module.less';

const PortalProjectList = () => {
  const navigate = useNavigate();
  const { projectId } = useParams();
  const [forceUpdate, setForceUpdate] = useState(1);
  const [collapsed, setCollapsed] = useState(true);
  const modalFormRef = useRef<ProFormInstance>();

  const { data: projectListRes } = useRequest(() => QueryProjectList({ page: 1, size: 9999 }), {
    refreshDeps: [forceUpdate],
  });

  useEffect(() => {
    if (!projectId && projectListRes) {
      const firstProject = projectListRes?.items?.[0];

      if (firstProject) {
        navigate(`/setting-project-list/${firstProject.id}/${firstProject.tagId}/edit`);
      }
    }
  }, [projectId, projectListRes]);

  return (
    <PageContainer className={styles.portal_project_list_wrapper} pageHeaderRender={() => <></>}>
      <ProLayout
        collapsed={collapsed}
        // 产品要暂时去掉
        // waterMarkProps={
        //   isTestEnvironment
        //     ? {
        //         content: '测试环境',
        //       }
        //     : undefined
        // }
        onCollapse={() => setCollapsed((prev) => !prev)}
        menu={{
          collapsedShowGroupTitle: true,
          params: [projectListRes],
          request: async () => {
            return projectListRes?.items
              ? projectListRes?.items?.map((item: any) => ({
                  ...item,
                  path: `/setting-project-list/${item.id}/${item.tagId}/edit`,
                  name: item.name,
                }))
              : [];
          },
        }}
        fixSiderbar={false}
        menuHeaderRender={false}
        menuFooterRender={() => {
          return (
            <div style={{ width: '100%', textAlign: 'center' }}>
              <ModalForm
                trigger={
                  <Button
                    type="default"
                    size="small"
                    style={{ width: collapsed ? 32 : '96%', height: collapsed ? 32 : 28 }}
                  >
                    +{!collapsed && <span>&nbsp;新建项目</span>}
                  </Button>
                }
                layout={'horizontal'}
                key="createProject"
                title="新建项目"
                width="50%"
                formRef={modalFormRef}
                modalProps={{
                  destroyOnHidden: true,
                }}
                labelCol={{ span: 6 }}
                wrapperCol={{ span: 18 }}
                onFinish={async (values) => {
                  await waitTime(2000);
                  await CreateProject(values);
                  message.success('提交成功');
                  history.push('/project-list');
                  return true;
                }}
              >
                <Form.Item
                  name="iconUrl"
                  label="上传项目图片"
                  rules={[{ required: true, message: '请上传项目图片' }]}
                >
                  <ImageUpload
                    customRequest={async (req) => {
                      await buildUploadFileFn()(req, modalFormRef, 'iconUrl');
                    }}
                  />
                </Form.Item>

                <ProFormText
                  width="md"
                  name="name"
                  label="项目名称"
                  placeholder="请输入"
                  rules={[{ required: true, message: '请输入项目名称' }]}
                />
                <ProFormTextArea
                  width="md"
                  name="description"
                  label="项目描述"
                  placeholder="请输入"
                />
                <ProFormText
                  width="md"
                  name="projectKey"
                  label="项目标识"
                  placeholder="请输入"
                  rules={[
                    {
                      required: true,
                      message: '请输入字母+数字格式',
                      pattern: /^[A-Za-z][A-Za-z0-9]*$/,
                    },
                  ]}
                />
                <ProFormSelect
                  width="md"
                  name="industryType"
                  label="行业选择"
                  valueEnum={{
                    [IndustryType.CAR]: '汽车行业',
                    [IndustryType.E_COMMERCE]: '电商行业',
                  }}
                  placeholder="请选择"
                  rules={[{ required: true, message: '请选择行业' }]}
                />
              </ModalForm>
            </div>
          );
        }}
        menuItemRender={(item) => {
          return (
            <div
              onClick={() => {
                item.path && navigate(item.path);
              }}
            >
              <Flex style={{ width: '100%' }} justify="space-between" align="center">
                <img src={item.iconUrl} className="h-[24px] w-[24px] rounded-[4px]" alt="" />
                <span className="absolute left-[44px] inline-block w-[130px] overflow-hidden text-ellipsis whitespace-nowrap font-bold">
                  {item.name}
                </span>
                <Dropdown
                  className="project-action"
                  menu={{
                    items: [
                      {
                        key: 'delete',
                        label: '删除',
                        onClick() {
                          Modal.confirm({
                            title: '删除项目',
                            content: '确定删除该项目吗？',
                            onOk: async () => {
                              await DeleteProject([item.id]);
                              setForceUpdate(forceUpdate + 1);
                              message.success('删除成功');
                            },
                          });
                        },
                      },
                    ],
                  }}
                  trigger={['click']}
                >
                  <MoreOutlined />
                </Dropdown>
              </Flex>
              <Flex
                style={{ width: '100%', marginTop: 6, fontSize: 12, pointerEvents: 'none' }}
                align="center"
                justify="space-between"
              >
                <span
                  title={item.projectKey || ''}
                  className="mr-1 w-[60px] overflow-hidden text-ellipsis whitespace-nowrap text-[14px]"
                >
                  {item.projectKey}
                </span>
                <span
                  className="mr-1 w-[65px] overflow-hidden text-ellipsis"
                  title={item.projectUserCount}
                >
                  用户数:{item.projectUserCount}
                </span>
                <span
                  className="w-[65px] overflow-hidden text-ellipsis"
                  title={item.competitionCount}
                >
                  竞品数:{item.competitionCount}
                </span>
              </Flex>
            </div>
          );
        }}
      >
        <PageContainer pageHeaderRender={() => <></>} title={<></>}>
          {<PortalProjectEdit />}
        </PageContainer>
      </ProLayout>
    </PageContainer>
  );
};

export default PortalProjectList;
