.portal_project_list_wrapper {
  background-color: #f3f5f9;
  box-sizing: border-box;
  overflow-x: hidden;

  img {
    pointer-events: none;
  }

  :global {
    .ant-pro-page-container-children-container {
      padding: 0;
      .ant-pro-layout .ant-layout-sider.ant-pro-sider {
        height: calc(100vh - 56px);
        background-color: #fff;
      }
      .ant-layout-sider-collapsed {
        .ant-layout-sider-children {
          padding: 15px 8px;
        }
        .project-icon {
          width: 24px;
          height: 24px;
          transform: translate(26%, 25%);
        }
        .project-action {
          display: none;
        }
      }
      .ant-layout-sider-children {
        padding: 15px 16px;
      }
    }

    .ant-pro-layout .ant-pro-layout-content-has-page-container {
      height: calc(100vh - 56px);
      overflow-y: auto;
      padding: 15px;
    }
    .ant-layout-sider-children {
      .ant-menu-inline .ant-menu-item {
        line-height: initial;
        height: 76px;
        border-radius: 4px;
        border: 1px solid #ebedf2;
        padding: 10px !important;
        box-sizing: border-box;
        margin-bottom: 8px;
      }
      .ant-menu-light .ant-menu-item-selected {
        background-color: #1e5eff1a;
        border: 1px solid #1e5eff;
        color: #1e5eff !important;
      }
    }
    .ant-pro-base-menu-vertical-collapsed .ant-menu-item {
      padding: 15%;
      img {
        margin-left: 18%;
      }
    }
    .ant-layout-sider-children div:nth-child(1) {
      &::-webkit-scrollbar {
        display: none;
      }
    }

    .ant-pro-page-container-children-container .ant-layout-sider-children {
      div:nth-child(1) {
        order: 2;
      }
      .ant-pro-sider-footer {
        padding-block-end: 8px;
      }
    }
  }
}
