/**
 * login page
 */
import logo from '@/assets/logo.png';
import NewMediaBanner from '@/components/banner';
import { useNavigate } from '@umijs/max';
import { Button, Form, Input, message } from 'antd';
import { IRegister, Register } from '../../services/auth';
import styles from './index.module.less';

export default function RegisterPage() {
  const navigate = useNavigate();

  const onSubmit = async (values: IRegister) => {
    const res = await Register(values);
    if (res.code === 0) {
      // 注册成功, 跳转登录页
      navigate('/login', { replace: true });
    } else {
      message.error(res.msg);
    }
  };

  return (
    <div className={styles.container}>
      <div className={styles.imageBg}>
        <NewMediaBanner />
      </div>
      <div className={styles.content}>
        <div className={styles.main}>
          <div
            style={{
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'start',
            }}
          >
            <img alt="logo" className={styles.logo} height={25} src={logo} />
            注册后台账号
          </div>

          <Form<IRegister>
            name="basic"
            onFinish={onSubmit}
            autoComplete="off"
            style={{
              padding: '30px 0px',
              textAlign: 'center',
              overflow: 'hidden',
            }}
          >
            <Form.Item name="name" rules={[{ required: true, message: '请输入姓名!' }]}>
              <Input placeholder="请输入姓名" />
            </Form.Item>

            <Form.Item name="account" rules={[{ required: true, message: '请输入邮箱!' }]}>
              <Input placeholder="请输入邮箱" />
            </Form.Item>

            <Form.Item name="password" rules={[{ required: true, message: '请输入登录密码!' }]}>
              <Input.Password placeholder="请输入登录密码" />
            </Form.Item>
            <Form.Item>
              <Button type="primary" htmlType="submit" block>
                提交
              </Button>
            </Form.Item>
          </Form>
        </div>
      </div>
    </div>
  );
}
