import IconDouYin from '@/assets/platformIcon/douyin.png';
import IconWXVideo from '@/assets/platformIcon/wxvideo.png';
import IconXiaoHongShu from '@/assets/platformIcon/xiaohongshu.png';
import { QueryDouyinAuthLink, ReportDouyinAuthCode } from '@/services/douyin';
import { GrantAccountItem, QueryGrantTeamInfo } from '@/services/project';
import { OS } from '@/utils/const';
import { PlatForm } from '@/utils/platform';
import { fillParams, getUrlParams } from '@/utils/url';
import { LoadingOutlined } from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';
import { useSearchParams } from '@umijs/max';
import { useRequest, useTitle } from 'ahooks';
import { Alert, Flex, message, Result } from 'antd';
import { useEffect, useRef, useState } from 'react';
import { PlatformShow } from './components/platFormShow';
import QrCodeAuthModal from './qrCodeModal';

type PageStatus = 'before' | 'authing' | 'success' | 'fail'; // 授权状态

const DouyinAuth = () => {
  useTitle('新媒体系统账号授权页');
  const [pageStatus, setPageStatus] = useState<PageStatus>();
  const [authAccountId, setAuthAccountId] = useState<string>();
  const [loadingLink, setLoadingLink] = useState<boolean>(false);
  const [oAuthCheckResult, setOAuthCheckResult] = useState<{ code: number; msg?: string }>();
  const [showXiaoHongShuModal, setShowXiaoHongShuModal] = useState<boolean>(false);
  const [showWxVideoModal, setShowWxVideoModal] = useState<boolean>(false);
  const teamCodeRef = useRef<string>('');
  const [searchParams] = useSearchParams();
  const projectId = searchParams.get('projectId');

  const getDouyinAuthLink = (accountId: string) => {
    const teamCode = teamCodeRef.current;

    return QueryDouyinAuthLink({
      projectAccountId: Number(accountId),
    }).then((res) => {
      if (res.code === 0 && res.data) {
        return injectInfoToDouyinLinkState(res.data, {
          teamCode,
          accountId,
        });
      } else {
        message.error(res.msg || '获取抖音授权链接失败');
      }
    });
  };

  const {
    data: teamInfo,
    loading: loadingTeamInfo,
    refresh: refreshTeamInfo,
    run: initTeamInfo,
  } = useRequest(QueryGrantTeamInfo, {
    manual: true,
    onSuccess: (res) => {
      if (res.code === 0) {
        if (!res.data) {
          message.error(res.msg || '团队授权信息缺失');
        }
      } else {
        message.error(res.msg || '获取团队授权信息失败');
      }
    },
  });

  const teamName = teamInfo?.data?.teamName;
  const accounts = teamInfo?.data?.accounts || [];
  // 抖音平台账号信息
  const douyinAccount = accounts.filter((item) => item.platform === PlatForm.Douyin);
  // 小红书平台账号
  const xiahongshuAccount = accounts.filter((item) => item.platform === PlatForm.XiaoHongShu);
  // 视频号平台账号
  const wxVideoAccount = accounts.filter((item) => item.platform === PlatForm.WXVideo);

  const toDouyinAuth = async (account: GrantAccountItem) => {
    if (!OS.isPc) {
      message.error('目前手机端暂不支持扫码授权，若要授权请用电脑端打开该链接。');
      return;
    }

    if (loadingLink) {
      return;
    }

    setLoadingLink(true);
    const douyinAuthLink = await getDouyinAuthLink(account.id.toString());
    setLoadingLink(false);

    if (douyinAuthLink) {
      window.location.href = douyinAuthLink;
    }
  };

  useEffect(() => {
    const { teamCode, beforeAuth, code, state } = getUrlParams<undefined>();

    if (beforeAuth) {
      setPageStatus('before');
      initTeamInfo({ teamCode });
      teamCodeRef.current = teamCode;
    } else {
      const { originState, teamCode, accountId } = JSON.parse(state || '{}');

      if (code) {
        setPageStatus('authing');
        ReportDouyinAuthCode({
          code,
          state: originState,
        })
          .then((res) => {
            if (res.code === 0) {
              setPageStatus('success');
            } else {
              setOAuthCheckResult({
                code: res.code,
                msg: res.msg,
              });
              setPageStatus('fail');
            }
          })
          .catch(() => {
            setPageStatus('fail');
          });
      } else {
        setPageStatus('fail');
      }

      if (teamCode) {
        initTeamInfo({ teamCode });
      }

      if (accountId) {
        setAuthAccountId(accountId);
      }
    }
  }, []);

  return (
    <div>
      <header className="flex h-14 items-center bg-white p-0">
        <img
          alt="logo"
          src="https://media-**********.cos.ap-guangzhou.myqcloud.com/%E5%9B%BE%E7%89%87%E7%B4%A0%E6%9D%90/logo.png"
          width={34}
          height={34}
          className="ml-8 mt-[5px]"
        />
        <span className="ml-[5px] font-['OPPOSans'] text-lg font-normal text-[#333]">
          新媒体系统账号授权页
        </span>
      </header>

      {pageStatus === 'before' && (
        <ProCard bodyStyle={{ padding: '30px' }} loading={loadingTeamInfo} direction="column">
          {!OS.isPc && (
            <Alert
              message="目前手机端暂不支持扫码授权，若要授权请用电脑端打开该链接。"
              type="error"
              showIcon
            />
          )}

          <Flex align="center" justify="center" className="text-xl font-bold">
            {teamName ? `"${teamName}"店` : null}
          </Flex>

          <PlatformShow
            icon={IconDouYin}
            title="抖音平台授权"
            accounts={douyinAccount}
            onAuth={toDouyinAuth}
            authAccountId={authAccountId}
            oAuthCheckResult={oAuthCheckResult}
            type={PlatForm.Douyin}
          />

          <PlatformShow
            icon={IconXiaoHongShu}
            title="小红书平台授权"
            accounts={xiahongshuAccount}
            onAuth={() => setShowXiaoHongShuModal(true)}
            type={PlatForm.XiaoHongShu}
          />

          <PlatformShow
            icon={IconWXVideo}
            title="视频号平台授权"
            accounts={wxVideoAccount}
            onAuth={() => setShowWxVideoModal(true)}
            type={PlatForm.WXVideo}
          />

          <div className="mt-5 text-center text-red-500">
            扫码没反应，可以按住ctrl+滚动鼠标放大二维码，再尝试扫码哦。一般是因为电脑分辨率较小，需要将二维码放大才能查看。
          </div>
        </ProCard>
      )}

      {pageStatus === 'authing' && (
        <Result icon={<LoadingOutlined style={{ fontSize: 60 }} spin />} title="账号授权中..." />
      )}

      {pageStatus === 'fail' && (
        <Result
          status="error"
          title={
            <Flex vertical gap={15}>
              <div>授权失败,扫码账号不正确</div>
              <div>请检查扫码账号是否正确,有疑问请联系管理员</div>
            </Flex>
          }
        />
      )}

      {pageStatus === 'success' && <Result status="success" title="授权完成，感谢使用" />}

      <QrCodeAuthModal
        show={showXiaoHongShuModal}
        closeModal={() => setShowXiaoHongShuModal(false)}
        refreshTeamInfo={refreshTeamInfo}
        platform={PlatForm.XiaoHongShu}
        projectId={projectId}
      />

      <QrCodeAuthModal
        show={showWxVideoModal}
        closeModal={() => setShowWxVideoModal(false)}
        refreshTeamInfo={refreshTeamInfo}
        platform={PlatForm.WXVideo}
        projectId={projectId}
      />
    </div>
  );
};

function injectInfoToDouyinLinkState(
  originLink: string,
  info: {
    teamCode: string;
    accountId: string;
  },
) {
  const search = getUrlParams<undefined>(undefined, originLink);
  const originState = search.state;

  if (!originState) {
    return originLink;
  }

  const newState = JSON.stringify({
    teamCode: info.teamCode,
    accountId: info.accountId,
    originState,
  });

  return fillParams(
    {
      ...search,
      state: encodeURIComponent(newState),
    },
    originLink,
  );
}

export default DouyinAuth;
