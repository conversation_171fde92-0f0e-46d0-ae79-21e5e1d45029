import { AuthStatus, closeAuth, createQrCode, queryAuthStatus } from '@/services/spider-account';
import { RedoOutlined } from '@ant-design/icons';
import { useRequest } from '@umijs/max';
import { Flex, message, Modal, Spin } from 'antd';
import { useEffect, useRef } from 'react';
import IconWXVideo from '@/assets/platformIcon/wxvideo.png';
import IconXiaoHongshu2 from '@/assets/platformIcon/xiaohongshu2.png';
import { PlatForm } from '@/utils/platform';

type QrCodeAuthModalProps = {
  show: boolean;
  closeModal: () => void;
  refreshTeamInfo: () => void;
  platform: PlatForm.WXVideo | PlatForm.XiaoHongShu;
  projectId?: string | null;
};

const platformConfig = {
  [PlatForm.WXVideo]: {
    title: '视频号账号扫码授权',
    icon: IconWXVideo,
    iconAlt: 'wxVideo',
  },
  [PlatForm.XiaoHongShu]: {
    title: '小红书账号扫码授权',
    icon: IconXiaoHongshu2,
    iconAlt: 'xiaohongshu',
  },
};

const QrCodeAuthModal = (props: QrCodeAuthModalProps) => {
  const { show, closeModal, refreshTeamInfo, platform, projectId } = props;
  const { title, icon, iconAlt } = platformConfig[platform];

  const qrCodeUuid = useRef<string>();

  const {
    run: fetchQRCodeId,
    loading: loadingQRCode,
    mutate: setQRCodeRes,
  } = useRequest(
    () =>
      createQrCode({
        platform,
        projectId,
      }),
    {
      manual: true,
      onSuccess(res) {
        qrCodeUuid.current = res;
        setTimeout(startPollingAuthStatus, 0);
      },
    },
  );

  useEffect(() => {
    if (show) {
      fetchQRCodeId();
    }
  }, [show]);

  const {
    data: authStatusRes,
    run: startPollingAuthStatus,
    cancel: stopPollingAuthStatus,
    mutate: setAuthStatusRes,
  } = useRequest(
    () => {
      if (!qrCodeUuid.current) {
        return Promise.reject();
      }
      return queryAuthStatus({
        uuid: qrCodeUuid.current,
      });
    },
    {
      refreshDeps: [qrCodeUuid.current],
      pollingInterval: 2000,
      manual: true,
      onSuccess(res) {
        if (res?.status === AuthStatus.授权成功) {
          stopPollingAuthStatus();
          message.success('授权成功');
          refreshTeamInfo();
          setTimeout(() => {
            closeModal();
          }, 1000);
        }
        if (res?.status === AuthStatus.二维码过期) {
          stopPollingAuthStatus();
        }
      },
    },
  );

  useEffect(() => {
    /** 关闭标签页时关闭授权 */
    const handleTabClose = (event: any) => {
      event.preventDefault();
      if (qrCodeUuid.current) {
        closeAuth({ uuid: qrCodeUuid.current });
      }
    };

    if (show) {
      window.addEventListener('beforeunload', handleTabClose);
    }

    return () => {
      window.removeEventListener('beforeunload', handleTabClose);
      if (qrCodeUuid.current) {
        closeAuth({ uuid: qrCodeUuid.current });
      }
    };
  }, [show]);

  return (
    <Modal
      title={title}
      width={468}
      maskClosable={false}
      open={show}
      onCancel={closeModal}
      afterClose={() => {
        stopPollingAuthStatus();
        if (qrCodeUuid.current) {
          closeAuth({ uuid: qrCodeUuid.current });
        }
        setQRCodeRes(undefined);
        setAuthStatusRes(undefined);
      }}
      footer={null}
    >
      <Spin spinning={loadingQRCode}>
        <div style={{ padding: '16px 12px', textAlign: 'center' }}>
          <img src={icon} alt={iconAlt} width={40} height={40} style={{ marginBottom: '20px' }} />
          <Flex
            align="center"
            justify="center"
            style={{
              width: 250,
              height: 250,
              textAlign: 'center',
              left: '50%',
              transform: 'translateX(-50%)',
              backgroundColor: authStatusRes?.qrCode ? 'transparent' : '#00000013',
              position: 'relative',
              borderRadius: '12px',
              overflow: 'hidden',
            }}
          >
            {authStatusRes?.qrCode ? (
              <>
                {authStatusRes?.status === AuthStatus.二维码过期 && (
                  <Flex
                    onClick={() => fetchQRCodeId()}
                    style={{
                      backgroundColor: '#000000',
                      color: '#fff',
                      position: 'absolute',
                      zIndex: 9,
                      cursor: 'pointer',
                      padding: '8px 12px',
                      borderRadius: '4px',
                      boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
                      transition: 'background-color 0.3s',
                    }}
                  >
                    二维码已过期 点击刷新
                    <RedoOutlined />
                  </Flex>
                )}
                <div
                  style={{
                    background: '#fff',
                    boxShadow: '0 4px 32px rgba(0, 0, 0, 0.1)',
                    borderRadius: '12px',
                  }}
                >
                  <img
                    src={authStatusRes?.qrCode}
                    alt="qrCode"
                    style={{ width: 230, height: 230, background: 'black' }}
                  />
                </div>
              </>
            ) : (
              <Spin spinning={true} />
            )}
          </Flex>
        </div>
      </Spin>
    </Modal>
  );
};

export default QrCodeAuthModal;
