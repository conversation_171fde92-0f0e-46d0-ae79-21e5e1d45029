import { ProCard } from '@ant-design/pro-components';
import { GrantAccountItem } from '@/services/project';
import { Row, Empty } from 'antd';
import { PlatformCard } from './platFormCard';
import { PlatForm } from '@/utils/platform';

type PlatformShowProps = {
  icon: string;
  title: string;
  accounts: GrantAccountItem[];
  onAuth: (account: GrantAccountItem) => void;
  authAccountId?: string;
  oAuthCheckResult?: { code: number; msg?: string };
  type: PlatForm;
};

export const PlatformShow = ({
  icon,
  title,
  accounts,
  onAuth,
  authAccountId,
  oAuthCheckResult,
  type,
}: PlatformShowProps) => (
  <ProCard
    direction="column"
    headStyle={{ justifyContent: 'center', gap: '20px' }}
    extra={
      <div style={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}>
        <img src={icon} alt={title} width={50} height={50} />
        <div style={{ fontWeight: 'bold', fontSize: '15px', marginLeft: '10px', color: '#333' }}>
          {title}
        </div>
      </div>
    }
  >
    {accounts.length > 0 ? (
      <Row wrap gutter={[8, 8]} justify={accounts.length < 3 ? 'center' : 'start'}>
        {accounts.map((account) => (
          <PlatformCard
            key={account.id}
            account={account}
            onAuth={() => onAuth(account)}
            authAccountId={authAccountId}
            oAuthCheckResult={oAuthCheckResult}
            type={type}
          />
        ))}
      </Row>
    ) : (
      <Empty description="团队没有账号数据" />
    )}
  </ProCard>
);
