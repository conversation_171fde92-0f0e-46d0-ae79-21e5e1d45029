import { GrantAccountItem } from '@/services/project';
import { Badge, Card, Col, Row, Button, Flex, Avatar, Typography } from 'antd';
import defaultAvatar from '@/assets/default-avatar.png';
import { Space } from 'antd';
import { PlatForm } from '@/utils/platform';

type PlatformCardProps = {
  account: GrantAccountItem;
  onAuth: () => void;
  authAccountId?: string;
  oAuthCheckResult?: { code: number; msg?: string };
  type: PlatForm;
};

const renderCardTitle = (avatar?: string, nickname?: string) => (
  <Space>
    <Avatar size="small" src={avatar || defaultAvatar} alt="avatar" crossOrigin="anonymous" />
    <span>{nickname}</span>
  </Space>
);

const getState = (stateCode?: number) => {
  const states = {
    1: { status: 'success' as const, text: '已授权' },
    2: { status: 'default' as const, text: '已失效' },
    default: { status: 'warning' as const, text: '未授权' },
  };
  return states[stateCode as keyof typeof states] || states.default;
};

export const PlatformCard = ({
  account,
  onAuth,
  authAccountId,
  oAuthCheckResult,
  type,
}: PlatformCardProps) => {
  // 抖音用grantStatus，小红书、视频号用hostingGrantStatus
  const { status, text } = getState(
    type === PlatForm.Douyin ? account.grantStatus : account.hostingGrantStatus,
  );

  return (
    <Col xs={24} sm={24} md={12} lg={8} xl={8} xxl={6}>
      <Card
        title={renderCardTitle(account.avatar, account.nickname)}
        extra={<Badge status={status} text={text} />}
        style={{ minWidth: 282 }}
      >
        <Flex vertical gap={12}>
          <Row>账号：{account.showAccountId}</Row>
          <Row>
            授权时间：
            {type === PlatForm.Douyin ? account.grantTime : account.hostingGrantTime || '-'}
          </Row>
          <Row justify="center">
            <Button type="primary" block onClick={onAuth}>
              扫码授权
            </Button>
          </Row>
          {authAccountId === account.id &&
            oAuthCheckResult?.msg &&
            !oAuthCheckResult.msg.includes('ErrCode = 10007') && (
              <Row justify="center">
                <Typography.Text type="danger">授权失败，请重新扫码授权</Typography.Text>
              </Row>
            )}
        </Flex>
      </Card>
    </Col>
  );
};
