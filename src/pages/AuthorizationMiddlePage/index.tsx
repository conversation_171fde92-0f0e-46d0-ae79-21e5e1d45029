import { useRequest } from '@umijs/max';
import {
  GetAuthorization,
  GetOceanengineAuthorizationTaskPage,
  TaskStatus,
} from '@/services/oceanengine';
import { useSearchParams } from '@umijs/max';
import { cn } from '@/lib/utils';
import PendingIcon from '@/assets/authStatus/pending.png';
import SuccessIcon from '@/assets/authStatus/success.png';
import FailedIcon from '@/assets/authStatus/failed.png';
import InvalidIcon from '@/assets/authStatus/invalid.png';
import NotExistIcon from '@/assets/authStatus/noexist.png';
import { Loader2 } from 'lucide-react';

export const statusMap: Record<TaskStatus, { text: string; color: string; icon: React.ReactNode }> =
  {
    [TaskStatus.Pending]: {
      text: '待授权',
      color: 'text-gray-500',
      icon: <img src={PendingIcon} alt="pending" className="w-48" />,
    },
    [TaskStatus.InProgress]: {
      text: '授权中',
      color: 'text-blue-500',
      icon: <Loader2 className="size-36 animate-spin text-[#1E5EFF]" />,
    },
    [TaskStatus.Success]: {
      text: '授权成功',
      color: 'text-blue-500',
      icon: <img src={SuccessIcon} alt="success" className="w-48" />,
    },
    [TaskStatus.Failed]: {
      text: '授权失败',
      color: 'text-red-500',
      icon: <img src={FailedIcon} alt="failed" className="w-48" />,
    },
    [TaskStatus.Invalid]: {
      text: '授权失效',
      color: 'text-orange-500',
      icon: <img src={InvalidIcon} alt="invalid" className="w-48" />,
    },
    [TaskStatus.NotExist]: {
      text: '授权任务不存在',
      color: 'text-gray-500',
      icon: <img src={NotExistIcon} alt="not-exist" className="w-48" />,
    },
  };

type StateType = {
  projectId: string;
  operatorId: string;
  appId: string;
  taskId: string;
};

export default function AuthorizationMiddlePage() {
  const [searchParams] = useSearchParams();
  // base64 解码
  const originalState = searchParams.get('state');
  const parseState = JSON.parse(atob(originalState ?? '')) as StateType;
  const authCode = searchParams.get('auth_code');
  const taskId = parseState.taskId;

  const { data, cancel } = useRequest(() => GetOceanengineAuthorizationTaskPage({ taskId }), {
    pollingInterval: 3000,
    pollingWhenHidden: false,
    ready: !!taskId,
    onSuccess: (result) => {
      if (result?.status === TaskStatus.Success || result?.status === TaskStatus.Failed) {
        // 成功后停止轮询
        cancel();
      }
    },
  });

  useRequest(
    () => {
      if (!authCode || !originalState) {
        return Promise.resolve();
      }
      return GetAuthorization({
        authCode: authCode,
        state: originalState,
      });
    },
    {
      ready: !!authCode && !!originalState,
    },
  );

  const currentStatus = data?.status ?? TaskStatus.NotExist;
  const statusInfo = statusMap[currentStatus];

  if (!taskId) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-white">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-800">未查询到授权任务ID</h1>
        </div>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen items-center justify-center bg-white">
      <div className="text-center">
        <div className="mb-8 flex justify-center">
          <div className="relative">{statusInfo.icon}</div>
        </div>

        <div className={cn('mb-6 text-3xl font-semibold', statusInfo.color)}>{statusInfo.text}</div>

        {currentStatus === TaskStatus.InProgress && (
          <p className="text-base text-gray-500">正在处理授权请求，请稍候...</p>
        )}

        {currentStatus === TaskStatus.Success && (
          <p className="text-base text-green-500">授权成功，请关闭页面</p>
        )}

        {currentStatus === TaskStatus.Failed && (
          <p className="text-base text-red-500">授权失败，请重试</p>
        )}
      </div>
    </div>
  );
}
