import XGVideoCard from '@/components/xgVideoCard';
import { spiderSyncUserTags, spiderSyncUserTagsReloader } from '@/models/store';
import { AccountLiveItem, QueryAccountLivesForPagination } from '@/services/account';
import { getLastQueryTagIds, LSQueryTagIdsKey } from '@/services/constants';
import { proTableRequestAdapterParamsAndData } from '@/utils';
import { AccountPlatformEnum, AccountPlatformKeysMap } from '@/utils/platform';
import { CheckCircleFilled, LinkOutlined } from '@ant-design/icons';
import {
  ActionType,
  PageContainer,
  ProColumns,
  ProFormInstance,
  ProTable,
} from '@ant-design/pro-components';
import { NavLink } from '@umijs/max';
import { Button, Input, message, Popover, QRCode, Row, Space } from 'antd';
import copy from 'copy-to-clipboard';
import dayjs from 'dayjs';
import { useAtomValue, useSetAtom } from 'jotai';
import { useEffect, useRef } from 'react';
import SpiderSyncUserTagSelect from '../TagList/Components/spiderSyncUserTagSelect';

const LiveList: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const formRef = useRef<ProFormInstance>();

  const tagGroups = useAtomValue(spiderSyncUserTags);
  const reloadSpiderSyncUserTags = useSetAtom(spiderSyncUserTagsReloader);

  useEffect(() => {
    reloadSpiderSyncUserTags();
  }, []);

  const columns: Array<ProColumns<AccountLiveItem>> = [
    {
      title: '账号ID',
      dataIndex: 'accountId',
      hideInSearch: false,
      hideInTable: true,
    },
    {
      title: '账号名称',
      dataIndex: 'nickname',
      hideInSearch: false,
      hideInTable: true,
    },
    {
      title: '账号',
      dataIndex: 'nickname',
      hideInSearch: true,
      hideInTable: false,
      render(dom, record) {
        const entity = record.userInfo;
        if (!entity) return null;

        return (
          <Space>
            <NavLink
              to={`/monitor/account/user-list/detail/${AccountPlatformKeysMap[entity.platform]}/${
                entity.accountId
              }`}
            >
              <Row align="middle">
                <img
                  src={entity.avatar}
                  alt={entity.nickname}
                  width={24}
                  height={24}
                  style={{ marginRight: 8 }}
                />

                <span className="block text-sm">{entity.nickname}</span>
                {entity.blueVipFlag === 1 && (
                  <Popover content={entity.blueVipReason} trigger="hover">
                    <CheckCircleFilled style={{ marginLeft: 4 }} />
                  </Popover>
                )}
              </Row>
            </NavLink>
            <Button
              type="default"
              size="small"
              onClick={() => {
                if (entity.accountId) {
                  copy(entity.accountId);
                  message.success('已复制到剪切板');
                }
              }}
            >
              ID
            </Button>
          </Space>
        );
      },
    },
    {
      title: '账号标签',
      dataIndex: 'tagIds',
      hideInSearch: false,
      hideInTable: true,
      valueType: 'text',
      renderFormItem: () => {
        return <SpiderSyncUserTagSelect data={tagGroups} />;
      },
      initialValue: getLastQueryTagIds(),
    },
    {
      title: '账号平台',
      dataIndex: 'platform',
      hideInSearch: false,
      hideInTable: false,
      valueEnum: AccountPlatformEnum,
    },
    {
      title: '直播间',
      dataIndex: 'roomTitle',
      hideInSearch: true,
      hideInTable: false,
      render(dom, entity) {
        return (
          <Space>
            <span>{entity.roomTitle}</span>
            <Button
              type="default"
              size="small"
              onClick={() => {
                if (entity.roomId) {
                  copy(entity.roomId);
                  message.success('已复制到剪切板');
                }
              }}
            >
              ID
            </Button>
          </Space>
        );
      },
    },
    {
      title: '在线人数',
      dataIndex: 'roomUserCount',
      hideInSearch: true,
      hideInTable: false,
      valueType: 'digit',
    },
    {
      title: '累计观看人数',
      dataIndex: 'totalUserCountStr',
      hideInSearch: true,
      hideInTable: false,
    },
    {
      title: '点赞人数',
      dataIndex: 'likeCount',
      hideInSearch: true,
      hideInTable: false,
    },
    {
      title: '直播状态',
      dataIndex: 'roomStatus',
      hideInSearch: false,
      hideInTable: false,
      valueEnum: {
        1: {
          text: '直播中',
          color: 'green',
        },
        0: {
          text: '已结束',
          color: 'gray',
        },
      },
    },
    {
      title: '质检状态',
      dataIndex: 'qualityStatus',
      hideInSearch: false,
      hideInTable: false,
      valueEnum: {
        1: {
          text: '已触发',
          color: 'green',
        },
        0: {
          text: '未触发',
          color: 'gray',
        },
      },
    },
    {
      title: '直播录制',
      dataIndex: 'replayStatus',
      hideInSearch: true,
      hideInTable: false,
      valueEnum: {
        2: {
          text: '生成中',
          color: 'green',
        },
        1: {
          text: '录制中',
          color: 'green',
        },
        0: {
          text: '未录制',
          color: 'gray',
        },
      },
      renderText(_, record) {
        return record.replayStatus || 0;
      },
      render(dom, entity) {
        if (entity.replayStatus === 2 && entity.replayFileUrl) {
          return (
            <Space>
              <XGVideoCard
                playableVideoUrl={entity.replayFileUrl}
                videoUrl={entity.replayFileUrl}
                width={54}
                height={95}
                videoWidth={entity.replayFileWidth || 540}
                videoHeight={entity.replayFileHeight || 540}
                isLive={entity.roomStatus === 1}
                type="m3u8"
              />
              <Button
                size="small"
                type="text"
                onClick={() => {
                  if (entity.replayFileUrl) {
                    copy(entity.replayFileUrl);
                    message.success('已复制到剪切板');
                  }
                }}
              >
                <LinkOutlined />
              </Button>
            </Space>
          );
        }

        return dom;
      },
    },
    {
      title: '开播时间',
      dataIndex: 'start_time',
      hideInSearch: true,
      hideInTable: false,
      renderText(_, record) {
        if (!record.startTime) {
          return '-';
        }

        return dayjs(record.startTime).format('YYYY-MM-DD HH:mm');
      },
    },
    {
      title: '停播时间',
      dataIndex: 'start_time',
      hideInSearch: true,
      hideInTable: false,
      renderText(_, record) {
        if (!record.stopTime) {
          return '-';
        }

        return dayjs(record.stopTime).format('YYYY-MM-DD HH:mm');
      },
    },
    {
      title: '开播时间',
      dataIndex: 'live_start_time',
      valueType: 'dateTimeRange',
      hideInTable: true,
      initialValue: [dayjs().subtract(7, 'days'), dayjs()],
      search: {
        transform: (value) => {
          return {
            startTime: value[0],
            endTime: value[1],
          };
        },
      },
    },
    {
      title: '房间 ID',
      dataIndex: 'roomId',
      hideInTable: true,
    },
    {
      title: '操作',
      dataIndex: 'action',
      hideInSearch: true,
      hideInTable: false,
      render(_, record) {
        return (
          <Space>
            {record.platform === 1 && (
              <a
                onClick={() => {
                  window.open(`https://live.douyin.com/${record.webRid}`);
                }}
              >
                抖音页面
              </a>
            )}
            {record.replayStatus && record.replayStatus > 0 && record.qualityStatus === 1 && (
              <>
                <a
                  onClick={() => {
                    window.open(
                      `${window.location.origin}/live/detail/${record.roomId}/${record.platform}/${record.projectIndustryType}`,
                    );
                  }}
                >
                  直播详情监控
                </a>
                <Popover
                  content={
                    <div className="flex flex-col items-center justify-center">
                      <QRCode
                        value={`${
                          window.location.href.includes('dev')
                            ? 'https://ailive-dev.xiaofeilun.cn'
                            : 'https://ailive.xiaofeilun.cn'
                        }/${record.userInfo?.liveUid}`}
                      />
                      <Space.Compact style={{ width: '100%' }}>
                        <Input
                          disabled
                          value={`${
                            window.location.href.includes('dev')
                              ? 'https://ailive-dev.xiaofeilun.cn'
                              : 'https://ailive.xiaofeilun.cn'
                          }/${record.userInfo?.liveUid}`}
                        />
                        <Button
                          onClick={() => {
                            if (record.userInfo?.liveUid) {
                              copy(
                                `${
                                  window.location.href.includes('dev')
                                    ? 'https://ailive-dev.xiaofeilun.cn'
                                    : 'https://ailive.xiaofeilun.cn'
                                }/${record.userInfo.liveUid}`,
                              );
                              message.success('已复制到剪切板');
                            }
                          }}
                        >
                          复制链接
                        </Button>
                      </Space.Compact>
                      <Button
                        type="default"
                        size="small"
                        onClick={() => {
                          if (record.userInfo?.liveUid) {
                            copy(record.userInfo.liveUid);
                            message.success('已复制到剪切板');
                          }
                        }}
                      >
                        AI助播 ID: {record.userInfo?.liveUid}
                      </Button>
                    </div>
                  }
                  title="扫码前往AI助播系统"
                >
                  <a
                    href={`${
                      window.location.href.includes('dev')
                        ? 'https://ailive-dev.xiaofeilun.cn'
                        : 'https://ailive.xiaofeilun.cn'
                    }/${record.userInfo?.liveUid}`}
                    target="_blank"
                    rel="noreferrer"
                  >
                    AI助播
                  </a>
                </Popover>
              </>
            )}
          </Space>
        );
      },
    },
  ];

  return (
    <PageContainer extra={[]}>
      <ProTable<AccountLiveItem>
        formRef={formRef}
        actionRef={actionRef}
        scroll={{ x: 'max-content' }}
        columns={columns}
        rowKey="id"
        toolBarRender={false}
        bordered
        size="small"
        dateFormatter="string"
        pagination={{ defaultPageSize: 15 }}
        search={{ filterType: 'query', defaultCollapsed: false }}
        request={(params, sorter) => {
          localStorage.setItem(LSQueryTagIdsKey, JSON.stringify({ tagIds: params.tagIds }));
          return proTableRequestAdapterParamsAndData(
            params,
            sorter,
            QueryAccountLivesForPagination,
          );
        }}
      />
    </PageContainer>
  );
};

export default LiveList;
