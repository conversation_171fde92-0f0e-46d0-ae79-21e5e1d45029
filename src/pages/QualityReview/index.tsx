import { FunctionCode, GetProjectListByFunctionCode } from '@/services/system';
import { PageContainer, ProCard } from '@ant-design/pro-components';
import { useAccess, useModel, useRequest } from '@umijs/max';
import { useEventEmitter, useResetState } from 'ahooks';
import { Button, ConfigProvider, Radio, Select, Space } from 'antd';
import { atom } from 'jotai';
import { useState } from 'react';
import AnalysisReview from './analysisReview';
import AnalysisReviewData from './components/analysisReviewData';
import PendingReviewData from './components/pendingReviewData';
import ReviewRecord from './components/reviewRecord';
import TimeFilter, { getTimeByType, TimeType } from './components/timeFilter';
import PendingReview from './pendingReview';

export const monitoringWordMapAtom = atom<Record<string, string>>({});

// 想要访问这样模块还挺难的 需要判断三处权限真的很恶心 1.菜单权限 2.项目是否开启 3.用户角色是否是管理员或者包含项目权限
const QualityReview = () => {
  const access = useAccess();
  const isAdmin = access.isAdmin;
  const { initialState } = useModel('@@initialState');
  const projectIds = initialState?.currentUser?.projectIds;
  const [projectId, setProjectId] = useState<string>();
  const [rangeTime, setRangeTime] = useState<string[]>(getTimeByType(TimeType.Day));
  const [reviewTabType, setReviewTabType] = useState('pending-review');
  const [pendingReviewCategoryType, setPendingReviewCategoryType, resetPendingReviewCategoryType] =
    useResetState<number>(1);
  const [
    analysisReviewCategoryType,
    setAnalysisReviewCategoryType,
    resetAnalysisReviewCategoryType,
  ] = useResetState<number>(1);
  const [openRecordDrawer, setOpenRecordDrawer] = useState(false);
  const [monitoringWordId, setMonitoringWordId, resetMonitoringWordId] = useResetState<
    string | undefined
  >(undefined);
  const [pendingReviewTotal, setPendingReviewTotal] = useState<number>(0);
  // 上报事件
  const reviewEvent$ = useEventEmitter();
  // 回滚事件
  const rollbackEvent$ = useEventEmitter();
  // 撤销审核
  const revokeEvent$ = useEventEmitter();
  // 刷新时间
  const refreshEvent$ = useEventEmitter();

  const { data: projectList } = useRequest(
    () => GetProjectListByFunctionCode({ functionCode: FunctionCode.QualityCheck }),
    {
      onSuccess: (res) => {
        if (res && res?.length > 0) {
          const filterProject = res.filter((item) => projectIds?.includes(item.id));
          const firstProjectId = isAdmin ? res[0].id : filterProject[0].id;
          setProjectId(firstProjectId);
        }
      },
    },
  );

  const projectSelectOptionsDefault = projectList?.map((item) => ({
    label: item.name,
    value: item.id,
  }));

  const projectSelectOptions = isAdmin
    ? projectSelectOptionsDefault
    : projectSelectOptionsDefault?.filter((item) => projectIds?.includes(item.value));

  return (
    <ConfigProvider
      theme={{
        token: {
          colorPrimary: '#1E5EFF',
        },
      }}
    >
      <PageContainer
        pageHeaderRender={() => <></>}
        style={{ backgroundColor: '#F5F6FA', paddingTop: '20px', minWidth: '1215px' }}
      >
        <ProCard>
          <Space>
            <Select
              value={projectId}
              style={{ width: 150 }}
              placeholder="请选择项目"
              onChange={(value) => setProjectId(value)}
              options={projectSelectOptions}
            />
            <TimeFilter setRangeTime={setRangeTime} />
            <Radio.Group
              defaultValue="pending-review"
              buttonStyle="solid"
              value={reviewTabType}
              onChange={(e) => {
                setReviewTabType(e.target.value);
                // 切换 tabs 把状态都重置一下避免出现异常
                resetAnalysisReviewCategoryType();
                resetPendingReviewCategoryType();
                resetMonitoringWordId();
              }}
            >
              <Radio.Button value="pending-review">待审核&nbsp;({pendingReviewTotal})</Radio.Button>
              <Radio.Button value="analysis-review">审核统计分析</Radio.Button>
            </Radio.Group>
          </Space>
          <Space style={{ float: 'right' }}>
            <Button onClick={() => setOpenRecordDrawer(true)}>审核记录</Button>
            {reviewTabType === 'pending-review' && (
              <Button onClick={() => refreshEvent$.emit()}>刷新</Button>
            )}
          </Space>

          {reviewTabType === 'pending-review' && projectId && (
            <PendingReviewData
              categoryType={pendingReviewCategoryType}
              setCategoryType={setPendingReviewCategoryType}
              projectId={projectId}
              rangeTime={rangeTime}
              setPendingReviewTotal={setPendingReviewTotal}
              rollbackEvent$={rollbackEvent$}
              reviewEvent$={reviewEvent$}
              refreshEvent$={refreshEvent$}
              revokeEvent$={revokeEvent$}
            />
          )}

          {reviewTabType === 'analysis-review' && projectId && (
            <AnalysisReviewData
              projectId={projectId}
              rangeTime={rangeTime}
              monitoringWordId={monitoringWordId}
              setMonitoringWordId={setMonitoringWordId}
              setCategoryType={setAnalysisReviewCategoryType}
              rollbackEvent$={rollbackEvent$}
              revokeEvent$={revokeEvent$}
            />
          )}
        </ProCard>

        {reviewTabType === 'pending-review' && projectId && (
          <PendingReview
            rangeTime={rangeTime}
            projectId={projectId}
            categoryType={pendingReviewCategoryType}
            rollbackEvent$={rollbackEvent$}
            reviewEvent$={reviewEvent$}
            refreshEvent$={refreshEvent$}
          />
        )}
        {reviewTabType === 'analysis-review' && projectId && monitoringWordId && (
          <AnalysisReview
            rangeTime={rangeTime}
            projectId={projectId}
            categoryType={analysisReviewCategoryType}
            monitoringWordId={monitoringWordId}
            rollbackEvent$={rollbackEvent$}
            reviewEvent$={reviewEvent$}
            revokeEvent$={revokeEvent$}
          />
        )}

        <ReviewRecord
          openRecordDrawer={openRecordDrawer}
          setOpenRecordDrawer={setOpenRecordDrawer}
          projectId={projectId}
          rollbackEvent$={rollbackEvent$}
        />
      </PageContainer>
    </ConfigProvider>
  );
};

export default QualityReview;
