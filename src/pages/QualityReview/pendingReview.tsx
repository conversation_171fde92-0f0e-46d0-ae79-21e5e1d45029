import { GetAuditCategoryOverview, GetAuditMonitoringWordOverview } from '@/services/quality-audit';
import { HideScrollBarDiv } from '@/utils/commonStyle';
import { CheckCard, ProCard } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { EventEmitter } from 'ahooks/lib/useEventEmitter';
import { Checkbox, Empty, Space, Splitter, Tabs } from 'antd';
import { useEffect, useState } from 'react';
import QualityImgList from './components/qualityImgList';
import QualityList from './components/qualityList';
import styles from './components/style.module.less';

const qualityRuleTypeOptions = [
  { label: '标题文字', value: 1 },
  { label: '语音', value: 2 },
  { label: '封面', value: 3 },
  { label: '画面', value: 4 },
];

export const targetTypeOptions = [
  { label: '直播', value: 1 },
  { label: '作品', value: 2 },
];

type PendingReviewProps = {
  rangeTime: string[];
  projectId: string;
  categoryType: number;
  reviewEvent$: EventEmitter<void>;
  rollbackEvent$: EventEmitter<void>;
  refreshEvent$: EventEmitter<void>;
};

const PendingReview = (props: PendingReviewProps) => {
  const { rangeTime, projectId, categoryType, reviewEvent$, rollbackEvent$, refreshEvent$ } = props;
  // 顶部的分类 ID
  const [monitoringWordId, setMonitoringWordId] = useState<string>();
  // 左侧的词
  const [monitoringWord, setMonitoringWord] = useState<string>();
  const [targetTypeList, setTargetTypeList] = useState<number[]>();
  const [qualityRuleTypeList, setQualityRuleTypeList] = useState<number[]>();

  // 切换大分类就清空之前的 state
  useEffect(() => {
    setMonitoringWordId('');
    setMonitoringWord('');
  }, [categoryType, projectId]);

  const { data: auditCategoryOverview, refresh: refreshAuditCategoryOverview } = useRequest(
    () =>
      GetAuditCategoryOverview({
        projectId,
        startDate: rangeTime[0],
        endDate: rangeTime[1],
        categoryType,
      }),
    {
      refreshDeps: [categoryType, rangeTime, projectId],
      onSuccess: (res) => {
        if (!monitoringWordId) {
          setMonitoringWordId(res?.[0]?.monitoringWordId);
        }
      },
    },
  );

  const monitoringWordMap = auditCategoryOverview?.reduce(
    (acc, item) => {
      acc[item.monitoringWordId] = item.wordCode;
      return acc;
    },
    {} as { [key: string]: string },
  );

  // 画面违规切换呈现方式特判
  const isImageViolation =
    (monitoringWordMap &&
      monitoringWordId &&
      monitoringWordMap[monitoringWordId] === 'quote-violation') ||
    categoryType === 3;

  const categoryItems = auditCategoryOverview?.map((item) => ({
    key: item.monitoringWordId,
    label: (
      <>
        {item.label} ({item.count}){' '}
      </>
    ),
  }));

  const { data: auditMonitoringWordOverview, refresh: refreshMonitoringWordOverview } = useRequest(
    () => {
      if (!monitoringWordId) return Promise.reject(undefined);
      return GetAuditMonitoringWordOverview({
        projectId,
        startDate: rangeTime[0],
        endDate: rangeTime[1],
        categoryType,
        monitoringWordId,
      });
    },
    {
      refreshDeps: [monitoringWordId, rangeTime, projectId, categoryType],
      ready: !!monitoringWordId,
      onSuccess: (res) => {
        if (!monitoringWord) {
          setMonitoringWord(res?.[0]?.label);
        }
      },
    },
  );

  reviewEvent$.useSubscription(() => {
    refreshMonitoringWordOverview();
    refreshAuditCategoryOverview();
  });

  rollbackEvent$.useSubscription(() => {
    refreshMonitoringWordOverview();
    refreshAuditCategoryOverview();
  });

  refreshEvent$.useSubscription(() => {
    refreshAuditCategoryOverview();
    refreshMonitoringWordOverview();
    // 产品逻辑要清空筛选项
    setQualityRuleTypeList([]);
    setTargetTypeList([]);
  });

  return (
    <ProCard style={{ marginTop: '20px', height: '100%', overflow: 'hidden' }} direction="column">
      <ProCard
        split="vertical"
        ghost
        headerBordered
        title={
          <div>
            <span style={{ fontSize: '14px', marginRight: '10px', fontWeight: 500 }}>
              选择分类:
            </span>
            <Tabs
              className={styles.blue_tabs}
              activeKey={monitoringWordId}
              onChange={(activeKey) => {
                setMonitoringWordId(activeKey);
                // 切换 tabs 清空掉目标类型
                setTargetTypeList([]);
              }}
              items={categoryItems}
              indicator={{ size: () => 0 }}
            />
          </div>
        }
        headStyle={{
          paddingBottom: '10px',
          paddingTop: '0px',
        }}
      >
        {isImageViolation ? (
          <>
            {categoryType === 3 ? null : (
              <Space style={{ marginTop: '10px' }}>
                <span style={{ fontSize: '14px', fontWeight: 500 }}>类型:</span>
                <Checkbox.Group
                  options={targetTypeOptions}
                  onChange={(checkedValues) => setTargetTypeList(checkedValues as number[])}
                />
              </Space>
            )}
            <QualityImgList
              projectId={projectId}
              rangeTime={rangeTime}
              categoryType={categoryType}
              monitoringWordId={monitoringWordId}
              targetTypeList={targetTypeList}
              reviewEvent$={reviewEvent$}
              rollbackEvent$={rollbackEvent$}
              type="quality"
            />
          </>
        ) : (
          <Splitter>
            <Splitter.Panel defaultSize={230} max={230} collapsible style={{ overflow: 'auto' }}>
              {auditMonitoringWordOverview && auditMonitoringWordOverview?.length > 0 ? (
                <HideScrollBarDiv
                  style={{
                    height: '100%',
                    maxHeight: 'calc(100vh - 320px)',
                    overflowX: 'hidden',
                  }}
                >
                  <CheckCard.Group
                    className={styles.custom_check_card_group_left}
                    value={monitoringWord}
                    onChange={(value) => {
                      value && setMonitoringWord(value as string);
                    }}
                    style={{
                      paddingTop: '16px',
                      paddingRight: '24px',
                      paddingBottom: '100px',
                    }}
                  >
                    {auditMonitoringWordOverview?.map((item) => (
                      <CheckCard
                        key={item.label}
                        size="small"
                        title={
                          <>
                            <span className="title">{item.label}</span>
                            <span className="title-num">({item.count})</span>
                          </>
                        }
                        value={item.label}
                      />
                    ))}
                  </CheckCard.Group>
                </HideScrollBarDiv>
              ) : (
                <Empty
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                  description={<span>暂无数据</span>}
                  style={{ paddingTop: '50px' }}
                />
              )}
            </Splitter.Panel>
            <Splitter.Panel collapsible>
              <div style={{ padding: '16px' }}>
                <Space>
                  <span style={{ fontSize: '14px', fontWeight: 500 }}>筛选场景:</span>
                  <Checkbox.Group
                    options={qualityRuleTypeOptions}
                    value={qualityRuleTypeList}
                    onChange={(checkedValues) => setQualityRuleTypeList(checkedValues as number[])}
                  />
                </Space>
                <Space style={{ marginLeft: '20px' }}>
                  <span style={{ fontSize: '14px', fontWeight: 500 }}>类型:</span>
                  <Checkbox.Group
                    options={targetTypeOptions}
                    value={targetTypeList}
                    onChange={(checkedValues) => setTargetTypeList(checkedValues as number[])}
                  />
                </Space>
              </div>
              <QualityList
                projectId={projectId}
                rangeTime={rangeTime}
                categoryType={categoryType}
                monitoringWordId={monitoringWordId}
                monitoringWord={monitoringWord}
                qualityRuleTypeList={qualityRuleTypeList}
                targetTypeList={targetTypeList}
                reviewEvent$={reviewEvent$}
                rollbackEvent$={rollbackEvent$}
                type="quality"
              />
            </Splitter.Panel>
          </Splitter>
        )}
      </ProCard>
    </ProCard>
  );
};

export default PendingReview;
