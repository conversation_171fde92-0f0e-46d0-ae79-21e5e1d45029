import { GetAuditOperationPage, PostAuditOperationRollback } from '@/services/quality-audit';
import { WarningFilled } from '@ant-design/icons';
import { useRequest } from '@umijs/max';
import { EventEmitter } from 'ahooks/lib/useEventEmitter';
import { Button, DatePicker, Flex, message, Popconfirm, Timeline } from 'antd';
import { RangePickerProps } from 'antd/es/date-picker';
import { useState } from 'react';
import styled from 'styled-components';
import SimpleModal from './simpleModal';

const TimelineItem = styled.div`
  padding: 10px 18px;
  border-radius: 8px;
  margin-block: 5px;
  font-weight: 400;
  letter-spacing: 1px;
  line-height: 25px;
`;

const { RangePicker } = DatePicker;

type RecordTimeLineProps = {
  projectId?: string;
  type: 1 | 2;
  rollbackEvent$: EventEmitter<void>;
};

const categoryMap: Record<string, string> = {
  1: '品牌违规',
  2: '平台违规',
  3: '主播形象违规',
};

const RecordTimeLine = (props: RecordTimeLineProps) => {
  const { projectId, type, rollbackEvent$ } = props;
  const [rangeTime, setRangeTime] = useState<RangePickerProps['value']>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [auditRecordId, setAuditRecordId] = useState<number | undefined>();
  const [contentType, setContentType] = useState<number | undefined>();

  const { data, refresh: refreshData } = useRequest(
    () => {
      if (!projectId) return Promise.reject(null);
      return GetAuditOperationPage({
        projectId,
        page: 1,
        size: 999,
        type,
        startTime: rangeTime?.[0]?.format('YYYY-MM-DD HH:mm:ss'),
        endTime: rangeTime?.[1]?.format('YYYY-MM-DD HH:mm:ss'),
        orderBy: 'operationTime',
        orderType: 'desc',
      });
    },
    {
      refreshDeps: [projectId, type, rangeTime],
    },
  );

  const handleClose = () => {
    setIsModalOpen(false);
  };

  const onChange = (date: RangePickerProps['value']) => {
    setRangeTime(date);
  };

  const listData = data?.items;

  const timelineItem =
    listData?.map((item) => ({
      children: (
        <>
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <span style={{ color: '#989898' }}>{item.operationTime}</span>
          </div>
          <TimelineItem style={{ backgroundColor: '#fbfcff' }}>
            {item.categoryType ? (
              <div style={{ lineHeight: '25px' }}>
                {categoryMap[item.categoryType]}, 共{type === 1 ? '确认' : '撤销'}违规{' '}
                <a
                  onClick={() => {
                    setIsModalOpen(true);
                    setAuditRecordId(item.id);
                    setContentType(item.contentType);
                  }}
                >
                  {item.auditIdsSize}
                </a>{' '}
                条记录。
              </div>
            ) : null}
            <Flex justify="space-between">
              <div style={{ color: '#696666', fontSize: '12px', fontWeight: 400 }}>
                操作人: {item.operator}
              </div>
              {item.status === 0 ? (
                <Popconfirm
                  title={'撤销审核'}
                  description={
                    <>
                      撤销审核之后, 相关的审核记录将
                      <br /> 重新恢复到待审核状态
                    </>
                  }
                  placement="bottomRight"
                  icon={<WarningFilled style={{ color: 'orange' }} />}
                  onConfirm={async () => {
                    if (item.id) {
                      const result = await PostAuditOperationRollback({
                        projectId,
                        id: item.id,
                      });
                      if (result.code === 0) {
                        message.success('撤销成功');
                        refreshData();
                        rollbackEvent$.emit();
                      } else {
                        message.error('撤销失败');
                      }
                    }
                  }}
                  key={'revoke'}
                >
                  <Button color="primary" size="small" variant="outlined">
                    撤销此审核
                  </Button>
                </Popconfirm>
              ) : (
                <Button color="primary" size="small" variant="outlined" disabled>
                  已回滚
                </Button>
              )}
            </Flex>
          </TimelineItem>
        </>
      ),
    })) || [];

  return (
    <>
      <RangePicker showTime onChange={onChange} value={rangeTime} />
      <Timeline style={{ paddingTop: '20px' }} items={[...timelineItem]} />
      {auditRecordId && contentType && (
        <SimpleModal
          isModalOpen={isModalOpen}
          onClose={handleClose}
          projectId={projectId}
          id={auditRecordId}
          contentType={contentType}
        />
      )}
    </>
  );
};

export default RecordTimeLine;
