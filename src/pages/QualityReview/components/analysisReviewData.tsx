import { GetAnalysisCategoryOverview, GetAnalysisOverview } from '@/services/quality-audit';
import { DownOutlined, UpOutlined } from '@ant-design/icons';
import { useRequest } from '@umijs/max';
import { EventEmitter } from 'ahooks/lib/useEventEmitter';
import { useSetAtom } from 'jotai';
import { useState } from 'react';
import { monitoringWordMapAtom } from '..';
import AnalysisOverview from './analysisOverview';

type AnalysisReviewDataProps = {
  projectId: string;
  rangeTime: string[];
  setCategoryType: React.Dispatch<React.SetStateAction<number>>;
  monitoringWordId?: string;
  setMonitoringWordId: React.Dispatch<React.SetStateAction<string | undefined>>;
  rollbackEvent$: EventEmitter<void>;
  revokeEvent$: EventEmitter<void>;
};

const AnalysisReviewData = (props: AnalysisReviewDataProps) => {
  const {
    projectId,
    rangeTime,
    setCategoryType,
    monitoringWordId,
    setMonitoringWordId,
    rollbackEvent$,
    revokeEvent$,
  } = props;
  const [fold, setFold] = useState(false);
  const setMonitoringWordMap = useSetAtom(monitoringWordMapAtom);

  const { data: analysisOverview, refresh: refreshAnalysisOverview } = useRequest(
    () =>
      GetAnalysisOverview({
        projectId,
        startDate: rangeTime[0],
        endDate: rangeTime[1],
      }),
    {
      refreshDeps: [projectId, rangeTime],
    },
  );

  const { data: brandViolation, refresh: refreshBrandViolation } = useRequest(
    () =>
      GetAnalysisCategoryOverview({
        projectId,
        startDate: rangeTime[0],
        endDate: rangeTime[1],
        categoryType: 1,
      }),
    {
      refreshDeps: [projectId, rangeTime],
      onSuccess: (res) => {
        if (res?.categoryOverviewItemList && res?.categoryOverviewItemList?.length > 0) {
          // 选首个违规词
          setMonitoringWordId(res?.categoryOverviewItemList?.[0].monitoringWordId);
        }
        const monitoringWordList = res?.categoryOverviewItemList;
        if (monitoringWordList && monitoringWordList.length > 0) {
          const monitoringWordMap = monitoringWordList.reduce((acc, item) => {
            acc[item.monitoringWordId] = item.wordCode;
            return acc;
          }, {} as { [key: string]: string });
          setMonitoringWordMap((prev) => ({ ...prev, ...monitoringWordMap }));
        }
      },
    },
  );

  const { data: platformViolation, refresh: refreshPlatformViolation } = useRequest(
    () =>
      GetAnalysisCategoryOverview({
        projectId,
        startDate: rangeTime[0],
        endDate: rangeTime[1],
        categoryType: 2,
      }),
    {
      refreshDeps: [projectId, rangeTime],
      onSuccess: (res) => {
        const monitoringWordList = res?.categoryOverviewItemList;
        if (monitoringWordList && monitoringWordList.length > 0) {
          const monitoringWordMap = monitoringWordList.reduce((acc, item) => {
            acc[item.monitoringWordId] = item.wordCode;
            return acc;
          }, {} as { [key: string]: string });
          setMonitoringWordMap((prev) => ({ ...prev, ...monitoringWordMap }));
        }
      },
    },
  );

  const { data: anchorImageViolation, refresh: refreshAnchorImageViolation } = useRequest(
    () =>
      GetAnalysisCategoryOverview({
        projectId,
        startDate: rangeTime[0],
        endDate: rangeTime[1],
        categoryType: 3,
      }),
    {
      refreshDeps: [projectId, rangeTime],
      onSuccess: (res) => {
        const monitoringWordList = res?.categoryOverviewItemList;
        if (monitoringWordList && monitoringWordList.length > 0) {
          const monitoringWordMap = monitoringWordList.reduce((acc, item) => {
            acc[item.monitoringWordId] = item.wordCode;
            return acc;
          }, {} as { [key: string]: string });
          setMonitoringWordMap((prev) => ({ ...prev, ...monitoringWordMap }));
        }
      },
    },
  );

  rollbackEvent$.useSubscription(() => {
    refreshBrandViolation();
    refreshPlatformViolation();
    refreshAnchorImageViolation();
    refreshAnalysisOverview();
  });

  revokeEvent$.useSubscription(() => {
    refreshBrandViolation();
    refreshPlatformViolation();
    refreshAnchorImageViolation();
    refreshAnalysisOverview();
  });

  const handleChangeCategoryType = (type: number) => {
    setCategoryType(type);
  };

  return (
    <div style={{ marginTop: '16px' }}>
      <div>
        <span
          style={{
            color: '#0E1015',
            fontSize: 18,
            fontWeight: '500',
          }}
        >
          总误判率
        </span>
        <div
          style={{
            display: 'inline-block',
            color: '#64666B',
            fontSize: 14,
            fontWeight: '400',
            marginLeft: '8px',
          }}
        >
          &nbsp;
          <span>
            误判率 <span style={{ color: '#1E5EFF' }}>{analysisOverview?.errorRate}% </span>
          </span>
          ,
          <span>
            已审核 <span style={{ color: '#1E5EFF' }}>{analysisOverview?.totalAuditCount} 条</span>
          </span>
          ,
          <span>
            撤销违规{' '}
            <span style={{ color: '#1E5EFF' }}>{analysisOverview?.totalRevokeCount} 条</span>
          </span>
        </div>
      </div>
      <div style={{ height: '5px' }} />
      <AnalysisOverview
        title="品牌违规"
        data={brandViolation}
        monitoringWordId={monitoringWordId}
        setMonitoringWordId={setMonitoringWordId}
        changeCategoryType={() => handleChangeCategoryType(1)}
        fold={fold}
      />

      <AnalysisOverview
        title="平台违规"
        data={platformViolation}
        monitoringWordId={monitoringWordId}
        setMonitoringWordId={setMonitoringWordId}
        changeCategoryType={() => handleChangeCategoryType(2)}
        fold={fold}
      />

      <AnalysisOverview
        title="主播形象"
        data={anchorImageViolation}
        monitoringWordId={monitoringWordId}
        setMonitoringWordId={setMonitoringWordId}
        changeCategoryType={() => handleChangeCategoryType(3)}
        fold={fold}
      />
      <span
        style={{ float: 'right', color: '#1E5EFF' }}
        onClick={() => {
          setFold((prev) => !prev);
        }}
      >
        {!fold ? '收起' : '展开'} {!fold ? <UpOutlined /> : <DownOutlined />}
      </span>
    </div>
  );
};

export default AnalysisReviewData;
