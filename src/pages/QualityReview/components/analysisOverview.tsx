import { QualityAuditAnalysisCategoryOverviewVO } from '@/services/quality-audit';
import { Empty, Flex, Tabs } from 'antd';
import { AnimatePresence, motion } from 'framer-motion';
import styles from './style.module.less';
import { CheckCard } from '@ant-design/pro-components';

type AnalysisOverviewProps = {
  data?: QualityAuditAnalysisCategoryOverviewVO;
  monitoringWordId: string | undefined;
  setMonitoringWordId: React.Dispatch<React.SetStateAction<string | undefined>>;
  title: string;
  changeCategoryType: () => void;
  fold: boolean;
};

const AnalysisOverview = (props: AnalysisOverviewProps) => {
  const { data, monitoringWordId, setMonitoringWordId, title, changeCategoryType, fold } = props;

  const categoryItems = data?.categoryOverviewItemList?.map((item) => ({
    key: item.monitoringWordId,
    label: (
      <>
        {item.label} ({item.auditCount}){' '}
      </>
    ),
  }));

  return (
    <>
      <Flex align="center" gap={5}>
        <div style={{ width: 2, height: 14, background: '#1E5EFF' }} />
        <span
          style={{
            color: '#0E1015',
            fontSize: 16,
            fontWeight: '500',
          }}
        >
          {title}
        </span>
        <div
          style={{
            display: 'inline-block',
            color: '#64666B',
            fontSize: 14,
            fontWeight: '400',
            marginLeft: '8px',
          }}
        >
          <span>
            误判率 <span style={{ color: '#1E5EFF' }}>{data?.errorRate}% </span>
          </span>
          ,
          <span>
            已审核 <span style={{ color: '#1E5EFF' }}>{data?.totalAuditCount} 条</span>
          </span>
          ,
          <span>
            撤销违规 <span style={{ color: '#1E5EFF' }}>{data?.totalRevokeCount} 条</span>
          </span>
        </div>
      </Flex>

      {data?.categoryOverviewItemList && data?.categoryOverviewItemList?.length > 0 ? (
        <AnimatePresence>
          {fold ? (
            <motion.div
              key="simpleCheckCard"
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0, transition: { duration: 0.3 } }}
              transition={{ duration: 0.3 }}
              style={{ overflow: 'hidden', marginBlock: '5px' }}
            >
              <Tabs
                className={styles.simple_check_card}
                activeKey={monitoringWordId}
                onChange={(activeKey) => {
                  setMonitoringWordId(activeKey);
                  changeCategoryType();
                }}
                items={categoryItems}
                indicator={{ size: () => 0 }}
                style={{ marginBlock: '5px' }}
              />
            </motion.div>
          ) : (
            <motion.div
              key="customCheckCardGroup"
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0, transition: { duration: 0.3 } }}
              transition={{ duration: 0.3 }}
              style={{ overflow: 'hidden', display: 'block', paddingBlock: '5px' }}
            >
              <CheckCard.Group
                className={styles.custom_check_card_group}
                value={monitoringWordId}
                onChange={(value) => {
                  if (value) {
                    setMonitoringWordId(value as string);
                    changeCategoryType();
                  }
                }}
                style={{ display: 'block', paddingBlock: '5px' }}
              >
                {data.categoryOverviewItemList.map((item) => (
                  <CheckCard
                    className={styles.custom_check_card}
                    key={item.monitoringWordId}
                    description={
                      <Flex
                        justify="space-between"
                        style={{
                          color: monitoringWordId === item.monitoringWordId ? '#1E5EFF' : 'black',
                        }}
                      >
                        <Flex vertical>
                          <div
                            className="title"
                            style={{
                              color:
                                monitoringWordId === item.monitoringWordId ? '#1E5EFF' : '#64666B',
                            }}
                          >
                            {item.label}
                          </div>
                          <div>
                            <span className="num-hight">{item.errorRate}%</span>
                          </div>
                        </Flex>
                        <Flex
                          vertical
                          gap={8}
                          style={{ fontSize: 12, fontWeight: 400, marginRight: '12px' }}
                          justify="center"
                        >
                          <div>
                            已审核 <span className="blue">{item.auditCount} 条</span>
                          </div>
                          <div>
                            撤销违规 <span className="blue">{item.revokeCount} 条</span>
                          </div>
                        </Flex>
                      </Flex>
                    }
                    value={item.monitoringWordId}
                    style={{ width: 250, marginBlockEnd: 0 }}
                  />
                ))}
              </CheckCard.Group>
            </motion.div>
          )}
        </AnimatePresence>
      ) : (
        <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description={<span>暂无数据</span>} />
      )}
    </>
  );
};

export default AnalysisOverview;
