import { CloseOutlined } from '@ant-design/icons';
import { EventEmitter } from 'ahooks/lib/useEventEmitter';
import { Drawer, Tabs, TabsProps } from 'antd';
import RecordTimeLine from './recordTimeLine';

type ReviewRecordProps = {
  openRecordDrawer: boolean;
  setOpenRecordDrawer: (open: boolean) => void;
  projectId?: string;
  rollbackEvent$: EventEmitter<void>;
};

const ReviewRecord = (props: ReviewRecordProps) => {
  const { openRecordDrawer, setOpenRecordDrawer, projectId, rollbackEvent$ } = props;

  const items: TabsProps['items'] = [
    {
      label: '确认违规',
      key: 'confirm-violation',
      children: <RecordTimeLine projectId={projectId} type={1} rollbackEvent$={rollbackEvent$} />,
    },
    {
      label: '撤销违规',
      key: 'revoke-violation',
      children: <RecordTimeLine projectId={projectId} type={2} rollbackEvent$={rollbackEvent$} />,
    },
  ];

  return (
    <Drawer
      title="审核记录"
      onClose={() => setOpenRecordDrawer(false)}
      open={openRecordDrawer}
      size="large"
      closeIcon={null}
      extra={<CloseOutlined onClick={() => setOpenRecordDrawer(false)} />}
      styles={{
        body: {
          paddingTop: 0,
        },
      }}
      destroyOnHidden
    >
      <Tabs defaultActiveKey="confirm-violation" items={items} />
    </Drawer>
  );
};

export default ReviewRecord;
