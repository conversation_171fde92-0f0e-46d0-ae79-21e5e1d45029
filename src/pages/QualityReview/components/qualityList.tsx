import { customPaginationRender } from '@/pages/ProjectHome/style';
import {
  GetAnalysisPage,
  GetAuditPage,
  PostAuditHandle,
  PostAuditRevoke,
  QualityAuditAnalysisPageVO,
} from '@/services/quality-audit';
import { proTableRequestAdapter } from '@/utils';
import { InfoCircleOutlined } from '@ant-design/icons';
import { ActionType, ProList } from '@ant-design/pro-components';
import { Link } from '@umijs/max';
import { useSelections } from 'ahooks';
import { EventEmitter } from 'ahooks/lib/useEventEmitter';
import { Checkbox, Flex, message, Popconfirm, Popover, Space } from 'antd';
import { Key, useEffect, useRef, useState } from 'react';
import QualityTagsModal from './qualityTagsModal';
import VideoDetailModal from './videoDetailModal';
import styles from './style.module.less';

export const renderQualityContent = (content?: string, mark?: string) => {
  if (!mark) return content;
  const searchRegex = new RegExp(mark, 'gi');
  const showContent = content?.replace(searchRegex, '<span style="color: red;">$&</span>');
  return showContent;
};

type QualityListProps = {
  projectId: string;
  rangeTime: string[];
  categoryType: number;
  monitoringWordId?: string;
  monitoringWord?: string;
  qualityRuleTypeList?: number[];
  targetTypeList?: number[];
  reviewEvent$: EventEmitter<void>;
  rollbackEvent$: EventEmitter<void>;
  revokeEvent$?: EventEmitter<void>;
  type: 'quality' | 'analysis';
};

const QualityList = (props: QualityListProps) => {
  const {
    projectId,
    rangeTime,
    categoryType,
    monitoringWordId,
    monitoringWord,
    qualityRuleTypeList,
    targetTypeList,
    reviewEvent$,
    rollbackEvent$,
    revokeEvent$,
    type,
  } = props;
  const actionRef = useRef<ActionType>();
  const [showVideoDetail, setShowVideoDetail] = useState(false);
  const [videoDetailData, setVideoDetailData] = useState<QualityAuditAnalysisPageVO>();
  const [showTagsModal, setShowTagsModal] = useState(false);
  const [submitIds, setSubmitIds] = useState<string[]>([]);
  const [listData, setListData] = useState<QualityAuditAnalysisPageVO[]>([]);
  const { selected, allSelected, toggle, toggleAll, partiallySelected, clearAll, unSelectAll } =
    useSelections(listData || [], {
      itemKey: 'id',
    });

  // 侧边栏撤销后也要重新拉取数据
  rollbackEvent$.useSubscription(() => {
    actionRef.current?.reload();
    actionRef.current?.clearSelected?.();
  });

  useEffect(() => {
    // state状态变更查询条件变更清除选中
    actionRef.current?.clearSelected?.();
    // 自定义的全选也要控制清除选中
    unSelectAll();
    clearAll();
  }, [monitoringWordId, monitoringWord, qualityRuleTypeList, targetTypeList, projectId, rangeTime]);

  const handleShowVideoDetail = (
    data: QualityAuditAnalysisPageVO,
    e: React.MouseEvent<HTMLAnchorElement, MouseEvent>,
  ) => {
    e.stopPropagation();
    setShowVideoDetail(true);
    setVideoDetailData(data);
  };

  const handleCloseVideoDetail = () => {
    setShowVideoDetail(false);
  };

  const handleCloseTagsModal = () => {
    setShowTagsModal(false);
  };

  const handleRevokeQuality = (selectedRowKeys: string[]) => {
    if (selectedRowKeys.length > 0) {
      setShowTagsModal(true);
      setSubmitIds(selectedRowKeys);
    } else {
      message.error('请先选择数据');
    }
  };

  const handleConfirmQuality = async (selectedRowKeys: string[]) => {
    if (selectedRowKeys.length > 0) {
      // 确认违规
      const result = await PostAuditHandle({
        projectId,
        ids: selectedRowKeys,
        categoryType,
        handleType: 1,
        contentType: 1,
      });
      if (result.code === 0) {
        message.success('操作成功');
        actionRef.current?.reload();
        actionRef.current?.clearSelected?.();
        clearAll();
        reviewEvent$.emit();
      } else {
        message.error('操作失败');
      }
    } else {
      message.error('请先选择数据');
    }
  };

  const handleRevokeAnalysis = async (selectedRowKeys: number[]) => {
    if (selectedRowKeys.length > 0) {
      const res = await PostAuditRevoke({
        categoryType,
        ids: selectedRowKeys,
        projectId,
      });
      if (res.code === 0) {
        message.success('撤销成功');
        actionRef.current?.reload();
        actionRef.current?.clearSelected?.();
        clearAll();
        revokeEvent$?.emit();
      } else {
        message.error('撤销失败');
      }
    } else {
      message.error('请先选择数据');
    }
  };

  return (
    <>
      <div className={styles.prolist_wrapper}>
        <ProList<QualityAuditAnalysisPageVO>
          itemCardProps={{}}
          ghost
          actionRef={actionRef}
          className={type}
          pagination={{
            defaultPageSize: 10,
            showTotal: (total, range) => `${range[0]}-${range[1]} 共 ${total} 条`,
            itemRender: customPaginationRender,
            showSizeChanger: true,
          }}
          style={{
            marginTop: '0px',
            paddingInline: 16,
          }}
          rowSelection={{
            alwaysShowAlert: true,
            selectedRowKeys: (selected.map((item) => item.id) as Key[]) || [],
            onSelect: (record) => {
              toggle(record);
            },
          }}
          rowKey="id"
          grid={{ gutter: 24, column: 1 }}
          params={{
            projectId,
            startDate: rangeTime[0],
            endDate: rangeTime[1],
            categoryType,
            monitoringWordId,
            monitoringWord,
            qualityRuleTypeList,
            targetTypeList,
          }}
          postData={(data: QualityAuditAnalysisPageVO[]) => {
            setListData(data);
            const transformData = data.map((item) => {
              return {
                ...item,
                content: renderQualityContent(item.content, item.mark),
              };
            });
            return transformData;
          }}
          request={(params, sorter, filter) => {
            if (!monitoringWordId) return Promise.resolve();
            const requestFn = type === 'analysis' ? GetAnalysisPage : GetAuditPage;
            return proTableRequestAdapter(params, sorter, filter, requestFn);
          }}
          tableAlertRender={({ selectedRowKeys }) => {
            return (
              <Space style={{ width: '500px' }}>
                <Checkbox
                  checked={allSelected}
                  onClick={toggleAll}
                  indeterminate={partiallySelected}
                >
                  全选
                </Checkbox>
                已选{selectedRowKeys.length}项<a onClick={unSelectAll}>取消选择</a>
              </Space>
            );
          }}
          tableAlertOptionRender={({ selectedRowKeys }) => {
            return (
              <Space size={16} wrap>
                {type === 'analysis' ? (
                  <Popconfirm
                    title={'是否确认撤销审核'}
                    onConfirm={async () => {
                      handleRevokeAnalysis(selectedRowKeys as number[]);
                    }}
                    key={'revoke'}
                  >
                    <a>撤销审核</a>
                  </Popconfirm>
                ) : (
                  <>
                    <Popconfirm
                      title={'是否确认违规'}
                      onConfirm={async () => {
                        handleConfirmQuality(selectedRowKeys as string[]);
                      }}
                      key={'delete'}
                    >
                      <a>确认违规</a>
                    </Popconfirm>
                    <a onClick={() => handleRevokeQuality(selectedRowKeys as string[])}>撤销违规</a>
                  </>
                )}
              </Space>
            );
          }}
          metas={{
            content: {
              render: (text, row) => {
                return (
                  <Flex vertical style={{ width: '100%' }}>
                    <div>
                      {row.targetType === 1 ? (
                        <div
                          className={styles.tag}
                          style={{ backgroundColor: '#CEDAFC', color: '#1E5EFF' }}
                        >
                          直播
                        </div>
                      ) : (
                        <div
                          className={styles.tag}
                          style={{ backgroundColor: '#DBF0F9', color: '#1AC0DC' }}
                        >
                          作品
                        </div>
                      )}
                      <span
                        dangerouslySetInnerHTML={{
                          __html: row.content,
                        }}
                      />
                    </div>
                    <Flex justify="space-between" style={{ marginTop: '5px' }}>
                      {type === 'analysis' ? (
                        <div
                          style={{
                            flex: 1,
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap',
                            display: 'flex',
                            alignItems: 'center',
                          }}
                        >
                          <span
                            style={{
                              color: '#0E1015',
                              fontSize: 14,
                              fontWeight: 600,
                            }}
                          >
                            审核理由：
                          </span>
                          {row.auditReason}
                          <Popover
                            content={
                              <div>
                                <p>
                                  <span style={{ color: '#64666B' }}>机器识别时间:</span>{' '}
                                  {row.exCreateTime}
                                </p>
                                <p>
                                  <span style={{ color: '#64666B' }}>审核时间:</span>{' '}
                                  {row.auditTime}
                                </p>
                                <p>
                                  <span style={{ color: '#64666B' }}>最新操作者:</span>{' '}
                                  {row.latestOperator}
                                </p>
                                <p>
                                  <Link
                                    style={{ display: 'flex', flexDirection: 'row-reverse' }}
                                    target="_blank"
                                    to={`/${row.targetType === 1 ? 'live' : 'post'}/detail/${
                                      row.targetId
                                    }/1`}
                                    onClick={(e) => e.stopPropagation()}
                                  >
                                    审核详情
                                  </Link>
                                </p>
                              </div>
                            }
                          >
                            <InfoCircleOutlined style={{ marginLeft: '5px' }} />
                          </Popover>
                        </div>
                      ) : (
                        <div />
                      )}
                      <Space style={{ justifyContent: 'flex-end' }} size={16}>
                        <Link
                          target="_blank"
                          to={`/${row.targetType === 1 ? 'live' : 'post'}/detail/${row.targetId}/1`}
                          onClick={(e) => e.stopPropagation()}
                        >
                          查看详情
                        </Link>
                        <a onClick={(e) => handleShowVideoDetail(row, e)}>视频片段</a>
                      </Space>
                    </Flex>
                  </Flex>
                );
              },
            },
          }}
        />
      </div>
      {videoDetailData && (
        <VideoDetailModal
          isModalOpen={showVideoDetail}
          handleCancel={handleCloseVideoDetail}
          videoDetailData={videoDetailData}
        />
      )}
      <QualityTagsModal
        title="撤销违规"
        open={showTagsModal}
        closeModal={handleCloseTagsModal}
        projectId={projectId}
        submitIds={submitIds}
        categoryType={categoryType}
        actionRef={actionRef}
        reviewEvent$={reviewEvent$}
        clearAll={clearAll}
        contentType={1}
      />
    </>
  );
};

export default QualityList;
