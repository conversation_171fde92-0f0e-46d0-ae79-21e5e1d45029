import { customPaginationRender } from '@/pages/ProjectHome/style';
import { GetAuditOperationDetail, QualityAuditOperationDetailVO } from '@/services/quality-audit';
import { ProList } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Flex, Modal, Popover, Image } from 'antd';
import { renderQualityContent } from './qualityList';
import styles from './style.module.less';

type SimpleModalProps = {
  isModalOpen: boolean;
  onClose: () => void;
  projectId?: string;
  id: number;
  contentType: number;
};

const SimpleModal = (props: SimpleModalProps) => {
  const { isModalOpen, onClose, projectId, id, contentType } = props;

  const { data } = useRequest(() => GetAuditOperationDetail({ projectId, id }), {
    refreshDeps: [projectId, id],
  });

  const girdConfig =
    contentType === 1
      ? { gutter: 24, column: 1 }
      : { gutter: 16, xxl: 8, xl: 6, lg: 4, md: 4, sm: 4, xs: 4 };

  return (
    <Modal title="审核记录详情" open={isModalOpen} footer={null} onCancel={onClose} width="80%">
      <div style={{ height: 1, backgroundColor: '#F1F1F1', marginBottom: 20 }} />
      <div className={styles.prolist_wrapper}>
        <ProList<QualityAuditOperationDetailVO>
          itemCardProps={{}}
          ghost
          className={contentType === 1 ? 'quality' : 'pro-list-img'}
          pagination={{
            defaultPageSize: 10,
            showTotal: (total, range) => `${range[0]}-${range[1]} 共 ${total} 条`,
            itemRender: customPaginationRender,
            showSizeChanger: true,
          }}
          style={{
            marginTop: '0px',
            paddingInline: 16,
          }}
          rowKey="id"
          grid={girdConfig}
          params={{
            projectId,
            id,
          }}
          postData={(data: QualityAuditOperationDetailVO[]) => {
            const transformData = data.map((item) => {
              return {
                ...item,
                content: renderQualityContent(item.content, item.mark),
              };
            });
            return transformData;
          }}
          dataSource={data || []}
          metas={{
            content: {
              render: (text, row) => {
                return (
                  <Flex vertical style={{ width: '100%' }}>
                    {contentType === 1 ? (
                      <div>
                        {row.targetType === 1 ? (
                          <div
                            className={styles.tag}
                            style={{ backgroundColor: '#CEDAFC', color: '#1E5EFF' }}
                          >
                            直播
                          </div>
                        ) : (
                          <div
                            className={styles.tag}
                            style={{ backgroundColor: '#DBF0F9', color: '#1AC0DC' }}
                          >
                            作品
                          </div>
                        )}
                        <span
                          dangerouslySetInnerHTML={{
                            __html: row.content,
                          }}
                        />
                      </div>
                    ) : (
                      <Popover
                        placement="left"
                        destroyTooltipOnHide
                        content={
                          <Image src={row.picUrl} width={450} height={660} preview={false} />
                        }
                        title={null}
                      >
                        <div style={{ position: 'relative', width: '100%', height: '100%' }}>
                          <Image
                            src={row.picUrl}
                            width="100%"
                            // height={330}
                            preview={false}
                            style={{ borderRadius: '8px' }}
                          />
                          <div style={{ position: 'absolute', top: '5px', right: '5px' }}>
                            {row.targetType === 1 ? (
                              <div
                                className={styles.tag}
                                style={{ backgroundColor: '#CEDAFC', color: '#1E5EFF' }}
                              >
                                直播
                              </div>
                            ) : (
                              <div
                                className={styles.tag}
                                style={{ backgroundColor: '#DBF0F9', color: '#1AC0DC' }}
                              >
                                作品
                              </div>
                            )}
                          </div>
                        </div>
                      </Popover>
                    )}
                  </Flex>
                );
              },
            },
          }}
        />
      </div>
    </Modal>
  );
};

export default SimpleModal;
