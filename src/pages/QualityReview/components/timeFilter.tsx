import { DatePicker, Radio, RadioChangeEvent, Space } from 'antd';
import { RangePickerProps } from 'antd/es/date-picker';
import dayjs from 'dayjs';
import { useState } from 'react';

const { RangePicker } = DatePicker;

export enum TimeType {
  Day = 'day',
  Yesterday = 'yesterday',
  PastThreeDay = 'past-three-day',
}

export const getTimeByType = (type: TimeType) => {
  const today = dayjs();
  let startTime, endTime;

  switch (type) {
    case TimeType.Day:
      startTime = today.startOf('day');
      endTime = today.endOf('day');
      break;
    case TimeType.Yesterday:
      startTime = today.subtract(1, 'day').startOf('day');
      endTime = today.subtract(1, 'day').endOf('day');
      break;
    case TimeType.PastThreeDay:
      startTime = today.subtract(2, 'day').startOf('day');
      endTime = today.endOf('day');
      break;
    default:
      break;
  }

  if (startTime && endTime) {
    return [startTime.format('YYYY-MM-DD'), endTime.format('YYYY-MM-DD')];
  } else {
    return [];
  }
};

type TimeFilterProps = {
  setRangeTime: React.Dispatch<React.SetStateAction<string[]>>;
};

const TimeFilter = (props: TimeFilterProps) => {
  const { setRangeTime } = props;
  const [radioValue, setRadioValue] = useState<TimeType | null>(TimeType.Day);
  const [showRangeTime, setShowRangeTime] = useState<(string | undefined)[]>(
    getTimeByType(TimeType.Day),
  );

  const onChange = (e: RadioChangeEvent) => {
    const value = e.target.value;
    setRadioValue(value);
    const range = getTimeByType(value);
    setRangeTime(range);
    setShowRangeTime(range);
  };

  const onChangeRange = (date: RangePickerProps['value'], dateString: string[]) => {
    setRadioValue(null);
    setRangeTime(dateString);
    setShowRangeTime(dateString);
  };

  return (
    <Space>
      <Radio.Group
        value={radioValue}
        onChange={onChange}
        buttonStyle="solid"
        style={{ whiteSpace: 'nowrap' }}
      >
        <Radio.Button value={TimeType.Day}>今天</Radio.Button>
        <Radio.Button value={TimeType.Yesterday}>昨天</Radio.Button>
        <Radio.Button value={TimeType.PastThreeDay}>近 3 天</Radio.Button>
      </Radio.Group>

      <RangePicker
        onChange={onChangeRange}
        allowClear={false}
        defaultValue={[dayjs(showRangeTime[0]), dayjs(showRangeTime[1])]}
        value={[dayjs(showRangeTime[0]), dayjs(showRangeTime[1])]}
      />
    </Space>
  );
};

export default TimeFilter;
