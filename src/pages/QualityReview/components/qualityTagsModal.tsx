import { SvgIcon } from '@/components/SvgIcon';
import CreateModal, {
  TagGroupCreateParam,
  TagGroupEditParam,
} from '@/components/tagsList/createTagModal';
import { PostAuditHandle } from '@/services/quality-audit';
import {
  CreateTagGroup,
  DeleteTagGroup,
  EditTagGroup,
  GetTagGroups,
  TagsType,
} from '@/services/tag';
import { CommonTagGroup } from '@/services/typings';
import { PlusCircleOutlined, SearchOutlined, EditOutlined } from '@ant-design/icons';
import { ActionType } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { EventEmitter } from 'ahooks/lib/useEventEmitter';
import { Button, Input, InputRef, message, Modal, Space, Tag, Tooltip } from 'antd';
import { useEffect, useRef, useState } from 'react';

type TagsModalProps = {
  title: string;
  open: boolean;
  closeModal: () => void;
  projectId: string;
  submitIds: string[];
  categoryType: number;
  actionRef: React.MutableRefObject<ActionType | undefined>;
  reviewEvent$: EventEmitter<void>;
  clearAll: () => void;
  contentType: number;
};

const tagInputStyle: React.CSSProperties = {
  width: 64,
  height: 22,
  marginInlineEnd: 8,
  verticalAlign: 'top',
};

const QualityTagsModal = (props: TagsModalProps) => {
  const {
    title,
    open,
    closeModal,
    projectId,
    submitIds: ids,
    categoryType,
    actionRef,
    reviewEvent$,
    clearAll,
    contentType,
  } = props;
  const [searchKey, setSearchKey] = useState('');
  const [selectedTagId, setSelectedTagId] = useState<number | null>(null);
  const [editModalVisible, setEditModalVisible] = useState<boolean>(false);
  const [selectingRow, setSelectingRow] = useState<CommonTagGroup>();
  const [editingRow, setEditingRow] = useState<CommonTagGroup>();
  const editInputRef = useRef<InputRef>(null);
  const [editInputValue, setEditInputValue] = useState('');

  useEffect(() => {
    editInputRef.current?.focus();
  }, [editInputValue]);

  const { data: tagGroups, refresh: refreshTagGroups } = useRequest(
    () => GetTagGroups(TagsType.qualityReviewTag, { projectId }),
    {
      refreshDeps: [projectId],
      ready: open,
    },
  );

  const { run: addTags } = useRequest(
    () => {
      if (editingRow) {
        return EditTagGroup(editingRow.id, {
          name: editingRow.name,
          addTagNames: [editInputValue],
        });
      }
      return Promise.reject();
    },
    {
      manual: true,
      onSuccess() {
        refreshTagGroups();
      },
    },
  );

  const tagGroupFilter = searchKey
    ? tagGroups?.filter((tagGroup) => tagGroup.tags.some((tag) => tag.name.includes(searchKey)))
    : tagGroups;

  const handleOk = async () => {
    if (selectedTagId) {
      const result = await PostAuditHandle({
        projectId,
        ids,
        categoryType,
        handleType: 2,
        tagId: selectedTagId,
        contentType,
      });
      if (result.code === 0) {
        message.success('操作成功');
        actionRef.current?.reload();
        actionRef.current?.clearSelected?.();
        clearAll();
        reviewEvent$.emit();
        closeModal();
      } else {
        message.error('操作失败');
      }
    } else {
      message.error('请选择标签');
    }
  };

  const handleChange = (tag: number) => {
    setSelectedTagId((prevSelectedTagId) => (prevSelectedTagId === tag ? null : tag));
  };

  const handleTagsChange = async (params: TagGroupEditParam | TagGroupCreateParam) => {
    const gotChange = Object.keys(params).some((key) => {
      const param = (params as TagGroupEditParam)?.[key as keyof TagGroupEditParam];
      const nameChange = params['name'] !== selectingRow?.name;
      const tagChange =
        key !== 'name' &&
        ((Array.isArray(param) && param.length > 0) ||
          (typeof param === 'object' && Object.keys(param).length > 0));
      return nameChange || tagChange;
    });
    if (selectingRow && gotChange) {
      const res = await EditTagGroup(selectingRow.id, params);
      refreshTagGroups();
      return res.code === 0;
    } else if (!selectingRow) {
      const res = await CreateTagGroup(TagsType.qualityReviewTag, {
        ...(params as TagGroupCreateParam),
        projectId,
      });
      refreshTagGroups();
      return res.code === 0;
    }
    return true;
  };

  const handleDeleteGroup = async (group: CommonTagGroup) => {
    Modal.confirm({
      title: `删除标签分组`,
      content: `是否确认删除「${group.name}」分组？`,
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk() {
        DeleteTagGroup(group.id).then((res) => {
          if (res.code === 0) {
            refreshTagGroups();
          }
        });
      },
    });
  };

  const handleEditInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEditInputValue(e.target.value);
  };

  const handleEditInputConfirm = () => {
    if (editInputValue) {
      addTags();
    }
    setEditInputValue('');
    setEditingRow(undefined);
  };

  return (
    <>
      <Modal title={title} open={open} onOk={handleOk} onCancel={closeModal} destroyOnHidden>
        <div style={{ height: 1, backgroundColor: '#F1F1F1', marginBottom: 20 }} />
        <Input
          placeholder="输入关键词检索标签和标签组"
          onChange={(e) => setSearchKey(e.target.value)}
          prefix={<SearchOutlined style={{ color: '#696666' }} />}
        />

        <div style={{ marginTop: 10, maxHeight: 220, overflow: 'auto' }}>
          {tagGroupFilter?.map((group) => {
            return (
              <div key={group.id} style={{ marginBottom: 15 }}>
                <Space style={{ marginBlockEnd: '8px' }}>
                  <span style={{ fontSize: 14, fontWeight: 400 }}>{group.name}</span>
                  <Tooltip key={'remove'} title="删除">
                    <SvgIcon
                      icon="local:outline/delete-outline"
                      y={3}
                      onClick={() => handleDeleteGroup(group)}
                    />
                  </Tooltip>
                  <Tooltip key={'edit'} title="编辑">
                    <EditOutlined
                      style={{ color: '#696666' }}
                      onClick={() => {
                        setSelectingRow(group);
                        setEditModalVisible(true);
                      }}
                    />
                  </Tooltip>
                </Space>
                <div>
                  {group.tags.map((tag) => (
                    <Tag.CheckableTag
                      key={tag.id}
                      style={{ border: '1px solid #DBDBDB', marginBlockEnd: '5px' }}
                      checked={selectedTagId === tag.id}
                      onChange={() => handleChange(tag.id)}
                    >
                      {tag.name}
                    </Tag.CheckableTag>
                  ))}
                  {editingRow?.id === group.id ? (
                    <Input
                      ref={editInputRef}
                      key={group.id}
                      size="small"
                      style={tagInputStyle}
                      value={editInputValue}
                      onChange={handleEditInputChange}
                      onBlur={handleEditInputConfirm}
                      onPressEnter={handleEditInputConfirm}
                    />
                  ) : (
                    <Button size="small" onClick={() => setEditingRow(group)}>
                      +添加
                    </Button>
                  )}
                </div>
              </div>
            );
          })}
          <div style={{ color: '#2160F9' }} onClick={() => setEditModalVisible(true)}>
            <PlusCircleOutlined /> 添加标签组
          </div>
        </div>
      </Modal>
      <CreateModal
        type={selectingRow?.id ? 'edit' : 'create'}
        setVisible={setEditModalVisible}
        visible={editModalVisible}
        initialValues={selectingRow}
        onSubmitEdit={handleTagsChange}
        onSubmitCreate={handleTagsChange}
      />
    </>
  );
};

export default QualityTagsModal;
