.custom_check_card_group {
  :global {
    .ant-pro-checkcard {
      background-color: #f6f9fe;
      border: none;
    }

    .ant-pro-checkcard.ant-pro-checkcard-checked {
      background-color: #edf3ff;
      border-color: #1e5eff;
      border: 1px solid #1e5eff;
    }
    .ant-pro-checkcard.ant-pro-checkcard-checked:after {
      width: 20px;
      height: 20px;
      display: block;
      content: '';
      position: absolute;
      background: url('@/assets/wa_video-type-icon.png') no-repeat;
      background-size: 100% auto;
      top: -1px;
      right: 0;
      border: none;
    }

    .ant-pro-checkcard-loading {
      width: 228px;
      height: 101px;
      overflow: hidden;
    }
  }
}

.custom_check_card {
  :global {
    .title {
      font-size: 14px;
      font-weight: 500;
      word-break: break-word;
    }

    .num-hight {
      font-size: 30px;
      font-weight: 600;
      word-break: break-word;
    }

    .num-unit {
      font-size: 12px;
      font-weight: 400;
      margin-left: 4px;
    }

    .blue {
      color: #1e5eff;
    }
  }
}

.custom_check_card_group_left {
  :global {
    .ant-pro-checkcard {
      background-color: #f6f9fe;
      border: none;
    }

    .ant-pro-checkcard-content {
      padding-block: 12px;
    }

    .ant-pro-checkcard-title {
      font-size: 14px;
      font-weight: 500;

      .title {
      }
      .title-num {
        margin-left: 5px;
      }
    }

    .ant-pro-checkcard.ant-pro-checkcard-checked {
      background-color: #edf3ff;
      border: 1px solid #1e5eff;

      &::after {
        border: none;
      }

      .ant-pro-checkcard-title {
        color: #1e5eff;
      }
    }
  }
}

.prolist_wrapper {
  :global {
    .ant-pro-card-body {
      position: relative;
    }

    .ant-pagination-total-text {
      margin-top: 5px;
    }
    .ant-pagination-disabled {
      div {
        color: #d6d6d6 !important;
        border-color: #d6d6d6 !important;
      }

      div:hover {
        color: #d6d6d6 !important;
        background-color: unset !important;
        border-color: #d6d6d6 !important;
      }
    }

    .ant-pagination-jump-next-custom-icon {
      height: 32px !important;
      line-height: 32px !important;
    }

    .ant-pagination-options {
      .ant-pagination-options-size-changer {
        height: 34px;
      }
    }

    .ant-pro-checkcard-content {
      padding: 0;
    }

    .ant-pro-checkcard.ant-pro-checkcard-checked {
      background-color: #edf3ff;
      border: 1px solid #1e5eff;

      &::after {
        border: none;
      }
    }

    .ant-list {
      height: 100vh;
      overflow-y: auto;
      overflow-x: hidden;
      padding-inline: 10px;
      position: static;
      padding-bottom: 50px;
    }

    .ant-list-pagination {
      position: absolute;
      right: 0;
      bottom: 8px;
      width: 100%;
      background-color: #fff;
      padding-block: 10px;
    }

    .ant-pagination-item {
      min-width: 32px !important;
      height: 32px !important;
      font-family: OPPOSans;
      line-height: 32px !important;
      background-color: #e8edfb !important;
      margin-inline: 8px !important;

      &.ant-pagination-item-active {
        line-height: 32px;
        background-color: #2160f9 !important;
        border: none;
        a {
          color: #fff;
          font-weight: 400;
          font-size: 14px;
          font-style: normal;
        }
      }
    }

    /* 图片形态特殊设置 */
    .pro-list-img {
      .ant-list-pagination {
        bottom: 0px;
      }
    }

    .analysis {
      .ant-list {
        /* 不能固定高度 使用 css 变量动态传入的高度 */
        /* height: 100%;
          max-height: var(--custom-height); */
        /* height: calc(100vh - 500px); */
      }

      .ant-list-pagination {
        bottom: 0px;
      }
    }
  }
}

.blue_tabs {
  display: inline-block;

  & > .ant-tabs-nav {
    position: relative;
    margin-bottom: 0px;

    &::before {
      border-bottom: none;
    }
  }
  & .ant-tabs-tab {
    color: #64666b;
    padding: 0;
    margin: 0 24px 0 0px !important;
  }

  :global {
    .ant-tabs-content-holder {
      padding: 0;
    }

    .ant-radio-button-wrapper {
      border: none;
    }

    .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
      color: #ffffff !important;
      background-color: #1e5eff;
      border-radius: 4px;
      padding: 0 4px;
      height: 24px;
      line-height: 24px;
    }
  }
}

.simple_check_card {
  & > .ant-tabs-nav {
    position: relative;
    margin-bottom: 0px;

    &::before {
      border-bottom: none;
    }
  }

  & .ant-tabs-tab {
    background: rgba(30, 94, 255, 0.04);
    border-radius: 4px;
    color: #64666b;
    padding: 5px 8px;
    margin: 0 12px 0 0px !important;
    font-size: 14px;
    box-sizing: border-box;
  }
  & .ant-tabs-tab.ant-tabs-tab-active {
    border: 1px #1e5eff solid;
  }
  :global {
    .ant-tabs-content-holder {
      padding: 0;
    }
  }
}

.tag {
  width: 40px;
  height: 20px;
  white-space: nowrap;
  padding: 1px 5px;
  border-radius: 3px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  margin-right: 5px;
  font-size: 12px;
  font-weight: 400;
}
