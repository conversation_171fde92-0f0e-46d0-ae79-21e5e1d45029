import { waitTime } from '@/pages/ProjectHome/Business/incentiveForm';
import { GetAuditOverview } from '@/services/quality-audit';
import { DownOutlined, UpOutlined } from '@ant-design/icons';
import { useGetState, useRequest } from 'ahooks';
import { EventEmitter } from 'ahooks/lib/useEventEmitter';
import { Flex, message } from 'antd';
import { useEffect, useState } from 'react';
import { CheckCard } from '@ant-design/pro-components';
import styles from './style.module.less';

type PendingReviewDataProps = {
  categoryType: number;
  setCategoryType: React.Dispatch<React.SetStateAction<number>>;
  projectId: string;
  rangeTime: string[];
  setPendingReviewTotal?: React.Dispatch<React.SetStateAction<number>>;
  rollbackEvent$: EventEmitter<void>;
  reviewEvent$: EventEmitter<void>;
  refreshEvent$: EventEmitter<void>;
  revokeEvent$: EventEmitter<void>;
};

const PendingReviewData = (props: PendingReviewDataProps) => {
  const {
    categoryType,
    setCategoryType,
    projectId,
    rangeTime,
    setPendingReviewTotal,
    rollbackEvent$,
    reviewEvent$,
    refreshEvent$,
    revokeEvent$,
  } = props;
  const [total, setTotal, getCount] = useGetState(0);
  const [messageApi, contextHolder] = message.useMessage();
  const [fold, setFold] = useState(false);

  useEffect(() => {
    setTotal(0);
  }, [rangeTime, projectId]);

  const messageInfo = (distance: number) => {
    messageApi.open({
      type: 'success',
      content: `新增${distance}条待审核数据`,
      style: {
        marginTop: '10vh',
      },
    });
  };

  const {
    data: auditOverviewResult,
    refresh: refreshAuditOverview,
    refreshAsync: refreshAuditOverviewAsync,
    loading: loadingAuditOverview,
  } = useRequest(
    () => {
      return GetAuditOverview({
        projectId,
        startDate: rangeTime[0],
        endDate: rangeTime[1],
      });
    },
    {
      refreshDeps: [projectId, rangeTime],
      onSuccess: (res) => {
        const result = res.data;
        if (result) {
          const {
            brandViolationUnAuditCount,
            platformViolationUnAuditCount,
            anchorImageViolationUnAuditCount,
          } = result;
          if (
            brandViolationUnAuditCount &&
            platformViolationUnAuditCount &&
            anchorImageViolationUnAuditCount
          ) {
            const totalNum =
              Number(brandViolationUnAuditCount) +
              Number(platformViolationUnAuditCount) +
              Number(anchorImageViolationUnAuditCount);
            console.log('totalNum', totalNum);
            setTotal(totalNum);
            setPendingReviewTotal && setPendingReviewTotal(totalNum);
          }
        }
      },
    },
  );

  const auditOverview = auditOverviewResult?.data;

  rollbackEvent$.useSubscription(() => {
    refreshAuditOverview();
    // 将统计 state 重置
    setTotal(0);
  });

  reviewEvent$.useSubscription(() => {
    refreshAuditOverview();
    // 将统计 state 重置
    setTotal(0);
  });

  revokeEvent$.useSubscription(() => {
    // 将统计 state 重置
    setTotal(0);
  });

  refreshEvent$.useSubscription(async () => {
    console.log('getCount()', getCount());
    console.log('total', total);
    await refreshAuditOverviewAsync();
    await waitTime(1000);
    // 最新的统计值和之前的统计值做减
    const distance = getCount() - total;
    const adjustedDistance = Math.max(distance, 0);
    adjustedDistance >= 0 && messageInfo(adjustedDistance);
  });

  return (
    <>
      {contextHolder}
      {!fold ? (
        <CheckCard.Group
          value={categoryType}
          className={styles.custom_check_card_group}
          onChange={(value) => {
            value && setCategoryType(value as number);
          }}
          loading={loadingAuditOverview}
          defaultValue={1}
          style={{ display: 'block', paddingTop: '24px' }}
        >
          <CheckCard
            className={styles.custom_check_card}
            description={
              <Flex
                justify="space-between"
                style={{ color: categoryType === 1 ? '#1E5EFF' : 'black' }}
              >
                <Flex vertical>
                  <div
                    className="title"
                    style={{ color: categoryType === 1 ? '#1E5EFF' : '#64666B' }}
                  >
                    品牌违规
                  </div>
                  <div>
                    <span className="num-hight">{auditOverview?.brandViolationCount}</span>
                    <span className="num-unit">条</span>
                  </div>
                </Flex>
                <Flex
                  vertical
                  gap={8}
                  style={{ fontSize: 12, fontWeight: 400, marginRight: '12px' }}
                  justify="center"
                >
                  <div>
                    已审核{' '}
                    <span className="blue">{auditOverview?.brandViolationAuditCount} 条</span>
                  </div>
                  <div>
                    待审核{' '}
                    <span className="blue">{auditOverview?.brandViolationUnAuditCount} 条</span>
                  </div>
                </Flex>
              </Flex>
            }
            value={1}
            style={{ width: 230, marginBlockEnd: 0 }}
          />
          <CheckCard
            className={styles.custom_check_card}
            description={
              <Flex
                justify="space-between"
                style={{ color: categoryType === 2 ? '#1E5EFF' : 'black' }}
              >
                <Flex vertical>
                  <div
                    className="title"
                    style={{ color: categoryType === 2 ? '#1E5EFF' : '#64666B' }}
                  >
                    平台违规
                  </div>
                  <div>
                    <span className="num-hight">{auditOverview?.platformViolationCount}</span>
                    <span className="num-unit">条</span>
                  </div>
                </Flex>
                <Flex
                  vertical
                  gap={8}
                  style={{ fontSize: 12, fontWeight: 400, marginRight: '12px' }}
                  justify="center"
                >
                  <div>
                    已审核{' '}
                    <span className="blue">{auditOverview?.platformViolationAuditCount} 条</span>
                  </div>
                  <div>
                    待审核{' '}
                    <span className="blue">{auditOverview?.platformViolationUnAuditCount} 条</span>
                  </div>
                </Flex>
              </Flex>
            }
            value={2}
            style={{ width: 230, marginBlockEnd: 0 }}
          />
          <CheckCard
            className={styles.custom_check_card}
            description={
              <Flex
                justify="space-between"
                style={{ color: categoryType === 3 ? '#1E5EFF' : 'black' }}
              >
                <Flex vertical>
                  <div
                    className="title"
                    style={{ color: categoryType === 3 ? '#1E5EFF' : '#64666B' }}
                  >
                    主播形象违规
                  </div>
                  <div>
                    <span className="num-hight">{auditOverview?.anchorImageViolationCount}</span>
                    <span className="num-unit">条</span>
                  </div>
                </Flex>
                <Flex
                  vertical
                  gap={8}
                  style={{ fontSize: 12, fontWeight: 400, marginRight: '12px' }}
                  justify="center"
                >
                  <div>
                    已审核{' '}
                    <span className="blue">{auditOverview?.anchorImageViolationAuditCount} 条</span>
                  </div>
                  <div>
                    待审核{' '}
                    <span className="blue">
                      {auditOverview?.anchorImageViolationUnAuditCount} 条
                    </span>
                  </div>
                </Flex>
              </Flex>
            }
            value={3}
            style={{ width: 230, marginBlockEnd: 0 }}
          />
        </CheckCard.Group>
      ) : null}
      <span
        style={{ float: 'right', color: '#1E5EFF', marginTop: '5px', marginRight: '10px' }}
        onClick={() => {
          setFold((prev) => !prev);
        }}
      >
        {!fold ? '收起' : '展开'} {!fold ? <UpOutlined /> : <DownOutlined />}
      </span>
    </>
  );
};

export default PendingReviewData;
