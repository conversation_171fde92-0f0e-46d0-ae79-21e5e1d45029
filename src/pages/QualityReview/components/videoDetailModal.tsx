import { QualityAuditAnalysisPageVO, QualityAuditPageVO } from '@/services/quality-audit';
import { Empty, Flex, Modal, Spin } from 'antd';
import { lazy, Suspense, useCallback, useMemo, useRef } from 'react';
import Player from 'xgplayer';

type VideoDetailModalProps = {
  isModalOpen: boolean;
  handleCancel: () => void;
  videoDetailData: QualityAuditPageVO | QualityAuditAnalysisPageVO;
};

const XGPlayer = lazy(() => import('@/components/xgplayer'));

const VideoDetailModal = (props: VideoDetailModalProps) => {
  const { isModalOpen, handleCancel, videoDetailData } = props;
  const videoPlayerRef = useRef<Player | null>(null);
  const { url, targetType, startTime, content, cleanFlag } = videoDetailData;

  const playerOptions = useMemo(() => {
    const videoWidth = 1080;
    const videoHeight = 1920;
    const maxWidth = 400 - 40 - 20;
    const height = (videoHeight * maxWidth) / videoWidth;
    return {
      width: maxWidth,
      height: height,
      loop: true,
      controls: true,
      autoplay: false,
      playbackRate: [0.5, 1, 1.5, 2],
    };
  }, []);

  const onVideoReady = useCallback(
    (player: Player) => {
      videoPlayerRef.current = player;

      const cfg = localStorage.getItem('video-card-preview-volume');
      if (cfg) {
        try {
          const { volume, muted } = JSON.parse(cfg);
          player.volume = volume;
          player.muted = muted;
        } catch (err) {
          console.log(err);
        }
      }

      if (videoPlayerRef.current) {
        videoPlayerRef.current.currentTime = Number(startTime);
        videoPlayerRef.current.play();
      }
    },
    [startTime],
  );

  return (
    <Modal
      centered
      title="视频片段"
      open={isModalOpen}
      onCancel={handleCancel}
      footer={false}
      destroyOnHidden
      width={760}
    >
      <Flex>
        <div>
          {cleanFlag ? (
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description={<span>视频超过存储时长，已清理</span>}
              style={{ paddingTop: '50px' }}
            />
          ) : (
            <Suspense fallback={<Spin />}>
              <XGPlayer
                url={url}
                isLive={false}
                type={targetType === 1 ? 'm3u8' : 'mp4'}
                playerOptions={playerOptions}
                onPlayerReady={onVideoReady}
              />
            </Suspense>
          )}
        </div>
        <Flex vertical style={{ paddingLeft: '20px' }}>
          <h3>文本内容</h3>
          <div
            style={{ width: '360px', maxHeight: '575px', overflowY: 'auto' }}
            dangerouslySetInnerHTML={{
              __html: content,
            }}
          />
        </Flex>
      </Flex>
    </Modal>
  );
};

export default VideoDetailModal;
