import { GetAnalysisMonitoringWordOverview } from '@/services/quality-audit';
import { CheckCard, ProCard } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { EventEmitter } from 'ahooks/lib/useEventEmitter';
import { Checkbox, Flex, Space, Splitter } from 'antd';
import { useAtomValue } from 'jotai';
import { useEffect, useState } from 'react';
import { monitoringWordMapAtom } from '.';
import QualityImgList from './components/qualityImgList';
import QualityList from './components/qualityList';
import styles from './components/style.module.less';

const targetTypeOptions = [
  { label: '直播', value: 1 },
  { label: '作品', value: 2 },
];

type AnalysisReviewProps = {
  rangeTime: string[];
  projectId: string;
  categoryType: number;
  monitoringWordId: string;
  reviewEvent$: EventEmitter<void>;
  rollbackEvent$: EventEmitter<void>;
  revokeEvent$: EventEmitter<void>;
};

const AnalysisReview = (props: AnalysisReviewProps) => {
  const {
    projectId,
    rangeTime,
    categoryType,
    monitoringWordId,
    reviewEvent$,
    rollbackEvent$,
    revokeEvent$,
  } = props;
  const [targetTypeList, setTargetTypeList] = useState<number[]>();
  const [monitoringWord, setMonitoringWord] = useState<string>();
  const monitoringWordMap = useAtomValue(monitoringWordMapAtom);

  const { data: monitoringWordData } = useRequest(
    () =>
      GetAnalysisMonitoringWordOverview({
        projectId,
        startDate: rangeTime[0],
        endDate: rangeTime[1],
        categoryType,
        monitoringWordId,
      }),
    {
      refreshDeps: [projectId, rangeTime, categoryType, monitoringWordId],
      onSuccess: (res) => {
        setMonitoringWord(res?.[0]?.label);
      },
    },
  );

  useEffect(() => {
    setMonitoringWord('');
  }, [monitoringWordId]);

  // 图片违规的形态
  const isImageViolation =
    categoryType === 3 || monitoringWordMap[monitoringWordId] === 'quote-violation';

  return (
    <ProCard
      style={{
        marginTop: '20px',
        height: '100%',
        overflow: 'hidden',
      }}
      direction="column"
    >
      <ProCard
        split="vertical"
        ghost
        headerBordered
        headStyle={{
          paddingBottom: '10px',
          paddingTop: '0px',
        }}
      >
        {isImageViolation ? (
          <>
            {categoryType === 3 ? null : (
              <Space style={{ marginTop: '10px' }}>
                <span style={{ fontSize: '14px', fontWeight: 500 }}>类型:</span>
                <Checkbox.Group
                  options={targetTypeOptions}
                  onChange={(checkedValues) => setTargetTypeList(checkedValues as number[])}
                />
              </Space>
            )}
            <QualityImgList
              projectId={projectId}
              rangeTime={rangeTime}
              categoryType={categoryType}
              monitoringWordId={monitoringWordId}
              targetTypeList={targetTypeList}
              reviewEvent$={reviewEvent$}
              rollbackEvent$={rollbackEvent$}
              revokeEvent$={revokeEvent$}
              type="analysis"
            />
          </>
        ) : (
          <Splitter>
            <Splitter.Panel defaultSize={270} max={270} collapsible style={{ overflow: 'auto' }}>
              <div
                style={{
                  overflowY: 'auto',
                  height: '100%',
                  maxHeight: 'calc(100vh - 320px)',
                  overflowX: 'hidden',
                }}
              >
                <CheckCard.Group
                  className={styles.custom_check_card_group}
                  value={monitoringWord}
                  onChange={(value) => {
                    value && setMonitoringWord(value as string);
                  }}
                  style={{ paddingTop: '16px' }}
                >
                  {monitoringWordData?.map((item) => (
                    <CheckCard
                      className={styles.custom_check_card}
                      key={item.label}
                      description={
                        <Flex
                          justify="space-between"
                          style={{
                            color: monitoringWord === item.label ? '#1E5EFF' : 'black',
                          }}
                        >
                          <Flex vertical>
                            <div
                              className="title"
                              style={{
                                color: monitoringWord === item.label ? '#1E5EFF' : '#64666B',
                              }}
                            >
                              {item.label}
                            </div>
                            <div>
                              <span className="num-hight">{item.errorRate}%</span>
                            </div>
                          </Flex>
                          <Flex
                            vertical
                            gap={8}
                            style={{ fontSize: 12, fontWeight: 400, marginRight: '12px' }}
                            justify="center"
                          >
                            <div>
                              总违规 <span className="blue">{item.auditCount} 条</span>
                            </div>
                            <div>
                              撤销违规 <span className="blue">{item.revokeCount} 条</span>
                            </div>
                          </Flex>
                        </Flex>
                      }
                      value={item.label}
                      style={{ width: 250, marginBlockEnd: 10 }}
                    />
                  ))}
                </CheckCard.Group>
              </div>
            </Splitter.Panel>
            <Splitter.Panel>
              <div style={{ padding: '16px' }}>
                <Space style={{ marginLeft: '20px' }}>
                  <span style={{ fontSize: '14px', fontWeight: 500 }}>类型:</span>
                  <Checkbox.Group
                    options={targetTypeOptions}
                    onChange={(checkedValues) => setTargetTypeList(checkedValues as number[])}
                  />
                </Space>
              </div>
              <QualityList
                projectId={projectId}
                rangeTime={rangeTime}
                categoryType={categoryType}
                monitoringWordId={monitoringWordId}
                monitoringWord={monitoringWord}
                targetTypeList={targetTypeList}
                reviewEvent$={reviewEvent$}
                rollbackEvent$={rollbackEvent$}
                revokeEvent$={revokeEvent$}
                type="analysis"
              />
            </Splitter.Panel>
          </Splitter>
        )}
      </ProCard>
    </ProCard>
  );
};

export default AnalysisReview;
