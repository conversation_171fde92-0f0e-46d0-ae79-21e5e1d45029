.custom-table {
  .ant-table-sticky-scroll-bar {
    display: none;
  }
  .ant-table-container table > thead > tr:first-child > th.highlight-table-header {
    color: #1e5eff !important;
  }
  &.ant-table-wrapper .ant-table-thead > tr > th {
    background: #fafcfe;
  }
  .ant-pagination-total-text {
    height: 32px !important;
    line-height: 32px !important;
  }
  .ant-pagination-disabled {
    div {
      color: #d6d6d6 !important;
      border-color: #d6d6d6 !important;
    }

    div:hover {
      color: #d6d6d6 !important;
      background-color: unset !important;
      border-color: #d6d6d6 !important;
    }
  }

  .ant-pagination-jump-next-custom-icon {
    height: 32px !important;
    line-height: 32px !important;
  }

  .ant-pagination-item {
    min-width: 32px !important;
    height: 32px !important;
    font-family: OPPOSans;
    line-height: 32px !important;
    background-color: #e8edfb !important;
    margin-inline: 8px !important;

    &.ant-pagination-item-active {
      line-height: 32px;
      background-color: #2160f9 !important;
      border: none;

      a {
        color: #fff;
        font-weight: 400;
        font-size: 14px;
        font-style: normal;
      }
    }
  }

  .ant-pagination-options {
    .ant-select {
      height: 32px !important;
    }
    .ant-pagination-options-size-changer {
      height: 34px;
    }
  }

  .ant-table-body {
    &::-webkit-scrollbar {
      // display: none;
      opacity: 0;
    }
  }

  :global {
    .ant-table-container .ant-table-cell {
      white-space: nowrap;
    }
  }

  // 解决 scroll x: max-content的时候空数据空白列的问题
  .live-info {
    width: 336px;
  }

  .video-info {
    width: 336px;
  }

  .afkLevel-info {
    width: 300px;
  }

  .table-90-col {
    width: 90px;
  }

  .table-120-col {
    width: 120px;
  }

  .table-150-col {
    width: 150px;
  }

  .table-300-col {
    width: 300px;
  }
}
