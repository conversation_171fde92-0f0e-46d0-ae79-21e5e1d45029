import ChatReport from '@/components/defaultRemindComponent/chatReport';
import LiveQualityReport from '@/components/defaultRemindComponent/liveQualityReport';
import PostQualityReport from '@/components/defaultRemindComponent/postQualityReport';
import PlatformSwitch from '@/components/platformSwitch';
import { loginOut } from '@/components/RightContent/AvatarDropdown';
import TimeFilterByType from '@/components/timFilterByType.tsx';
import useTabKeySearchParams from '@/hooks/useTabKeySearchParams';
import { checkFreePage } from '@/utils';
import { loginPath } from '@/utils/const';
import { PageContainer } from '@ant-design/pro-components';
import { useModel, useSearchParams, history, useParams } from '@umijs/max';
import { useTitle } from 'ahooks';
import { Button, Space, Tabs } from 'antd';
import { useState } from 'react';
import { flushSync } from 'react-dom';
import './index.less';

type TimeFilterByType = {
  rangeTime: string[];
  radioValue: 'day' | 'week' | 'month' | null;
};

const WebDaily = () => {
  useTitle('提醒日报');
  const { projectId } = useParams();
  const [searchParams] = useSearchParams();
  const type = searchParams.get('type');
  const typeArr = type?.split(',');
  const { setInitialState } = useModel('@@initialState');
  const defaultKey = typeArr?.[0] === 'chat' ? 'chat-daily' : 'live-quality-daily';
  const [activeKey, onTabChange] = useTabKeySearchParams(defaultKey);
  const [timeFilterByType, setTimeFilterByType] = useState<TimeFilterByType>({
    rangeTime: [],
    radioValue: null,
  });

  const handleTimeFilterChange = (filter: TimeFilterByType) => {
    setTimeFilterByType(filter);
  };

  const defaultItems =
    typeArr?.flatMap((type) => {
      const resultItems = [];
      if (type === 'quality') {
        resultItems.push(
          {
            label: '直播质检日报',
            key: 'live-quality-daily',
            children: (
              // 基本上是从DailyReport复制出来改造的 不再封装了 定制化需求太多 估计后面还会变
              <LiveQualityReport
                rangeTime={timeFilterByType.rangeTime}
                dataType={timeFilterByType.radioValue}
                projectId={projectId}
              />
            ),
          },
          {
            label: '作品质检日报',
            key: 'post-quality-daily',
            // 基本上是从DailyReport复制出来改造的 不再封装了 定制化需求太多 估计后面还会变
            children: (
              <PostQualityReport
                rangeTime={timeFilterByType.rangeTime}
                dataType={timeFilterByType.radioValue}
                projectId={projectId}
              />
            ),
          },
        );
      }
      if (type === 'chat') {
        resultItems.push({
          label: '私信日报',
          key: 'chat-daily',
          children: (
            <ChatReport
              rangeTime={timeFilterByType.rangeTime}
              dataType={timeFilterByType.radioValue}
              projectId={projectId}
            />
          ),
        });
      }
      return resultItems;
    }) || [];

  const logoutAndClearInfo = () => {
    flushSync(() => {
      setInitialState((s: any) => ({ ...s, currentUser: undefined }));
    });
    if (!checkFreePage()) {
      history.replace(
        loginPath +
          '?redirect=' +
          encodeURIComponent(window.location.pathname + window.location.search),
      );
    }
  };

  const handleLogout = () => {
    logoutAndClearInfo();
    loginOut();
  };

  return (
    <PageContainer
      title="提醒管理/每日日报"
      extra={
        <Space>
          <PlatformSwitch />
          <Button key="logout" onClick={handleLogout}>
            退出登录
          </Button>
        </Space>
      }
    >
      <Tabs
        activeKey={activeKey}
        onChange={onTabChange}
        items={defaultItems}
        tabBarExtraContent={<TimeFilterByType onTimeFilterChange={handleTimeFilterChange} />}
        className="horizontal-tab"
      />
    </PageContainer>
  );
};

export default WebDaily;
