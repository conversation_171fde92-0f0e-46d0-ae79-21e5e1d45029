import { selectPlatformAtom } from '@/pages/ProjectHome/atom';
import {
  QualityExV2LiveViolationAggregation,
  QualityLiveViolationAggregationDailyV2VO,
} from '@/services/quality-v2';
import { useRequest } from '@umijs/max';
import { CheckListValue } from 'antd-mobile/es/components/check-list';
import { useAtomValue } from 'jotai';
import { useEffect, useState } from 'react';
import { FlexDiv } from './liveQualityCardByAccount';
import {
  Column,
  DataContain,
  DataContainItem,
  DetailCard,
  HeaderContent,
  ItemContent,
  ItemTitle,
  MonitorContent,
  MonitorTitle,
  MonitorWords,
  Title,
} from './styles';

type TeamCardDetailProps = {
  value: QualityLiveViolationAggregationDailyV2VO;
  afkState?: boolean;
  anchorImageState?: boolean;
};

const TeamCardDetail = ({ value, afkState, anchorImageState }: TeamCardDetailProps) => {
  return (
    <DetailCard>
      <Column>
        <HeaderContent>
          <Title>{value.aggregationName}</Title>
        </HeaderContent>
        <DataContain>
          <DataContainItem>
            <ItemTitle>{value.liveCount}</ItemTitle>
            <ItemContent>总直播场次</ItemContent>
          </DataContainItem>
          <DataContainItem>
            <ItemTitle>{value.liveViolationCount}</ItemTitle>
            <ItemContent>违规直播场次</ItemContent>
          </DataContainItem>
          <DataContainItem>
            <ItemTitle>{value.violationPercent}%</ItemTitle>
            <ItemContent>违规占比</ItemContent>
          </DataContainItem>
          <DataContainItem>
            <ItemTitle>{value.violationRate}%</ItemTitle>
            <ItemContent>违规率</ItemContent>
          </DataContainItem>
          <DataContainItem>
            <ItemTitle>{value.brandViolationCount}</ItemTitle>
            <ItemContent>品牌违规场次</ItemContent>
          </DataContainItem>
          {anchorImageState && (
            <DataContainItem>
              <ItemTitle>{value.anchorImageViolationCount}</ItemTitle>
              <ItemContent>主播违规场次</ItemContent>
            </DataContainItem>
          )}
          <DataContainItem>
            <ItemTitle>{value.platformViolationCount}</ItemTitle>
            <ItemContent>平台违规场次</ItemContent>
          </DataContainItem>
          {afkState && (
            <DataContainItem>
              <ItemTitle>{value.liveAfkCount}</ItemTitle>
              <ItemContent>空挂播场次</ItemContent>
            </DataContainItem>
          )}
        </DataContain>
      </Column>
      {value.violationTypeNameStr && (
        <MonitorWords>
          <MonitorTitle>高频违规类型：</MonitorTitle>
          <MonitorContent>{value.violationTypeNameStr}</MonitorContent>
        </MonitorWords>
      )}
      {value.monitoringWordStr && (
        <MonitorWords>
          <MonitorTitle>高频触发监控词：</MonitorTitle>
          <MonitorContent>{value.monitoringWordStr}</MonitorContent>
        </MonitorWords>
      )}
    </DetailCard>
  );
};

type QualityCardByTeamProps = {
  rangeDate?: string[];
  fieldValueStr?: string;
  keyword?: string;
  liveAfkLevel?: CheckListValue | null;
  monitorType?: CheckListValue[];
  afkState?: boolean;
  anchorImageState?: boolean;
  fieldId?: number;
  projectId?: string;
};

const LiveQualityCardByTeam = (props: QualityCardByTeamProps) => {
  const { rangeDate, fieldId, keyword, projectId, afkState, anchorImageState } = props;
  const [startDate, endDate] = rangeDate || [undefined, undefined];
  const [filteredData, setFilteredData] = useState<QualityLiveViolationAggregationDailyV2VO[]>([]);
  const platform = useAtomValue(selectPlatformAtom);

  const { data } = useRequest(
    () =>
      QualityExV2LiveViolationAggregation({
        projectId,
        startDate,
        endDate,
        dateType: 4, // 移动端永远为自定义
        fieldId,
        platform,
      }),
    {
      ready: !!fieldId && !!rangeDate,
      refreshDeps: [fieldId, rangeDate, platform],
    },
  );

  useEffect(() => {
    if (data && keyword) {
      setFilteredData(data.filter((item) => item?.aggregationName?.includes(keyword)));
    } else {
      setFilteredData(data || []);
    }
  }, [keyword, data]);

  return (
    <FlexDiv>
      {filteredData?.map((item, index: number) => (
        <TeamCardDetail
          key={`card-${index}`}
          value={item}
          afkState={afkState}
          anchorImageState={anchorImageState}
        />
      ))}
    </FlexDiv>
  );
};

export default LiveQualityCardByTeam;
