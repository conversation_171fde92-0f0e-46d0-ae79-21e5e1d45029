import { scrollAntdMobileCalendar } from '@/utils/dailyUtils';
import { useEffect, useRef, useState } from 'react';
import { ClassifyType } from '@/utils/common';
import { ActionType } from '@ant-design/pro-components';
import { BodyDiv, FilterItem, MobileProTable } from './styles';
import { CalendarPicker, Input, Popup } from 'antd-mobile';
import { DownFill, SearchOutline } from 'antd-mobile-icons';
import dayjs from 'dayjs';
import { useParams, useRequest, useSearchParams } from '@umijs/max';
import CheckList from 'antd-mobile/es/components/check-list';
import {
  QualityExV2LiveViolationAccount,
  QualityExV2LiveViolationAggregation,
  QualityLiveViolationAccountDailyV2VO,
  QualityLiveViolationAggregationDailyV2VO,
} from '@/services/quality-v2';
import { ApiResult } from '@/services/common';
import { GetTeamDepthSelect } from '@/services/team';
import { renderLiveQualityColumns } from '@/components/defaultRemindComponent/liveQualityReport';
import useLiveAfkFG from '@/hooks/fg/useLiveAfkFG';
import useAnchorImageFG from '@/hooks/fg/useAnchorImageFG';
import { useAtomValue } from 'jotai';
import { selectPlatformAtom } from '@/pages/ProjectHome/atom';

const today = new Date();
const max = new Date(today.setDate(today.getDate() - 1));
const min = new Date(today.setMonth(today.getMonth() - 6));

const LandScapeScreenLiveTable = () => {
  const actionRef = useRef<ActionType>();
  const [searchParams] = useSearchParams();
  const { projectId } = useParams();
  const [classifyType, setClassifyType] = useState(ClassifyType.Team);
  const [calendarVisible, setCalendarVisible] = useState(false);
  const date = searchParams.get('date');
  const [rangeTime, setRangeTime] = useState<string[] | undefined | null>(
    date ? [date, date] : undefined,
  );
  const [startDate, endDate] = rangeTime || [];
  const [inputValue, setInputValue] = useState<string>('');
  const [keyword, setKeyword] = useState<string | undefined>(undefined);
  const [fieldId, setFieldId] = useState<number>();
  const { data: teamDepthData } = useRequest(() => GetTeamDepthSelect({ projectId }), {
    onSuccess: (data) => {
      const res = data?.[0].value;
      setFieldId(Number(res));
    },
  });
  const fieldName = teamDepthData?.find((item) => item.value === String(fieldId))?.name;
  const afkState = useLiveAfkFG(projectId);
  const anchorImageState = useAnchorImageFG(projectId);
  const [teamDepthVisible, setTeamDepthVisible] = useState(false);
  const platform = useAtomValue(selectPlatformAtom);

  useEffect(() => {
    actionRef.current?.reload();
    setKeyword('');
  }, [classifyType]);

  // antd-mobile组件暂时没有实现滚动到默认选中的日期 自行滚动下
  useEffect(() => {
    if (calendarVisible) {
      scrollAntdMobileCalendar();
    }
  }, [calendarVisible]);

  return (
    <>
      <BodyDiv>
        <MobileProTable
          // @ts-ignore
          columns={renderLiveQualityColumns(classifyType, fieldName, afkState, anchorImageState)}
          actionRef={actionRef}
          params={{
            projectId,
            startDate,
            endDate,
            dateType: 4,
            fieldId,
            keyword,
            platform,
          }}
          ghost
          scroll={{ x: 'max-content' }}
          size="small"
          style={{ overflow: 'hidden' }}
          headerTitle={
            <div
              style={{
                background: '#f3f3f3',
                borderRadius: '4px',
                width: '15rem',
                display: 'flex',
                alignItems: 'center',
                paddingInline: '1rem',
              }}
            >
              <Input
                placeholder="请输入关键词"
                value={inputValue}
                clearable
                onClear={() => {
                  setInputValue('');
                  setKeyword(undefined);
                }}
                onChange={(val) => {
                  setInputValue(val);
                }}
                onEnterPress={(e) => {
                  // @ts-ignore
                  setKeyword(e.target.value);
                }}
                style={{ '--font-size': '14px' }}
              />
              <SearchOutline onClick={() => setKeyword(inputValue)} />
            </div>
          }
          toolBarRender={() => [
            <FilterItem
              key="classify"
              onClick={() => {
                classifyType === ClassifyType.Account
                  ? setClassifyType(ClassifyType.Team)
                  : setClassifyType(ClassifyType.Account);
              }}
            >
              按账号
            </FilterItem>,
            <FilterItem
              key="date"
              onClick={() => {
                setCalendarVisible(true);
              }}
            >
              日期
              <DownFill style={{ fontSize: '0.5rem' }} />
            </FilterItem>,
            <FilterItem key="teamdepth" onClick={() => setTeamDepthVisible(true)}>
              按{fieldName} <DownFill style={{ fontSize: '0.5rem' }} />
            </FilterItem>,
          ]}
          postData={(
            data:
              | QualityLiveViolationAccountDailyV2VO[]
              | QualityLiveViolationAggregationDailyV2VO[],
          ) => {
            if (keyword) {
              if (classifyType === ClassifyType.Team) {
                return (data as QualityLiveViolationAggregationDailyV2VO[]).filter((item) =>
                  item?.aggregationName?.includes(keyword),
                );
              } else if (classifyType === ClassifyType.Account) {
                return (data as QualityLiveViolationAccountDailyV2VO[]).filter((item) =>
                  item?.nickname?.includes(keyword),
                );
              }
            }
            return data;
          }}
          request={async (params) => {
            let fetchFn: any;
            if (!rangeTime || !fieldId)
              return {
                total: 0,
                data: [],
                success: false,
              };
            if (classifyType === ClassifyType.Team) {
              fetchFn = QualityExV2LiveViolationAggregation;
            } else if (classifyType === ClassifyType.Account) {
              fetchFn = QualityExV2LiveViolationAccount;
            }
            delete params.current;
            delete params.pageSize;
            delete params.keyword;
            const result: ApiResult<
              QualityLiveViolationAccountDailyV2VO[] | QualityLiveViolationAggregationDailyV2VO[]
            > = await fetchFn({
              ...params,
            });
            return {
              total: result?.data?.length,
              data: result?.data,
              success: result.code === 0,
            };
          }}
          search={false}
          options={false}
          rowKey={(record) => record.teamCode + Math.random()}
          pagination={{
            defaultPageSize: 5,
            showSizeChanger: false,
          }}
          dateFormatter="string"
        />
      </BodyDiv>
      <CalendarPicker
        visible={calendarVisible}
        selectionMode="range"
        max={max}
        min={min}
        onClose={() => setCalendarVisible(false)}
        onMaskClick={() => setCalendarVisible(false)}
        defaultValue={rangeTime ? [new Date(rangeTime[0]), new Date(rangeTime[1])] : null}
        onConfirm={(val: [Date, Date] | null) => {
          const formatVal = val?.map((item) => dayjs(item).format('YYYY-MM-DD'));
          setRangeTime(formatVal);
        }}
      />
      <Popup visible={teamDepthVisible} onMaskClick={() => setTeamDepthVisible(false)}>
        <CheckList
          defaultValue={fieldId ? [fieldId] : []}
          onChange={(val) => {
            setFieldId?.(val[0] as number);
            setTeamDepthVisible(false);
          }}
        >
          {teamDepthData?.map(({ name, value }) => (
            // @ts-ignore
            <CheckList.Item key={name} value={value}>
              {name}
            </CheckList.Item>
          ))}
        </CheckList>
      </Popup>
    </>
  );
};

export default LandScapeScreenLiveTable;
