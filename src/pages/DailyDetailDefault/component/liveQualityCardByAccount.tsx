import { selectPlatform<PERSON>tom } from '@/pages/ProjectHome/atom';
import {
  QualityExV2LiveViolationAccount,
  QualityLiveViolationAccountDailyV2VO,
} from '@/services/quality-v2';
import { useRequest } from '@umijs/max';
import { CheckListValue } from 'antd-mobile/es/components/check-list';
import { useAtomValue } from 'jotai';
import { useEffect, useState } from 'react';
import styled from 'styled-components';
import {
  Column,
  DataContain,
  DataContainItem,
  DetailCard,
  HeaderContent,
  ItemContent,
  ItemTitle,
  MonitorContent,
  MonitorTitle,
  MonitorWords,
  Title,
  UIDLeft,
  UIDRow,
} from './styles';

type AccountCardDetailProps = {
  value: QualityLiveViolationAccountDailyV2VO;
  afkState?: boolean;
  anchorImageState?: boolean;
};

const AccountCardDetail = ({ value, afkState, anchorImageState }: AccountCardDetailProps) => {
  return (
    <DetailCard>
      <Column>
        <HeaderContent>
          <Title>{value.nickname}</Title>
        </HeaderContent>
        <UIDRow>
          <UIDLeft>ID:{value.showAccountId}</UIDLeft>
          <span>{value.teamName}</span>
        </UIDRow>
        <DataContain>
          <DataContainItem>
            <ItemTitle>{value.liveCount}</ItemTitle>
            <ItemContent>总直播场次</ItemContent>
          </DataContainItem>
          <DataContainItem>
            <ItemTitle>{value.liveViolationCount}</ItemTitle>
            <ItemContent>违规直播场次</ItemContent>
          </DataContainItem>
          <DataContainItem>
            <ItemTitle>{value.violationPercent}%</ItemTitle>
            <ItemContent>违规占比</ItemContent>
          </DataContainItem>
          <DataContainItem>
            <ItemTitle>{value.violationRate}%</ItemTitle>
            <ItemContent>违规率</ItemContent>
          </DataContainItem>
          <DataContainItem>
            <ItemTitle>{value.brandViolationCount}</ItemTitle>
            <ItemContent>品牌违规场次</ItemContent>
          </DataContainItem>
          {anchorImageState && (
            <DataContainItem>
              <ItemTitle>{value.anchorImageViolationCount}</ItemTitle>
              <ItemContent>主播违规场次</ItemContent>
            </DataContainItem>
          )}
          <DataContainItem>
            <ItemTitle>{value.platformViolationCount}</ItemTitle>
            <ItemContent>平台违规场次</ItemContent>
          </DataContainItem>
          {afkState && (
            <DataContainItem>
              <ItemTitle>{value.liveAfkCount}</ItemTitle>
              <ItemContent>空挂播场次</ItemContent>
            </DataContainItem>
          )}
        </DataContain>
      </Column>
      {value.violationTypeNameStr && (
        <MonitorWords>
          <MonitorTitle>高频违规类型：</MonitorTitle>
          <MonitorContent>{value.violationTypeNameStr}</MonitorContent>
        </MonitorWords>
      )}
      {value.monitoringWordStr && (
        <MonitorWords>
          <MonitorTitle>高频触发监控词：</MonitorTitle>
          <MonitorContent>{value.monitoringWordStr}</MonitorContent>
        </MonitorWords>
      )}
    </DetailCard>
  );
};

type QualityCardByAccountProps = {
  rangeDate?: string[];
  fieldValueStr?: string;
  keyword?: string;
  liveAfkLevel?: CheckListValue | null;
  monitorType?: CheckListValue[];
  afkState?: boolean;
  anchorImageState?: boolean;
  projectId?: string;
};

export const FlexDiv = styled.div`
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 30px;
`;

const LiveQualityCardByAccount = (props: QualityCardByAccountProps) => {
  const { rangeDate, keyword, projectId, afkState, anchorImageState } = props;
  const [startDate, endDate] = rangeDate || [undefined, undefined];
  const [filteredData, setFilteredData] = useState<QualityLiveViolationAccountDailyV2VO[]>([]);
  const platform = useAtomValue(selectPlatformAtom);

  const { data } = useRequest(
    () =>
      QualityExV2LiveViolationAccount({
        projectId,
        startDate,
        endDate,
        dateType: 4, // 移动端永远为自定义
        platform,
      }),
    {
      ready: !!rangeDate,
      refreshDeps: [rangeDate, platform],
    },
  );

  useEffect(() => {
    if (data && keyword) {
      setFilteredData(data.filter((item) => item?.nickname?.includes(keyword)));
    } else {
      setFilteredData(data || []);
    }
  }, [keyword, data]);

  return (
    <FlexDiv>
      {filteredData?.map((item, index: number) => (
        <AccountCardDetail
          key={`card-${index}`}
          value={item}
          afkState={afkState}
          anchorImageState={anchorImageState}
        />
      ))}
    </FlexDiv>
  );
};

export default LiveQualityCardByAccount;
