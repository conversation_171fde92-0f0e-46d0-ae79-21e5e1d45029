import { renderColumns, scrollAntdMobileCalendar } from '@/utils/dailyUtils';
import { useEffect, useRef, useState } from 'react';
import { ClassifyType } from '@/utils/common';
import { ActionType } from '@ant-design/pro-components';
import { BodyDiv, FilterItem, MobileProTable } from './styles';
import { CalendarPicker, Cascader, Input } from 'antd-mobile';
import { DownFill, FilterOutline, SearchOutline } from 'antd-mobile-icons';
import dayjs from 'dayjs';
import { useParams, useSearchParams } from '@umijs/max';
import useGetCascaderOptionsByProjectId from '@/hooks/useGetCascaderOptionsByProjectId';
import { CheckListValue } from 'antd-mobile/es/components/check-list';
import { ChatViolationAccount, ChatViolationTeam } from '@/services/daily';
import { proTableRequestAdapterParamsAndData } from '@/utils';
import { useAtomValue } from 'jotai';
import { selectPlatformAtom } from '@/pages/ProjectHome/atom';

const today = new Date();
const max = new Date(today.setDate(today.getDate() - 1));
const min = new Date(today.setMonth(today.getMonth() - 6));

const LandScapeScreenChatTable = () => {
  const actionRef = useRef<ActionType>();
  const [searchParams] = useSearchParams();
  const { projectId } = useParams();
  const [classifyType, setClassifyType] = useState(ClassifyType.Team);
  const [calendarVisible, setCalendarVisible] = useState(false);
  const [treeSelectVisible, setTreeSelectVisible] = useState(false);
  const [showSelectRes, setShowSelectRes] = useState<string | null>(null);
  const date = searchParams.get('date');
  const [rangeTime, setRangeTime] = useState<string[] | undefined | null>(
    date ? [date, date] : undefined,
  );
  const [startDate, endDate] = rangeTime || [];
  const cascaderOptions = useGetCascaderOptionsByProjectId(projectId);
  const [cascaderValue, setCascaderValue] = useState<CheckListValue[]>([]);
  const [inputValue, setInputValue] = useState<string>('');
  const [keyword, setKeyword] = useState<string | undefined>(undefined);
  const [fieldValueStr, setFieldValueStr] = useState<CheckListValue | undefined>(undefined);
  const platform = useAtomValue(selectPlatformAtom);

  useEffect(() => {
    const field = cascaderValue.at(-1);
    setFieldValueStr(field);
  }, [cascaderValue]);

  useEffect(() => {
    actionRef.current?.reload();
    setKeyword('');
  }, [classifyType]);

  // antd-mobile组件暂时没有实现滚动到默认选中的日期 自行滚动下
  useEffect(() => {
    if (calendarVisible) {
      scrollAntdMobileCalendar();
    }
  }, [calendarVisible]);

  return (
    <>
      <BodyDiv>
        <MobileProTable
          // @ts-ignore
          columns={renderColumns('chat', classifyType)}
          actionRef={actionRef}
          params={{
            startDate,
            endDate,
            keyword,
            fieldValueStr,
            platform,
          }}
          ghost
          scroll={{ x: 'max-content' }}
          size="small"
          style={{ overflow: 'hidden' }}
          headerTitle={
            <div
              style={{
                background: '#f3f3f3',
                borderRadius: '4px',
                width: '15rem',
                display: 'flex',
                alignItems: 'center',
                paddingInline: '1rem',
              }}
            >
              <Input
                placeholder="请输入经销商或者账号名称/ID"
                value={inputValue}
                clearable
                onClear={() => {
                  setInputValue('');
                  setKeyword(undefined);
                }}
                onChange={(val) => {
                  setInputValue(val);
                }}
                onEnterPress={(e) => {
                  // @ts-ignore
                  setKeyword(e.target.value);
                }}
                style={{ '--font-size': '14px' }}
              />
              <SearchOutline onClick={() => setKeyword(inputValue)} />
            </div>
          }
          toolBarRender={() => [
            <FilterItem
              key="classify"
              onClick={() => {
                classifyType === ClassifyType.Account
                  ? setClassifyType(ClassifyType.Team)
                  : setClassifyType(ClassifyType.Account);
              }}
            >
              按账号
            </FilterItem>,
            <FilterItem
              key="date"
              onClick={() => {
                setCalendarVisible(true);
              }}
            >
              日期
              <DownFill style={{ fontSize: '0.5rem' }} />
            </FilterItem>,
            <FilterItem
              key="region"
              onClick={() => {
                setTreeSelectVisible(true);
              }}
            >
              地区
              <FilterOutline />
              <span>{showSelectRes}</span>
            </FilterItem>,
          ]}
          request={(params, sorter) => {
            if (!rangeTime) return Promise.resolve();
            let fetchFn: any;
            if (classifyType === ClassifyType.Team) {
              fetchFn = ChatViolationTeam;
            } else if (classifyType === ClassifyType.Account) {
              fetchFn = ChatViolationAccount;
            }
            const formattedData: any = {
              ...params,
              projectId,
            };
            if (params.fieldValueStr) {
              const [value, fieldId] = params.fieldValueStr.split('-');
              formattedData.fieldList = [{ fieldId, value }];
              delete formattedData.fieldValueStr;
            }
            return proTableRequestAdapterParamsAndData(formattedData, sorter, fetchFn, [
              'size',
              'page',
              'payload',
              'orderBy',
              'orderType',
            ]);
          }}
          search={false}
          options={false}
          rowKey={(record) => record.teamCode + Math.random()}
          pagination={{
            defaultPageSize: 5,
            showSizeChanger: false,
          }}
          dateFormatter="string"
        />
      </BodyDiv>
      <CalendarPicker
        visible={calendarVisible}
        selectionMode="range"
        max={max}
        min={min}
        onClose={() => setCalendarVisible(false)}
        onMaskClick={() => setCalendarVisible(false)}
        defaultValue={rangeTime ? [new Date(rangeTime[0]), new Date(rangeTime[1])] : null}
        onConfirm={(val: [Date, Date] | null) => {
          const formatVal = val?.map((item) => dayjs(item).format('YYYY-MM-DD'));
          setRangeTime(formatVal);
        }}
      />
      <Cascader
        options={cascaderOptions}
        visible={treeSelectVisible}
        onClose={() => {
          setTreeSelectVisible(false);
        }}
        value={cascaderValue}
        onConfirm={(value: CheckListValue[], extend) => {
          setCascaderValue(value);
          const extendItems = extend.items;
          if (extendItems.every((item) => item === null)) {
            setShowSelectRes('未选择');
          } else {
            const res = extendItems.map((item) => item?.label ?? '未选择').join('-');
            setShowSelectRes(res);
          }
        }}
      />
    </>
  );
};

export default LandScapeScreenChatTable;
