import useLiveAfkFG from '@/hooks/fg/useLiveAfkFG';
import { ClassifyType } from '@/utils/common';
import { useParams } from '@umijs/max';
import { CheckListValue } from 'antd-mobile/es/components/check-list';
import { useState } from 'react';
import FilterParamsComponent from './filterParams';
import { DailyDetailBody } from './styles';
import LiveQualityCardByTeam from './liveQualityCardByTeam';
import LiveQualityCardByAccount from './liveQualityCardByAccount';
import useAnchorImageFG from '@/hooks/fg/useAnchorImageFG';

type QualitySwiperItemProps = {
  date: string | null;
  keyword?: string;
};

const LiveQualitySwiperItem = (props: QualitySwiperItemProps) => {
  const { keyword } = props;
  const { projectId } = useParams();
  const [classifyType, setClassifyType] = useState(ClassifyType.Team);
  const [rangeDate, setRangeDate] = useState<string[] | undefined>(undefined);
  const [fieldValueStr, setFieldValueStr] = useState<string | undefined>(undefined);
  const [liveAfkLevel, setLiveAfkLevel] = useState<CheckListValue | null>(null);
  const [monitorType, setMonitorType] = useState<CheckListValue[] | undefined>();
  const afkState = useLiveAfkFG(projectId);
  const anchorImageState = useAnchorImageFG(projectId);
  const [fieldId, setFieldId] = useState<number>();

  return (
    <div style={{ paddingInline: '0.625rem' }}>
      <FilterParamsComponent
        classifyType={classifyType}
        setClassifyType={setClassifyType}
        setRangeDate={setRangeDate}
        setFieldValueStr={setFieldValueStr}
        liveAfkLevel={liveAfkLevel}
        setLiveAfkLevel={setLiveAfkLevel}
        monitorType={monitorType}
        setMonitorType={setMonitorType}
        afkState={afkState}
        showRegion={false}
        showTeamDepth={classifyType === ClassifyType.Team}
        fieldId={fieldId}
        setFieldId={setFieldId}
      />
      <DailyDetailBody id="scrollableDiv">
        {classifyType === ClassifyType.Account ? (
          <LiveQualityCardByAccount
            rangeDate={rangeDate}
            keyword={keyword}
            liveAfkLevel={liveAfkLevel}
            monitorType={monitorType}
            afkState={afkState}
            anchorImageState={anchorImageState}
            projectId={projectId}
          />
        ) : (
          <LiveQualityCardByTeam
            rangeDate={rangeDate}
            fieldValueStr={fieldValueStr}
            keyword={keyword}
            liveAfkLevel={liveAfkLevel}
            monitorType={monitorType}
            afkState={afkState}
            anchorImageState={anchorImageState}
            projectId={projectId}
            fieldId={fieldId}
          />
        )}
      </DailyDetailBody>
    </div>
  );
};

export default LiveQualitySwiperItem;
