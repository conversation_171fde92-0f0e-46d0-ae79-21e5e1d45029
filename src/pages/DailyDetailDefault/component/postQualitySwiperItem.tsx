import useLiveAfkFG from '@/hooks/fg/useLiveAfkFG';
import { ClassifyType } from '@/utils/common';
import { useParams } from '@umijs/max';
import { CheckListValue } from 'antd-mobile/es/components/check-list';
import { useState } from 'react';
import FilterParamsComponent from './filterParams';
import { DailyDetailBody } from './styles';
import PostQualityCardByAccount from './postQualityCardByAccount';
import PostQualityCardByTeam from './postQualityCardByTeam';

type QualitySwiperItemProps = {
  date: string | null;
  keyword?: string;
};

const PostQualitySwiperItem = (props: QualitySwiperItemProps) => {
  const { keyword } = props;
  const { projectId } = useParams();
  const [classifyType, setClassifyType] = useState(ClassifyType.Team);
  const [rangeDate, setRangeDate] = useState<string[] | undefined>(undefined);
  const [fieldValueStr, setFieldValueStr] = useState<string | undefined>(undefined);
  const [liveAfkLevel, setLiveAfkLevel] = useState<CheckListValue | null>(null);
  const [monitorType, setMonitorType] = useState<CheckListValue[] | undefined>();
  const afkState = useLiveAfkFG(projectId);
  const [fieldId, setFieldId] = useState<number>();

  return (
    <div style={{ paddingInline: '0.625rem' }}>
      <FilterParamsComponent
        classifyType={classifyType}
        setClassifyType={setClassifyType}
        setRangeDate={setRangeDate}
        setFieldValueStr={setFieldValueStr}
        liveAfkLevel={liveAfkLevel}
        setLiveAfkLevel={setLiveAfkLevel}
        monitorType={monitorType}
        setMonitorType={setMonitorType}
        afkState={afkState}
        showRegion={false}
        showTeamDepth={classifyType === ClassifyType.Team}
        fieldId={fieldId}
        setFieldId={setFieldId}
      />
      <DailyDetailBody id="scrollableDiv">
        {classifyType === ClassifyType.Account ? (
          <PostQualityCardByAccount
            rangeDate={rangeDate}
            keyword={keyword}
            liveAfkLevel={liveAfkLevel}
            monitorType={monitorType}
            afkState={afkState}
            projectId={projectId}
          />
        ) : (
          <PostQualityCardByTeam
            rangeDate={rangeDate}
            fieldValueStr={fieldValueStr}
            keyword={keyword}
            liveAfkLevel={liveAfkLevel}
            monitorType={monitorType}
            afkState={afkState}
            projectId={projectId}
            fieldId={fieldId}
          />
        )}
      </DailyDetailBody>
    </div>
  );
};

export default PostQualitySwiperItem;
