import { ProTable } from '@ant-design/pro-components';
import { Tabs } from 'antd';
import styled from 'styled-components';

export const DetailCard = styled.div`
  border-radius: 16px;
  background-color: #fff;
  display: flex;
  width: 100%;
  flex-direction: column;
  padding: 0.9375rem 0.875rem;
`;

export const Column = styled.div`
  display: flex;
  flex-direction: column;
  line-height: normal;
`;

export const HeaderContent = styled.div`
  display: flex;
  align-items: center;
`;

export const QualityTag = styled.div<{
  $bgColor: string;
  $textColor: string;
}>`
  float: right;
  box-sizing: border-box;
  font-size: 0.75rem;
  padding: 1px 5px;
  background: ${(props) => props.$bgColor};
  color: ${(props) => props.$textColor};
  border-radius: 4px;
  font-weight: 400;
  border-color: transparent;
`;

export const Title = styled.div`
  color: #0e1015;
  font-weight: 500;
  font-size: 0.875rem;
  flex: 1;
`;

export const UID = styled.div`
  justify-content: center;
  border-radius: 0.5rem;
  background-color: rgba(30, 94, 255, 0.12);
  align-self: start;
  margin-top: 0.9375rem;
  color: #1e5eff;
  padding: 0.3125rem 0.375rem;
  font-size: 0.75rem;
`;

export const DataContain = styled.div`
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  margin-top: 1.0625rem;
  gap: 0.625rem;
  justify-content: space-between;
`;

export const DataContainItem = styled.div`
  display: flex;
  flex-direction: column;
  width: 23%;
  text-align: center;
`;

export const ItemTitle = styled.div`
  color: #0e1015;
  align-self: center;
  font-size: 0.875rem;
  font-family: OPPOSans;
`;

export const ItemContent = styled.div`
  color: #95979c;
  margin-top: 0.625rem;
  font-size: 0.75rem;
`;

export const MonitorWords = styled.div`
  border-radius: 8px;
  background-color: #f9fafe;
  display: flex;
  margin-top: 0.6875rem;
  font-size: 0.75rem;
  font-weight: 400;
  white-space: nowrap;
  padding: 0.1875rem 0;
`;

export const MonitorTitle = styled.div`
  color: #95979c;
`;

export const MonitorContent = styled.div`
  color: #1e5eff;
  flex-grow: 1;
  flex-basis: auto;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
`;

export const DailyDetailBody = styled.div`
  background-color: #f5f6fa;
  height: calc(100vh - 2rem - 2.75rem - 2.25rem);
  overflow-x: hidden;
  overflow-y: auto;

  .infinite-scroll-component {
    display: flex;
    flex-direction: column;
    gap: 0.625rem;
    align-items: center;
  }

  &::-webkit-scrollbar {
    display: none; /* Safari 和 Chrome */
  }
  -ms-overflow-style: none; /* IE 和 Edge */
  scrollbar-width: none; /* Firefox */
`;

export const UIDRow = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-top: 0.9375rem;
  gap: 0.5rem;

  span {
    color: #64666b;
    font-size: 0.75rem;
  }
`;

export const UIDLeft = styled.div`
  justify-content: center;
  border-radius: 0.5rem;
  background-color: rgba(30, 94, 255, 0.12);
  align-self: start;
  color: #1e5eff;
  padding: 0.3125rem 0.375rem;
  font-size: 0.75rem;
`;

export const FilterItem = styled.div`
  border-radius: 0.25rem;
  padding: 0.1875rem 0.3rem;
  height: 1.5rem;
  border: 0.0625rem solid #d3d5da;
  line-height: 1.4rem;
  text-align: center;
  color: #64666b;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.2rem;
  max-width: 17.5rem;
  overflow: hidden;
  flex-wrap: nowrap;
  white-space: nowrap;

  span {
    overflow: hidden;
    text-overflow: ellipsis;
  }
`;

export const BodyDiv = styled.div`
  display: flex;
  padding: 0px 1rem;
  overflow: hidden;
`;

export const MobileTabs = styled(Tabs)`
  .ant-tabs-nav {
    margin-bottom: 0px;
  }
`;

export const MobileProTable = styled(ProTable)`
  width: 100%;
  .ant-pro-table-list-toolbar-container {
    padding-block: 0.5rem;
  }

  @media (max-width: 768px) {
    .ant-pro-table-list-toolbar-left {
      margin: 0px;
    }

    .ant-pro-table-list-toolbar-container {
      flex-direction: row;
      flex-wrap: nowrap;
    }
  }
`;
