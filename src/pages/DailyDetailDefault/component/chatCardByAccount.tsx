import UseInfiniteScroll from '@/hooks/useInfiniteScroll';
import { ChatViolationAccount, ChatViolationAccountItem } from '@/services/daily';
import {
  Column,
  DataContain,
  DataContainItem,
  DetailCard,
  ItemContent,
  ItemTitle,
  Title,
  UIDLeft,
  UIDRow,
} from './styles';

type AccountCardDetailProps = {
  value: ChatViolationAccountItem;
};

const AccountCardDetail = ({ value }: AccountCardDetailProps) => {
  return (
    <DetailCard>
      <Column>
        <Title>{value.nickname}</Title>
        <UIDRow>
          <UIDLeft>ID:{value.showAccountId}</UIDLeft>
          <span>{value.teamName}</span>
        </UIDRow>
        <DataContain>
          <DataContainItem>
            <ItemTitle>{value.chatRound}</ItemTitle>
            <ItemContent>会话人数</ItemContent>
          </DataContainItem>
          <DataContainItem>
            <ItemTitle>{value.receiveMsgCount}</ItemTitle>
            <ItemContent>接收消息数</ItemContent>
          </DataContainItem>
          <DataContainItem>
            <ItemTitle>{value.replayMsgCount}</ItemTitle>
            <ItemContent>回复消息数</ItemContent>
          </DataContainItem>
          <DataContainItem>
            <ItemTitle>{value.notifyCount}</ItemTitle>
            <ItemContent>被提醒次数</ItemContent>
          </DataContainItem>
        </DataContain>
      </Column>
    </DetailCard>
  );
};

type ChatCardByAccountProps = {
  rangeDate?: string[];
  fieldValueStr?: string;
  keyword?: string;
};

const ChatCardByAccount = (props: ChatCardByAccountProps) => {
  const { rangeDate, fieldValueStr, keyword } = props;

  return (
    <UseInfiniteScroll<ChatViolationAccountItem>
      fetchFn={ChatViolationAccount}
      rangeDate={rangeDate}
      fieldValueStr={fieldValueStr}
      keyword={keyword}
    >
      {(data) => (
        <>
          {data.map((item, index: number) => (
            <AccountCardDetail key={`card-${index}`} value={item} />
          ))}
        </>
      )}
    </UseInfiniteScroll>
  );
};

export default ChatCardByAccount;
