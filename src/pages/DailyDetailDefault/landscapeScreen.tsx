import styled from 'styled-components';
import SwitchBtnImg from '@/assets/switch.png';
import useTabKeySearchParams from '@/hooks/useTabKeySearchParams';
import { useSearchParams } from '@umijs/max';
import { Modal } from 'antd-mobile';
import { useEffect } from 'react';
import LandScapeScreenChatTable from './component/landScapeScreenChatTable';
import LandScapeScreenLiveTable from './component/landScapeScreenLiveTable';
import { MobileTabs } from './component/styles';
import LandScapeScreenPostTable from './component/landScapeScreenPostTable';

const LandScreenBody = styled.div`
  padding-inline: 1rem;

  @media screen and (orientation: portrait) {
    position: absolute;
    top: 0;
    left: 100vw;
    width: 100vh;
    height: 100vw;
    -webkit-transform: rotate(90deg);
    -moz-transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    transform: rotate(90deg);
    -webkit-transform-origin: 0% 0%;
    -moz-transform-origin: 0% 0%;
    -ms-transform-origin: 0% 0%;
    transform-origin: 0% 0%;
  }
`;

const LandScapeScreen = ({
  setIslandscapeScreen,
}: {
  setIslandscapeScreen: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
  const [searchParams] = useSearchParams();
  const type = searchParams.get('type');
  const typeArr = type?.split(',');
  const [activeKey, onTabChange] = useTabKeySearchParams(typeArr ? typeArr[0] : 'chat');

  const tabItems: Array<{ label: string; key: string; children: React.JSX.Element }> =
    typeArr?.flatMap((type) => {
      const result = [];
      if (type === 'chat') {
        result.push({
          label: '私信日报',
          key: 'chat',
          children: <LandScapeScreenChatTable />,
        });
      }
      if (type === 'quality') {
        result.push(
          {
            label: '直播质检日报',
            key: 'quality-live',
            children: <LandScapeScreenLiveTable />,
          },
          {
            label: '作品质检日报',
            key: 'quality-post',
            children: <LandScapeScreenPostTable />,
          },
        );
      }
      return result;
    }) || [];

  useEffect(() => {
    if (window.orientation == 180 || window.orientation == 0) {
      Modal.show({
        content: (
          <div style={{ textAlign: 'center', fontSize: '0.75rem' }}>
            <div>为了更好的使用体验,请横屏使用</div>
            <div>开启手机旋转,确保底部按钮处于横屏状态</div>
          </div>
        ),
        closeOnMaskClick: true,
      });
    }
  }, []);

  return (
    <LandScreenBody>
      <MobileTabs
        activeKey={activeKey}
        onChange={onTabChange}
        items={tabItems}
        tabBarExtraContent={
          <img
            style={{ width: '1.5rem', height: '1.5rem', marginRight: '1rem' }}
            src={SwitchBtnImg}
            alt="switch"
            onClick={() => setIslandscapeScreen(false)}
          />
        }
      />
    </LandScreenBody>
  );
};

export default LandScapeScreen;
