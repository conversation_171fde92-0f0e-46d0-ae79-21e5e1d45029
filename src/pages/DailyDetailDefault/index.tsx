import { useEffect, useState } from 'react';
import LandScapeScreen from './landscapeScreen';
import VerticalScreen from './verticalScreen';

const MobileDailyDetail = () => {
  const [islandscapeScreen, setIslandscapeScreen] = useState(false);

  const orientationChange = () => {
    if (window.orientation == 180 || window.orientation == 0) {
      setIslandscapeScreen(false);
    }
    if (window.orientation == 90 || window.orientation == -90) {
      setIslandscapeScreen(true);
    }
  };

  useEffect(() => {
    orientationChange();
    window.addEventListener('orientationchange', orientationChange);
    return () => {
      window.removeEventListener('orientationchange', orientationChange);
    };
  }, []);

  return (
    <>
      {islandscapeScreen ? (
        <LandScapeScreen setIslandscapeScreen={setIslandscapeScreen} />
      ) : (
        <VerticalScreen setIslandscapeScreen={setIslandscapeScreen} />
      )}
    </>
  );
};

export default MobileDailyDetail;
