import React, { useEffect, useMemo, useRef } from 'react';
import ReactECharts from 'echarts-for-react';
import { max, min } from 'lodash-es';

// 类型用 type
export type PropsType = {
  chartData: { date: string; value: number }[];
  uniqId: string;
  width: number;
  height: number;
};

const LineChart = ({ chartData, uniqId, width, height }: PropsType) => {
  // 计算最大最小值，和原逻辑一致
  const maxValue = useMemo(() => max(chartData.map((i) => i.value)) || 1, [chartData]);
  const minValue = useMemo(() => min(chartData.map((i) => i.value)) || 0, [chartData]);
  const chartRef = useRef<any>(null);

  useEffect(() => {
    chartRef.current?.getEchartsInstance().resize();
  }, [chartRef.current]);

  // 组装 ECharts option
  const option = useMemo(
    () => ({
      grid: {
        left: 40,
        right: 20,
        top: 20,
        bottom: 45,
      },
      dataZoom: [
        {
          type: 'inside',
          xAxisIndex: [0],
          start: 0,
          end: 100,
          zoomOnMouseWheel: true,
          moveOnMouseMove: true,
        },
      ],
      xAxis: {
        type: 'category',
        data: chartData.map((item) => item.date),
        axisLine: { show: true, lineStyle: { color: '#ccc' } },
        axisTick: { show: true },
        axisLabel: {
          rotate: 90,
          color: '#666',
          fontSize: 12,
        },
        splitLine: { show: true, lineStyle: { color: '#f0f0f0' } },
      },
      yAxis: {
        type: 'value',
        min: Math.floor(minValue - minValue * 0.1),
        max: Math.ceil(maxValue + 0.1 * maxValue),
        axisLine: { show: true, lineStyle: { color: '#ccc' } },
        axisTick: { show: true },
        axisLabel: {
          color: '#666',
          fontSize: 12,
        },
        splitLine: { show: true, lineStyle: { color: '#f0f0f0' } },
      },
      series: [
        {
          data: chartData.map((item) => item.value),
          type: 'line',
          smooth: true,
          showSymbol: false,
          symbol: 'circle',
          symbolSize: 6,
          emphasis: {
            scale: 2,
            itemStyle: {
              color: '#6370FF',
            },
          },
          lineStyle: {
            color: '#6370FF',
            width: 2,
            opacity: 0.6,
          },
          label: {
            show: true,
            position: 'top',
            color: '#6370FF',
            fontSize: 12,
          },
          itemStyle: {
            color: '#6370FF',
            opacity: 0.8,
          },
        },
      ],
      tooltip: {
        trigger: 'axis',
        formatter: (params: any) => {
          const p = Array.isArray(params) ? params[0] : params;
          return `${p.axisValue}: ${p.data}`;
        },
        backgroundColor: '#fff',
        borderColor: '#ccc',
        borderWidth: 1,
        textStyle: { color: '#333' },
      },
    }),
    [chartData, maxValue, minValue],
  );

  return (
    <div id={uniqId} style={{ width, height }}>
      <ReactECharts ref={chartRef} option={option} style={{ width: '100%', height: '100%' }} />
    </div>
  );
};

export default LineChart;
