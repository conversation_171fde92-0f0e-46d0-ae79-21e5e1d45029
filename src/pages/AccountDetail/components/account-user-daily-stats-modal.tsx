import { GetAccountUserDailyStats } from '@/services/account';
import { AccountUserDailyStat } from '@/services/typings';
import {
  ProCard,
  ProForm,
  ProFormDateRangePicker,
  ProFormInstance,
} from '@ant-design/pro-components';
import { Modal } from 'antd';
import dayjs from 'dayjs';
import { max, min } from 'lodash-es';
import { useEffect, useRef, useState, useMemo } from 'react';
import ReactECharts from 'echarts-for-react';

interface PropsType {
  title: string;
  open: boolean;
  setOpen: (value: boolean) => void;
  accountId?: string;
  platform?: number;
  defaultLabelKey?: string;
}

const defaultFromDate = dayjs().add(-30, 'day').format('YYYY-MM-DD');
const defaultToDate = dayjs().format('YYYY-MM-DD');

const AccountUserDailyStatsModal = ({
  title,
  open,
  setOpen,
  accountId,
  platform,
  defaultLabelKey,
}: PropsType) => {
  const formRef = useRef<ProFormInstance>();
  const [isLoading, setIsLoading] = useState(false);
  const [params, setParams] = useState<{
    accountId: string;
    platform: number;
    fromDate: string;
    toDate: string;
  }>();
  const [data, setData] = useState<AccountUserDailyStat[]>();
  const [chartData, setChartData] = useState<{ date: string; value: number }[]>([]);
  const [labelKey, setLabelKey] = useState<string>();

  useEffect(() => {
    if (accountId && platform) {
      formRef.current?.setFieldValue('dateRange', [defaultFromDate, defaultToDate]);
      setParams({
        accountId,
        platform,
        fromDate: defaultFromDate,
        toDate: defaultToDate,
      });
    }
  }, [accountId, platform]);

  useEffect(() => {
    setLabelKey(defaultLabelKey);
  }, [defaultLabelKey]);

  useEffect(() => {
    if (params) {
      setIsLoading(true);
      GetAccountUserDailyStats(params).then((res) => {
        setData(res.data);
        setIsLoading(false);
      });
    }
  }, [params]);

  useEffect(() => {
    if (data) {
      setChartData(
        data.map((item) => {
          return {
            date: item.date,
            // @ts-ignore
            value: item[labelKey] ? +item[labelKey] : 0,
          };
        }),
      );
    } else {
      setChartData([]);
    }
  }, [data, labelKey]);

  // 计算最大最小值
  const maxValue = useMemo(() => max(chartData.map((i) => i.value)) || 1, [chartData]);
  const minValue = useMemo(() => min(chartData.map((i) => i.value)) || 0, [chartData]);

  // echarts 配置
  const option = useMemo(
    () => ({
      grid: {
        left: 40,
        right: 20,
        top: 20,
        bottom: 90,
      },
      xAxis: {
        type: 'category',
        data: chartData.map((item) => item.date),
        axisLine: { show: true, lineStyle: { color: '#ccc' } },
        axisTick: { show: true },
        axisLabel: {
          rotate: 90,
          color: '#666',
          fontSize: 12,
        },
        splitLine: { show: true, lineStyle: { color: '#f0f0f0' } },
      },
      yAxis: {
        type: 'value',
        min: Math.floor(minValue - minValue * 0.1),
        max: Math.ceil(maxValue + 0.1 * maxValue),
        axisLine: { show: true, lineStyle: { color: '#ccc' } },
        axisTick: { show: true },
        axisLabel: {
          color: '#666',
          fontSize: 12,
        },
        splitLine: { show: true, lineStyle: { color: '#f0f0f0' } },
      },
      series: [
        {
          data: chartData.map((item) => item.value),
          type: 'line',
          smooth: true,
          showSymbol: false,
          symbol: 'circle',
          symbolSize: 6,
          emphasis: {
            scale: 2,
            itemStyle: {
              color: '#6370FF',
            },
          },
          lineStyle: {
            color: '#6370FF',
            width: 2,
            opacity: 0.6,
          },
          label: {
            show: true,
            position: 'top',
            color: '#6370FF',
            fontSize: 12,
          },
          itemStyle: {
            color: '#6370FF',
            opacity: 0.8,
          },
        },
      ],
      tooltip: {
        trigger: 'axis',
        formatter: (params: any) => {
          const p = Array.isArray(params) ? params[0] : params;
          return `${p.axisValue}: ${p.data}`;
        },
        backgroundColor: '#fff',
        borderColor: '#ccc',
        borderWidth: 1,
        textStyle: { color: '#333' },
      },
    }),
    [chartData, maxValue, minValue],
  );

  return (
    <Modal
      title={title}
      open={open}
      onCancel={() => {
        setOpen(false);
      }}
      width={1200}
      footer={false}
    >
      <ProCard direction="column" gutter={[24, 24]} colSpan={24} ghost>
        <ProCard colSpan={24} ghost>
          <ProForm
            initialValues={{
              dateRange: [defaultFromDate, defaultToDate],
            }}
            layout="inline"
            formRef={formRef}
            onFinish={async (values) => {
              if (params && values?.dateRange) {
                setParams({
                  ...params,
                  fromDate: values.dateRange[0],
                  toDate: values.dateRange[1],
                });
              }
            }}
          >
            <ProFormDateRangePicker name={'dateRange'} />
          </ProForm>
        </ProCard>
        <ProCard colSpan={24} bordered loading={isLoading}>
          <ReactECharts option={option} style={{ width: '100%', height: 400 }} />
        </ProCard>
      </ProCard>
    </Modal>
  );
};

export default AccountUserDailyStatsModal;
