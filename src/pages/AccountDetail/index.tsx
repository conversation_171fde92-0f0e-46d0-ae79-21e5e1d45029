import XGVideoCard from '@/components/xgVideoCard';
import {
  AccountLiveItem,
  AccountPostItem,
  AccountUser,
  GetAccountUserDailyStats,
  GetAccountUserInfo,
  QueryAccountLivesForPagination,
  QueryAccountPostsForPagination,
} from '@/services/account';
import { AccountUserDailyStat } from '@/services/typings';
import { proTableRequestAdapterParamsAndData } from '@/utils';
import { AccountPlatformEnum, AccountPlatformKeysEnum, PlatForm } from '@/utils/platform';
import { ExportOutlined, LineChartOutlined, LinkOutlined } from '@ant-design/icons';
import {
  PageContainer,
  ProCard,
  ProColumns,
  ProDescriptions,
  ProDescriptionsItemProps,
  ProTable,
  StatisticCard,
} from '@ant-design/pro-components';
import { NavLink, useLocation, useParams, useSearchParams } from '@umijs/max';
import { But<PERSON>, Col, Image, message, Row, Space, Typography, Tooltip, DatePicker } from 'antd';
import copy from 'copy-to-clipboard';
import dayjs from 'dayjs';
import RcResizeObserver from 'rc-resize-observer';
import { useEffect, useState } from 'react';
import PostListCard from '../PostList/components/post-list-card';
import AccountUserDailyStatsModal from './components/account-user-daily-stats-modal';
import LineChart from './components/line-chart';

const chartDataItems: { key: string; label: string }[] = [
  {
    key: 'postCount',
    label: '最新作品数',
  },
  {
    key: 'followerCount',
    label: '最新粉丝数',
  },
  {
    key: 'totalFavorited',
    label: '最新获赞数',
  },
];

function useBaseInfo() {
  const location = useLocation();
  const { accountId, platformKey } = useParams();
  const [searchParams] = useSearchParams();

  if (location.pathname.indexOf('account-detail') > -1) {
    return {
      platformKey: searchParams.get('platformKey'),
      accountId: searchParams.get('accountId'),
    };
  }
  return { accountId, platformKey };
}

const AccountDetail: React.FC = () => {
  const { accountId, platformKey } = useBaseInfo();

  const platform = (platformKey && AccountPlatformKeysEnum[platformKey]) || 0;
  const [accountUser, setAccountUser] = useState<AccountUser>();
  const [accountUserDailyStats, setAccountUserDailyStats] = useState<AccountUserDailyStat[]>([]);

  const [chartSize, setChartSize] = useState<{
    width: number;
    cellWidth: number;
    colSpan: number;
  }>();

  const [openChartModal, setOpenChartModal] = useState(false);
  const [openChartData, setOpenChartData] = useState<{
    title: string;
    accountId: string;
    platform: number;
    defaultLabelKey: string;
  }>();

  const [postsTimeRange, setPostsTimeRange] = useState<[string, string]>([
    dayjs().subtract(7, 'days').format('YYYY-MM-DD'),
    dayjs().format('YYYY-MM-DD'),
  ]);
  const [livesTimeRange, setLivesTimeRange] = useState<[string, string]>([
    dayjs().subtract(7, 'days').format('YYYY-MM-DD'),
    dayjs().format('YYYY-MM-DD'),
  ]);

  useEffect(() => {
    if (!accountId || !platform) {
      return;
    }

    GetAccountUserInfo({ accountId, platform }).then((res) => {
      if (res.code === 0) {
        setAccountUser(res.data);
      } else {
        message.error('请求账号信息失败');
      }
    });

    GetAccountUserDailyStats({ accountId, platform, size: 7 }).then((res) => {
      if (res.code === 0) {
        setAccountUserDailyStats(res.data);
      } else {
        message.error('请求账号信息失败');
      }
    });
  }, [accountId, platform]);

  const accountUserColumns: Array<ProDescriptionsItemProps<AccountUser>> = [
    {
      title: '账号昵称',
      dataIndex: 'nickname',
      span: 1,
    },
    {
      title: '账号平台',
      dataIndex: 'platform',
      span: 1,
      renderText(_, record: { platform: PlatForm }) {
        return AccountPlatformEnum[record.platform]?.text || '';
      },
    },
    {
      title: 'IP信息',
      dataIndex: 'ipLocation',
      span: 1,
    },
    {
      title: '签名',
      dataIndex: 'signature',
      span: 3,
    },
    {
      title: '直播状态',
      dataIndex: 'roomId',
      hideInSearch: true,
      hideInTable: false,
      render(dom, entity) {
        if (!entity.liveInfo) {
          return '-';
        }

        return (
          <Space>
            <NavLink
              to={''}
              onClick={() => {
                window.open(`https://live.douyin.com/${entity.liveInfo?.webRid}`);
              }}
            >
              {`直播中(${entity.liveInfo.roomUserCount}人)`}
            </NavLink>
          </Space>
        );
      },
    },
    {
      title: '近7天直播',
      dataIndex: 'recentLiveNum',
      hideInSearch: true,
      hideInTable: false,
      renderText(dom, entity) {
        if (entity.recentLiveNum) {
          return `${entity.recentLiveNum}场`;
        }

        return '0';
      },
    },
    {
      title: '直播录制',
      dataIndex: 'liveReplayStatus',
      hideInSearch: true,
      hideInTable: false,
      renderText(text, record) {
        if (!record.syncInfo) {
          return null;
        }

        return record.syncInfo.liveReplayStatus === 1 ? '自动' : null;
      },
    },
    {
      title: '同步状态',
      dataIndex: 'lastSyncTime',
      hideInSearch: true,
      hideInTable: false,
      valueType: 'select',
      valueEnum: {
        正常: {
          text: '正常',
          color: 'green',
        },
        错误: {
          text: '错误',
          color: 'red',
        },
      },
      renderText(_, record) {
        const { syncInfo } = record;
        if (!syncInfo) {
          return '-';
        }

        return syncInfo.lastSyncPostStatus === -1 || syncInfo.lastSyncProfileStatus === -1
          ? '错误'
          : '正常';
      },
    },
    {
      title: '同步时间',
      dataIndex: 'last_sync_time',
      hideInSearch: true,
      hideInTable: false,
      renderText(_, record) {
        const { syncInfo } = record;
        if (!syncInfo) {
          return '-';
        }
        return dayjs(syncInfo.lastSyncTime).format('YYYY-MM-DD HH:mm');
      },
    },
    {
      title: '下次同步时间',
      dataIndex: 'next_sync_time',
      hideInSearch: true,
      hideInTable: false,
      renderText(_, record) {
        const { syncInfo } = record;
        if (!syncInfo) {
          return '-';
        }
        return dayjs(syncInfo.nextSyncTime).format('YYYY-MM-DD HH:mm');
      },
    },
  ];

  const postsColumns: Array<ProColumns<AccountPostItem>> = [
    {
      title: '发布时间',
      dataIndex: 'post_create_time',
      hideInSearch: true,
      hideInTable: false,
      renderText(_, record) {
        if (!record.postCreateTime) {
          return '-';
        }
        return dayjs(record.postCreateTime).format('YYYY-MM-DD HH:mm');
      },
    },
    {
      title: '标题',
      dataIndex: 'title',
      hideInSearch: true,
      hideInTable: false,
      width: 240,
      render(dom, entity) {
        let title: string | undefined = entity.title;
        let description: string | undefined = entity.description;
        if (!description && title) {
          description = title;
          title = undefined;
        }
        return (
          <div style={{ width: 240, overflowX: 'hidden' }}>
            {title && (
              <Typography.Paragraph
                style={{
                  margin: 0,
                  width: 240,
                  fontWeight: 'bold',
                }}
                ellipsis={{ rows: 1, expandable: true, symbol: '更多' }}
              >
                {title}
              </Typography.Paragraph>
            )}
            {description && (
              <Typography.Paragraph
                style={{ margin: 0, width: 240 }}
                ellipsis={{ rows: 4, expandable: true, symbol: '更多' }}
              >
                {description}
              </Typography.Paragraph>
            )}
          </div>
        );
      },
    },
    {
      title: '视频',
      dataIndex: 'videoUrl',
      hideInSearch: true,
      hideInTable: false,
      render(_, entity) {
        return <PostListCard entity={entity} />;
      },
    },
    {
      title: '最新点赞数',
      dataIndex: 'diggCount',
      hideInSearch: true,
      hideInTable: false,
      valueType: (entity) => (entity.diggCount !== null ? 'digit' : 'text'),
      renderText: (_, entity) =>
        entity.diggCount !== null ? entity.diggCount : entity.diggCountStr,
    },
    {
      title: '最新评论数',
      dataIndex: 'commentCount',
      hideInSearch: true,
      hideInTable: false,
      valueType: (entity) => (entity.commentCount !== null ? 'digit' : 'text'),
      renderText: (_, entity) =>
        entity.commentCount !== null ? entity.commentCount : entity.commentCountStr,
    },
    {
      title: '最新收藏数',
      dataIndex: 'collectCount',
      hideInSearch: true,
      hideInTable: false,
      valueType: (entity) => (entity.collectCount !== null ? 'digit' : 'text'),
      renderText: (_, entity) =>
        entity.collectCount !== null ? entity.collectCount : entity.collectCountStr,
    },
    {
      title: '最新分享数',
      dataIndex: 'shareCount',
      hideInSearch: true,
      hideInTable: false,
      valueType: (entity) => (entity.shareCount !== null ? 'digit' : 'text'),
      renderText: (_, entity) =>
        entity.shareCount !== null ? entity.shareCount : entity.shareCountStr,
    },
    {
      title: '发布时间',
      dataIndex: 'post_create_time',
      valueType: 'dateTimeRange',
      hideInTable: true,
      initialValue: [dayjs().subtract(7, 'days'), dayjs()],
      search: {
        transform: (value) => {
          return {
            startTime: value[0],
            endTime: value[1],
          };
        },
      },
    },
    {
      title: '作品 ID',
      dataIndex: 'postId',
      hideInTable: true,
    },
    // {
    //   title: '操作',
    //   dataIndex: 'action',
    //   hideInSearch: true,
    //   hideInTable: false,
    //   render(_, record) {
    //     return (
    //       <Space>
    //         <a
    //           onClick={() => {
    //             // navigation(`/car/car-lib/series/${entity.id}`);
    //           }}
    //         >
    //           查看
    //         </a>
    //       </Space>
    //     );
    //   },
    // },
  ];

  const livesColumns: Array<ProColumns<AccountLiveItem>> = [
    {
      title: '开播时间',
      dataIndex: 'start_time',
      hideInSearch: true,
      hideInTable: false,
      renderText(_, record) {
        if (!record.startTime) {
          return '-';
        }
        return dayjs(record.startTime).format('YYYY-MM-DD HH:mm');
      },
    },
    {
      title: '停播时间',
      dataIndex: 'start_time',
      hideInSearch: true,
      hideInTable: false,
      renderText(_, record) {
        if (!record.stopTime) {
          return '-';
        }
        return dayjs(record.stopTime).format('YYYY-MM-DD HH:mm');
      },
    },
    {
      title: '直播间',
      dataIndex: 'roomTitle',
      hideInSearch: true,
      hideInTable: false,
      renderText(_, record) {
        return (
          <Space>
            <span>{record.roomTitle || '-'}</span>
            <Tooltip title="复制直播间ID">
              <Button
                size="small"
                onClick={() => {
                  if (record.roomId) {
                    copy(record.roomId);
                    message.success('已复制到剪切板');
                  }
                }}
              >
                ID
              </Button>
            </Tooltip>
          </Space>
        );
      },
    },
    {
      title: '在线人数',
      dataIndex: 'roomUserCount',
      hideInSearch: true,
      hideInTable: false,
      valueType: 'digit',
    },
    {
      title: '累计观看人数',
      dataIndex: 'totalUserCountStr',
      hideInSearch: true,
      hideInTable: false,
    },
    {
      title: '直播状态',
      dataIndex: 'roomStatus',
      hideInSearch: false,
      hideInTable: false,
      valueEnum(entity) {
        return {
          1: {
            text: (
              <NavLink
                to={''}
                onClick={() => {
                  window.open(`https://live.douyin.com/${entity.webRid}`);
                }}
              >
                <Space>
                  直播中
                  <ExportOutlined />
                </Space>
              </NavLink>
            ),
            color: 'green',
          },
          0: {
            text: '已结束',
            color: 'gray',
          },
        };
      },
    },
    {
      title: '直播录制',
      dataIndex: 'replayStatus',
      hideInSearch: true,
      hideInTable: false,
      valueEnum(entity) {
        return {
          2: {
            text: entity.replayFileUrl ? (
              <Space>
                <XGVideoCard
                  playableVideoUrl={entity.replayFileUrl}
                  videoUrl={entity.replayFileUrl}
                  width={54}
                  height={95}
                  videoWidth={entity.replayFileWidth || 540}
                  videoHeight={entity.replayFileHeight || 540}
                  isLive={entity.roomStatus === 1}
                  type="m3u8"
                />
                <Tooltip title="复制直播地址">
                  <Button
                    size="small"
                    type="text"
                    onClick={() => {
                      if (entity.replayFileUrl) {
                        copy(entity.replayFileUrl);
                        message.success('已复制到剪切板');
                      }
                    }}
                  >
                    <LinkOutlined />
                  </Button>
                </Tooltip>
              </Space>
            ) : (
              '-'
            ),
            // color: 'green',
          },
          1: {
            text: '录制中',
            color: 'green',
          },
          0: {
            text: '未录制',
            color: 'gray',
          },
        };
      },
      renderText(text, record) {
        return record.replayStatus || 0;
      },
    },
    {
      title: '开播时间',
      dataIndex: 'live_start_time',
      valueType: 'dateTimeRange',
      hideInTable: true,
      initialValue: [dayjs().subtract(7, 'days'), dayjs()],
      search: {
        transform: (value) => {
          return {
            startTime: value[0],
            endTime: value[1],
          };
        },
      },
    },
    {
      title: '房间 ID',
      dataIndex: 'roomId',
      hideInTable: true,
    },
    // {
    //   title: '操作',
    //   dataIndex: 'action',
    //   hideInSearch: true,
    //   hideInTable: false,
    //   render(_, record) {
    //     return (
    //       <Space>
    //         <a
    //           onClick={() => {
    //             // navigation(`/car/car-lib/series/${entity.id}`);
    //           }}
    //         >
    //           查看
    //         </a>
    //       </Space>
    //     );
    //   },
    // },
  ];

  if (!accountId) {
    return null;
  }

  return (
    <PageContainer
      title={'账号详情'}
      onBack={() => {
        history.back();
      }}
    >
      <ProCard gutter={[0, 24]} ghost direction="column">
        <ProCard
          title="基本信息"
          extra={[
            <>
              {[PlatForm.Douyin, PlatForm.XiaoHongShu].includes(platform) && (
                <NavLink
                  key="douyin_page"
                  to={''}
                  onClick={() => {
                    if (platform === PlatForm.Douyin) {
                      window.open(`https://www.douyin.com/user/${accountId}`);
                    } else if (platform === PlatForm.XiaoHongShu) {
                      window.open(`https://www.xiaohongshu.com/user/profile/${accountId}`);
                    }
                  }}
                >
                  {platform === PlatForm.Douyin ? '抖音主页' : '小红书主页'}
                </NavLink>
              )}
            </>,
          ]}
        >
          <ProCard ghost direction="row" wrap>
            <ProCard ghost colSpan={{ md: 24, lg: 18 }}>
              <ProDescriptions
                bordered
                size="small"
                column={{ xs: 1, sm: 1, md: 1, lg: 1, xl: 2, xxl: 3 }}
                labelStyle={{ width: 110 }}
                columns={accountUserColumns}
                dataSource={accountUser}
              />
            </ProCard>
            <ProCard ghost colSpan={{ md: 24, lg: 6 }} layout="center">
              {accountUser && (
                <Image
                  src={accountUser.avatar}
                  width={120}
                  height={120}
                  style={{ objectFit: 'contain' }}
                />
              )}
            </ProCard>
          </ProCard>
        </ProCard>
        <ProCard title="基本数据">
          <RcResizeObserver
            key="resize-observer"
            onResize={(offset) => {
              if (offset.width >= 1200) {
                setChartSize({
                  width: offset.width,
                  cellWidth: Math.floor((offset.width - 48 - 40) / 3),
                  colSpan: 8,
                });
              } else if (offset.width >= 600) {
                setChartSize({
                  width: offset.width,
                  cellWidth: Math.floor((offset.width - 48 - 40) / 2),
                  colSpan: 12,
                });
              } else {
                setChartSize({
                  width: offset.width,
                  cellWidth: Math.floor(offset.width - 48),
                  colSpan: 24,
                });
              }
            }}
          >
            <Row style={{ paddingRight: 40 }} wrap>
              {accountUser &&
                chartSize &&
                chartDataItems.map((t) => {
                  // @ts-ignore
                  const value = accountUser[t.key as keyof AccountUserDailyStat] || 0;
                  const chartData =
                    (accountUserDailyStats &&
                      accountUserDailyStats.map((item: AccountUserDailyStat) => {
                        return {
                          date: item.date.slice(5, 10),
                          value: item[t.key as keyof AccountUserDailyStat] || 0,
                        };
                      })) ||
                    [];
                  return (
                    <Col key={t.key} span={chartSize.colSpan}>
                      <div
                        key={t.label}
                        style={{ cursor: 'pointer' }}
                        onClick={() => {
                          setOpenChartModal(true);
                          setOpenChartData({
                            title: `${t.label}趋势`,
                            accountId,
                            platform,
                            defaultLabelKey: t.key,
                          });
                        }}
                      >
                        <StatisticCard
                          statistic={{
                            title: (
                              <Space>
                                <span>{t.label}</span>
                                <LineChartOutlined />
                              </Space>
                            ),
                            value: value,
                          }}
                          chart={
                            <div style={{ width: '100%' }}>
                              <LineChart
                                uniqId={`chart-${t.key}-${t.label}`}
                                // @ts-ignore
                                chartData={chartData}
                                width={chartSize.cellWidth}
                                height={chartSize.cellWidth * 0.6}
                              />
                            </div>
                          }
                        />
                      </div>
                    </Col>
                  );
                })}
            </Row>
          </RcResizeObserver>
        </ProCard>

        <ProCard
          title="作品列表"
          extra={
            <DatePicker.RangePicker
              defaultValue={[dayjs().subtract(7, 'days'), dayjs()]}
              onChange={(dates) => {
                if (dates) {
                  setPostsTimeRange([
                    dates[0]?.format('YYYY-MM-DD') || '',
                    dates[1]?.format('YYYY-MM-DD') || '',
                  ]);
                }
              }}
            />
          }
        >
          <ProTable<AccountPostItem>
            scroll={{ x: 'max-content' }}
            columns={postsColumns}
            rowKey="id"
            toolBarRender={false}
            bordered
            size="small"
            dateFormatter="string"
            pagination={{ defaultPageSize: 15 }}
            search={false}
            params={{
              accountId,
              platform,
              startTime: dayjs(postsTimeRange[0]).format('YYYY-MM-DD 00:00:00'),
              endTime: dayjs(postsTimeRange[1]).format('YYYY-MM-DD 23:59:59'),
            }}
            request={(params, sorter) => {
              return proTableRequestAdapterParamsAndData(
                params,
                sorter,
                QueryAccountPostsForPagination,
              );
            }}
          />
        </ProCard>
        <ProCard
          title="直播列表"
          extra={
            <DatePicker.RangePicker
              defaultValue={[dayjs().subtract(7, 'days'), dayjs()]}
              onChange={(dates) => {
                if (dates) {
                  setLivesTimeRange([
                    dates[0]?.format('YYYY-MM-DD') || '',
                    dates[1]?.format('YYYY-MM-DD') || '',
                  ]);
                }
              }}
            />
          }
        >
          <ProTable<AccountLiveItem>
            scroll={{ x: 'max-content' }}
            columns={livesColumns}
            rowKey="id"
            toolBarRender={false}
            bordered
            size="small"
            dateFormatter="string"
            pagination={{ defaultPageSize: 15 }}
            search={false}
            params={{
              accountId,
              platform,
              startTime: dayjs(livesTimeRange[0]).format('YYYY-MM-DD 00:00:00'),
              endTime: dayjs(livesTimeRange[1]).format('YYYY-MM-DD 23:59:59'),
            }}
            request={(params, sorter) => {
              return proTableRequestAdapterParamsAndData(
                params,
                sorter,
                QueryAccountLivesForPagination,
              );
            }}
          />
        </ProCard>
      </ProCard>

      {openChartData && (
        <AccountUserDailyStatsModal
          {...openChartData}
          open={openChartModal}
          setOpen={setOpenChartModal}
          accountId={accountId}
          platform={platform}
        />
      )}
    </PageContainer>
  );
};

export default AccountDetail;
