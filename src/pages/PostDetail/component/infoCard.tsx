import QualityDuring from '@/assets/svg/quality-during.svg';
import QualityEnd from '@/assets/svg/quality-end.svg';
import { PostQualityItem } from '@/services/quality';
import { formatNum } from '@/utils/common';
import { formatSecond } from '@/utils/time';
import { UserOutlined } from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';
import { Avatar } from 'antd';
import styles from './infocard.module.less';
import { AccountPlatformEnum, PlatForm } from '@/utils/platform';

type InfoCardProps = {
  postData: PostQualityItem | null;
  platform?: string;
};

const InfoCard = (props: InfoCardProps) => {
  const { postData, platform } = props;
  const topicArr = postData?.topic ? postData?.topic.split(',') : [];

  return (
    <ProCard bordered className={styles.infoCard}>
      <div className={styles.userInfo}>
        <Avatar
          size={64}
          icon={<UserOutlined />}
          src={postData?.avatar}
          className={styles.avatar}
        />
        <div className={styles.name}>
          <div>{postData?.nickname}</div>
          <div>粉丝总数: {formatNum(postData?.followerCount)}</div>
        </div>
      </div>
      <div className={styles.detail}>
        <div className={styles.platform}>
          平台: {platform && AccountPlatformEnum[platform as unknown as PlatForm]?.text}
        </div>
        <div className={styles.title}>
          作品标题:{' '}
          <span
            dangerouslySetInnerHTML={{
              __html: postData?.title || '',
            }}
          />
        </div>
        <div className={styles.postTime}>作品时长: {formatSecond(postData?.duration)}</div>
        <div className={styles.publishTime}>发布时间: {postData?.publishTime}</div>
        <div>
          质检状态: &nbsp;
          <span className={styles.tag}>
            {postData?.qualityStatus === 0 ? (
              <span>
                <img src={QualityDuring} />
                质检中
              </span>
            ) : (
              <span>
                <img src={QualityEnd} />
                已结束
              </span>
            )}
          </span>
        </div>
        {topicArr.length > 0 && (
          <div className={styles.topic}>
            <div>作品话题: &nbsp;</div>
            {topicArr?.map((item: string) => (
              <div key={`topic-${item}`} className={styles.tag}>
                {item}
              </div>
            ))}
          </div>
        )}
      </div>
    </ProCard>
  );
};

export default InfoCard;
