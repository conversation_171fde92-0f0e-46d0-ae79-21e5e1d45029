import LineChartTrend from '@/components/chart/lineChartTrend';
import StatistCardDark from '@/components/statistCardDark';
import WordCloudComponent from '@/components/wordCloud';
import { QualityTargetType } from '@/services/constants';
import { GetPostTrend, GetPostWordCloud, WordCloudData } from '@/services/quality';
import { formatNum } from '@/utils/common';
import {
  CustomDateRadioButtonDark,
  CustomDateRadioGroup,
  HideScrollBarDiv,
  ProCardDark,
} from '@/utils/commonStyle';
import { disabledDate, getTimes } from '@/utils/time';
import { useParams } from '@umijs/max';
import { useAsyncEffect } from 'ahooks';
import { ConfigProvider, DatePicker, Flex, Radio, RadioChangeEvent, theme } from 'antd';
import { RangePickerProps } from 'antd/es/date-picker';
import dayjs from 'dayjs';
import { useState } from 'react';

import { styled } from 'styled-components';

const options = [
  { label: '播放量', value: 'play' },
  { label: '点赞量', value: 'digg' },
  { label: '评论量', value: 'comment' },
  { label: '分享量', value: 'share' },
  // TODO 获取不到 暂时注释
  // { label: '粉丝增量', value: 'followerGrowth' },
  // { label: '完播率', value: 'completePlayRate' },
];

const dataformOptions = [
  { label: '总量', value: 'total' },
  { label: '增量', value: 'increment' },
];

const { RangePicker } = DatePicker;

type DataSituationProps = {
  postData: any;
};

const CustomRadioGroup = styled(Radio.Group)`
  .ant-radio-button-wrapper:not(:first-child)::before {
    display: none;
  }
  .ant-radio-button-wrapper {
    color: #8f9093;
    background-color: #1b1d22;
    border-color: #3d4350;

    &::before {
      border-color: none;
    }

    &.ant-radio-button-wrapper-checked {
      background-color: #3870ff;
    }

    &.ant-radio-button-wrapper-checked:hover {
      background-color: #3870ff;
    }
  }
`;

const DataSituation = (props: DataSituationProps) => {
  const { postData } = props;
  const { postId, platform } = useParams();
  const [type, setType] = useState('play');
  const [dataform, setDataForm] = useState<'total' | 'increment'>('total');
  const [radioValue, setRadioValue] = useState<number | null>(29); // radio组件的value不能传入数组 只能单独新创建一个state记录
  const [rangeTime, setRangeTime] = useState<string[]>(getTimes(29, 'YYYY-MM-DD HH:mm:ss')); // 近七天
  const [lineChartData, setLineChartData] = useState<any[]>([]);
  const [wordCloudData, setWordCloudData] = useState<WordCloudData[]>([]);

  const onChangeType = ({ target: { value } }: RadioChangeEvent) => {
    setType(value);
  };

  const onChangeFormType = ({ target: { value } }: RadioChangeEvent) => {
    setDataForm(value);
  };

  const onChange = (e: RadioChangeEvent) => {
    const range = getTimes(e.target.value, 'YYYY-MM-DD 00:00:00');
    setRangeTime(range);
    setRadioValue(e.target.value);
  };

  const onChangeRange = (date: RangePickerProps['value'], dateString: string[]) => {
    setRangeTime(dateString.map((item) => item + ' 00:00:00'));
    setRadioValue(null);
  };

  useAsyncEffect(async () => {
    const [startTime, endTime] = rangeTime;
    const res = await GetPostTrend({ postId, startTime, endTime, type, platform });
    const { data } = res;
    setLineChartData(data as any);
  }, [rangeTime, type]);

  useAsyncEffect(async () => {
    const res = await GetPostWordCloud({ postId, platform });
    const wordData = res.data;
    wordData && setWordCloudData(wordData);
  }, []);

  return (
    <HideScrollBarDiv style={{ height: 'calc(100vh - 66px - 56px - 40px)' }}>
      <div
        style={{
          width: '100%',
          height: 'max-content',
          alignItems: 'stretch',
          display: 'flex',
          flexDirection: 'column',
          paddingBottom: '20px',
        }}
      >
        <ProCardDark title="互动数据" ghost bodyStyle={{ display: 'flex' }}>
          <StatistCardDark title="总播放量" value={formatNum(postData?.playCount)} />
          <StatistCardDark title="总点赞量" value={formatNum(postData?.diggCount)} />
          <StatistCardDark title="总评论量" value={formatNum(postData?.commentCount)} />
          <StatistCardDark title="总分享量" value={formatNum(postData?.shareCount)} />
          {/* TODO 获取不到暂时隐藏 */}
          {/* <StatistCardDark
            title="总粉丝增量"
            value={formatNum(postData?.followerGrowth)}
            tip="数据来源于账号扫码授权，未授权时数据不展示"
          />
          <StatistCardDark
            title="完播率"
            value={(postData?.completionPlayRate * 100).toFixed(2) + '%'}
            tip="数据来源于账号扫码授权，未授权时数据不展示"
          /> */}
        </ProCardDark>

        <ProCardDark style={{ marginTop: '20px' }} title="视频热度趋势" ghost>
          <CustomRadioGroup
            options={options}
            onChange={onChangeType}
            value={type}
            optionType="button"
            buttonStyle="solid"
            style={{ marginBottom: 16 }}
          />

          <CustomRadioGroup
            options={dataformOptions}
            onChange={onChangeFormType}
            value={dataform}
            optionType="button"
            buttonStyle="solid"
            style={{ float: 'right', marginRight: 33 }}
          />

          <Flex align="flex-start" style={{ marginBottom: 16 }}>
            <CustomDateRadioGroup value={radioValue} buttonStyle="solid" onChange={onChange}>
              <CustomDateRadioButtonDark value={1}>昨天</CustomDateRadioButtonDark>
              <CustomDateRadioButtonDark value={6}>近7天</CustomDateRadioButtonDark>
              <CustomDateRadioButtonDark value={14}>近15天</CustomDateRadioButtonDark>
              <CustomDateRadioButtonDark value={29}>近30天</CustomDateRadioButtonDark>
            </CustomDateRadioGroup>
            <ConfigProvider
              theme={{
                algorithm: theme.darkAlgorithm,
                token: {
                  colorBgElevated: '#1b1d22',
                  colorBorder: '#3d4350',
                },
              }}
            >
              <RangePicker
                disabledDate={disabledDate}
                onChange={onChangeRange}
                style={{ marginLeft: '10px' }}
                allowClear={false}
                defaultValue={[dayjs(rangeTime[0]), dayjs(rangeTime[1])]}
                value={[dayjs(rangeTime[0]), dayjs(rangeTime[1])]}
              />
            </ConfigProvider>
          </Flex>
          <LineChartTrend
            lineChartData={lineChartData}
            dataform={dataform}
            target={QualityTargetType.POST}
          />
        </ProCardDark>
        <WordCloudComponent wordCloudData={wordCloudData} target={QualityTargetType.POST} />
      </div>
    </HideScrollBarDiv>
  );
};

export default DataSituation;
