import CommentShow from '@/components/commentShow';
import { QualityTargetType } from '@/services/constants';
import { PostQualityItem } from '@/services/quality';
import { ProCard } from '@ant-design/pro-components';
import { ConfigProvider, Empty, Spin, Tabs, theme } from 'antd';
import { lazy, Suspense, useCallback, useMemo } from 'react';
import Player from 'xgplayer';

const XGPlayer = lazy(() => import('@/components/xgplayer'));

type VideoCardProps = {
  postData: PostQualityItem;
  videoPlayerRef: any;
};

const VideoCard = (props: VideoCardProps) => {
  const { postData, videoPlayerRef } = props;

  const onVideoReady = useCallback((player: Player) => {
    videoPlayerRef.current = player;

    const cfg = localStorage.getItem('video-card-preview-volume');
    if (cfg) {
      try {
        const { volume, muted } = JSON.parse(cfg);
        player.volume = volume;
        player.muted = muted;
      } catch (err) {
        console.log(err);
      }
    }
  }, []);

  const videoJsOptions = useMemo(() => {
    const videoWidth = 1080;
    const videoHeight = 1920;
    const maxWidth = 400 - 40 - 20;
    const height = (videoHeight * maxWidth) / videoWidth;
    return {
      width: maxWidth,
      height: height,
      loop: true,
      controls: true,
      autoplay: false,
      playbackRate: [0.5, 1, 1.5, 2],
    };
  }, [postData]);

  const tabItems = [
    {
      label: '视频',
      key: '1',
      children: (
        <>
          {postData?.cleanFlag ? (
            <ConfigProvider theme={{ algorithm: theme.darkAlgorithm }}>
              <Empty
                image={Empty.PRESENTED_IMAGE_SIMPLE}
                description={<span>视频超过存储时长，已清理</span>}
                style={{ paddingTop: '50px' }}
              />
            </ConfigProvider>
          ) : (
            <Suspense fallback={<Spin />}>
              <XGPlayer
                playerOptions={videoJsOptions}
                isLive={false}
                url={postData?.postUrl}
                onPlayerReady={onVideoReady}
              />
            </Suspense>
          )}
        </>
      ),
    },
    {
      label: '评论',
      key: '2',
      children: <CommentShow qualityData={postData} type={QualityTargetType.POST} />,
    },
  ];

  return (
    <ProCard
      bordered
      style={{
        borderRadius: '6px',
        marginTop: '16px',
        backgroundColor: '#1b1d22',
        padding: '10px',
        marginInline: 'auto',
      }}
      bodyStyle={{ overflow: 'hidden' }}
      ghost
    >
      <Tabs items={tabItems} centered={true} indicatorSize={0} type="card" />
    </ProCard>
  );
};

export default VideoCard;
