import { HitExTime } from '@/pages/LiveDetail/component/liveAtom';
import { VideoSubtitleItem } from '@/pages/LiveDetail/component/types';
import { findLocalStorageValue } from '@/utils/quality';
import { atom } from 'jotai';

export const rightSiderActiveKeyAtom = atom('confirmQuality');
export const selectedPostFrameAtom = atom<HitExTime[]>([]);
export const selectedPostSubtitleAtom = atom<VideoSubtitleItem[]>([]);
export const postTabKeyAtom = atom(
  findLocalStorageValue<string>('postDetailRecords', 'tabKey') || 'frame',
);
export const showPostHitStateAtom = atom(
  findLocalStorageValue<boolean>('postDetailRecords', 'showHitState') || false,
);
export const togglePostRightSiderShowAtom = atom(
  findLocalStorageValue<boolean>('postDetailRecords', 'toggleRightSiderShow') ?? true,
);
