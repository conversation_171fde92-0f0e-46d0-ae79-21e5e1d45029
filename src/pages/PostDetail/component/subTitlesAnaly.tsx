import { Timeline } from '@/components/antd/timeline';
import { GetPostSubtitle, LiveQualityItem } from '@/services/quality';
import { convertTimeStr, getDurationFromTimeStr } from '@/utils/time';
import { PlayCircleOutlined } from '@ant-design/icons';
import { EventEmitter } from 'ahooks/lib/useEventEmitter';
import { Button, ConfigProvider, Flex, Input, message, Pagination, Space, Spin } from 'antd';
import dayjs from 'dayjs';
import { useAtom } from 'jotai';
import { debounce, isNull } from 'lodash-es';
import { useCallback, useEffect, useState } from 'react';
import { styled } from 'styled-components';
import Player from 'xgplayer';
import { flashAnimation } from './frameAnaly';
import { VideoSubtitleItem } from '@/pages/LiveDetail/component/types';
import { selectedPostSubtitleAtom } from './postAtom';

type SubTitlesAnalyProps = {
  liveData?: LiveQualityItem;
  videoPlayerRef: React.MutableRefObject<Player | null>;
  targetId?: string;
  platform?: string;
  showHit: boolean;
  postEvent$: EventEmitter<void>;
  tabKey?: string;
};

const { Search } = Input;

const SubTitleDiv = styled.div`
  .highlight {
    color: black;
    background: yellow;
  }
`;

const SubTitleSpan = styled.span<{
  $subtitleIsSelected: boolean;
  $showHit: boolean;
  $exInfo: boolean;
}>`
  text-decoration: ${(props) =>
    props.$exInfo && props.$showHit
      ? 'underline wavy red'
      : props.$subtitleIsSelected
        ? 'underline solid'
        : 'none'};

  cursor: ${(props) => (props.$showHit ? 'pointer' : 'default')};

  :hover {
    text-decoration: underline;
    text-decoration-style: ${(props) => (!props.$showHit ? 'dotted' : 'solid')};
  }

  [data-tooltip] {
    position: relative;
  }

  [data-tooltip]:hover::after {
    position: absolute;
    top: -200%;
    right: 0;
    left: 0;
    width: 50px;
    margin-top: 5px;
    padding: 2px 5px;
    color: white;
    white-space: nowrap;
    text-align: center;
    word-wrap: break-word;
    background-color: #424242;
    border-radius: 5px;
    box-shadow:
      0 6px 16px 0 rgba(0, 0, 0, 0.08),
      0 3px 6px -4px rgba(0, 0, 0, 0.12),
      0 9px 28px 8px rgba(0, 0, 0, 0.05);
    transform: translateX(-25%);
    content: attr(data-tooltip);
  }

  &.flash-animation {
    background-color: #6c8fdd;
    animation: ${flashAnimation} 2s 2;
  }
  &.flash-animation .mark-highlight {
    color: black;
    background: yellow;
  }
`;

const SubTitlesAnaly = (props: SubTitlesAnalyProps) => {
  const { videoPlayerRef, targetId, platform, showHit, postEvent$, tabKey } = props;
  const [subtitles, setSubtitles] = useState<VideoSubtitleItem[]>([]);
  const [page, setPage] = useState(1);
  const [searchValue, setSearchValue] = useState<string>();
  const [loading, setLoading] = useState<boolean>(true);
  const [selectSubtitles, setSelectSubtitles] = useAtom(selectedPostSubtitleAtom);

  postEvent$.useSubscription(() => {
    debounceFetchSubtitle(page);
  });

  const fetchSubtitle = async (page: number, searchValue?: string) => {
    const newSubtitleItems: VideoSubtitleItem[] = [];
    let allContent: string | undefined = '';
    setLoading(true);
    const res = await GetPostSubtitle({ postId: targetId, platform, content: searchValue });
    setLoading(false);
    allContent = res.data?.subtitle;
    const id = res.data?.id;
    const isHitQuality = !isNull(res.data?.exInfo);
    if (allContent) {
      allContent?.split('\n').forEach((subtitleRaw) => {
        let content = subtitleRaw;
        let startTime = 0;
        let endTime = 0;
        const splitIdx = subtitleRaw.indexOf('] ');
        if (splitIdx !== -1) {
          content = subtitleRaw.substring(splitIdx + 2);
          if (searchValue) {
            const searchRegex = new RegExp(searchValue, 'gi');
            content = content.replace(searchRegex, '<span class="highlight">$&</span>');
          }
          const timeStr = subtitleRaw.substring(1, splitIdx);
          const timeStrs = timeStr.split(',');
          startTime = getDurationFromTimeStr(timeStrs[0]);
          endTime = getDurationFromTimeStr(timeStrs[1]);
        }
        // 为空不插入
        if (content.trim()) {
          newSubtitleItems.push({
            startTime,
            endTime,
            content,
            id,
            isHitQuality,
          });
        }
      });
      if (newSubtitleItems.length > 1 && !newSubtitleItems.at(-1)?.content) {
        newSubtitleItems.pop();
      }
      setSubtitles(newSubtitleItems);
    } else {
      setSubtitles([]);
    }
  };

  const debounceFetchSubtitle = useCallback(
    debounce((page: number, searchValue?: string) => {
      fetchSubtitle(page, searchValue);
    }, 700),
    [],
  );

  useEffect(() => {
    debounceFetchSubtitle(page, searchValue);
  }, [page, searchValue]);

  const onChangePagination = (page: number) => {
    setPage(page);
  };

  const handleExportSubTitles = () => {
    if (subtitles.length === 0) {
      message.warning('暂无口播字幕');
      return;
    }
    const headers = ['开始时间', '结束时间', '内容'];
    const rows = subtitles.map((subtitle) => [
      // 兼容作品的字幕没有修改
      convertTimeStr(subtitle.startTime, true),
      convertTimeStr(subtitle.endTime, true),
      subtitle.content,
    ]);

    let csvContent = headers.join(',') + '\n';
    rows.forEach((row) => {
      csvContent += row.join(',') + '\n';
    });
    const data = new Blob([new Uint8Array([0xef, 0xbb, 0xbf]), csvContent], {
      type: 'text/csv;charset=utf-8;',
    });

    const link = document.createElement('a');
    link.href = URL.createObjectURL(data);
    link.download = `口播字幕 ${dayjs().format('YYYY-MM-DD')}.csv`;
    link.click();
  };

  return (
    <>
      <Flex justify="space-between" gap={10}>
        <Search
          placeholder="输入关键词过滤"
          onChange={(e) => setSearchValue(e.target.value)}
          value={searchValue}
          allowClear
          style={{ flex: 1, maxWidth: '95%' }}
        />
        <Button onClick={handleExportSubTitles}>导出</Button>
      </Flex>

      <div style={{ marginTop: '20px', height: 'calc(100vh - 56px - 300px)' }}>
        {loading && page === 1 ? (
          <Spin
            style={{
              width: '100%',
              height: '300px',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
            }}
          />
        ) : (
          <Timeline
            items={subtitles.map((subtitleItem, idx) => {
              const timeLabel = `${convertTimeStr(subtitleItem.startTime, true)} - ${convertTimeStr(
                subtitleItem.endTime,
                true,
              )}`;
              const isSelect = selectSubtitles.some(
                (selectedItem) => selectedItem.id === subtitleItem.id,
              );
              return {
                children: (
                  <SubTitleDiv key={`timeLineItem-${idx}`}>
                    <div>
                      <Space>
                        <span style={{ color: '#8c8e91' }}>{timeLabel}</span>
                        <Button
                          type="link"
                          size="small"
                          icon={<PlayCircleOutlined />}
                          onClick={() => {
                            if (videoPlayerRef.current) {
                              videoPlayerRef.current.currentTime = subtitleItem.startTime - 1;
                              videoPlayerRef.current.play();
                            }
                          }}
                        />
                      </Space>
                    </div>
                    <SubTitleSpan
                      $showHit={showHit || isSelect}
                      $subtitleIsSelected={isSelect}
                      $exInfo={subtitleItem.isHitQuality}
                      dangerouslySetInnerHTML={{
                        __html: subtitleItem.content || subtitleItem.subtitle || '',
                      }}
                      id={`subtitle-${tabKey}-${subtitleItem.startTime}`}
                      onClick={() => {
                        if (!showHit) return;
                        if (isSelect) {
                          setSelectSubtitles(
                            selectSubtitles.filter(
                              (selectedItem) => selectedItem.id !== subtitleItem.id,
                            ),
                          );
                        } else {
                          setSelectSubtitles([
                            ...selectSubtitles,
                            {
                              ...subtitleItem,
                            },
                          ]);
                        }
                      }}
                    />
                  </SubTitleDiv>
                ),
              };
            })}
          />
        )}

        <ConfigProvider
          theme={{
            components: {
              Pagination: {
                colorText: '#fff',
              },
            },
          }}
        >
          <Pagination
            style={{ float: 'right' }}
            defaultCurrent={1}
            current={page}
            defaultPageSize={300}
            hideOnSinglePage={true}
            onChange={onChangePagination}
            showSizeChanger={false}
          />
        </ConfigProvider>
      </div>
    </>
  );
};

export default SubTitlesAnaly;
