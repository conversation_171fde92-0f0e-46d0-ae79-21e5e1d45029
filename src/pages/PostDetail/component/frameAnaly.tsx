import { QualityTargetType } from '@/services/constants';
import {
  GetQualityExList,
  GetQualitySprite,
  LiveQualityItem,
  spriteInfoListItem,
} from '@/services/quality';
import { HideScrollBarDiv } from '@/utils/commonStyle';
import { InfoCircleOutlined } from '@ant-design/icons';
import { useQuery } from '@umijs/max';
import { useAsyncEffect } from 'ahooks';
import { EventEmitter } from 'ahooks/lib/useEventEmitter';
import { useAtom } from 'jotai';
import { useEffect, useState } from 'react';
import { keyframes, styled } from 'styled-components';
import Player from 'xgplayer';
import { HitExTime } from '@/pages/LiveDetail/component/liveAtom';
import { selectedPostFrameAtom } from './postAtom';

type FrameInfo = {
  width: number;
  height: number;
  frameNum: number;
};

type FrameAnalyProps = {
  liveData?: LiveQualityItem;
  videoPlayerRef: React.MutableRefObject<Player | null>;
  showHit: boolean;
  targetId?: string;
  platform?: string;
  targetType: QualityTargetType;
  isLive?: boolean;
  isInQuality?: boolean;
  postEvent$?: EventEmitter<void>;
  tabKey?: string;
};

type NewFrame = {
  startTime: number;
  endTime: number;
  frameUrl: string;
  frameKey: string;
  frameIdx: number;
  isHit?: boolean;
  targetUrl?: string;
  groupId?: string;
  monitoringWordName?: string;
};

export const flashAnimation = keyframes`
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
`;

const FrameDiv = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  background-repeat: no-repeat;
  border: solid 1px rgba(33, 33, 33, 0.6);
  background: #51241c;
  margin-block-start: 5px;

  &.flash-animation {
    background-color: #6c8fdd;
    animation: ${flashAnimation} 2s 2;
  }
`;

const FrameAnaly = (props: FrameAnalyProps) => {
  const { videoPlayerRef, showHit, targetId, platform, targetType, postEvent$, tabKey } = props;
  const [spriteInfoList, setSpriteInfoList] = useState<spriteInfoListItem[]>([]);
  const [frameInfo, setFrameInfo] = useState<FrameInfo>({
    width: 100,
    height: 180,
    frameNum: 10,
  });
  const [newFrame, setNewFrame] = useState<NewFrame[]>();
  const [hitExTimeState, setHitExTimeState] = useState<HitExTime[]>();
  const [selectedTimeIntervals, setSelectedTimeIntervals] = useAtom(selectedPostFrameAtom);

  const { data: qualityData, refetch } = useQuery({
    queryFn: () => GetQualityExList({ targetId, targetType, platform }),
    queryKey: ['qualityDetail', { targetId, targetType, platform }],
  });

  // 作品页暂时简单做 数据量不大 无需走乐观更新
  postEvent$?.useSubscription(() => {
    refetch();
  });

  useEffect(() => {
    if (qualityData) {
      const qualityExData = qualityData.data;
      const hitExTime = qualityExData
        ?.map((item) => {
          if (item.startTime && item.endTime) {
            return {
              startTime: item.startTime,
              endTime: item.endTime,
              groupId: item.groupId,
              monitoringWordName: item.monitoringWordName,
              picUrl: item.picUrl,
            };
          }
        })
        .filter((item) => item !== undefined);
      // @ts-ignore
      setHitExTimeState(hitExTime as HitExTime[]);
    }
  }, [qualityData]);

  useAsyncEffect(async () => {
    // 修改为只有作品用这个接口 获取缩略雪碧图
    const res = await GetQualitySprite({
      targetId,
      targetType,
      platform,
    });
    if (res.data) {
      if (
        res.data.frameInfo &&
        +res.data.frameInfo.frameNum &&
        +res.data.frameInfo.width &&
        +res.data.frameInfo.height
      ) {
        setFrameInfo({
          width: +res.data.frameInfo.width,
          height: +res.data.frameInfo.height,
          frameNum: +res.data.frameInfo.frameNum,
        });
        setSpriteInfoList(res.data.spriteInfoList);
      }
    }
  }, []);

  useEffect(() => {
    if (frameInfo) {
      const newFrameItems: NewFrame[] = [];
      const frameNum = frameInfo.frameNum;
      spriteInfoList.map((item) => {
        const { spriteUrls, targetUrl } = item;
        spriteUrls.forEach((frameUrl, idx) => {
          new Array(100).fill(0).forEach((_, idx2) => {
            if (idx * 100 + idx2 >= frameNum) {
              return;
            }
            const intervalTime = 5;
            const startTime = intervalTime * (idx * 100 + idx2);
            const endTime = startTime + intervalTime;

            const isInRange = (item: HitExTime) => {
              const picUrlLen = item.picUrl?.length || 0;
              const startTimeArr = [];
              for (let i = 0; i < picUrlLen; i++) {
                const startTime = Number(item.startTime) + i * 5;
                startTimeArr.push(startTime);
              }
              if (startTimeArr.includes(startTime)) {
                return true;
              } else {
                return false;
              }
            };
            const isHit = hitExTimeState?.some(isInRange);
            const groupId = hitExTimeState?.find(isInRange)?.groupId;
            const monitoringWordName = hitExTimeState?.find(isInRange)?.monitoringWordName;
            newFrameItems.push({
              startTime,
              endTime,
              frameUrl,
              frameIdx: idx2,
              frameKey: `frame_${idx}_${idx2}`,
              isHit,
              targetUrl,
              groupId,
              monitoringWordName,
            });
          });
        });
      });
      setNewFrame(newFrameItems);
    }
  }, [frameInfo, spriteInfoList, hitExTimeState]);

  const postFrameClick = (item: NewFrame, index: number) => {
    if (showHit) {
      const isSelected = selectedTimeIntervals.some(
        (time) => item.startTime === time.startTime && item.endTime === time.endTime,
      );
      if (isSelected) {
        const updatedIntervals = selectedTimeIntervals.filter(
          (time) => item.startTime !== time.startTime || item.endTime !== time.endTime,
        );
        setSelectedTimeIntervals(updatedIntervals);
      } else {
        setSelectedTimeIntervals([
          ...selectedTimeIntervals,
          {
            startTime: item.startTime,
            endTime: item.endTime,
            targetUrl: item.targetUrl,
            index,
          },
        ]);
      }
    } else {
      if (videoPlayerRef.current) {
        videoPlayerRef.current.currentTime = item.startTime;
      }
    }
  };

  return (
    <div
      style={{
        width: '100%',
        height: 'max-content',
      }}
    >
      <div style={{ color: '#d1d2d3', margin: '10px 0px' }}>
        <InfoCircleOutlined /> 打开【开启人工标记】按钮，可进行人工质检标记
      </div>
      <HideScrollBarDiv
        id="scrollableDiv"
        style={{
          display: 'flex',
          width: '100%',
          flexDirection: 'row',
          flexWrap: 'wrap',
          height: 'calc(100vh - 56px - 300px)',
          alignContent: 'flex-start',
        }}
      >
        {newFrame &&
          newFrame.map((item, index) => {
            const isSelected = selectedTimeIntervals.some(
              (time) => item.startTime === time.startTime && item.endTime === time.endTime,
            );
            return (
              <FrameDiv
                key={item.frameKey}
                style={{
                  objectFit: 'cover',
                  position: 'relative',
                  width: frameInfo.width / 2,
                  height: frameInfo.height / 2,
                  backgroundImage: `url(${item.frameUrl})`,
                  backgroundPositionX: (-frameInfo.width / 2) * (item.frameIdx % 10),
                  backgroundPositionY: (-frameInfo.height / 2) * Math.floor(item.frameIdx / 10),
                  backgroundSize: `${frameInfo.width * 5}px ${frameInfo.height * 5}px`,
                  ...(showHit &&
                    (item.isHit || isSelected) && {
                      backgroundBlendMode: 'screen',
                    }),
                }}
                id={`frame-${tabKey}-${item.startTime}`}
                onClick={() => postFrameClick(item, index)}
              >
                {showHit && (
                  <span
                    style={{
                      position: 'absolute',
                      backgroundColor: '272C36',
                      color: '#FFFFFF',
                      whiteSpace: 'nowrap',
                      fontSize: '10px',
                      bottom: 0,
                      width: '100%',
                      textAlign: 'center',
                      overflow: 'hidden',
                      background: '#272c36',
                    }}
                  >
                    {item?.monitoringWordName}
                  </span>
                )}
              </FrameDiv>
            );
          })}
      </HideScrollBarDiv>
    </div>
  );
};

export default FrameAnaly;
