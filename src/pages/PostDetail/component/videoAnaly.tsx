import { SubmitQualityCard } from '@/components/submitQualityCard';
import QualityDetail from '@/pages/LiveDetail/component/qualityDetail';
import { ButtonTabs } from '@/pages/ProjectHome/style';
import { QualityTargetType } from '@/services/constants';
import { QualityMonitorWordList } from '@/services/quality';
import { HideScrollBarDiv, ProCardDarkGhost, ShowRightSiderBtn } from '@/utils/commonStyle';
import { DoubleLeftOutlined, DoubleRightOutlined } from '@ant-design/icons';
import { useParams } from '@umijs/max';
import { useEventEmitter, useRequest } from 'ahooks';
import { Flex, Switch, Tabs, TabsProps } from 'antd';
import { useAtom } from 'jotai';
import { isUndefined } from 'lodash-es';
import { useEffect, useRef, useState } from 'react';
import { styled } from 'styled-components';
import Player from 'xgplayer';
import FrameAnaly from './frameAnaly';
import SubTitlesAnaly from './subTitlesAnaly';
import {
  showPostHitStateAtom,
  togglePostRightSiderShowAtom,
  selectedPostFrameAtom,
  rightSiderActiveKeyAtom,
  selectedPostSubtitleAtom,
  postTabKeyAtom,
} from './postAtom';

type VideoAnalyProps = {
  videoPlayerRef: React.MutableRefObject<Player | null>;
  targetId?: string;
  projectId?: string;
  platform?: string;
  monitoringWordType: QualityTargetType;
  isInQuality?: boolean;
};

const TabsNoMarginBottom = styled(Tabs)`
  .ant-tabs-nav {
    margin-bottom: 0;
  }
`;

const VideoAnaly = (props: VideoAnalyProps) => {
  const { postId } = useParams();
  const { videoPlayerRef, targetId, platform, monitoringWordType, isInQuality, projectId } = props;
  const [showHitState, setShowHitState] = useAtom(showPostHitStateAtom);
  const [rightSiderWidth, setRightSiderWidth] = useState<number | undefined>(undefined);
  const rightSiderRef = useRef<HTMLDivElement>(null);
  const [toggleRightSiderShow, setToggleRightSiderShow] = useAtom(togglePostRightSiderShowAtom);
  const [rightSiderActiveKey, setRightSiderActiveKey] = useAtom(rightSiderActiveKeyAtom);
  const [btnTop, setBtnTop] = useState<number>(80);
  const [isDragging, setIsDragging] = useState(false);
  const startYRef = useRef(0);
  // 是否移动了按钮
  const [isMove, setIsMove] = useState(false);
  const [selectedPostFrame, setSelectedPostFrame] = useAtom(selectedPostFrameAtom);
  const [selectedPostSubtitle, setSelectedPostSubtitle] = useAtom(selectedPostSubtitleAtom);
  const [tabKey, setTabKey] = useAtom(postTabKeyAtom);

  // 1-文字标识 2-行为标识 3-弹幕标识
  const { data: subTitleMonitorData } = useRequest(
    () => QualityMonitorWordList({ projectId, sceneType: 1 }),
    {
      ready: !isUndefined(projectId),
    },
  );
  const { data: frameMonitorData } = useRequest(
    () => QualityMonitorWordList({ projectId, sceneType: 2 }),
    {
      ready: !isUndefined(projectId),
    },
  );

  const postEvent$ = useEventEmitter();

  const tabItems = [
    {
      label: '画面',
      key: 'frame',
      children: (
        <div style={{ padding: '0px 20px 10px' }}>
          <FrameAnaly
            videoPlayerRef={videoPlayerRef}
            showHit={showHitState}
            targetId={targetId}
            platform={platform}
            targetType={monitoringWordType}
            isInQuality={isInQuality}
            postEvent$={postEvent$}
            tabKey="frame"
          />
        </div>
      ),
    },
    {
      label: '口播字幕',
      key: 'subtitles',
      children: (
        <div style={{ padding: '0px 20px 10px' }}>
          <SubTitlesAnaly
            videoPlayerRef={videoPlayerRef}
            showHit={showHitState}
            targetId={targetId}
            platform={platform}
            postEvent$={postEvent$}
            tabKey="subtitles"
          />
        </div>
      ),
    },
  ];

  const rightSiderItems: TabsProps['items'] = [
    {
      key: 'confirmQuality',
      label: '违规内容',
      children: (
        <QualityDetail
          targetId={postId}
          targetType={QualityTargetType.POST}
          postEvent$={postEvent$}
          tabKey={tabKey}
        />
      ),
    },
  ];

  const handleMouseDown = (e: any) => {
    setIsDragging(true);
    startYRef.current = e.clientY - btnTop; // 记录鼠标相对于元素顶部的偏移量
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const handleMouseMove = (e: any) => {
    if (isDragging) {
      const newTop = e.clientY - startYRef.current;
      setBtnTop(newTop);
      setIsMove(true);
    }
  };

  useEffect(() => {
    const transitionDom = rightSiderRef.current;
    transitionDom?.addEventListener('transitionend', () => {
      setRightSiderWidth(transitionDom.offsetWidth);
    });
  }, [toggleRightSiderShow]);

  return (
    <Flex gap={10} style={{ height: 'calc(100vh - 66px - 56px - 40px - 50px)' }}>
      <HideScrollBarDiv style={{ height: '100%' }}>
        <div
          style={{
            width: '100%',
            alignItems: 'stretch',
            display: 'flex',
            flexDirection: 'column',
            position: 'relative',
          }}
        >
          <ProCardDarkGhost ghost>
            <ButtonTabs
              type="card"
              tabBarExtraContent={
                <Switch
                  checkedChildren="手动标记"
                  unCheckedChildren="手动标记"
                  checked={showHitState}
                  onChange={(checked) => setShowHitState(checked)}
                />
              }
              items={tabItems}
              activeKey={tabKey}
              onChange={(activeKey: string) => {
                setTabKey(activeKey);
                // 切换tab把之前的都清空
                setSelectedPostFrame([]);
                setSelectedPostSubtitle([]);
              }}
            />
          </ProCardDarkGhost>
          {showHitState && selectedPostFrame.length > 0 && (
            <SubmitQualityCard
              triggerType={'frame'}
              data={frameMonitorData?.data}
              style={{
                position: 'absolute',
                bottom: '5%',
                left: '50%',
                transform: 'translateX(-50%)',
              }}
              targetType={monitoringWordType}
              platform={platform}
              targetId={targetId}
              selectItems={selectedPostFrame}
              setSelectItems={setSelectedPostFrame}
            />
          )}
          {showHitState && selectedPostSubtitle.length > 0 && (
            <SubmitQualityCard
              triggerType={'subtitle'}
              data={subTitleMonitorData?.data}
              style={{
                position: 'absolute',
                bottom: '5%',
                left: '50%',
                transform: 'translateX(-50%)',
              }}
              targetType={monitoringWordType}
              platform={platform}
              targetId={targetId}
              selectItems={selectedPostSubtitle}
              setSelectItems={setSelectedPostSubtitle}
              postEvent$={postEvent$}
            />
          )}
        </div>
      </HideScrollBarDiv>
      {/* 通过width 0 来隐藏 方便展示收缩展开的动画 */}
      <HideScrollBarDiv
        style={{
          backgroundColor: '#1b1d22',
          width: toggleRightSiderShow ? '400px' : '0px',
          height: '100%',
          transition: 'width 0.3s ease',
        }}
        ref={rightSiderRef}
      >
        <TabsNoMarginBottom
          activeKey={rightSiderActiveKey}
          onChange={(key) => {
            setRightSiderActiveKey(key);
          }}
          items={rightSiderItems}
          tabBarStyle={{
            position: 'sticky',
            top: 0,
            zIndex: 999,
          }}
          tabBarExtraContent={
            <span
              style={{ fontSize: '14px', color: '#fff' }}
              onClick={() => setToggleRightSiderShow(false)}
            >
              收起 <DoubleRightOutlined />
            </span>
          }
        />
      </HideScrollBarDiv>
      {rightSiderWidth === 0 && (
        <ShowRightSiderBtn
          $top={btnTop}
          onMouseDown={handleMouseDown}
          onMouseUp={handleMouseUp}
          onMouseMove={handleMouseMove}
          onClick={() => {
            if (!isMove) {
              setToggleRightSiderShow(true);
            } else {
              setIsMove(false);
            }
          }}
        >
          <div>
            <DoubleLeftOutlined />
            展开
          </div>
        </ShowRightSiderBtn>
      )}
    </Flex>
  );
};

export default VideoAnaly;
