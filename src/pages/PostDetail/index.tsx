import CommonHeader from '@/components/header';
import useBeforeUnloadRecords from '@/hooks/useBeforeUnloadRecord';
import { QualityTargetType } from '@/services/constants';
import { GetPostQuality, PostQualityItem } from '@/services/quality';
import { CustomHeaderTabsDark, ScrollSider } from '@/utils/commonStyle';
import { findLocalStorageValue } from '@/utils/quality';
import { useParams } from '@umijs/max';
import { useAsyncEffect, useTitle } from 'ahooks';
import { App, Breadcrumb, ConfigProvider, Flex, TabsProps, theme } from 'antd';
import { useAtomValue } from 'jotai';
import { useRef, useState } from 'react';
import Player from 'xgplayer';
import DataSituation from './component/dataSituation';
import InfoCard from './component/infoCard';
import VideoAnaly from './component/videoAnaly';
import VideoCard from './component/videoCard';
import {
  postTabKey<PERSON>tom,
  rightSiderActiveKeyAtom,
  showPostHitStateAtom,
  togglePostRightSiderShowAtom,
} from './component/postAtom';

const PostDetail = () => {
  useTitle('质检详情');
  const { postId, platform } = useParams();
  const [postData, setPostData] = useState<PostQualityItem | null>(null);
  const videoPlayerRef = useRef<Player | null>(null);
  const [headerActiveKey, setHeaderActiveKey] = useState<string>(
    findLocalStorageValue<string>('postDetailRecords', 'headerActiveKey') || 'videoAnaly',
  );
  const rightSiderActiveKey = useAtomValue(rightSiderActiveKeyAtom);
  const tabKey = useAtomValue(postTabKeyAtom);
  const showPostHitState = useAtomValue(showPostHitStateAtom);
  const toggleRightSiderShow = useAtomValue(togglePostRightSiderShowAtom);

  useBeforeUnloadRecords('postDetailRecords', {
    rightSiderActiveKey,
    headerActiveKey,
    tabKey,
    showHitState: showPostHitState,
    toggleRightSiderShow,
  });

  const items: TabsProps['items'] = [
    {
      label: '数据情况',
      key: 'dataSituation',
      children: <DataSituation postData={postData} />,
    },
    {
      label: '视频分析',
      key: 'videoAnaly',
      children: (
        <VideoAnaly
          videoPlayerRef={videoPlayerRef}
          targetId={postId}
          projectId={postData?.projectId}
          platform={platform}
          monitoringWordType={QualityTargetType.POST}
        />
      ),
    },
  ];

  useAsyncEffect(async () => {
    const res = await GetPostQuality({ postId, platform });
    const postInfoData = res.data || null;
    setPostData(postInfoData);
  }, []);

  return (
    <ConfigProvider theme={{ algorithm: theme.darkAlgorithm }}>
      <App
        style={{
          height: '100%',
        }}
      >
        <Flex vertical style={{ height: '100%' }}>
          <CommonHeader />
          <Breadcrumb
            style={{
              background: '#06070b',
              padding: '10px 20px',
            }}
            items={[
              {
                title: '质量管理',
              },
              {
                title: '作品质检',
              },
              {
                title: '作品监控详情',
              },
            ]}
          />
          <div
            style={{
              background: '#06070b',
              flex: 1,
              overflow: 'hidden',
              display: 'flex',
              flexDirection: 'row',
              overflowX: 'auto',
            }}
          >
            <ScrollSider>
              <div
                style={{
                  width: '100%',
                  height: 'max-content',
                  paddingInline: '20px',
                  alignItems: 'center',
                  display: 'flex',
                  flexDirection: 'column',
                }}
              >
                <InfoCard postData={postData} platform={platform} />
                {postData && <VideoCard postData={postData} videoPlayerRef={videoPlayerRef} />}
              </div>
            </ScrollSider>

            <div
              style={{
                paddingRight: '20px',
                height: '100%',
                flex: 1,
                overflow: 'hidden',
                minWidth: '700px',
              }}
            >
              <CustomHeaderTabsDark
                activeKey={headerActiveKey}
                onChange={(activeKey: string) => {
                  setHeaderActiveKey(activeKey);
                }}
                items={items}
              />
            </div>
          </div>
        </Flex>
      </App>
    </ConfigProvider>
  );
};

export default PostDetail;
