import { RoleCode } from '@/access';
import { GetCurrentUser } from '@/services/auth';
import { AODICLUE, LEDAO, WEILAI, XIAOPENG } from '@/utils/constant';
import { useRequest } from '@umijs/max';
import { GetProp, Result, Select, SelectProps, Spin } from 'antd';
import { useEffect, useState } from 'react';
import AODITable from './AODI/aodiTable';
import WeiLaiTable from './WeiLai/weiLaiTable';
import XiaoPengTable from './XiaoPeng/xiaoPengTable';
import LeDaoTable from './LeDao/leDaoTable';

type SelectOptionType = GetProp<SelectProps, 'options'>[number];

type ClueValue = 'weilai' | 'xiaopeng' | 'aodi-clue' | 'ledao';

type ClueOption = {
  value: ClueValue;
  label: string;
  projectId: string;
};

const CLUE_OPTIONS: ClueOption[] = [
  { value: 'weilai', label: '蔚来线索', projectId: WEILAI },
  { value: 'xiaopeng', label: '小鹏线索', projectId: XIAOPENG },
  { value: 'aodi-clue', label: '奥迪短视频专项', projectId: AODICLUE },
  { value: 'ledao', label: '乐道线索', projectId: LEDAO },
];

const ClueMapping = () => {
  const [clueValue, setClueValue] = useState<ClueValue>();
  const [options, setOptions] = useState<SelectOptionType[]>([]);

  const { loading } = useRequest(() => GetCurrentUser(), {
    onSuccess: (res) => {
      const hasPermissionList = res?.projectIds || [];
      const isSuperAdmin = res?.roleCodes.includes(RoleCode.SUPER_ADMIN);

      const availableOptions = CLUE_OPTIONS.filter(
        (option) => isSuperAdmin || hasPermissionList.includes(option.projectId),
      ).map(({ value, label }) => ({ value, label }));

      setOptions(availableOptions);
    },
  });

  useEffect(() => {
    if (options.length > 0) {
      setClueValue(options[0].value as ClueValue);
    }
  }, [options]);

  if (loading) {
    return <Spin className="flex h-[300px] w-full items-center justify-center" />;
  }

  if (options.length === 0) {
    return <Result status="error" title="请联系管理员申请项目权限" />;
  }

  const clueComponents: Record<ClueValue, React.ReactNode> = {
    weilai: <WeiLaiTable />,
    xiaopeng: <XiaoPengTable />,
    'aodi-clue': <AODITable />,
    ledao: <LeDaoTable />,
  };

  return (
    <div>
      <Select<ClueValue>
        value={clueValue}
        className="mb-5 w-[150px]"
        onChange={setClueValue}
        options={options}
      />
      {clueValue && clueComponents[clueValue]}
    </div>
  );
};

export default ClueMapping;
