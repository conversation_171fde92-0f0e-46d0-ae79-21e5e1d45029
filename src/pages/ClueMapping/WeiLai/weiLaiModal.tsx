import { WeiLaiClueCreate, WeiLaiClueUpdate, WeiLaiFormValues } from '@/services/clue-mapping';
import { ActionType, ModalForm, ProFormRadio, ProFormText } from '@ant-design/pro-components';
import { Button, message } from 'antd';

type WeiLaiModalProps = {
  text: string;
  actionRef: React.MutableRefObject<ActionType | undefined>;
  initValue?: Partial<WeiLaiFormValues>;
  trigger?: JSX.Element;
  id?: number;
};

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 18 },
};

const WeiLaiModal = (props: WeiLaiModalProps) => {
  const { text, initValue, trigger, id, actionRef } = props;

  return (
    <ModalForm<WeiLaiFormValues>
      title={text}
      autoFocusFirstInput
      layout={'horizontal'}
      {...formItemLayout}
      width={600}
      modalProps={{
        destroyOnHidden: true,
        centered: true,
      }}
      trigger={trigger || <Button type="primary">{text}</Button>}
      onFinish={async (values) => {
        let result;
        if (id) {
          result = await WeiLaiClueUpdate({ id, ...values });
        } else {
          result = await WeiLaiClueCreate(values);
        }
        result.code === 0 ? message.success('提交成功') : message.error('提交失败');
        actionRef.current?.reload();
        return true;
      }}
    >
      <ProFormText
        width="md"
        name="referDyId"
        label="抖音号"
        placeholder="抖音号"
        initialValue={initValue?.referDyId}
        rules={[{ required: true }]}
      />
      <ProFormText
        width="md"
        name="advertiserId"
        label="广告 ID"
        placeholder="请输入广告 ID"
        initialValue={initValue?.advertiserId}
        rules={[{ required: true }]}
      />
      <ProFormRadio.Group
        name="accountType"
        label="账号类型"
        initialValue={initValue?.accountType}
        rules={[{ required: true, message: '请选择账号类型' }]}
        options={[
          { label: '官号', value: 1 },
          { label: '区域号', value: 2 },
          { label: '门店号', value: 3 },
          { label: '员工号', value: 4 },
        ]}
      />
      <ProFormRadio.Group
        name="advertiserType"
        label="广告 ID 类型"
        initialValue={initValue?.advertiserType}
        rules={[{ required: true, message: '请选择广告 ID 类型' }]}
        options={[
          { label: '短视频', value: 1 },
          { label: '直播', value: 2 },
        ]}
      />
      <ProFormText
        width="md"
        name="srcInterface"
        label="活动编码"
        initialValue={initValue?.srcInterface}
        placeholder="请输入活动编码"
        rules={[{ required: true }]}
      />
      <ProFormText
        width="md"
        name="sourceCommerceKey"
        label="来源门店ID"
        placeholder="来源门店ID"
        initialValue={initValue?.sourceCommerceKey}
        dependencies={['accountType']}
        rules={[
          ({ getFieldValue }) => {
            const accountType = getFieldValue('accountType');
            return {
              required: accountType === 2 || accountType === 3, // 区域号和门店号需要必填
              message: '来源门店ID为必填字段',
            };
          },
        ]}
      />
      <ProFormText
        width="md"
        name="empDomain"
        label="员工域账号"
        initialValue={initValue?.empDomain}
        placeholder="请输入员工域账号"
      />
      <ProFormText
        width="md"
        name="koeEmpDomain"
        label="推荐员工域账号"
        initialValue={initValue?.koeEmpDomain}
        placeholder="请输入推荐员工域账号"
      />
    </ModalForm>
  );
};

export default WeiLaiModal;
