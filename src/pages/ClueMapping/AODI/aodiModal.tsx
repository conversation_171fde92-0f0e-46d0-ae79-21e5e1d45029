import { AODIClueCreate, AODIClueUpdate, AODIFormValues } from '@/services/clue-mapping';
import { ActionType, ModalForm, ProFormSelect, ProFormText } from '@ant-design/pro-components';
import { Button, message } from 'antd';

type AODIModalProps = {
  text: string;
  actionRef: React.MutableRefObject<ActionType | undefined>;
  initValue?: Partial<AODIFormValues>;
  trigger?: JSX.Element;
  id?: number;
};

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 18 },
};

const AODIModal = (props: AODIModalProps) => {
  const { text, initValue, trigger, id, actionRef } = props;

  return (
    <ModalForm<AODIFormValues>
      title={text}
      autoFocusFirstInput
      layout={'horizontal'}
      {...formItemLayout}
      width={600}
      modalProps={{
        destroyOnHidden: true,
        centered: true,
      }}
      trigger={trigger || <Button type="primary">{text}</Button>}
      onFinish={async (values) => {
        let result;
        if (id) {
          result = await AODIClueUpdate({ id, ...values });
        } else {
          result = await AODIClueCreate(values);
        }
        result.code === 0 ? message.success('提交成功') : message.error('提交失败');
        actionRef.current?.reload();
        return true;
      }}
    >
      <ProFormText
        width="md"
        name="referDyName"
        label="来源抖音昵称"
        placeholder="请输入抖音昵称"
        initialValue={initValue?.referDyName}
      />
      <ProFormText
        width="md"
        name="referDyId"
        label="来源抖音号"
        placeholder="请输入抖音号"
        initialValue={initValue?.referDyId}
        rules={[{ required: true }]}
      />
      <ProFormSelect
        width="md"
        name="sourceType"
        label="客户来源"
        placeholder="请选择客户来源"
        valueEnum={{
          1: '抖音本地通',
          2: '抖音',
          3: '奥迪官号',
          4: '南区矩阵',
          5: '西区矩阵',
        }}
        initialValue={initValue?.sourceType?.toString()}
        rules={[{ required: true }]}
      />
    </ModalForm>
  );
};

export default AODIModal;
