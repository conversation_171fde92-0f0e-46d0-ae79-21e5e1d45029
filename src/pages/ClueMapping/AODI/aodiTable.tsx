import { usePollingExport } from '@/hooks/usePollingExport';
import {
  AODIClueData,
  AODIClueDelete,
  AODIClueExport,
  AODIClueImport,
  AODIClueQuery,
  AODIDownloadTemplate,
} from '@/services/clue-mapping';
import { proTableRequestAdapter } from '@/utils';
import { ActionType, ProColumns, ProFormInstance, ProTable } from '@ant-design/pro-components';
import { Space, Popconfirm, message, Button } from 'antd';
import saveAs from 'file-saver';
import { useRef, useMemo } from 'react';
import ImportCheckModal from '../importCheckModal';
import AODIModal from './aodiModal';

const AODITable = () => {
  const actionRef = useRef<ActionType>();
  const formRef = useRef<ProFormInstance>();
  const { pollingExport } = usePollingExport();

  const columns: ProColumns<AODIClueData>[] = useMemo(
    () => [
      {
        dataIndex: 'id',
        hidden: true,
        hideInSearch: true,
      },
      {
        title: '来源抖音昵称',
        dataIndex: 'referDyName',
        fixed: 'left',
        hideInSearch: true,
        align: 'center',
      },
      {
        title: '来源抖音号',
        dataIndex: 'referDyId',
        hideInSearch: false,
        align: 'center',
        formItemProps: {
          label: '抖音号',
        },
      },
      {
        title: '客户来源',
        dataIndex: 'sourceType',
        align: 'center',
        valueType: 'select',
        valueEnum: new Map([
          [1, '抖音本地通'],
          [2, '抖音'],
          [3, '奥迪官号'],
          [4, '南区矩阵'],
          [5, '西区矩阵'],
        ]),
      },
      {
        title: '创建时间',
        dataIndex: 'createTime',
        align: 'center',
        render: (_, record) => {
          return <div>{record.createTime}</div>;
        },
        valueType: 'dateTimeRange',
        search: {
          transform: (value) => {
            return {
              createTimeStart: value[0],
              createTimeEnd: value[1],
            };
          },
        },
      },
      {
        title: '更新时间',
        dataIndex: 'updateTime',
        align: 'center',
        render: (_, record) => {
          return <div>{record.updateTime}</div>;
        },
        valueType: 'dateTimeRange',
        search: {
          transform: (value) => {
            return {
              updateTimeStart: value[0],
              updateTimeEnd: value[1],
            };
          },
        },
      },
      {
        title: '创建人',
        dataIndex: 'createBy',
        align: 'center',
        hideInSearch: true,
      },
      {
        title: '更新人',
        dataIndex: 'updateBy',
        align: 'center',
        hideInSearch: true,
      },
      {
        title: '操作',
        valueType: 'option',
        fixed: 'right',
        align: 'center',
        render: (text, record, index, action) => {
          return (
            <Space>
              <Popconfirm
                title={'是否确认删除'}
                onConfirm={async () => {
                  if (record.id) {
                    const result = await AODIClueDelete([record.id]);
                    result.code === 0 ? message.success('删除成功') : message.error('删除失败');
                    action?.reload();
                  }
                }}
                key={'delete'}
              >
                <a>删除</a>
              </Popconfirm>
              <AODIModal
                text="编辑"
                trigger={<a>编辑</a>}
                id={record.id}
                initValue={record}
                actionRef={actionRef}
              />
            </Space>
          );
        },
      },
    ],
    [],
  );

  return (
    <ProTable<AODIClueData>
      columns={columns}
      scroll={{ x: 'max-content' }}
      actionRef={actionRef}
      formRef={formRef}
      headerTitle={[
        <Space key="table-action">
          <ImportCheckModal
            importCheckUrl="/new-media-api/leads/aodi/filter-store/import-check"
            key="batch-import"
            columnsNameMap={{
              referDyId: '来源抖音号',
              referDyName: '来源抖音昵称',
              sourceType: '客户来源',
            }}
            actionRef={actionRef}
            importFn={AODIClueImport}
            isAodi
          />
          <Button
            key="download-template"
            type="primary"
            onClick={async () => {
              const downloadBlob = await AODIDownloadTemplate();
              saveAs(downloadBlob, '导入模版.xlsx');
            }}
          >
            下载模版
          </Button>
          <Button
            type="primary"
            onClick={async () => {
              const currentFormValue = formRef.current?.getFieldsValue();
              const res = await AODIClueExport(currentFormValue);
              if (res.data) {
                pollingExport({ taskId: res.data });
              } else {
                message.error('导出失败');
              }
            }}
          >
            导出
          </Button>
          <AODIModal text="新增" actionRef={actionRef} />
        </Space>,
      ]}
      rowKey="id"
      search={{
        labelWidth: 'auto',
      }}
      request={(params, sorter, filter) => {
        return proTableRequestAdapter(params, sorter, filter, AODIClueQuery);
      }}
      pagination={{
        defaultPageSize: 10,
        showSizeChanger: true,
      }}
    />
  );
};

export default AODITable;
