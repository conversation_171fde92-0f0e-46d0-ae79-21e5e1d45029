import { usePollingExport } from '@/hooks/usePollingExport';
import {
  LeDaoClueData,
  LeDaoClueDelete,
  LeDaoClueExport,
  LeDaoClueImport,
  LeDaoClueQuery,
  LeDaoDownloadTemplate,
} from '@/services/clue-mapping';
import { proTableRequestAdapter } from '@/utils';
import { ActionType, ProColumns, ProFormInstance, ProTable } from '@ant-design/pro-components';
import { Button, message, Popconfirm, Space } from 'antd';
import saveAs from 'file-saver';
import { useMemo, useRef } from 'react';
import ImportCheckModal from '../importCheckModal';
import LeDaoModal from './leDaoModal';

const LeDaoTable = () => {
  const actionRef = useRef<ActionType>();
  const formRef = useRef<ProFormInstance>();
  const { pollingExport } = usePollingExport();

  const columns: ProColumns<LeDaoClueData>[] = useMemo(
    () => [
      {
        dataIndex: 'id',
        hidden: true,
        hideInSearch: true,
      },
      {
        title: '抖音号',
        dataIndex: 'referDyId',
        align: 'center',
      },
      {
        title: '来源门店 ID',
        dataIndex: 'sourceCommerceKey',
        fixed: 'left',
        hideInSearch: false,
        align: 'center',
        formItemProps: {
          label: '来源门店 ID',
        },
      },
      {
        title: '广告 ID',
        dataIndex: 'advertiserId',
        align: 'center',
      },
      {
        title: '广告 ID 类型',
        dataIndex: 'advertiserType',
        valueEnum: {
          1: { text: '短视频' },
          2: { text: '直播' },
        },
        hideInSearch: true,
        align: 'center',
      },
      {
        title: '活动编码',
        dataIndex: 'srcInterface',
        hideInSearch: true,
        align: 'center',
      },
      {
        title: '员工域账号',
        dataIndex: 'empDomain',
        hideInSearch: true,
        align: 'center',
      },
      {
        title: '推荐员工域账号',
        dataIndex: 'koeEmpDomain',
        hideInSearch: true,
        align: 'center',
      },
      {
        title: '账号类型',
        dataIndex: 'accountType',
        align: 'center',
        valueEnum: {
          1: { text: '官号' },
          2: { text: '区域号' },
          3: { text: '门店号' },
          4: { text: '员工号' },
        },
      },
      {
        title: '创建时间',
        dataIndex: 'createTime',
        align: 'center',
        render: (_, record) => {
          return <div>{record.createTime}</div>;
        },
        valueType: 'dateTimeRange',
        search: {
          transform: (value) => {
            return {
              createTimeStart: value[0],
              createTimeEnd: value[1],
            };
          },
        },
      },
      {
        title: '更新时间',
        dataIndex: 'updateTime',
        align: 'center',
        render: (_, record) => {
          return <div>{record.updateTime}</div>;
        },
        valueType: 'dateTimeRange',
        search: {
          transform: (value) => {
            return {
              updateTimeStart: value[0],
              updateTimeEnd: value[1],
            };
          },
        },
      },
      {
        title: '创建人',
        dataIndex: 'createBy',
        align: 'center',
        hideInSearch: true,
      },
      {
        title: '更新人',
        dataIndex: 'updateBy',
        align: 'center',
        hideInSearch: true,
      },
      {
        title: '操作',
        valueType: 'option',
        fixed: 'right',
        align: 'center',
        render: (text, record, index, action) => {
          return (
            <Space>
              <Popconfirm
                title={'是否确认删除'}
                onConfirm={async () => {
                  if (record.id) {
                    const result = await LeDaoClueDelete([record.id]);
                    result.code === 0 ? message.success('删除成功') : message.error('删除失败');
                    action?.reload();
                  }
                }}
                key={'delete'}
              >
                <a>删除</a>
              </Popconfirm>
              <LeDaoModal
                text="编辑"
                trigger={<a>编辑</a>}
                id={record.id}
                initValue={record}
                actionRef={actionRef}
              />
            </Space>
          );
        },
      },
    ],
    [],
  );

  return (
    <ProTable<LeDaoClueData>
      columns={columns}
      scroll={{ x: 'max-content' }}
      actionRef={actionRef}
      formRef={formRef}
      headerTitle={[
        <Space key="table-action">
          <ImportCheckModal
            importCheckUrl="/new-media-api/leads/ledao/import-check"
            key="batch-import"
            columnsNameMap={{
              advertiserId: '广告主id',
              advertiserType: '广告主类型',
              sourceCommerceKey: '来源门店 ID',
              srcInterface: '活动编码',
              empDomain: '员工域账号',
              koeEmpDomain: '推荐员工域账号',
              accountType: '账号类型',
              referDyId: '抖音号',
            }}
            actionRef={actionRef}
            importFn={LeDaoClueImport}
            isWeilai
          />
          <Button
            key="download-template"
            type="primary"
            onClick={async () => {
              const downloadBlob = await LeDaoDownloadTemplate();
              saveAs(downloadBlob, '导入模版.xlsx');
            }}
          >
            下载模版
          </Button>
          <Button
            type="primary"
            onClick={async () => {
              const currentFormValue = formRef.current?.getFieldsValue();
              const res = await LeDaoClueExport(currentFormValue);
              if (res.data) {
                pollingExport({ taskId: res.data });
              } else {
                message.error('导出失败');
              }
            }}
          >
            导出
          </Button>
          <LeDaoModal text="新增" actionRef={actionRef} />
        </Space>,
      ]}
      rowKey="id"
      search={{
        labelWidth: 'auto',
      }}
      request={(params, sorter, filter) => {
        return proTableRequestAdapter(params, sorter, filter, LeDaoClueQuery);
      }}
      pagination={{
        defaultPageSize: 10,
        showSizeChanger: true,
      }}
    />
  );
};

export default LeDaoTable;
