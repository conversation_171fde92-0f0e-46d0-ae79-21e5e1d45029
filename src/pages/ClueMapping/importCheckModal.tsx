import { ApiResult } from '@/services/common';
import { InboxOutlined } from '@ant-design/icons';
import { ActionType, ModalForm, ProColumns, ProTable } from '@ant-design/pro-components';
import { Button, message, Radio, RadioChangeEvent, Space, UploadProps } from 'antd';
import Dragger from 'antd/es/upload/Dragger';
import { isUndefined } from 'lodash-es';
import { useState } from 'react';

type ImportCheckModalProps = {
  importCheckUrl: string;
  columnsNameMap: { [key: string]: string };
  actionRef: React.MutableRefObject<ActionType | undefined>;
  importFn: (params: any, data: FormData) => Promise<ApiResult<boolean>>;
  isWeilai?: boolean; // 蔚来和乐道同理
  isAodi?: boolean;
};

const ImportCheckModal = (props: ImportCheckModalProps) => {
  const { importCheckUrl, columnsNameMap, actionRef, importFn, isWeilai, isAodi } = props;
  const [checkImportData, setCheckImportData] = useState<boolean>(false);
  const [checkTableData, setCheckTableData] = useState<any[]>([]);
  const [checkTableDataShow, setCheckTableDataShow] = useState<any[]>([]);
  const [tempFile, setTempFile] = useState<File | Blob | null>(null);
  const [checkTableColumns, setCheckTableColumns] = useState<ProColumns<any>[]>([]);
  const [value, setValue] = useState(1);

  const onChange = (e: RadioChangeEvent) => {
    setValue(e.target.value);
  };

  const importAction = () => {
    if (isWeilai) {
      return `${importCheckUrl}?accountType=${value}`;
    } else if (isAodi) {
      return `${importCheckUrl}?sourceType=${value}`;
    } else {
      return importCheckUrl;
    }
  };

  const importCheckProps: UploadProps = {
    name: 'file',
    accept: '.xlsx,.xls',
    maxCount: 1,
    action: importAction(),
    headers: {
      contentType: 'multipart/form-data',
    },
    showUploadList: false,
    beforeUpload: (file) => {
      setTempFile(file);
    },
    onChange(info) {
      const handleSuccess = (data: any[]) => {
        const columns = Object.keys(data[0])
          .filter((item) => item !== 'status')
          .map((item) => {
            let columnConfig: ProColumns<any> = {
              title: columnsNameMap[item],
              dataIndex: item,
              align: 'center',
              hideInSearch: true,
            };
            // 针对 accountType 字段设置特定属性
            if (item === 'accountType') {
              columnConfig = {
                ...columnConfig,
                valueType: 'select',
                valueEnum: {
                  1: '短视频专项',
                  2: '区域号专项',
                },
              };
            }

            // 针对 sourceType 字段设置特定属性
            if (item === 'sourceType') {
              columnConfig = {
                ...columnConfig,
                valueType: 'select',
                valueEnum: {
                  1: '抖音本地通',
                  2: '抖音',
                  3: '账号-奥迪官号',
                  4: '账号-南区矩阵',
                  5: '账号-西区矩阵',
                },
              };
            }

            return columnConfig;
          });

        const defaultColumns = [
          {
            title: '状态',
            dataIndex: 'status',
            fixed: 'right',
            align: 'center',
            valueEnum: {
              '1': '新增',
              '2': '修改',
            },
            valueType: 'select',
          },
        ];

        setCheckTableColumns([...defaultColumns, ...columns] as ProColumns<any>[]);
        setCheckTableData(data);
        setCheckTableDataShow(data);
        setCheckImportData(true);
      };

      if (info.file.status === 'done') {
        const respond = info.file.response;
        if (respond?.data?.length > 0) {
          handleSuccess(respond.data);
        } else if (respond.code !== 0) {
          message.error(`导入失败, 错误原因: ${respond.msg}`);
        }
      } else if (info.file.status === 'error') {
        message.error(`${info.file.name} 文件导入失败`);
      }
    },
  };

  return (
    <ModalForm
      title="导入信息确认"
      key="import-check"
      trigger={<Button type="primary">批量导入数据</Button>}
      width="80%"
      modalProps={{
        destroyOnHidden: true,
        centered: true,
        afterClose: () => {
          setCheckImportData(false);
        },
      }}
      onFinish={async () => {
        if (tempFile) {
          const formData = new FormData();
          formData.append('file', tempFile);
          // weilai 是 accountType aodi是 sourceType
          let params = {};
          if (isWeilai) {
            params = { accountType: value };
          } else if (isAodi) {
            params = { sourceType: value };
          }
          const result = await importFn(params, formData);
          if (result.code !== 0) {
            message.error(`文件导入失败 数据项格式可能不正确请检查; 错误原因: ${result.msg}`);
          } else {
            message.success(`导入成功`);
            actionRef.current?.reload();
          }
        }
        return true;
      }}
    >
      {checkImportData ? (
        <ProTable
          columns={checkTableColumns}
          dataSource={checkTableDataShow}
          onSubmit={(params) => {
            const { status } = params;
            const newCheckTableData = checkTableData.filter((item) => {
              if (!isUndefined(status)) {
                return item.status == status;
              } else {
                return true;
              }
            });
            setCheckTableDataShow(newCheckTableData);
          }}
          headerTitle={
            <Space>
              <span>新增数:{checkTableData.filter((item) => item.status == 1).length}条</span>
              <span>修改数:{checkTableData.filter((item) => item.status == 2).length}条</span>
            </Space>
          }
          search={{ filterType: 'light' }}
          rowKey="referDyId"
          options={false}
          ghost
          scroll={{ x: 'max-content' }}
          size="small"
          tableStyle={{
            paddingTop: '30px',
          }}
          pagination={{
            defaultPageSize: 10,
          }}
        />
      ) : (
        <>
          {isWeilai && (
            <Radio.Group onChange={onChange} value={value} style={{ marginBottom: '10px' }}>
              <Radio value={1}>官号</Radio>
              <Radio value={2}>区域号</Radio>
              <Radio value={3}>门店号</Radio>
              <Radio value={4}>员工号</Radio>
            </Radio.Group>
          )}
          {isAodi && (
            <Radio.Group onChange={onChange} value={value} style={{ marginBottom: '10px' }}>
              <Radio value={1}>抖音本地通</Radio>
              <Radio value={2}>抖音</Radio>
              <Radio value={3}>奥迪官号</Radio>
              <Radio value={4}>南区矩阵</Radio>
              <Radio value={5}>西区矩阵</Radio>
            </Radio.Group>
          )}
          <Dragger {...importCheckProps}>
            <p className="ant-upload-drag-icon">
              <InboxOutlined />
            </p>
            <p className="ant-upload-text">点击或拖拽上传文件</p>
          </Dragger>
        </>
      )}
    </ModalForm>
  );
};

export default ImportCheckModal;
