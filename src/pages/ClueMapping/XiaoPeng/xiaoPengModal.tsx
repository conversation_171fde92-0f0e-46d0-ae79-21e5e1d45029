import {
  Xiao<PERSON><PERSON><PERSON>lueC<PERSON>,
  Xiao<PERSON><PERSON><PERSON>lueUpdate,
  XiaoPengFormValues,
} from '@/services/clue-mapping';
import { ActionType, ModalForm, ProFormText } from '@ant-design/pro-components';
import { Button, message } from 'antd';

type WeiLaiModalProps = {
  text: string;
  actionRef: React.MutableRefObject<ActionType | undefined>;
  initValue?: Partial<XiaoPengFormValues>;
  trigger?: JSX.Element;
  id?: number;
};

const formItemLayout = {
  labelCol: { span: 6 },
  wrapperCol: { span: 18 },
};

const XiaoPengModal = (props: WeiLaiModalProps) => {
  const { text, initValue, trigger, id, actionRef } = props;

  return (
    <ModalForm<XiaoPengFormValues>
      title={text}
      autoFocusFirstInput
      layout={'horizontal'}
      {...formItemLayout}
      width={600}
      modalProps={{
        destroyOnHidden: true,
        centered: true,
      }}
      trigger={trigger || <Button type="primary">{text}</Button>}
      onFinish={async (values) => {
        let result;
        if (id) {
          result = await XiaoPengClueUpdate({ id, ...values });
        } else {
          result = await XiaoPengClueCreate(values);
        }
        result.code === 0 ? message.success('提交成功') : message.error('提交失败');
        actionRef.current?.reload();
        return true;
      }}
    >
      <ProFormText
        width="md"
        name="referDyName"
        label="来源抖音昵称"
        placeholder="请输入抖音昵称"
        initialValue={initValue?.referDyName}
      />
      <ProFormText
        width="md"
        name="referDyId"
        label="来源抖音号"
        placeholder="请输入抖音号"
        initialValue={initValue?.referDyId}
        rules={[{ required: true }]}
      />
      <ProFormText
        width="md"
        name="sourceCommerce"
        label="来源经销商名称"
        placeholder="请输入来源经销商名称"
        initialValue={initValue?.sourceCommerce}
      />
      <ProFormText
        width="md"
        name="sourceCommerceKey"
        label="来源经销商编码"
        placeholder="请输入来源经销商编码"
        initialValue={initValue?.sourceCommerceKey}
      />
      <ProFormText
        width="md"
        name="srcInterface"
        label="市场活动编码"
        initialValue={initValue?.srcInterface}
        placeholder="请输入活动编码"
        rules={[{ required: true }]}
      />
      <ProFormText
        width="md"
        name="srcInterfaceName"
        label="市场活动编码名称"
        initialValue={initValue?.srcInterfaceName}
        placeholder="请输入活动编码名称"
      />
    </ModalForm>
  );
};

export default XiaoPengModal;
