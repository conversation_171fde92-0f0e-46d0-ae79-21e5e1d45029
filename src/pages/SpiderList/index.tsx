import { spiderSyncUserTags, spiderSyncUserTagsReloader } from '@/models/store';
import { GetAllProject } from '@/services/project';
import {
  BatchEditSpiderSyncUser,
  BatchEditSpiderSyncUserParams,
  EditSpiderSyncUser,
  QuerySpiderSyncUserForPagination,
} from '@/services/spider';
import { BatchEditObjectTags, UpdateObjectTags } from '@/services/tag';
import { proTableRequestAdapter } from '@/utils';
import { CheckCircleFilled, CheckCircleTwoTone } from '@ant-design/icons';
import {
  ActionType,
  PageContainer,
  ProColumns,
  ProFormInstance,
  ProTable,
} from '@ant-design/pro-components';
import { NavLink, useNavigate, useRequest } from '@umijs/max';
import {
  Button,
  Dropdown,
  MenuProps,
  message,
  Popover,
  Space,
  Tag,
  Tooltip,
  Typography,
} from 'antd';
import copy from 'copy-to-clipboard';
import dayjs from 'dayjs';
import { useAtomValue, useSetAtom } from 'jotai';
import { keyBy } from 'lodash-es';
import { useEffect, useMemo, useRef, useState } from 'react';
import CommonTagSelectionModal from '../TagList/Components/selectModal';
import SpiderSyncUserTagSelect from '../TagList/Components/spiderSyncUserTagSelect';
import QualityRuleModal from './components/qualityRuleModal';
import relativeTime from 'dayjs/plugin/relativeTime';
import { AccountPlatformKeysMap, LiveRecordSupportingPlatforms, PlatForm } from '@/utils/platform';
import { SpiderSyncUser } from '@/services/account';

dayjs.locale('zh-cn');
dayjs.extend(relativeTime);

const items: MenuProps['items'] = [
  {
    key: '0',
    label: '未知',
  },
  {
    key: '1',
    label: '官号',
  },
  {
    key: '2',
    label: '经销商',
  },
];

type SpiderListProps = {
  platform: number;
};

function getAccountOutLink(entity: SpiderSyncUser) {
  if (entity.platform === 1) {
    return (
      <a
        onClick={() => {
          window.open(`https://www.douyin.com/user/${entity.accountId}`);
        }}
      >
        抖音主页
      </a>
    );
  } else if (entity.platform === 6) {
    return (
      <a
        onClick={() => {
          window.open(`https://www.xiaohongshu.com/user/profile/${entity.accountId}`);
        }}
      >
        小红书主页
      </a>
    );
  } else {
    return '-';
  }
}

const SpiderUserList: React.FC<SpiderListProps> = ({ platform = 0 }: SpiderListProps) => {
  const navigation = useNavigate();
  const actionRef = useRef<ActionType>();
  const formRef = useRef<ProFormInstance>();

  const [currentSpiderSyncUser, setCurrentSpiderSyncUser] = useState<SpiderSyncUser>();
  const [selectingSpiderSyncUsers, setSelectingSpiderSyncUsers] = useState<SpiderSyncUser[]>();
  const [showTagsModal, setShowTagsModal] = useState(false);
  const [showLiveRuleModal, setShowLiveRuleModal] = useState(false);

  const { data: projectList } = useRequest(GetAllProject);

  const projectRuleMap = useMemo(() => {
    return keyBy(projectList, (item) => item.id);
  }, [projectList]);

  const projectMap = useMemo(() => {
    const result: Record<string, { text: string }> = {};
    projectList?.forEach((projectRule) => {
      result[projectRule.id] = {
        text: projectRule.name,
      };
    });
    return result;
  }, [projectList]);

  const tagGroups = useAtomValue(spiderSyncUserTags);
  const reloadSpiderSyncUserTags = useSetAtom(spiderSyncUserTagsReloader);

  useEffect(() => {
    reloadSpiderSyncUserTags();
  }, []);

  const columns: Array<ProColumns<SpiderSyncUser>> = [
    {
      title: 'ID',
      dataIndex: 'accountId',
      hideInSearch: false,
      hideInTable: true,
    },
    {
      title: '账号',
      dataIndex: 'nickname',
      hideInSearch: true,
      hideInTable: false,
      fixed: 'left',
      render(dom, record) {
        const entity = record.accountUser;
        if (!entity) {
          return (
            <div style={{ width: 300, overflowX: 'hidden' }}>
              <Typography.Text copyable style={{ margin: 0, width: 300 }} ellipsis>
                {record.accountId}
              </Typography.Text>
            </div>
          );
        }

        return (
          <div style={{ display: 'flex', width: 300 }}>
            <img
              src={entity.avatar}
              alt={entity.nickname}
              width={24}
              height={24}
              style={{ marginRight: 8 }}
              referrerPolicy="no-referrer"
            />
            <Popover style={{ flex: 1 }} content={entity.nickname} trigger="hover">
              <NavLink
                to={`/monitor/account/user-list/detail/${AccountPlatformKeysMap[entity.platform]}/${
                  entity.accountId
                }`}
              >
                <Typography.Text
                  style={{
                    margin: 0,
                    width: 236 - (entity.blueVipFlag === 1 ? 18 : 0),
                    color: '#1677ff',
                  }}
                  ellipsis
                >
                  {entity.nickname}
                </Typography.Text>
              </NavLink>
            </Popover>
            {entity.blueVipFlag === 1 && (
              <Popover content={entity.blueVipReason} trigger="hover">
                <CheckCircleFilled style={{ marginLeft: 4, color: '#1677ff' }} rev={undefined} />
              </Popover>
            )}
            <Button
              type="default"
              size="small"
              style={{ marginLeft: 4 }}
              onClick={() => {
                if (entity.accountId) {
                  copy(entity.accountId);
                  message.success('已复制到剪切板');
                }
              }}
            >
              ID
            </Button>
          </div>
        );
      },
    },
    {
      title: '备注',
      dataIndex: 'remark',
      hideInSearch: false,
      hideInTable: false,
      render(dom) {
        return <Typography.Text style={{ margin: 0 }}>{dom}</Typography.Text>;
      },
    },
    {
      title: '账号类型',
      dataIndex: 'type',
      valueEnum: {
        0: '未知',
        1: '官号',
        2: '经销商',
      },
    },
    {
      title: '标签',
      dataIndex: 'tagIds',
      hideInSearch: false,
      valueType: 'text',
      renderFormItem: () => {
        return <SpiderSyncUserTagSelect data={tagGroups} />;
      },
      render: (_, entity) => {
        return (
          <div
            onClick={() => {
              setCurrentSpiderSyncUser(entity);
              setSelectingSpiderSyncUsers(undefined);
              setShowTagsModal(true);
            }}
          >
            {entity.tags && entity.tags.length > 0 && (
              <Space>
                {entity.tags.map((tag) => {
                  return <Tag key={tag.id}>{tag.name}</Tag>;
                })}
              </Space>
            )}
            {(!entity.tags || entity.tags.length === 0) && (
              <Button size="small" type="dashed">
                +
              </Button>
            )}
          </div>
        );
      },
    },
    {
      title: '状态',
      dataIndex: 'isDisable',
      hideInSearch: false,
      hideInTable: false,
      valueEnum: {
        0: {
          text: '已启用',
        },
        1: {
          text: '已禁用',
        },
      },
      render(_, entity) {
        return (
          <Button
            type="dashed"
            size="small"
            danger={entity.isDisable === 1}
            onClick={() => {
              const hide = message.loading('请求中');
              EditSpiderSyncUser(entity.id, { isDisable: entity.isDisable ? 0 : 1 }).then((res) => {
                hide();
                if (res.code === 0) {
                  message.success('请求成功');
                  actionRef.current?.reload();
                } else {
                  message.error(res.msg);
                }
              });
            }}
          >
            {entity.isDisable ? '已禁用' : '已启用'}
          </Button>
        );
      },
    },
    {
      title: '同步频率',
      dataIndex: 'syncFrequency',
      hideInSearch: false,
      hideInTable: false,
      valueEnum: {
        3: {
          text: '每一天',
        },
        2: {
          text: '每小时',
        },
        1: {
          text: '每10分钟',
        },
      },
    },
    {
      title: '作品保存',
      dataIndex: 'postSaveStatus',
      valueEnum: {
        0: {
          text: '不保存',
        },
        1: {
          text: '保存',
        },
      },
      render(_, entity) {
        if (entity.isDisable === 1) {
          return '-';
        }

        return (
          <Button
            type="dashed"
            size="small"
            onClick={() => {
              const hide = message.loading('请求中');
              EditSpiderSyncUser(entity.id, {
                postSaveStatus: entity.postSaveStatus ? 0 : 1,
              }).then((res) => {
                hide();
                if (res.code === 0) {
                  message.success('请求成功');
                  actionRef.current?.reload();
                } else {
                  message.error(res.msg);
                }
              });
            }}
            icon={
              entity.postSaveStatus === 1 && (
                <CheckCircleTwoTone twoToneColor="#52c41a" rev={undefined} />
              )
            }
          >
            {entity.postSaveStatus ? '保存' : '不保存'}
          </Button>
        );
      },
    },
    {
      title: '直播录制',
      dataIndex: 'liveReplayStatus',
      hideInSearch: !LiveRecordSupportingPlatforms.includes(platform),
      hideInTable: !LiveRecordSupportingPlatforms.includes(platform),
      valueEnum: {
        0: {
          text: '不录制',
        },
        1: {
          text: '录制',
        },
      },
      render(_, entity) {
        if (entity.isDisable === 1) {
          return '-';
        }

        return (
          <Button
            type="dashed"
            size="small"
            onClick={() => {
              const hide = message.loading('请求中');
              EditSpiderSyncUser(entity.id, {
                liveReplayStatus: entity.liveReplayStatus ? 0 : 1,
              }).then((res) => {
                hide();
                if (res.code === 0) {
                  message.success('请求成功');
                  actionRef.current?.reload();
                } else {
                  message.error(res.msg);
                }
              });
            }}
            icon={
              entity.liveReplayStatus === 1 && (
                <CheckCircleTwoTone twoToneColor="#52c41a" rev={undefined} />
              )
            }
          >
            {entity.liveReplayStatus ? '录制' : '不录制'}
          </Button>
        );
      },
    },
    {
      title: '作品质检',
      dataIndex: 'postQualityStatus',
      valueEnum: {
        0: {
          text: '不质检',
        },
        1: {
          text: '质检',
        },
      },
      render(_, entity) {
        if (entity.isDisable === 1) {
          return '-';
        }

        return (
          <Button
            type="dashed"
            size="small"
            onClick={() => {
              const hide = message.loading('取消中');
              EditSpiderSyncUser(entity.id, {
                postQualityStatus: entity.postQualityStatus === 1 ? 0 : 1,
              }).then((res) => {
                hide();
                if (res.code === 0) {
                  message.success('取消成功');
                  actionRef.current?.reload();
                } else {
                  message.error(res.msg);
                }
              });
            }}
            icon={
              entity.postQualityStatus === 1 && (
                <CheckCircleTwoTone twoToneColor="#52c41a" rev={undefined} />
              )
            }
          >
            {entity.postQualityStatus === 1 ? `质检` : '不质检'}
          </Button>
        );
      },
    },
    {
      title: '直播质检',
      dataIndex: 'liveQualityStatus',
      hideInSearch: !LiveRecordSupportingPlatforms.includes(platform),
      hideInTable: !LiveRecordSupportingPlatforms.includes(platform),
      valueEnum: {
        0: {
          text: '不质检',
        },
        1: {
          text: '质检',
        },
      },
      render(_, entity) {
        if (entity.isDisable === 1) {
          return '-';
        }

        return (
          <Button
            type="dashed"
            size="small"
            onClick={() => {
              const hide = message.loading('取消中');
              const param: any = {
                liveQualityStatus: entity.liveQualityStatus === 1 ? 0 : 1,
              };

              if (param.liveQualityStatus === 1) {
                param.liveReplayStatus = 1;
              }
              EditSpiderSyncUser(entity.id, param).then((res) => {
                hide();
                if (res.code === 0) {
                  message.success('取消成功');
                  actionRef.current?.reload();
                } else {
                  message.error(res.msg);
                }
              });
            }}
            icon={
              entity.liveQualityStatus === 1 && (
                <CheckCircleTwoTone twoToneColor="#52c41a" rev={undefined} />
              )
            }
          >
            {entity.liveQualityStatus === 1 ? `质检` : '不质检'}
          </Button>
        );
      },
    },
    {
      title: '质检规则',
      dataIndex: 'qualityRuleProjectId',
      hideInSearch: true,
      render(_, entity) {
        if (entity.isDisable === 1) {
          return '-';
        }

        if (entity.liveQualityStatus === 0 && entity.postQualityStatus === 0) {
          return '-';
        }

        const projectRule =
          entity.qualityRuleProjectId && projectRuleMap[entity.qualityRuleProjectId];
        return (
          <>
            <Button
              type="dashed"
              size="small"
              danger={!projectRule}
              onClick={() => {
                setCurrentSpiderSyncUser(entity);
                setSelectingSpiderSyncUsers(undefined);
                setShowLiveRuleModal(true);
              }}
            >
              {projectRule ? projectRule.name : '待设置'}
            </Button>
          </>
        );
      },
    },
    {
      title: '上次同步',
      dataIndex: 'lastSyncTime',
      hideInSearch: true,
      hideInTable: false,
      valueEnum(record) {
        return {
          正常: {
            color: 'green',
            text: (
              <Tooltip title={dayjs(record.lastSyncTime).format('YYYY-MM-DD HH:mm')}>
                正常({dayjs(record.lastSyncTime).fromNow().replaceAll(' ', '')})
              </Tooltip>
            ),
          },
          失败: {
            color: 'red',
            text: (
              <Tooltip title={dayjs(record.lastSyncTime).format('YYYY-MM-DD HH:mm')}>
                失败({dayjs(record.lastSyncTime).fromNow().replaceAll(' ', '')})
              </Tooltip>
            ),
          },
        };
      },
      renderText(_, record) {
        if (!record.lastSyncTime) {
          return '-';
        }

        return record.lastSyncPostStatus === -1 || record.lastSyncProfileStatus === -1
          ? '失败'
          : '正常';
      },
    },
    {
      title: '下次同步',
      dataIndex: 'next_sync_time',
      hideInSearch: true,
      hideInTable: false,
      renderText(_, record) {
        if (!record.nextSyncTime) {
          return '-';
        }

        return (
          <Tooltip title={dayjs(record.nextSyncTime).format('YYYY-MM-DD HH:mm')}>
            {dayjs(record.nextSyncTime).fromNow().replaceAll(' ', '')}
          </Tooltip>
        );
      },
      sorter: true,
    },
    {
      title: '创建时间',
      dataIndex: 'create_time',
      hideInSearch: true,
      hideInTable: false,
      renderText(_, record) {
        return dayjs(record.createTime).format('YYYY-MM-DD HH:mm');
      },
      sorter: true,
    },
    {
      title: '操作',
      dataIndex: 'action',
      hideInSearch: true,
      hideInTable: false,
      render(_, entity) {
        return <Space>{getAccountOutLink(entity)}</Space>;
      },
    },
    {
      title: '关联项目',
      dataIndex: 'projectId',
      hideInSearch: false,
      hideInTable: true,
      valueType: 'select',
      valueEnum: projectMap,
    },
  ];

  const onBatchChangeSyncFrequency = (ids: number[], syncFrequency: number) => {
    if (ids.length === 0) {
      return;
    }

    const hide = message.loading('请求中');
    BatchEditSpiderSyncUser({ ids, syncFrequency }).then((res) => {
      hide();
      if (res.code === 0) {
        message.success('请求成功');
        actionRef.current?.reload();
      } else {
        message.error(res.msg);
      }
    });
  };

  const handleBatchEditSpiderSyncUser = (
    selectedRows: SpiderSyncUser[],
    statusKey: keyof BatchEditSpiderSyncUserParams,
    value: any,
  ) => {
    if (selectedRows.length) {
      const hide = message.loading('请求中');
      const params: BatchEditSpiderSyncUserParams = {
        ids: selectedRows.map((i) => i.id),
        [statusKey]: value,
      };

      BatchEditSpiderSyncUser(params).then((res) => {
        hide();
        if (res.code === 0) {
          actionRef.current?.reload();
          actionRef.current?.clearSelected?.();
        } else {
          message.error(res.msg);
        }
      });
    }
  };

  return (
    <PageContainer
      extra={[
        <Button
          key="create"
          onClick={() => {
            if (platform === PlatForm.WXVideo) {
              navigation('weixin-create');
            } else {
              navigation('create');
            }
          }}
        >
          新增
        </Button>,
      ]}
    >
      <ProTable<SpiderSyncUser>
        formRef={formRef}
        actionRef={actionRef}
        scroll={{ x: 'max-content' }}
        columns={columns}
        rowKey="id"
        toolBarRender={false}
        bordered
        size="small"
        dateFormatter="string"
        pagination={{ defaultPageSize: 15, pageSizeOptions: [15, 50, 100], showSizeChanger: true }}
        search={{ filterType: 'query', defaultCollapsed: false }}
        params={{ platform }}
        request={(params, sorter, filter) => {
          if (params.liveReplayStatus === 'all') {
            delete params.liveReplayStatus;
          }
          if (params.isDisable === 'all') {
            delete params.isDisable;
          }
          if (params.syncFrequency === 'all') {
            delete params.syncFrequency;
          }
          return proTableRequestAdapter(params, sorter, filter, QuerySpiderSyncUserForPagination);
        }}
        rowSelection={{
          type: 'checkbox',
        }}
        tableAlertRender={({ selectedRowKeys, onCleanSelected }) => {
          return (
            <Space size={24}>
              <span>
                已选 {selectedRowKeys.length} 项
                <a style={{ marginInlineStart: 8 }} onClick={onCleanSelected}>
                  取消选择
                </a>
              </span>
            </Space>
          );
        }}
        tableAlertOptionRender={({ selectedRows }) => {
          return (
            <Space size={16}>
              <Space>
                账号类型:
                <Dropdown
                  menu={{
                    items,
                    onClick: ({ key }) =>
                      handleBatchEditSpiderSyncUser(selectedRows, 'type', Number(key)),
                  }}
                >
                  <a onClick={(e) => e.preventDefault()}>选择</a>
                </Dropdown>
              </Space>
              <Space>
                作品保存:
                <a onClick={() => handleBatchEditSpiderSyncUser(selectedRows, 'postSaveStatus', 1)}>
                  开启
                </a>
                <a onClick={() => handleBatchEditSpiderSyncUser(selectedRows, 'postSaveStatus', 0)}>
                  关闭
                </a>
              </Space>
              <Space>
                直播录制:
                <a
                  onClick={() => handleBatchEditSpiderSyncUser(selectedRows, 'liveReplayStatus', 1)}
                >
                  开启
                </a>
                <a
                  onClick={() => handleBatchEditSpiderSyncUser(selectedRows, 'liveReplayStatus', 0)}
                >
                  关闭
                </a>
              </Space>
              <Space>
                作品/直播质检:
                <a
                  onClick={() => {
                    if (selectedRows.length) {
                      setCurrentSpiderSyncUser(undefined);
                      setSelectingSpiderSyncUsers(selectedRows);
                      setShowLiveRuleModal(true);
                    }
                  }}
                >
                  开启
                </a>
                <a
                  onClick={() => {
                    if (selectedRows.length) {
                      const hide = message.loading('请求中');
                      BatchEditSpiderSyncUser({
                        ids: selectedRows.map((i) => i.id),
                        liveQualityStatus: 0,
                        postQualityStatus: 0,
                      }).then((res) => {
                        hide();
                        if (res.code === 0) {
                          actionRef.current?.reload();
                          actionRef.current?.clearSelected?.();
                        } else {
                          message.error(res.msg);
                        }
                      });
                    }
                  }}
                >
                  关闭
                </a>
              </Space>
              <Space>
                标签:
                <a
                  onClick={() => {
                    if (selectedRows.length) {
                      setCurrentSpiderSyncUser(undefined);
                      setSelectingSpiderSyncUsers(selectedRows);
                      setShowTagsModal(true);
                    }
                  }}
                >
                  批量编辑
                </a>
              </Space>
              <Space>
                同步频率:
                <a
                  onClick={() => {
                    onBatchChangeSyncFrequency(
                      selectedRows.map((i) => i.id),
                      3,
                    );
                  }}
                >
                  每一天
                </a>
                <a
                  onClick={() => {
                    onBatchChangeSyncFrequency(
                      selectedRows.map((i) => i.id),
                      2,
                    );
                  }}
                >
                  每小时
                </a>
                <a
                  onClick={() => {
                    onBatchChangeSyncFrequency(
                      selectedRows.map((i) => i.id),
                      1,
                    );
                  }}
                >
                  每10分钟
                </a>
              </Space>
              <Space>
                <a
                  onClick={() =>
                    handleBatchEditSpiderSyncUser(
                      selectedRows,
                      'nextSyncTime',
                      dayjs().format('YYYY-MM-DD HH:mm:ss'),
                    )
                  }
                >
                  立即触发
                </a>
              </Space>
            </Space>
          );
        }}
      />
      {projectList && (
        <QualityRuleModal
          projectList={projectList}
          visible={showLiveRuleModal}
          setVisible={setShowLiveRuleModal}
          onFinish={(projectId) => {
            if (currentSpiderSyncUser) {
              const hide = message.loading('请求中');
              EditSpiderSyncUser(currentSpiderSyncUser.id, {
                postSaveStatus: 1,
                liveReplayStatus: 1,
                qualityRuleProjectId: projectId,
              }).then((res) => {
                hide();
                if (res.code === 0) {
                  message.success('设置成功');
                  actionRef.current?.reload();
                } else {
                  message.error(res.msg);
                }
              });
            } else if (selectingSpiderSyncUsers && selectingSpiderSyncUsers.length) {
              const hide = message.loading('请求中');
              BatchEditSpiderSyncUser({
                ids: selectingSpiderSyncUsers.map((i) => i.id),
                postSaveStatus: 1,
                postQualityStatus: 1,
                liveQualityStatus: 1,
                liveReplayStatus: 1,
                qualityRuleProjectId: projectId,
              }).then((res) => {
                hide();
                if (res.code === 0) {
                  message.success('设置成功');
                  actionRef.current?.reload();
                  actionRef.current?.clearSelected?.();
                } else {
                  message.error(res.msg);
                }
              });
            }
          }}
        />
      )}

      <CommonTagSelectionModal
        visible={showTagsModal}
        setVisible={setShowTagsModal}
        mode="multiple"
        defaultValues={(currentSpiderSyncUser && currentSpiderSyncUser.tags) || []}
        onFinish={(values) => {
          if (currentSpiderSyncUser) {
            const hide = message.loading('请求中');
            UpdateObjectTags({
              type: 1,
              objectId: currentSpiderSyncUser.accountId,
              tagIds: values.map((i) => i.id),
            }).then((res) => {
              hide();
              if (res.code === 0) {
                actionRef.current?.reload();
              } else {
                message.error(res.msg);
              }
            });
          } else if (selectingSpiderSyncUsers && selectingSpiderSyncUsers.length) {
            const hide = message.loading('请求中');
            BatchEditObjectTags({
              type: 1,
              objectIds: selectingSpiderSyncUsers.map((i) => i.accountId),
              tagIds: values.map((i) => i.id),
            }).then((res) => {
              hide();
              if (res.code === 0) {
                actionRef.current?.reload();
                actionRef.current?.clearSelected?.();
              } else {
                message.error(res.msg);
              }
            });
          }
        }}
        data={tagGroups}
      />
    </PageContainer>
  );
};

export default SpiderUserList;
