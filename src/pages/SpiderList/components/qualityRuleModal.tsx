import { ProjectDataItem } from '@/services/project';
import { QualityMonitorWordList, SceneTypeEnum } from '@/services/quality';
import { CheckCard, ModalForm, ProCard, ProTable } from '@ant-design/pro-components';
import { useRequest } from 'ahooks';
import { Flex, ModalProps } from 'antd';
import { Dispatch, SetStateAction, useState } from 'react';

export type QualityRuleModalProps = ModalProps & {
  visible: boolean;
  setVisible: Dispatch<SetStateAction<boolean>>;
  projectList: ProjectDataItem[];
  onFinish?: (projectId: number) => void;
};

const QualityRuleModal = (props: QualityRuleModalProps) => {
  const { visible, setVisible, projectList, onFinish, ...rest } = props;
  const [projectId, setProjectId] = useState<any>(projectList[0].id);

  const { data: monitorWordData, loading } = useRequest(
    () => QualityMonitorWordList({ projectId }),
    {
      refreshDeps: [projectId],
      ready: !!projectId,
    },
  );

  const conversionMonitorWordData = monitorWordData?.data?.map((item) => {
    const suspectedWord = item.suspectedWord?.join(',');
    const sensitiveWord = item.sensitiveWord?.join(',');
    return {
      ...item,
      suspectedWord,
      sensitiveWord,
    };
  });

  const textDataSource = conversionMonitorWordData?.filter(
    (item) => item.sceneType === SceneTypeEnum.TEXT,
  );
  const behaviorDataSource = conversionMonitorWordData?.filter(
    (item) => item.sceneType === SceneTypeEnum.BEHAVIOR,
  );
  const commentDataSource = conversionMonitorWordData?.filter(
    (item) => item.sceneType === SceneTypeEnum.COMMENT,
  );

  return (
    <ModalForm
      {...rest}
      modalProps={{ zIndex: 1001, destroyOnHidden: true }}
      open={visible}
      width="90%"
      layout={'horizontal'}
      onOpenChange={setVisible}
      submitter={{
        searchConfig: { submitText: '提交' },
      }}
      onFinish={async () => {
        if (onFinish) {
          onFinish(projectId);
          setVisible(false);
        }
      }}
      title="选择质检规则"
    >
      <ProCard ghost split="vertical">
        <ProCard ghost colSpan={'200px'}>
          {projectList.length > 0 && (
            <CheckCard.Group
              size="small"
              onChange={(value) => {
                if (value !== undefined) {
                  setProjectId(value);
                }
              }}
              value={projectId}
              defaultValue={projectList[0].id}
            >
              {projectList.map((item) => {
                return (
                  <CheckCard
                    style={{ width: 180 }}
                    key={`card-${item.id}`}
                    title={item.name}
                    value={item.id}
                  />
                );
              })}
            </CheckCard.Group>
          )}
        </ProCard>

        <ProCard bodyStyle={{ paddingTop: 0 }}>
          <Flex vertical gap={20}>
            <ProTable
              headerTitle={'文字标识'}
              dataSource={textDataSource}
              rowKey="name"
              search={false}
              options={false}
              ghost
              loading={loading}
              size="small"
              pagination={{
                defaultPageSize: 10,
                hideOnSinglePage: true,
              }}
              columns={[
                {
                  title: '分类',
                  dataIndex: 'name',
                },
                {
                  title: '添加类型',
                  dataIndex: 'type',
                  valueEnum: {
                    1: '默认',
                    2: '自定义',
                  },
                },
                {
                  title: '疑似词',
                  dataIndex: 'suspectedWord',
                  valueType: 'textarea',
                },
                {
                  title: '敏感词',
                  dataIndex: 'sensitiveWord',
                  valueType: 'textarea',
                },
              ]}
            />
            <ProTable
              headerTitle={'行为标识'}
              dataSource={behaviorDataSource}
              rowKey="name"
              search={false}
              options={false}
              ghost
              loading={loading}
              size="small"
              pagination={{
                defaultPageSize: 10,
                hideOnSinglePage: true,
              }}
              columns={[
                {
                  title: '分类',
                  dataIndex: 'name',
                },
                {
                  title: '添加类型',
                  dataIndex: 'type',
                  valueEnum: {
                    1: '默认',
                    2: '自定义',
                  },
                },
                {
                  title: '说明',
                  dataIndex: 'description',
                },
              ]}
            />
            <ProTable
              headerTitle={'弹幕标识'}
              dataSource={commentDataSource}
              rowKey="name"
              search={false}
              options={false}
              ghost
              loading={loading}
              size="small"
              pagination={{
                defaultPageSize: 10,
                hideOnSinglePage: true,
              }}
              columns={[
                {
                  title: '分类',
                  dataIndex: 'name',
                },
                {
                  title: '添加类型',
                  dataIndex: 'type',
                  valueEnum: {
                    1: '默认',
                    2: '自定义',
                  },
                },
                {
                  title: '疑似词',
                  dataIndex: 'suspectedWord',
                },
              ]}
            />
          </Flex>
        </ProCard>
      </ProCard>
    </ModalForm>
  );
};

export default QualityRuleModal;
