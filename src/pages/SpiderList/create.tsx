import {
  AccountPlatformEnum,
  AccountPlatformKeysEnum,
  LiveRecordSupportingPlatforms,
  TextToPlatForm,
  PlatForm,
} from '@/utils/platform';
import {
  BatchCreateSpiderSyncUserV2,
  CheckCreateSpiderSyncUserV2,
  GetSpiderMonitorTemplate,
  SpiderUserInfo,
} from '@/services/spider';
import { saveFile } from '@/utils/client';
import { FileExcelOutlined } from '@ant-design/icons';
import {
  PageContainer,
  ProCard,
  ProColumns,
  ProForm,
  ProFormInstance,
  ProFormRadio,
  ProFormSwitch,
  ProFormText,
  ProFormTextArea,
  ProTable,
} from '@ant-design/pro-components';
import { useParams } from '@umijs/max';
import { Button, message, Space } from 'antd';
import { UploadFile } from 'antd/es/upload';
import Dragger from 'antd/es/upload/Dragger';
import { uniqBy } from 'lodash-es';
import { useEffect, useRef, useState } from 'react';
import readXlsxFile from 'read-excel-file';

const ParseCreateLinkMarkItemSchema = {
  平台: {
    prop: 'platform',
    type: String,
    require: true,
  },
  账号备注: {
    prop: 'remark',
    type: String,
    require: true,
  },
  账号链接: {
    prop: 'link',
    type: String,
    require: true,
  },
};

type ExcelItem = {
  platform: string;
  remark: string;
  link: string;
};

type SubmitFormValues = {
  syncFrequency: number;
  liveReplayStatus: number;
};

const CreateSpiderUsers: React.FC = () => {
  const [parseItems, setParseItems] = useState<SpiderUserInfo[]>([]);
  const [uploadItems, setUploadItems] = useState<SpiderUserInfo[]>([]);
  const [file, setFile] = useState<UploadFile>();
  const { platformKey } = useParams();
  const platform = ((platformKey && AccountPlatformKeysEnum[platformKey]) || 0) as PlatForm;
  const formRef = useRef<ProFormInstance>(null);

  const columns: Array<ProColumns<SpiderUserInfo>> = [
    {
      title: '账号ID',
      dataIndex: 'accountId',
    },
    {
      title: '账号备注',
      dataIndex: 'remark',
    },
    {
      title: '状态',
      render(_, entity) {
        if (entity?.isExist) {
          return '已存在，忽略';
        }

        return '待录入';
      },
    },
  ];

  useEffect(() => {
    (async () => {
      if (!file?.originFileObj) {
        setParseItems([]);
        return;
      }
      if (file.status !== 'done') {
        return;
      }
      let newItems: ExcelItem[] = [];
      const { rows, errors } = await readXlsxFile(file?.originFileObj, {
        schema: ParseCreateLinkMarkItemSchema,
        transformData: (data) => {
          // 跳过前 4 行，从第 5 行开始读取
          return data.slice(4);
        },
      });

      errors.forEach((error) => {
        console.error(error);
      });
      rows.forEach((row) => {
        newItems.push(row as ExcelItem);
      });

      newItems = uniqBy(newItems, (i) => i.link);

      newItems = newItems.filter((i) => TextToPlatForm[i.platform] === platform);

      const hide = message.loading('检查数据中');
      CheckCreateSpiderSyncUserV2({
        items: newItems.map((i) => ({ link: i.link, remark: i.remark })),
        platform,
      }).then((res) => {
        hide();
        if (res.code === 0 && res.data && res.data?.length > 0) {
          setParseItems(res.data);
          setUploadItems(res.data);
          message.success(
            `成功导入${AccountPlatformEnum[platform]?.text}平台数据, 解析出${res.data.length}个账号`,
          );
        } else {
          message.error(res.msg);
        }
      });
    })();
  }, [file]);

  const handleLinkMarkFormSubmit = async (values: { link: string; remark: string }) => {
    const hide = message.loading('检查数据中');
    const res = await CheckCreateSpiderSyncUserV2({
      items: [values],
      platform,
    });
    hide();

    if (res.code === 0 && res.data && res.data?.length > 0) {
      const newItem = res.data[0];

      // 检查是否已存在
      const isExist = uploadItems.some(
        (item) => item.accountId === newItem.accountId || item.link === newItem.link,
      );

      if (isExist) {
        message.warning('该账号已在列表中，请勿重复添加');
        return;
      }

      // 添加新项到列表
      setUploadItems([...uploadItems, newItem].filter(Boolean));
      setParseItems([...parseItems, newItem].filter(Boolean));

      // 清空表单
      formRef.current?.resetFields();
      message.success('添加成功');
    } else if (res.code === 0 && res.data && res.data?.length === 0) {
      message.error(res.msg || '添加失败,未解析到账号');
    } else {
      message.error(res.msg || '添加失败');
    }
  };

  const handleSubmitForm = async (
    values: SubmitFormValues,
    {
      platform,
      uploadItems,
      onSuccess,
    }: {
      platform: PlatForm;
      uploadItems: SpiderUserInfo[];
      onSuccess: () => void;
    },
  ) => {
    const hide = message.loading('提交中');
    try {
      const res = await BatchCreateSpiderSyncUserV2({
        items: uploadItems.map((i) => ({
          accountId: i.accountId || '',
          link: i.link,
          remark: i.remark,
        })),
        platform,
        syncFrequency: values.syncFrequency,
        liveReplayStatus: values.liveReplayStatus ? 1 : 0,
      });

      hide();
      if (res.code === 0) {
        message.success('提交成功');
        onSuccess();
      } else {
        message.error(res.msg);
      }
    } catch (error) {
      hide();
      message.error('提交失败，请重试');
      console.error('Submit form error:', error);
    }
  };

  const handleDownloadTemplate = () => {
    GetSpiderMonitorTemplate().then((templateBlob) => {
      saveFile(templateBlob, '导入模版.xlsx');
    });
  };

  const renderTooltip = () => {
    if (platform === PlatForm.Douyin) {
      return (
        <div>
          用户主页举例:
          https://www.douyin.com/user/MS4wLjABAAAA8U_l6rBzmy7bcy6xOJel4v0RzoR_wfAubGPeJimN__4
        </div>
      );
    } else if (platform === PlatForm.XiaoHongShu) {
      return (
        <div>用户主页举例: https://www.xiaohongshu.com/user/profile/5a1933fddb2e60610ccd3e70</div>
      );
    }
  };

  return (
    <PageContainer
      title={`新增${AccountPlatformEnum[platform] && AccountPlatformEnum[platform]?.text}监控用户`}
      onBack={() => {
        history.back();
      }}
    >
      <ProCard gutter={[0, 24]} ghost direction="column">
        <ProCard
          title="导入数据"
          extra={
            <Space>
              <Button key="download-template" onClick={handleDownloadTemplate}>
                下载模板
              </Button>
            </Space>
          }
          split="vertical"
        >
          <ProCard>
            <ProForm<{ link: string; remark: string }>
              formRef={formRef}
              initialValues={{ syncFrequency: 3, liveReplayStatus: 0 }}
              layout="horizontal"
              submitter={{ submitButtonProps: { title: '添加' } }}
              onFinish={(values) => handleLinkMarkFormSubmit(values)}
            >
              <ProFormTextArea
                name="link"
                label="主页链接"
                rules={[{ required: true, message: '请输入账号ID或主页链接' }]}
                placeholder={'请输入用户主页URL'}
                tooltip={renderTooltip()}
              />
              <ProFormText
                name="remark"
                label="账号备注"
                placeholder={'请输入账号备注，以便区分账号'}
                rules={[{ required: true, message: '请输入账号备注' }]}
              />
            </ProForm>
          </ProCard>
          <ProCard>
            <Dragger
              name="file"
              multiple={false}
              onChange={(info) => {
                console.log(info);
                if (info.file.status === 'removed') {
                  setFile(undefined);
                } else {
                  setFile(info.file);
                }
              }}
              fileList={file ? [file] : []}
            >
              <p className="ant-upload-drag-icon">
                <FileExcelOutlined />
              </p>
              <p className="ant-upload-text">点击或拖拽Excel文件到这里</p>
              <p className="ant-upload-hint">请先下载模板，按字段填充数据后批量导入</p>
            </Dragger>
          </ProCard>
        </ProCard>
        <ProCard title="解析数据">
          <ProTable<SpiderUserInfo>
            scroll={{ x: 'max-content' }}
            columns={columns}
            rowKey="accountId"
            toolBarRender={false}
            bordered
            size="small"
            dateFormatter="string"
            pagination={{ defaultPageSize: 15 }}
            search={false}
            dataSource={parseItems}
          />
        </ProCard>
        <ProCard title="提交数据">
          <ProForm<SubmitFormValues>
            initialValues={{ syncFrequency: 3, liveReplayStatus: 0 }}
            onValuesChange={(changeValues) => console.log(changeValues)}
            layout="horizontal"
            submitter={{ submitButtonProps: { disabled: uploadItems.length === 0 } }}
            onFinish={async (values) => {
              await handleSubmitForm(values, {
                platform,
                uploadItems,
                onSuccess: () => {
                  setFile(undefined);
                  setParseItems([]);
                  setUploadItems([]);
                },
              });
            }}
          >
            <ProFormRadio.Group
              name="syncFrequency"
              label="监听频率"
              radioType="button"
              options={[
                {
                  label: '每天',
                  value: 3,
                },
                {
                  label: '每小时',
                  value: 2,
                },
                {
                  label: '每10分钟',
                  value: 1,
                },
              ]}
            />
            <ProFormSwitch
              label={
                LiveRecordSupportingPlatforms.includes(platform)
                  ? '直播录制'
                  : '直播录制(当前平台暂不支持)'
              }
              name="liveReplayStatus"
              disabled={!LiveRecordSupportingPlatforms.includes(platform)}
            />
          </ProForm>
        </ProCard>
      </ProCard>
    </PageContainer>
  );
};

export default CreateSpiderUsers;
