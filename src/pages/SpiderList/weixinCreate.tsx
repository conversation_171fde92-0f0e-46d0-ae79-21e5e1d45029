import {
  AccountPlatformEnum,
  AccountPlatformKeysEnum,
  LiveRecordSupportingPlatforms,
  PlatForm,
} from '@/utils/platform';
import { BatchCreateSpiderSyncUser, CheckCreateSpiderSyncUser } from '@/services/spider';
import { saveFile } from '@/utils/client';
import { FileExcelOutlined } from '@ant-design/icons';
import {
  PageContainer,
  ProCard,
  ProColumns,
  ProForm,
  ProFormInstance,
  ProFormRadio,
  ProFormSwitch,
  ProFormText,
  ProFormTextArea,
  ProTable,
} from '@ant-design/pro-components';
import { useParams } from '@umijs/max';
import { Button, message, Space } from 'antd';
import { UploadFile } from 'antd/es/upload';
import Dragger from 'antd/es/upload/Dragger';
import { uniqBy } from 'lodash-es';
import { useEffect, useRef, useState } from 'react';
import readXlsxFile from 'read-excel-file';

interface CreateItem {
  accountId: string;
  remark: string;
  isExisting: boolean;
}

const ParseCreateItemSchema = {
  账号备注: {
    prop: 'remark',
    type: String,
    require: true,
  },
  账号ID: {
    prop: 'accountId',
    type: String,
    require: true,
  },
};

// 和 create.tsx 基本差不多，但是由于要在那个文件写好多 if else，我怕后续还有定制化修改，所以还是单独拆一个文件吧
const WeixinCreateSpiderUsers: React.FC = () => {
  const [parseItems, setParseItems] = useState<CreateItem[]>([]);
  const [uploadItems, setUploadItems] = useState<CreateItem[]>([]);
  const [file, setFile] = useState<UploadFile>();
  const { platformKey } = useParams();
  const platform = ((platformKey && AccountPlatformKeysEnum[platformKey]) || 0) as PlatForm;
  const formRef = useRef<ProFormInstance>(null);

  useEffect(() => {
    (async () => {
      if (!file?.originFileObj) {
        setParseItems([]);
        return;
      }

      let newItems: CreateItem[] = [];
      const { rows, errors } = await readXlsxFile(file?.originFileObj, {
        schema: ParseCreateItemSchema,
      });
      errors.forEach((error) => {
        console.error(error);
      });
      rows.forEach((row) => {
        // @ts-ignore
        newItems.push(row as CreateItem);
      });

      newItems = uniqBy(newItems, (i) => i.accountId);

      const hide = message.loading('检查数据中');
      CheckCreateSpiderSyncUser({ accountIds: newItems.map((i) => i.accountId), platform }).then(
        (res) => {
          hide();
          if (res.code === 0) {
            const existingAccountIds = res.data?.map((i) => i.accountId) || [];
            newItems = newItems.map((item) => {
              item.isExisting = existingAccountIds.includes(item.accountId);
              return item;
            });
            setParseItems(newItems);
            setUploadItems(newItems.filter((item) => !item.isExisting));
          } else {
            message.error(res.msg);
          }
        },
      );
    })();
  }, [file]);

  const columns: Array<ProColumns<CreateItem>> = [
    {
      title: '账号ID',
      dataIndex: 'accountId',
    },
    {
      title: '账号备注',
      dataIndex: 'remark',
    },
    {
      title: '状态',
      render(_, entity) {
        if (entity?.isExisting) {
          return '已存在，忽略';
        }

        return '待录入';
      },
    },
  ];

  return (
    <PageContainer
      title={`新增${AccountPlatformEnum[platform] && AccountPlatformEnum[platform]?.text}监控用户`}
      onBack={() => {
        history.back();
      }}
    >
      <ProCard gutter={[0, 24]} ghost direction="column">
        <ProCard
          title="导入数据"
          extra={
            <Space>
              <Button
                key="download-template"
                onClick={() => {
                  saveFile(
                    'https://media-**********.cos.ap-guangzhou.myqcloud.com/fe/%E7%9B%91%E6%8E%A7%E8%B4%A6%E5%8F%B7%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx',
                  );
                }}
              >
                下载模板
              </Button>
            </Space>
          }
          split="vertical"
        >
          <ProCard>
            <ProForm<{ accountId: string; remark: string }>
              formRef={formRef}
              initialValues={{ syncFrequency: 3, liveReplayStatus: 0 }}
              layout="horizontal"
              submitter={{ submitButtonProps: { title: '添加' } }}
              onFinish={async ({ accountId, remark }) => {
                if (parseItems.find((i) => i.accountId === accountId)) {
                  message.warning('请勿重复添加');
                  return;
                }

                const hide = message.loading('检查数据中');
                CheckCreateSpiderSyncUser({
                  accountIds: [accountId],
                  platform,
                }).then((res) => {
                  hide();
                  if (res.code === 0) {
                    const existingAccountIds = res.data?.map((i) => i.accountId) || [];
                    const isExisting = existingAccountIds.length > 0;
                    const newItem = {
                      accountId,
                      remark,
                      isExisting,
                    };

                    setParseItems([...parseItems, newItem]);
                    if (!isExisting) {
                      setUploadItems([...uploadItems, newItem]);
                    }
                  } else {
                    message.error(res.msg);
                  }
                });
              }}
            >
              <ProFormTextArea
                name="accountId"
                label="账号ID"
                rules={[
                  { required: true, message: '请输入账号ID' },
                  ...(platform !== PlatForm.WXVideo
                    ? [
                        {
                          required: true,
                          message: '账号ID格式错误',
                          pattern: /^[0-9a-zA-Z-_]+$/,
                        },
                      ]
                    : []),
                ]}
                placeholder={'请输入账号ID、或用户主页URL'}
                tooltip="前往 项目管理模块-运控账号 复制账号 ID"
              />
              <ProFormText
                name="remark"
                label="账号备注"
                placeholder={'请输入账号备注，以便区分账号'}
                rules={[{ required: true, message: '请输入账号备注' }]}
              />
            </ProForm>
          </ProCard>
          <ProCard>
            <Dragger
              name="file"
              multiple={false}
              onChange={(info) => {
                console.log(info);
                if (info.file.status === 'removed') {
                  setFile(undefined);
                } else {
                  setFile(info.file);
                }
              }}
              fileList={file ? [file] : []}
            >
              <p className="ant-upload-drag-icon">
                <FileExcelOutlined />
              </p>
              <p className="ant-upload-text">点击或拖拽Excel文件到这里</p>
              <p className="ant-upload-hint">请先下载模板，按字段填充数据后批量导入</p>
            </Dragger>
          </ProCard>
        </ProCard>
        <ProCard title="解析数据">
          <ProTable<CreateItem>
            scroll={{ x: 'max-content' }}
            columns={columns}
            rowKey="id"
            toolBarRender={false}
            bordered
            size="small"
            dateFormatter="string"
            pagination={{ defaultPageSize: 15 }}
            search={false}
            dataSource={parseItems}
          />
        </ProCard>
        <ProCard title="提交数据">
          <ProForm<{ syncFrequency: number; liveReplayStatus: number }>
            initialValues={{ syncFrequency: 3, liveReplayStatus: 0 }}
            onValuesChange={(changeValues) => console.log(changeValues)}
            layout="horizontal"
            submitter={{ submitButtonProps: { disabled: uploadItems.length === 0 } }}
            onFinish={async (values) => {
              console.log(values);
              const hide = message.loading('提交中');
              BatchCreateSpiderSyncUser({
                items: uploadItems,
                platform,
                liveReplayStatus: values.liveReplayStatus ? 1 : 0,
                syncFrequency: values.syncFrequency,
              }).then((res) => {
                hide();
                if (res.code === 0) {
                  message.success('提交成功');
                  setFile(undefined);
                  setParseItems([]);
                  setUploadItems([]);
                } else {
                  message.error(res.msg);
                }
              });
              console.log(values);
            }}
          >
            <ProFormRadio.Group
              name="syncFrequency"
              label="监听频率"
              radioType="button"
              options={[
                {
                  label: '每天',
                  value: 3,
                },
                {
                  label: '每小时',
                  value: 2,
                },
                {
                  label: '每10分钟',
                  value: 1,
                },
              ]}
            />
            <ProFormSwitch
              label={
                LiveRecordSupportingPlatforms.includes(platform)
                  ? '直播录制'
                  : '直播录制(当前平台暂不支持)'
              }
              name="liveReplayStatus"
              disabled={!LiveRecordSupportingPlatforms.includes(platform)}
            />
          </ProForm>
        </ProCard>
      </ProCard>
    </PageContainer>
  );
};

export default WeixinCreateSpiderUsers;
