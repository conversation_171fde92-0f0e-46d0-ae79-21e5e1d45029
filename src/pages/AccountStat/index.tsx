import { spiderSyncUserTags, spiderSyncUserTagsReloader } from '@/models/store';
import { GetAccountStatSum } from '@/services/account';
import { getLastQueryTagIds, LSQueryTagIdsKey } from '@/services/constants';
import { AccountUserDailyStat } from '@/services/typings';
import { AccountPlatformEnum, AccountPlatformKeysEnum } from '@/utils/platform';
import { LineChartOutlined } from '@ant-design/icons';
import {
  ActionType,
  PageContainer,
  ProCard,
  ProColumns,
  ProFormInstance,
  ProTable,
  StatisticCard,
} from '@ant-design/pro-components';
import { Col, Row, Space } from 'antd';
import dayjs from 'dayjs';
import { useAtomValue, useSetAtom } from 'jotai';
import RcResizeObserver from 'rc-resize-observer';
import { useEffect, useRef, useState } from 'react';
import LineChart from '../AccountDetail/components/line-chart';
import SpiderSyncUserTagSelect from '../TagList/Components/spiderSyncUserTagSelect';

const chartDataItems: { key: string; label: string }[] = [
  {
    key: 'postCount',
    label: '累计作品数',
  },
  {
    key: 'followerCount',
    label: '累计粉丝数',
  },
  {
    key: 'totalFavorited',
    label: '累计获赞数',
  },
];

const AccountStat: React.FC = () => {
  // const navigation = useNavigate();
  const actionRef = useRef<ActionType>();
  const formRef = useRef<ProFormInstance>();

  const [chartSize, setChartSize] = useState<{
    width: number;
    cellWidth: number;
    colSpan: number;
  }>();

  const tagGroups = useAtomValue(spiderSyncUserTags);
  const reloadSpiderSyncUserTags = useSetAtom(spiderSyncUserTagsReloader);

  useEffect(() => {
    reloadSpiderSyncUserTags();
  }, []);

  const columns: Array<ProColumns<AccountUserDailyStat>> = [
    {
      title: '账号平台',
      dataIndex: 'platform',
      hideInSearch: false,
      hideInTable: true,
      valueEnum: AccountPlatformEnum,
      initialValue: `${AccountPlatformKeysEnum.douyin}`,
      fieldProps: { allowClear: false },
    },
    {
      title: '日期',
      dataIndex: 'dateRange',
      hideInSearch: false,
      hideInTable: true,
      valueType: 'dateRange',
      initialValue: [dayjs().add(-7, 'day'), dayjs()],
      fieldProps: { allowClear: false },
    },
    {
      title: '日期',
      dataIndex: 'date',
      hideInSearch: true,
      hideInTable: false,
    },
    {
      title: '账号数',
      dataIndex: 'accountNum',
      hideInSearch: true,
      hideInTable: false,
      valueType: 'digit',
    },
    {
      title: '累计粉丝数',
      dataIndex: 'followerCount',
      hideInSearch: true,
      hideInTable: false,
      valueType: 'digit',
    },
    {
      title: '累计获赞数',
      dataIndex: 'totalFavorited',
      hideInSearch: true,
      hideInTable: false,
      valueType: 'digit',
    },
    {
      title: '累计作品数',
      dataIndex: 'postCount',
      hideInSearch: true,
      hideInTable: false,
      valueType: 'digit',
    },
    {
      title: '标签',
      dataIndex: 'tagIds',
      hideInSearch: false,
      hideInTable: true,
      valueType: 'text',
      renderFormItem: () => {
        return <SpiderSyncUserTagSelect data={tagGroups} />;
      },
      initialValue: getLastQueryTagIds(),
    },
    // {
    //   title: '操作',
    //   dataIndex: 'action',
    //   hideInSearch: true,
    //   hideInTable: false,
    //   render() {
    //     return (
    //       <Space>
    //         <a
    //           onClick={() => {
    //             // navigation(`/car/car-lib/series/${entity.id}`);
    //           }}
    //         >
    //           查看
    //         </a>
    //       </Space>
    //     );
    //   },
    // },
  ];

  return (
    <PageContainer extra={[]}>
      <ProTable<AccountUserDailyStat>
        formRef={formRef}
        actionRef={actionRef}
        scroll={{ x: 'max-content' }}
        columns={columns}
        rowKey="id"
        toolBarRender={false}
        bordered
        size="small"
        dateFormatter="string"
        pagination={false}
        search={{ filterType: 'query', defaultCollapsed: false }}
        request={async (params) => {
          localStorage.setItem(LSQueryTagIdsKey, JSON.stringify({ tagIds: params.tagIds }));
          params.fromDate = params.dateRange[0];
          params.toDate = params.dateRange[1];
          delete params.dateRange;
          // @ts-ignore
          const res = await GetAccountStatSum(params);
          return {
            data: res.data,
            success: res.code === 0,
            total: res.data?.length,
          };
        }}
        tableExtraRender={(_, dataSource) => {
          return (
            <ProCard>
              <RcResizeObserver
                key="resize-observer"
                onResize={(offset) => {
                  if (offset.width >= 1200) {
                    setChartSize({
                      width: offset.width,
                      cellWidth: Math.floor((offset.width - 48 - 40) / 3),
                      colSpan: 8,
                    });
                  } else if (offset.width >= 600) {
                    setChartSize({
                      width: offset.width,
                      cellWidth: Math.floor((offset.width - 48 - 40) / 2),
                      colSpan: 12,
                    });
                  } else {
                    setChartSize({
                      width: offset.width,
                      cellWidth: Math.floor(offset.width - 48),
                      colSpan: 24,
                    });
                  }
                }}
              >
                <Row style={{ paddingRight: 40 }} wrap>
                  {chartSize &&
                    chartDataItems.map((t) => {
                      const chartData =
                        dataSource.map((item: AccountUserDailyStat & any) => {
                          return { date: item.date.slice(5, 10), value: item[t.key] || 0 };
                        }) || [];
                      const value =
                        chartData[chartData.length - 1] && chartData[chartData.length - 1].value;
                      return (
                        <Col key={t.key} span={chartSize.colSpan}>
                          <div key={t.label}>
                            <StatisticCard
                              statistic={{
                                title: (
                                  <Space>
                                    <span>{t.label}</span>
                                    <LineChartOutlined />
                                  </Space>
                                ),
                                value: value,
                              }}
                              chart={
                                <div style={{ width: '100%' }}>
                                  <LineChart
                                    uniqId={`chart-${t.key}-${t.label}`}
                                    chartData={chartData}
                                    width={chartSize.cellWidth}
                                    height={chartSize.cellWidth * 0.6}
                                  />
                                </div>
                              }
                            />
                          </div>
                        </Col>
                      );
                    })}
                </Row>
              </RcResizeObserver>
            </ProCard>
          );
        }}
      />
    </PageContainer>
  );
};

export default AccountStat;
