import { PageContainer } from '@ant-design/pro-components';
import styled from 'styled-components';
import { VideoCategoryList } from './components/VideoCategoryList';
import { VideoCategoryForm } from './components/videoCategoryForm';
import { BrandTabs } from './components/BrandTabs';
import { VideosTable } from './components/VideosTable';
import { useAtomValue } from 'jotai';
import { collapseFormAtom } from './atom';
import { useIndustryTagGroups } from '@/hooks/request/use-industry-tag-groups';
import { useEffect, useMemo } from 'react';
import { useAtomCallback } from 'jotai/utils';
import { videoCategoryParamsAtom } from './atom';
import { TagGroupWithIndustry } from '@/services/typings';
import BreadCrumbSimple from '@/components/ui/breadcrumbSimple';

const WorkAnalysisWrapper = styled(PageContainer)`
  .ant-pro-page-container-children-container {
    padding: 16px;
  }
`;

const WorkAnalysisTop = () => {
  const collapseForm = useAtomValue(collapseFormAtom);
  return (
    <div className="mb-[24px] rounded-[5px] bg-[#fff] px-[1.66vw] py-[22px]">
      <VideoCategoryForm />
      <div style={{ display: collapseForm ? 'none' : 'block' }}>
        <VideoCategoryList />
      </div>
    </div>
  );
};

const WorkAnalysis = () => {
  const isManagemenEnd = useMemo(() => {
    return location.pathname.startsWith('/project/');
  }, []);

  const { data: industryTagGroups } = useIndustryTagGroups();

  const initVideoCategoryParams = useAtomCallback(
    (get, set, firstIndustryTagGroup: TagGroupWithIndustry) => {
      const videoCategoryParams = get(videoCategoryParamsAtom);

      if (
        videoCategoryParams.industryType === undefined ||
        videoCategoryParams.tagGroupId === undefined
      ) {
        set(videoCategoryParamsAtom, {
          ...videoCategoryParams,
          industryType: firstIndustryTagGroup.industryType,
          tagGroupId: firstIndustryTagGroup.id,
        });
      }
    },
  );

  useEffect(() => {
    if (industryTagGroups && industryTagGroups.length > 0) {
      initVideoCategoryParams(industryTagGroups[0]);
    }
  }, [industryTagGroups, initVideoCategoryParams]);

  if (isManagemenEnd) {
    return (
      <PageContainer title={<BreadCrumbSimple breadcrumbs={['行业管理', '行业爆款拆解']} />}>
        <WorkAnalysisTop />

        <div className="mb-[24px] rounded-[5px] bg-[#fff] px-[1.66vw] py-[22px]">
          <BrandTabs />
          <VideosTable />
        </div>
      </PageContainer>
    );
  }

  return (
    <WorkAnalysisWrapper
      className="box-border overflow-x-hidden bg-[#f3f5f9]"
      pageHeaderRender={() => <></>}
    >
      <WorkAnalysisTop />

      <div className="mb-[24px] rounded-[5px] bg-[#fff] px-[1.66vw] py-[22px]">
        <BrandTabs />
        <VideosTable />
      </div>
    </WorkAnalysisWrapper>
  );
};

export default WorkAnalysis;
