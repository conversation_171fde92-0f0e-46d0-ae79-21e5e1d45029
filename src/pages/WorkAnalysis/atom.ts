import { IndustryType } from '@/utils/const';
import dayjs from 'dayjs';
import { atom } from 'jotai';

export const collapseFormAtom = atom<boolean>(false);

/** 当前视频类型id */
export const selectingVideoTypeIdAtom = atom<string | undefined>(undefined);

/** 当前品牌标签id */
export const selectingBrandTagIdAtom = atom<string | undefined>(undefined);

const DEFAULT_PARAMS = {
  dateRangeStart: dayjs().subtract(7, 'day').format('YYYY-MM-DD'),
  dateRangeEnd: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
  dateRange: 7,
};

interface VideoCategoryParams {
  dateRangeStart: string;
  dateRangeEnd: string;
  dateRange: number; // 3 | 7 | 30
  industryType?: IndustryType;
  tagGroupId?: number;
}

export const videoCategoryParamsAtom = atom<VideoCategoryParams>(DEFAULT_PARAMS);

export const brandMapAtom = atom<Record<string, string>>({});
