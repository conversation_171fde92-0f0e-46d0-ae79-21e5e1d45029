import { Checkbox, Col, Flex, Input, Popover, Row, Table, TableProps } from 'antd';
import styles from '../index.module.less';
import type { SearchProps } from 'antd/es/input/Search';
import { useEffect, useState } from 'react';
import { useAtomValue } from 'jotai';
import {
  selectingBrandTagIdAtom,
  selectingVideoTypeIdAtom,
  videoCategoryParamsAtom,
} from '../../atom';
import { WorksAnalysisVO, getScreenTypeList, getWorkAnalysisList } from '@/services/work-analysis';
import { useRequest } from 'ahooks';
import { CaretDownOutlined, CaretUpOutlined } from '@ant-design/icons';

const SELECT_ALL_SCREEN = 'SELECT_ALL_SCREEN';

const DEFAULT_TABLE_QUERY_PARAMS = {
  pageSize: 10,
  page: 1,
  sort: undefined,
  sortBy: undefined,

  selectingTagIds: [SELECT_ALL_SCREEN] as string[],
  searchKey: undefined,
};

export const VideosTable = () => {
  const videoCategoryParams = useAtomValue(videoCategoryParamsAtom);
  const videoType = useAtomValue(selectingVideoTypeIdAtom);
  const selectingBrandTagId = useAtomValue(selectingBrandTagIdAtom);

  const [tableQueryParams, setTableQueryParams] = useState<typeof DEFAULT_TABLE_QUERY_PARAMS>(
    DEFAULT_TABLE_QUERY_PARAMS,
  );

  useEffect(() => {
    setTableQueryParams((prev) => ({
      ...DEFAULT_TABLE_QUERY_PARAMS,
      sort: prev.sort,
      sortBy: prev.sortBy,
    }));
  }, [selectingBrandTagId, videoType, videoCategoryParams]);

  const { data: screenTypeRes } = useRequest(
    () => {
      if (!videoType) {
        return Promise.resolve(null);
      }
      return getScreenTypeList({
        startTime: videoCategoryParams.dateRangeStart,
        endTime: videoCategoryParams.dateRangeEnd,
        videoType: videoType || '',
        tagId: selectingBrandTagId,
        industryType: videoCategoryParams.industryType,
        tagGroupId: videoCategoryParams.tagGroupId,
      });
    },
    {
      refreshDeps: [videoCategoryParams, videoType, selectingBrandTagId],
      ready: videoCategoryParams.industryType != null && videoCategoryParams.tagGroupId != null,
    },
  );

  const { data: workAnalysisListRes } = useRequest(
    () => {
      if (!videoType) {
        return Promise.reject();
      }

      return getWorkAnalysisList({
        size: tableQueryParams.pageSize,
        page: tableQueryParams.page,
        startTime: videoCategoryParams.dateRangeStart,
        endTime: videoCategoryParams.dateRangeEnd,
        videoType: videoType || '',
        screenType: tableQueryParams.selectingTagIds.includes(SELECT_ALL_SCREEN)
          ? undefined
          : tableQueryParams.selectingTagIds,
        tagId: selectingBrandTagId,
        title: tableQueryParams.searchKey,
        orderBy: tableQueryParams.sortBy,
        orderType:
          tableQueryParams.sort === 'ascend'
            ? 'asc'
            : tableQueryParams.sort === 'descend'
              ? 'desc'
              : undefined,
        industryType: videoCategoryParams.industryType,
        tagGroupId: videoCategoryParams.tagGroupId,
      });
    },
    {
      refreshDeps: [videoCategoryParams, videoType, tableQueryParams, selectingBrandTagId],
      ready: videoCategoryParams.industryType != null && videoCategoryParams.tagGroupId != null,
    },
  );

  const columns: TableProps<WorksAnalysisVO>['columns'] = [
    {
      title: '作品信息',
      width: 380,
      key: 'title',
      render: (_, rec) => {
        return (
          <Flex>
            <Popover
              destroyTooltipOnHide
              content={<video controls height={400} src={rec.videoUrl} />}
              trigger="click"
            >
              <img
                src={rec.coverUrl}
                style={{
                  width: 60,
                  height: 60,
                  marginRight: 8,
                  borderRadius: 4,
                }}
                alt=""
              />
            </Popover>
            <div
              title={rec.title}
              className={styles.limit_decs}
              style={{
                width: 224,
              }}
            >
              {rec.title}
            </div>
          </Flex>
        );
      },
    },
    {
      title: '镜头类型',
      width: 356,
      dataIndex: 'screenTypes',
      key: 'screenTypes',
      render: (_, rec) => {
        return (
          <Popover content={rec.screenTypes?.join('、')}>
            <div className={styles.limit_decs}>
              {rec.screenTypes?.map((item) => {
                return (
                  <div key={item + rec.title} className={styles.screen_tag}>
                    {item}
                  </div>
                );
              })}
            </div>
          </Popover>
        );
      },
    },
    {
      title: '总互动量',
      key: 'interactCount',
      dataIndex: 'interactCount',
      width: 110,
      sorter: true,
    },
    {
      title: '点赞量',
      key: 'diggCount',
      dataIndex: 'diggCount',
      width: 100,
      sorter: true,
    },
    {
      title: '评论量',
      dataIndex: 'commentCount',
      key: 'commentCount',
      width: 100,
      sorter: true,
    },
    {
      title: '分享量',
      dataIndex: 'shareCount',
      key: 'shareCount',
      width: 100,
      sorter: true,
    },
    {
      title: '收藏量',
      dataIndex: 'collectCount',
      key: 'collectCount',
      width: 100,
      sorter: true,
    },
  ];

  const onSearch: SearchProps['onSearch'] = (value) => {
    setTableQueryParams((prev: any) => {
      return {
        ...prev,
        searchKey: value,
        page: 1,
      };
    });
  };

  return (
    <div className={styles.videos_table_wrapper}>
      <Flex justify="space-between" align="center">
        <span className={styles.title}>{videoType == '-' ? '其他' : videoType}视频素材分析</span>

        <div>
          <Input.Search
            placeholder="搜索内容标题"
            onSearch={onSearch}
            enterButton
            style={{ width: 209 }}
          />
        </div>
      </Flex>

      <div className={styles.tag_list_wrapper}>
        <div className={styles.tag_list}>
          <div style={{ flexShrink: 0 }}>筛选:</div>
          <Checkbox
            className={styles.tag_item}
            checked={tableQueryParams.selectingTagIds.includes(SELECT_ALL_SCREEN)}
            onChange={(e) => {
              setTableQueryParams((prev) => ({
                ...prev,
                selectingTagIds: e.target.checked
                  ? [SELECT_ALL_SCREEN]
                  : prev.selectingTagIds?.filter((id) => id !== SELECT_ALL_SCREEN),
                page: 1,
              }));
            }}
          >
            不限
          </Checkbox>
          {screenTypeRes?.data?.map((item) => (
            <Checkbox
              key={item}
              className={styles.tag_item}
              checked={tableQueryParams.selectingTagIds?.includes(item)}
              onChange={(e) => {
                setTableQueryParams((prev) => ({
                  ...prev,
                  selectingTagIds: e.target.checked
                    ? [...prev.selectingTagIds.filter((item) => item !== SELECT_ALL_SCREEN), item]
                    : prev.selectingTagIds.filter((id) => id !== item),
                  page: 1,
                }));
              }}
            >
              {item}
            </Checkbox>
          ))}
        </div>
      </div>

      <Table
        columns={columns}
        dataSource={workAnalysisListRes?.data?.items}
        onChange={(pagination, filter, sorter, extra) => {
          const { action } = extra;
          if (action === 'sort') {
            setTableQueryParams((prev: any) => ({
              ...prev,
              // @ts-ignore
              sortBy: sorter.column ? sorter.field : undefined,
              // @ts-ignore
              sort: sorter.order,
            }));
          }
        }}
        pagination={{
          total: workAnalysisListRes?.data?.total,
          current: tableQueryParams?.page,
          pageSize: tableQueryParams?.pageSize,
          onChange(page, pageSize) {
            setTableQueryParams((prev: any) => ({ ...prev, page, pageSize }));
          },
          size: 'small',
          showSizeChanger: true,
        }}
        scroll={{ x: 'max-content' }}
        expandable={{
          expandIcon: ({ expanded, onExpand, record }) =>
            expanded ? (
              <CaretUpOutlined onClick={(e) => onExpand(record, e)} />
            ) : (
              <CaretDownOutlined onClick={(e) => onExpand(record, e)} />
            ),
          expandedRowRender: (record) => {
            const hasFormula = record?.scriptFormula && record?.scriptFormula !== '-';
            return (
              <Flex key={record.title} vertical style={{ fontSize: 14 }}>
                <Row style={{ marginBottom: 30 }}>
                  <Col span={2}>脚本公式</Col>
                  <Col span={21}>
                    {hasFormula ? (
                      <span>
                        {record.scriptFormula?.split('+').map((formulaPart, i) => {
                          return (
                            <div key={formulaPart + record.title} className={styles.formula_part}>
                              {i > 0 && <span style={{ margin: '0 5px' }}>+</span>}
                              <span className={styles.formula_part_name}>{formulaPart}</span>
                            </div>
                          );
                        })}
                      </span>
                    ) : (
                      '-'
                    )}
                  </Col>
                </Row>
                <Row>
                  <Col span={2}>本视频脚本</Col>
                  <Col span={21}>
                    <span style={{ color: '#64666B' }}>{record.subtitles}</span>
                  </Col>
                </Row>
              </Flex>
            );
          },
        }}
      />
    </div>
  );
};
