import { Divider, Spin, Tabs, TabsProps } from 'antd';
import styles from '../index.module.less';
import { DashBoard } from './dashBoard';
import { useAtom, useAtomValue, useSetAtom } from 'jotai';
import {
  brandMapAtom,
  selectingBrandTagIdAtom,
  selectingVideoTypeIdAtom,
  videoCategoryParamsAtom,
} from '../../atom';
import { FormulaList } from './formulaList';
import { useRequest } from 'ahooks';
import { getBrandList } from '@/services/work-analysis';
import { useEffect, useMemo } from 'react';

export const BrandTabs = () => {
  const [selectingBrandId, setSelectingBrandId] = useAtom(selectingBrandTagIdAtom);
  const videoCategoryParams = useAtomValue(videoCategoryParamsAtom);
  const videoType = useAtomValue(selectingVideoTypeIdAtom);
  const setBrandMap = useSetAtom(brandMapAtom);

  const { data: brandListRes, loading } = useRequest(
    () => {
      if (!videoType) {
        return Promise.reject();
      }
      return getBrandList({
        startTime: videoCategoryParams.dateRangeStart,
        endTime: videoCategoryParams.dateRangeEnd,
        videoType: videoType || '',
        industryType: videoCategoryParams.industryType,
        tagGroupId: videoCategoryParams.tagGroupId,
      });
    },
    {
      refreshDeps: [videoCategoryParams, videoType],
      ready: videoCategoryParams.industryType != null && videoCategoryParams.tagGroupId != null,
    },
  );

  const onChange = (key: string) => {
    if (!brandListRes?.data?.some((item) => item.brandId == key)) {
      setSelectingBrandId(undefined);
      return;
    }
    setSelectingBrandId(key);
  };

  const items: TabsProps['items'] = useMemo(() => {
    const defaultItem = {
      key: undefined as unknown as string,
      label: '全部',
    };
    const brandItems = brandListRes?.data?.map((item) => ({
      key: item.brandId,
      label: item.brandName,
    }));
    return [defaultItem, ...(brandItems || [])];
  }, [brandListRes?.data]);

  useEffect(() => {
    if (items?.length && items?.length > 0) {
      setSelectingBrandId(items[0].key);
    }
    setBrandMap(() => {
      const brands: Record<string, string> = {};
      items?.forEach((item) => {
        brands[item.key] = item.label as string;
      });
      return brands;
    });
  }, [items]);

  return (
    <div className={styles.brand_tabs_wrapper}>
      <Spin spinning={loading}>
        <Tabs activeKey={selectingBrandId} items={items} onChange={onChange} />
      </Spin>
      <DashBoard />
      <FormulaList />
      <Divider />
    </div>
  );
};
