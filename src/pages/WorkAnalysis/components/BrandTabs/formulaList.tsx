import { getFormulaList } from '@/services/work-analysis';
import { useRequest } from 'ahooks';
import { useAtomValue } from 'jotai';
import styles from '../index.module.less';
import {
  selectingBrandTagIdAtom,
  selectingVideoTypeIdAtom,
  videoCategoryParamsAtom,
} from '../../atom';
import { Flex, Spin } from 'antd';
import { useState } from 'react';
import { FormulaSkeleton } from '@/components/sekleton';

export const FormulaList = () => {
  const videoCategoryParams = useAtomValue(videoCategoryParamsAtom);
  const videoType = useAtomValue(selectingVideoTypeIdAtom);
  const selectingBrandTagId = useAtomValue(selectingBrandTagIdAtom);
  const [expand, setExpand] = useState(true);
  const { data: formulaListRes, loading } = useRequest(
    () => {
      if (!videoType) {
        return Promise.resolve(null);
      }
      return getFormulaList({
        startTime: videoCategoryParams.dateRangeStart,
        endTime: videoCategoryParams.dateRangeEnd,
        videoType: videoType || '',
        tagId: selectingBrandTagId,
        industryType: videoCategoryParams.industryType,
        tagGroupId: videoCategoryParams.tagGroupId,
      });
    },
    {
      refreshDeps: [videoCategoryParams, videoType, selectingBrandTagId],
      ready: videoCategoryParams.industryType != null && videoCategoryParams.tagGroupId != null,
    },
  );

  return formulaListRes?.data?.length || loading ? (
    <div className={styles.formula_list_wrapper}>
      <div className={styles.scroll_content}>
        <div className={styles.expand_btn} onClick={() => setExpand(!expand)}>
          {expand ? '收起' : '展开'}
        </div>
        <Spin spinning={false}>
          {loading && <FormulaSkeleton />}

          {formulaListRes?.data?.map((item, index) => {
            if (loading) {
              return <></>;
            }
            const hasFormula = item?.scriptFormula && item?.scriptFormula !== '-';
            return (
              <Flex
                vertical
                justify="space-between"
                key={item?.scriptFormula}
                className={styles.formula_wrapper}
                style={{
                  height: expand ? 146 : 65,
                  marginBottom: expand ? 48 : 24,
                }}
              >
                <div>内容公式{index + 1}</div>

                <Flex>
                  {hasFormula && (
                    <span>
                      {item?.scriptFormula?.split('+').map((formulaPart, i) => {
                        return (
                          <div key={formulaPart} className={styles.formula_part}>
                            {i > 0 && <span style={{ margin: '0 5px' }}>+</span>}
                            <span className={styles.formula_part_name}>{formulaPart}</span>
                          </div>
                        );
                      })}
                    </span>
                  )}
                  <span style={{ marginLeft: hasFormula ? 24 : 0 }}>
                    {hasFormula
                      ? `${item?.scriptCount}个视频使用该脚本`
                      : `${item?.scriptCount}个视频未使用口播配音`}
                  </span>

                  <div style={{ marginLeft: 24 }} className={styles.formula_part_title}>
                    镜头数:{item?.recommendCount}
                  </div>
                </Flex>

                {expand && (
                  <Flex align="center">
                    <Flex align="center">
                      <span className={styles.formula_part_title}>开头</span>
                      {item.startScripts?.map((s) => (
                        <Flex
                          vertical
                          justify="space-between"
                          key={s?.startScript}
                          className={styles.lens_item}
                        >
                          <span
                            title={s?.startScript}
                            style={{
                              maxWidth: '80%',
                              whiteSpace: 'nowrap',
                              textOverflow: 'ellipsis',
                              overflow: 'hidden',
                            }}
                          >
                            {s?.startScript}
                          </span>
                          <span style={{ fontSize: 12, color: '#64666B', whiteSpace: 'nowrap' }}>
                            视频占比{s?.startScriptRatio?.toFixed(0)}%
                          </span>
                        </Flex>
                      ))}
                    </Flex>

                    <Flex align="center" style={{ marginLeft: 40 }}>
                      <span className={styles.formula_part_title}>内容</span>
                      {item.scriptContentInfo?.map((lensItem) => {
                        return (
                          <Flex
                            vertical
                            justify="space-between"
                            key={lensItem?.scriptContent}
                            className={styles.lens_item}
                          >
                            <span
                              title={lensItem?.scriptContent}
                              style={{
                                maxWidth: '80%',
                                whiteSpace: 'nowrap',
                                textOverflow: 'ellipsis',
                                overflow: 'hidden',
                              }}
                            >
                              {lensItem?.scriptContent}
                            </span>
                            <span style={{ fontSize: 12, color: '#64666B', whiteSpace: 'nowrap' }}>
                              视频占比{lensItem?.contentRatio?.toFixed(0)}%
                            </span>
                          </Flex>
                        );
                      })}
                    </Flex>
                  </Flex>
                )}
              </Flex>
            );
          })}
        </Spin>
      </div>
    </div>
  ) : (
    <></>
  );
};
