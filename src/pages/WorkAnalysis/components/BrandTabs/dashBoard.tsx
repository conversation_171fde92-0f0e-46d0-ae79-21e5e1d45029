import { Empty, Flex, Space, Switch } from 'antd';
import { useMemo, useState } from 'react';
import styles from '../index.module.less';
import EChartsReact from 'echarts-for-react';
import { EChartsOption } from 'echarts/types/dist/shared';
import { useRequest } from 'ahooks';
import { getTagCompareData } from '@/services/work-analysis';
import { useAtomValue } from 'jotai';
import {
  brandMapAtom,
  selectingBrandTagIdAtom,
  selectingVideoTypeIdAtom,
  videoCategoryParamsAtom,
} from '../../atom';

import IconComment from '@/assets/wa_comment.png';
import IconInteract from '@/assets/wa_interact.png';
import IconLike from '@/assets/wa_like.png';
import IconShare from '@/assets/wa_share.png';
import IconStar from '@/assets/wa_star.png';
import { formatNum } from '@/utils/common';
import { TagCompareDataSkeleton } from '@/components/sekleton';

export const DashBoard = () => {
  const [showGraph, setShowGraph] = useState(true);
  const videoCategoryParams = useAtomValue(videoCategoryParamsAtom);
  const videoType = useAtomValue(selectingVideoTypeIdAtom);
  const selectingBrandTagId = useAtomValue(selectingBrandTagIdAtom);
  const brandMap = useAtomValue(brandMapAtom);

  const { data: tagCompareDataRes, loading: tagCompareDataLoading } = useRequest(
    () => {
      if (!videoType) {
        return Promise.resolve(null);
      }
      return getTagCompareData({
        startTime: videoCategoryParams.dateRangeStart,
        endTime: videoCategoryParams.dateRangeEnd,
        videoType: videoType || '',
        tagId: selectingBrandTagId,
        industryType: videoCategoryParams.industryType,
        tagGroupId: videoCategoryParams.tagGroupId,
      });
    },
    {
      refreshDeps: [videoCategoryParams, videoType, selectingBrandTagId],
      ready: videoCategoryParams.industryType != null && videoCategoryParams.tagGroupId != null,
    },
  );

  const simpleData = useMemo(() => {
    return [
      {
        name: '互动量',
        value1: tagCompareDataRes?.data?.totalInteraction ?? '-',
        value2: tagCompareDataRes?.data?.medianInteraction ?? '-',
        icon: IconInteract,
      },
      {
        name: '点赞量',
        value1: tagCompareDataRes?.data?.totalDigg ?? '-',
        value2: tagCompareDataRes?.data?.medianDigg ?? '-',
        icon: IconLike,
      },
      {
        name: '评论量',
        value1: tagCompareDataRes?.data?.totalComment ?? '-',
        value2: tagCompareDataRes?.data?.medianComment ?? '-',
        icon: IconComment,
      },
      {
        name: '收藏量',
        value1: tagCompareDataRes?.data?.totalCollect ?? '-',
        value2: tagCompareDataRes?.data?.medianCollect ?? '-',
        icon: IconStar,
      },
      {
        name: '分享量',
        value1: tagCompareDataRes?.data?.totalShare ?? '-',
        value2: tagCompareDataRes?.data?.medianShare ?? '-',
        icon: IconShare,
      },
    ];
  }, [tagCompareDataRes?.data]);

  const graph1Data = tagCompareDataRes?.data?.tags;
  const option1: EChartsOption = {
    grid: {
      left: '5%',
      right: '5%',
      containLabel: true,
    },
    legend: {
      show: true,
      orient: 'horizontal',
      itemGap: 15,
      icon: 'circle',
      textStyle: {
        color: 'gray',
      },
    },
    tooltip: {
      show: true,
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
      },
      formatter: function (params: any) {
        let tooltipText = '<div style="text-align: left;">';
        params.forEach((param: any) => {
          tooltipText += `
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <div>${param.marker} ${param.seriesName}</div>
            &nbsp;&nbsp;<div style="color: #666; font-weight: 900; font-size: 14px;">${formatNum(
              param.value,
            )}</div>
          </div>
        `;
        });
        tooltipText += '</div>';
        return tooltipText;
      },
    },
    xAxis: { type: 'category', data: graph1Data?.map((item) => brandMap[item.tagId]) },
    yAxis: [
      {
        type: 'value',
        name: '作品',
        position: 'left',
        alignTicks: true,
        axisLine: {
          show: false,
          lineStyle: {
            // color: 'blue',
          },
        },
        axisLabel: {
          show: true,
          formatter: '{value} ',
        },
      },
      {
        type: 'value',
        name: '互动',
        position: 'right',
        alignTicks: true,
        axisLabel: {
          show: true,
          formatter: '{value} ',
        },
      },
    ],
    series: [
      {
        name: '作品量',
        data: graph1Data?.map((item) => item.worksCount || 0),
        type: 'bar',
        barGap: 0,
        color: '#0057f8',
        yAxisIndex: 0,
      },
      {
        name: '互动量',
        data: graph1Data?.map((item) => item.interactionCount || 0),
        type: 'bar',
        color: '#1ac2c1',
        yAxisIndex: 1,
      },
    ],
  };

  const graph2Data = tagCompareDataRes?.data?.worksDailySumVOList;
  const option2: EChartsOption = {
    grid: {
      left: '5%',
      right: '5%',
      containLabel: true,
    },
    legend: {
      icon: 'circle',
      textStyle: {
        color: 'gray',
      },
    },
    xAxis: {
      type: 'category',
      data: graph2Data?.map((item) => item.date || ''),
    },
    tooltip: {
      show: true,
      trigger: 'item',
      axisPointer: {
        type: 'cross',
      },
    },
    yAxis: {
      type: 'value',
      name: brandMap[selectingBrandTagId || ''],
      alignTicks: true,
      // offset: 80,
      axisLine: {
        show: true,
        lineStyle: {
          // color: 'blue',
        },
      },
    },
    series: [
      {
        name: '作品量',
        data: graph2Data?.map((item) => item.worksCount || 0),
        color: '#0057f8',
        type: 'bar',
      },
    ],
  };

  return (
    <div className={styles.dash_board_wrapper}>
      <span className={styles.title}>{videoType == '-' ? '其他' : videoType}视频素材概览</span>

      <Flex align="center" justify="space-between">
        <span className="sum">
          所选时间周期内，本视频类型共记{tagCompareDataRes?.data?.totalWorks ?? 0}
          个视频，以下为整体分析情况
        </span>
        <Space>
          <span style={{ fontSize: 16, fontWeight: 600 }}>数据对比</span>
          &nbsp;
          <Switch defaultChecked={showGraph} onChange={() => setShowGraph(!showGraph)} />
        </Space>
      </Flex>

      <Flex justify="space-between" gap={21} wrap className="mb-6 mt-4">
        {simpleData.map((item) => {
          return tagCompareDataLoading ? (
            <TagCompareDataSkeleton />
          ) : (
            <div key={item.name} className={styles.data_item}>
              <Flex justify="space-between" align="center" style={{ height: '100%' }}>
                <Flex style={{ fontSize: 14 }} align="center">
                  <img src={item.icon} style={{ width: 20, height: 20, marginRight: 4 }} alt="" />总
                  {item.name}
                </Flex>
                <br />
                <span style={{ fontSize: 12 }}>{item.name}中位数</span>
              </Flex>
              <Flex justify="space-between" align="center" style={{ height: '100%' }}>
                <span style={{ fontWeight: 500, fontSize: 18, textAlign: 'left' }}>
                  {item.value1}
                </span>
                <br />

                <span style={{ fontWeight: 500, fontSize: 16, textAlign: 'right' }}>
                  {item.value2}
                </span>
              </Flex>
            </div>
          );
        })}
      </Flex>

      {showGraph &&
        (tagCompareDataRes?.data ? (
          selectingBrandTagId ? (
            <div className={styles.graph_wrapper}>
              <EChartsReact
                option={option2}
                showLoading={false}
                notMerge={true}
                style={{ width: '100%', height: '100%' }}
              />
            </div>
          ) : (
            <div className={styles.graph_wrapper}>
              <EChartsReact
                option={option1}
                showLoading={false}
                notMerge={true}
                style={{ width: '100%', height: '100%' }}
              />
            </div>
          )
        ) : (
          <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} />
        ))}
    </div>
  );
};
