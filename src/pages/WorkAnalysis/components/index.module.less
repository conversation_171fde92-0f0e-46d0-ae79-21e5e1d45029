.video_category_form_wrapper {
  width: 100%;
  :global {
    .ant-select-selector,
    .ant-picker-outlined {
      border: 0 !important;
      border-radius: 5px;
      height: 33px;
      background-color: #f5f6fa !important;
    }
  }
}

.dash_board_wrapper {
  .title {
    font-size: 16px;
    margin-bottom: 6px;
    font-weight: 600;
    display: block;
  }
  .sum {
    font-size: 14px;
    color: #000000b2;
  }
  .graph_wrapper {
    height: 320px;
    width: 100%;
    margin-bottom: 46px;
  }
  .data_item {
    flex: 1;
    min-width: 182px;
    height: 85px;
    background-color: #f3f5fb;
    border-radius: 4px;
    display: flex;
    padding: 16px 12px;
    box-sizing: border-box;
    flex-direction: column;
    justify-content: space-between;
    align-items: space-between;
  }
}

.formula_list_wrapper {
  background-color: #f5f6fa;
  border-radius: 4px;
  position: relative;
  overflow: hidden;
  padding: 24px;
  .scroll_content {
    overflow-x: auto;
    &::-webkit-scrollbar {
      width: 4px;
      height: 4px;
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      width: 1px;
      height: 2px;
      background: rgba(0, 0, 0, 0.1);
      border-radius: 10px;
    }

    &::-webkit-scrollbar-thumb:hover {
      height: 2px;
      border-radius: 10px;
    }
  }
  .expand_btn {
    position: absolute;
    width: 54px;
    height: 24px;
    border-top-right-radius: 4px;
    background-color: #edf3ff;
    top: 0;
    right: 0;
    text-align: center;
    line-height: 24px;
    font-size: 14px;
    color: #1e5eff;
  }
  .formula_wrapper {
    width: 100%;
    margin-bottom: 48px;
    font-size: 16px;
    font-weight: 500;
    color: #0e1015;
    .lens_item {
      width: 86px;
      height: 65px;
      font-size: 14px;
      color: #1e5eff;
      padding: 12px 7px;
      background-color: #ffffff;
      align-items: center;
      margin: 0 8px;
      border-radius: 4px;
      box-sizing: border-box;
      text-overflow: ellipsis;
      overflow: hidden;
    }
    .formula_part {
      display: inline-block;
      .formula_part_name {
        display: inline-block;
      }
      .formula_part_name::after {
        content: '';
        display: block;
        width: '100%';
        height: 4px;
        background-color: #1e5eff;
      }
    }
    .formula_part_title {
      margin-right: 8px;
      display: block;
    }
  }
  :global {
    .ant-spin-container {
      display: grid;
    }
  }
}

.brand_tabs_wrapper {
  margin-bottom: 20px;
  :global {
    .ant-tabs > .ant-tabs-nav {
      margin: 4px 0 18px 0;
    }
    .ant-tabs .ant-tabs-tab {
      color: #64666b;
      padding: 0;
    }
    .ant-tabs .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
      color: #ffffff;
      background-color: #1e5eff;
      border-radius: 4px;
      padding: 0 4px;
      height: 24px;
      line-height: 24px;
    }
    .ant-tabs > .ant-tabs-nav .ant-tabs-nav-wrap {
      padding-bottom: 16px;
    }
    .ant-tabs-top > .ant-tabs-nav .ant-tabs-ink-bar {
      display: none;
    }
  }
}

.video_category_list_wrapper {
  margin-top: 19px;
  :global {
    .ant-select-selector {
      border: 0 !important;
      border-radius: 5px;
      height: 33px;
      background-color: #f1f4fa !important;
    }
  }
  .title {
    width: 100%;
    font-family: 'OPPOSans';
    color: #000;
    font-size: 16px;
  }

  .video_category_item {
    min-width: 240px;
    max-width: 300px;
    flex: 1;
    height: 93px;
    background-color: #f3f5fb;
    border-radius: 6px;
    padding: 12px;
    font-size: 14px;
    color: #0e1015;
    font-family: PingFang SC;
    font-weight: 500;
    box-sizing: border-box;
    border: 1.5px solid transparent;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    /* align-items: center; */
    &.selected {
      border-color: #1e5eff;
      color: #1e5eff !important;
      span {
        color: #1e5eff !important;
      }
    }
    &.selected::after {
      width: 20px;
      height: 20px;
      display: block;
      content: '';
      position: absolute;
      background: url(@/assets/wa_video-type-icon.png) no-repeat;
      background-size: 100% auto;
      top: 0;
      right: 0;
    }
  }
  .text_ellipsis {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.videos_table_wrapper {
  padding: 0 0 34px 0;

  .title {
    font-size: 16px;
    display: inline-block;
    margin-bottom: 10px;
    font-weight: 500;
  }
  :global {
    .ant-table-wrapper .ant-table-thead > tr > th {
      padding: 7px 12px;
      background: #fafcfe;
      text-align: left !important;
    }
    .ant-input-outlined {
      background-color: #fff;
    }
    .ant-input-group > .ant-input:first-child {
      border-radius: 2px !important;
    }
    .ant-input-search
      > .ant-input-group
      > .ant-input-group-addon:last-child
      .ant-input-search-button {
      border-radius: 2px !important;
      background-color: #fff;
      color: #000;
      width: 30px;
      display: flex;
      justify-content: center;
      align-items: center;
      border: 1px solid rgb(217, 217, 217);
    }

    .ant-pagination-options {
      order: -1;
    }
    .ant-select-outlined:not(.ant-select-customize-input) .ant-select-selector {
      background-color: #f5f6fa;
      border-color: #ebedf2;
      height: 24px;
    }
    .ant-pagination .ant-pagination-item-active {
      width: 24px;
      height: 24px;
      line-height: 24px;
      background-color: #1e5eff;
      margin-right: 8px;
    }
    .ant-pagination .ant-pagination-item-active a {
      color: #fff;
    }
    .ant-pagination.ant-pagination-mini:not(.ant-pagination-disabled)
      .ant-pagination-item:not(.ant-pagination-item-active) {
      background-color: #f5f6fa;
      border-color: #ebedf2;
      margin-right: 8px;
    }
    .ant-table-wrapper .ant-table-pagination.ant-pagination {
      margin: 24px 0 16px 0;
    }
  }
  .tag_list_wrapper {
    width: 100%;
    position: relative;
    .tag_list {
      display: flex;
      padding-right: 50px;
      margin-top: 5px;
      margin-bottom: 20px;
      align-items: center;
      width: 100%;
      overflow-x: auto;
      &::-webkit-scrollbar {
        display: none;
      }
    }
    &::after {
      position: absolute;
      content: '';
      right: 0;
      top: 0;
      width: 50px;
      height: 20px;
      background: linear-gradient(90deg, transparent, #ffffff79, #fff);
    }

    .tag_item {
      flex-shrink: 0;
      font-size: 12px;
      margin-left: 7px;
    }
  }
  .screen_tag {
    height: 24px;
    color: #1e5eff;
    padding: 0 8px;
    background-color: #edf3ff;
    margin-right: 8px;
    display: inline-block !important;
    margin-bottom: 8px;
  }
  .limit_decs {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 3;
    height: 65;
  }
  .formula_part {
    display: inline-block;
    .formula_part_name {
      display: inline-block;
      font-weight: 500;
      font-size: 14px;
      color: #0e1015;
    }
    .formula_part_name::after {
      content: '';
      display: block;
      width: '100%';
      height: 4px;
      background-color: #1e5eff;
    }
  }
}
