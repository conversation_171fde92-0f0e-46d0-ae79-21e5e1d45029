import { useRequest } from 'ahooks';
import { Col, Empty, Flex, Row, Select, Space, Spin } from 'antd';
import { useAtom, useAtomValue } from 'jotai';
import { useCallback, useEffect, useState } from 'react';
import styles from '../index.module.less';
import { selectingVideoTypeIdAtom, videoCategoryParamsAtom } from '../../atom';
import { getVideoTypeList } from '@/services/work-analysis';
import { VideoTypesSkeleton } from '@/components/sekleton';
import classNames from 'classnames';

export const VideoCategoryList = () => {
  const [sort, setSort] = useState(0);
  const videoCategoryParams = useAtomValue(videoCategoryParamsAtom);
  const [selectingVideoType, setSelectingVideoType] = useAtom(selectingVideoTypeIdAtom);
  const { data: videoTypesRes, loading } = useRequest(
    () =>
      getVideoTypeList({
        startTime: videoCategoryParams.dateRangeStart,
        endTime: videoCategoryParams.dateRangeEnd,
        sortType: sort,
        industryType: videoCategoryParams.industryType,
        tagGroupId: videoCategoryParams.tagGroupId,
      }),
    {
      refreshDeps: [videoCategoryParams, sort],
      ready: videoCategoryParams.industryType != null && videoCategoryParams.tagGroupId != null,
    },
  );

  const handleVideoTypeClick = useCallback((id: string) => {
    setSelectingVideoType(id);
  }, []);

  useEffect(() => {
    setSelectingVideoType(videoTypesRes?.data?.[0]?.videoType || '-');
  }, [videoTypesRes?.data]);

  return (
    <div className={styles.video_category_list_wrapper}>
      <Flex justify="space-between" align="center" style={{ marginBottom: 18 }}>
        <span className={styles.title}>视频类型分布</span>

        <Space style={{ width: 222 }} align="center">
          <div style={{ whiteSpace: 'nowrap' }}>排序方式</div>
          <Select
            defaultValue={sort}
            style={{ width: 150 }}
            onChange={(value) => setSort(value)}
            options={[
              { value: 0, label: ' 作品占比' },
              { value: 1, label: ' 互动占比' },
            ]}
          />
        </Space>
      </Flex>

      <Spin spinning={loading}>
        <Row className={styles.video_category_list} gutter={[8, 8]}>
          {!loading ? (
            videoTypesRes?.data?.length ? (
              videoTypesRes?.data?.map((item) => {
                return (
                  <Col span={4} xxl={4} xl={6} lg={8} md={8} sm={12} xs={24} key={item.videoType}>
                    <div
                      className={classNames(styles.video_category_item, {
                        [styles.selected]: item?.videoType === selectingVideoType,
                      })}
                      key={item.videoType}
                      onClick={() => handleVideoTypeClick(item?.videoType)}
                    >
                      <div className={styles.text_ellipsis} title={item?.videoType}>
                        {item?.videoType == '-' ? '其他' : item?.videoType}
                      </div>
                      <div>{item?.ratio?.toFixed(2)}%</div>
                      <Flex style={{ fontSize: 12 }} justify="space-between" align="baseline">
                        <span style={{ color: '#64666B', fontWeight: 500 }}>
                          作品量{item?.videoCount}
                        </span>
                        <span style={{ color: '#0E1015', fontWeight: 500 }}>
                          品牌数{item?.brandCount}
                        </span>
                      </Flex>
                    </div>
                  </Col>
                );
              })
            ) : (
              <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} className="m-auto" />
            )
          ) : (
            <>
              <VideoTypesSkeleton />
              <VideoTypesSkeleton />
              <VideoTypesSkeleton />
              <VideoTypesSkeleton />
            </>
          )}
        </Row>
      </Spin>
    </div>
  );
};
