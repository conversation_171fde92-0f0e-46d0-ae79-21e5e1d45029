import { DatePicker, Select } from 'antd';
import dayjs from 'dayjs';
import { useAtom } from 'jotai';
import { collapseFormAtom, videoCategoryParamsAtom } from '../atom';
import styles from './index.module.less';
import { RangePickerProps } from 'antd/es/date-picker';
import { DownOutlined, UpOutlined } from '@ant-design/icons';
import { useIndustryTagGroups } from '@/hooks/request/use-industry-tag-groups';

const { RangePicker } = DatePicker;

const disabledDate: RangePickerProps['disabledDate'] = (current) => {
  // Can not select days before today and today
  return current && current >= dayjs().startOf('day');
};

export const VideoCategoryForm = () => {
  const [params, setParams] = useAtom(videoCategoryParamsAtom);
  const [collapseForm, setCollapseForm] = useAtom(collapseFormAtom);

  const { data: industryTagGroups, loading: loadingIndustryTagGroups } = useIndustryTagGroups();

  return (
    <div className={styles.video_category_form_wrapper}>
      <span style={{ marginRight: 15 }}>行业</span>
      <Select
        style={{ marginRight: 30, width: 200 }}
        loading={loadingIndustryTagGroups}
        options={industryTagGroups}
        fieldNames={{
          label: 'name',
          value: 'id',
        }}
        allowClear={false}
        value={params.tagGroupId}
        onChange={(value, option) => {
          if (option && !(option instanceof Array)) {
            setParams({
              ...params,
              industryType: option?.industryType,
              tagGroupId: value,
            });
          }
        }}
      />

      <span style={{ marginRight: 15 }}>时间</span>
      <Select
        value={params.dateRange}
        style={{ marginRight: 5, width: 100 }}
        onChange={(value) => {
          if (value === -1) {
            return;
          }
          setParams({
            ...params,
            dateRange: value,
            dateRangeStart: dayjs().subtract(value, 'day').format('YYYY-MM-DD'),
            dateRangeEnd: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
          });
        }}
        options={[
          { value: 3, label: '近3天' },
          { value: 7, label: '近7天' },
          { value: 30, label: '近30天' },
          { value: -1, label: '自定义' },
        ]}
      />

      <RangePicker
        value={[dayjs(params.dateRangeStart), dayjs(params.dateRangeEnd)]}
        disabledDate={disabledDate}
        onChange={(v) => {
          if (!v) {
            return;
          }

          setParams({
            ...params,
            dateRange: -1,
            dateRangeStart: dayjs(v[0]).format('YYYY-MM-DD'),
            dateRangeEnd: dayjs(v[1]).format('YYYY-MM-DD'),
          });
        }}
      />

      <span
        style={{ float: 'right', color: '#1E5EFF' }}
        onClick={() => {
          setCollapseForm((prev) => !prev);
        }}
      >
        {!collapseForm ? '收起' : '展开'} {!collapseForm ? <UpOutlined /> : <DownOutlined />}
      </span>
    </div>
  );
};
