import { DeleteRole, GetRoleList, RoleData } from '@/services/system';
import { ExclamationCircleOutlined, MoreOutlined } from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-components';
import { Dropdown, Layout, Menu, MenuProps, message, Popconfirm, Tag } from 'antd';
import { useEffect, useState } from 'react';
import styled from 'styled-components';
import AddRoleTable from './addRoleTable';
import RoleTable from './roleTable';

const { Sider, Content } = Layout;

const SiderMenu = styled(Sider)`
  .role-menu .ant-menu-submenu-arrow {
    display: none;
  }
`;

const CustomMenuItem = styled(Menu.Item)`
  span[aria-label='more'] {
    position: absolute;
    right: 5px;
    opacity: 0;
  }
  span[aria-label='more']:hover {
    opacity: 1;
  }
`;

const PortalRoleList = () => {
  const [roleList, setRoleList] = useState<RoleData[]>([]);
  const [roleCode, setRoleCode] = useState('project:admin');
  const roleType = roleList?.filter((item) => item.code === roleCode)?.[0]?.type;

  const fetchRoleList = async () => {
    const res = await GetRoleList();
    setRoleList(res.data || []);
  };

  useEffect(() => {
    fetchRoleList();
  }, []);

  const renderDropdownmenu = (id: string) => {
    return (
      <Menu>
        <Menu.Item key="delete-key">
          <Popconfirm
            title="确定要删除该角色吗?"
            onConfirm={async () => {
              await DeleteRole(id);
              fetchRoleList();
            }}
            onCancel={() => message.error('已取消删除操作')}
            okText="确定"
            cancelText="取消"
          >
            删除
          </Popconfirm>
        </Menu.Item>
      </Menu>
    );
  };

  const renderMenuItem = (key: string, label: string, id: string, count = 0) => {
    return (
      <CustomMenuItem
        icon={
          roleType === 0 ? null : (
            <Dropdown overlay={() => renderDropdownmenu(id)}>
              <MoreOutlined />
            </Dropdown>
          )
        }
        key={key}
      >
        {label} {<span>({count})</span>}
      </CustomMenuItem>
    );
  };

  const onClick: MenuProps['onClick'] = (e) => {
    setRoleCode(e.key);
  };

  return (
    <PageContainer
      header={{
        style: { background: '#ffffff', margin: '10px 40px', borderRadius: '6px' },
      }}
      extra={[<AddRoleTable key="createRole" fetchRoleList={fetchRoleList} />]}
    >
      <Layout>
        <SiderMenu>
          <Menu
            onClick={onClick}
            style={{ width: 200, height: '100%' }}
            defaultSelectedKeys={['project:admin']}
            mode="inline"
            className="role-menu"
          >
            <Menu.ItemGroup key="grp" title="角色列表">
              {roleList.map(({ code, name, id, userCount }) =>
                renderMenuItem(code, name, id, userCount),
              )}
            </Menu.ItemGroup>
          </Menu>
        </SiderMenu>
        <Layout style={{ marginLeft: '10px' }}>
          <Content style={{ background: '#fff', padding: '10px' }}>
            <Tag
              style={{ marginBottom: '10px' }}
              icon={<ExclamationCircleOutlined />}
              color="#55acee"
            >
              内置系统角色不允许删除及修改权限
            </Tag>
            <RoleTable roleCode={roleCode} roleType={roleType} />
          </Content>
        </Layout>
      </Layout>
    </PageContainer>
  );
};

export default PortalRoleList;
