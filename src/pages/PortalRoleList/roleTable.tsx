import { PermissionCode } from '@/access';
import { GetRolePermission, GrantRole } from '@/services/system';
import { ProColumns, ProForm, ProFormRadio, ProTable } from '@ant-design/pro-components';
import { Form, FormListFieldData, message } from 'antd';

type RoleTableProps = {
  data?: string[];
  roleType?: 0 | 1; // 内置角色 0 业务角色 1
  roleCode: string;
};

export type RoleColumnsData = {
  module: string;
  permissions: string;
  name?: string;
};

const generateOptions = (name?: string) => {
  return [
    { label: '所有权限', value: 'all' },
    {
      label: '仅查看',
      value: 'read',
      disabled: name === 'material' || name === 'leads' || name === 'manual-customer-service',
    },
    { label: '无权限', value: 'none' },
  ];
};

const modulePermissionMap: { [key: number]: { [key: string]: string[] } } = {
  0: {
    all: [PermissionCode.MonitorRead, PermissionCode.MonitorWrite],
    read: [PermissionCode.MonitorRead],
  },
  1: {
    all: [PermissionCode.ProjectRead, PermissionCode.ProjectWrite],
    read: [PermissionCode.ProjectRead],
  },
  2: {
    all: [PermissionCode.ManageRead, PermissionCode.ManageWrite],
    read: [PermissionCode.ManageRead],
  },
  3: {
    all: [PermissionCode.MaterialRead, PermissionCode.MaterialWrite],
    read: [PermissionCode.MaterialRead],
  },
  4: {
    all: [PermissionCode.LeadsRead, PermissionCode.LeadsWrite],
    read: [PermissionCode.LeadsRead],
  },
  5: {
    all: [PermissionCode.AuditRead, PermissionCode.AuditWrite],
    read: [PermissionCode.AuditRead],
  },
  6: {
    all: [PermissionCode.ManualCustomerServiceRead, PermissionCode.ManualCustomerServiceWrite],
    read: [PermissionCode.ManualCustomerServiceRead],
  },
};

// name 填写 src/access.ts:7 的权限名称
// 添加了新权限记得修改 src/pages/PortalRoleList/roleTable.tsx:26 与 src/pages/PortalRoleList/roleTable.tsx:18
export const defaultData = [
  {
    module: '监控中心',
    name: 'monitor',
    permissions: '',
  },
  {
    module: '矩阵项目',
    name: 'project',
    permissions: '',
  },
  {
    module: '管理中心',
    name: 'manage',
    permissions: '',
  },
  {
    module: '素材中心',
    name: 'material',
    permissions: '',
  },
  {
    module: '线索映射',
    name: 'leads',
    permissions: '',
  },
  {
    module: '质检审核',
    name: 'audit',
    permissions: '',
  },
  {
    module: '人工客服',
    name: 'manual-customer-service',
    permissions: '',
  },
];

export const permissionToRadio = (data?: string[]) => {
  if (data) {
    const dataTransform = data.map((item) => {
      const [name, permission] = item.split(':');
      return {
        name,
        permission,
      };
    });
    const res = defaultData.map((item) => {
      const findPermission = dataTransform.filter((i) => i.name === item.name);
      const findLen = findPermission.length;
      if (findLen > 1) {
        item.permissions = 'all';
      } else if (findLen === 1) {
        item.permissions = 'read';
      } else {
        item.permissions = 'none';
      }
      return item;
    });
    return res;
  } else {
    return defaultData;
  }
};

export const radioToPermission = (data: string[]) => {
  const rolePermissionArr: string[] = [];
  data.forEach((item: string, index: number) => {
    const modulePermissions = modulePermissionMap[index];
    if (modulePermissions) {
      rolePermissionArr.push(...(modulePermissions[item] || []));
    }
  });
  return rolePermissionArr;
};

export const getColumns = (
  fields: FormListFieldData[],
  roleType = 1,
): Array<ProColumns<RoleColumnsData>> => {
  return [
    {
      title: '功能模块',
      dataIndex: 'module',
      align: 'center',
      width: '50%',
    },
    {
      title: '权限',
      dataIndex: 'permissions',
      align: 'center',
      render: (_, record, index) => {
        return (
          <ProFormRadio.Group
            {...fields[index]}
            options={generateOptions(record.name)}
            disabled={roleType === 0}
          />
        );
      },
    },
  ] as Array<ProColumns<RoleColumnsData>>;
};

const RoleTable = (props: RoleTableProps) => {
  const { roleType, roleCode } = props;

  return (
    <ProForm
      submitter={{
        render: (_, dom) => {
          if (roleType === 0) {
            return false;
          } else {
            return <div style={{ textAlign: 'right', marginTop: '20px' }}>{dom}</div>;
          }
        },
      }}
      params={{ roleCode, timestamp: Math.random() }}
      request={async () => {
        const res = await GetRolePermission({ roleCode });
        const permissionData = res.data;
        const transformData = permissionToRadio(permissionData);
        const rolePermission = transformData.map((item) => item.permissions);
        return { rolePermission };
      }}
      onFinish={async (values) => {
        const roleArr = radioToPermission(values.rolePermission);
        const res = await GrantRole({ roleCode, permissionCodes: roleArr });
        if (res.code === 0) {
          message.success('修改权限成功');
        } else {
          message.error('修改失败');
        }
      }}
    >
      <Form.List name="rolePermission">
        {(fields) => {
          return (
            <ProTable
              search={false}
              columns={getColumns(fields, roleType)}
              dataSource={defaultData}
              rowKey="name"
              toolBarRender={false}
              bordered
              pagination={false}
            />
          );
        }}
      </Form.List>
    </ProForm>
  );
};

export default RoleTable;
