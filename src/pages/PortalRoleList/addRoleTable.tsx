import { CreateRole } from '@/services/system';
import { ModalForm, ProFormText, ProTable } from '@ant-design/pro-components';
import { Button, Form, message } from 'antd';
import { defaultData, getColumns, radioToPermission } from './roleTable';

type AddRoleTableProps = {
  fetchRoleList: () => void;
};

const AddRoleTable = (props: AddRoleTableProps) => {
  const { fetchRoleList } = props;

  return (
    <ModalForm
      trigger={<Button type="primary">添加角色</Button>}
      layout={'horizontal'}
      title="添加角色"
      modalProps={{
        destroyOnHidden: true,
      }}
      onFinish={async (values) => {
        const { rolePermission } = values;
        const rolePermissionArr = radioToPermission(rolePermission);
        const res = await CreateRole({
          name: values.name,
          rolePermission: rolePermissionArr,
        });
        if (res.code === 0) {
          message.success('添加成功');
          fetchRoleList();
        }
        return true;
      }}
      initialValues={{ rolePermission: Array.from({ length: defaultData.length }, () => '') }}
    >
      <ProFormText
        width="md"
        name="name"
        label="角色名称"
        placeholder="请输入"
        rules={[{ required: true, message: '请输入角色名称' }]}
      />
      <Form.List name="rolePermission">
        {(fields) => {
          return (
            <ProTable
              search={false}
              columns={getColumns(fields)}
              dataSource={defaultData}
              rowKey="name"
              toolBarRender={false}
              bordered
              pagination={false}
            />
          );
        }}
      </Form.List>
    </ModalForm>
  );
};

export default AddRoleTable;
