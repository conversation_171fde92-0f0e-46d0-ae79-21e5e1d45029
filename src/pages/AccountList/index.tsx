import { spiderSyncUserTags, spiderSyncUserTagsReloader } from '@/models/store';
import { AccountUser, QueryAccountUsersForPagination } from '@/services/account';
import { AccountPlatformEnum, AccountPlatformKeysMap } from '@/utils/platform';
import { BatchAddObjectTags, UpdateObjectTags } from '@/services/tag';
import { proTableRequestAdapter } from '@/utils';
import { CheckCircleFilled, ExportOutlined } from '@ant-design/icons';
import {
  ActionType,
  PageContainer,
  ProColumns,
  ProFormInstance,
  ProTable,
} from '@ant-design/pro-components';
import { NavLink } from '@umijs/max';
import { Button, message, Row, Space, Tag, Tooltip } from 'antd';
import copy from 'copy-to-clipboard';
import dayjs from 'dayjs';
import { useAtomValue, useSetAtom } from 'jotai';
import { useEffect, useRef, useState } from 'react';
import CommonTagSelectionModal from '../TagList/Components/selectModal';
import SpiderSyncUserTagSelect from '../TagList/Components/spiderSyncUserTagSelect';
import { LSQueryTagIdsKey } from '@/services/constants';

const AccountList: React.FC = () => {
  // const navigation = useNavigate();
  const actionRef = useRef<ActionType>();
  const formRef = useRef<ProFormInstance>();

  const tagGroups = useAtomValue(spiderSyncUserTags);
  const reloadSpiderSyncUserTags = useSetAtom(spiderSyncUserTagsReloader);

  const [currentAccountUser, setCurrentAccountUser] = useState<AccountUser>();
  const [selectingAccountUsers, setSelectingAccountUsers] = useState<AccountUser[]>();
  const [showTagsModal, setShowTagsModal] = useState(false);
  const [current, setCurrent] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(15);
  useEffect(() => {
    reloadSpiderSyncUserTags();
  }, []);

  const columns: Array<ProColumns<AccountUser>> = [
    {
      title: '账号ID',
      dataIndex: 'accountId',
      hideInSearch: false,
      hideInTable: true,
    },
    {
      title: '账号名称',
      dataIndex: 'nickname',
      hideInSearch: false,
      hideInTable: true,
    },
    {
      title: '账号',
      dataIndex: 'nickname',
      hideInSearch: true,
      hideInTable: false,
      render(dom, entity) {
        return (
          <Space>
            <NavLink
              to={`/monitor/account/user-list/detail/${AccountPlatformKeysMap[entity.platform]}/${
                entity.accountId
              }`}
            >
              <Row align="middle">
                <img
                  src={entity.avatar}
                  alt={entity.nickname}
                  width={24}
                  height={24}
                  style={{ marginRight: 8 }}
                  referrerPolicy="no-referrer"
                />
                <span className="block text-sm">{entity.nickname}</span>
                {entity.blueVipFlag === 1 && (
                  <Tooltip title={entity.blueVipReason}>
                    <CheckCircleFilled style={{ marginLeft: 4 }} />
                  </Tooltip>
                )}
              </Row>
            </NavLink>
            <Tooltip title="复制账号ID">
              <Button
                type="default"
                size="small"
                onClick={() => {
                  if (entity.accountId) {
                    copy(entity.accountId);
                    message.success('已复制到剪切板');
                  }
                }}
              >
                ID
              </Button>
            </Tooltip>
          </Space>
        );
      },
    },
    {
      title: '账号标签',
      dataIndex: 'tagIds',
      hideInSearch: false,
      valueType: 'text',
      renderFormItem: () => {
        return <SpiderSyncUserTagSelect data={tagGroups} />;
      },
      render: (_, entity) => {
        return (
          <div
            onClick={() => {
              setCurrentAccountUser(entity);
              setSelectingAccountUsers(undefined);
              setShowTagsModal(true);
            }}
          >
            {entity.tags && entity.tags.length > 0 && (
              <Space>
                {entity.tags.map((tag) => {
                  return <Tag key={tag.id}>{tag.name}</Tag>;
                })}
              </Space>
            )}
            {(!entity.tags || entity.tags.length === 0) && (
              <Button size="small" type="dashed">
                +
              </Button>
            )}
          </div>
        );
      },
    },
    {
      title: '账号平台',
      dataIndex: 'platform',
      hideInSearch: false,
      hideInTable: false,
      valueEnum: AccountPlatformEnum,
    },
    {
      title: '粉丝数',
      dataIndex: 'followerCount',
      hideInSearch: true,
      hideInTable: false,
      valueType: (entity) => (entity.followerCount !== null ? 'digit' : 'text'),
      renderText: (_, entity) => {
        return entity.followerCount !== null ? entity.followerCount : entity.followerCountStr;
      },
    },
    {
      title: '获赞数',
      dataIndex: 'totalFavorited',
      hideInSearch: true,
      hideInTable: false,
      valueType: (entity) => (entity.totalFavorited !== null ? 'digit' : 'text'),
      renderText: (_, entity) =>
        entity.totalFavorited !== null ? entity.totalFavorited : entity.totalFavoritedStr,
    },
    {
      title: '作品数',
      dataIndex: 'postCount',
      hideInSearch: true,
      hideInTable: false,
      valueType: (entity) => (entity.postCount !== null ? 'digit' : 'text'),
      renderText: (_, entity) =>
        entity.postCount !== null ? entity.postCount : entity.postCountStr,
    },
    {
      title: '直播状态',
      dataIndex: 'roomId',
      hideInSearch: true,
      hideInTable: false,
      render(dom, entity) {
        if (!entity.liveInfo) {
          return '-';
        }

        return (
          <Tooltip title="点击跳转抖音网页查看">
            <Button
              type="link"
              size="small"
              onClick={() => {
                window.open(`https://live.douyin.com/${entity.liveInfo?.webRid}`);
              }}
            >
              {`直播中(${entity.liveInfo.roomUserCount}人)`} <ExportOutlined />
            </Button>
          </Tooltip>
        );
      },
    },
    {
      title: '直播状态',
      dataIndex: 'liveStatus',
      hideInSearch: false,
      hideInTable: true,
      valueEnum: {
        1: {
          text: '直播中',
        },
        0: {
          text: '未直播',
        },
      },
    },
    {
      title: '近7天直播',
      dataIndex: 'recentLiveNum',
      hideInSearch: true,
      hideInTable: false,
      renderText(dom, entity) {
        if (entity.recentLiveNum) {
          return `${entity.recentLiveNum}场`;
        }

        return '-';
      },
    },
    {
      title: '直播录制',
      dataIndex: 'liveReplayStatus',
      hideInSearch: true,
      hideInTable: false,
      renderText(text, record) {
        if (!record.syncInfo) {
          return null;
        }

        return record.syncInfo.liveReplayStatus === 1 ? '自动' : null;
      },
    },
    {
      title: '同步状态',
      dataIndex: 'lastSyncTime',
      hideInSearch: true,
      hideInTable: false,
      valueType: 'select',
      valueEnum: {
        正常: {
          text: '正常',
          color: 'green',
        },
        错误: {
          text: '错误',
          color: 'red',
        },
      },
      renderText(_, record) {
        const { syncInfo } = record;
        if (!syncInfo) {
          return '-';
        }

        return syncInfo.lastSyncPostStatus === -1 || syncInfo.lastSyncProfileStatus === -1
          ? '错误'
          : '正常';
      },
    },
    {
      title: '同步时间',
      dataIndex: 'last_sync_time',
      hideInSearch: true,
      hideInTable: false,
      renderText(_, record) {
        const { syncInfo } = record;
        if (!syncInfo) {
          return '-';
        }

        return dayjs(syncInfo.lastSyncTime).format('YYYY-MM-DD HH:mm');
      },
    },
    {
      title: '下次同步时间',
      dataIndex: 'next_sync_time',
      hideInSearch: true,
      hideInTable: false,
      renderText(_, record) {
        const { syncInfo } = record;
        if (!syncInfo) {
          return '-';
        }

        return dayjs(syncInfo.nextSyncTime).format('YYYY-MM-DD HH:mm');
      },
    },
    // {
    //   title: '操作',
    //   dataIndex: 'action',
    //   hideInSearch: true,
    //   hideInTable: false,
    //   render() {
    //     return (
    //       <Space>
    //         <a
    //           onClick={() => {
    //             // navigation(`/car/car-lib/series/${entity.id}`);
    //           }}
    //         >
    //           查看
    //         </a>
    //       </Space>
    //     );
    //   },
    // },
  ];

  return (
    <PageContainer extra={[]}>
      <ProTable<AccountUser>
        formRef={formRef}
        actionRef={actionRef}
        scroll={{ x: 'max-content' }}
        columns={columns}
        rowKey="id"
        toolBarRender={false}
        bordered
        size="small"
        dateFormatter="string"
        pagination={{
          defaultPageSize: 15,
          current,
          pageSize,
          hideOnSinglePage: false,
          showSizeChanger: true,
        }}
        search={{ filterType: 'query', defaultCollapsed: false }}
        request={(params, sorter, filter) => {
          setCurrent(params.current as number);
          setPageSize(params.pageSize as number);
          localStorage.setItem(LSQueryTagIdsKey, JSON.stringify({ tagIds: params.tagIds }));
          return proTableRequestAdapter(params, sorter, filter, QueryAccountUsersForPagination);
        }}
        rowSelection={{
          type: 'checkbox',
        }}
        tableAlertRender={({ selectedRowKeys, onCleanSelected }) => {
          return (
            <Space size={24}>
              <span>
                已选 {selectedRowKeys.length} 项
                <a style={{ marginInlineStart: 8 }} onClick={onCleanSelected}>
                  取消选择
                </a>
              </span>
            </Space>
          );
        }}
        tableAlertOptionRender={({ selectedRows }) => {
          return (
            <Space size={16}>
              <a
                onClick={() => {
                  if (selectedRows.length) {
                    setCurrentAccountUser(undefined);
                    setSelectingAccountUsers(selectedRows);
                    setShowTagsModal(true);
                  }
                }}
              >
                批量添加标签
              </a>
            </Space>
          );
        }}
      />
      <CommonTagSelectionModal
        visible={showTagsModal}
        setVisible={setShowTagsModal}
        mode="multiple"
        defaultValues={(currentAccountUser && currentAccountUser.tags) || []}
        onFinish={(values) => {
          console.log(values);
          if (currentAccountUser) {
            const hide = message.loading('请求中');
            UpdateObjectTags({
              type: 1,
              objectId: currentAccountUser.accountId,
              tagIds: values.map((i) => i.id),
            }).then((res) => {
              hide();
              if (res.code === 0) {
                actionRef.current?.reload();
              } else {
                message.error(res.msg);
              }
            });
          } else if (selectingAccountUsers && selectingAccountUsers.length) {
            const hide = message.loading('请求中');
            BatchAddObjectTags({
              type: 1,
              objectIds: selectingAccountUsers.map((i) => i.accountId),
              tagIds: values.map((i) => i.id),
            }).then((res) => {
              hide();
              if (res.code === 0) {
                actionRef.current?.reload();
                actionRef.current?.clearSelected?.();
              } else {
                message.error(res.msg);
              }
            });
          }
        }}
        data={tagGroups}
      />
    </PageContainer>
  );
};

export default AccountList;
