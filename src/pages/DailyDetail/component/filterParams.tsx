import useGetCascaderOptions from '@/hooks/useGetCascaderOptions';
import { GetAnchorImageViolationSelectDaily } from '@/services/quality';
import { GetQualityTypeSelectDaily } from '@/services/unauth';
import { ClassifyType } from '@/utils/common';
import { scrollAntdMobileCalendar } from '@/utils/dailyUtils';
import { useRequest, useSearchParams } from '@umijs/max';
import { CalendarPicker, Cascader, Popup } from 'antd-mobile';
import { DownFill, FilterOutline } from 'antd-mobile-icons';
import CheckList, { CheckListValue } from 'antd-mobile/es/components/check-list';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import styled from 'styled-components';
import { FilterItem } from './styles';

const FilterParams = styled.div`
  display: flex;
  flex-direction: row;
  gap: 0.625rem;
  width: 22.5rem;
  padding-inline-end: 0.625rem;
  margin-block: 0.6rem;
`;

const today = new Date();
const max = new Date(today.setDate(today.getDate() - 1));
const min = new Date(today.setMonth(today.getMonth() - 6));

type FilterParamsComponentProps = {
  classifyType: ClassifyType;
  setClassifyType: React.Dispatch<React.SetStateAction<ClassifyType>>;
  setRangeDate: React.Dispatch<React.SetStateAction<string[] | undefined>>;
  setFieldValueStr: React.Dispatch<React.SetStateAction<string | undefined>>;
  showQualityFilter?: boolean;
  liveAfkLevel?: CheckListValue | null;
  setLiveAfkLevel?: React.Dispatch<React.SetStateAction<CheckListValue | null>>;
  monitorType?: CheckListValue[] | undefined;
  setMonitorType?: React.Dispatch<React.SetStateAction<CheckListValue[] | undefined>>;
  afkState?: boolean;
};

const afkLevel = [
  {
    label: '严重空播挂播',
    value: 1,
  },
  {
    label: '轻微空播挂播',
    value: 2,
  },
  {
    label: '正常',
    value: 3,
  },
];

const FilterParamsComponent = (props: FilterParamsComponentProps) => {
  const {
    classifyType,
    setClassifyType,
    setRangeDate,
    setFieldValueStr,
    showQualityFilter,
    liveAfkLevel,
    setLiveAfkLevel,
    monitorType,
    setMonitorType,
    afkState,
  } = props;
  const [calendarVisible, setCalendarVisible] = useState(false);
  const [treeSelectVisible, setTreeSelectVisible] = useState(false);
  const [showSelectRes, setShowSelectRes] = useState<string | null>(null);
  const [cascaderValue, setCascaderValue] = useState<CheckListValue[]>([]);
  const [searchParams] = useSearchParams();
  const date = searchParams.get('date');
  const payload = searchParams.get('payload');
  const cascaderOptions = useGetCascaderOptions(payload);
  const defaultDate = date ? new Date(date) : null;
  const [showRangeDate, setShowRangeDate] = useState<[Date, Date] | null>(
    defaultDate ? [defaultDate, defaultDate] : null,
  );
  const [afkLevelVisible, setAfkLevelVisible] = useState(false);
  const [monitorTypeVisible, setMonitorTypeVisible] = useState(false);
  const { data: qualityData } = useRequest(() => GetQualityTypeSelectDaily({ payload }));
  const { data: anchorImageViolationData } = useRequest(() =>
    GetAnchorImageViolationSelectDaily({ payload }),
  );

  const showQualityData = qualityData?.filter((item) => item.disabled === false);

  const showAnchorImageViolation = anchorImageViolationData?.filter(
    (item) => item.disabled === false,
  );

  useEffect(() => {
    showRangeDate && setRangeDate(showRangeDate.map((item) => dayjs(item).format('YYYY-MM-DD')));
  }, [showRangeDate]);

  useEffect(() => {
    const field = cascaderValue.at(-1);
    setFieldValueStr(field as string);
  }, [cascaderValue]);

  // antd-mobile组件暂时没有实现滚动到默认选中的日期 自行滚动下
  useEffect(() => {
    if (calendarVisible) {
      scrollAntdMobileCalendar();
    }
  }, [calendarVisible]);

  return (
    <>
      <FilterParams>
        <FilterItem
          onClick={() => {
            setCalendarVisible(true);
          }}
        >
          日期
          <DownFill style={{ fontSize: '0.5rem' }} />
        </FilterItem>
        <FilterItem
          onClick={() => {
            setTreeSelectVisible(true);
          }}
        >
          地区
          <FilterOutline />
          <span>{showSelectRes}</span>
        </FilterItem>
        <FilterItem
          onClick={() => {
            setClassifyType(
              classifyType === ClassifyType.Account ? ClassifyType.Team : ClassifyType.Account,
            );
          }}
        >
          {classifyType === ClassifyType.Account ? '按账号' : '按门店'}
        </FilterItem>
        {showQualityFilter && (
          <>
            <FilterItem onClick={() => setMonitorTypeVisible(true)}>质检类型</FilterItem>
            {afkState && (
              <FilterItem
                onClick={() => {
                  setAfkLevelVisible(true);
                }}
              >
                疑似空播挂播
              </FilterItem>
            )}
          </>
        )}
      </FilterParams>
      <CalendarPicker
        visible={calendarVisible}
        selectionMode="range"
        max={max}
        min={min}
        onClose={() => setCalendarVisible(false)}
        onMaskClick={() => setCalendarVisible(false)}
        defaultValue={showRangeDate}
        onConfirm={(val: [Date, Date] | null) => setShowRangeDate(val)}
      />
      <Cascader
        options={cascaderOptions}
        visible={treeSelectVisible}
        onClose={() => {
          setTreeSelectVisible(false);
        }}
        value={cascaderValue}
        onConfirm={(value: CheckListValue[], extend) => {
          setCascaderValue(value);
          const extendItems = extend.items;
          if (extendItems.every((item) => item === null)) {
            setShowSelectRes('未选择');
          } else {
            const res = extendItems.map((item) => item?.label ?? '未选择').join('-');
            setShowSelectRes(res);
          }
        }}
      />
      <Popup
        visible={afkLevelVisible}
        onMaskClick={() => {
          setAfkLevelVisible(false);
        }}
        destroyOnClose
      >
        <CheckList
          defaultValue={liveAfkLevel ? [liveAfkLevel] : []}
          onChange={(val) => {
            setLiveAfkLevel?.(val[0]);
            setAfkLevelVisible(false);
          }}
        >
          {afkLevel.map(({ label, value }) => (
            <CheckList.Item key={label} value={value}>
              {label}
            </CheckList.Item>
          ))}
        </CheckList>
      </Popup>

      <Popup
        visible={monitorTypeVisible}
        onMaskClick={() => {
          setMonitorTypeVisible(false);
        }}
        destroyOnClose
        bodyStyle={{ height: '80vh' }}
      >
        <div style={{ height: '80vh', overflowY: 'auto' }}>
          <CheckList
            defaultValue={monitorType}
            multiple
            onChange={(val) => {
              setMonitorType?.(val);
            }}
          >
            {showQualityData?.map((item) => (
              <CheckList.Item key={item.name} value={item.value}>
                {item.name}
              </CheckList.Item>
            ))}
            {showAnchorImageViolation?.map((item) => (
              <CheckList.Item key={item.name} value={item.value}>
                {item.name}
              </CheckList.Item>
            ))}
          </CheckList>
        </div>
      </Popup>
    </>
  );
};

export default FilterParamsComponent;
