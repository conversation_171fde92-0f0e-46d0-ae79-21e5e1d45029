import UseInfiniteScroll from '@/hooks/useInfiniteScroll';
import { ChatViolationTeamItem } from '@/services/daily';
import { ChatViolationTeamDaily } from '@/services/unauth';
import {
  Column,
  DataContain,
  DataContainItem,
  DetailCard,
  ItemContent,
  ItemTitle,
  Title,
} from './styles';

type TeamCardDetailProps = {
  value: ChatViolationTeamItem;
};

const TeamCardDetail = ({ value }: TeamCardDetailProps) => {
  return (
    <DetailCard>
      <Column>
        <Title>{value.teamName}</Title>
        <DataContain>
          <DataContainItem>
            <ItemTitle>{value.chatRound}</ItemTitle>
            <ItemContent>会话人数</ItemContent>
          </DataContainItem>
          <DataContainItem>
            <ItemTitle>{value.receiveMsgCount}</ItemTitle>
            <ItemContent>接收消息数</ItemContent>
          </DataContainItem>
          <DataContainItem>
            <ItemTitle>{value.replayMsgCount}</ItemTitle>
            <ItemContent>回复消息数</ItemContent>
          </DataContainItem>
          <DataContainItem>
            <ItemTitle>{value.notifyCount}</ItemTitle>
            <ItemContent>被提醒次数</ItemContent>
          </DataContainItem>
        </DataContain>
      </Column>
    </DetailCard>
  );
};

type ChatCardByTeamProps = {
  rangeDate?: string[];
  fieldValueStr?: string;
  keyword?: string;
};

const ChatCardByTeam = (props: ChatCardByTeamProps) => {
  const { rangeDate, fieldValueStr, keyword } = props;
  return (
    <UseInfiniteScroll<ChatViolationTeamItem>
      fetchFn={ChatViolationTeamDaily}
      rangeDate={rangeDate}
      fieldValueStr={fieldValueStr}
      keyword={keyword}
    >
      {(data) => (
        <>
          {data.map((item, index: number) => (
            <TeamCardDetail key={`card-${index}`} value={item} />
          ))}
        </>
      )}
    </UseInfiniteScroll>
  );
};

export default ChatCardByTeam;
