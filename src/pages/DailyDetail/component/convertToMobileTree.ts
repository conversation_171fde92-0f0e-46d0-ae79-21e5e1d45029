import { DepthList, TreeData } from '@/services/team';

export type TreeNodeMobile = {
  label: string;
  value: string;
  key: string;
  children?: TreeNodeMobile[];
};

const treeData: TreeNodeMobile[] = [];
const firstSet = new Set();
const secondSet = new Set();
const thirdSet = new Set();
const fourthSet = new Set();
const fifthSet = new Set();
const teamCodeSet = new Set();
let maxDepth: number;

function getLevelSet(depth: number) {
  switch (depth) {
    case 0:
      return firstSet;
    case 1:
      return secondSet;
    case 2:
      return thirdSet;
    case 3:
      return fourthSet;
    case 4:
      return fifthSet;
  }
}

function findNodePush(
  levels: string[],
  depth: number,
  key: string,
  name: string,
  fieldId: string | number,
  teamCode = '',
) {
  let parentNode: TreeNodeMobile | undefined;
  let findNode: TreeNodeMobile | undefined;

  levels.forEach((level, index) => {
    if (index === 0) {
      findNode = treeData.find((item) => item.label === level);
    } else {
      findNode = parentNode?.children?.find((item) => item.label === level);
    }
    if (findNode) {
      // 找到节点记录父节点
      parentNode = findNode;
    }
  });

  if (!findNode && depth === 0) {
    treeData.push({
      label: name,
      value: `${name}-${fieldId}`,
      key: key,
      children: [],
    });
    return;
  }
  if (!findNode && depth < maxDepth && depth > 0 && !getLevelSet(depth)?.has(key)) {
    parentNode?.children?.push({
      label: name,
      value: `${name}-${fieldId}`,
      key: key,
      children: [],
    });
    return;
  }
  if (depth === maxDepth && !teamCodeSet.has(teamCode)) {
    teamCodeSet.add(teamCode);
    parentNode?.children?.push({
      label: name,
      value: `${name}-${fieldId}`,
      key: teamCode,
    });
    return;
  }
}

function transRowData(item: DepthList) {
  const teamCode = item.teamCode;
  const rowData = item.projectTeamFieldValueDTOList;

  const levels = new Array(maxDepth + 1).fill('');

  rowData.forEach((item) => {
    if (item.bizType === 2) {
      levels[item.depth - 1] = item.fieldValue;
      const filterLevels = levels.filter(Boolean);
      const key = filterLevels.join('-');
      if (!getLevelSet(item.depth - 1)?.has(key)) {
        findNodePush(filterLevels, item.depth - 1, key, item.fieldValue, item.fieldId);
        getLevelSet(item.depth - 1)?.add(key);
      }
    } else if (item.bizType === 1 && item.depth === 0) {
      const filterLevels = levels.filter(Boolean);
      findNodePush(
        filterLevels,
        maxDepth,
        item.fieldValue,
        item.fieldValue,
        item.fieldId,
        teamCode,
      );
    }
  });
}

export function convertToMobileTree(data: TreeData) {
  // 先排好序 最大层在上面 最底层在最后
  maxDepth = data?.maxDepth || 0;
  data.depthList.forEach((item) => {
    item.projectTeamFieldValueDTOList.sort((a, b) => {
      if (a.bizType === 1 && b.bizType !== 1) {
        return 1;
      } else if (a.bizType !== 1 && b.bizType === 1) {
        return -1;
      } else if (a.bizType === 1 && b.bizType === 1) {
        return 0;
      } else {
        return a.depth - b.depth;
      }
    });
    transRowData(item);
  });

  return treeData;
}
