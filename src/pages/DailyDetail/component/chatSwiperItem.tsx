import { ClassifyType } from '@/utils/common';
import { useState } from 'react';
import ChatCardByAccount from './chatCardByAccount';
import ChatCardByTeam from './chatCardByTeam';
import FilterParamsComponent from './filterParams';
import { DailyDetailBody } from './styles';

type ChatSwiperItemProps = {
  date: string | null;
  keyword?: string;
};

const ChatSwiperItem = (props: ChatSwiperItemProps) => {
  const { keyword } = props;
  const [classifyType, setClassifyType] = useState(ClassifyType.Account);
  const [rangeDate, setRangeDate] = useState<string[] | undefined>(undefined);
  const [fieldValueStr, setFieldValueStr] = useState<string | undefined>(undefined);

  return (
    <div style={{ paddingInline: '0.625rem' }}>
      <FilterParamsComponent
        classifyType={classifyType}
        setClassifyType={setClassifyType}
        setRangeDate={setRangeDate}
        setFieldValueStr={setFieldValueStr}
      />
      <DailyDetailBody id="scrollableDiv">
        {classifyType === ClassifyType.Account ? (
          <ChatCardByAccount
            rangeDate={rangeDate}
            fieldValueStr={fieldValueStr}
            keyword={keyword}
          />
        ) : (
          <ChatCardByTeam rangeDate={rangeDate} fieldValueStr={fieldValueStr} keyword={keyword} />
        )}
      </DailyDetailBody>
    </div>
  );
};

export default ChatSwiperItem;
