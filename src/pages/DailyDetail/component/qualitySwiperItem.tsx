import useLiveAfkFG from '@/hooks/fg/useLiveAfkFG';
import { ClassifyType } from '@/utils/common';
import { useSearchParams } from '@umijs/max';
import { CheckListValue } from 'antd-mobile/es/components/check-list';
import { useState } from 'react';
import FilterParamsComponent from './filterParams';
import QualityCardByAccount from './qualityCardByAccount';
import QualityCardByTeam from './qualityCardByTeam';
import { DailyDetailBody, QualityTag } from './styles';

type QualitySwiperItemProps = {
  date: string | null;
  keyword?: string;
};

export const renderQualityTag = (level?: number) => {
  switch (level) {
    case 1:
      return (
        <QualityTag $bgColor="#fff2f0" $textColor="#ff4d4f">
          严重空播挂播
        </QualityTag>
      );
    case 2:
      return (
        <QualityTag $bgColor="#fbebe2" $textColor="#FF7533">
          轻微空播挂播
        </QualityTag>
      );
    default:
      return null;
  }
};

const QualitySwiperItem = (props: QualitySwiperItemProps) => {
  const { keyword } = props;
  const [searchParams] = useSearchParams();
  const payload = searchParams.get('payload');
  const [classifyType, setClassifyType] = useState(ClassifyType.Account);
  const [rangeDate, setRangeDate] = useState<string[] | undefined>(undefined);
  const [fieldValueStr, setFieldValueStr] = useState<string | undefined>(undefined);
  const [liveAfkLevel, setLiveAfkLevel] = useState<CheckListValue | null>(null);
  const [monitorType, setMonitorType] = useState<CheckListValue[] | undefined>();
  const afkState = useLiveAfkFG(payload, true);

  return (
    <div style={{ paddingInline: '0.625rem' }}>
      <FilterParamsComponent
        classifyType={classifyType}
        setClassifyType={setClassifyType}
        setRangeDate={setRangeDate}
        setFieldValueStr={setFieldValueStr}
        showQualityFilter
        liveAfkLevel={liveAfkLevel}
        setLiveAfkLevel={setLiveAfkLevel}
        monitorType={monitorType}
        setMonitorType={setMonitorType}
        afkState={afkState}
      />
      <DailyDetailBody id="scrollableDiv">
        {classifyType === ClassifyType.Account ? (
          <QualityCardByAccount
            rangeDate={rangeDate}
            fieldValueStr={fieldValueStr}
            keyword={keyword}
            liveAfkLevel={liveAfkLevel}
            monitorType={monitorType}
            afkState={afkState}
          />
        ) : (
          <QualityCardByTeam
            rangeDate={rangeDate}
            fieldValueStr={fieldValueStr}
            keyword={keyword}
            liveAfkLevel={liveAfkLevel}
            monitorType={monitorType}
            afkState={afkState}
          />
        )}
      </DailyDetailBody>
    </div>
  );
};

export default QualitySwiperItem;
