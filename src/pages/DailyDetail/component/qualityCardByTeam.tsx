import UseInfiniteScroll from '@/hooks/useInfiniteScroll';
import { QualityExViiolationTeamItem } from '@/services/daily';
import { QualityExViolationTeamDaily } from '@/services/unauth';
import { CheckListValue } from 'antd-mobile/es/components/check-list';
import { renderQualityTag } from './qualitySwiperItem';
import {
  Column,
  DataContain,
  DataContainItem,
  DetailCard,
  HeaderContent,
  ItemContent,
  ItemTitle,
  MonitorContent,
  MonitorTitle,
  MonitorWords,
  Title,
} from './styles';

type TeamCardDetailProps = {
  value: QualityExViiolationTeamItem;
  afkState?: boolean;
};

const TeamCardDetail = ({ value, afkState }: TeamCardDetailProps) => {
  return (
    <DetailCard>
      <Column>
        <HeaderContent>
          <Title>{value.teamName}</Title>
          {afkState && renderQualityTag(value.liveAfkLevel)}
        </HeaderContent>
        <DataContain>
          <DataContainItem>
            <ItemTitle>{value.violationCount}</ItemTitle>
            <ItemContent>违规总次数</ItemContent>
          </DataContainItem>
          <DataContainItem>
            <ItemTitle>{value.liveViolationCount}</ItemTitle>
            <ItemContent>直播违规次数</ItemContent>
          </DataContainItem>
          <DataContainItem>
            <ItemTitle>{value.postViolationCount}</ItemTitle>
            <ItemContent>作品违规次数</ItemContent>
          </DataContainItem>
        </DataContain>
        {afkState && (
          <DataContain style={{ justifyContent: 'space-around' }}>
            <DataContainItem>
              <ItemTitle>{value.liveSeriousAfkCount}</ItemTitle>
              <ItemContent>严重空播挂播场次</ItemContent>
            </DataContainItem>
            <DataContainItem>
              <ItemTitle>{value.liveMinorAfkCount}</ItemTitle>
              <ItemContent>轻微空播挂播场次</ItemContent>
            </DataContainItem>
          </DataContain>
        )}
      </Column>
      <MonitorWords>
        <MonitorTitle>质检类型：</MonitorTitle>
        <MonitorContent>{value.monitoringNameStr}</MonitorContent>
        <MonitorContent>{value.liveAnchorImageViolationStr}</MonitorContent>
      </MonitorWords>
      <MonitorWords>
        <MonitorTitle>触发监控词：</MonitorTitle>
        <MonitorContent>{value.violationMonitoringWord}</MonitorContent>
      </MonitorWords>
    </DetailCard>
  );
};

type QualityCardByTeamProps = {
  rangeDate?: string[];
  fieldValueStr?: string;
  keyword?: string;
  liveAfkLevel?: CheckListValue | null;
  monitorType?: CheckListValue[];
  afkState?: boolean;
};

const QualityCardByTeam = (props: QualityCardByTeamProps) => {
  const { rangeDate, fieldValueStr, keyword, liveAfkLevel, monitorType, afkState } = props;

  return (
    <UseInfiniteScroll<QualityExViiolationTeamItem>
      fetchFn={QualityExViolationTeamDaily}
      rangeDate={rangeDate}
      fieldValueStr={fieldValueStr}
      keyword={keyword}
      liveAfkLevel={liveAfkLevel}
      monitorType={monitorType}
    >
      {(data) => (
        <>
          {data.map((item, index: number) => (
            <TeamCardDetail key={`card-${index}`} value={item} afkState={afkState} />
          ))}
        </>
      )}
    </UseInfiniteScroll>
  );
};

export default QualityCardByTeam;
