import SwitchBtnImg from '@/assets/switch.png';
import PlatformSwitchMobile from '@/components/platformSwitchMobile';
import { history, useLocation, useSearchParams } from '@umijs/max';
import { useTitle } from 'ahooks';
import { Input, Swiper, SwiperRef, Tabs } from 'antd-mobile';
import { LeftOutline, SearchOutline } from 'antd-mobile-icons';
import { sortBy } from 'lodash-es';
import { useRef, useState } from 'react';
import styled from 'styled-components';
import ChatSwiperItem from './component/chatSwiperItem';
import QualitySwiperItem from './component/qualitySwiperItem';

const NavBar = styled.div`
  width: 100vw;
  height: 2.75rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 1rem;
  font-weight: 500;
  padding-inline: 1.25rem;
`;

const TabsContainer = styled.div`
  width: 100vw;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 2.25rem;

  .tabs {
    width: 50%;
  }

  .search {
    width: 15%;
  }
`;

const CustomTabs = styled(Tabs)`
  .adm-tabs-tab {
    font-size: 0.9rem;
  }
`;

const BodyDiv = styled.div`
  flex: 1;
  background-color: #f5f6fa;
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: relative;
`;

const SearchContainer = styled.div`
  display: flex;
  flex: 1;
  flex-direction: row;
  width: 100vw;
  gap: 1rem;
  justify-content: center;
  align-items: center;
  padding-inline: 1rem;
`;

const SearchDiv = styled.div`
  border-radius: 4px;
  background: #f3f3f3;
  flex: 1;
`;

const SwitchBtn = styled.img`
  position: absolute;
  bottom: 20vh;
  right: 10vw;
  width: 1.5rem;
  height: 1.5rem;
`;

const VerticalScreen = ({
  setIslandscapeScreen,
}: {
  setIslandscapeScreen: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
  useTitle('提醒日报详情');
  const swiperRef = useRef<SwiperRef>(null);
  const [enableSearch, setEnableSearch] = useState<boolean>(false);
  const [inputValue, setInputValue] = useState<string>('');
  const [keyword, setKeyword] = useState<string | undefined>(undefined);
  const [searchParams] = useSearchParams();
  const date = searchParams.get('date');
  const type = searchParams.get('type');
  const payload = searchParams.get('payload');
  const typeArr = type?.split(',');
  const tabItems: Array<{ key: string; title: string }> =
    typeArr?.map((item) => {
      return {
        key: item,
        title: item === 'chat' ? '私信详情' : '质检详情',
      };
    }) || [];
  const location = useLocation();
  const activeKey = (location?.state as string) || 'chat';
  const [activeIndex, setActiveIndex] = useState(
    typeArr && typeArr?.length > 1 ? (activeKey === 'chat' ? 0 : 1) : 0,
  );

  const sortTabItems = sortBy(tabItems, 'key');
  const contentRef = useRef(null);

  const renderSwiperBody = () => {
    if (sortTabItems.length === 1) {
      const item = sortTabItems[0];
      return (
        <>
          <BodyDiv>
            {item.key === 'chat' ? (
              <ChatSwiperItem date={date} keyword={keyword} />
            ) : (
              <QualitySwiperItem date={date} keyword={keyword} />
            )}
          </BodyDiv>
          <SwitchBtn src={SwitchBtnImg} alt="switch" onClick={() => setIslandscapeScreen(true)} />
        </>
      );
    } else if (sortTabItems.length > 1) {
      return (
        <>
          <Swiper
            direction="horizontal"
            loop
            indicator={() => null}
            ref={swiperRef}
            defaultIndex={activeIndex}
            onIndexChange={(index) => {
              // 切换tab把keyword重置
              setInputValue('');
              setKeyword(undefined);
              setActiveIndex(index);
            }}
            style={{
              flex: 1,
              backgroundColor: '#f5f6fa',
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              position: 'relative',
            }}
          >
            <Swiper.Item>
              <ChatSwiperItem date={date} keyword={keyword} />
            </Swiper.Item>
            <Swiper.Item>
              <QualitySwiperItem date={date} keyword={keyword} />
            </Swiper.Item>
          </Swiper>
          <SwitchBtn src={SwitchBtnImg} alt="switch" onClick={() => setIslandscapeScreen(true)} />
        </>
      );
    } else {
      return null;
    }
  };

  return (
    <div style={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      <NavBar>
        <LeftOutline onClick={() => history.push(`/daily${window.location.search}`)} />
        提醒日报
        <div />
      </NavBar>
      <TabsContainer>
        {enableSearch ? (
          <SearchContainer>
            <SearchDiv>
              <Input
                placeholder="请输入经销商或者账号名称/ID"
                value={inputValue}
                clearable
                onClear={() => {
                  setInputValue('');
                  setKeyword(undefined);
                }}
                onChange={(val) => {
                  setInputValue(val);
                }}
                onEnterPress={(e) => {
                  // @ts-ignore
                  setKeyword(e.target.value);
                }}
                style={{ '--font-size': '14px', '--text-align': 'center' }}
              />
            </SearchDiv>
            <div
              onClick={() => {
                setKeyword(inputValue);
                setEnableSearch(false);
              }}
            >
              确定
            </div>
            <div onClick={() => setEnableSearch(false)}>取消</div>
          </SearchContainer>
        ) : (
          <>
            <div className="search" />
            <div className="tabs">
              <CustomTabs
                activeKey={sortTabItems[activeIndex].key}
                onChange={(key) => {
                  const index = sortTabItems.findIndex((item) => item.key === key);
                  setActiveIndex(index);
                  swiperRef.current?.swipeTo(index);
                  // 切换tab把keyword重置
                  setInputValue('');
                  setKeyword(undefined);
                }}
              >
                {sortTabItems.map((item) => (
                  <Tabs.Tab title={item.title} key={item.key} />
                ))}
              </CustomTabs>
            </div>
            <SearchOutline
              className="search"
              style={{ color: keyword ? '#1677ff' : 'none' }}
              onClick={() => setEnableSearch(true)}
            />
          </>
        )}
      </TabsContainer>
      {renderSwiperBody()}
      <PlatformSwitchMobile payload={payload} dragContentRef={contentRef} />
    </div>
  );
};

export default VerticalScreen;
