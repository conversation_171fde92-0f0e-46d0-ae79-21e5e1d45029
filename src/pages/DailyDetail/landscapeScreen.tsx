import styled from 'styled-components';

import SwitchBtnImg from '@/assets/switch.png';
import useGetCascaderOptions from '@/hooks/useGetCascaderOptions';
import useTabKeySearchParams from '@/hooks/useTabKeySearchParams';
import {
  ChatViolationAccountDaily,
  ChatViolationTeamDaily,
  GetQualityTypeSelectDaily,
  QualityExViolationAccountDaily,
  QualityExViolationTeamDaily,
} from '@/services/unauth';
import { ClassifyType } from '@/utils/common';
import { renderColumns, scrollAntdMobileCalendar } from '@/utils/dailyUtils';
import { ActionType, ProTable } from '@ant-design/pro-components';
import { useRequest, useSearchParams } from '@umijs/max';
import { Tabs } from 'antd';
import { CalendarPicker, Cascader, Input, Modal, Popup } from 'antd-mobile';
import { DownFill, FilterOutline, SearchOutline } from 'antd-mobile-icons';
import CheckList, { CheckListValue } from 'antd-mobile/es/components/check-list';
import dayjs from 'dayjs';
import { useEffect, useRef, useState } from 'react';
import { FilterItem } from './component/styles';
import { proTableRequestAdapterParamsAndData } from '@/utils';
import { GetAnchorImageViolationSelectDaily } from '@/services/quality';
import useLiveAfkFG from '@/hooks/fg/useLiveAfkFG';
import { selectPlatformAtom } from '../ProjectHome/atom';
import { useAtomValue } from 'jotai';
import PlatformSwitchMobile from '@/components/platformSwitchMobile';

const BodyDiv = styled.div`
  display: flex;
  padding: 0px 1rem;
  overflow: hidden;
`;

const MobileTabs = styled(Tabs)`
  .ant-tabs-nav {
    margin-bottom: 0px;
  }
`;

const MobileProTable = styled(ProTable)`
  .ant-pro-table-list-toolbar-container {
    padding-block: 0.5rem;
  }

  @media (max-width: 768px) {
    .ant-pro-table-list-toolbar-left {
      margin: 0px;
    }

    .ant-pro-table-list-toolbar-container {
      flex-direction: row;
      flex-wrap: nowrap;
    }
  }
`;

const afkLevel = [
  {
    label: '严重空播挂播',
    value: 1,
  },
  {
    label: '轻微空播挂播',
    value: 2,
  },
  {
    label: '正常',
    value: 3,
  },
];

const today = new Date();
const max = new Date(today.setDate(today.getDate() - 1));
const min = new Date(today.setMonth(today.getMonth() - 6));

const LandScapeScreenTable = ({ type }: { type: string }) => {
  const [searchParams] = useSearchParams();
  const payload = searchParams.get('payload');
  const date = searchParams.get('date');
  const [classifyType, setClassifyType] = useState(ClassifyType.Team);
  const [rangeTime, setRangeTime] = useState<string[] | undefined | null>(
    date ? [date, date] : undefined,
  );
  const [startDate, endDate] = rangeTime || [];
  const actionRef = useRef<ActionType>();
  const [calendarVisible, setCalendarVisible] = useState(false);
  const [treeSelectVisible, setTreeSelectVisible] = useState(false);
  const cascaderOptions = useGetCascaderOptions(payload);
  const [cascaderValue, setCascaderValue] = useState<CheckListValue[]>([]);
  const [showSelectRes, setShowSelectRes] = useState<string | null>(null);
  const [fieldValueStr, setFieldValueStr] = useState<CheckListValue | undefined>(undefined);
  const [inputValue, setInputValue] = useState<string>('');
  const [keyword, setKeyword] = useState<string | undefined>(undefined);
  const [afkLevelVisible, setAfkLevelVisible] = useState(false);
  const [monitorTypeVisible, setMonitorTypeVisible] = useState(false);
  const { data: qualityData } = useRequest(() => GetQualityTypeSelectDaily({ payload }));
  const { data: anchorImageViolationData } = useRequest(() =>
    GetAnchorImageViolationSelectDaily({ payload }),
  );
  const [liveAfkLevel, setLiveAfkLevel] = useState<CheckListValue | null>(null);
  const [monitorType, setMonitorType] = useState<CheckListValue[] | undefined>(undefined);
  const afkState = useLiveAfkFG(payload, true);
  const platform = useAtomValue(selectPlatformAtom);

  const showQualityData = qualityData?.filter((item) => item.disabled === false);

  const showAnchorImageViolation = anchorImageViolationData?.filter(
    (item) => item.disabled === false,
  );

  useEffect(() => {
    const field = cascaderValue.at(-1);
    setFieldValueStr(field);
  }, [cascaderValue]);

  useEffect(() => {
    actionRef.current?.reload();
    setKeyword('');
  }, [classifyType, type]);

  // antd-mobile组件暂时没有实现滚动到默认选中的日期 自行滚动下
  useEffect(() => {
    if (calendarVisible) {
      scrollAntdMobileCalendar();
    }
  }, [calendarVisible]);

  return (
    <>
      <BodyDiv>
        <MobileProTable
          // @ts-ignore
          columns={renderColumns(type, classifyType, afkState)}
          actionRef={actionRef}
          params={{
            startDate,
            endDate,
            keyword,
            fieldValueStr,
            liveAfkLevel,
            qualityCategoryTypeList: monitorType,
            platform,
          }}
          ghost
          scroll={{ x: 'max-content' }}
          size="small"
          style={{ overflow: 'hidden' }}
          headerTitle={
            <div
              style={{
                background: '#f3f3f3',
                borderRadius: '4px',
                width: '15rem',
                display: 'flex',
                alignItems: 'center',
                paddingInline: '1rem',
              }}
            >
              <Input
                placeholder="请输入经销商或者账号名称/ID"
                value={inputValue}
                clearable
                onClear={() => {
                  setInputValue('');
                  setKeyword(undefined);
                }}
                onChange={(val) => {
                  setInputValue(val);
                }}
                onEnterPress={(e) => {
                  // @ts-ignore
                  setKeyword(e.target.value);
                }}
                style={{ '--font-size': '14px' }}
              />
              <SearchOutline onClick={() => setKeyword(inputValue)} />
            </div>
          }
          toolBarRender={() => [
            <FilterItem
              key="classify"
              onClick={() => {
                classifyType === ClassifyType.Account
                  ? setClassifyType(ClassifyType.Team)
                  : setClassifyType(ClassifyType.Account);
              }}
            >
              按账号
            </FilterItem>,
            <FilterItem
              key="date"
              onClick={() => {
                setCalendarVisible(true);
              }}
            >
              日期
              <DownFill style={{ fontSize: '0.5rem' }} />
            </FilterItem>,
            <FilterItem
              key="region"
              onClick={() => {
                setTreeSelectVisible(true);
              }}
            >
              地区
              <FilterOutline />
              <span>{showSelectRes}</span>
            </FilterItem>,
            <>
              {type === 'quality' && afkState && (
                <>
                  <FilterItem onClick={() => setMonitorTypeVisible(true)}>质检类型</FilterItem>
                  <FilterItem
                    onClick={() => {
                      setAfkLevelVisible(true);
                    }}
                  >
                    疑似空播挂播
                  </FilterItem>
                </>
              )}
            </>,
          ]}
          debounceTime={classifyType === ClassifyType.Account ? 800 : 10}
          request={(params, sorter) => {
            if (!rangeTime) return Promise.resolve();
            let fetchFn: any;
            if (type === 'quality') {
              if (classifyType === ClassifyType.Team) {
                fetchFn = QualityExViolationTeamDaily;
              } else if (classifyType === ClassifyType.Account) {
                fetchFn = QualityExViolationAccountDaily;
              }
            } else {
              if (classifyType === ClassifyType.Team) {
                fetchFn = ChatViolationTeamDaily;
              } else if (classifyType === ClassifyType.Account) {
                fetchFn = ChatViolationAccountDaily;
              }
            }
            const formattedData: any = {
              ...params,
              payload,
              projectId: -1,
              platform,
            };
            if (params.fieldValueStr) {
              const [value, fieldId] = params.fieldValueStr.split('-');
              formattedData.fieldList = [{ fieldId, value }];
              delete formattedData.fieldValueStr;
            }

            return proTableRequestAdapterParamsAndData(formattedData, sorter, fetchFn, [
              'size',
              'page',
              'payload',
              'orderBy',
              'orderType',
            ]);
          }}
          search={false}
          options={false}
          rowKey={(record) => record.teamCode + Math.random()}
          pagination={{
            defaultPageSize: 5,
            showSizeChanger: false,
          }}
          dateFormatter="string"
        />
      </BodyDiv>
      <CalendarPicker
        visible={calendarVisible}
        selectionMode="range"
        max={max}
        min={min}
        onClose={() => setCalendarVisible(false)}
        onMaskClick={() => setCalendarVisible(false)}
        defaultValue={rangeTime ? [new Date(rangeTime[0]), new Date(rangeTime[1])] : null}
        onConfirm={(val: [Date, Date] | null) => {
          const formatVal = val?.map((item) => dayjs(item).format('YYYY-MM-DD'));
          setRangeTime(formatVal);
        }}
      />
      <Cascader
        options={cascaderOptions}
        visible={treeSelectVisible}
        onClose={() => {
          setTreeSelectVisible(false);
        }}
        value={cascaderValue}
        onConfirm={(value: CheckListValue[], extend) => {
          setCascaderValue(value);
          const extendItems = extend.items;
          if (extendItems.every((item) => item === null)) {
            setShowSelectRes('未选择');
          } else {
            const res = extendItems.map((item) => item?.label ?? '未选择').join('-');
            setShowSelectRes(res);
          }
        }}
      />
      <Popup
        visible={afkLevelVisible}
        onMaskClick={() => {
          setAfkLevelVisible(false);
        }}
        destroyOnClose
      >
        <CheckList
          defaultValue={liveAfkLevel ? [liveAfkLevel] : []}
          onChange={(val) => {
            setLiveAfkLevel?.(val[0]);
            setAfkLevelVisible(false);
          }}
        >
          {afkLevel.map(({ label, value }) => (
            <CheckList.Item key={label} value={value}>
              {label}
            </CheckList.Item>
          ))}
        </CheckList>
      </Popup>
      <Popup
        visible={monitorTypeVisible}
        onMaskClick={() => {
          setMonitorTypeVisible(false);
        }}
        destroyOnClose
        bodyStyle={{ height: '80vh' }}
      >
        <div style={{ height: '80vh', overflowY: 'auto' }}>
          <CheckList
            defaultValue={monitorType}
            multiple
            onChange={(val) => {
              setMonitorType?.(val);
            }}
          >
            {showQualityData?.map((item) => (
              <CheckList.Item key={item.name} value={item.value}>
                {item.name}
              </CheckList.Item>
            ))}
            {showAnchorImageViolation?.map((item) => (
              <CheckList.Item key={item.name} value={item.value}>
                {item.name}
              </CheckList.Item>
            ))}
          </CheckList>
        </div>
      </Popup>
    </>
  );
};

const LandScreenBody = styled.div`
  padding-inline: 1rem;

  @media screen and (orientation: portrait) {
    position: absolute;
    top: 0;
    left: 100vw;
    width: 100vh;
    height: 100vw;
    -webkit-transform: rotate(90deg);
    -moz-transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    transform: rotate(90deg);
    -webkit-transform-origin: 0% 0%;
    -moz-transform-origin: 0% 0%;
    -ms-transform-origin: 0% 0%;
    transform-origin: 0% 0%;
  }
`;

const LandScapeScreen = ({
  setIslandscapeScreen,
}: {
  setIslandscapeScreen: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
  const [searchParams] = useSearchParams();
  const type = searchParams.get('type');
  const typeArr = type?.split(',');
  const payload = searchParams.get('payload');
  const [activeKey, onTabChange] = useTabKeySearchParams(typeArr ? typeArr[0] : 'quality');
  const contentRef = useRef(null);

  const items = typeArr?.map((type) => {
    return {
      label: type === 'quality' ? '质检日报' : '私信日报',
      key: type,
      children: <LandScapeScreenTable type={type} />,
    };
  });

  useEffect(() => {
    if (window.orientation == 180 || window.orientation == 0) {
      Modal.show({
        content: (
          <div style={{ textAlign: 'center', fontSize: '0.75rem' }}>
            <div>为了更好的使用体验,请横屏使用</div>
            <div>开启手机旋转,确保底部按钮处于横屏状态</div>
          </div>
        ),
        closeOnMaskClick: true,
      });
    }
  }, []);

  return (
    <LandScreenBody ref={contentRef}>
      <MobileTabs
        activeKey={activeKey}
        onChange={onTabChange}
        items={items}
        tabBarExtraContent={
          <img
            style={{ width: '1.5rem', height: '1.5rem', marginRight: '1rem' }}
            src={SwitchBtnImg}
            alt="switch"
            onClick={() => setIslandscapeScreen(false)}
          />
        }
      />
      <PlatformSwitchMobile payload={payload} dragContentRef={contentRef} />
    </LandScreenBody>
  );
};

export default LandScapeScreen;
