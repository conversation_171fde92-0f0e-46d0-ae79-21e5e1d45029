import { selectPlatformAtom } from '@/pages/ProjectHome/atom';
import { GetChatCountDaily, GetQualityExCountDaily } from '@/services/unauth';
import { scrollAntdMobileCalendar } from '@/utils/dailyUtils';
import { dateTypeMap } from '@/utils/dateTypeMap';
import { history, useRequest, useSearchParams } from '@umijs/max';
import { CalendarPicker, Skeleton } from 'antd-mobile';
import { DownFill, RightOutline } from 'antd-mobile-icons';
import classNames from 'classnames';
import dayjs from 'dayjs';
import { useAtomValue } from 'jotai';
import { useEffect, useState } from 'react';
import styled from 'styled-components';

type DailyCardProps = {
  title: string;
  type: 'quality' | 'chat';
};

const CardBody = styled.div`
  width: 100%;
  background: #fff;
  padding: 16px 16px 0px;
  border-radius: 0.5rem;
  overflow: hidden;

  .title {
    color: #0e1015;
    font-weight: 500;
    font-size: 1rem;
  }

  .datefilter-container {
    display: flex;
    flex-wrap: nowrap;
    gap: 1.25rem;
    margin-block: 0.75rem;

    .datefilter-item {
      position: relative;
      font-size: 0.75rem;
      white-space: nowrap;
    }

    .datefilter-item.active::before {
      position: absolute;
      top: 0.75rem;
      left: 0;
      width: 100%;
      height: 100%;
      border-bottom: 2px solid #000;
      content: '';
    }

    .datefilter-item:hover::before {
      width: 100%;
    }
  }

  .divider {
    height: 0.0625rem;
    background-color: #e2e2e2;
    border: none;
  }

  .violation-card-container {
    display: flex;
    gap: 0.5rem;
    justify-content: space-between;
    margin-block: 0.875rem;

    .violation-card {
      flex: 1;
      height: 4.375rem;
      padding: 0.75rem;
      background: #f9fafe;
      border-radius: 1rem;

      .card-title {
        color: #95979c;
        font-size: 0.875rem;
      }

      .card-number {
        display: flex;
        align-items: flex-end;
        justify-content: space-between;
        margin-top: 0.7rem;

        .card-number-count {
          color: #0e1015;
          font-weight: 700;
          font-size: 0.875rem;
        }
      }
    }
  }

  .nav-link {
    display: flex;
    justify-content: space-between;
    font-size: 0.875rem;
    margin-block: 0.625rem;
  }
`;

const renderMom = (data?: string | number) => {
  if (!data) return <></>;
  if (Number(data) > 0) {
    return (
      <div style={{ color: '#D30A0A', display: 'flex', alignItems: 'center' }}>
        <DownFill style={{ fontSize: '0.5rem', rotate: '180deg' }} /> {data}%
      </div>
    );
  } else {
    return (
      <div style={{ color: '#1EBAA1', display: 'flex', alignItems: 'center' }}>
        <DownFill style={{ fontSize: '0.5rem' }} /> {data}%
      </div>
    );
  }
};

const today = new Date();
const max = new Date(today.setDate(today.getDate() - 1));
const min = new Date(today.setMonth(today.getMonth() - 6));

const DailyCard = (props: DailyCardProps) => {
  const { title, type } = props;
  const [searchParams] = useSearchParams();
  const payload = searchParams.get('payload');
  const today = dayjs();
  const date = searchParams.get('date');
  const isYesterday = date === today.subtract(1, 'day').format('YYYY-MM-DD');
  const defaultDate = date ? (isYesterday ? null : new Date(date)) : null;
  const currentTabDefault = date ? (isYesterday ? 'day' : null) : 'day';
  const [currentTab, setCurrentTab] = useState<'day' | 'week' | 'month' | null>(currentTabDefault);
  const [showRangeDate, setShowRangeDate] = useState<[Date, Date] | null>(
    defaultDate ? [defaultDate, defaultDate] : null,
  );
  const [calendarVisible, setCalendarVisible] = useState(false);
  const [startDate, endDate] = showRangeDate?.map((item) => dayjs(item).format('YYYY-MM-DD')) || [
    undefined,
    undefined,
  ];
  const platform = useAtomValue(selectPlatformAtom);

  const { data: chatData, loading: chatDataLoading } = useRequest(
    () => {
      if (!currentTab && !startDate && !endDate) {
        return Promise.resolve(null);
      }
      return GetChatCountDaily({
        projectId: -1,
        payload,
        dateType: currentTab ? dateTypeMap[currentTab] : 4,
        startDate,
        endDate,
        platform,
      });
    },
    {
      refreshDeps: [currentTab, showRangeDate, platform],
      ready: type === 'chat',
    },
  );
  const { data: qualityData, loading: qualityDataLoading } = useRequest(
    () => {
      if (!currentTab && !startDate && !endDate) {
        return Promise.resolve(null);
      }
      return GetQualityExCountDaily({
        projectId: -1,
        payload,
        dateType: currentTab ? dateTypeMap[currentTab] : 4,
        startDate,
        endDate,
        platform,
      });
    },
    {
      refreshDeps: [currentTab, showRangeDate, platform],
      ready: type === 'quality',
    },
  );

  const handleClick = (tab: 'day' | 'week' | 'month' | null) => {
    if (tab !== null) {
      setShowRangeDate(null);
    }
    setCurrentTab(tab);
  };

  const cardConfig = {
    chat: [
      {
        title: '被提醒门店数',
        count: chatData?.teamViolationCount,
        mom: chatData?.teamViolationCountMom,
      },
      {
        title: '被提醒账号数',
        count: chatData?.accountViolationCount,
        mom: chatData?.accountViolationCountMom,
      },
    ],
    quality: [
      {
        title: '违规门店数',
        count: qualityData?.teamViolationCount,
        mom: qualityData?.teamViolationCountMom,
      },
      {
        title: '违规账号数',
        count: qualityData?.accountViolationCount,
        mom: qualityData?.accountViolationCountMom,
      },
    ],
  };

  const renderCards = (
    cards: { title: string; count?: number; mom?: string }[],
    loading: boolean,
  ) => {
    if (loading) {
      return (
        <>
          <Skeleton.Title animated style={{ width: '9.6875rem' }} />
          <Skeleton.Title animated style={{ width: '9.6875rem' }} />
        </>
      );
    } else {
      return (
        <>
          {cards.map((card, index) => {
            return (
              <div className="violation-card" key={index}>
                <div className="card-title">{card.title}</div>
                <div className="card-number">
                  <div className="card-number-count">{card.count}</div>
                  {renderMom(card.mom)}
                </div>
              </div>
            );
          })}
        </>
      );
    }
  };

  // antd-mobile组件暂时没有实现滚动到默认选中的日期 自行滚动下
  useEffect(() => {
    if (calendarVisible) {
      scrollAntdMobileCalendar();
    }
  }, [calendarVisible]);

  return (
    <>
      <CardBody>
        <div className="title">{title}</div>
        <div className="datefilter-container">
          <div
            className={classNames('datefilter-item', { active: currentTab === 'day' })}
            onClick={() => handleClick('day')}
          >
            昨天
            {currentTab === 'day' && ` ${dayjs().subtract(1, 'day').format('YYYY-MM-DD')}`}
          </div>
          <div
            className={classNames('datefilter-item', { active: currentTab === 'week' })}
            onClick={() => handleClick('week')}
          >
            自然周
            {currentTab === 'week'
              ? dayjs().day() === 1
                ? `${dayjs().subtract(1, 'week').year()}年 - 第${dayjs()
                    .subtract(1, 'week')
                    .week()}周`
                : `${dayjs().year()}年 - 第${dayjs().week()}周`
              : null}
          </div>
          <div
            className={classNames('datefilter-item', { active: currentTab === 'month' })}
            onClick={() => handleClick('month')}
          >
            自然月
            {currentTab === 'month'
              ? dayjs().date() === 1
                ? `${dayjs().subtract(1, 'month').format('YYYY-MM')}`
                : `${dayjs().format('YYYY-MM')}`
              : null}
          </div>
          <div
            className={classNames('datefilter-item', { active: currentTab === null })}
            onClick={() => {
              setCalendarVisible(true);
              setCurrentTab(null);
            }}
          >
            自定义
            {showRangeDate
              ? `${dayjs(showRangeDate[0]).format('YYYY-MM-DD')}-${dayjs(showRangeDate[1]).format(
                  'YYYY-MM-DD',
                )}`
              : null}
          </div>
        </div>
        <hr className="divider" />
        <div className="violation-card-container">
          {type === 'chat'
            ? renderCards(cardConfig.chat, chatDataLoading)
            : renderCards(cardConfig.quality, qualityDataLoading)}
        </div>
        <hr className="divider" />
        <div
          className="nav-link"
          onClick={() => history.push(`/daily/detail${window.location.search}`, type)}
        >
          <div>查看详情</div>
          <RightOutline />
        </div>
      </CardBody>
      <CalendarPicker
        visible={calendarVisible}
        selectionMode="range"
        max={max}
        min={min}
        onClose={() => setCalendarVisible(false)}
        onMaskClick={() => setCalendarVisible(false)}
        defaultValue={showRangeDate}
        onConfirm={(val: [Date, Date] | null) => setShowRangeDate(val)}
      />
    </>
  );
};

export default DailyCard;
