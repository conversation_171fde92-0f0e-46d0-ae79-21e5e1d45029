import PlatformSwitch from '@/components/platformSwitch';
import { loginOut } from '@/components/RightContent/AvatarDropdown';
import TimeFilterByType from '@/components/timFilterByType.tsx';
import useTabKeySearchParams from '@/hooks/useTabKeySearchParams';
import UnAuthDailyReport from '@/pages/Daily/component/unAuthDailyReport';
import { checkFreePage } from '@/utils';
import { loginPath } from '@/utils/const';
import { PageContainer } from '@ant-design/pro-components';
import { useModel, useSearchParams, history } from '@umijs/max';
import { useTitle } from 'ahooks';
import { Button, Space, Tabs } from 'antd';
import { useState } from 'react';
import { flushSync } from 'react-dom';

type TimeFilterByType = {
  rangeTime: string[];
  radioValue: 'day' | 'week' | 'month' | null;
};

const WebDaily = () => {
  useTitle('提醒日报');
  const [searchParams] = useSearchParams();
  const type = searchParams.get('type');
  const payload = searchParams.get('payload');
  const typeArr = type?.split(',');
  const { setInitialState } = useModel('@@initialState');

  const [activeKey, onTabChange] = useTabKeySearchParams(typeArr ? typeArr[0] : 'quality');
  const [timeFilterByType, setTimeFilterByType] = useState<TimeFilterByType>({
    rangeTime: [],
    radioValue: null,
  });

  const handleTimeFilterChange = (filter: TimeFilterByType) => {
    setTimeFilterByType(filter);
  };

  const items = typeArr?.map((type) => {
    return {
      label: type === 'quality' ? '质检日报' : '私信日报',
      key: type,
      children: (
        <UnAuthDailyReport
          rangeTime={timeFilterByType.rangeTime}
          dataType={timeFilterByType.radioValue}
          type={type}
        />
      ),
    };
  });

  const logoutAndClearInfo = () => {
    flushSync(() => {
      setInitialState((s: any) => ({ ...s, currentUser: undefined }));
    });
    if (!checkFreePage()) {
      history.replace(
        loginPath +
          '?redirect=' +
          encodeURIComponent(window.location.pathname + window.location.search),
      );
    }
  };

  const handleLogout = () => {
    logoutAndClearInfo();
    loginOut();
  };

  return (
    <PageContainer
      title="提醒管理/每日日报"
      extra={
        <Space>
          <PlatformSwitch payload={payload} />
          <Button key="logout" onClick={handleLogout}>
            退出登录
          </Button>
        </Space>
      }
    >
      <Tabs
        activeKey={activeKey}
        onChange={onTabChange}
        items={items}
        tabBarExtraContent={<TimeFilterByType onTimeFilterChange={handleTimeFilterChange} />}
        className="horizontal-tab"
      />
    </PageContainer>
  );
};

export default WebDaily;
