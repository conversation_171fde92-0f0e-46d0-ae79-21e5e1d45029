import AccountImg from '@/assets/account.png';
import TeamImg from '@/assets/team.png';
import DynamicTree from '@/components/dynamicTree';
import ExportButton from '@/components/exportButton';
import QualityTypeSelect from '@/components/qualityTypeSelect';
import { SummaryCount } from '@/services/daily';
import { TrendList } from '@/services/quality';
import {
  ChatTrendDaily,
  ChatViolationAccountDaily,
  ChatViolationTeamDaily,
  ExportChatAccountDaily,
  ExportChatTeamDaily,
  ExportQualityAccountDaily,
  ExportQualityTeamDaily,
  GetChatCountDaily,
  GetQualityExCountDaily,
  QualityExTrendDaily,
  QualityExViolationAccountDaily,
  QualityExViolationTeamDaily,
} from '@/services/unauth';
import { ClassifyType, STICKY_OFFSETHEADER } from '@/utils/common';
import { echartAreaStyleGradient, renderMom } from '@/utils/commonStyle';
import { renderColumns } from '@/utils/dailyUtils';
import { renderXAxisByDataType } from '@/utils/time';
import { ActionType, CheckCard, ProCard, ProTable } from '@ant-design/pro-components';
import { useSearchParams } from '@umijs/max';
import { useAsyncEffect } from 'ahooks';
import { Flex, Input, PaginationProps, Select, Space, message } from 'antd';
import ReactECharts from 'echarts-for-react';
import { useEffect, useRef, useState } from 'react';
import styled from 'styled-components';
import './index.less';
import { proTableRequestAdapterParamsAndData } from '@/utils';
import { usePollingExport } from '@/hooks/usePollingExport';
import useLiveAfkFG from '@/hooks/fg/useLiveAfkFG';
import { dateTypeMap } from '@/utils/dateTypeMap';
import { useAtomValue } from 'jotai';
import { selectPlatformAtom } from '@/pages/ProjectHome/atom';
import { proTableOptionsConfig, proTablePaginationConfig } from '@/utils/proTableConfig';

export type DailyReportProps = {
  rangeTime: string[];
  dataType: 'day' | 'week' | 'month' | null;
  type: 'quality' | 'chat' | string;
};

export const CustomPaginationBtn = styled.div`
  width: 86px;
  height: 34px;
  border: 1px solid;
  border-radius: 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-color: #2160f9;
  color: #2160f9;
  font-family: OPPOSans;

  &:hover {
    color: #fff;
    background-color: #2160f9;
  }
`;

export const customPaginationRender: PaginationProps['itemRender'] = (_, type, originalElement) => {
  if (type === 'prev') {
    return <CustomPaginationBtn>上一页</CustomPaginationBtn>;
  }
  if (type === 'next') {
    return <CustomPaginationBtn style={{ marginRight: '10px' }}>下一页</CustomPaginationBtn>;
  }
  return originalElement;
};

// 不鉴权接口相关的日报面板web端
const UnAuthDailyReport = (props: DailyReportProps) => {
  const { rangeTime, dataType: propsDataType, type } = props;
  const [searchParams] = useSearchParams();
  const payload = searchParams.get('payload');
  const actionRef = useRef<ActionType>();
  const [startDate, endDate] = rangeTime;
  const [classifyType, setClassifyType] = useState(ClassifyType.Team);
  const [trendData, setTrendData] = useState<TrendList[]>([]);
  const [treeValue, setTreeValue] = useState<string[] | undefined>(undefined);
  const [keyword, setKeyword] = useState<string>('');
  const [summary, setSummary] = useState<SummaryCount>({});
  const [qualityCategoryTypeList, setQualityCategoryTypeList] = useState<string[] | undefined>();
  const [liveAfkLevel, setLiveAfkLevel] = useState<string[] | undefined>();
  const { pollingExport, percent, pollingLoading } = usePollingExport();
  const afkState = useLiveAfkFG(payload, true);
  const platform = useAtomValue(selectPlatformAtom);

  const handleExportRank = async (type: string, classifyType: ClassifyType) => {
    let fetchExportFn;
    if (type === 'quality') {
      if (classifyType === ClassifyType.Account) {
        fetchExportFn = ExportQualityAccountDaily;
      } else {
        fetchExportFn = ExportQualityTeamDaily;
      }
    } else {
      if (classifyType === ClassifyType.Account) {
        fetchExportFn = ExportChatAccountDaily;
      } else {
        fetchExportFn = ExportChatTeamDaily;
      }
    }
    const res = await fetchExportFn({ projectId: -1, startDate, endDate, platform }, { payload });
    if (res.data) {
      pollingExport({ taskId: res.data });
    } else {
      message.error('导出失败');
    }
  };

  const options = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985',
          precision: 0, // 省略小数点
        },
      },
      formatter: function (params: any) {
        const mom = params[0].data.mom;
        const value = params[0].data.value;
        return (
          params[0].name +
          `<br>${
            type === 'quality'
              ? `违规${classifyType === ClassifyType.Account ? '账号' : '门店'}个数`
              : `提醒${classifyType === ClassifyType.Account ? '账号' : '门店'}个数`
          } ` +
          `<span style="color: #000">${value}</span>` +
          '<br>' +
          `<span style="color:${mom > 0 ? 'red' : '#30B824'};">环比${
            mom > 0 ? '增加' : '减少'
          } ${mom} % </span>`
        );
      },
    },
    grid: {
      top: '3%',
      left: 1,
      right: '2%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: renderXAxisByDataType(propsDataType),
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        data: trendData.map((item) => {
          return { value: item.count, mom: item.mom };
        }),
        type: 'line',
        stack: 'Total',
        smooth: true,
        areaStyle: echartAreaStyleGradient,
        emphasis: {
          focus: 'series',
        },
      },
    ],
  };

  useAsyncEffect(async () => {
    if (rangeTime.length === 0) return;
    let fetchSummaryFn;
    if (type === 'quality') {
      fetchSummaryFn = GetQualityExCountDaily;
    } else {
      fetchSummaryFn = GetChatCountDaily;
    }
    const res = await fetchSummaryFn({
      projectId: -1,
      dateType: propsDataType ? dateTypeMap[propsDataType] : 4,
      startDate,
      endDate,
      payload,
      platform,
    });
    setSummary(res?.data || {});
  }, [propsDataType, rangeTime, platform]);

  // 趋势图部分的接口
  useAsyncEffect(async () => {
    let res;
    if (propsDataType !== null) {
      if (type === 'quality') {
        res = await QualityExTrendDaily({
          projectId: -1,
          payload,
          type: classifyType,
          dateType: propsDataType ? dateTypeMap[propsDataType] : 4,
          platform,
        });
      } else {
        res = await ChatTrendDaily({
          projectId: -1,
          payload,
          type: classifyType,
          dateType: propsDataType ? dateTypeMap[propsDataType] : 4,
          platform,
        });
      }
      setTrendData(res?.data?.trendList || []);
      actionRef.current?.reload();
    }
  }, [classifyType, propsDataType, platform]);

  useEffect(() => {
    actionRef.current?.reload();
    setTreeValue(undefined);
    setKeyword('');
  }, [classifyType]);

  return (
    <ProCard>
      <CheckCard.Group
        onChange={(value) => {
          if (!value) return;
          setClassifyType(value as number);
        }}
        defaultValue={classifyType}
        value={classifyType}
      >
        <CheckCard
          avatar={TeamImg}
          size="small"
          title={type === 'quality' ? '违规门店个数' : '提醒门店个数'}
          value={ClassifyType.Team}
          description={
            <Flex justify="space-between" align="center">
              <span style={{ fontSize: '18px', fontWeight: 600 }}>
                {summary?.teamViolationCount}
              </span>
              {renderMom(summary?.teamViolationCountMom)}
            </Flex>
          }
        />
        <CheckCard
          avatar={AccountImg}
          size="small"
          title={type === 'quality' ? '违规账号个数' : '提醒账号个数'}
          value={ClassifyType.Account}
          description={
            <Flex justify="space-between" align="center">
              <span style={{ fontSize: '18px', fontWeight: 600 }}>
                {summary?.accountViolationCount}
              </span>
              {renderMom(summary?.accountViolationCountMom)}
            </Flex>
          }
        />
      </CheckCard.Group>

      {propsDataType !== null && (
        <>
          <div style={{ marginBottom: '20px' }}>违规趋势</div>
          <div style={{ height: '200px' }}>
            <ReactECharts
              option={options}
              notMerge={true}
              style={{ width: '100%', height: '100%' }}
            />
          </div>
        </>
      )}
      <ProTable
        // @ts-ignore
        columns={renderColumns(type, classifyType, afkState)}
        actionRef={actionRef}
        params={{
          startDate,
          endDate,
          teamCodeList: treeValue,
          keyword,
          liveAfkLevel,
          qualityCategoryTypeList,
          platform,
        }}
        ghost
        sticky={{ offsetHeader: STICKY_OFFSETHEADER }}
        tableClassName="custom-table"
        scroll={{ x: 'max-content' }}
        headerTitle={
          <Space>
            {classifyType === ClassifyType.Team ? (
              <DynamicTree
                style={{ width: '200px' }}
                value={treeValue}
                setValue={setTreeValue}
                unAuth={true}
                payload={payload}
              />
            ) : (
              <Input
                style={{ width: '200px' }}
                value={keyword}
                onChange={(e) => {
                  setKeyword(e.target.value);
                }}
                placeholder="账号名称或ID搜索"
              />
            )}
            {type === 'quality' && (
              <>
                <QualityTypeSelect
                  style={{ width: '200px' }}
                  payload={payload}
                  unAuth
                  value={qualityCategoryTypeList}
                  onChange={(value) => setQualityCategoryTypeList(value)}
                />
                {afkState && (
                  <Select
                    allowClear
                    style={{ width: '200px' }}
                    placeholder="是否疑似空挂播"
                    value={liveAfkLevel}
                    onChange={(value) => {
                      setLiveAfkLevel(value);
                    }}
                    options={[
                      { label: '严重空挂播', value: 1 },
                      { label: '轻微空挂播', value: 2 },
                      { label: '正常', value: 3 },
                    ]}
                  />
                )}
              </>
            )}
          </Space>
        }
        debounceTime={classifyType === ClassifyType.Account ? 800 : 10}
        request={(params, sorter) => {
          if (!rangeTime) return Promise.resolve();
          let fetchFn: any;
          if (type === 'quality') {
            if (classifyType === ClassifyType.Team) {
              fetchFn = QualityExViolationTeamDaily;
            } else if (classifyType === ClassifyType.Account) {
              fetchFn = QualityExViolationAccountDaily;
            }
          } else {
            if (classifyType === ClassifyType.Team) {
              fetchFn = ChatViolationTeamDaily;
            } else if (classifyType === ClassifyType.Account) {
              fetchFn = ChatViolationAccountDaily;
            }
          }
          const formattedData = {
            ...params,
            projectId: -1,
            payload,
            platform,
          };

          return proTableRequestAdapterParamsAndData(formattedData, sorter, fetchFn, [
            'size',
            'page',
            'payload',
            'orderBy',
            'orderType',
          ]);
        }}
        search={false}
        options={{ ...proTableOptionsConfig }}
        pagination={{ ...proTablePaginationConfig }}
        toolBarRender={() => [
          <ExportButton
            exportFn={() => handleExportRank(type, classifyType)}
            loading={pollingLoading}
            percent={percent}
            key="export"
          />,
        ]}
        rowKey={(record) => record.teamCode + Math.random()}
        dateFormatter="string"
      />
    </ProCard>
  );
};

export default UnAuthDailyReport;
