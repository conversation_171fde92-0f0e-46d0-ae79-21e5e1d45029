import { useModel, useSearchParams, history } from '@umijs/max';
import { useTitle } from 'ahooks';
import styled from 'styled-components';
import DailyCard from './dailyCard';
import { ActionSheet } from 'antd-mobile';
import { useRef, useState } from 'react';
import type { Action, ActionSheetShowHandler } from 'antd-mobile/es/components/action-sheet';
import { loginOut } from '@/components/RightContent/AvatarDropdown';
import { flushSync } from 'react-dom';
import { checkFreePage } from '@/utils';
import { loginPath } from '@/utils/const';
import PlatformSwitchMobile from '@/components/platformSwitchMobile';
import React from 'react';
import { Bolt } from 'lucide-react';

const NavBar = styled.div`
  width: 100vw;
  height: 2.75rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 1rem;
  font-weight: 500;
`;

const Body = styled.div`
  height: calc(100vh - 2.75rem);
  width: 100vw;
  background-color: #f5f6fa;
  padding: 0.5rem;
  padding-inline: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.825rem;
  align-items: center;
`;

const MobileDaily = () => {
  useTitle('提醒日报');
  const [searchParams] = useSearchParams();
  const type = searchParams.get('type');
  const payload = searchParams.get('payload');
  const typeArr = type?.split(',');
  const [settingVisible, setSettingVisible] = useState<boolean>(false);
  const actionSheet = useRef<ActionSheetShowHandler>();
  const { setInitialState } = useModel('@@initialState');
  const contentRef = useRef(null);

  const logoutAndClearInfo = () => {
    flushSync(() => {
      setInitialState((s: any) => ({ ...s, currentUser: undefined }));
    });
    if (!checkFreePage()) {
      history.replace(
        loginPath +
          '?redirect=' +
          encodeURIComponent(window.location.pathname + window.location.search),
      );
    }
  };

  const actions: Action[] = [
    {
      key: 'logout',
      text: '退出登录',
      onClick: () => {
        const logoutActions: Action[] = [
          {
            text: '确认',
            key: 'confirm',
            danger: true,
            onClick: async () => {
              logoutAndClearInfo();
              loginOut();
              actionSheet.current?.close();
            },
          },
        ];
        actionSheet.current = ActionSheet.show({
          actions: logoutActions,
          extra: '是否确认退出登录',
          cancelText: '取消',
        });
      },
    },
  ];

  return (
    <div className="h-full w-full" ref={contentRef}>
      <NavBar>
        <div style={{ width: '30px' }} />
        <div style={{ flex: 1, textAlign: 'center' }}>提醒日报</div>
        <div style={{ float: 'right', width: '30px' }}>
          <Bolt size={18} onClick={() => setSettingVisible(true)} />
        </div>
      </NavBar>
      <Body>
        {typeArr?.includes('chat') && <DailyCard title="私信违规" type="chat" />}
        {typeArr?.includes('quality') && <DailyCard title="质检违规" type="quality" />}
      </Body>
      <ActionSheet
        cancelText="取消"
        visible={settingVisible}
        actions={actions}
        onClose={() => setSettingVisible(false)}
      />
      <PlatformSwitchMobile payload={payload} dragContentRef={contentRef} />
    </div>
  );
};

export default MobileDaily;
