import RightContent from '@/components/RightContent';
import { dailyDetailPath, dailyPath, douyinAuthPath, loginPath, registerPath } from '@/utils/const';
import type { MenuDataItem, Settings as LayoutSettings } from '@ant-design/pro-components';
import { GridContent } from '@ant-design/pro-components';
import Tracker from '@openreplay/tracker';
import trackerAssist from '@openreplay/tracker-assist';
import type { RunTimeLayoutConfig } from '@umijs/max';
import { history } from '@umijs/max';
import Aegis from 'aegis-web-sdk';
import React, { useEffect } from 'react';
import defaultSettings from '../config/defaultSettings';
import { PermissionCode } from './access';
import './app.less';
import { ThemeToggle } from './components/themeToggle';
import { requestConfig } from './requestConfig';
import { GetCurrentUser } from './services/auth';
import { PortalUser } from './services/typings';
import { fetchProjectInfo } from './utils/common';
import CustomerOnlineStatus from './components/customerOnlineStatus';

const isDev = process.env.NODE_ENV === 'development';
const isTest = process.env.NODE_ENV === 'test';
let tracker: Tracker;
function initTracker() {
  if (!isDev && !isTest) {
    new Aegis({
      id: 'EPkqjsr2315JjEXyp2', // 上报 id
      uin: '100033325378', // 用户唯一 ID（可选）
      reportApiSpeed: true, // 接口测速
      reportAssetSpeed: true, // 静态资源测速
      spa: true, // spa 应用页面跳转的时候开启 pv 计算
      hostUrl: 'https://rumt-zh.com',
    });

    tracker = new Tracker({
      projectKey: 'kKYQlVaILGc8aFJCTjhF',
      ingestPoint: 'https://replay.arabcariana.com/ingest',
    });
    tracker.use(trackerAssist());
    tracker.start();
  }
}
initTracker();

window.global = globalThis;

/**
 * @see  https://umijs.org/zh-CN/plugins/plugin-initial-state
 * */
export async function getInitialState(): Promise<{
  settings?: Partial<LayoutSettings>;
  currentUser?: PortalUser;
  loading?: boolean;
  projectData?: {
    [key: string]: string;
  };

  fetchUserInfo: () => Promise<PortalUser | undefined>;
}> {
  const fetchUserInfo = async () => {
    try {
      const res = await GetCurrentUser();
      if (res.code === 0) {
        if (!isDev) {
          res.data?.name && tracker.setUserID(res.data?.name);
        }
        return res.data;
      }
    } catch (error) {
      history.push(loginPath);
    }
    return undefined;
  };

  const projectData = await fetchProjectInfo();

  // 如果不是登录页面，执行
  const { location } = history;
  if (
    location.pathname !== loginPath &&
    location.pathname !== registerPath &&
    location.pathname !== douyinAuthPath &&
    location.pathname !== dailyPath &&
    location.pathname !== dailyDetailPath
  ) {
    const currentUser = await fetchUserInfo();
    return {
      fetchUserInfo,
      currentUser,
      settings: defaultSettings,
      projectData,
    };
  }
  return {
    fetchUserInfo,
    settings: defaultSettings,
    projectData,
  };
}

// ProLayout 支持的api https://procomponents.ant.design/components/layout
export const layout: RunTimeLayoutConfig = ({ initialState }) => {
  return {
    rightContentRender: () => (
      <div style={{ display: 'flex', alignItems: 'center', marginLeft: 'auto' }}>
        <CustomerOnlineStatus />
        <ThemeToggle />
        <RightContent />
      </div>
    ),
    // waterMarkProps: {
    //   content: initialState?.currentUser?.name,
    // },
    footerRender: () => <div />,
    breadcrumbRender: false,
    onPageChange: () => {
      const { location } = history;
      // 如果没有登录，重定向到 login
      if (
        !initialState?.currentUser &&
        location.pathname !== loginPath &&
        location.pathname !== registerPath &&
        location.pathname !== douyinAuthPath
      ) {
        history.push(loginPath);
      }
    },
    bgLayoutImgList: [
      {
        src: 'https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/D2LWSqNny4sAAAAAAAAAAAAAFl94AQBr',
        left: 85,
        bottom: 100,
        height: '303px',
      },
      {
        src: 'https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/C2TWRpJpiC0AAAAAAAAAAAAAFl94AQBr',
        bottom: -68,
        right: -45,
        height: '303px',
      },
      {
        src: 'https://mdn.alipayobjects.com/yuyan_qk0oxh/afts/img/F6vSTbj8KpYAAAAAAAAAAAAAFl94AQBr',
        bottom: 0,
        left: 0,
        width: '331px',
      },
    ],
    // links: isDev
    //   ? [
    //       <Link key="openapi" to="/umi/plugin/openapi" target="_blank">
    //         <LinkOutlined />
    //         <span>OpenAPI 文档</span>
    //       </Link>,
    //     ]
    //   : [],
    menuHeaderRender: undefined,
    menuRender: (props, defaultDom) => {
      const { location } = history;
      if (location.pathname.startsWith('/project/')) {
        return false;
      } else {
        return defaultDom;
      }
    },
    // 自定义 403 页面
    // unAccessible: <div>unAccessible</div>,
    // 增加一个 loading 的状态
    childrenRender: (children) => {
      const { location } = history;

      if (
        location.pathname.startsWith('/project/') ||
        location.pathname.startsWith('/project-list') ||
        location.pathname.startsWith('/setting-project-list')
      ) {
        return children;
      } else {
        return (
          <GridContent contentWidth="Fixed" style={{ margin: '0 auto' }}>
            {children}
          </GridContent>
        );
      }
    },
    postMenuData(menusData) {
      if (!menusData) {
        return menusData as unknown as MenuDataItem[];
      }

      let newMenusData = [...menusData];
      const permissionCodes = initialState?.currentUser?.permissionCodes;

      if (
        !initialState ||
        !initialState.currentUser ||
        !permissionCodes?.includes(PermissionCode.ManageRead)
      ) {
        newMenusData = newMenusData.filter((i) => i.key !== '/setting');
      }

      if (
        !initialState ||
        !initialState.currentUser ||
        !permissionCodes?.includes(PermissionCode.MonitorRead)
      ) {
        newMenusData = newMenusData.filter((i) => i.key !== '/monitor');
      }

      return newMenusData;
    },
    ...initialState?.settings,
  };
};

/**
 * @name request 配置，可以配置错误处理
 * 它基于 axios 和 ahooks 的 useRequest 提供了一套统一的网络请求和错误处理方案。
 * @doc https://umijs.org/docs/max/request#配置
 */
export const request = {
  ...requestConfig,
};

export const reactQuery = {
  devtool: {
    initialIsOpen: true,
  },
  queryClient: {
    defaultOptions: {
      queries: {
        // 🟡 此配置具有的表现往往令人出乎意料，若无特殊需求，请默认关闭
        refetchOnWindowFocus: false,
      },
    },
  },
};

/**
 * @name rootContainer
 * @description 修改交给 react-dom 渲染时的根组件，添加路由标识
 */
export function rootContainer(container: JSX.Element) {
  const RouteContainer = () => {
    useEffect(() => {
      // 监听路由变化，更新 data-route 属性
      const updateDataRoute = () => {
        const pathname = history.location.pathname;
        const parts = pathname.split('/');
        const base = '/' + parts[1];
        document.documentElement.setAttribute('data-route', base);
      };

      updateDataRoute();

      const unlisten = history.listen(() => {
        updateDataRoute();
      });

      // 清理监听器
      return () => {
        unlisten();
      };
    }, []);

    return container;
  };

  return React.createElement(RouteContainer);
}
