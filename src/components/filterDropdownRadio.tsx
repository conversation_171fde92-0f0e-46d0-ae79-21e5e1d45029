import { CheckboxOptionType, Radio, RadioChangeEvent } from 'antd';
import { FilterDropdownProps } from 'antd/es/table/interface';

type FilterDropdownRadioProps = {
  title: string;
  options: (string | number | CheckboxOptionType<any>)[];
  filterDropdownProps: FilterDropdownProps;
  setValueChange: any;
};

const FilterDropdownRadio = (props: FilterDropdownRadioProps) => {
  const { title, options, filterDropdownProps, setValueChange } = props;
  const { setSelectedKeys, confirm, selectedKeys } = filterDropdownProps;

  return (
    <div style={{ paddingBlock: '6px' }}>
      <div style={{ fontWeight: 'bold', marginBottom: '4px', paddingInline: '12px' }}>{title}</div>
      <Radio.Group
        style={{
          display: 'flex',
          flexDirection: 'column',
          paddingInline: '12px',
          gap: '4px',
        }}
        options={options}
        onChange={(e: RadioChangeEvent) => {
          setSelectedKeys(e.target.value);
          confirm();
          setValueChange(e.target.value);
        }}
        value={selectedKeys}
      />
      <a
        style={{
          paddingInline: '12px',
          display: 'inline-block',
          borderTop: '1px solid #f0f0f0',
          width: '100%',
          marginTop: '4px',
          paddingTop: '4px',
        }}
        onClick={() => {
          setSelectedKeys([]);
          confirm();
          setValueChange(undefined);
        }}
      >
        重置
      </a>
    </div>
  );
};

export default FilterDropdownRadio;
