import { nanoid } from '@ant-design/pro-components';
import { useEffect, useRef } from 'react';

import Player, { Events, IPlayerOptions } from 'xgplayer';
import FlvPlugin from 'xgplayer-flv';
import HlsPlugin from 'xgplayer-hls';
import Mp4Plugin from 'xgplayer-mp4';
import 'xgplayer/dist/index.min.css';

interface PropsType {
  url?: string | { src: string; type?: string }[];
  type?: 'm3u8' | 'flv' | 'mp4';
  isLive: boolean;
  playerOptions?: IPlayerOptions | null;
  onPlayerReady?: (player: Player) => void;
  onError?: (errType: string) => void;
  onEnded?: () => void;
}

const XGPlayer = ({
  url,
  type,
  isLive,
  playerOptions,
  onPlayerReady,
  onError,
  onEnded,
}: PropsType) => {
  const domId = useRef(nanoid());
  const videoRef = useRef(null);

  useEffect(() => {
    const videoElement = videoRef.current;
    if (!videoElement) return;

    const options: IPlayerOptions = {
      id: domId.current,
      url,
      isLive,
      ...playerOptions,
    };

    if (type === 'flv' && FlvPlugin.isSupported()) {
      options.plugins = options.plugins ? [...options.plugins, FlvPlugin] : [FlvPlugin];
    } else if (type === 'm3u8' && HlsPlugin.isSupported()) {
      options.plugins = options.plugins ? [...options.plugins, HlsPlugin] : [HlsPlugin];
    } else if (type === 'mp4') {
      options.plugins = options.plugins ? [...options.plugins, Mp4Plugin] : [Mp4Plugin];
    }

    let player: Player | null = new Player(options);

    player.on(Events.READY, () => {
      if (player) {
        onPlayerReady && onPlayerReady(player);
      }
    });

    player.on(Events.ERROR, (error) => {
      if (onError) {
        onError(error.errorType);
      }
    });

    player.on(Events.ENDED, () => {
      if (onEnded) {
        onEnded();
      }
    });

    return () => {
      if (player) {
        player.destroy();
        player = null;
      }
    };
  }, [url, videoRef]);

  return <div id={domId.current} ref={videoRef} />;
};

export default XGPlayer;
