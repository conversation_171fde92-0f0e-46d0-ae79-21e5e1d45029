import { DateType } from '@/services/business';
import { XIAOPENG } from '@/utils/constant';
import { getTimeByTypeBusiness } from '@/utils/time';
import { DatePicker, Space } from 'antd';
import { RangePickerProps } from 'antd/es/date-picker';
import dayjs, { Dayjs } from 'dayjs';
import { useEffect, useState } from 'react';
import { Tabs, TabsList, TabsTrigger } from '../ui/tabs';

const { RangePicker } = DatePicker;

const disabledDate = (current: Dayjs, projectId?: string) => {
  if (projectId === XIAOPENG) {
    return current && current >= dayjs().subtract(1, 'day').startOf('day');
  } else {
    return current && current > dayjs().subtract(1, 'day').startOf('day');
  }
};

type TimeFilterDataDashboardProps = {
  radioValue?: DateType;
  setRadioValue?: React.Dispatch<React.SetStateAction<DateType | undefined>>;
  setRangeTime: React.Dispatch<React.SetStateAction<string[]>>;
  style?: React.CSSProperties;
  firstShowRangeTime?: (string | undefined)[];
  projectId?: string;
};

const TimeFilterDataDashboard = (props: TimeFilterDataDashboardProps) => {
  const { setRangeTime, radioValue, setRadioValue, style, firstShowRangeTime, projectId } = props;
  const [showRangeTime, setShowRangeTime] = useState<(string | undefined)[]>(
    firstShowRangeTime ? firstShowRangeTime : getTimeByTypeBusiness(DateType.LastWeek, projectId),
  );
  useEffect(() => {
    firstShowRangeTime && setShowRangeTime(firstShowRangeTime);
  }, [firstShowRangeTime]);

  const onChange = (value: string) => {
    setRadioValue?.(value as unknown as DateType);
    const range = getTimeByTypeBusiness(value as unknown as DateType, projectId);
    setRangeTime(range);
    setShowRangeTime(range);
  };

  const onChangeRange = (date: RangePickerProps['value'], dateString: string[]) => {
    setRangeTime(dateString);
    setShowRangeTime(dateString);
    setRadioValue?.(DateType.Custom);
  };

  return (
    <Space style={style}>
      <Tabs value={radioValue} onValueChange={onChange} className="whitespace-nowrap">
        <TabsList value={radioValue}>
          {projectId !== XIAOPENG ? (
            <TabsTrigger value={DateType.Yesterday}>昨天</TabsTrigger>
          ) : null}
          <TabsTrigger value={DateType.LastWeek}>近7天</TabsTrigger>
          <TabsTrigger value={DateType.LastMonth}>近30天</TabsTrigger>
          <TabsTrigger value={DateType.Last90Days}>近90天</TabsTrigger>
          {projectId !== XIAOPENG ? (
            <>
              <TabsTrigger value={DateType.NaturalWeek}>自然周</TabsTrigger>
              <TabsTrigger value={DateType.NaturalMonth}>自然月</TabsTrigger>
            </>
          ) : null}
        </TabsList>
      </Tabs>
      <RangePicker
        onChange={onChangeRange}
        disabledDate={(currentDate) => disabledDate(currentDate, projectId)}
        allowClear={false}
        defaultValue={[dayjs(showRangeTime[0]), dayjs(showRangeTime[1])]}
        value={[dayjs(showRangeTime[0]), dayjs(showRangeTime[1])]}
      />
    </Space>
  );
};

export default TimeFilterDataDashboard;
