import CreateModal, {
  TagGroupCreateParam,
  TagGroupEditParam,
} from '@/components/tagsList/createTagModal';
import useProjectId from '@/hooks/useProjectId';
import {
  CreateTagGroup,
  DeleteTagGroup,
  EditTagGroup,
  GetTagGroups,
  OrderTagsGroup,
  TagsType,
} from '@/services/tag';
import { CommonTagGroup } from '@/services/typings';
import { PlusCircleOutlined, SearchOutlined } from '@ant-design/icons';
import {
  ActionType,
  DragSortTable,
  PageContainer,
  ProColumns,
  ProFormInstance,
} from '@ant-design/pro-components';
import { useDebounce, useRequest } from 'ahooks';
import { Button, message, Popconfirm, Select, Space, Tag } from 'antd';
import { useRef, useState } from 'react';

import './index.module.less';

type TagsListProps = {
  title: React.ReactNode;
  typeId: TagsType;
};

const TagsList = (props: TagsListProps) => {
  const projectId = useProjectId();
  const { title, typeId } = props;

  const [editModalVisible, setEditModalVisible] = useState<boolean>(false);
  const [editingRow, setEditingRow] = useState<CommonTagGroup>();
  const [selectingRow, setSelectingRow] = useState<CommonTagGroup>();
  const [editingValue, setEditingValue] = useState<string[]>();
  const editingValueDebounced = useDebounce(editingValue);

  const formRef = useRef<ProFormInstance>();
  const tagTableRef = useRef<ActionType>();

  const { run: addTags } = useRequest(
    () => {
      if (editingRow) {
        return EditTagGroup(editingRow.id, {
          name: editingRow.name,
          addTagNames: editingValueDebounced,
        });
      }
      return Promise.reject();
    },
    {
      manual: true,
      onSuccess() {
        tagTableRef.current?.reload();
      },
    },
  );

  const handleDragSortEnd = async (
    beforeIndex: number,
    afterIndex: number,
    newDataSource: CommonTagGroup[],
  ) => {
    const tagGroupIds = newDataSource.map((item) => item.id);
    await OrderTagsGroup({
      tagGroupIds,
    });
    tagTableRef.current?.reload();
    message.success('修改列表排序成功');
  };
  const columns: ProColumns<CommonTagGroup>[] = [
    {
      title: '',
      dataIndex: 'sort',
      width: 10,
      hideInSearch: true,
      className: 'drag-visible',
    },
    {
      title: '标签组',
      dataIndex: 'name',
      fieldProps: {
        placeholder: '请输入标签组',
        prefix: <SearchOutlined />,
      },
      formItemProps: {
        label: null,
      },
      width: 174,
    },
    {
      title: '标签',
      dataIndex: 'tag',
      fieldProps: {
        placeholder: '请输入标签',
        prefix: <SearchOutlined />,
      },
      formItemProps: {
        label: null,
      },
      width: 636,
      render(_, record) {
        return (
          <div>
            {record.tags.map((tag) => (
              <Tag key={tag.id}>{tag.name}</Tag>
            ))}
            {editingRow?.id === record.id ? (
              <Select
                mode="tags"
                size="small"
                value={editingValue}
                autoFocus
                placeholder="多个标签可按回车或英文逗号自动分隔"
                style={{ minWidth: '150px' }}
                tokenSeparators={[',']}
                onChange={(v) => setEditingValue(v)}
                onBlur={() => {
                  if (editingValue && editingValue?.length > 0) {
                    addTags();
                    tagTableRef.current?.reload();
                  }
                  setEditingValue([]);
                  setEditingRow(undefined);
                }}
              />
            ) : (
              <Button size="small" onClick={() => setEditingRow(record)}>
                +添加
              </Button>
            )}
          </div>
        );
      },
    },
    {
      title: '操作',
      dataIndex: 'options',
      hideInSearch: true,
      align: 'left',
      width: 120,
      fixed: 'right',
      render(_, record) {
        return (
          <Space>
            <a
              type="link"
              onClick={() => {
                setSelectingRow(record);
                setEditModalVisible(true);
              }}
            >
              编辑
            </a>
            <Popconfirm
              onConfirm={async () => {
                await DeleteTagGroup(record.id);
                tagTableRef.current?.reload();
              }}
              title="确认删除"
            >
              <Button type="link">删除</Button>
            </Popconfirm>
          </Space>
        );
      },
    },
  ];

  const handleTagsChange = async (params: TagGroupEditParam | TagGroupCreateParam) => {
    const gotChange = Object.keys(params).some((key) => {
      const param = (params as TagGroupEditParam)?.[key as keyof TagGroupEditParam];
      const nameChange = params['name'] !== selectingRow?.name;
      const tagChange =
        key !== 'name' &&
        ((Array.isArray(param) && param.length > 0) ||
          (typeof param === 'object' && Object.keys(param).length > 0));
      return nameChange || tagChange;
    });
    if (selectingRow && gotChange) {
      const res = await EditTagGroup(selectingRow.id, params);
      tagTableRef.current?.reload();
      return res.code === 0;
    } else if (!selectingRow) {
      const res = await CreateTagGroup(typeId, { ...(params as TagGroupCreateParam), projectId });
      tagTableRef.current?.reload();
      return res.code === 0;
    }
    return true;
  };

  return (
    <PageContainer
      title={title}
      extra={[
        <Button
          key="add tag"
          type="primary"
          size="middle"
          style={{ transform: 'translate(0px,-5px)' }}
          onClick={() => {
            setSelectingRow(undefined);
            setEditModalVisible(true);
          }}
        >
          <PlusCircleOutlined />
          <span style={{ marginInlineStart: 4 }}>添加标签组</span>
        </Button>,
      ]}
    >
      <div style={{ background: '#fff' }}>
        <DragSortTable<CommonTagGroup>
          columns={columns}
          actionRef={tagTableRef}
          formRef={formRef}
          rowKey="id"
          pagination={false}
          scroll={{ x: 'max-content' }}
          className="custom-table"
          search={{
            labelWidth: 'auto',
            span: {
              xs: 24,
              sm: 24,
              md: 12,
              lg: 12,
              xl: 6,
              xxl: 6,
            },
            searchGutter: 20,
            collapsed: false,
            optionRender: () => [
              <Button key="search" onClick={() => formRef.current?.submit()}>
                查询
              </Button>,
            ],
          }}
          request={async (formParams) => {
            const res = await GetTagGroups(typeId, { projectId });
            let tagsFiltered = res.data;
            if (formParams.name) {
              // PM说都统一成模糊匹配了
              tagsFiltered = tagsFiltered?.filter((item) => item.name.includes(formParams.name));
            }
            if (formParams.tag) {
              tagsFiltered = tagsFiltered?.filter((item) =>
                item.tags.some((tag) => tag.name.includes(formParams.tag)),
              );
            }

            return {
              data: tagsFiltered,
              success: res.code === 0,
            };
          }}
          dragSortKey="sort"
          onDragSortEnd={handleDragSortEnd}
          options={false}
        />
      </div>

      <CreateModal
        type={selectingRow?.id ? 'edit' : 'create'}
        setVisible={setEditModalVisible}
        visible={editModalVisible}
        initialValues={selectingRow}
        onSubmitEdit={handleTagsChange}
        onSubmitCreate={handleTagsChange}
      />
    </PageContainer>
  );
};

export default TagsList;
