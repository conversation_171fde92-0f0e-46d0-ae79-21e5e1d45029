import type { FormInstance } from 'antd';
import { message, Modal, Space, Tooltip } from 'antd';
import type { Dispatch, SetStateAction } from 'react';
import React, { useEffect, useRef, useState } from 'react';

import { PlusCircleOutlined } from '@ant-design/icons';
import {
  ModalFormProps,
  ProForm,
  ProFormList,
  ProFormRadio,
  ProFormText,
} from '@ant-design/pro-components';
import type { FormListFieldData, FormListOperation } from 'antd/lib/form/FormList';
import { cloneDeep } from 'lodash-es';
import { CircleArrowDown, CircleArrowUp, Copy, Trash2 } from 'lucide-react';
export interface TagGroupCreateParam {
  name: string;
  // 1 最后 -1 最前
  defaultPosition?: number;
  projectId?: string;
  addTagNames?: string[];
  tagOrders?: string[];
}

export interface TagGroupEditParam {
  name?: string;
  addTagNames?: string[];
  changeTagNames?: Record<number, string>;
  deleteTagIds?: number[];
  tagOrders?: string[];
}

export type CreateModalFormProps = Omit<
  ModalFormProps,
  'onFinish' | 'visible' | 'initialValues'
> & {
  type: 'create' | 'edit';
  visible: boolean;
  setVisible: Dispatch<SetStateAction<boolean>>;
  onSubmitCreate?: (params: TagGroupCreateParam) => Promise<boolean>;
  onSubmitEdit?: (params: TagGroupEditParam) => Promise<boolean>;
  initialValues?: Partial<{ id?: number; name?: string; tags: { id?: number; name: string }[] }>;
};

const CreateModal: React.FC<CreateModalFormProps> = (props) => {
  const [deleteTagIds, setDeleteTagIds] = useState<number[]>([]);
  const formRef = useRef<FormInstance>();

  useEffect(() => {
    setDeleteTagIds([]);
  }, [props.initialValues]);

  return (
    <>
      <Modal
        title={props.type === 'create' ? '新建标签组' : '修改标签组'}
        width={468}
        open={props.visible}
        onOk={async () => {
          const fieldsValue = await formRef.current?.validateFields();
          if (!fieldsValue.tags) {
            message.error('有必填项未填');
          }
          formRef.current?.submit();
        }}
        onCancel={() => {
          props.setVisible(false);
        }}
        destroyOnHidden
      >
        <div style={{ height: 1, backgroundColor: '#F1F1F1', marginBottom: 20 }} />
        <ProForm<{ name: string; tags: { id?: number; name: string }[]; defaultPosition: number }>
          submitter={{
            render: false,
          }}
          initialValues={props.initialValues}
          formRef={formRef}
          layout="horizontal"
          labelAlign="right"
          labelCol={{ span: 7 }}
          style={{ marginLeft: -35 }}
          onFinish={async (values) => {
            if (props.type === 'create') {
              const params: TagGroupCreateParam = {
                name: values.name,
                addTagNames: values.tags.map((i) => i.name) || [],
                defaultPosition: values.defaultPosition,
                tagOrders: values.tags.map((i) => i.name) || [],
              };
              if (props.onSubmitCreate && (await props.onSubmitCreate(params))) {
                props.setVisible(false);
              }
            } else if (props.type === 'edit') {
              const changeTagNames: Record<number, string> = {};
              values.tags.forEach((i) => {
                if (i.id) {
                  changeTagNames[i.id] = i.name;
                }
              });
              const params: TagGroupEditParam = {
                name: values.name,
                addTagNames: values.tags.filter((i) => !i.id).map((i) => i.name),
                changeTagNames,
                deleteTagIds,
                tagOrders: values.tags.map((i) => i.name) || [],
              };
              console.log('params', params);

              if (props.onSubmitEdit && (await props.onSubmitEdit(params))) {
                props.setVisible(false);
              }
            }
          }}
        >
          <ProFormText
            name="name"
            label="标签组名称"
            width="md"
            placeholder="请输入标签组名称"
            rules={[{ required: true, message: '标签组名称必填' }]}
          />

          {props.type === 'create' && (
            <ProFormRadio.Group
              name="defaultPosition"
              width="md"
              label="默认排序"
              options={[
                {
                  label: '排在前面',
                  value: -1,
                },
                {
                  label: '排在后面',
                  value: 1,
                },
              ]}
            />
          )}
          <ProFormList
            label="标签名称"
            name="tags"
            actionRender={(field: FormListFieldData, action: FormListOperation) => {
              const currentKey = field.name;
              return [
                <Space key={'actions'} className="ml-2 flex items-center">
                  <Tooltip key={'up'} title="上移">
                    <CircleArrowUp
                      size={16}
                      strokeWidth={1}
                      onClick={() => {
                        if (currentKey > 0) {
                          action.move(currentKey - 1, currentKey);
                        }
                      }}
                    />
                  </Tooltip>
                  <Tooltip key={'down'} title="下移">
                    <CircleArrowDown
                      size={16}
                      strokeWidth={1}
                      onClick={() => {
                        if (currentKey < formRef.current?.getFieldValue('tags')?.length - 1) {
                          action.move(currentKey + 1, currentKey);
                        }
                      }}
                    />
                  </Tooltip>
                  <Tooltip key={'copy'} title="复制">
                    <Copy
                      size={16}
                      strokeWidth={1}
                      onClick={() => {
                        const tag = cloneDeep(formRef.current?.getFieldValue('tags')[currentKey]);
                        delete tag.id;
                        action.add(tag);
                      }}
                    />
                  </Tooltip>
                  <Tooltip key={'remove'} title="删除">
                    <Trash2
                      size={16}
                      strokeWidth={1}
                      onClick={() => {
                        if (formRef.current?.getFieldValue('tags')[currentKey]?.id) {
                          setDeleteTagIds([
                            ...deleteTagIds,
                            formRef.current?.getFieldValue('tags')[currentKey].id,
                          ]);
                        }
                        action.remove(currentKey);
                      }}
                    />
                  </Tooltip>
                </Space>,
              ];
            }}
            creatorButtonProps={{
              type: 'link',
              style: { left: '-36%' },
              position: 'bottom',
              icon: <PlusCircleOutlined />,
              creatorButtonText: '添加标签',
            }}
            creatorRecord={{
              name: '',
            }}
            rules={[
              {
                required: true,
                message: '请添加标签',
                validator(rule, value, callback) {
                  if (Array.isArray(value) && value.length === 0) {
                    callback('');
                    return;
                  }

                  callback();
                },
              },
              {
                required: true,
                // message: '存在重复标签',
                validator(rule, value, callback) {
                  if (Array.isArray(value) && value.length > 0) {
                    const items = value;
                    for (let i = 0; i < items.length; i++) {
                      for (let j = 0; j < i; j++) {
                        if (items[i].name === items[j].name) {
                          callback(
                            `第${j + 1}个和第${i + 1}个标签的名字重复, 都是${items[i].name}`,
                          );
                          return;
                        }
                      }
                    }
                  }

                  callback();
                },
              },
            ]}
          >
            <ProFormText
              name="name"
              width={225}
              fieldProps={{
                allowClear: false,
                onChange: () => {
                  formRef.current?.validateFields(['tags']);
                },
              }}
              placeholder="请输入标签名称"
              rules={[
                {
                  required: true,
                  message: '标签名称必填',
                },
              ]}
            />
          </ProFormList>
        </ProForm>
      </Modal>
    </>
  );
};

export default CreateModal;
