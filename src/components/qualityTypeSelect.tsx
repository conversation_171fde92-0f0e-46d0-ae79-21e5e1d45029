import {
  GetAnchorImageViolationSelect,
  GetAnchorImageViolationSelectDaily,
  GetQualityTypeSelect,
  QualityTypeSelectItem,
} from '@/services/quality';
import { GetQualityTypeSelectDaily } from '@/services/unauth';
import { useRequest } from '@umijs/max';
import { TreeSelect } from 'antd';

type QualityTypeSelectProps = {
  value?: string[];
  onChange?: (value: string[]) => void;
  projectId?: string;
  style?: React.CSSProperties;
  payload?: string | null;
  unAuth?: boolean;
  isPost?: boolean;
};

type TreeNode = {
  title: string;
  value: string;
  key: string;
  children?: TreeNode[];
};

const QualityTypeSelect = (props: QualityTypeSelectProps) => {
  const { value, onChange, projectId, style, payload, unAuth, isPost } = props;

  const { data: qualityData } = useRequest(
    unAuth
      ? () => GetQualityTypeSelectDaily({ payload })
      : () => GetQualityTypeSelect({ projectId }),
  );

  const qualityDataSelect = qualityData?.filter((item) => item.disabled === false);

  const { data: anchorImageSelectData } = useRequest(
    unAuth
      ? () => GetAnchorImageViolationSelectDaily({ payload })
      : () => GetAnchorImageViolationSelect({ projectId }),
  );

  const anchorImageSelect = anchorImageSelectData?.filter((item) => item.disabled === false);

  const groupData = (
    qualityDataSelect?: QualityTypeSelectItem[],
    anchorImageSelect?: QualityTypeSelectItem[],
  ) => {
    if (!qualityDataSelect) return [];

    const groupedByType: { [key: string | number]: TreeNode[] } = {};
    qualityDataSelect.forEach((item) => {
      if (!groupedByType[item.type]) {
        groupedByType[item.type] = [];
      }
      groupedByType[item.type].push({
        title: item.name,
        value: item.value,
        key: item.value,
      });
    });

    // 构造treeData数组
    const treeData = Object.keys(groupedByType).map((type, index) => ({
      title: type,
      value: `${type}-${index}`,
      key: `${type}-${index}`,
      children: groupedByType[type],
    }));

    if (anchorImageSelect && anchorImageSelect.length > 0 && !isPost) {
      treeData.push({
        title: '主播形象违规',
        value: 'anchor-image',
        key: 'anchor-image',
        children: anchorImageSelect.map((item) => ({
          title: item.name,
          value: item.value,
          key: item.value,
        })),
      });
    }
    return treeData;
  };

  const treeSelectData = groupData(qualityDataSelect, anchorImageSelect);

  return (
    <>
      {treeSelectData.length > 0 ? (
        <TreeSelect
          style={style}
          value={value}
          styles={{
            popup: {
              root: { maxHeight: 700, overflow: 'auto' },
            },
          }}
          treeData={treeSelectData}
          placeholder="质检类型"
          treeCheckable={true}
          onChange={onChange}
          maxTagCount="responsive"
          allowClear
          treeDefaultExpandAll
        />
      ) : (
        <TreeSelect
          style={style}
          value={undefined}
          styles={{
            popup: {
              root: { maxHeight: 700, overflow: 'auto' },
            },
          }}
          treeData={treeSelectData}
          placeholder="质检类型"
          treeCheckable={true}
          onChange={onChange}
          maxTagCount="responsive"
          allowClear
          treeDefaultExpandAll
        />
      )}
    </>
  );
};

export default QualityTypeSelect;
