import useProjectId from '@/hooks/useProjectId';
import { GetConversationSourceAccount } from '@/services/custom-data';
import { useRequest } from '@umijs/max';
import { Select } from 'antd';

type ConversationAccountSelectProps = {
  value?: string[];
  onChange?: (value: string[]) => void;
  style?: React.CSSProperties;
};

export default function ConversationAccountSelect(props: ConversationAccountSelectProps) {
  const { style } = props;
  const projectId = useProjectId();

  const { data: conversationAccountData } = useRequest(() =>
    GetConversationSourceAccount({ projectId }),
  );

  return (
    <Select
      mode="multiple"
      allowClear
      style={style}
      value={props?.value}
      placeholder="来源账号昵称"
      onChange={props?.onChange}
      options={conversationAccountData?.map((item) => {
        return {
          label: item?.name,
          value: item.value,
        };
      })}
    />
  );
}
