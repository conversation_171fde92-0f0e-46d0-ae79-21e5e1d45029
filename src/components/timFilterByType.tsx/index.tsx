import { getTimeByType } from '@/utils/time';
import { useSearchParams } from '@umijs/max';
import { DatePicker, Radio, RadioChangeEvent, Space } from 'antd';
import { RangePickerProps } from 'antd/es/date-picker';
import dayjs, { Dayjs } from 'dayjs';
import { useEffect, useState } from 'react';

const { RangePicker } = DatePicker;

type TimeFilterByTypeProps = {
  onTimeFilterChange: (data: {
    rangeTime: string[];
    radioValue: 'day' | 'week' | 'month' | null;
  }) => void;
};

// 跟timeFilter的差别在于固定单选类型 日 周 月
const TimeFilterByType = (props: TimeFilterByTypeProps) => {
  const { onTimeFilterChange } = props;
  const [searchParams] = useSearchParams();
  const today = dayjs();
  const date = searchParams.get('date');
  const initValue = date ? [date, date] : getTimeByType('day');
  const [rangeTime, setRangeTime] = useState<string[]>(initValue);
  const radioValueDefault = date
    ? date === today.subtract(1, 'day').format('YYYY-MM-DD')
      ? 'day'
      : null
    : 'day';
  const [radioValue, setRadioValue] = useState<'day' | 'week' | 'month' | null>(radioValueDefault);
  const [showRangeTime, setShowRangeTime] = useState<(string | undefined)[]>(initValue);

  const onChange = (e: RadioChangeEvent) => {
    const value = e.target.value;
    setRadioValue(value);
    const range = getTimeByType(value);
    setRangeTime(range);
    setShowRangeTime(range);
  };

  const onChangeRange = (date: RangePickerProps['value'], dateString: string[]) => {
    setRangeTime(dateString);
    setShowRangeTime(dateString);
    setRadioValue(null);
  };

  const disabledDate = (current: Dayjs) => {
    return current && current > dayjs().subtract(1, 'day').endOf('day');
  };

  useEffect(() => {
    onTimeFilterChange({ rangeTime, radioValue });
  }, [rangeTime, radioValue]);

  return (
    <Space>
      <Radio.Group
        value={radioValue}
        onChange={onChange}
        buttonStyle="solid"
        style={{ whiteSpace: 'nowrap' }}
      >
        <Radio.Button value="day">
          昨日
          {radioValue === 'day' ? ` ${dayjs().subtract(1, 'day').format('YYYY-MM-DD')}` : null}
        </Radio.Button>
        <Radio.Button value="week">
          自然周
          {radioValue === 'week'
            ? dayjs().day() === 1
              ? `${dayjs().subtract(1, 'week').year()}年 - 第${dayjs()
                  .subtract(1, 'week')
                  .week()}周`
              : `${dayjs().year()}年 - 第${dayjs().week()}周`
            : null}
        </Radio.Button>
        <Radio.Button value="month">
          自然月
          {radioValue === 'month'
            ? dayjs().date() === 1
              ? `${dayjs().subtract(1, 'month').format('YYYY-MM')}`
              : `${dayjs().format('YYYY-MM')}`
            : null}
        </Radio.Button>
      </Radio.Group>
      <RangePicker
        onChange={onChangeRange}
        disabledDate={disabledDate}
        allowClear={false}
        defaultValue={[dayjs(showRangeTime[0]), dayjs(showRangeTime[1])]}
        value={[dayjs(showRangeTime[0]), dayjs(showRangeTime[1])]}
      />
    </Space>
  );
};

export default TimeFilterByType;
