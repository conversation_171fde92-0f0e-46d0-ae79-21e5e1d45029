import { UserTag } from '@/pages/ProjectHome/PotentialUser/userList';
import { TagType } from '@/services/setting';
import { Tooltip } from 'antd';

type TagsShowProps = {
  tags?: TagType[];
};

const TagsShow = (props: TagsShowProps) => {
  const { tags } = props;
  const filterTags = tags?.filter((item) => item !== null) || [];

  if (filterTags?.length > 0) {
    return (
      <Tooltip
        color="#fff"
        title={
          <div>
            {filterTags?.map((item) => (
              <UserTag
                style={{ color: '#5b5c61', backgroundColor: '#f3f5fa', borderColor: '#000' }}
                key={item.id}
              >
                {item.name}
              </UserTag>
            ))}
          </div>
        }
      >
        {filterTags?.slice(0, 4).map((item) => (
          <UserTag
            style={{ color: '#5b5c61', backgroundColor: '#f3f5fa', borderColor: '#000' }}
            key={item.id}
          >
            {item.name}
          </UserTag>
        ))}
      </Tooltip>
    );
  } else {
    return null;
  }
};

export default TagsShow;
