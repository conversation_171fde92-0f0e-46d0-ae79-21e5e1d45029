import useProjectId from '@/hooks/useProjectId';
import { GetTagGroups, TagsType } from '@/services/tag';
import { useQuery } from '@umijs/max';
import { Select } from 'antd';

type TagsSelectProps = {
  value?: string[];
  onChange?: (value: string[]) => void;
  style?: React.CSSProperties;
};

const TagsSelect = (props: TagsSelectProps) => {
  const { style } = props;
  const projectId = useProjectId();

  const { data: tagsGroupData } = useQuery(
    ['account-tagsData', projectId],
    () => GetTagGroups(TagsType.accountTag, { projectId }),
    {
      staleTime: Number.POSITIVE_INFINITY,
    },
  );

  const tagsData = tagsGroupData?.data;

  return (
    <Select
      mode="multiple"
      maxTagCount="responsive"
      allowClear
      style={style}
      value={props?.value}
      placeholder="账号标签"
      onChange={props?.onChange}
      options={tagsData?.map((item) => {
        return {
          label: item?.name,
          options: item?.tags.map((tag) => {
            return {
              label: tag?.name,
              value: tag?.id,
            };
          }),
        };
      })}
    />
  );
};

export default TagsSelect;
