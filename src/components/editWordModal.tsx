import { QualityMonitorWordList, SceneTypeEnum } from '@/services/quality';
import { PlusCircleOutlined } from '@ant-design/icons';
import { ActionType, ModalForm } from '@ant-design/pro-components';
import { useRequest } from 'ahooks';
import { Button, Space } from 'antd';
import { Settings } from 'lucide-react';
import BehaviorEditTable from './editWordEditTable/behaviorEditTable';
import CommentEditTable from './editWordEditTable/commentEditTable';
import TextEditTable from './editWordEditTable/textEditTable';

type EditWordModalProps = {
  projectId?: string;
};

export const toolBarBtnRender = (action: ActionType | undefined) => (
  <Button
    type="primary"
    size="middle"
    key={'add'}
    onClick={() => {
      action?.addEditRecord?.({
        id: (Math.random() * 1000000).toFixed(0),
        type: 2,
      });
    }}
  >
    <PlusCircleOutlined />
    添加
  </Button>
);

const EditWordModal = (props: EditWordModalProps) => {
  const { projectId } = props;
  const {
    data: monitorWordData,
    loading,
    refresh,
  } = useRequest(() => QualityMonitorWordList({ projectId }), {
    ready: !!projectId,
  });

  const conversionMonitorWordData = monitorWordData?.data?.map((item) => {
    const suspectedWord = item.suspectedWord?.join(',');
    const sensitiveWord = item.sensitiveWord?.join(',');
    return {
      ...item,
      suspectedWord,
      sensitiveWord,
    };
  });

  const textDataSource = conversionMonitorWordData?.filter(
    (item) => item.sceneType === SceneTypeEnum.TEXT,
  );
  const behaviorDataSource = conversionMonitorWordData?.filter(
    (item) => item.sceneType === SceneTypeEnum.BEHAVIOR,
  );
  const commentDataSource = conversionMonitorWordData?.filter(
    (item) => item.sceneType === SceneTypeEnum.COMMENT,
  );

  return (
    <ModalForm
      title="编辑"
      key={'editWords'}
      submitter={false}
      trigger={
        <Space className="text-primary hover:cursor-pointer">
          <Settings size={13} /> 敏感词设置
        </Space>
      }
      autoFocusFirstInput
      modalProps={{
        destroyOnHidden: true,
      }}
      width="80%"
    >
      <TextEditTable
        projectId={projectId}
        textDataSource={textDataSource}
        refresh={refresh}
        loading={loading}
      />
      <BehaviorEditTable
        projectId={projectId}
        behaviorDataSource={behaviorDataSource}
        refresh={refresh}
        loading={loading}
      />
      <CommentEditTable
        projectId={projectId}
        commentDataSource={commentDataSource}
        refresh={refresh}
        loading={loading}
      />
    </ModalForm>
  );
};

export default EditWordModal;
