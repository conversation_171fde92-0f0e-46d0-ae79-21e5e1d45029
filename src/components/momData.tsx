import { SvgIcon } from '@/components/SvgIcon';
import { isNil } from 'lodash-es';
import { cn } from '@/lib/utils';

type MomDataProps = {
  rate?: string | number;
  style?: React.CSSProperties;
  className?: string;
};

const MomData = (props: MomDataProps) => {
  const { rate, className } = props;

  if (rate === '' || isNil(rate)) {
    return <div className="text-gray-500">--</div>;
  }

  const baseStyles =
    "flex items-center justify-center gap-[1px] w-[50px] mx-auto text-xs font-normal font-['OPPOSans']";

  return (
    <>
      {Number(rate) >= 0 ? (
        <div className={cn(baseStyles, 'text-[#f54848]', className)}>
          <SvgIcon icon={'local:outline/rate-rise'} x={-3} />
          {rate}%
        </div>
      ) : (
        <div className={cn(baseStyles, 'text-[#30b824]', className)}>
          <SvgIcon icon={'local:outline/rate-decline'} x={-3} />
          {rate}%
        </div>
      )}
    </>
  );
};

export default MomData;
