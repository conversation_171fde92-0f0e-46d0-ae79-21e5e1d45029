type DataDictionaryItem = {
  name: string;
  definition: string;
  updateTime: string;
  dataSource: string;
};

export type DataDictionaryCategory = {
  name: string;
  routeKeys?: string[]; // 新增：关联的路由关键词
  items: DataDictionaryItem[];
};

// 数据字典数据 - 基于飞书文档"数据集字段字典"部分内容
export const dataDictionaryData: DataDictionaryCategory[] = [
  {
    name: '数据大盘',
    routeKeys: ['overview/data-dashboard', 'overview'],
    items: [
      {
        name: '作品发布数',
        definition: '统计周期内发布的短视频数量',
        updateTime:
          '更新时间不固定，一般正常10点左右可正常看昨日数据。每天9点～12点，每隔1小时同步一次昨日（T-1）数据',
        dataSource: '对齐车云店企业号后台-BI工具-图表，最近1天有数',
      },
      {
        name: '直播场次',
        definition: '统计周期内开播次数',
        updateTime:
          '每天早上10点开始更新昨日（T-1）数据，官方数据未更新时，每隔1小时重新获取一次数据',
        dataSource: '巨量官方接口，可对齐巨量引擎后台',
      },
      {
        name: '直播时长',
        definition: '统计周期内开播场次累计的直播时长',
        updateTime:
          '每天早上10点开始更新昨日（T-1）数据，官方数据未更新时，每隔1小时重新获取一次数据',
        dataSource: '巨量官方接口，可对齐巨量引擎后台',
      },
      {
        name: '空挂播场次',
        definition: '统计周期内有空挂播违规的直播场次数',
        updateTime: '次日凌晨00:00开始生成结果昨日判断结果',
        dataSource: '乾坤圈自定义计算逻辑',
      },
      {
        name: '敏感词场次',
        definition: '统计周期内直播中存在敏感词违规的直播场次数',
        updateTime: '次日凌晨00:00开始生成结果昨日判断结果',
        dataSource: '乾坤圈自定义计算逻辑',
      },
      {
        name: '主播形象违规',
        definition: '统计周期内主播着装、妆容、发型等存在违规的直播场次',
        updateTime: '次日凌晨00:00开始生成结果昨日判断结果',
        dataSource: '乾坤圈自定义计算逻辑',
      },
      {
        name: '作品互动总数',
        definition: '统计周期内发布的短视频获得的合计点赞+评论+分享次数。',
        updateTime: '待确定',
        dataSource: '待确定',
      },
      {
        name: '播放量',
        definition: '统计周期内发布的短视频的播放量',
        updateTime: '',
        dataSource: '',
      },
      {
        name: '点赞数',
        definition: '统计周期内发布的短视频的点赞数',
        updateTime: '',
        dataSource: '',
      },
      {
        name: '评论数',
        definition: '统计周期内发布的短视频的评论数',
        updateTime: '',
        dataSource: '',
      },
      {
        name: '分享数',
        definition: '统计周期内发布的短视频的分享数',
        updateTime: '',
        dataSource: '',
      },
      {
        name: '直播观看次数',
        definition: '统计周期内开播的累计观看次数',
        updateTime:
          '每隔10分钟更新一次数据；每天早上10点开始更新昨日（T-1）数据，官方数据未更新时，每隔1小时重新获取一次数据',
        dataSource: '每隔10分钟从抖音app主页获取；巨量官方接口，可对齐巨量引擎后台',
      },
      {
        name: '观看人数',
        definition: '统计周期内开播的累计观看人数',
        updateTime:
          '每天早上10点开始更新昨日（T-1）数据，官方数据未更新时，每隔1小时重新获取一次数据',
        dataSource: '巨量官方接口，可对齐巨量引擎后台',
      },
      {
        name: '直播点赞',
        definition: '统计周期内通过直播获得的累计点赞数',
        updateTime:
          '每隔10分钟更新一次数据；每天早上10点开始更新昨日（T-1）数据，官方数据未更新时，每隔1小时重新获取一次数据',
        dataSource: '每隔10分钟从抖音app主页获取；巨量官方接口，可对齐巨量引擎后台',
      },
      {
        name: '直播弹幕',
        definition: '统计周期内通过直播获得的累计弹幕数',
        updateTime:
          '每隔10分钟更新一次数据；每天早上10点开始更新昨日（T-1）数据，官方数据未更新时，每隔1小时重新获取一次数据',
        dataSource: '每隔10分钟更新一次数据；巨量官方接口，可对齐巨量引擎后台',
      },
      {
        name: '私信人数',
        definition: '统计周期内有开口发起咨询的用户人数',
        updateTime:
          '每天早上10点开始更新昨日（T-1）数据，官方数据未更新时，每隔1小时重新获取一次数据',
        dataSource: '抖音创作者后台（接口下架且未授权，找别的账号代替）',
      },
      {
        name: '接收消息',
        definition: '统计周期内接收到的用户发起的消息次数',
        updateTime:
          '每天早上10点开始更新昨日（T-1）数据，官方数据未更新时，每隔1小时重新获取一次数据',
        dataSource: '抖音创作者后台（接口下架且未授权，找别的账号代替）',
      },
      {
        name: '3min回复',
        definition: '统计周期内有在3min内回复的私信消息数',
        updateTime:
          '每天早上10点开始更新昨日（T-1）数据，官方数据未更新时，每隔1小时重新获取一次数据',
        dataSource: '抖音创作者后台（接口下架且未授权，找别的账号代替）',
      },
      {
        name: '涨粉总数',
        definition: '统计周期内账号的涨粉总数',
        updateTime: '每隔10分钟更新一次数据',
        dataSource: '每隔10分钟更新一次数据',
      },
      {
        name: '直播涨粉',
        definition: '统计周期内通过直播获得的新增粉丝数',
        updateTime:
          '每天早上10点开始更新昨日（T-1）数据，官方数据未更新时，每隔1小时重新获取一次数据',
        dataSource: '巨量官方接口，可对齐巨量引擎后台',
      },
      {
        name: '其他涨粉',
        definition: '统计周期内，除了直播之外的途径获得的粉丝数。其他涨粉=涨粉总数-直播涨粉',
        updateTime:
          '每天早上10点开始更新昨日（T-1）数据，官方数据未更新时，每隔1小时重新获取一次数据',
        dataSource: '巨量官方接口，可对齐巨量引擎后台',
      },
      {
        name: '线索量',
        definition: '统计周期内获得的总线索量',
        updateTime:
          '每天早上10点开始更新昨日（T-1）数据，官方数据未更新时，每隔1小时重新获取一次数据。通过线索明细进行汇总统计',
        dataSource: '对齐企业号车云店用户管理-线索管理-线索公海明细',
      },
      {
        name: '直播线索',
        definition: '统计周期内通过直播获得的去重线索数',
        updateTime:
          '每天早上10点开始更新昨日（T-1）数据，官方数据未更新时，每隔1小时重新获取一次数据。通过线索明细进行汇总统计',
        dataSource: '对齐企业号车云店用户管理-线索管理-线索公海明细',
      },
      {
        name: '作品线索',
        definition: '统计周期内通过短视频获得的去重线索数',
        updateTime:
          '每天早上10点开始更新昨日（T-1）数据，官方数据未更新时，每隔1小时重新获取一次数据。通过线索明细进行汇总统计',
        dataSource: '对齐企业号车云店用户管理-线索管理-线索公海明细',
      },
      {
        name: '私信线索',
        definition: '统计周期内通过私信获得的去重线索数',
        updateTime:
          '每天早上10点开始更新昨日（T-1）数据，官方数据未更新时，每隔1小时重新获取一次数据。通过线索明细进行汇总统计',
        dataSource: '对齐企业号车云店用户管理-线索管理-线索公海明细',
      },
      {
        name: '其他线索',
        definition:
          '统计周期内，除了直播、作品、私信之外的途径获得的去重线索数。其他线索数=总线索数-直播线索数-作品线索数-私信线索数',
        updateTime:
          '每天早上10点开始更新昨日（T-1）数据，官方数据未更新时，每隔1小时重新获取一次数据。通过线索明细进行汇总统计',
        dataSource: '对齐企业号车云店用户管理-线索管理-线索公海明细',
      },
    ],
  },
  {
    name: '实时直播地图',
    routeKeys: ['overview/live-map'],
    items: [
      {
        name: '观看人数',
        definition: '直播间观看人数',
        updateTime: '每隔10分钟更新一次数据',
        dataSource: '每隔10分钟从抖音app主页获取',
      },
      {
        name: '点赞次数',
        definition: '直播间点赞次数',
        updateTime: '每隔10分钟更新一次数据',
        dataSource: '每隔10分钟从抖音app主页获取',
      },
    ],
  },
  {
    name: '行业总榜',
    routeKeys: ['management/overview'],
    items: [
      {
        name: '相关账号数',
        definition: '对应品牌下，行业总榜中进行排行的矩阵账号数',
        updateTime: '每隔10分钟更新一次数据',
        dataSource: '每隔10分钟从抖音app主页获取',
      },
      {
        name: '账号数量环比增长',
        definition: '账号数量环比增长率 = （本期账号数 - 上期账号数） / 上期账号数 × 100%',
        updateTime: '每隔10分钟更新一次数据',
        dataSource: '每隔10分钟从抖音app主页获取',
      },
      {
        name: '粉丝总量',
        definition: '账号累计粉丝总数',
        updateTime: '每隔10分钟更新一次数据',
        dataSource: '每隔10分钟从抖音app主页获取',
      },
      {
        name: '粉丝增量',
        definition: '账号累计粉丝新增粉丝数（算上流失数），可能会存在负值',
        updateTime: '每隔10分钟更新一次数据',
        dataSource: '每隔10分钟从抖音app主页获取',
      },
      {
        name: '作品互动',
        definition: '作品点赞次数+作品分享次数+作品评论次数',
        updateTime: '每隔10分钟更新一次数据',
        dataSource: '每隔10分钟从抖音app主页获取',
      },
      {
        name: '作品发布量',
        definition: '账号总作品数',
        updateTime: '每隔10分钟更新一次数据',
        dataSource: '每隔10分钟从抖音app主页获取',
      },
      {
        name: '直播场次',
        definition: '账号总直播场次（品牌开始录入时的直播场次）',
        updateTime: '每隔10分钟更新一次数据',
        dataSource: '每隔10分钟从抖音app主页获取',
      },
      {
        name: '直播观看人次',
        definition: '直播间观看次数',
        updateTime: '每隔10分钟更新一次数据',
        dataSource: '每隔10分钟从抖音app主页获取',
      },
      {
        name: '直播点赞数',
        definition: '直播间点赞次数',
        updateTime: '每隔10分钟更新一次数据',
        dataSource: '每隔10分钟从抖音app主页获取',
      },
      {
        name: '直播点赞数环比增长',
        definition: '点赞数环比增长率 = （本期点赞数 - 上期点赞数） / 上期点赞数 × 100%',
        updateTime: '每隔10分钟更新一次数据',
        dataSource: '每隔10分钟从抖音app主页获取',
      },
    ],
  },
  {
    name: '行业作品榜',
    routeKeys: ['management/post-overview'],
    items: [
      {
        name: '互动量',
        definition: '作品点赞次数+作品分享次数+作品评论次数',
        updateTime: '每隔10分钟更新一次数据',
        dataSource: '每隔10分钟从抖音app主页获取',
      },
      {
        name: '发布时间',
        definition: '行业TOP视频的发布时间',
        updateTime: '每隔10分钟更新一次数据',
        dataSource: '每隔10分钟从抖音app主页获取',
      },
      {
        name: '视频时长',
        definition: '短视频时长，单位s，当为0秒时表示视频为图文类型',
        updateTime: '每隔10分钟更新一次数据',
        dataSource: '每隔10分钟从抖音app主页获取',
      },
      {
        name: '视频点赞',
        definition: '作品从发布到昨天（T-1）累计的点赞数',
        updateTime: '每隔10分钟更新一次数据',
        dataSource: '每隔10分钟从抖音app主页获取',
      },
      {
        name: '作品评论数',
        definition: '作品从发布到昨天（T-1）累计的评论数',
        updateTime: '每隔10分钟更新一次数据',
        dataSource: '每隔10分钟从抖音app主页获取',
      },
      {
        name: '作品转发数',
        definition: '作品从发布到昨天（T-1）累计的分享数',
        updateTime: '每隔10分钟更新一次数据',
        dataSource: '每隔10分钟从抖音app主页获取',
      },
    ],
  },
  {
    name: '行业直播榜',
    routeKeys: ['management/live-overview'],
    items: [
      {
        name: '直播点赞数',
        definition: '直播间点赞次数',
        updateTime: '每隔10分钟更新一次数据',
        dataSource: '每隔10分钟从抖音app主页获取',
      },
      {
        name: '发布时间',
        definition: '行业TOP视频的发布时间',
        updateTime: '每隔10分钟更新一次数据',
        dataSource: '每隔10分钟从抖音app主页获取',
      },
    ],
  },
  {
    name: '团队总览',
    routeKeys: ['team/overview'],
    items: [
      {
        name: '3min回复率',
        definition: '3min内回复消息数 / 私信消息接收数',
        updateTime: '更新时间不固定，每天9点～14点，每隔1小时同步一次昨日（T-1）数据',
        dataSource: '对齐车云店企业号后台-BI工具-图表',
      },
      {
        name: '总消耗',
        definition: '短视频广告消耗+直播广告消耗',
        updateTime: '更新时间不固定，每天9点～14点，每隔1小时同步一次昨日（T-1）数据',
        dataSource: '对齐车云店企业号后台-BI工具-图表',
      },
      {
        name: '线索总数',
        definition: '作品线索数+直播线索数',
        updateTime: '更新时间不固定，每天9点～14点，每隔1小时同步一次昨日（T-1）数据',
        dataSource: '对齐车云店企业号后台-BI工具-图表',
      },
      {
        name: '单条线索成本',
        definition: '总消耗/线索总数，包含直播和短视频',
        updateTime: '更新时间不固定，每天9点～14点，每隔1小时同步一次昨日（T-1）数据',
        dataSource: '对齐车云店企业号后台-BI工具-图表',
      },
      {
        name: '作品发布总数',
        definition: '所选时间周期内，新发布的视频数量',
        updateTime: '更新时间不固定，每天9点～14点，每隔1小时同步一次昨日（T-1）数据',
        dataSource: '对齐车云店企业号后台-BI工具-图表',
      },
      {
        name: '投流作品数',
        definition: '有产生广告消耗的作品数量',
        updateTime: '更新时间不固定，每天9点～14点，每隔1小时同步一次昨日（T-1）数据',
        dataSource: '对齐车云店企业号后台-BI工具-图表',
      },
      {
        name: '粉丝增长数',
        definition: '所选时间周期内，整个账号净增粉丝数（流失也计算在内）',
        updateTime: '更新时间不固定，每天9点～14点，每隔1小时同步一次昨日（T-1）数据',
        dataSource: '对齐车云店企业号后台-BI工具-图表',
      },
      {
        name: '作品互动数',
        definition: '作品点赞数+作品评论数+作品分享数',
        updateTime: '更新时间不固定，每天9点～14点，每隔1小时同步一次昨日（T-1）数据',
        dataSource: '对齐车云店企业号后台-BI工具-图表',
      },
      {
        name: '作品播放数',
        definition:
          '该播放量是服务器口径， 而播放人数是抖音APP端埋点口径，如果发现播放量 比 播放人数还小，那可能该短视频存在作弊流量被服务器过滤了',
        updateTime: '更新时间不固定，每天9点～14点，每隔1小时同步一次昨日（T-1）数据',
        dataSource: '对齐车云店企业号后台-BI工具-图表',
      },
      {
        name: '作品平均播放数',
        definition: '所选时间周期内，作品播放数/作品发布总数，四舍五入',
        updateTime: '更新时间不固定，每天9点～14点，每隔1小时同步一次昨日（T-1）数据',
        dataSource: '对齐车云店企业号后台-BI工具-图表',
      },
      {
        name: '作品点赞数',
        definition: '所选时间周期内发布的作品产生的点赞数',
        updateTime: '更新时间不固定，每天9点～14点，每隔1小时同步一次昨日（T-1）数据',
        dataSource: '对齐车云店企业号后台-BI工具-图表',
      },
      {
        name: '作品分享数',
        definition: '所选时间周期内发布的作品产生的分享数',
        updateTime: '更新时间不固定，每天9点～14点，每隔1小时同步一次昨日（T-1）数据',
        dataSource: '对齐车云店企业号后台-BI工具-图表',
      },
      {
        name: '作品评论数',
        definition: '所选时间周期内发布的作品产生的评论数',
        updateTime: '更新时间不固定，每天9点～14点，每隔1小时同步一次昨日（T-1）数据',
        dataSource: '对齐车云店企业号后台-BI工具-图表',
      },
      {
        name: '作品完播率',
        definition:
          '完播率是用 抖音APP端 埋点数据统计的， 即抖音APP端看完短视频的事件数量 / 抖音APP端 视频播放埋点事件数',
        updateTime: '更新时间不固定，每天9点～14点，每隔1小时同步一次昨日（T-1）数据',
        dataSource: '对齐车云店企业号后台-BI工具-图表',
      },
      {
        name: '作品广告消耗',
        definition: '所选周期内，发布的作品产生的广告消耗费用',
        updateTime: '更新时间不固定，每天9点～14点，每隔1小时同步一次昨日（T-1）数据',
        dataSource: '对齐车云店企业号后台-BI工具-图表',
      },
      {
        name: '作品线索数',
        definition: '所选周期内，发布的作品产生的线索数',
        updateTime: '更新时间不固定，每天9点～14点，每隔1小时同步一次昨日（T-1）数据',
        dataSource: '对齐车云店企业号后台-BI工具-图表',
      },
      {
        name: '作品线索成本',
        definition:
          '短视频锚点/评论区链接挂载的表单/电话组件获得的线索总次数，不包含引导到私信获得的线索',
        updateTime: '更新时间不固定，每天9点～14点，每隔1小时同步一次昨日（T-1）数据',
        dataSource: '对齐车云店企业号后台-BI工具-图表',
      },
      {
        name: '引导私信线索量',
        definition: '从短视频 私信咨询锚点 跳转到私信然后留资的次数',
        updateTime: '更新时间不固定，每天9点～14点，每隔1小时同步一次昨日（T-1）数据',
        dataSource: '对齐车云店企业号后台-BI工具-图表',
      },
      {
        name: '直播场次',
        definition: '所选时间周期内，开播的直播场次（以开播时间进行计算）',
        updateTime: '更新时间不固定，每天9点～14点，每隔1小时同步一次昨日（T-1）数据',
        dataSource: '对齐车云店企业号后台-BI工具-图表',
      },
      {
        name: '投流直播场次',
        definition: '有产生广告消耗的直播场次',
        updateTime: '更新时间不固定，每天9点～14点，每隔1小时同步一次昨日（T-1）数据',
        dataSource: '对齐车云店企业号后台-BI工具-图表',
      },
      {
        name: '直播间曝光人数',
        definition: '直播间曝光人数',
        updateTime: '更新时间不固定，每天9点～14点，每隔1小时同步一次昨日（T-1）数据',
        dataSource: '对齐车云店企业号后台-BI工具-图表',
      },
      {
        name: '直播间进入人数',
        definition: '直播间观看人数',
        updateTime: '更新时间不固定，每天9点～14点，每隔1小时同步一次昨日（T-1）数据',
        dataSource: '对齐车云店企业号后台-BI工具-图表',
      },
      {
        name: '直播互动人数',
        definition:
          '直播间互动人数，由于会进行去重，所以不等于直播评论人数+点赞人数+分享人数。例如：同个用户即点赞又评论，只算1。',
        updateTime: '更新时间不固定，每天9点～14点，每隔1小时同步一次昨日（T-1）数据',
        dataSource: '对齐车云店企业号后台-BI工具-图表',
      },
      {
        name: '直播评论人数',
        definition: '直播间评论人数',
        updateTime: '更新时间不固定，每天9点～14点，每隔1小时同步一次昨日（T-1）数据',
        dataSource: '对齐车云店企业号后台-BI工具-图表',
      },
      {
        name: '直播点赞人数',
        definition: '直播间点赞人数',
        updateTime: '更新时间不固定，每天9点～14点，每隔1小时同步一次昨日（T-1）数据',
        dataSource: '对齐车云店企业号后台-BI工具-图表',
      },
      {
        name: '直播分享人数',
        definition: '直播间分享人数',
        updateTime: '更新时间不固定，每天9点～14点，每隔1小时同步一次昨日（T-1）数据',
        dataSource: '对齐车云店企业号后台-BI工具-图表',
      },
      {
        name: '小风车和讲解卡点击人数',
        definition:
          '不包含小雪花，同时和直播大屏统计口径会有出入。这个指标是按卡片粒度去重的 ，直播大屏页面是全局去重的。 如果一场直播中途切换了卡片，那么数据集的这个指标会大一些， 直播大屏是全局去重不管你用了多少不同卡片',
        updateTime: '更新时间不固定，每天9点～14点，每隔1小时同步一次昨日（T-1）数据',
        dataSource: '对齐车云店企业号后台-BI工具-图表',
      },
      {
        name: '直播广告消耗',
        definition: '所选周期内，开播直播间产生的广告消耗费用',
        updateTime: '更新时间不固定，每天9点～14点，每隔1小时同步一次昨日（T-1）数据',
        dataSource: '对齐车云店企业号后台-BI工具-图表',
      },
      {
        name: '直播线索数',
        definition: '直播期间飞鱼全场景线索人数',
        updateTime: '更新时间不固定，每天9点～14点，每隔1小时同步一次昨日（T-1）数据',
        dataSource: '对齐车云店企业号后台-BI工具-图表',
      },
      {
        name: '直播线索成本',
        definition: '直播广告消耗/直播线索数，四舍五入',
        updateTime: '更新时间不固定，每天9点～14点，每隔1小时同步一次昨日（T-1）数据',
        dataSource: '对齐车云店企业号后台-BI工具-图表',
      },
    ],
  },
  {
    name: '作品列表',
    routeKeys: ['platform/product-setting'],
    items: [
      {
        name: '作品时长',
        definition: '短视频时长，单位s，当为0秒时表示视频为图文类型',
        updateTime:
          '更新时间不固定，一般正常10点左右可正常看昨日数据。每天9点～12点，每隔1小时同步一次昨日（T-1）数据',
        dataSource: '对齐车云店企业号后台-BI工具-图表，最近1天有数据的作品',
      },
      {
        name: '互动量',
        definition: '点赞数+评论数+分享数',
        updateTime:
          '更新时间不固定，一般正常10点左右可正常看昨日数据。每天9点～12点，每隔1小时同步一次昨日（T-1）数据',
        dataSource: '对齐车云店企业号后台-BI工具-图表，最近1天有数据的作品',
      },
      {
        name: '播放量',
        definition:
          '作品从发布到昨天（T-1）累计的播放量。该播放量是服务器口径， 而播放人数是抖音APP端埋点口径，如果发现播放量 比 播放人数还小，那可能该短视频存在作弊流量被服务器过滤了',
        updateTime:
          '更新时间不固定，一般正常10点左右可正常看昨日数据。每天9点～12点，每隔1小时同步一次昨日（T-1）数据。由于为T-1数据，所以和当天在抖音app主页看到的会有出入。',
        dataSource: '对齐车云店企业号后台-BI工具-图表，最近1天有数据的作品',
      },
      {
        name: '点赞数',
        definition: '作品从发布到昨天（T-1）累计的点赞数',
        updateTime:
          '更新时间不固定，一般正常10点左右可正常看昨日数据。每天9点～12点，每隔1小时同步一次昨日（T-1）数据。由于为T-1数据，所以和当天在抖音app主页看到的会有出入。',
        dataSource: '对齐车云店企业号后台-BI工具-图表，最近1天有数据的作品',
      },
      {
        name: '评论数',
        definition: '作品从发布到昨天（T-1）累计的评论数',
        updateTime:
          '更新时间不固定，一般正常10点左右可正常看昨日数据。每天9点～12点，每隔1小时同步一次昨日（T-1）数据。由于为T-1数据，所以和当天在抖音app主页看到的会有出入。',
        dataSource: '对齐车云店企业号后台-BI工具-图表，最近1天有数据的作品',
      },
      {
        name: '收藏数',
        definition: '作品从发布到昨天（T-1）累计的收藏数',
        updateTime: '数据产生变化时，每隔1小时更新1次数据。',
        dataSource: '数据产生变化时，每隔1小时从抖音app主页获取。车云店企业号后台没有此数据维度',
      },
      {
        name: '分享数',
        definition: '作品从发布到昨天（T-1）累计的分享数',
        updateTime:
          '更新时间不固定，一般正常10点左右可正常看昨日数据。每天9点～12点，每隔1小时同步一次昨日（T-1）数据。由于为T-1数据，所以和当天在抖音app主页看到的会有出入。',
        dataSource: '对齐车云店企业号后台-BI工具-图表，最近1天有数据的作品',
      },
    ],
  },
  {
    name: '直播列表',
    routeKeys: ['platform/live-setting'],
    items: [
      {
        name: '直播开始时间/直播结束时间',
        definition: '直播开始时间/直播结束时间',
        updateTime:
          '每天早上10点开始更新昨日（T-1）数据，官方数据未更新时，每隔1小时重新获取一次数据',
        dataSource: '巨量官方接口，可对齐巨量引擎后台',
      },
      {
        name: '直播时长',
        definition: '时分秒呈现，直播时长 = 关播时间-开播时间',
        updateTime:
          '每天早上10点开始更新昨日（T-1）数据，官方数据未更新时，每隔1小时重新获取一次数据',
        dataSource: '巨量官方接口，可对齐巨量引擎后台',
      },
      {
        name: '空挂播',
        definition:
          '次日更新判断结果，空播率≥20%以上判定为疑似空播挂播，其中空播率≥50%为严重空播挂播，20%≤空播率＜50%为轻微空播挂播，空播率＜20%为正常。',
        updateTime: '次日凌晨00:00开始生成结果',
        dataSource: '乾坤圈自定义计算逻辑',
      },
      {
        name: '检测时长',
        definition: '用于检测空播挂播的片段时长，由于监控开播存在10分钟延迟，检测时长≠直播时长',
        updateTime: '次日凌晨00:00开始生成结果',
        dataSource: '乾坤圈自定义计算逻辑',
      },
      {
        name: '空播时长',
        definition: '画面无变化且讲解内容和直播无关的时长',
        updateTime: '次日凌晨00:00开始生成结果',
        dataSource: '乾坤圈自定义计算逻辑',
      },
      {
        name: '空播率',
        definition:
          '空播时长/实际直播时长，由于实际直播时长需要次日早上10点开始更新，所以空播率也需要次日更新',
        updateTime:
          '每天早上10点开始更新昨日（T-1）数据，官方数据未更新时，每隔1小时重新获取一次数据',
        dataSource: '乾坤圈自定义计算逻辑',
      },
      {
        name: '直播时长是否≥30min',
        definition: '是和否，关播时间-开播时间进行计算，然后判断是否≥30min',
        updateTime:
          '每天早上10点开始更新昨日（T-1）数据，官方数据未更新时，每隔1小时重新获取一次数据',
        dataSource: '巨量官方接口，可对齐巨量引擎后台',
      },
      {
        name: '直播时长是否≥60min',
        definition: '是和否，关播时间-开播时间进行计算，然后判断是否≥60min',
        updateTime:
          '每天早上10点开始更新昨日（T-1）数据，官方数据未更新时，每隔1小时重新获取一次数据',
        dataSource: '巨量官方接口，可对齐巨量引擎后台',
      },
      {
        name: '是否挂载转化组件',
        definition: '是和否，直播间是否挂在小风车或者小雪花',
        updateTime:
          '更新时间不固定，一般正常10点左右可正常看昨日数据。每天早上9点～12点，每隔1小时同步一次昨日（T-1）数据。',
        dataSource: '对齐车云店企业号后台-BI工具-图表-直播大屏数据集',
      },
      {
        name: '投放消耗',
        definition: '所选周期内，开播直播间产生的广告消耗费用',
        updateTime:
          '更新时间不固定，一般正常10点左右可正常看昨日数据。每天早上9点～12点，每隔1小时同步一次昨日（T-1）数据。',
        dataSource: '对齐车云店企业号后台-BI工具-图表-直播大屏数据集',
      },
      {
        name: '投流CPL',
        definition: '消耗 / 全场景线索人数，全场景线索人数指期间飞鱼全场景线索人数',
        updateTime:
          '更新时间不固定，一般正常10点左右可正常看昨日数据。每天早上9点～12点，每隔1小时同步一次昨日（T-1）数据。',
        dataSource: '对齐车云店企业号后台-BI工具-图表-直播大屏数据集',
      },
      {
        name: '曝光人数',
        definition: '直播间曝光人数',
        updateTime:
          '更新时间不固定，一般正常10点左右可正常看昨日数据。每天早上9点～12点，每隔1小时同步一次昨日（T-1）数据。',
        dataSource: '对齐车云店企业号后台-BI工具-图表-直播大屏数据集',
      },
      {
        name: '曝光次数',
        definition: '直播间曝光次数',
        updateTime:
          '更新时间不固定，一般正常10点左右可正常看昨日数据。每天早上9点～12点，每隔1小时同步一次昨日（T-1）数据。',
        dataSource: '对齐车云店企业号后台-BI工具-图表-直播大屏数据集',
      },
      {
        name: '观看次数',
        definition: '直播间观看次数',
        updateTime:
          '每天早上10点开始更新昨日（T-1）数据，官方数据未更新时，获取抖音app主页直播间获取的数据（由于存在延迟，数据和车云店后台有略微出入）',
        dataSource:
          '一般早上10点之前，获取抖音app主页直播间获取的数据（由于存在延迟，数据和车云店后台有略微出入）；一般早上10点之后，获取的口径可对齐车云店企业号后台-BI工具-图表-直播大屏数据集',
      },
      {
        name: '观看人数',
        definition: '直播间观看人数',
        updateTime:
          '每天早上10点开始更新昨日（T-1）数据，官方数据未更新时，每隔1小时重新获取一次数据',
        dataSource: '巨量官方接口，可对齐巨量引擎后台',
      },
      {
        name: '曝光进入率',
        definition: '曝光进入率',
        updateTime:
          '更新时间不固定，一般正常10点左右可正常看昨日数据。每天早上9点～12点，每隔1小时同步一次昨日（T-1）数据。',
        dataSource: '对齐车云店企业号后台-BI工具-图表-直播大屏数据集',
      },
      {
        name: 'PCU',
        definition: '直播间峰值在线人数',
        updateTime:
          '更新时间不固定，一般正常10点左右可正常看昨日数据。每天早上9点～12点，每隔1小时同步一次昨日（T-1）数据。',
        dataSource: '对齐车云店企业号后台-BI工具-图表-直播大屏数据集',
      },
      {
        name: 'ACU',
        definition: '直播间平均在线人数',
        updateTime:
          '更新时间不固定，一般正常10点左右可正常看昨日数据。每天早上9点～12点，每隔1小时同步一次昨日（T-1）数据。',
        dataSource: '对齐车云店企业号后台-BI工具-图表-直播大屏数据集',
      },
      {
        name: '广告流量观看次数',
        definition: '直播间中流量类型为Dou+、竞价广告、品牌广告的观看次数',
        updateTime:
          '每天早上10点开始更新昨日（T-1）数据，官方数据未更新时，每隔1小时重新获取一次数据',
        dataSource: '巨量官方接口，可对齐巨量引擎后台',
      },
      {
        name: '自然流量观看次数',
        definition: '直播间中流量类型为自然流量的观看次数',
        updateTime:
          '每天早上10点开始更新昨日（T-1）数据，官方数据未更新时，每隔1小时重新获取一次数据',
        dataSource: '巨量官方接口，可对齐巨量引擎后台',
      },
      {
        name: '广告流量观看人数',
        definition: '直播间中流量类型为Dou+、竞价广告、品牌广告的观看人数',
        updateTime:
          '每天早上10点开始更新昨日（T-1）数据，官方数据未更新时，每隔1小时重新获取一次数据',
        dataSource: '巨量官方接口，可对齐巨量引擎后台',
      },
      {
        name: '自然流量观看人数',
        definition: '直播间中流量类型为自然流量的观看人数',
        updateTime:
          '每天早上10点开始更新昨日（T-1）数据，官方数据未更新时，每隔1小时重新获取一次数据',
        dataSource: '巨量官方接口，可对齐巨量引擎后台',
      },
      {
        name: '广告流量观看时长（分）',
        definition: '直播间中流量类型为Dou+、竞价广告、品牌广告的观看时长（分）',
        updateTime:
          '每天早上10点开始更新昨日（T-1）数据，官方数据未更新时，每隔1小时重新获取一次数据',
        dataSource: '巨量官方接口，可对齐巨量引擎后台',
      },
      {
        name: '自然流量观看时长（分）',
        definition: '直播间中流量类型为自然流量的观看时长（分）',
        updateTime:
          '每天早上10点开始更新昨日（T-1）数据，官方数据未更新时，每隔1小时重新获取一次数据',
        dataSource: '巨量官方接口，可对齐巨量引擎后台',
      },
      {
        name: '加粉丝团人数',
        definition: '直播间加入粉丝团人数',
        updateTime:
          '每天早上10点开始更新昨日（T-1）数据，官方数据未更新时，每隔1小时重新获取一次数据',
        dataSource: '巨量官方接口，可对齐巨量引擎后台',
      },
      {
        name: '互动人数',
        definition: '直播间互动人数，由于存在去重，不等于分享人数+点赞人数+评论人数',
        updateTime:
          '更新时间不固定，一般正常10点左右可正常看昨日数据。每天早上9点～12点，每隔1小时同步一次昨日（T-1）数据。',
        dataSource: '对齐车云店企业号后台-BI工具-图表-直播大屏数据集',
      },
      {
        name: '互动次数',
        definition: '分享次数+点赞次数+评论次数',
        updateTime:
          '每天早上10点开始更新昨日（T-1）数据，官方数据未更新时，每隔1小时重新获取一次数据',
        dataSource: '巨量官方接口，可对齐巨量引擎后台',
      },
      {
        name: '互动率',
        definition: '统计周期内,互动人数/观看人数之比，四舍五入',
        updateTime:
          '更新时间不固定，一般正常10点左右可正常看昨日数据。每天早上9点～12点，每隔1小时同步一次昨日（T-1）数据。',
        dataSource:
          '对齐车云店企业号后台-BI工具-图表-直播大屏数据；巨量官方接口，可对齐巨量引擎后台',
      },
      {
        name: '分享人数',
        definition: '直播间分享人数',
        updateTime:
          '更新时间不固定，一般正常10点左右可正常看昨日数据。每天早上9点～12点，每隔1小时同步一次昨日（T-1）数据。',
        dataSource: '对齐车云店企业号后台-BI工具-图表-直播大屏数据集',
      },
      {
        name: '分享次数',
        definition: '直播间分享次数',
        updateTime:
          '每天早上10点开始更新昨日（T-1）数据，官方数据未更新时，每隔1小时重新获取一次数据',
        dataSource: '巨量官方接口，可对齐巨量引擎后台',
      },
      {
        name: '点赞人数',
        definition: '直播间点赞人数',
        updateTime:
          '更新时间不固定，一般正常10点左右可正常看昨日数据。每天早上9点～12点，每隔1小时同步一次昨日（T-1）数据。',
        dataSource: '对齐车云店企业号后台-BI工具-图表-直播大屏数据集',
      },
      {
        name: '打赏金额',
        definition: '直播间礼物总金额',
        updateTime:
          '每天早上10点开始更新昨日（T-1）数据，官方数据未更新时，每隔1小时重新获取一次数据',
        dataSource: '巨量官方接口，可对齐巨量引擎后台',
      },
      {
        name: '评论人数',
        definition: '直播间评论人数',
        updateTime:
          '更新时间不固定，一般正常10点左右可正常看昨日数据。每天早上9点～12点，每隔1小时同步一次昨日（T-1）数据。',
        dataSource: '对齐车云店企业号后台-BI工具-图表-直播大屏数据集',
      },
      {
        name: '评论次数',
        definition: '直播间弹幕条数',
        updateTime:
          '每天早上10点开始更新昨日（T-1）数据，官方数据未更新时，每隔1小时重新获取一次数据',
        dataSource: '巨量官方接口，可对齐巨量引擎后台',
      },
      {
        name: '广告流量关注数',
        definition: '直播间中流量类型为Dou+、竞价广告、品牌广告的新增粉丝数',
        updateTime:
          '每天早上10点开始更新昨日（T-1）数据，官方数据未更新时，每隔1小时重新获取一次数据',
        dataSource: '巨量官方接口，可对齐巨量引擎后台',
      },
      {
        name: '自然流量关注数',
        definition: '直播间中流量类型为自然流量的的新增粉丝数',
        updateTime:
          '每天早上10点开始更新昨日（T-1）数据，官方数据未更新时，每隔1小时重新获取一次数据',
        dataSource: '巨量官方接口，可对齐巨量引擎后台',
      },
      {
        name: '小风车点击次数',
        definition: '组件点击数，即观众在直播间内点击小风车组件+卡片组件的总次数，不包含小雪花',
        updateTime:
          '每天早上10点开始更新昨日（T-1）数据，官方数据未更新时，每隔1小时重新获取一次数据',
        dataSource: '巨量官方接口，可对齐巨量引擎后台',
      },
      {
        name: '小风车曝光次数',
        definition:
          '组件曝光数，即小风车组件在直播间内的展现次数+卡片组件在直播间内的展现次数+卡片组件在信息流中的展现次数',
        updateTime:
          '每天早上10点开始更新昨日（T-1）数据，官方数据未更新时，每隔1小时重新获取一次数据',
        dataSource: '巨量官方接口，可对齐巨量引擎后台',
      },
      {
        name: '直播间进入私信数',
        definition: '从直播间跳转进入私信的用户人数',
        updateTime:
          '每天早上10点开始更新昨日（T-1）数据，官方数据未更新时，每隔1小时重新获取一次数据',
        dataSource: '巨量官方接口，可对齐巨量引擎后台',
      },
      {
        name: '全场景线索人数',
        definition: '直播期间飞鱼全场景线索人数',
        updateTime:
          '更新时间不固定，一般正常10点左右可正常看昨日数据。每天早上9点～12点，每隔1小时同步一次昨日（T-1）数据。',
        dataSource: '对齐车云店企业号后台-BI工具-图表-直播大屏数据集',
      },
      {
        name: '留资率',
        definition: '统计周期内,全场景线索人数/观看人数',
        updateTime:
          '更新时间不固定，一般正常10点左右可正常看昨日数据。每天早上9点～12点，每隔1小时同步一次昨日（T-1）数据。',
        dataSource:
          '对齐车云店企业号后台-BI工具-图表-直播大屏数据；巨量官方接口，可对齐巨量引擎后台',
      },
      {
        name: '人均停留时长',
        definition: '平均观看时长，观看时长/观看人数',
        updateTime:
          '每天早上10点开始更新昨日（T-1）数据，官方数据未更新时，每隔1小时重新获取一次数据',
        dataSource: '巨量官方接口，可对齐巨量引擎后台',
      },
      {
        name: '点赞次数',
        definition: '直播间点赞次数',
        updateTime:
          '每天早上10点开始更新昨日（T-1）数据，官方数据未更新时，每隔1小时重新获取一次数据',
        dataSource: '巨量官方接口，可对齐巨量引擎后台',
      },
      {
        name: '关注人数',
        definition: '直播间的新增粉丝数，包含自然和投流',
        updateTime:
          '每天早上10点开始更新昨日（T-1）数据，官方数据未更新时，每隔1小时重新获取一次数据',
        dataSource: '巨量官方接口，可对齐巨量引擎后台',
      },
    ],
  },
  {
    name: '线索对接',
    routeKeys: ['clue/clue-docking'],
    items: [
      {
        name: '姓名',
        definition: '留资用户的姓名',
        updateTime: '实时同步，延迟1分钟左右',
        dataSource: '对齐企业号车云店用户管理-线索管理-线索公海明细',
      },
      {
        name: '来源经销商昵称',
        definition: '留资用户的来源门店',
        updateTime: '实时同步，延迟1分钟左右',
        dataSource: '对齐企业号车云店用户管理-线索管理-线索公海明细',
      },
      {
        name: '留资平台',
        definition: '留资用户的来源平台',
        updateTime: '实时同步，延迟1分钟左右',
        dataSource: '对齐企业号车云店用户管理-线索管理-线索公海明细',
      },
    ],
  },
];
