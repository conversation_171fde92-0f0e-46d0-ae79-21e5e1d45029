import { DataDictionaryCategory, dataDictionaryData } from './data';

/**
 * 根据路由路径提取关键路由段
 */
const extractRouteKey = (pathname: string): string => {
  // 移除项目前缀，提取核心路由段
  const projectRoutePattern = /^\/project\/[^/]+\/(.+)$/;
  const match = pathname.match(projectRoutePattern);

  if (match) {
    const routePath = match[1];
    // 返回完整的路由路径，而不是只返回最后一段
    return routePath;
  }

  return '';
};

/**
 * 根据当前路由对数据字典进行智能排序
 */
export const sortDataDictionaryByRoute = (
  pathname: string,
  data: DataDictionaryCategory[] = dataDictionaryData,
): DataDictionaryCategory[] => {
  const currentRouteKey = extractRouteKey(pathname);

  if (!currentRouteKey) {
    return [...data];
  }

  // 找到匹配当前路由的模块
  const matchedModuleIndex = data.findIndex((category) =>
    category.routeKeys?.includes(currentRouteKey),
  );

  if (matchedModuleIndex === -1) {
    return [...data];
  }

  // 将匹配的模块移到最前面
  const sortedData = [...data];
  const [matchedModule] = sortedData.splice(matchedModuleIndex, 1);
  sortedData.unshift(matchedModule);

  return sortedData;
};
