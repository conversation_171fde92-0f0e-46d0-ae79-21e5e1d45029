import { cn } from '@/lib/utils';
import { SearchOutlined, UpOutlined, DownOutlined } from '@ant-design/icons';
import { Drawer, Input, Button } from 'antd';
import { useMemo, useState, useEffect, useRef, useCallback } from 'react';
import { useLocation } from '@umijs/max';
import { sortDataDictionaryByRoute } from './utils';
import Mark from 'mark.js';
import './index.css';

type DataDictionaryDrawerProps = {
  openDrawer: boolean;
  setOpenDrawer: (value: boolean) => void;
};

export default function DataDictionaryDrawer(props: DataDictionaryDrawerProps) {
  const { openDrawer, setOpenDrawer } = props;
  const [searchValue, setSearchValue] = useState('');
  const [currentMatchIndex, setCurrentMatchIndex] = useState(0);
  const [totalMatches, setTotalMatches] = useState(0);
  const location = useLocation();
  const contentRef = useRef<HTMLDivElement>(null);
  const markInstanceRef = useRef<Mark | null>(null);

  const handleClose = () => {
    setOpenDrawer(false);
    setSearchValue('');
    setCurrentMatchIndex(0);
    setTotalMatches(0);
    if (markInstanceRef.current) {
      markInstanceRef.current.unmark();
    }
  };

  useEffect(() => {
    if (contentRef.current && openDrawer) {
      markInstanceRef.current = new Mark(contentRef.current);
    }
    return () => {
      if (markInstanceRef.current) {
        markInstanceRef.current.unmark();
      }
    };
  }, [openDrawer]);

  // 搜索和高亮功能
  const performSearch = useCallback((keyword: string) => {
    if (!markInstanceRef.current || !keyword.trim()) {
      markInstanceRef.current?.unmark();
      setTotalMatches(0);
      setCurrentMatchIndex(0);
      return;
    }

    // 清除之前的高亮
    markInstanceRef.current.unmark();

    // 执行新的搜索和高亮
    markInstanceRef.current.mark(keyword, {
      className: 'search-highlight',
      each: (element) => {
        // 为每个匹配项添加数据属性，用于跳转
        const index = Array.from(
          contentRef.current?.querySelectorAll('.search-highlight') || [],
        ).indexOf(element);
        element.setAttribute('data-match-index', index.toString());
      },
      done: (totalMatches) => {
        setTotalMatches(totalMatches);
        setCurrentMatchIndex(totalMatches > 0 ? 1 : 0);

        // 如果有匹配项，滚动到第一个
        if (totalMatches > 0) {
          scrollToMatch(0);
        }
      },
    });
  }, []);

  const scrollToMatch = (index: number) => {
    const matches = contentRef.current?.querySelectorAll('.search-highlight');
    if (matches && matches[index]) {
      // 移除之前的当前高亮
      contentRef.current?.querySelectorAll('.search-highlight.current').forEach((el) => {
        el.classList.remove('current');
      });

      // 添加当前高亮
      matches[index].classList.add('current');

      // 滚动到匹配项
      matches[index].scrollIntoView({
        behavior: 'smooth',
        block: 'center',
      });
    }
  };

  // 跳转到下一个匹配项
  const goToNextMatch = () => {
    if (totalMatches === 0) return;

    const nextIndex = currentMatchIndex >= totalMatches ? 1 : currentMatchIndex + 1;
    setCurrentMatchIndex(nextIndex);
    scrollToMatch(nextIndex - 1);
  };

  // 跳转到上一个匹配项
  const goToPrevMatch = () => {
    if (totalMatches === 0) return;

    const prevIndex = currentMatchIndex <= 1 ? totalMatches : currentMatchIndex - 1;
    setCurrentMatchIndex(prevIndex);
    scrollToMatch(prevIndex - 1);
  };

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      goToNextMatch();
    }
  };

  // 搜索输入变化时执行搜索
  useEffect(() => {
    const timer = setTimeout(() => {
      performSearch(searchValue);
    }, 300); // 防抖

    return () => clearTimeout(timer);
  }, [searchValue, performSearch]);

  // 根据当前路由排序数据字典
  const sortedData = useMemo(() => {
    return sortDataDictionaryByRoute(location.pathname);
  }, [location.pathname]);

  // 过滤数据（当有搜索词时）
  const filteredData = useMemo(() => {
    if (!searchValue.trim()) {
      return sortedData;
    }

    const keyword = searchValue.toLowerCase();
    return sortedData
      .map((category) => ({
        ...category,
        items: category.items.filter(
          (item) =>
            item.name.toLowerCase().includes(keyword) ||
            item.definition.toLowerCase().includes(keyword) ||
            item.dataSource.toLowerCase().includes(keyword),
        ),
      }))
      .filter((category) => category.items.length > 0);
  }, [sortedData, searchValue]);

  return (
    <Drawer
      title={
        <div className="flex items-center">
          <div className="flex-1">数据字典</div>
          <Button
            type="link"
            href="https://hj81r4bz4v.feishu.cn/wiki/Lntuws7nmixlY2kFJQScHf3nnhc"
            target="_blank"
          >
            乾坤圈数据集说明
          </Button>
        </div>
      }
      width={780}
      onClose={handleClose}
      open={openDrawer}
      closable
      className="data-dictionary-drawer"
      styles={{
        body: {
          padding: 0,
        },
      }}
    >
      <div className="flex h-full flex-col">
        {/* 固定的搜索区域 */}
        <div className="flex-shrink-0 px-6 py-4">
          <div className="flex items-center gap-3">
            <Input
              placeholder="请输入关键词（按 Enter 键切换匹配项）"
              prefix={<SearchOutlined className="text-gray-400" />}
              suffix={
                searchValue && (
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-500">
                      {totalMatches > 0 ? `${currentMatchIndex}/${totalMatches}` : '0/0'}
                    </span>
                    <div className="flex">
                      <Button
                        type="text"
                        size="small"
                        icon={<UpOutlined />}
                        onClick={goToPrevMatch}
                        disabled={totalMatches === 0}
                        className="px-1"
                      />
                      <Button
                        type="text"
                        size="small"
                        icon={<DownOutlined />}
                        onClick={goToNextMatch}
                        disabled={totalMatches === 0}
                        className="px-1"
                      />
                    </div>
                  </div>
                )
              }
              value={searchValue}
              onChange={(e) => setSearchValue(e.target.value)}
              onKeyDown={handleKeyDown}
              className="w-96"
              size="middle"
            />
          </div>
        </div>

        {/* 可滚动的内容区域 */}
        <div ref={contentRef} className="flex-1 overflow-y-auto px-6 pb-6">
          {filteredData.length === 0 && searchValue ? (
            <div className="no-search-results">
              <p>未找到匹配的结果</p>
              <p className="text-sm">请尝试其他关键词</p>
            </div>
          ) : (
            filteredData.map((category) => (
              <div key={category.name} className="mb-8">
                <h3 className="mb-4 text-base font-medium text-gray-900">{category.name}</h3>

                <div className="space-y-4">
                  {category.items.map((item) => (
                    <div
                      key={item.name}
                      className={cn(
                        'rounded-md bg-blue-50/30 p-3',
                        'border border-transparent transition-colors hover:border-blue-100',
                      )}
                    >
                      <div className="space-y-3">
                        <h4 className="text-sm font-medium text-gray-900">{item.name}</h4>

                        <div className="space-y-2 text-sm text-gray-600">
                          <p>
                            <span className="font-medium">字段定义：</span>
                            {item.definition}
                          </p>

                          <p>
                            <span className="font-medium">字段更新时间：</span>
                            {item.updateTime}
                          </p>

                          <p>
                            <span className="font-medium">数据来源：</span>
                            {item.dataSource}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </Drawer>
  );
}
