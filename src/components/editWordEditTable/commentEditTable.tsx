import { MonitorTable } from '@/pages/PortalProjectEdit/QualityRule';
import {
  QualityMonitorWordDelete,
  QualityMonitorWordSave,
  QualityMonitorWordUpdate,
  SceneTypeEnum,
} from '@/services/quality';
import { checkChineseComma } from '@/utils/common';
import { ActionType, EditableProTable, ProColumns } from '@ant-design/pro-components';
import { Form, message, Popconfirm, Space } from 'antd';
import { useRef, useState } from 'react';
import { toolBarBtnRender } from '../editWordModal';

const commentColumns = (refresh: () => void): ProColumns<MonitorTable>[] => [
  {
    dataIndex: 'id',
    hidden: true,
  },
  {
    title: '规范类型',
    dataIndex: 'standardType',
    align: 'center',
    width: 120,
    valueType: 'select',
    valueEnum: new Map([
      [1, '品牌'],
      [2, '平台'],
    ]),
  },
  {
    title: '分类',
    dataIndex: 'name',
    align: 'center',
    width: 150,
    ellipsis: true,
    formItemProps: {
      rules: [
        {
          required: true,
          message: '此项为必填项',
        },
      ],
    },
  },
  {
    title: '添加类型',
    dataIndex: 'type',
    align: 'center',
    width: 100,
    valueEnum: {
      1: '默认',
      2: '自定义',
    },
    editable: false,
  },
  {
    title: '疑似词',
    dataIndex: 'suspectedWord',
    valueType: 'textarea',
    align: 'center',
    tooltip: '疑似词只做高亮标识，需要人工进行二次复检',
    fieldProps: {
      placeholder: '多个监控词用英文逗号隔开',
    },
    formItemProps: {
      rules: [
        {
          validator: checkChineseComma,
        },
      ],
    },
  },
  {
    title: '操作',
    valueType: 'option',
    hideInSearch: true,
    align: 'center',
    fixed: 'right',
    width: 100,
    render: (text, record, _, action) => {
      return (
        <Space>
          {record.type === 2 && (
            <Popconfirm
              title={'是否确认删除'}
              onConfirm={async () => {
                const res = await QualityMonitorWordDelete({ id: record.id });
                res.code === 0 ? message.success('删除成功') : message.error('删除失败');
                refresh();
              }}
              key={'delete'}
            >
              <a>删除</a>
            </Popconfirm>
          )}
          <a
            key="editable"
            onClick={() => {
              action?.startEditable?.(record.id);
            }}
          >
            编辑
          </a>
        </Space>
      );
    },
  },
];

type CommentEditTableProps = {
  refresh: () => void;
  loading: boolean;
  commentDataSource: any;
  projectId?: string;
};

const CommentEditTable = (props: CommentEditTableProps) => {
  const { refresh, loading, commentDataSource, projectId } = props;
  const actionRef = useRef<ActionType>();
  const [form] = Form.useForm();
  const [dataSource, setDataSource] = useState<readonly MonitorTable[]>([]);
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);

  return (
    <EditableProTable<MonitorTable>
      headerTitle="弹幕标识"
      tooltip="弹幕词系统会自动进行识别，但只做高亮提示，不会最终确认为违规，需要人工进行二次校验"
      columns={commentColumns(refresh)}
      rowKey="id"
      ghost
      recordCreatorProps={false}
      size="small"
      controlled
      loading={loading}
      value={dataSource}
      onChange={setDataSource}
      params={commentDataSource}
      request={async () => ({
        data: commentDataSource,
        total: commentDataSource?.length,
        success: true,
      })}
      editable={{
        form,
        editableKeys,
        onChange: setEditableRowKeys,
        actionRender: (row, config, dom) => [dom.save, dom.cancel],
        onSave: async (_, record) => {
          const { id, suspectedWord, name, standardType } = record;
          const isEditing = dataSource.map((item) => item.id).includes(id);
          const submitSaveData = {
            projectId,
            sceneType: SceneTypeEnum.COMMENT,
            suspectedWord,
            name: name || '',
            id: isEditing ? id : undefined,
            standardType,
          };
          if (isEditing) {
            await QualityMonitorWordUpdate(submitSaveData);
          } else {
            await QualityMonitorWordSave(submitSaveData);
          }
          await refresh?.();
          actionRef.current?.reload();
        },
      }}
      toolBarRender={(action) => [toolBarBtnRender(action)]}
      pagination={{
        defaultPageSize: 10,
        hideOnSinglePage: true,
      }}
    />
  );
};

export default CommentEditTable;
