import { MonitorTable } from '@/pages/PortalProjectEdit/QualityRule';
import {
  QualityMonitorWordDelete,
  QualityMonitorWordSave,
  QualityMonitorWordUpdate,
  SceneTypeEnum,
} from '@/services/quality';
import { checkChineseComma } from '@/utils/common';
import { ActionType, EditableProTable, ProColumns } from '@ant-design/pro-components';
import { Form, message, Popconfirm, Space } from 'antd';
import { useRef, useState } from 'react';
import { toolBarBtnRender } from '../editWordModal';

const textColumns = (refresh: () => void): ProColumns<MonitorTable>[] => [
  {
    dataIndex: 'id',
    hidden: true,
  },
  {
    title: '规范类型',
    dataIndex: 'standardType',
    align: 'center',
    width: 120,
    valueType: 'select',
    // 后端返回的是number
    valueEnum: new Map([
      [1, '品牌'],
      [2, '平台'],
    ]),
  },
  {
    title: '分类',
    dataIndex: 'name',
    align: 'center',
    width: 150,
    ellipsis: true,
    formItemProps: {
      rules: [
        {
          required: true,
          message: '此项为必填项',
        },
      ],
    },
  },
  {
    title: '添加类型',
    dataIndex: 'type',
    align: 'center',
    width: 80,
    valueEnum: {
      1: '默认',
      2: '自定义',
    },
    editable: false,
  },
  {
    title: '疑似词',
    dataIndex: 'suspectedWord',
    valueType: 'textarea',
    fieldProps: {
      placeholder: '多个监控词用英文逗号隔开',
    },
    formItemProps: {
      rules: [
        {
          validator: checkChineseComma,
        },
      ],
    },
    align: 'center',
    tooltip: '疑似词只做高亮标识，需要人工进行二次复检',
  },
  {
    title: '敏感词',
    dataIndex: 'sensitiveWord',
    valueType: 'textarea',
    fieldProps: {
      placeholder: '多个监控词用英文逗号隔开',
    },
    formItemProps: {
      rules: [
        {
          validator: checkChineseComma,
        },
      ],
    },
    align: 'center',
    tooltip: '敏感词为最终确认违规类型，直接进行标记确认为违规',
  },
  {
    title: '操作',
    valueType: 'option',
    hideInSearch: true,
    align: 'center',
    fixed: 'right',
    width: 100,
    render: (text, record, _, action) => {
      return (
        <Space>
          {record.type === 2 && (
            <Popconfirm
              title={'是否确认删除，删除后，历史已标记和确认的违规内容也会跟随删除且无法找回。'}
              onConfirm={async () => {
                const res = await QualityMonitorWordDelete({ id: record.id });
                res.code === 0 ? message.success('删除成功') : message.error('删除失败');
                await refresh();
                action?.reload();
              }}
              key={'delete'}
            >
              <a>删除</a>
            </Popconfirm>
          )}
          <a
            key="editable"
            onClick={() => {
              action?.startEditable?.(record.id);
            }}
          >
            编辑
          </a>
        </Space>
      );
    },
  },
];

type TextEditTableProps = {
  refresh: () => void;
  loading: boolean;
  textDataSource: any;
  projectId?: string;
};

const TextEditTable = (props: TextEditTableProps) => {
  const { refresh, loading, textDataSource, projectId } = props;
  const actionRef = useRef<ActionType>();
  const [form] = Form.useForm();
  const [dataSource, setDataSource] = useState<readonly MonitorTable[]>([]);
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);

  return (
    <EditableProTable<MonitorTable>
      headerTitle="文字标识"
      tooltip="文字标识：标题、语音中涉及到的文字会自动识别。疑似词只做高亮标识，需要人工进行二次复检。敏感词为最终确认违规类型，系统直接进行标记确认为违规"
      columns={textColumns(refresh)}
      rowKey="id"
      ghost
      recordCreatorProps={false}
      size="small"
      controlled
      actionRef={actionRef}
      loading={loading}
      value={dataSource}
      onChange={setDataSource}
      params={textDataSource}
      request={async () => ({
        data: textDataSource,
        total: textDataSource?.length,
        success: true,
      })}
      editable={{
        form,
        editableKeys,
        onChange: setEditableRowKeys,
        actionRender: (row, config, dom) => [dom.save, dom.cancel],
        onSave: async (_, record) => {
          const { id, suspectedWord, sensitiveWord, name, standardType } = record;
          if (suspectedWord && sensitiveWord) {
            const illegalWordsArr = suspectedWord.split(',');
            const sensitiveWordsArr = sensitiveWord.split(',');
            const isRepeat = illegalWordsArr.some((item: string) =>
              sensitiveWordsArr.includes(item),
            );
            if (isRepeat) {
              message.error('疑似词与敏感词不能重复');
              return;
            }
          }
          const isEditing = dataSource.map((item) => item.id).includes(id);
          const submitSaveData = {
            projectId,
            sceneType: SceneTypeEnum.TEXT,
            suspectedWord,
            sensitiveWord,
            name: name || '',
            id: isEditing ? id : undefined,
            standardType,
          };
          if (isEditing) {
            await QualityMonitorWordUpdate(submitSaveData);
          } else {
            await QualityMonitorWordSave(submitSaveData);
          }
          await refresh?.();
          actionRef.current?.reload();
        },
      }}
      toolBarRender={(action) => [toolBarBtnRender(action)]}
      pagination={{
        defaultPageSize: 10,
        hideOnSinglePage: true,
      }}
    />
  );
};

export default TextEditTable;
