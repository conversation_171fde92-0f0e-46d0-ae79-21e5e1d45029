import {
  ChatViolationAccountItem,
  ChatViolationTeamItem,
  QualityExViiolationTeamItem,
  QualityExViolationAccountItem,
} from '@/services/daily';
import { TagSpan } from '@/utils/commonStyle';
import { ProColumns } from '@ant-design/pro-components';
import { Flex } from 'antd';

const afkLevelColumns = [
  {
    title: '空播播',
    dataIndex: 'liveAfkLevel',
    width: 130,
    align: 'left',
    valueType: 'select',
    tooltip:
      '空播率≥20%以上判定为疑似空播挂播，其中空播率≥50%为严重空播挂播，20%≤空播率＜50%为轻微空播挂播。次日更新判断结果',
    valueEnum: {
      1: '严重空播挂播',
      2: '轻微空播挂播',
      3: '正常',
    },
    // @ts-ignore
    render: (text, record) => {
      const { liveAfkLevel } = record;
      switch (liveAfkLevel) {
        case 1:
          return (
            <TagSpan $bgColor="#fff2f0" $textColor="#ff4d4f">
              {text}
            </TagSpan>
          );
        case 2:
          return (
            <TagSpan $bgColor="#fbebe2" $textColor="#FF7533">
              {text}
            </TagSpan>
          );
        case 3:
          return (
            <TagSpan $bgColor="#dfe7fd" $textColor="#1E5EFF">
              {text}
            </TagSpan>
          );
      }
    },
  },
];

const afkLiveCountColumns = [
  {
    title: '严重空播挂播场次',
    dataIndex: 'liveSeriousAfkCount',
    align: 'right',
    tooltip: '严重空播挂播场次指空播率≥50%的直播场次,数据次日更新',
    width: 180,
    hideInSearch: true,
    sorter: true,
  },
  {
    title: '轻微空播挂播场次',
    dataIndex: 'liveMinorAfkCount',
    align: 'right',
    tooltip: '轻微空播挂播场次指20%≤空播率＜50%的直播场次,数据次日更新',
    width: 180,
    hideInSearch: true,
    sorter: true,
  },
];

export const accountQualityColumns = (afkState?: boolean) => {
  return [
    {
      dataIndex: 'teamCode',
      hideInTable: true,
    },
    {
      title: '违规账号名称',
      dataIndex: 'nickname',
      align: 'left',
      width: 400,
    },
    {
      title: '门店名称',
      dataIndex: 'teamName',
      align: 'left',
      width: 200,
    },
    {
      title: '违规账号',
      dataIndex: 'showAccountId',
      align: 'right',
      width: 200,
    },
    ...(afkState ? afkLiveCountColumns : []),
    {
      title: '违规总次数',
      dataIndex: 'violationCount',
      align: 'right',
      width: 150,
      sorter: true,
    },
    {
      title: '直播违规次数',
      dataIndex: 'liveViolationCount',
      align: 'right',
      width: 150,
      sorter: true,
    },
    {
      title: '作品违规次数',
      dataIndex: 'postViolationCount',
      align: 'right',
      width: 150,
      sorter: true,
    },
    {
      title: '质检类型',
      dataIndex: 'monitoringNameStr',
      align: 'left',
      width: 300,
      render: (_, record) => {
        const { monitoringNameStr, liveAnchorImageViolationStr } = record;
        const monitorArr = monitoringNameStr ? monitoringNameStr.split(',') : [];
        const liveAnchorImageArr = liveAnchorImageViolationStr
          ? liveAnchorImageViolationStr.split(',')
          : [];
        if (monitorArr.length > 0 || liveAnchorImageArr.length > 0) {
          return (
            <Flex wrap="wrap" gap={5} justify="flex-start">
              {monitorArr.length > 0 &&
                monitorArr.map((item) => (
                  <TagSpan key={item} $bgColor="#fbebe2" $textColor="#FF7533">
                    {item}
                  </TagSpan>
                ))}
              {liveAnchorImageArr.length > 0 &&
                liveAnchorImageArr.map((item) => (
                  <TagSpan key={item} $bgColor="#fbebe2" $textColor="#FF7533">
                    {item}
                  </TagSpan>
                ))}
            </Flex>
          );
        } else {
          return '-';
        }
      },
    },
    ...(afkState ? afkLevelColumns : []),
    {
      title: '触发监控词',
      dataIndex: 'violationMonitoringWord',
      align: 'left',
      width: 500,
      ellipsis: true,
    },
  ] as ProColumns<QualityExViolationAccountItem>[];
};

export const teamQualityColumns = (afkState?: boolean) => {
  return [
    {
      dataIndex: 'teamCode',
      hideInTable: true,
    },
    {
      title: '门店简称',
      dataIndex: 'teamName',
      align: 'left',
      width: 400,
    },
    ...(afkState ? afkLiveCountColumns : []),
    {
      title: '违规总次数',
      dataIndex: 'violationCount',
      align: 'right',
      width: 150,
      sorter: true,
    },
    {
      title: '直播违规次数',
      dataIndex: 'liveViolationCount',
      align: 'right',
      width: 150,
      sorter: true,
    },
    {
      title: '作品违规次数',
      dataIndex: 'postViolationCount',
      align: 'right',
      width: 150,
      sorter: true,
    },
    {
      title: '质检类型',
      dataIndex: 'monitoringNameStr',
      width: 300,
      align: 'left',
      render: (_, record) => {
        const { monitoringNameStr, liveAnchorImageViolationStr } = record;
        const monitorArr = monitoringNameStr ? monitoringNameStr.split(',') : [];
        const liveAnchorImageArr = liveAnchorImageViolationStr
          ? liveAnchorImageViolationStr.split(',')
          : [];
        if (monitorArr.length > 0 || liveAnchorImageArr.length > 0) {
          return (
            <Flex wrap="wrap" gap={5} justify="flex-start">
              {monitorArr.length > 0 &&
                monitorArr.map((item) => (
                  <TagSpan key={item} $bgColor="#fbebe2" $textColor="#FF7533">
                    {item}
                  </TagSpan>
                ))}
              {liveAnchorImageArr.length > 0 &&
                liveAnchorImageArr.map((item) => (
                  <TagSpan key={item} $bgColor="#fbebe2" $textColor="#FF7533">
                    {item}
                  </TagSpan>
                ))}
            </Flex>
          );
        } else {
          return '-';
        }
      },
    },
    ...(afkState ? afkLevelColumns : []),
    {
      title: '触发监控词',
      dataIndex: 'violationMonitoringWord',
      align: 'left',
      width: 500,
      ellipsis: true,
    },
  ] as ProColumns<QualityExViiolationTeamItem>[];
};

export const chatAccountColumns: ProColumns<ChatViolationAccountItem>[] = [
  {
    dataIndex: 'teamCode',
    hideInTable: true,
  },
  {
    title: '账号名称',
    dataIndex: 'nickname',
    align: 'left',
  },
  {
    title: '门店名称',
    dataIndex: 'teamName',
    align: 'left',
    width: 200,
  },
  {
    title: '账号ID',
    dataIndex: 'showAccountId',
    align: 'left',
    width: 150,
  },
  {
    title: '会话人数',
    dataIndex: 'chatRound',
    align: 'right',
    width: 150,
  },
  {
    title: '接收消息数',
    dataIndex: 'receiveMsgCount',
    align: 'right',
    width: 150,
  },
  {
    title: '回复消息数',
    dataIndex: 'replayMsgCount',
    align: 'right',
    width: 150,
  },
  {
    title: '被提醒次数',
    dataIndex: 'notifyCount',
    align: 'right',
    width: 150,
    tooltip: '被提醒次数指3min未回复的消息条数。',
  },
];

export const chatTeamColumns: ProColumns<ChatViolationTeamItem>[] = [
  {
    dataIndex: 'teamCode',
    hideInTable: true,
  },
  {
    title: '门店简称',
    dataIndex: 'teamName',
    align: 'left',
    width: 200,
  },
  {
    title: '会话人数',
    dataIndex: 'chatRound',
    align: 'right',
    width: 150,
  },
  {
    title: '接收消息数',
    dataIndex: 'receiveMsgCount',
    align: 'right',
    width: 150,
  },
  {
    title: '回复消息数',
    dataIndex: 'replayMsgCount',
    align: 'right',
    width: 150,
  },
  {
    title: '被提醒次数',
    dataIndex: 'notifyCount',
    align: 'right',
    width: 150,
    tooltip: '被提醒次数指3min未回复的消息条数。',
  },
];
