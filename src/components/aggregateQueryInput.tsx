import { SearchOutlined } from '@ant-design/icons';
import { Input, InputNumber, Select, Space } from 'antd';
import { isArray } from 'lodash-es';
import { useState } from 'react';

type AggregateQueryInputProps = {
  selectOptions:
    | {
        value: string;
        label: string;
      }[]
    | undefined;
  value?: any;
  onChange?: (value: any) => void;
};

const AggregateQueryInput = (props: AggregateQueryInputProps) => {
  const { selectOptions, value, onChange } = props;
  const [searchParams, setSearchParams] = useState<string | undefined>(selectOptions?.[0]?.value);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange?.([searchParams, e.target.value]);
  };

  const handleInputNumberChange = (value: number | string | null) => {
    onChange?.([searchParams, value]);
  };

  const formatValue = (value?: string[]) => {
    if (isArray(value) && value) {
      return value[1];
    } else {
      return value;
    }
  };

  const inputValue = formatValue(value);

  const inputAddonBefore = (
    <Select
      options={selectOptions}
      value={searchParams}
      onChange={(value) => {
        setSearchParams(value);
        onChange?.([value, undefined]);
      }}
      style={{ width: 100 }}
    />
  );

  if (value?.[0] === 'liveAfkDuration') {
    return (
      <InputNumber
        addonBefore={inputAddonBefore}
        prefix={<SearchOutlined style={{ color: '#DBDBDB' }} />}
        placeholder="空播时长分钟数≥"
        precision={0}
        style={{ width: '100%' }}
        value={inputValue ? Number(inputValue) : undefined}
        onChange={handleInputNumberChange}
      />
    );
  }

  if (value?.[0] === 'liveAfkRate') {
    return (
      <InputNumber
        addonBefore={inputAddonBefore}
        prefix={<SearchOutlined style={{ color: '#DBDBDB' }} />}
        placeholder="空播率≥"
        precision={0}
        formatter={(value) => (value && `${value}%`) as string}
        parser={(value) => value?.replace('%', '') as unknown as string}
        style={{ width: '100%' }}
        value={inputValue}
        onChange={handleInputNumberChange}
      />
    );
  }

  return (
    <Space.Compact style={{ width: '100%' }}>
      {inputAddonBefore}
      <Input
        prefix={<SearchOutlined style={{ color: '#DBDBDB' }} />}
        placeholder="请输入"
        allowClear
        value={inputValue}
        onChange={handleChange}
      />
    </Space.Compact>
  );
};

export default AggregateQueryInput;
