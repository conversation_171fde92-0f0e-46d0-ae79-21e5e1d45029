import React, { useEffect, useRef } from 'react';

import { useState } from 'react';
import styled from 'styled-components';

const EditorWrapper = styled.div`
  font-family: Arial, sans-serif;
  position: relative;
  height: 500px;
  overflow: hidden;
`;

const HiddenTextArea = styled.textarea`
  width: 100%;
  height: 500px;
  font-size: 16px;
  padding: 10px;
  border: 1px solid #1890ff;
  border-radius: 4px;
  background: transparent;
  color: transparent;
  caret-color: black;
  resize: none;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  overflow-y: auto;
`;

const PreviewArea = styled.div`
  width: 100%;
  height: 500px;
  font-size: 16px;
  padding: 10px;
  border: 1px solid #1890ff;
  border-radius: 4px;
  white-space: pre-wrap;
  word-wrap: break-word;
  position: relative;
  z-index: 0;
  overflow-y: auto;
  pointer-events: none;
`;

const HighlightedLine = styled.span`
  display: block;
  font-size: 16px;
  font-weight: bold;
`;

interface TextAreaProps {
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
}

export const RichTextTextArea: React.FC<TextAreaProps> = (props) => {
  const { placeholder = 'Enter some text...', value = '', onChange } = props;
  const [text, setText] = useState<string>('');
  useEffect(() => {
    setText(value);
  }, [value]);
  const textAreaRef = useRef<HTMLTextAreaElement>(null);
  const previewRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const textArea = textAreaRef.current;
    const preview = previewRef.current;

    const syncScroll = () => {
      if (preview) {
        preview.scrollTop = textArea!.scrollTop;
      }
    };

    if (textArea) {
      textArea.addEventListener('scroll', syncScroll);
    }

    return () => {
      if (textArea) {
        textArea.removeEventListener('scroll', syncScroll);
      }
    };
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setText(e.target.value);
    onChange?.(e.target.value);
  };

  const renderPreview = () => {
    return text.split('\n').map((line, index) => {
      if (line.startsWith('#')) {
        return <HighlightedLine key={index}>{line}</HighlightedLine>;
      }
      return (
        <span key={index}>
          {line}
          {index < text.split('\n').length - 1 ? '\n' : ''}
        </span>
      );
    });
  };

  return (
    <EditorWrapper>
      <HiddenTextArea
        ref={textAreaRef}
        value={text}
        onChange={handleChange}
        placeholder={placeholder}
      />
      <PreviewArea ref={previewRef}>{renderPreview()}</PreviewArea>
    </EditorWrapper>
  );
};
