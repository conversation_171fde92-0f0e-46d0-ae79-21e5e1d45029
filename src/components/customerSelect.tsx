import useProjectId from '@/hooks/useProjectId';
import { ManualTeamSelect } from '@/services/douyin-im/manual-team';
import { useRequest } from '@umijs/max';
import { Select } from 'antd';

type CustomerSelectProps = {
  value?: string[];
  onChange?: (value: string[]) => void;
  style?: React.CSSProperties;
};

export default function CustomerSelect(props: CustomerSelectProps) {
  const { style } = props;
  const projectId = useProjectId();

  const { data: customerData } = useRequest(() => ManualTeamSelect({ projectId }));

  return (
    <Select
      mode="multiple"
      allowClear
      style={style}
      value={props?.value}
      placeholder="客服昵称"
      onChange={props?.onChange}
      options={customerData?.map((item) => {
        return {
          label: item?.name,
          value: item.value,
        };
      })}
    />
  );
}
