import AccountImg from '@/assets/account.png';
import TeamImg from '@/assets/team.png';
import { ClassifyType, STICKY_OFFSETHEADER } from '@/utils/common';
import { echartAreaStyleGradient, renderMom } from '@/utils/commonStyle';
import { renderXAxisByDataType } from '@/utils/time';
import { ActionType, CheckCard, ProCard, ProColumns, ProTable } from '@ant-design/pro-components';
import { Flex, message, Radio, RadioChangeEvent } from 'antd';
import ReactECharts from 'echarts-for-react';
import { useEffect, useRef, useState } from 'react';
import { usePollingExport } from '@/hooks/usePollingExport';
import { useRequest } from '@umijs/max';
import { GetTeamDepthSelect } from '@/services/team';
import {
  ExportQualityEXV2PostViolationAccount,
  ExportQualityEXV2PostViolationAggregation,
  GetQualityExV2Count,
  QualityExV2PostViolationAccount,
  QualityExV2PostViolationAggregation,
  QualityExV2Trend,
  QualityPostViolationAccountDailyV2VO,
  QualityPostViolationAggregationDailyV2VO,
} from '@/services/quality-v2';
import { ApiResult } from '@/services/common';
import ExportButton from '@/components/exportButton';
import { customPaginationRender } from './liveQualityReport';
import { dateTypeMap } from '@/utils/dateTypeMap';
import { useAsyncEffect } from 'ahooks';
import { TrendList } from '@/services/quality';
import { selectPlatformAtom } from '@/pages/ProjectHome/atom';
import { useAtomValue } from 'jotai';

type DailyReportProps = {
  rangeTime: string[];
  dataType: 'day' | 'week' | 'month' | null;
  projectId?: string;
};

const teamPostQualityColumns = (
  fieldName?: string,
): ProColumns<QualityPostViolationAggregationDailyV2VO>[] => [
  {
    title: `${fieldName}`,
    dataIndex: 'aggregationName',
    align: 'center',
    fixed: 'left',
    width: 150,
  },
  {
    title: '总作品数',
    dataIndex: 'postCount',
    align: 'center',
    width: 120,
    sorter: (a, b) => Number(a.postCount) - Number(b.postCount),
  },
  {
    title: '总违规作品数',
    dataIndex: 'postViolationCount',
    tooltip: '总违规直播场次包含品牌、平台和空挂播所有违规内容。同一场直播存在多种违规，只算作一场',
    align: 'center',
    width: 140,
    sorter: (a, b) => Number(a.postViolationCount) - Number(b.postViolationCount),
  },
  {
    title: '违规占比',
    dataIndex: 'violationPercent',
    tooltip: '违规占比=对应大区违规作品数/所有大区违规作品数',
    align: 'center',
    width: 120,
    sorter: (a, b) => Number(a.violationPercent) - Number(b.violationPercent),
    render: (_, record) => renderMom(record.violationPercent),
  },
  {
    title: '违规率',
    dataIndex: 'violationRate',
    tooltip: '违规率=大区违规作品数/大区总作品数',
    align: 'center',
    width: 120,
    sorter: (a, b) => Number(a.violationRate) - Number(b.violationRate),
    render: (_, record) => renderMom(record.violationRate),
  },
  {
    title: '品牌违规数',
    dataIndex: 'brandViolationCount',
    tooltip: '官方平台发布的违规监测规范',
    align: 'center',
    width: 120,
    sorter: (a, b) => Number(a.brandViolationCount) - Number(b.brandViolationCount),
  },
  {
    title: '平台违规场次',
    dataIndex: 'platformViolationCount',
    tooltip: '指内部制定的短视频监测规范',
    align: 'center',
    width: 140,
    sorter: (a, b) => Number(a.platformViolationCount) - Number(b.platformViolationCount),
  },
  {
    title: '高频违规类型',
    dataIndex: 'violationTypeNameStr',
    align: 'center',
    width: 200,
    sorter: true,
  },
  {
    title: '高频监控词',
    dataIndex: 'monitoringWordStr',
    align: 'center',
    width: 200,
    sorter: true,
  },
];

const accountPostQualityColumns: ProColumns<QualityPostViolationAccountDailyV2VO>[] = [
  {
    title: '违规账号名称',
    dataIndex: 'nickname',
    align: 'center',
    width: 300,
  },
  {
    title: '门店名称',
    dataIndex: 'teamName',
    align: 'center',
    width: 300,
  },
  {
    title: '违规账号',
    dataIndex: 'showAccountId',
    align: 'center',
    width: 200,
  },
  {
    title: '总作品数',
    dataIndex: 'postCount',
    align: 'center',
    width: 120,
    sorter: (a, b) => Number(a.postCount) - Number(b.postCount),
  },
  {
    title: '总违规作品数',
    dataIndex: 'postViolationCount',
    tooltip: '总违规直播场次包含品牌、平台和空挂播所有违规内容。同一场直播存在多种违规，只算作一场',
    align: 'center',
    width: 140,
    sorter: (a, b) => Number(a.postViolationCount) - Number(b.postViolationCount),
  },
  {
    title: '违规率',
    dataIndex: 'violationRate',
    tooltip: '违规率=大区违规作品数/大区总作品数',
    align: 'center',
    width: 120,
    sorter: (a, b) => Number(a.violationRate) - Number(b.violationRate),
    render: (_, record) => renderMom(record.violationRate),
  },
  {
    title: '品牌违规数',
    dataIndex: 'brandViolationCount',
    tooltip: '官方平台发布的违规监测规范',
    align: 'center',
    width: 120,
    sorter: (a, b) => Number(a.brandViolationCount) - Number(b.brandViolationCount),
  },
  {
    title: '平台违规场次',
    dataIndex: 'platformViolationCount',
    tooltip: '指内部制定的短视频监测规范',
    align: 'center',
    width: 140,
    sorter: (a, b) => Number(a.platformViolationCount) - Number(b.platformViolationCount),
  },
  {
    title: '高频违规类型',
    dataIndex: 'violationTypeNameStr',
    align: 'center',
    width: 200,
    sorter: true,
  },
  {
    title: '高频监控词',
    dataIndex: 'monitoringWordStr',
    align: 'center',
    width: 200,
    sorter: true,
  },
];

export const renderPostQualityColumns = (classifyType: ClassifyType, fieldName?: string) => {
  if (classifyType === ClassifyType.Team) {
    return teamPostQualityColumns(fieldName);
  } else {
    return accountPostQualityColumns;
  }
};

const PostQualityReport = (props: DailyReportProps) => {
  const { rangeTime, dataType: propsDataType, projectId } = props;
  const actionRef = useRef<ActionType>();
  const [startDate, endDate] = rangeTime;
  const [classifyType, setClassifyType] = useState(ClassifyType.Team);
  const { pollingExport, percent, pollingLoading } = usePollingExport();
  const [fieldId, setFieldId] = useState<number>();
  const [trendData, setTrendData] = useState<TrendList[]>([]);
  const platform = useAtomValue(selectPlatformAtom);

  const { data: teamDepthData } = useRequest(() => GetTeamDepthSelect({ projectId }), {
    onSuccess: (data) => {
      const res = data?.[0].value;
      setFieldId(Number(res));
    },
  });

  const fieldName = teamDepthData?.find((item) => item.value === String(fieldId))?.name;

  const handleExportRank = async (classifyType: ClassifyType) => {
    let fetchExportFn;
    if (classifyType === ClassifyType.Account) {
      fetchExportFn = ExportQualityEXV2PostViolationAccount;
    } else {
      fetchExportFn = ExportQualityEXV2PostViolationAggregation;
    }
    const res = await fetchExportFn({
      projectId,
      startDate,
      endDate,
      dateType: propsDataType ? dateTypeMap[propsDataType] : 4,
      fieldId,
      platform,
    });
    if (res.data) {
      pollingExport({ taskId: res.data });
    } else {
      message.error('导出失败');
    }
  };

  useAsyncEffect(async () => {
    if (propsDataType !== null) {
      const res = await QualityExV2Trend({
        projectId,
        type: classifyType,
        dateType: dateTypeMap[propsDataType],
        queryType: 2,
        platform,
      });
      setTrendData(res?.data?.trendList || []);
      actionRef.current?.reload();
    }
  }, [classifyType, propsDataType, platform]);

  const { data: summaryData } = useRequest(
    () =>
      GetQualityExV2Count({
        projectId,
        dateType: propsDataType ? dateTypeMap[propsDataType] : 4,
        startDate,
        endDate,
        queryType: 2,
        platform,
      }),
    {
      ready: !!rangeTime.length,
      refreshDeps: [rangeTime, propsDataType, platform],
    },
  );

  useEffect(() => {
    actionRef.current?.reload();
  }, [classifyType]);

  const options = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985',
          precision: 0, // 省略小数点
        },
      },
      formatter: function (params: any) {
        const mom = params[0].data.mom;
        const value = params[0].data.value;
        return (
          params[0].name +
          `<br>${`违规${classifyType === ClassifyType.Account ? '账号' : '门店'}个数`} ` +
          `<span style="color: #000">${value}</span>` +
          '<br>' +
          `<span style="color:${mom > 0 ? 'red' : '#30B824'};">环比${
            mom > 0 ? '增加' : '减少'
          } ${mom} % </span>`
        );
      },
    },
    grid: {
      top: '3%',
      left: 1,
      right: '2%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: renderXAxisByDataType(propsDataType),
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        data: trendData?.map((item) => {
          return { value: item.count, mom: item.mom };
        }),
        type: 'line',
        stack: 'Total',
        smooth: true,
        areaStyle: echartAreaStyleGradient,
        emphasis: {
          focus: 'series',
        },
      },
    ],
  };

  return (
    <ProCard>
      {summaryData?.violationRate && (
        <div style={{ marginBottom: '10px' }}>
          总违规率{' '}
          <span style={{ color: 'red', fontSize: '20px', fontWeight: 600 }}>
            {summaryData?.violationRate}%
          </span>
        </div>
      )}
      <CheckCard.Group
        onChange={(value) => {
          if (!value) return;
          setClassifyType(value as number);
        }}
        defaultValue={classifyType}
        value={classifyType}
      >
        <CheckCard
          avatar={TeamImg}
          size="small"
          title="违规门店个数"
          value={ClassifyType.Team}
          description={
            <Flex justify="space-between" align="center">
              <span style={{ fontSize: '18px', fontWeight: 600 }}>
                {summaryData?.teamViolationCount}
              </span>
              {renderMom(summaryData?.teamViolationCountMom)}
            </Flex>
          }
        />
        <CheckCard
          avatar={AccountImg}
          size="small"
          title="违规账号个数"
          value={ClassifyType.Account}
          description={
            <Flex justify="space-between" align="center">
              <span style={{ fontSize: '18px', fontWeight: 600 }}>
                {summaryData?.accountViolationCount}
              </span>
              {renderMom(summaryData?.accountViolationCountMom)}
            </Flex>
          }
        />
      </CheckCard.Group>

      {propsDataType !== null && (
        <>
          <div style={{ marginBottom: '20px' }}>违规趋势</div>
          <div style={{ height: '200px' }}>
            <ReactECharts
              option={options}
              notMerge={true}
              style={{ width: '100%', height: '100%' }}
            />
          </div>
        </>
      )}
      <ProTable
        // @ts-ignore
        columns={renderPostQualityColumns(classifyType, fieldName)}
        actionRef={actionRef}
        params={{
          projectId,
          startDate,
          endDate,
          dateType: propsDataType ? dateTypeMap[propsDataType] : 4,
          fieldId,
          platform,
        }}
        ghost
        sticky={{ offsetHeader: STICKY_OFFSETHEADER }}
        tableClassName="custom-table"
        scroll={{ x: 'max-content' }}
        headerTitle={
          <>
            {teamDepthData && (
              <Radio.Group
                value={fieldId?.toString()}
                onChange={(e: RadioChangeEvent) => setFieldId(e.target.value)}
                buttonStyle="solid"
                style={{ whiteSpace: 'nowrap' }}
              >
                {teamDepthData.map((item) => (
                  <Radio.Button key={item.value} value={item.value}>
                    {item.name}
                  </Radio.Button>
                ))}
              </Radio.Group>
            )}
          </>
        }
        request={async (params) => {
          let fetchFn: any;
          if (!rangeTime || !fieldId)
            return {
              total: 0,
              data: [],
              success: false,
            };
          if (classifyType === ClassifyType.Team) {
            fetchFn = QualityExV2PostViolationAggregation;
          } else if (classifyType === ClassifyType.Account) {
            fetchFn = QualityExV2PostViolationAccount;
          }
          delete params.current;
          delete params.pageSize;
          const result: ApiResult<
            QualityPostViolationAggregationDailyV2VO[] | QualityPostViolationAccountDailyV2VO[]
          > = await fetchFn({
            ...params,
          });
          return {
            total: result?.data?.length,
            data: result?.data,
            success: result.code === 0,
          };
        }}
        search={false}
        toolBarRender={() => [
          <ExportButton
            exportFn={() => handleExportRank(classifyType)}
            loading={pollingLoading}
            percent={percent}
            key="export"
          />,
        ]}
        rowKey={classifyType === ClassifyType.Team ? 'aggregationName' : 'nickname'}
        pagination={{
          defaultPageSize: 10,
          showTotal: (total, range) => `${range[0]}-${range[1]} 共 ${total} 条`,
          itemRender: customPaginationRender,
          showSizeChanger: true,
        }}
        dateFormatter="string"
      />
    </ProCard>
  );
};

export default PostQualityReport;
