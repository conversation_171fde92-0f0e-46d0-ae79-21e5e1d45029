import { ClassifyType, STICKY_OFFSETHEADER } from '@/utils/common';
import { ActionType, CheckCard, ProCard, ProTable } from '@ant-design/pro-components';
import { Flex, Input, message } from 'antd';
import ExportButton from '../exportButton';
import { customPaginationRender } from './liveQualityReport';
import AccountImg from '@/assets/account.png';
import TeamImg from '@/assets/team.png';
import { useEffect, useRef, useState } from 'react';
import { usePollingExport } from '@/hooks/usePollingExport';
import { useRequest } from '@umijs/max';
import { echartAreaStyleGradient, renderMom } from '@/utils/commonStyle';
import ReactECharts from 'echarts-for-react';
import {
  ChatTrend,
  ChatViolationAccount,
  ChatViolationTeam,
  ExportChatAccount,
  ExportChatTeam,
  GetChatCount,
} from '@/services/daily';
import { renderXAxisByDataType } from '@/utils/time';
import { renderColumns } from '@/utils/dailyUtils';
import DynamicTree from '../dynamicTree';
import { useDebounce } from 'ahooks';
import { proTableRequestAdapterParamsAndData } from '@/utils';
import { dateTypeMap } from '@/utils/dateTypeMap';
import { useAtomValue } from 'jotai';
import { selectPlatformAtom } from '@/pages/ProjectHome/atom';

type ChatReportProps = {
  rangeTime: string[];
  dataType: 'day' | 'week' | 'month' | null;
  projectId?: string;
};

const ChatReport = (props: ChatReportProps) => {
  const { rangeTime, dataType: propsDataType, projectId } = props;
  const actionRef = useRef<ActionType>();
  const [startDate, endDate] = rangeTime;
  const [classifyType, setClassifyType] = useState(ClassifyType.Team);
  const { pollingExport, percent, pollingLoading } = usePollingExport();
  const [keyword, setKeyword] = useState<string>('');
  const debounceKeyword = useDebounce(keyword);
  const [treeValue, setTreeValue] = useState<string[] | undefined>(undefined);
  const platform = useAtomValue(selectPlatformAtom);

  const handleExportRank = async (classifyType: ClassifyType) => {
    let fetchExportFn;
    if (classifyType === ClassifyType.Account) {
      fetchExportFn = ExportChatAccount;
    } else {
      fetchExportFn = ExportChatTeam;
    }
    const res = await fetchExportFn({ projectId, startDate, endDate });
    if (res.data) {
      pollingExport({ taskId: res.data });
    } else {
      message.error('导出失败');
    }
  };

  const { data: trendData } = useRequest(
    () =>
      ChatTrend({
        projectId,
        type: classifyType,
        dateType: propsDataType ? dateTypeMap[propsDataType] : 4,
        platform,
      }),
    {
      ready: !!propsDataType,
      refreshDeps: [classifyType, propsDataType, platform],
      onSuccess: () => actionRef.current?.reload(),
    },
  );

  const { data: summaryData } = useRequest(
    () =>
      GetChatCount({
        projectId,
        dateType: propsDataType ? dateTypeMap[propsDataType] : 4,
        startDate,
        endDate,
        platform,
      }),
    {
      ready: !!rangeTime.length,
      refreshDeps: [rangeTime, propsDataType, platform],
    },
  );

  useEffect(() => {
    actionRef.current?.reload();
    setTreeValue(undefined);
    setKeyword('');
  }, [classifyType]);

  const options = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985',
          precision: 0, // 省略小数点
        },
      },
      formatter: function (params: any) {
        const mom = params[0].data.mom;
        const value = params[0].data.value;
        return (
          params[0].name +
          `<br>${`违规${classifyType === ClassifyType.Account ? '账号' : '门店'}个数`} ` +
          `<span style="color: #000">${value}</span>` +
          '<br>' +
          `<span style="color:${mom > 0 ? 'red' : '#30B824'};">环比${
            mom > 0 ? '增加' : '减少'
          } ${mom} % </span>`
        );
      },
    },
    grid: {
      top: '3%',
      left: 1,
      right: '2%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: renderXAxisByDataType(propsDataType),
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        data: trendData?.trendList?.map((item) => {
          return { value: item.count, mom: item.mom };
        }),
        type: 'line',
        stack: 'Total',
        smooth: true,
        areaStyle: echartAreaStyleGradient,
        emphasis: {
          focus: 'series',
        },
      },
    ],
  };

  return (
    <ProCard>
      <CheckCard.Group
        onChange={(value) => {
          if (!value) return;
          setClassifyType(value as number);
        }}
        defaultValue={classifyType}
        value={classifyType}
      >
        <CheckCard
          avatar={TeamImg}
          size="small"
          title="提醒门店个数"
          value={ClassifyType.Team}
          description={
            <Flex justify="space-between" align="center">
              <span style={{ fontSize: '18px', fontWeight: 600 }}>
                {summaryData?.teamViolationCount}
              </span>
              {renderMom(summaryData?.teamViolationCountMom)}
            </Flex>
          }
        />
        <CheckCard
          avatar={AccountImg}
          size="small"
          title="提醒账号个数"
          value={ClassifyType.Account}
          description={
            <Flex justify="space-between" align="center">
              <span style={{ fontSize: '18px', fontWeight: 600 }}>
                {summaryData?.accountViolationCount}
              </span>
              {renderMom(summaryData?.accountViolationCountMom)}
            </Flex>
          }
        />
      </CheckCard.Group>

      {propsDataType !== null && (
        <>
          <div style={{ marginBottom: '20px' }}>违规趋势</div>
          <div style={{ height: '200px' }}>
            <ReactECharts
              option={options}
              notMerge={true}
              style={{ width: '100%', height: '100%' }}
            />
          </div>
        </>
      )}
      <ProTable
        // @ts-ignore
        columns={renderColumns('chat', classifyType)}
        actionRef={actionRef}
        params={{
          projectId,
          startDate,
          endDate,
          teamCodeList: treeValue,
          keyword: debounceKeyword,
          platform,
        }}
        ghost
        sticky={{ offsetHeader: STICKY_OFFSETHEADER }}
        tableClassName="custom-table"
        scroll={{ x: 'max-content' }}
        headerTitle={
          <>
            {classifyType === ClassifyType.Team ? (
              <DynamicTree
                style={{ width: '200px' }}
                value={treeValue}
                setValue={setTreeValue}
                projectId={projectId}
              />
            ) : (
              <Input
                style={{ width: '200px' }}
                value={keyword}
                onChange={(e) => {
                  setKeyword(e.target.value);
                }}
                placeholder="账号名称或ID搜索"
              />
            )}
          </>
        }
        request={async (params, sorter) => {
          let fetchFn: any;
          if (!rangeTime) return Promise.resolve();
          if (classifyType === ClassifyType.Team) {
            fetchFn = ChatViolationTeam;
          } else if (classifyType === ClassifyType.Account) {
            fetchFn = ChatViolationAccount;
          }
          return proTableRequestAdapterParamsAndData(params, sorter, fetchFn);
        }}
        search={false}
        toolBarRender={() => [
          <ExportButton
            exportFn={() => handleExportRank(classifyType)}
            loading={pollingLoading}
            percent={percent}
            key="export"
          />,
        ]}
        rowKey={(record) => record.teamCode + Math.random()}
        pagination={{
          defaultPageSize: 10,
          showTotal: (total, range) => `${range[0]}-${range[1]} 共 ${total} 条`,
          itemRender: customPaginationRender,
          showSizeChanger: true,
        }}
        dateFormatter="string"
      />
    </ProCard>
  );
};

export default ChatReport;
