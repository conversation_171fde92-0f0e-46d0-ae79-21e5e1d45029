import { Row, Flex, Popover } from 'antd';
import { echartAreaStyleGradient, renderMom } from '@/utils/commonStyle';
import { CardTrend } from '@/services/data-card';
import ReactECharts from 'echarts-for-react';
import { secondToHours } from '@/utils/time';
import { useState } from 'react';
import DataCardDetail from './dataCardDetail';
import { formatNum } from '@/utils/common';
import { cn } from '@/lib/utils';

type DataCardProps = {
  title: string;
  data?: CardTrend;
  isTimeType?: boolean;
  isPercentage?: boolean;
  loading?: boolean;
  className?: string;
};

const DataCard = (props: DataCardProps) => {
  const { title, data, isTimeType, isPercentage, loading = false, className } = props;
  const [hovered, setHovered] = useState(false);

  const formatData = data?.trendList?.map((item) => {
    if (isTimeType) {
      return secondToHours(Number(item.count));
    } else if (isPercentage) {
      return Number(item.count).toFixed(2); // 后端已经乘100了
    } else {
      return item.count;
    }
  });

  const options = {
    tooltip: false,
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: data?.trendList?.map((item) => item.date),
      axisLabel: {
        show: false, // 隐藏刻度标签
      },
      axisTick: {
        show: false, // 隐藏刻度线
      },
      axisLine: {
        show: false, // 隐藏 X 轴底部的轴线
      },
      splitLine: {
        show: false, // 隐藏背景网格线
      },
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        show: false, // 隐藏刻度标签
      },
      axisTick: {
        show: false, // 隐藏刻度线
      },
      splitLine: {
        show: false, // 隐藏背景网格线
      },
    },
    series: [
      {
        data: formatData,
        type: 'line',
        stack: 'Total',
        symbol: 'none',
        smooth: true,
        areaStyle: echartAreaStyleGradient,
        emphasis: {
          focus: 'series',
        },
      },
    ],
    graphic: {
      type: 'text', // 类型：文本
      left: 'center',
      top: 'middle',
      silent: true, // 不响应事件
      invisible: data?.trendList && data?.trendList?.length > 0, // 有数据就隐藏
      style: {
        fill: '#9d9d9d',
        fontWeight: 'bold',
        text: '暂无数据',
        fontFamily: 'Microsoft YaHei',
        fontSize: '20px',
      },
    },
  };

  const handleHoverChange = (open: boolean) => {
    setHovered(open);
  };

  const renderTitleNum = () => {
    if (!data?.count) {
      return '--';
    }
    if (isTimeType) {
      return secondToHours(Number(data?.count));
    } else if (isPercentage) {
      return Number(data?.count).toFixed(2) + '%'; // 后端已经乘100了
    } else if (data?.count) {
      return formatNum(data?.count);
    }
  };

  return (
    <Flex align="middle" wrap={false} className={cn('rounded-lg bg-white px-5 py-3', className)}>
      {loading ? (
        <div role="status" className="flex w-full animate-pulse items-center justify-between">
          <div className="h-full w-[110px] space-y-4">
            <div className="h-2.5 w-full rounded-full bg-gray-200 dark:bg-gray-700" />
            <div className="h-2.5 w-full rounded-full bg-gray-200 dark:bg-gray-700" />
            <div className="h-2.5 w-full rounded-full bg-gray-200 dark:bg-gray-700" />
          </div>
          <div className="ml-4 h-full flex-1 rounded-lg bg-gray-200 dark:bg-gray-700" />
        </div>
      ) : (
        <>
          <Flex vertical style={{ width: '110px', marginRight: '10px', marginBlock: 'auto' }}>
            <div className="text-nowrap text-[16px] text-[#000000c7]">{title}</div>
            <div className="text-[32px] font-medium text-[#000000f2]">{renderTitleNum()}</div>
            <Row align="middle">{renderMom(data?.momRate)}</Row>
          </Flex>

          {data?.trendList && data?.trendList?.length > 1 ? (
            <Popover
              content={
                <DataCardDetail
                  data={data}
                  isPercentage={isPercentage}
                  isTimeType={isTimeType}
                  key="hover-data-card"
                />
              }
              trigger="hover"
              open={hovered}
              onOpenChange={handleHoverChange}
              arrow={false}
              placement="bottom"
            >
              <div style={{ flex: '1', overflow: 'hidden', minHeight: '79px' }}>
                <ReactECharts
                  option={options}
                  notMerge={true}
                  style={{ width: '100%', height: '100%' }}
                />
              </div>
            </Popover>
          ) : (
            <div style={{ flex: '1', overflow: 'hidden', minHeight: '79px' }}>
              <ReactECharts
                option={options}
                notMerge={true}
                style={{ width: '100%', height: '100%' }}
              />
            </div>
          )}
        </>
      )}
    </Flex>
  );
};

export default DataCard;
