import './index.module.less';

/** 作品分析-视频素材概览 */
export const TagCompareDataSkeleton = () => {
  return (
    <div className="data-item mb-2 justify-between">
      <div className="flex h-[20px] w-full">
        <div className="skeleton-loading-anime flex-1 rounded" />
        <div className="flex-1" />
        <div className="skeleton-loading-anime flex-1 rounded" />
      </div>
      <div className="flex h-[20px] w-full">
        <div className="skeleton-loading-anime flex-1 rounded" />
        <div className="flex-1" />
        <div className="skeleton-loading-anime flex-1 rounded" />
      </div>
    </div>
  );
};

/** 作品分析-视频类型 */
export const VideoTypesSkeleton = () => {
  return (
    <div className="video-category-item">
      <div className="skeleton-loading-anime h-5 w-1/3 rounded" />
      <div className="skeleton-loading-anime h-5 w-1/5 rounded" />
      <div className="flex h-5 w-full justify-between rounded">
        <div className="h/full skeleton-loading-anime w-1/4 rounded" />
        <div className="h/full skeleton-loading-anime w-1/4 rounded" />
      </div>
    </div>
  );
};

/** 作品分析-内容公式 */
export const FormulaSkeleton = () => {
  return (
    <div className="formula-wrapper mb-[48px] flex h-[146px] flex-col justify-between">
      <div>内容公式1</div>

      <div className="flex">
        <div className="skeleton-loading-anime relative mr-2 !w-[83px]" />+
        <div className="skeleton-loading-anime relative mx-2 !w-[83px]" />+
        <div className="skeleton-loading-anime relative mx-2 !w-[83px]" />
        <div className="ml-[24px] flex h-[24px] w-auto">
          镜头数: <div className="skeleton-loading-anime ml-1 !w-[30px]" />
        </div>
      </div>

      <div className="flex items-center">
        <div className="flex items-center">
          <span className="formula-part-title">开头</span>

          <div className="lens-item skeleton-loading-anime" />
          <div className="lens-item skeleton-loading-anime" />
        </div>

        <div className="ml-[40px] flex items-center">
          <span className="formula-part-title">内容</span>

          <div className="lens-item skeleton-loading-anime" />
          <div className="lens-item skeleton-loading-anime" />
          <div className="lens-item skeleton-loading-anime" />
        </div>
      </div>
    </div>
  );
};
