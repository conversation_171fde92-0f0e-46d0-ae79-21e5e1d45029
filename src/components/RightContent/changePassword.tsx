import { EditUserPassword } from '@/services/auth';
import { ModalForm, ProFormText } from '@ant-design/pro-components';
import { message } from 'antd';

type ChangePasswordProps = {
  open: boolean;
  onClose: () => void;
  logOut: () => void;
};

const formItemLayout = {
  labelCol: { span: 8 },
  wrapperCol: { span: 12 },
};

const ChangePassword = (props: ChangePasswordProps) => {
  const { open, onClose, logOut } = props;

  return (
    <ModalForm
      title="修改密码"
      open={open}
      layout="horizontal"
      width={500}
      {...formItemLayout}
      autoFocusFirstInput
      modalProps={{
        destroyOnHidden: true,
        onCancel: onClose,
      }}
      onFinish={async (values) => {
        const res = await EditUserPassword(values);
        if (res.code === 0) {
          message.success('修改成功');
          logOut();
          return true;
        }
      }}
    >
      <ProFormText.Password
        name="oldPassword"
        label="输入旧密码"
        placeholder="请输入旧密码"
        rules={[{ required: true, message: '请输入旧密码' }]}
      />
      <ProFormText.Password
        name="newPassword"
        label="输入新密码"
        placeholder="请输入新密码"
        rules={[
          ({ getFieldValue }) => ({
            validator(rule, value) {
              if (getFieldValue('oldPassword') === value) {
                return Promise.reject('新旧密码相同,请重新输入');
              } else {
                return Promise.resolve();
              }
            },
          }),
        ]}
      />
      <ProFormText.Password
        name="confirmPassword"
        label="重复输入新密码"
        placeholder="请重复输入新密码"
        rules={[
          ({ getFieldValue }) => ({
            validator(rule, value) {
              if (!value || getFieldValue('newPassword') === value) {
                return Promise.resolve();
              }
              return Promise.reject('两次密码输入不一致');
            },
          }),
        ]}
      />
    </ModalForm>
  );
};

export default ChangePassword;
