import defaultAvatar from '@/assets/default-avatar.png';
import { Logout } from '@/services/auth';
import { checkFreePage } from '@/utils';
import { loginPath } from '@/utils/const';
import {
  LogoutOutlined,
  SettingOutlined,
  UserOutlined,
  UserSwitchOutlined,
} from '@ant-design/icons';
import { setAlpha } from '@ant-design/pro-components';
import { useEmotionCss } from '@ant-design/use-emotion-css';
import { history, useModel } from '@umijs/max';
import { Avatar, Spin } from 'antd';
import { stringify } from 'querystring';
import React, { useCallback, useEffect, useState } from 'react';
import { flushSync } from 'react-dom';
import HeaderDropdown from '../HeaderDropdown';
import ChangePassword from './changePassword';

export type GlobalHeaderRightProps = {
  menu?: boolean;
};

const Name = () => {
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};

  const nameClassName = useEmotionCss(({ token }) => {
    return {
      height: '48px',
      overflow: 'hidden',
      lineHeight: '48px',
      whiteSpace: 'nowrap',
      textOverflow: 'ellipsis',
      [`@media only screen and (max-width: ${token.screenMD}px)`]: {
        display: 'none',
      },
      color: '#5A607F',
    };
  });

  return <span className={`${nameClassName} anticon`}>{currentUser?.name}</span>;
};

const AvatarLogo = () => {
  const { initialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};

  const avatarClassName = useEmotionCss(({ token }) => {
    return {
      marginRight: '8px',
      color: token.colorPrimary,
      verticalAlign: 'top',
      background: setAlpha(token.colorBgContainer, 0.85),
      [`@media only screen and (max-width: ${token.screenMD}px)`]: {
        margin: 0,
      },
    };
  });

  return (
    <Avatar
      size="small"
      className={avatarClassName}
      src={currentUser?.avatar || <img src={defaultAvatar} />}
      alt="avatar"
    />
  );
};

export const loginOut = async () => {
  await Logout();
  const { search, pathname } = window.location;
  const urlParams = new URL(window.location.href).searchParams;
  /** 此方法会跳转到 redirect 参数所在的位置 */
  const redirect = urlParams.get('redirect');
  // Note: There may be security issues, please note
  if (window.location.pathname !== '/login' && !redirect) {
    history.replace({
      pathname: '/login',
      search: stringify({
        redirect: pathname + search,
      }),
    });
  }

  // 有情况是先登录了无任何权限的账号存储了一份空的 localStorge projectList 导致再次登录有项目权限的账号会读取这份空的数据 所以退出应该清掉
  localStorage.removeItem('projectList');
};

const AvatarDropdown: React.FC<GlobalHeaderRightProps> = ({ menu }) => {
  const [openPasswordModal, setOpenPasswordModal] = useState(false);
  /**
   * 退出登录，并且将当前的 url 保存
   */

  const actionClassName = useEmotionCss(({ token }) => {
    return {
      display: 'flex',
      height: '48px',
      marginLeft: 'auto',
      overflow: 'hidden',
      alignItems: 'center',
      padding: '0 8px',
      cursor: 'pointer',
      borderRadius: token.borderRadius,
      '&:hover': {
        backgroundColor: token.colorBgTextHover,
      },
    };
  });
  const { initialState, setInitialState } = useModel('@@initialState');
  const { currentUser } = initialState || {};

  const logoutAndClearInfo = () => {
    flushSync(() => {
      setInitialState((s: any) => ({ ...s, currentUser: undefined }));
    });
    if (!checkFreePage()) {
      history.replace(
        loginPath +
          '?redirect=' +
          encodeURIComponent(window.location.pathname + window.location.search),
      );
    }
  };

  const onMenuClick = useCallback(
    ({ key }: { key: string }) => {
      if (key === 'logout') {
        logoutAndClearInfo();
        loginOut();
        return;
      }
      if (key === 'change-password') {
        setOpenPasswordModal(true);
        return;
      }
      history.push(`/${key}`);
    },
    [setInitialState],
  );

  const loading = (
    <span className={actionClassName}>
      <Spin
        size="small"
        style={{
          marginLeft: 8,
          marginRight: 8,
        }}
      />
    </span>
  );
  useEffect(() => {
    if (!currentUser || !currentUser.id) return;
    // @ts-ignore
    if (typeof umami !== 'undefined') {
      // @ts-ignore
      umami.track(currentUser.id.toString(), {
        name: currentUser.name,
        email: currentUser.email,
        avatar: currentUser.avatar,
      });
    }
  }, [currentUser]);
  if (!initialState) {
    return loading;
  }

  if (!currentUser || !currentUser.name) {
    return loading;
  }

  const menuItems = [
    ...(menu
      ? [
          {
            key: 'center',
            icon: <UserOutlined />,
            label: '个人中心',
          },
          {
            key: 'settings',
            icon: <SettingOutlined />,
            label: '个人设置',
          },
          {
            type: 'divider' as const,
          },
        ]
      : []),
    {
      key: 'change-password',
      icon: <UserSwitchOutlined />,
      label: '修改密码',
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
    },
  ];

  return (
    <>
      <HeaderDropdown
        menu={{
          selectedKeys: [],
          onClick: onMenuClick,
          items: menuItems,
        }}
        overlayStyle={{ top: 55 }}
      >
        <span className={actionClassName}>
          <AvatarLogo />
          <Name />
        </span>
      </HeaderDropdown>
      <ChangePassword
        open={openPasswordModal}
        onClose={() => setOpenPasswordModal(false)}
        logOut={logoutAndClearInfo}
      />
    </>
  );
};

export default AvatarDropdown;
