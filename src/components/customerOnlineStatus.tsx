import { cn } from '@/lib/utils';
import { GetChatOnlineStatus, PostChatOnlineStatusUpdate } from '@/services/douyin-im/chat';
import { useMatch, useRequest } from '@umijs/max';
import { useLocalStorageState } from 'ahooks';
import { Dropdown } from 'antd';
import { ChevronDown } from 'lucide-react';

// 在组件内添加下拉菜单项配置
const statusItems = [
  {
    key: 'online',
    label: (
      <div className="flex items-center gap-1 py-1">
        <span className="h-2 w-2 rounded-full bg-green-500" />
        在线
        <span className="text-xs text-gray-400">在人工客服服务时间段</span>
      </div>
    ),
  },
  {
    key: 'offline',
    label: (
      <div className="flex items-center gap-1 py-1">
        <span className="h-2 w-2 rounded-full bg-gray-500" />
        离线
        <span className="text-xs text-gray-400">不在人工客服服务时间段</span>
      </div>
    ),
  },
];

export default function CustomerOnlineStatus() {
  const [projectId] = useLocalStorageState<string | null>('customer_system_project_id', {
    listenStorageChange: true,
  });
  const match = useMatch('/customer-system');

  const { data: onlineStatus, refresh: refreshOnlineStatus } = useRequest(
    () => {
      if (!projectId) return Promise.reject();
      return GetChatOnlineStatus({ projectId });
    },
    {
      refreshDeps: [projectId],
      ready: !!projectId && !!match,
    },
  );

  const isOnline = Boolean(onlineStatus?.onlineStatus);

  if (!match) return null;

  return (
    <Dropdown
      menu={{
        items: statusItems,
        onClick: async (e) => {
          if (!projectId) return;
          await PostChatOnlineStatusUpdate({
            projectId,
            onlineStatus: e.key === 'online' ? 1 : 0,
          });
          refreshOnlineStatus();
        },
      }}
      trigger={['click']}
      className="mr-4"
    >
      <div
        className={cn('flex h-[32px] cursor-pointer items-center gap-1 rounded-sm px-2 py-1', {
          'bg-[#EAF5F0] text-[#0FA964]': isOnline,
          'bg-[#F3F5F9] text-new-media-gray-800': !isOnline,
        })}
      >
        <span className={cn('h-2 w-2 rounded-full', isOnline ? 'bg-green-500' : 'bg-gray-500')} />
        {isOnline ? '客服在线' : '客服离线'}
        <ChevronDown size={12} />
      </div>
    </Dropdown>
  );
}
