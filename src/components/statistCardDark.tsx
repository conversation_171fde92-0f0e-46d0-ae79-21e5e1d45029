import { StatisticCard } from '@ant-design/pro-components';
import { ReactNode } from 'react';
import { styled } from 'styled-components';

type StatistCardDarkProps = {
  title: string;
  value: string | number;
  tip?: string | ReactNode;
};

const CustomStatisticCard = styled(StatisticCard)`
  padding: 16px 24px;
  .ant-pro-card-statistic-layout-vertical {
    display: flex;
    flex-direction: column-reverse;
  }

  .ant-statistic-title {
    color: #5a5b5d;
    white-space: nowrap;
  }
`;

const StatistCardDark = (props: StatistCardDarkProps) => {
  const { title, value, tip } = props;

  return (
    <CustomStatisticCard
      ghost
      statistic={{
        title,
        value,
        valueStyle: {
          color: '#769DFF',
          fontSize: '24px',
          height: '37px',
        },
        tip,
      }}
    />
  );
};

export default StatistCardDark;
