import { Button } from 'antd';
import { Sun, Moon } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useDarkMode } from '@/hooks/useDarkMode';

export const ThemeToggle = () => {
  const { isDarkMode, toggleDarkMode } = useDarkMode();

  return (
    <Button
      type="text"
      className={cn(
        'inline-flex h-9 w-9 items-center justify-center rounded-full',
        isDarkMode
          ? 'hover:bg-neutral-700 hover:text-yellow-400'
          : 'hover:bg-gray-100 hover:text-blue-600',
      )}
      icon={isDarkMode ? <Sun className="h-4 w-4" /> : <Moon className="h-4 w-4" />}
      onClick={toggleDarkMode}
      style={{ marginRight: '16px' }}
    />
  );
};
