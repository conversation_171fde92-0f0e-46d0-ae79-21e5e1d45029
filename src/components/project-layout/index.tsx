import Exception from '@/components/exception';
import RightContent from '@/components/RightContent';
import { ThemeToggle } from '@/components/themeToggle';
import useProjectRoutes from '@/hooks/useProjectRoutes';
import { selectingDealsAtom } from '@/pages/ProjectHome/atom';
import { ProjectDataItem } from '@/services/project';
import { loginPath, registerPath } from '@/utils/const';
import { MenuDataItem, ProLayout } from '@ant-design/pro-components';
import {
  Access,
  history,
  Link,
  Outlet,
  useAccess,
  useLocation,
  useModel,
  useParams,
} from '@umijs/max';
import { Button, ConfigProvider, Flex, theme } from 'antd';
import { useEffect, useMemo, useState } from 'react';
// import { isTestEnvironment } from '@/utils/common';
import { useSetAtom } from 'jotai';
import styles from './style.module.less';
import DictionaryIcon from '@/assets/svg/dictionary.svg';
import DataDictionaryDrawer from '../dataDictionary';

// 把路径中的动态参数替换为实际值
const replacePathWithParams = (path: string, projectKey: string, industryType: string) => {
  return path.replace(':projectKey', projectKey).replace(':industryType', industryType);
};

const isDev = process.env.NODE_ENV === 'development';

const ProjectLayout = () => {
  const { initialState } = useModel('@@initialState');

  const location = useLocation();
  const { projectKey, industryType = '1' } = useParams();
  const {
    token: { colorBgContainer },
  } = theme.useToken();
  const accessConfig: Record<string, boolean | undefined> = useAccess();
  const projectRoutes = useProjectRoutes();
  const setSelectingDeals = useSetAtom(selectingDealsAtom);
  const pathname = useMemo(() => location.pathname, [location]);
  const [openDrawer, setOpenDrawer] = useState(false);

  // 取路由路径中最后的名称作为key取匹配组件
  const pathKey = useMemo(() => {
    if (!pathname) {
      return null;
    }

    return pathname.slice(pathname.lastIndexOf('/') + 1);
  }, [pathname]);

  const currentProject = useMemo(() => {
    try {
      const projectList: ProjectDataItem[] = JSON.parse(localStorage.getItem('projectList') || '');
      return projectList.find((item: ProjectDataItem) => item.projectKey === projectKey);
    } catch {
      return null;
    }
  }, [projectKey]);

  useEffect(() => {
    // 如果没有登录，重定向到 login
    if (!initialState?.currentUser && pathname !== loginPath && pathname !== registerPath) {
      history.replace(loginPath);
    } else {
      if (!projectRoutes.routes) {
        return;
      }

      let routeName: string | null = null;
      for (const item of projectRoutes.routes) {
        if (!item.routes) {
          if (item.path?.split('/')?.pop() === pathname?.split('/')?.pop()) {
            routeName = item.name || '';
            break;
          }
        } else {
          const currentRouteItem = item.routes.find((item) => {
            return item.path?.split('/')?.pop() === pathname?.split('/')?.pop();
          });
          if (currentRouteItem) {
            routeName = currentRouteItem?.name || '';
            break;
          }
        }
      }

      if (currentProject?.name || routeName) {
        document.title = `${isDev ? '[dev] ' : ''}${routeName || ''} - ${currentProject?.name || ''}`;
      }
    }
  }, [pathname, initialState]);

  const menuDataRender = (menuData: MenuDataItem[]): MenuDataItem[] => {
    return menuData
      .map((item) => {
        const { access, children, ...rest } = item;
        if (access && !accessConfig[access]) {
          return null;
        }
        //  处理子菜单
        if (children && children.length > 0) {
          const renderedChildren = menuDataRender(children); // 递归处理子菜单
          // 如果子菜单全部没有权限，则返回 null
          if (renderedChildren.every((child) => child === null)) {
            return null;
          }
          return {
            ...rest,
            children: renderedChildren,
          };
        }
        return rest;
      })
      .filter(Boolean) as MenuDataItem[]; // 过滤掉为 null 的菜单项
  };

  const isAccess = useMemo(() => {
    const _access: Record<string, boolean | undefined> = {
      'clue-docking': accessConfig.isAdmin,
      'edit-incentive': accessConfig.isInsideAccount,
      'account-tags': accessConfig.isAdmin,
    };

    if (pathKey && pathKey in _access) {
      return _access[pathKey] || false;
    } else {
      return true;
    }
  }, [accessConfig, pathKey, projectRoutes]);

  const contentStyle = useMemo<React.CSSProperties | undefined>(() => {
    if (pathKey === 'live-map') {
      return {
        padding: 0,
      };
    }
    return {
      // 1224px 是 1680*1050 的设备减去侧边栏情况下的 content 最小宽度
      minWidth: '1224px',
      backgroundColor: '#F5F5F5',
    };
  }, [pathKey]);

  return (
    <div
      style={{
        height: '100vh',
        overflow: 'auto',
      }}
    >
      <ConfigProvider
        theme={{
          token: {
            colorPrimary: '#1E5EFF',
          },
          components: {
            Button: {
              colorPrimary: '#1E5EFF',
            },
            Radio: {
              buttonSolidCheckedBg: '#1E5EFF',
              buttonSolidCheckedActiveBg: '#1E5EFF',
              buttonSolidCheckedHoverBg: '#487bfc',
            },
            Switch: {
              colorPrimary: '#1E5EFF',
            },
            Input: {
              colorBorder: '#D3D5DA',
            },
            Select: {
              colorBorder: '#D3D5DA',
            },
            DatePicker: {
              colorBorder: '#D3D5DA',
            },
            Segmented: {
              itemSelectedColor: '#1E5EFF',
            },
          },
        }}
      >
        <ProLayout
          contentStyle={contentStyle}
          // 产品要暂时去掉
          // waterMarkProps={
          //   isTestEnvironment
          //     ? {
          //         content: '测试环境',
          //       }
          //     : undefined
          // }
          className={styles.projectLayout}
          token={{
            sider: {
              colorMenuBackground: colorBgContainer,
            },
          }}
          layout="mix"
          breadcrumbRender={false}
          fixedHeader={true}
          siderWidth={200}
          location={location}
          route={projectRoutes}
          headerRender={() => {
            return (
              <div
                style={{
                  display: 'flex',
                  height: '56px',
                  alignItems: 'center',
                  padding: 0,
                  backgroundColor: '#fff',
                  borderBottom: '1px solid #E5E7F0',
                  boxShadow:
                    '0 1px 2px 0 rgba(0, 0, 0, 0.03),0 1px 6px -1px rgba(0, 0, 0, 0.02),0 2px 4px 0 rgba(0, 0, 0, 0.02)',
                }}
              >
                <Flex align="center" justify="center" style={{ marginLeft: '32px', gap: '10px' }}>
                  <img alt="logo" src={currentProject?.iconUrl} width={34} height={34} />
                  <span
                    style={{
                      fontSize: '18px',
                      fontWeight: '400',
                      color: '#000',
                      fontFamily: 'OPPOSans',
                    }}
                  >
                    {currentProject?.name}
                  </span>
                </Flex>
                <div style={{ display: 'flex', alignItems: 'center', marginLeft: 'auto' }}>
                  <Button
                    onClick={() => setOpenDrawer(true)}
                    className="mr-4"
                    icon={
                      <img src={DictionaryIcon} className="-translate-y-[1px]" alt="数据字典" />
                    }
                  >
                    数据字典
                  </Button>
                  <ThemeToggle />
                  <RightContent />
                </div>
              </div>
            );
          }}
          menuDataRender={menuDataRender}
          menuItemRender={(item, dom) => {
            if (item.path && projectKey) {
              const path = replacePathWithParams(item.path, projectKey, industryType);
              return (
                <Link
                  to={path}
                  onClick={() => {
                    setSelectingDeals([]);
                  }}
                >
                  {dom}
                </Link>
              );
            }
            return dom;
          }}
        >
          <Access
            accessible={isAccess}
            fallback={
              <Exception
                type={403}
                homePage={`/project/${projectKey}/${industryType}/overview/data-dashboard`}
              />
            }
          >
            <Outlet />
          </Access>
        </ProLayout>

        <DataDictionaryDrawer openDrawer={openDrawer} setOpenDrawer={setOpenDrawer} />
      </ConfigProvider>
    </div>
  );
};

export default ProjectLayout;
