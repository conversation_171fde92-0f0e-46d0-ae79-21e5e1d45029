.projectLayout {
  :global {
    .ant-menu-submenu-selected > div:first-child {
      font-weight: 500;
    }

    .ant-menu-item-selected {
      color: #fff;
      font-family: 'OPPOSans';
      background-color: #1e5eff;

      &:hover {
        color: #fff !important;
      }
    }

    // .ant-menu-item:not(.ant-menu-item-selected):active {
    //   font-family: 'OPPOSans';
    //   background-color: #1e5eff;
    // }

    .ant-menu-submenu-selected > .ant-menu-submenu-title {
      color: #335ff6;
    }

    .ant-layout-sider-children {
      border-inline-end: 1px solid #eceef2 !important;
    }
  }

  &.dark {
    :global {
      .ant-pro-card {
        background-color: #161a23;
      }

      .ant-pro-card-col {
        border-color: #2e333e;
      }
    }
  }
}

.cardDarkStyle {
  background-color: #161a23;
}

.buttonDarkStyle {
  color: #ffffffcc;
  background-color: #161a23;
}

.inputDarkStyle {
  background-color: #161a23;
  border-color: #2e333e;

  input {
    color: #ffffffcc;
    background-color: #161a23;

    &::placeholder {
      color: #2e333e;
    }
  }
}
