import { QualityTargetType } from '@/services/constants';
import {
  GetLiveComment,
  GetPostComment,
  LiveQualityItem,
  PostQualityItem,
} from '@/services/quality';
import { HideScrollBarDiv } from '@/utils/commonStyle';
import { ConfigProvider, Divider, Flex, Input, List, Skeleton, theme } from 'antd';
import { useEffect, useState } from 'react';
import InfiniteScroll from 'react-infinite-scroll-component';
import styled from 'styled-components';

type CommentShowProps = {
  qualityData: LiveQualityItem | PostQualityItem;
  type: QualityTargetType;
};

export type DanmuItem = {
  id: string;
  nickname: string;
  content: string;
  time: string;
  startTime?: number | string;
  count?: number;
};

const CustomList = styled(List)`
  .ant-list-empty-text {
    margin-top: 200px;
  }

  .highlight {
    color: black;
    background: yellow;
  }
`;

const { Search } = Input;

const CommentShow = (props: CommentShowProps) => {
  const { qualityData, type } = props;
  const [danmuList, setDanmuList] = useState<DanmuItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState<number>(0);
  const [page, setPage] = useState<number>(1);
  const [searchValue, setSearchValue] = useState<string>();

  const fetchComment = async (searchValue?: string) => {
    const commonParams = {
      page: searchValue ? 1 : page,
      size: 10,
    };
    let res;
    if (type === QualityTargetType.LIVE) {
      res = await GetLiveComment({
        roomId: (qualityData as LiveQualityItem).roomId,
        platform: qualityData.platform,
        content: searchValue,
        ...commonParams,
      });
    } else if (type === QualityTargetType.POST) {
      res = await GetPostComment({
        postId: (qualityData as PostQualityItem).postId,
        platform: qualityData.platform,
        content: searchValue,
        ...commonParams,
      });
    }
    const total = res?.data?.total;
    const data = res?.data?.items;
    setTotal(total || 0);
    if (data && data?.length > 0) {
      if (searchValue) {
        // 关键词查找的时候只显示查找内容
        setDanmuList(data);
      } else {
        // 做滚动触底加载
        setDanmuList([...danmuList, ...data]);
      }
      setLoading(false);
      setPage(() => page + 1);
    } else {
      setDanmuList([]);
      setLoading(false);
      setPage(1);
    }
  };

  const loadMoreData = async (searchValue?: string) => {
    if (loading) {
      return;
    }
    setLoading(true);
    fetchComment(searchValue);
  };

  useEffect(() => {
    loadMoreData(searchValue);
  }, [searchValue]);

  const renderContent = (content: string) => {
    let showContent = content;
    if (searchValue) {
      const searchRegex = new RegExp(searchValue, 'gi');
      showContent = showContent.replace(searchRegex, '<span class="highlight">$&</span>');
    }
    return showContent;
  };

  return (
    <Flex vertical gap={5}>
      <ConfigProvider theme={{ algorithm: theme.darkAlgorithm }}>
        <Search
          placeholder="输入关键词过滤"
          onChange={(e) => {
            const value = e.target.value;
            if (!value) {
              setDanmuList([]);
              setPage(1);
            }
            setSearchValue(e.target.value);
          }}
          value={searchValue}
          allowClear
          style={{ flex: 1 }}
        />
      </ConfigProvider>
      <HideScrollBarDiv
        id="scrollableDiv"
        style={{
          height: 600,
        }}
      >
        <InfiniteScroll
          dataLength={danmuList.length}
          next={loadMoreData}
          hasMore={danmuList.length < total}
          loader={<Skeleton paragraph={{ rows: 1 }} active />}
          endMessage={
            danmuList.length > 0 ? (
              <Divider plain style={{ color: '#d1d2d3' }}>
                已全部展示
              </Divider>
            ) : null
          }
          scrollableTarget="scrollableDiv"
        >
          <ConfigProvider theme={{ algorithm: theme.darkAlgorithm }}>
            <CustomList
              itemLayout="horizontal"
              dataSource={danmuList}
              style={{ width: '100%' }}
              renderItem={(_item) => {
                // 用styled类型提示报错了 这里as一下
                const item = _item as DanmuItem;
                const content = renderContent(item.content);
                return (
                  <List.Item>
                    <List.Item.Meta
                      title={
                        <div
                          style={{
                            display: 'flex',
                            flexDirection: 'row',
                            justifyContent: 'space-between',
                            color: 'rgba(255, 255, 255, 0.5)',
                          }}
                        >
                          <span>{item.nickname}</span>
                          <span>{item.time}</span>
                        </div>
                      }
                      description={
                        <span
                          dangerouslySetInnerHTML={{
                            __html: content,
                          }}
                          style={{ color: 'rgba(255, 255, 255, 0.80)' }}
                        />
                      }
                    />
                  </List.Item>
                );
              }}
            />
          </ConfigProvider>
        </InfiniteScroll>
      </HideScrollBarDiv>
    </Flex>
  );
};

export default CommentShow;
