import { Icon } from '@umijs/max';
import { HTMLAttributes } from 'react';
import styled from 'styled-components';

type PropsOf<C> = C extends (props: infer P) => any ? P : never;

const AdjustIcon = styled(Icon)<{ x?: number; y?: number; vertical?: VerticalType }>`
  &.adjust-style {
    display: inline-block;
    vertical-align: ${(props) => (props.vertical ? 'bottom' : '')};
    transform: translate(${(props) => props.x || 0}px, ${(props) => props.y || 0}px);
    fill: currentColor;
    stroke: currentColor;
  }
`;

type VerticalType =
  | 'baseline'
  | 'sub'
  | 'super'
  | 'text-top'
  | 'text-bottom'
  | 'middle'
  | 'top'
  | 'bottom';

/**
 * 指定icons下的svg的名字来使用icon 可以指定微小的x y偏移 和 vertical
 * @param props
 * @returns
 */
export const SvgIcon = (
  props: { x?: number; y?: number; vertical?: VerticalType } & PropsOf<typeof Icon> &
    HTMLAttributes<HTMLSpanElement>,
) => {
  // @ts-ignore
  return <AdjustIcon {...props} key={props.icon} className={`adjust-style ${props.className}`} />;
};
