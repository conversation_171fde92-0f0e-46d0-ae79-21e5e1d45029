import { QualityTargetType } from '@/services/constants';
import { WordCloudData } from '@/services/quality';
import { ProCardDark } from '@/utils/commonStyle';
import { useCallback, useEffect, useId } from 'react';
import WordCloud from 'wordcloud';

type WordCloudComponentProps = {
  wordCloudData: WordCloudData[];
  target: QualityTargetType;
};

const WordCloudComponent = (props: WordCloudComponentProps) => {
  const { wordCloudData, target } = props;

  const wordCloudContainerId = useId();

  const filterWordCloudData = useCallback(() => {
    return wordCloudData
      .filter((item) => {
        // 保留包含中文或英文字母的单词
        return /^[\u4e00-\u9fa5a-zA-Z]+$/.test(item.word);
      })
      .map((item) => ({
        ...item,
        count: item.count > 100 ? 100 : item.count,
      }));
  }, [wordCloudData]);

  useEffect(() => {
    const container = document.getElementById(wordCloudContainerId);
    if (container) {
      WordCloud(container, {
        list: filterWordCloudData().map((item) => [item.word, item.count]),
        gridSize: 5,
        weightFactor: 15,
        fontWeight: 600,
        shuffle: true,
        backgroundColor: '#1b1d22',
        shrinkToFit: true,
        rotateRatio: 0,
        drawOutOfBound: false,
        abortThreshold: 3000,
      });
    }
  }, [wordCloudData]);

  return (
    <ProCardDark
      style={{ marginTop: '20px' }}
      title={target === QualityTargetType.LIVE ? '弹幕词云' : '评论词云'}
      ghost
    >
      <div id={wordCloudContainerId} style={{ width: '100%', height: '200px' }}>
        {wordCloudData.map((word) => (
          <span key={word.word} data-word-id={word.word}>
            {word.word}
          </span>
        ))}
      </div>
    </ProCardDark>
  );
};

export default WordCloudComponent;
