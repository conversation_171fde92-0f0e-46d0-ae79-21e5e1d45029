import useProjectId from '@/hooks/useProjectId';
import { cn } from '@/lib/utils';
import { selectPlatformAtom } from '@/pages/ProjectHome/atom';
import { GetProjectInfo, GetProjectInfoByPayload } from '@/services/project';
import { useQuery } from '@umijs/max';
import { motion } from 'framer-motion';
import { useAtom } from 'jotai';
import { ReactNode, useState } from 'react';
import { Dropdown } from 'antd';
import { EllipsisOutlined } from '@ant-design/icons';
import { PlatForm, PLATFORM_LIST } from '@/utils/platform';

const buttonVariants = {
  initial: {
    gap: 0,
  },
  animate: (selected: boolean) => ({
    gap: selected ? '.5rem' : 0,
  }),
};

const transition = { delay: 0.1, type: 'spring', bounce: 0, duration: 0.35 };

type TabProps = {
  selected: boolean;
  setSelected: React.Dispatch<React.SetStateAction<PlatForm>>;
  children: ReactNode;
  platform: PlatForm;
  index: number;
};

const ShowMaxLogoNumber = 4;

const Tab = ({ selected, setSelected, platform, children }: TabProps) => {
  return (
    <motion.button
      variants={buttonVariants}
      initial="initial"
      animate="animate"
      custom={selected}
      onClick={() => setSelected(platform)}
      transition={transition}
      className={cn({
        'bg-blue-500/15 text-blue-500': selected,
        'hover:text-gray-900 dark:hover:text-gray-100': !selected,
        'relative flex h-[38px] items-center rounded-md px-1 text-sm font-medium text-gray-500 transition-colors duration-300 focus-within:outline-blue-500/50':
          true,
      })}
    >
      {children}
    </motion.button>
  );
};

// payload为日报链接上的参数 isLiveSetting为是否是直播设置页面 直播相关界面只有抖音平台可选
const PlatformSwitch = ({
  center,
  payload,
  onlyDouyin,
}: {
  center?: boolean;
  payload?: string | null;
  onlyDouyin?: boolean;
}) => {
  const projectId = useProjectId();
  const [platform, setPlatform] = useAtom(selectPlatformAtom);
  const [dropdownVisible, setDropdownVisible] = useState(false);

  const { data: projectInfoRes } = useQuery(
    ['projectInfo', projectId, payload],
    () => {
      if (payload) {
        return GetProjectInfoByPayload({ payload });
      } else {
        return GetProjectInfo({ projectId });
      }
    },
    {
      enabled: !!(projectId || payload),
      staleTime: Number.POSITIVE_INFINITY,
    },
  );

  const platformTabs = projectInfoRes?.data?.platformList;
  const renderTabs = onlyDouyin
    ? PLATFORM_LIST.filter((item) => item.value === PlatForm.Douyin)
    : PLATFORM_LIST.filter((item) => platformTabs?.includes(item.value));

  const visibleTabs = renderTabs.slice(0, ShowMaxLogoNumber);
  const dropdownTabs = renderTabs.slice(ShowMaxLogoNumber);

  // 如果当前选中的平台在下拉列表中，则将其移到可见列表的最后一个位置
  const selectedDropdownTab = dropdownTabs.find((tab) => tab.value === platform);
  const finalVisibleTabs = selectedDropdownTab
    ? [...visibleTabs.slice(0, ShowMaxLogoNumber - 1), selectedDropdownTab]
    : visibleTabs;
  const finalDropdownTabs = selectedDropdownTab
    ? [...dropdownTabs.filter((tab) => tab.value !== platform), visibleTabs[ShowMaxLogoNumber - 1]]
    : dropdownTabs;

  const dropdownItems = {
    items: finalDropdownTabs.map((tab) => ({
      key: tab.value,
      label: (
        <div className="flex items-center gap-2">
          <img src={tab.icon} alt={tab.label} width={30} height={30} />
          <span>{tab.label}</span>
        </div>
      ),
    })),
    onClick: ({ key }: { key: string }) => {
      setPlatform(Number(key) as PlatForm);
      setDropdownVisible(false);
    },
  };

  return (
    <div className={cn('flex items-center gap-1', center && 'justify-center')}>
      {finalVisibleTabs.map((tab, index) => (
        <Tab
          selected={platform === tab.value}
          setSelected={setPlatform}
          platform={tab.value}
          index={index}
          key={tab.label}
        >
          <img src={tab.icon} alt={tab.label} width={30} height={30} />
        </Tab>
      ))}
      {finalDropdownTabs.length > 0 && (
        <Dropdown
          menu={dropdownItems}
          open={dropdownVisible}
          onOpenChange={setDropdownVisible}
          placement="bottomRight"
        >
          <button className="flex h-[30px] w-[30px] items-center justify-center rounded-md border">
            <EllipsisOutlined />
          </button>
        </Dropdown>
      )}
    </div>
  );
};

export default PlatformSwitch;
