import useProjectId from '@/hooks/useProjectId';
import { GetTreeData, TreeData } from '@/services/team';
import { GetTreeDataDaily } from '@/services/unauth';
import { ActionType } from '@ant-design/pro-components';
import { useQuery } from '@umijs/max';
import { TreeSelect, TreeSelectProps } from 'antd';
import { memoize } from 'lodash-es';
import { useEffect, useRef, useState } from 'react';
import { ErrorBoundary } from 'react-error-boundary';
import { convertToTree, TreeNode } from './dataToTreeData';

type DynamicTreeProps = {
  value?: string[];
  onChange?: (value: any) => void;
  setValue?: React.Dispatch<React.SetStateAction<string[] | undefined>>;
  style?: React.CSSProperties;
  actionRef?: React.MutableRefObject<ActionType | undefined>;
  unAuth?: boolean;
  payload?: string | null;
  projectId?: string;
  nodeTitle?: string;
  /** 在下拉菜单收起时才setValue */
  triggerOnDropdownClose?: boolean;
};

const findNodeByTitle = (title: string, nodes: TreeNode[]): TreeNode[] => {
  let result: TreeNode[] = [];
  for (const node of nodes) {
    if (node.title === title) {
      result.push(node);
    }
    if (node.children) {
      result = result.concat(findNodeByTitle(title, node.children));
    }
  }
  return result;
};

// 动态字段生成层级结构
const DynamicTree = (props: DynamicTreeProps & TreeSelectProps) => {
  const projectId = useProjectId();
  const {
    value,
    onChange,
    setValue,
    style,
    actionRef,
    unAuth,
    payload,
    projectId: propsProjectId,
    nodeTitle,
    triggerOnDropdownClose,
    ...treeSelectProps
  } = props;
  const [treeData, setTreeData] = useState<any[]>([]);
  const [rawTreeData, setRawTreeData] = useState<any[]>([]);
  const selectingData = useRef(value);
  const memoizedConvertToTree = memoize(convertToTree);

  const { data } = useQuery(
    ['treeData', projectId, unAuth, payload],
    async () => {
      if (unAuth) {
        const res = await GetTreeDataDaily({ payload });
        return res?.data;
      } else if (projectId) {
        const res = await GetTreeData({ projectId: projectId || propsProjectId });
        return res?.data;
      }
      return [];
    },
    {
      staleTime: Number.POSITIVE_INFINITY,
    },
  );

  useEffect(() => {
    if (data) {
      const convertTreeData = memoizedConvertToTree(data as TreeData);
      setTreeData(convertTreeData);
      setRawTreeData(convertTreeData);
    }
  }, [data]);

  useEffect(() => {
    if (nodeTitle) {
      const convertTreeDataByTitle = findNodeByTitle(nodeTitle, rawTreeData) || [];
      setTreeData(convertTreeDataByTitle);
    }
  }, [nodeTitle, rawTreeData]);

  const treeSelectOnChange = (newValue: string[]) => {
    selectingData.current = newValue;
    if (!triggerOnDropdownClose) {
      setValue?.(newValue);
      onChange?.(newValue);
    }
    actionRef?.current?.clearSelected?.();
  };

  return (
    <ErrorBoundary fallback={<div>Something went wrong</div>}>
      <TreeSelect
        {...treeSelectProps}
        popupMatchSelectWidth={400}
        style={{ ...style, maxHeight: '32px' }}
        defaultValue={value}
        styles={{
          popup: {
            root: { maxHeight: 700, overflow: 'auto' },
          },
        }}
        treeData={treeData}
        placeholder="经销商名称"
        treeCheckable={true}
        onChange={treeSelectOnChange}
        maxTagCount="responsive"
        allowClear={true}
        treeNodeFilterProp="title" //根据看到的title来过滤
        onClear={() => {
          treeSelectProps.onClear?.();
          setValue?.([]);
        }}
        value={value}
        onOpenChange={(open) => {
          treeSelectProps.onOpenChange?.(open);
          if (!open) {
            setValue?.(selectingData.current);
          }
        }}
        // showCheckedStrategy={TreeSelect.SHOW_PARENT}
      />
    </ErrorBoundary>
  );
};

export default DynamicTree;
