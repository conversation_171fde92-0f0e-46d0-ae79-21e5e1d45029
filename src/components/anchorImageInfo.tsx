import { GetAnchorImageViolationDetailInfo } from '@/services/quality';
import { useRequest } from '@umijs/max';
import { Flex, Image } from 'antd';
import FallBackImg from '@/assets/fallback-img.png';

type AnchorImageViolationInfoProps = {
  projectId?: string;
  roomId?: string;
  type?: number;
  platform?: number;
};

const InfoTitle = ({ children }: { children: React.ReactNode }) => (
  <span className="mr-2 break-words text-base font-normal leading-5 text-[#989898]">
    {children}
  </span>
);
const InfoContent = ({ children }: { children: React.ReactNode }) => (
  <span className="break-words text-base font-normal leading-5 text-[#0e1015]">{children}</span>
);

const AnchorImageViolationInfo = (props: AnchorImageViolationInfoProps) => {
  const { projectId, roomId, type, platform } = props;

  const { data: anchorImageViolationInfoData } = useRequest(
    () =>
      GetAnchorImageViolationDetailInfo({
        projectId,
        roomId,
        type,
        platform,
      }),
    {
      refreshDeps: [projectId, roomId, type, platform],
    },
  );

  return (
    <Flex wrap="wrap" vertical style={{ width: '500px' }} gap={5}>
      <div>
        <InfoTitle>账号:</InfoTitle>
        <InfoContent>{anchorImageViolationInfoData?.nickname}</InfoContent>
      </div>
      <div>
        <InfoTitle>直播标题:</InfoTitle>
        <InfoContent>{anchorImageViolationInfoData?.title}</InfoContent>
      </div>
      <div>
        <InfoTitle>直播时间:</InfoTitle>
        <InfoContent>{anchorImageViolationInfoData?.exTime}</InfoContent>
      </div>
      <div>
        <Image.PreviewGroup>
          {anchorImageViolationInfoData?.picUrl?.map((item) => (
            <Image
              key={item}
              width={150}
              height={200}
              src={item}
              style={{ borderRadius: '4px', objectFit: 'cover' }}
              fallback={FallBackImg}
            />
          ))}
        </Image.PreviewGroup>
      </div>
    </Flex>
  );
};

export default AnchorImageViolationInfo;
