import useProjectId from '@/hooks/useProjectId';
import { selectPlatformAtom } from '@/pages/ProjectHome/atom';
import { GetProjectInfo, GetProjectInfoByPayload } from '@/services/project';
import { useQuery } from '@umijs/max';
import { AnimatePresence, motion, Variants } from 'framer-motion';
import { useAtom } from 'jotai';
import { useEffect, useRef, useState } from 'react';
import { PlatForm, PLATFORM_ICONS, PLATFORM_NAMES } from '@/utils/platform';

const menuAnimations: Variants = {
  initial: { opacity: 0, scale: 0.3 },
  animate: { opacity: 1, scale: 1 },
  exit: { opacity: 0, scale: 0.3 },
};

// 计算扇形布局的位置
const calculatePosition = (index: number, total: number, radius: number, isLeftSide: boolean) => {
  // 当只有一个平台时，直接显示在正上方
  if (total === 1) {
    return {
      x: 0,
      y: -radius,
    };
  }
  // 半圆的角度范围是 180 度（π 弧度）
  const angleStep = Math.PI / (total - 1);
  // 根据是否在左侧决定起始角度，-π/2 到 π/2 范围
  const startAngle = isLeftSide ? -Math.PI / 2 : Math.PI / 2;
  const angle = startAngle + index * angleStep;
  return {
    // 在左侧时向右展开（正值），在右侧时向左展开（负值）
    x: (isLeftSide ? 1 : -1) * Math.abs(radius * Math.cos(angle)),
    y: radius * Math.sin(angle),
  };
};

type PlatformSwitchMobileProps = {
  payload?: string | null;
  dragContentRef: React.MutableRefObject<null>;
};

const PlatformSwitchMobile = ({ payload, dragContentRef }: PlatformSwitchMobileProps) => {
  const projectId = useProjectId();
  const [platform, setPlatform] = useAtom(selectPlatformAtom);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const [isLeftSide, setIsLeftSide] = useState(false);

  // 检测组件位置并更新展开方向
  const onUpdatePosition = () => {
    if (containerRef.current) {
      const rect = containerRef.current.getBoundingClientRect();
      const screenMiddle = window.innerWidth / 2;
      setIsLeftSide(rect.left < screenMiddle);
    }
  };

  useEffect(() => {
    onUpdatePosition();
    window.addEventListener('resize', onUpdatePosition);
    return () => window.removeEventListener('resize', onUpdatePosition);
  }, []);

  const { data: projectInfoRes } = useQuery(
    ['projectInfo', projectId, payload],
    () => (payload ? GetProjectInfoByPayload({ payload }) : GetProjectInfo({ projectId })),
    {
      enabled: !!(projectId || payload),
      staleTime: Number.POSITIVE_INFINITY,
    },
  );

  const platformTabs = projectInfoRes?.data?.platformList || [];
  const availablePlatforms = (Object.values(PlatForm) as PlatForm[]).filter(
    (p) => platformTabs.includes(p) && p !== platform,
  );

  const onHandleMainIconClick = () => {
    onUpdatePosition();
    setIsMenuOpen(!isMenuOpen);
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsMenuOpen(false);
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, []);

  const onPlatformClick = (platformType: PlatForm) => {
    setPlatform(platformType);
    setIsMenuOpen(false);
  };

  const renderPlatformOption = (platformType: PlatForm, index: number) => {
    const position = calculatePosition(index, availablePlatforms.length, 80, isLeftSide);

    return (
      <motion.div
        key={platformType}
        onClick={() => onPlatformClick(platformType)}
        className="absolute flex rounded-full bg-white p-2 shadow-lg hover:shadow-2xl"
        initial={{ x: 0, y: 0, opacity: 0 }}
        animate={{
          x: isMenuOpen ? position.x : 0,
          y: isMenuOpen ? position.y : 0,
          opacity: 1,
        }}
        exit={{ x: 0, y: 0, opacity: 0 }}
        transition={{ type: 'spring', stiffness: 300, damping: 25 }}
        whileHover={{ scale: 1.1 }}
      >
        <motion.img
          src={PLATFORM_ICONS[platformType]}
          alt={PLATFORM_NAMES[platformType]}
          width={30}
          height={30}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3 }}
        />
      </motion.div>
    );
  };

  return (
    <motion.div
      ref={containerRef}
      drag
      dragConstraints={dragContentRef || false}
      whileDrag={{ scale: 1.5 }}
      dragElastic={0.1}
      onDrag={onUpdatePosition}
      className="group fixed bottom-20 right-0 flex h-24 w-24 items-center justify-center p-2"
    >
      <div
        onClick={onHandleMainIconClick}
        className="absolute z-50 flex items-center justify-center rounded-full bg-white p-3 shadow-xl"
      >
        <img src={PLATFORM_ICONS[platform]} alt={PLATFORM_NAMES[platform]} width={30} height={30} />
      </div>

      <AnimatePresence>
        {isMenuOpen ? (
          <motion.div
            className="absolute bottom-0 right-0 flex h-full w-full items-center justify-center"
            variants={menuAnimations}
            initial="initial"
            animate="animate"
            exit="exit"
          >
            {availablePlatforms.map((platformType, index) =>
              renderPlatformOption(platformType, index),
            )}
          </motion.div>
        ) : null}
      </AnimatePresence>
    </motion.div>
  );
};

export default PlatformSwitchMobile;
