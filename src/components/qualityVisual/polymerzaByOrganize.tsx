import { usePollingExport } from '@/hooks/usePollingExport';
import { selectPlatformAtom } from '@/pages/ProjectHome/atom';
import { customPaginationRender } from '@/pages/ProjectHome/style';
import {
  ExportQualityLiveOverviewAggregation,
  ExportQualityPostOverviewAggregation,
  GetQualityLiveOverviewAggregation,
  GetQualityPostOverviewAggregation,
} from '@/services/quality-visualization';
import { GetTeamDepthSelect, TeamDepthSelect } from '@/services/team';
import { proTableRequestAdapterParamsAndData } from '@/utils';
import { renderMom } from '@/utils/commonStyle';
import { ProCard, ProColumns, ProTable } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { message, Radio, RadioChangeEvent } from 'antd';
import { useAtomValue } from 'jotai';
import { omit } from 'lodash-es';
import { useRef, useState } from 'react';
import ExportButton from '../exportButton';

const liveColumns: ProColumns[] = [
  {
    title: '总直播场次',
    dataIndex: 'count',
    align: 'center',
    sorter: true,
  },
  {
    title: '违规直播场次',
    dataIndex: 'violationCount',
    align: 'center',
    sorter: true,
  },
  {
    title: '违规直播场次环比',
    dataIndex: 'violationMom',
    align: 'center',
    render: (_, record) => {
      return <>{renderMom(record.violationMom)}</>;
    },
    sorter: true,
  },
];

const postColumns: ProColumns[] = [
  {
    title: '总作品数',
    dataIndex: 'count',
    align: 'center',
    sorter: true,
  },
  {
    title: '违规作品数',
    dataIndex: 'violationCount',
    align: 'center',
    sorter: true,
  },
  {
    title: '违规作品环比',
    dataIndex: 'violationMom',
    align: 'center',
    render: (_, record) => {
      return <>{renderMom(record.violationMom)}</>;
    },
    sorter: true,
  },
];

const storeColumns: ProColumns[] = [
  {
    title: '总门店数',
    dataIndex: 'count',
    align: 'center',
    sorter: true,
  },
  {
    title: '违规门店数',
    dataIndex: 'violationCount',
    align: 'center',
    sorter: true,
  },
  {
    title: '违规门店数环比',
    dataIndex: 'violationMom',
    align: 'center',
    render: (_, record) => {
      return <>{renderMom(record.violationMom)}</>;
    },
    sorter: true,
  },
];

const accountColumns: ProColumns[] = [
  {
    title: '总账号数',
    dataIndex: 'count',
    align: 'center',
    sorter: true,
  },
  {
    title: '违规账号数',
    dataIndex: 'violationCount',
    align: 'center',
    sorter: true,
  },
  {
    title: '违规账号数环比',
    dataIndex: 'violationMom',
    align: 'center',
    render: (_, record) => {
      return <>{renderMom(record.violationMom)}</>;
    },
    sorter: true,
  },
];

const unitMap: Record<number, string> = {
  1: '场次',
  2: '门店数',
  3: '账号数',
};

const AfkColumns = (aggregationType: number) => {
  return [
    {
      title: `严重空播挂播${unitMap[aggregationType]}`,
      dataIndex: 'seriousAfkLiveCount',
      align: 'center',
      sorter: true,
    },
    {
      title: `轻微空播挂播${unitMap[aggregationType]}`,
      dataIndex: 'minorAfkLiveCount',
      align: 'center',
      sorter: true,
    },
  ];
};

type PolymerzatByOrganizeProps = {
  rangeTime: string[];
  aggregationType: number;
  projectId?: string;
  fieldList?: {
    fieldId?: number;
    value?: string;
  }[];
  dateType: number;
  type: 'live' | 'post';
  afkState?: boolean;
};

const PolymerzaByOrganize = (props: PolymerzatByOrganizeProps) => {
  const { rangeTime, aggregationType, projectId, fieldList, dateType, type, afkState } = props;
  const [fieldId, setFieldId] = useState<number | null>(null);
  const paramsRef = useRef<any>({});
  const { pollingExport, percent, pollingLoading } = usePollingExport();
  const recordColumnsRef = useRef([]);
  const platform = useAtomValue(selectPlatformAtom);

  const { data: teamDepthData } = useRequest(() => GetTeamDepthSelect({ projectId }), {
    onSuccess: (data) => {
      const res = data?.[0].value;
      setFieldId(Number(res));
    },
  });

  const handleExport = async () => {
    const omitPageParams = omit(paramsRef.current, ['pageSize', 'current']);
    const exportTitleList = recordColumnsRef.current?.map((item: any) => ({
      fieldName: item.dataIndex,
      title: item.title,
    }));
    const requestFn =
      type === 'live' ? ExportQualityLiveOverviewAggregation : ExportQualityPostOverviewAggregation;
    const res = await requestFn({ ...omitPageParams, exportTitleList });
    if (res.data) {
      pollingExport({ taskId: res.data });
    } else {
      message.error('导出失败,请重试');
    }
  };

  const renderColumns = (
    aggregationType: number,
    teamDepthData?: TeamDepthSelect[],
    afkState?: boolean,
  ) => {
    const result: any = [];
    const findFieldName = teamDepthData?.find((item) => item.value === fieldId?.toString());
    result.push({
      title: findFieldName?.name,
      dataIndex: 'name',
      align: 'center',
    });
    switch (aggregationType) {
      case 1:
        type === 'live' ? result.push(...liveColumns) : result.push(...postColumns);
        break;
      case 2:
        result.push(...storeColumns);
        break;
      case 3:
        result.push(...accountColumns);
    }
    if (type === 'live' && afkState) {
      result.push(...AfkColumns(aggregationType));
    }
    result.push(
      {
        title: '质检类型TOP3',
        dataIndex: 'monitoringWordNameStr',
        align: 'center',
      },
      {
        title: '高频监控词TOP3',
        dataIndex: 'highFrequencyMonitoringWordStr',
        align: 'center',
      },
    );
    recordColumnsRef.current = result;
    return result;
  };

  return (
    <>
      <h3>按组织架构聚合明细</h3>
      <ProCard style={{ backgroundColor: '#fafcff', marginBottom: 20 }}>
        <ProTable
          rowKey="name"
          ghost
          scroll={{ x: 'max-content' }}
          params={{
            projectId,
            fieldList,
            startDate: rangeTime[0],
            endDate: rangeTime[1],
            dateType,
            aggregationType,
            fieldId,
            platform,
          }}
          tableClassName="custom-table"
          // @ts-ignore
          columns={renderColumns(aggregationType, teamDepthData, afkState)}
          request={(params, sorter) => {
            if (!fieldId) return Promise.resolve([]);
            paramsRef.current = params;
            const requestFn =
              type === 'live'
                ? GetQualityLiveOverviewAggregation
                : GetQualityPostOverviewAggregation;
            return proTableRequestAdapterParamsAndData(params, sorter, requestFn);
          }}
          search={false}
          headerTitle={
            <>
              {teamDepthData && (
                <Radio.Group
                  value={fieldId?.toString()}
                  onChange={(e: RadioChangeEvent) => setFieldId(e.target.value)}
                  buttonStyle="solid"
                  style={{ whiteSpace: 'nowrap' }}
                >
                  {teamDepthData.map((item) => (
                    <Radio.Button key={item.value} value={item.value}>
                      {item.name}
                    </Radio.Button>
                  ))}
                </Radio.Group>
              )}
            </>
          }
          toolBarRender={() => [
            <ExportButton
              exportFn={() => handleExport()}
              loading={pollingLoading}
              percent={percent}
              key="export"
            />,
          ]}
          pagination={{
            defaultPageSize: 10,
            hideOnSinglePage: true,
            showTotal: (total, range) => `${range[0]}-${range[1]} 共 ${total} 条`,
            itemRender: customPaginationRender,
          }}
        />
      </ProCard>
    </>
  );
};

export default PolymerzaByOrganize;
