import { getTimeByTypeSimple } from '@/utils/time';
import { ProCard } from '@ant-design/pro-components';
import { Select, Space } from 'antd';
import { useState } from 'react';
import { styled } from 'styled-components';
import { useAsyncEffect } from 'ahooks';
import { QueryTeamListFields } from '@/services/project';
import { ProjectTeamFieldItem } from '@/services/typings';
import { GetFieldValue } from '@/services/team';
import OverAllTrend from './overAllTrend';
import AfkDistribution from './afkDistribution';
import TimeFilterByNatural from './timeFilterByNatural';
import PolymerizaByQualityType from './polymerizaByQualityType';
import PolymerzaByOrganize from './polymerzaByOrganize';
import useLiveAfkFG from '@/hooks/fg/useLiveAfkFG';

type VisualDataProps = {
  projectId?: string;
  type: 'live' | 'post';
};

const HideScrollBarProCard = styled(ProCard)`
  width: 100%;
  overflow-y: auto;
  position: relative;
  max-height: calc(100vh - 220px);
  &::-webkit-scrollbar {
    display: none; /* Safari 和 Chrome */
  }
  -ms-overflow-style: none; /* IE 和 Edge */
  scrollbar-width: none; /* Firefox */
`;

const QualityVisualData = (props: VisualDataProps) => {
  const { projectId, type } = props;
  const [rangeTime, setRangeTime] = useState<string[]>(getTimeByTypeSimple(7));
  const [depthFirstData, setDepthFirstData] = useState<ProjectTeamFieldItem>();
  const [options, setOptions] = useState<{ value: string; label: string }[]>([]);
  const [fieldValue, setFieldValue] = useState<string | undefined>(undefined);
  const [aggregationType, setAggregationType] = useState<number>(1); // 聚合类型 1-场次/条 2-门店数 3-账号数
  const [dateType, setDateType] = useState<number>(2); // 时间类型 1-近一天 2-近一周 3-近一月 4-自定义
  const fieldList = fieldValue ? [{ fieldId: depthFirstData?.id, value: fieldValue }] : undefined;
  const [depthId, setDepthId] = useState<string | undefined>(undefined);
  const afkState = useLiveAfkFG(projectId);

  useAsyncEffect(async () => {
    const teamListFieldsRes = await QueryTeamListFields({ projectId });
    const depthFirstData = teamListFieldsRes?.data?.find(
      (item) => item.bizType === 2 && item.depth === 1,
    );
    setDepthId(depthFirstData?.id.toString());
    setDepthFirstData(depthFirstData);
    const fieldValue = await GetFieldValue({ fieldId: depthFirstData?.id, projectId });
    const optionsRes = fieldValue?.data?.map((item) => ({ value: item.value, label: item.name })) || [];
    setOptions(optionsRes);
  }, []);

  return (
    <HideScrollBarProCard direction="column" bodyStyle={{ paddingTop: 0, minWidth: 1100 }}>
      <Space
        style={{
          position: 'sticky',
          top: 0,
          zIndex: 99,
          backgroundColor: '#fff',
          paddingBlock: 10,
          width: '100%',
        }}
      >
        <TimeFilterByNatural
          rangeTime={rangeTime}
          setRangeTime={setRangeTime}
          dateType={dateType}
          setDataType={setDateType}
        />
        <Select
          style={{ width: 200 }}
          value={fieldValue}
          onChange={(value: string) => {
            setFieldValue(value);
          }}
          allowClear
          options={options}
          placeholder={`选择${depthFirstData?.fieldName}`}
        />
        <Select
          style={{ width: 200 }}
          value={aggregationType.toString()}
          onChange={(value: string) => {
            setAggregationType(Number(value));
          }}
          options={[
            { value: '1', label: type === 'live' ? '按直播场次' : '按作品数' },
            { value: '2', label: '按门店数' },
            { value: '3', label: '按账号数' },
          ]}
        />
      </Space>
      <OverAllTrend
        projectId={projectId}
        rangeTime={rangeTime}
        aggregationType={aggregationType}
        dateType={dateType}
        fieldList={fieldList}
        type={type}
      />
      <PolymerizaByQualityType
        projectId={projectId}
        rangeTime={rangeTime}
        aggregationType={aggregationType}
        dateType={dateType}
        fieldList={fieldList}
        type={type}
        fieldValue={fieldValue}
        depthId={depthId}
      />
      {type === 'live' && afkState && (
        <AfkDistribution
          projectId={projectId}
          rangeTime={rangeTime}
          aggregationType={aggregationType}
          dateType={dateType}
          fieldList={fieldList}
          fieldValue={fieldValue}
          depthId={depthId}
        />
      )}
      <PolymerzaByOrganize
        projectId={projectId}
        rangeTime={rangeTime}
        aggregationType={aggregationType}
        dateType={dateType}
        fieldList={fieldList}
        type={type}
        afkState={afkState}
      />
    </HideScrollBarProCard>
  );
};

export default QualityVisualData;
