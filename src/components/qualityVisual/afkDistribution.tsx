import { useDarkMode } from '@/hooks/useDarkMode';
import { selectPlatformAtom } from '@/pages/ProjectHome/atom';
import {
  GetQualityLiveAfkAggregation,
  GetQualityLiveAfkOverview,
  GetQualityLiveAfkTrend,
  QualityLiveAfkOverview,
} from '@/services/quality-visualization';
import { GetTeamDepthSelect } from '@/services/team';
import { slicePieData } from '@/utils';
import { InfoCard, pieColor } from '@/utils/commonStyle';
import { renderXAxis } from '@/utils/time';
import { InfoCircleOutlined } from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Flex, Select, Skeleton, Space, Tooltip } from 'antd';
import ReactECharts from 'echarts-for-react';
import { useAtomValue } from 'jotai';
import { useEffect, useState } from 'react';

type AfkDistributionProps = {
  rangeTime: string[];
  aggregationType: number;
  projectId?: string;
  dateType: number;
  fieldList?: {
    fieldId?: number;
    value?: string;
  }[];
  fieldValue?: string;
  depthId?: string;
};

const AfkDistribution = (props: AfkDistributionProps) => {
  const { rangeTime, aggregationType, projectId, dateType, fieldList, fieldValue, depthId } = props;
  const [fieldId, setFieldId] = useState<number | undefined>(undefined);
  const [afkLevel, setAfkLevel] = useState<number | null>(null);
  const xAxisData = renderXAxis(rangeTime);
  const platform = useAtomValue(selectPlatformAtom);
  const { isDarkMode } = useDarkMode();

  const { data: teamDepthData } = useRequest(() => GetTeamDepthSelect({ projectId }), {
    onSuccess: (data) => {
      const res = data?.[0].value;
      setFieldId(Number(res));
    },
  });

  const { data: liveAfkOverViewData, loading: liveAfkOverViewDataLoading } = useRequest(
    () =>
      GetQualityLiveAfkOverview({
        projectId,
        dateType,
        aggregationType,
        startDate: rangeTime[0],
        endDate: rangeTime[1],
        fieldList,
        platform,
      }),
    {
      refreshDeps: [rangeTime, aggregationType, dateType, fieldList, platform],
    },
  );

  const { data: liveAfkTrendData } = useRequest(
    () =>
      GetQualityLiveAfkTrend({
        projectId,
        dateType,
        aggregationType,
        startDate: rangeTime[0],
        endDate: rangeTime[1],
        fieldList,
        platform,
      }),
    {
      refreshDeps: [rangeTime, aggregationType, dateType, fieldList, platform],
    },
  );

  const afkTrendData = [
    {
      type: 'line',
      stack: 'Total',
      name: '严重疑似空播挂播',
      data: liveAfkTrendData?.seriousAfkCountList,
    },
    {
      type: 'line',
      stack: 'Total',
      name: '轻微疑似空播挂播',
      data: liveAfkTrendData?.minorAfkCountList,
    },
  ];

  const unitMap: Record<string, string> = {
    1: '直播场次',
    2: '门店个数',
    3: '账号个数',
  };

  const afkTrend = {
    title: {
      text: '疑似空播挂播趋势',
      textStyle: {
        fontSize: 14,
      },
    },
    tooltip: {
      trigger: 'axis',
      formatter: function (params: any) {
        const relVal = `${params[0].name}     单位:${unitMap[aggregationType]}`;
        let tooltipText = '<div style="text-align: left;">';
        for (let i = 0, l = params.length; i < l; i++) {
          tooltipText += `
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <div>${params[i].marker} ${params[i].seriesName}</div>
            &nbsp;&nbsp;<div style="color: #666; font-weight: 900; font-size: 14px;">${params[i].value}</div>
          </div>
        `;
        }
        return relVal + tooltipText;
      },
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: xAxisData,
      axisLabel: {
        show: true,
        fontSize: 12,
        interval: 0,
        rotate: xAxisData.length >= 15 ? -45 : 0,
      },
    },
    yAxis: {
      type: 'value',
    },
    legend: {
      right: '4%',
      icon: 'circle',
      textStyle: {
        color: 'gray',
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      containLabel: true,
    },
    series: afkTrendData,
  };

  const { data: afkAggregationData } = useRequest(
    () =>
      GetQualityLiveAfkAggregation({
        projectId,
        dateType,
        aggregationType,
        startDate: rangeTime[0],
        endDate: rangeTime[1],
        fieldList,
        fieldId,
        afkLevel,
        platform,
      }),
    {
      refreshDeps: [rangeTime, aggregationType, dateType, fieldList, fieldId, afkLevel, platform],
      ready: !!fieldId,
    },
  );

  const pieUnitMap: Record<string, string> = {
    1: '场直播',
    2: '个门店',
    3: '个账号',
  };

  const pieChartAfk = {
    tooltip: {
      trigger: 'item',
      valueFormatter: function (value: any) {
        return `${value}${pieUnitMap[aggregationType]}`;
      },
    },
    title: {
      text: '查看组织架构分布',
      textStyle: {
        fontSize: 14,
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      containLabel: true,
    },
    series: [
      {
        type: 'pie',
        radius: ['50%', '80%'],
        color: pieColor,
        label: {
          formatter: '{b|{b}}  {d|{d}%}',
          lineHeight: 15,
          rich: {
            b: {
              color: '#95979C',
              fontSize: 14,
            },
            d: {
              color: isDarkMode ? '#E6E8EC' : '#0E1015',
              fontSize: 16,
            },
          },
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 18,
            fontWeight: 'bold',
          },
        },
        data: slicePieData(afkAggregationData),
      },
    ],
  };

  const teamDepthOptions = () => {
    const options = teamDepthData?.map((item) => ({
      value: item.value,
      label: `按${item.name}`,
    }));
    return fieldValue ? options?.filter((item) => item.value !== depthId) : options;
  };

  useEffect(() => {
    const firstSelectValue = teamDepthOptions()?.[0]?.value;
    setFieldId(Number(firstSelectValue));
  }, [fieldValue]);

  const renderInfoCards = (
    aggregationType: number,
    isLoading: boolean,
    data?: QualityLiveAfkOverview,
  ) => {
    let text = '';

    switch (aggregationType) {
      case 1:
        text = '直播场次';
        break;
      case 2:
        text = '门店数';
        break;
      case 3:
        text = '账号数';
        break;
      default:
        return null;
    }

    return (
      <Flex gap={20} style={{ maxWidth: 800 }}>
        {/* 写行内是因为 darkreader 的问题 不要改 */}
        <InfoCard style={{ backgroundColor: '#ffffff' }}>
          <div className="title">
            全部疑似空播挂播{text}
            <Tooltip
              title={`统计总数据和占比,例如: 全部疑似空播挂播${text}占比=全部疑似空播挂播${text}/总${text}。`}
            >
              <InfoCircleOutlined />
            </Tooltip>
          </div>
          {isLoading ? (
            <Skeleton active paragraph={{ rows: 1 }} />
          ) : (
            <>
              <div className="number">{data?.afkCount}</div>
              <div className="rate">
                <span className="rate-title">占比</span>
                <span className="rate-count">{data?.afkCountRate}%</span>
              </div>
            </>
          )}
        </InfoCard>
        <InfoCard style={{ backgroundColor: '#ffffff' }}>
          <div className="title">
            严重空播挂播{text}
            <Tooltip
              title={`统计总数据和占比,例如: 严重疑似空播挂播${text}占比=严重疑似空播挂播${text}/总${text}。`}
            >
              <InfoCircleOutlined />
            </Tooltip>
          </div>
          {isLoading ? (
            <Skeleton active paragraph={{ rows: 1 }} />
          ) : (
            <>
              <div className="number">{data?.seriousAfkCount}</div>
              <div className="rate">
                <span className="rate-title">占比</span>
                <span className="rate-count">{data?.seriousAfkCountRate}%</span>
              </div>
            </>
          )}
        </InfoCard>
        <InfoCard style={{ backgroundColor: '#ffffff' }}>
          <div className="title">
            轻微空播挂播{text}
            <Tooltip
              title={`统计总数据和占比,例如: 轻微疑似空播挂播${text}占比=轻微疑似空播挂播${text}/总${text}。`}
            >
              <InfoCircleOutlined />
            </Tooltip>
          </div>
          {isLoading ? (
            <Skeleton active paragraph={{ rows: 1 }} />
          ) : (
            <>
              <div className="number">{data?.minorAfkCount}</div>
              <div className="rate">
                <span className="rate-title">占比</span>
                <span className="rate-count">{data?.minorAfkCountRate}%</span>
              </div>
            </>
          )}
        </InfoCard>
      </Flex>
    );
  };

  return (
    <>
      <h3>疑似空播挂播分布</h3>
      <ProCard direction="column" style={{ backgroundColor: '#fafcff', marginBottom: 20 }}>
        {renderInfoCards(aggregationType, liveAfkOverViewDataLoading, liveAfkOverViewData)}
        {xAxisData.length > 1 && (
          <ReactECharts
            option={afkTrend}
            notMerge={true}
            style={{ width: '100%', height: 500, marginTop: 10 }}
          />
        )}
        <ProCard
          ghost
          extra={
            <Space>
              <Select
                style={{ width: 200 }}
                value={fieldId?.toString()}
                onChange={(value) => {
                  setFieldId(Number(value));
                }}
                options={teamDepthOptions()}
              />
              <Select
                style={{ width: 200 }}
                value={afkLevel}
                onChange={(value) => {
                  setAfkLevel(value ? Number(value) : null);
                }}
                options={[
                  { value: null, label: '全部疑似空播挂播' },
                  { value: 1, label: '严重疑似空播挂播' },
                  { value: 2, label: '轻微疑似空播挂播' },
                ]}
              />
            </Space>
          }
        >
          <div style={{ height: 450, overflow: 'hidden' }}>
            <ReactECharts
              option={pieChartAfk}
              notMerge={true}
              style={{ width: '100%', height: '90%' }}
            />
          </div>
        </ProCard>
      </ProCard>
    </>
  );
};

export default AfkDistribution;
