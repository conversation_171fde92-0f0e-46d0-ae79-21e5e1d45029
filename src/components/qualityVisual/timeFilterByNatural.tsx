import { getTimeByTypeSimple } from '@/utils/time';
import { DatePicker, Radio, RadioChangeEvent, Space } from 'antd';
import { DatePickerProps, RangePickerProps } from 'antd/es/date-picker';
import dayjs from 'dayjs';

const { RangePicker } = DatePicker;

type TimeFilterByNaturalProps = {
  rangeTime: string[];
  setRangeTime: React.Dispatch<React.SetStateAction<string[]>>;
  dateType: number;
  setDataType: React.Dispatch<React.SetStateAction<number>>;
  maxDays?: number;
  disabledDate?: DatePickerProps['disabledDate'];
};

// 与TimeFilter组件不同的是TimeFilter组件是今日为周一的话选按本周为 周一那一天
const TimeFilterByNatural = (props: TimeFilterByNaturalProps) => {
  const { rangeTime, setRangeTime, dateType, setDataType, maxDays = 31 } = props;

  const onChange = (e: RadioChangeEvent) => {
    const value = e.target.value;
    setDataType(value);
    const dateTypeMap: Record<number, number> = {
      1: 1,
      2: 7,
      3: 30,
    };
    const range = getTimeByTypeSimple(dateTypeMap[value]);
    setRangeTime(range);
  };

  const onChangeRange = (date: RangePickerProps['value'], dateString: string[]) => {
    setRangeTime(dateString);
    setDataType(4);
  };

  const disabledDate: DatePickerProps['disabledDate'] = (current, { from }) => {
    if (from) {
      // 检查选择的日期范围是否超过 **maxDays** 天
      const diffInDays = Math.abs(current.diff(from, 'days'));
      if (diffInDays >= maxDays) {
        return true;
      }
    }

    // 检查是否选择了今天以及今天之后的日期
    const today = dayjs().startOf('day');
    return current && current >= today;
  };

  return (
    <Space>
      <Radio.Group
        value={dateType}
        onChange={onChange}
        buttonStyle="solid"
        style={{ whiteSpace: 'nowrap' }}
      >
        <Radio.Button value={1}>近一天</Radio.Button>
        <Radio.Button value={2}>近一周</Radio.Button>
        <Radio.Button value={3}>近一个月</Radio.Button>
      </Radio.Group>
      <RangePicker
        onChange={onChangeRange}
        disabledDate={disabledDate}
        allowClear={false}
        defaultValue={[dayjs(rangeTime[0]), dayjs(rangeTime[1])]}
        value={[dayjs(rangeTime[0]), dayjs(rangeTime[1])]}
      />
    </Space>
  );
};

export default TimeFilterByNatural;
