import {
  GetQualityLiveMonitorOverview,
  GetQualityLiveMonitorWordAggregation,
  GetQualityLiveMonitorWrodCount,
  GetQualityLiveMonitorWrodTrend,
  GetQualityPostMonitorOverview,
  GetQualityPostMonitorWordAggregation,
  GetQualityPostMonitorWrodCount,
  GetQualityPostMonitorWrodTrend,
} from '@/services/quality-visualization';
import { GetTeamDepthSelect } from '@/services/team';
import { slicePieData } from '@/utils';
import { lineColor, pieColor } from '@/utils/commonStyle';
import { renderXAxis } from '@/utils/time';
import { InfoCircleOutlined } from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Popover, Select } from 'antd';
import { ECharts } from 'echarts';
import ReactECharts from 'echarts-for-react';
import { useEffect, useRef, useState } from 'react';
import TourGif from '@/assets/tour-gif.gif';
import { useAtomValue } from 'jotai';
import { selectPlatformAtom } from '@/pages/ProjectHome/atom';
import { useDarkMode } from '@/hooks/useDarkMode';

type DataItem = {
  value: number;
  groupId: string;
};

type PolymerizatByQualityTypeProps = {
  rangeTime: string[];
  aggregationType: number;
  projectId?: string;
  dateType: number;
  fieldList?: {
    fieldId?: number;
    value?: string;
  }[];
  type: 'live' | 'post';
  fieldValue?: string;
  depthId?: string;
};

const PolymerizaByQualityType = (props: PolymerizatByQualityTypeProps) => {
  const { rangeTime, aggregationType, projectId, dateType, fieldList, type, fieldValue, depthId } =
    props;
  const xAxisData = renderXAxis(rangeTime);
  const numIllegalEchartsRef = useRef<ECharts>();
  const [fieldId, setFieldId] = useState<number | undefined>(undefined);
  const [monitoringWordId, setMonitoringWordId] = useState<number | undefined>(undefined);
  const [monitorWord, setMonitorWord] = useState<string | undefined>(undefined);
  const platform = useAtomValue(selectPlatformAtom);
  const { isDarkMode } = useDarkMode();

  const { data: teamDepthData } = useRequest(() => GetTeamDepthSelect({ projectId }), {
    onSuccess: (data) => {
      const res = data?.[0].value;
      setFieldId(Number(res));
    },
  });

  const { data: monitorWrodTrendData } = useRequest(
    () => {
      const requestFn =
        type === 'live' ? GetQualityLiveMonitorWrodTrend : GetQualityPostMonitorWrodTrend;
      return requestFn({
        projectId,
        dateType,
        aggregationType,
        startDate: rangeTime[0],
        endDate: rangeTime[1],
        fieldList,
        platform,
      });
    },
    {
      refreshDeps: [rangeTime, aggregationType, dateType, fieldList, platform],
    },
  );

  const trendIllegalWordsData = monitorWrodTrendData?.map((item, index) => {
    return {
      color: lineColor[index],
      name: item.name,
      type: 'line',
      data: item.countList,
    };
  });

  const trendUnitMap: Record<string, string> = {
    1: type === 'live' ? '直播场次' : '作品数量',
    2: '门店个数',
    3: '账号个数',
  };

  const trendIllegalWords = {
    title: {
      text: '违规分类趋势',
      textStyle: {
        fontSize: 14,
      },
    },
    tooltip: {
      trigger: 'axis',
      formatter: function (params: any) {
        const relVal = `${params[0].name}     单位:${trendUnitMap[aggregationType]}`;
        let tooltipText = '<div style="text-align: left;">';
        for (let i = 0, l = params.length; i < l; i++) {
          tooltipText += `
          <div style="display: flex; justify-content: space-between; align-items: center;">
            <div>${params[i].marker} ${params[i].seriesName}</div>
            &nbsp;&nbsp;<div style="color: #666; font-weight: 900; font-size: 14px;">${params[i].value}</div>
          </div>
        `;
        }
        return relVal + tooltipText;
      },
    },
    legend: {
      data: monitorWrodTrendData?.map((item) => item.name),
      right: '4%',
      icon: 'circle',
      width: 900,
      formatter: ['{a|{name}}'].join('\n'),
      textStyle: {
        rich: {
          a: {
            width: 55,
            fontSize: 12,
            lineHeight: 12,
          },
        },
        color: 'gray',
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: xAxisData,
      axisLabel: {
        show: true,
        fontSize: 12,
        interval: 0,
        rotate: xAxisData.length >= 15 ? -45 : 0,
      },
    },
    yAxis: {
      type: 'value',
    },
    series: trendIllegalWordsData,
  };

  const { data: monitorWrodCountData, loading: monitorWrodCountDataLoading } = useRequest(
    () => {
      const requestFn =
        type === 'live' ? GetQualityLiveMonitorWrodCount : GetQualityPostMonitorWrodCount;
      return requestFn({
        projectId,
        dateType,
        aggregationType,
        startDate: rangeTime[0],
        endDate: rangeTime[1],
        fieldList,
        platform,
      });
    },
    {
      refreshDeps: [rangeTime, aggregationType, dateType, fieldList, platform],
      onSuccess: (data) => {
        if (data && data?.length > 0) {
          const firstMonitorWord = data?.[0];
          if (firstMonitorWord) {
            setMonitoringWordId(firstMonitorWord.monitoringWordId);
            setMonitorWord(firstMonitorWord.name);
          }
        }
      },
    },
  );

  const illegalCountData = monitorWrodCountData?.map((item) => {
    return {
      value: item.count,
      groupId: item.name,
    };
  });

  const seriesName: Record<string, string> = {
    1: type === 'live' ? '违规直播场次' : '违规作品数',
    2: '违规门店数',
    3: '违规账号数',
  };

  const illegalWord = {
    animationDurationUpdate: 500,
    tooltip: {
      trigger: 'axis',
      showDelay: 0,
      axisPointer: {
        type: 'shadow',
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: [
      {
        type: 'category',
        data: monitorWrodCountData?.map((item) => item.name),
        axisTick: {
          alignWithLabel: true,
        },
        axisLabel: {
          show: true,
          fontSize: 12,
          interval: 0,
          rotate: monitorWrodCountData && monitorWrodCountData?.length >= 15 ? -45 : 0,
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
      },
    ],
    series: [
      {
        type: 'bar',
        name: seriesName[aggregationType],
        id: 'illegalWord',
        barWidth: 50,
        data: illegalCountData as DataItem[],
        universalTransition: {
          enabled: true,
          divideShape: 'clone',
        },
      },
    ],
    graphic: {
      type: 'text', // 类型：文本
      left: 'center',
      top: 'middle',
      silent: true, // 不响应事件
      invisible: illegalCountData && illegalCountData?.length > 0, // 有数据就隐藏
      style: {
        fill: '#9d9d9d',
        fontWeight: 'bold',
        text: '暂无数据',
        fontFamily: 'Microsoft YaHei',
        fontSize: '1.5625rem',
      },
    },
  };

  // 获取违规词下钻数据
  const { run: getDrillData } = useRequest(
    (projectId, dateType, aggregationType, rangeTime, fieldList, monitoringWordId) => {
      const requestFn =
        type === 'live' ? GetQualityLiveMonitorOverview : GetQualityPostMonitorOverview;
      return requestFn({
        projectId,
        dateType,
        aggregationType,
        startDate: rangeTime[0],
        endDate: rangeTime[1],
        fieldList,
        monitoringWordId,
        platform,
      });
    },
    {
      manual: true,
    },
  );

  useEffect(() => {
    if (numIllegalEchartsRef.current) {
      const echart = numIllegalEchartsRef.current;
      if (!echart) return;
      echart.getZr()?.off('click');
      echart?.getZr()?.on('click', (param) => {
        // 已经处于下钻柱状图后不处理点击事件后续逻辑了
        const isSubEchart = echart
          .getOption()
          // @ts-ignore
          .title?.some((item) => item.text === '违规词分类数量');
        if (isSubEchart) return;
        // 获取 点击的 触发点像素坐标
        const pointInPixel = [param.offsetX, param.offsetY];
        // 判断给定的点是否在指定的坐标系或者系列上
        // @ts-ignore
        if (echart.containPixel('grid', pointInPixel)) {
          // 获取到点击的 x轴 下标  转换为逻辑坐标
          const xIndex = echart.convertFromPixel({ seriesIndex: 0 }, pointInPixel)[0];
          const xData = illegalWord.xAxis[0].data?.[xIndex];
          const findMonitorWord = monitorWrodCountData?.find((item) => item.name === xData);
          if (!findMonitorWord) return;
          setMonitorWord(findMonitorWord.name);
          setMonitoringWordId(findMonitorWord.monitoringWordId);
          getDrillData(
            projectId,
            dateType,
            aggregationType,
            rangeTime,
            fieldList,
            findMonitorWord?.monitoringWordId,
          ).then((drillData) => {
            const subData = {
              dataGroupId: xData,
              data: drillData?.map((item) => [item.monitoringWord, Number(item.count)]) || [],
            };
            if (!subData) {
              return;
            }

            const newOptions = {
              title: {
                text: '违规词分类数量',
                textStyle: {
                  fontSize: 14,
                },
              },
              xAxis: {
                type: 'category',
                data: subData.data.map(function (item) {
                  return item[0];
                }),
                axisLabel: {
                  show: true,
                  fontSize: 12,
                  interval: 0,
                  rotate: subData.data.length >= 15 ? -45 : 0,
                },
              },
              animationDurationUpdate: 500,
              tooltip: {
                trigger: 'axis',
                showDelay: 0,
                axisPointer: {
                  type: 'shadow',
                },
              },
              yAxis: {
                type: 'value',
              },
              grid: {
                left: '3%',
                right: '4%',
                bottom: '3%',
                containLabel: true,
              },
              series: [
                {
                  type: 'bar',
                  id: 'illegalWord',
                  dataGroupId: subData.dataGroupId,
                  data: subData.data.map((item) => item[1]),
                  universalTransition: {
                    enabled: true,
                    divideShape: 'clone',
                  },
                },
              ],
              graphic: [
                {
                  type: 'text',
                  left: 5,
                  top: 50,
                  style: {
                    text: '返回',
                    fontSize: 12,
                    fontWeight: '500',
                    fill: isDarkMode ? '#E6E8EC' : '#0E1015',
                    stroke: isDarkMode ? '#E6E8EC' : '#0E1015',
                  },
                  onclick: () => {
                    numIllegalEchartsRef.current?.clear();
                    // @ts-ignore
                    numIllegalEchartsRef.current?.setOption(illegalWord, true);
                  },
                },
                {
                  type: 'text', // 类型：文本
                  left: 'center',
                  top: 'middle',
                  id: 'subNoneTip',
                  silent: true, // 不响应事件
                  invisible: subData && subData.data?.length > 0, // 有数据就隐藏
                  style: {
                    fill: '#9d9d9d',
                    fontWeight: 'bold',
                    text: '此类别无违规词呈现',
                    fontFamily: 'Microsoft YaHei',
                    fontSize: '1.5625rem',
                  },
                },
              ],
            };

            numIllegalEchartsRef.current?.clear();
            // @ts-ignore
            numIllegalEchartsRef.current?.setOption(newOptions, true);
          });
        }
      });
    }
  }, [numIllegalEchartsRef.current, monitorWrodCountData]);

  const pieUnitMap: Record<string, string> = {
    1: type === 'live' ? '场直播' : '个作品',
    2: '个门店',
    3: '个账号',
  };

  const { data: pieData } = useRequest(
    () => {
      const requestFn =
        type === 'live'
          ? GetQualityLiveMonitorWordAggregation
          : GetQualityPostMonitorWordAggregation;
      if (!monitoringWordId) return;
      return requestFn({
        projectId,
        dateType,
        aggregationType,
        startDate: rangeTime[0],
        endDate: rangeTime[1],
        fieldList,
        monitoringWordId,
        fieldId,
        platform,
      });
    },
    {
      refreshDeps: [
        rangeTime,
        aggregationType,
        dateType,
        fieldList,
        monitoringWordId,
        fieldId,
        platform,
      ],
      ready: !!monitoringWordId && !!fieldId,
    },
  );

  // 类别变更把这个下钻后的饼图数据清空
  useEffect(() => {
    setMonitoringWordId(undefined);
  }, [aggregationType]);

  const monitorWordAggregationPie = {
    title: {
      text: '按分类查看组织架构分布{b|(点击违规分类柱状图切换类别查看)}',
      textStyle: {
        fontSize: 14,
        rich: {
          b: {
            color: '#7e8da8',
          },
        },
      },
      subtext:
        monitorWord && pieData && pieData?.length > 0 ? `当前类别: ${monitorWord || ''}` : null,
      subtextStyle: {
        color: '#335ff6',
        fontSize: 13,
        fontWeight: 'bolder',
      },
    },
    tooltip: {
      trigger: 'item',
      valueFormatter: function (value: any) {
        return `${value}${pieUnitMap[aggregationType]}`;
      },
    },
    series: [
      {
        type: 'pie',
        radius: ['50%', '80%'],
        color: pieColor,
        label: {
          formatter: '{b|{b}}  {d|{d}%}',
          lineHeight: 15,
          rich: {
            b: {
              color: '#95979C',
              fontSize: 14,
            },
            d: {
              color: isDarkMode ? '#E6E8EC' : '#0E1015',
              fontSize: 16,
            },
          },
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 18,
            fontWeight: 'bold',
          },
        },
        data: slicePieData(pieData),
      },
    ],
  };

  const teamDepthOptions = () => {
    const options = teamDepthData?.map((item) => ({
      value: item.value,
      label: `按${item.name}`,
    }));
    return fieldValue ? options?.filter((item) => item.value !== depthId) : options;
  };

  useEffect(() => {
    const firstSelectValue = teamDepthOptions()?.[0]?.value;
    setFieldId(Number(firstSelectValue));
  }, [fieldValue]);

  useEffect(() => {
    if (!monitorWrodCountDataLoading) {
      numIllegalEchartsRef.current?.hideLoading();
    }
  }, [monitorWrodCountDataLoading]);

  return (
    <>
      <h3>按质检类型聚合分布 </h3>
      <ProCard direction="column" style={{ backgroundColor: '#fafcff', marginBottom: 20 }}>
        {xAxisData.length > 1 && (
          <ProCard ghost style={{ height: 500 }}>
            <ReactECharts
              option={trendIllegalWords}
              notMerge={true}
              style={{ width: '100%', height: '90%' }}
            />
          </ProCard>
        )}
        <div>
          <span style={{ fontWeight: 500, marginLeft: 5 }}>违规分类数量</span>
          <span style={{ fontSize: 12, color: '#7e8da8' }}>(点击查看下钻数据)</span>
          <Popover content={<img src={TourGif} width={800} height={400} />}>
            <InfoCircleOutlined style={{ marginLeft: 5, verticalAlign: -3 }} />
          </Popover>
        </div>
        <ProCard ghost style={{ height: 500 }}>
          <ReactECharts
            option={illegalWord}
            notMerge={true}
            showLoading={true}
            style={{ width: '100%', height: '90%' }}
            onChartReady={(chartInstance) => {
              chartInstance.clear();
              numIllegalEchartsRef.current = chartInstance;
            }}
          />
        </ProCard>

        <ProCard
          ghost
          headStyle={{ paddingLeft: 30 }}
          extra={
            <Select
              style={{ width: 200 }}
              value={fieldId?.toString()}
              onChange={(value) => {
                setFieldId(Number(value));
              }}
              options={teamDepthOptions()}
            />
          }
        >
          <div style={{ height: 450, width: '100%', overflow: 'hidden' }}>
            <ReactECharts
              option={monitorWordAggregationPie}
              notMerge={true}
              style={{ width: '100%', height: '90%' }}
            />
          </div>
        </ProCard>
      </ProCard>
    </>
  );
};

export default PolymerizaByQualityType;
