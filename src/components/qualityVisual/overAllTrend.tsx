import { selectPlatformAtom } from '@/pages/ProjectHome/atom';
import {
  GetQualityLiveOverview,
  GetQualityLiveOverviewTrend,
  GetQualityPostOverview,
  GetQualityPostOverviewTrend,
} from '@/services/quality-visualization';
import { InfoCard, renderMom } from '@/utils/commonStyle';
import { renderXAxis } from '@/utils/time';
import { ProCard } from '@ant-design/pro-components';
import { useRequest } from '@umijs/max';
import { Flex, Skeleton } from 'antd';
import ReactECharts from 'echarts-for-react';
import { useAtomValue } from 'jotai';

type OverAllTrendProps = {
  rangeTime: string[];
  aggregationType: number;
  projectId?: string;
  dateType: number;
  fieldList?: {
    fieldId?: number;
    value?: string;
  }[];
  type: 'live' | 'post';
};

const OverAllTrend = (props: OverAllTrendProps) => {
  const { rangeTime, aggregationType, projectId, dateType, fieldList, type } = props;
  const xAxisData = renderXAxis(rangeTime);
  const platform = useAtomValue(selectPlatformAtom);

  const { data: overViewData, loading: overViewDataLoading } = useRequest(
    () => {
      const requestFn = type === 'live' ? GetQualityLiveOverview : GetQualityPostOverview;
      return requestFn({
        projectId,
        dateType,
        aggregationType,
        startDate: rangeTime[0],
        endDate: rangeTime[1],
        fieldList,
        platform,
      });
    },
    {
      refreshDeps: [rangeTime, aggregationType, dateType, fieldList, platform],
    },
  );

  const { data: overViewTrendData } = useRequest(
    () => {
      const requestFn = type === 'live' ? GetQualityLiveOverviewTrend : GetQualityPostOverviewTrend;
      return requestFn({
        projectId,
        dateType,
        aggregationType,
        startDate: rangeTime[0],
        endDate: rangeTime[1],
        fieldList,
        platform,
      });
    },
    {
      refreshDeps: [rangeTime, aggregationType, dateType, fieldList, platform],
    },
  );

  const seriesName: Record<string, string> = {
    1: type === 'live' ? '违规直播场次' : '违规作品数',
    2: '违规门店数',
    3: '违规账号数',
  };

  const option = {
    title: {
      text: '所有违规整体趋势',
      textStyle: {
        fontSize: 14,
      },
    },
    tooltip: {
      trigger: 'axis',
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: xAxisData,
      axisLabel: {
        show: true,
        fontSize: 12,
        interval: 0,
        rotate: xAxisData.length >= 15 ? -45 : 0,
      },
    },
    yAxis: {
      type: 'value',
    },
    legend: {
      right: '4%',
      icon: 'circle',
      textStyle: {
        color: 'gray',
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      containLabel: true,
    },
    series: [
      {
        data: overViewTrendData?.map((item) => item.count),
        type: 'line',
        name: seriesName[aggregationType],
        emphasis: {
          focus: 'series',
        },
      },
    ],
  };

  const renderInfoCards = (aggregationType: number) => {
    let title1 = '';
    let title2 = '';
    switch (aggregationType) {
      case 1:
        title1 = type === 'live' ? '总直播场次' : '总作品数';
        title2 = type === 'live' ? '违规直播场次' : '违规作品数';
        break;
      case 2:
        title1 = '总门店数';
        title2 = '违规门店数';
        break;
      case 3:
        title1 = '总账号数';
        title2 = '违规账号数';
        break;
      default:
        return null;
    }

    return (
      <>
        <InfoCard style={{ backgroundColor: '#ffffff' }}>
          <div className="title">{title1}</div>
          {overViewDataLoading ? (
            <Skeleton.Input size="small" active />
          ) : (
            <div className="number">{overViewData?.aggregationCount}</div>
          )}
        </InfoCard>
        <InfoCard style={{ backgroundColor: '#ffffff' }}>
          <div className="title">{title2}</div>
          {overViewDataLoading ? (
            <Skeleton.Input size="small" active />
          ) : (
            <>
              <div className="number">{overViewData?.violationAggregationCount}</div>
              <div>{renderMom(overViewData?.violationAggregationCountMom, '环比')}</div>
            </>
          )}
        </InfoCard>
      </>
    );
  };

  return (
    <>
      <h3>整体情况</h3>
      <ProCard style={{ backgroundColor: '#fafcff', marginBottom: 20 }}>
        <Flex gap={20} style={{ maxWidth: 500 }}>
          {renderInfoCards(aggregationType)}
        </Flex>
        {xAxisData.length > 1 && (
          <ReactECharts
            option={option}
            notMerge={true}
            style={{ width: '100%', height: 500, marginTop: 10 }}
          />
        )}
      </ProCard>
    </>
  );
};

export default OverAllTrend;
