import Logo from '@/assets/logo.png';
import { Layout } from 'antd';
import AvatarDropdown from './RightContent/AvatarDropdown';

const { Header } = Layout;

const CommonHeader = () => {
  return (
    <Header
      style={{
        display: 'flex',
        height: '56px',
        alignItems: 'center',
        backgroundColor: '#121212',
        padding: 0,
      }}
    >
      <img
        alt="logo"
        src={Logo}
        width={30}
        height={30}
        style={{ marginLeft: '32px', marginTop: '5px' }}
      />
      <span
        style={{
          marginLeft: '5px',
          fontSize: '18px',
          fontWeight: '400',
          color: '#FFF',
          fontFamily: 'OPPOSans',
        }}
      >
        新媒体管理监控系统
      </span>
      <AvatarDropdown />
    </Header>
  );
};

export default CommonHeader;
