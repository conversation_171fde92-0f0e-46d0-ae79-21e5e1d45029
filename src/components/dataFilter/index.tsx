import { initDataFilter } from '@/utils/common';
import useSetState, { SetState } from 'ahooks/lib/useSetState';
import { Button, Flex, InputNumber, Popover, Switch } from 'antd';
import { Filter } from 'lucide-react';
import { useEffect } from 'react';

type DataFilterState = {
  videoFilter: boolean;
  liveFilter: boolean;
  postPlayCount: number | null;
  liveDuration: number | null;
  postDuration?: number;
  postDurationFilter: boolean;
};

type DataFilterProps = {
  dataFilterState: DataFilterState;
  setDataFilterState: SetState<DataFilterState>;
  type?: 'post' | 'live';
  reset?: boolean;
};

const DataFilter = (props: DataFilterProps) => {
  const { dataFilterState, setDataFilterState, type } = props;
  // 直接把dataFilterState赋初值给useSetState回导致记录上一次的状态 导致重置按钮的清除失效了
  const [innerDataFilterState, setInnerDataFilterStat] =
    useSetState<DataFilterState>(initDataFilter);
  const { videoFilter, liveFilter, postPlayCount, liveDuration, postDuration, postDurationFilter } =
    innerDataFilterState;

  useEffect(() => {
    setInnerDataFilterStat(dataFilterState);
  }, [dataFilterState]);

  const content = () => {
    const videoFilterContent = () => (
      <Flex gap={10} align="center">
        <span>短视频播放量≥</span>
        <InputNumber
          style={{ width: '100px', height: '30px', borderRadius: '3px' }}
          disabled={!videoFilter}
          value={postPlayCount}
          min={0}
          precision={0}
          onChange={(value: number | null) => setInnerDataFilterStat({ postPlayCount: value })}
        />
        次
        <Switch
          checkedChildren="开"
          unCheckedChildren="关"
          checked={videoFilter}
          onChange={() => setInnerDataFilterStat({ videoFilter: !videoFilter })}
        />
      </Flex>
    );

    const postFilterContent = () => (
      <Flex gap={10} align="center">
        <span>作品时长≥</span>
        <InputNumber
          style={{ width: '100px', height: '30px', borderRadius: '3px' }}
          disabled={!postDurationFilter}
          value={postDuration}
          min={0}
          step={0.1}
          precision={1}
          onChange={(value: number | null) => setInnerDataFilterStat({ postDuration: value || 0 })}
        />
        分钟
        <Switch
          checkedChildren="开"
          unCheckedChildren="关"
          checked={postDurationFilter}
          onChange={() => setInnerDataFilterStat({ postDurationFilter: !postDurationFilter })}
        />
      </Flex>
    );

    const liveFilterContent = () => (
      <Flex gap={10} align="center">
        <span>直播时长≥</span>
        <InputNumber
          style={{ width: '100px', height: '30px', borderRadius: '3px' }}
          disabled={!liveFilter}
          value={liveDuration}
          min={0}
          precision={0}
          onChange={(value: number | null) => setInnerDataFilterStat({ liveDuration: value })}
        />
        分钟
        <Switch
          checkedChildren="开"
          unCheckedChildren="关"
          checked={liveFilter}
          onChange={() => setInnerDataFilterStat({ liveFilter: !liveFilter })}
        />
      </Flex>
    );

    if (type === 'post') {
      return (
        <Flex gap={5} vertical>
          {videoFilterContent()}
          {postFilterContent()}
        </Flex>
      );
    } else if (type === 'live') {
      return liveFilterContent();
    } else {
      return (
        <Flex gap={5} vertical>
          {videoFilterContent()}
          {liveFilterContent()}
        </Flex>
      );
    }
  };

  // 关闭了再修改表单的查询参数
  const popoverChange = (open: boolean) => {
    if (!open) {
      setDataFilterState(innerDataFilterState);
    }
  };

  return (
    <Popover
      content={() => content()}
      title="数据条件筛选"
      trigger="click"
      placement="bottom"
      onOpenChange={popoverChange}
    >
      <Button
        onClick={(e) => e.preventDefault()}
        className={videoFilter || liveFilter ? 'custom-button' : ''}
        icon={<Filter size={13} strokeWidth={1.5} />}
      >
        数据条件筛选
      </Button>
    </Popover>
  );
};

export default DataFilter;
