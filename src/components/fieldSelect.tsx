import useProjectId from '@/hooks/useProjectId';
import { GetFieldValue } from '@/services/team';
import { useQuery } from '@umijs/max';
import { Select } from 'antd';
import { useState, useEffect } from 'react';

type FieldSelectProps = {
  fieldId?: string | number;
  name: string;
  value?: any;
  onChange?: (value: any) => void;
};

const FieldSelect = (props: FieldSelectProps) => {
  const [options, setOptions] = useState<{ value: string; label: string }[]>([]);
  const projectId = useProjectId();
  const { fieldId, name, value, onChange } = props;

  const { data: fieldData } = useQuery(
    ['field-value', fieldId, projectId],
    () => GetFieldValue({ fieldId, projectId }),
    {
      enabled: !!fieldId,
      staleTime: Number.POSITIVE_INFINITY,
    },
  );

  useEffect(() => {
    const optionsRes = fieldData?.data?.map((item) => ({ value: item.value, label: item.name }));
    setOptions(optionsRes || []);
  }, [fieldData]);

  const filterOption = (input: string, option?: { label: string; value: string }) =>
    (option?.label ?? '').toLowerCase().includes(input.toLowerCase());

  return (
    <Select
      showSearch
      allowClear={true}
      placeholder={name}
      optionFilterProp="label"
      filterOption={filterOption}
      options={options}
      value={value}
      onChange={onChange}
    />
  );
};

export default FieldSelect;
