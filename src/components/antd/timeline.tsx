import { Timeline as AntdTimeline } from 'antd';
import styled from 'styled-components';

export const Timeline = styled(AntdTimeline)`
  .ant-timeline-item-content {
    color: rgba(255, 255, 255, 0.8);
  }

  .ant-timeline-item-tail {
    border-color: rgba(255, 255, 255, 0.5);
  }

  .ant-timeline-item-head {
    background-color: #0d0d0d;
  }

  .ant-timeline-item-head-blue {
    color: #326cff;
    border-color: #326cff;
  }
  .ant-timeline-item-tail {
    border-color: rgba(192, 196, 205, 0.27);
  }
`;

export const DanmuTimeline = styled(AntdTimeline)`
  .ant-timeline-item-content {
    color: rgba(255, 255, 255, 0.8);
  }

  .ant-timeline-item-tail {
    border-color: rgba(255, 255, 255, 0.5);
  }

  .ant-timeline-item-head {
    background-color: transparent;
  }

  .ant-timeline-item-head-blue {
    color: #326cff;
    border-color: #326cff;
  }
  .ant-timeline-item-tail {
    border-color: rgba(192, 196, 205, 0.27);
  }
`;
