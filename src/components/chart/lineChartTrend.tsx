import { QualityTargetType } from '@/services/constants';
import { TimeLineChartData } from '@/services/quality';
import { echartDefaultColor } from '@/utils/commonStyle';
import { ProCard } from '@ant-design/pro-components';
import dayjs from 'dayjs';
import ReactECharts from 'echarts-for-react';
import { styled } from 'styled-components';

type LineChartTrendProps = {
  lineChartData: TimeLineChartData[];
  showXAxis?: string;
  dataform?: string;
  target?: QualityTargetType;
};

const ProCardLineDark = styled(ProCard)`
  background-color: #1b1d22 !important;

  .ant-pro-card-title {
    color: #fff;
  }
`;

const LineChartTrend = (props: LineChartTrendProps) => {
  const { lineChartData, showXAxis, dataform, target } = props;
  const startTime = dayjs(lineChartData[0]?.time);
  const endTime = dayjs(lineChartData[lineChartData.length - 1]?.time);
  const timeDiffHour = endTime.diff(startTime, 'hour');
  const timeDiffDay = endTime.diff(startTime, 'day');

  const renderSeries = (lineChartData: TimeLineChartData[]) => {
    if (dataform === 'total') {
      return lineChartData.map((item) => item?.trendData?.value);
    } else {
      return lineChartData.map((item) => item?.trendData?.lastValue);
    }
  };

  const options = {
    color: echartDefaultColor,
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          precision: 0, // 省略小数点
        },
      },
    },
    grid: {
      left: '0px',
      right: '2%',
      bottom: '0px',
      top: '2%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: lineChartData.map((item) =>
        target === QualityTargetType.POST ? dayjs(item.time).format('YYYY-MM-DD') : item.time,
      ),
      axisLabel: {
        show: true,
        fontSize: 12,
        interval: 0,
        rotate: lineChartData.length >= 15 ? -45 : 0,
        textStyle: {
          color: 'rgba(255, 255, 255, 0.45)',
        },
        formatter: function (value: string) {
          // 如果间隔时间小于30小时的话显示小时刻度 超过30天不显示x轴名称
          // YYYY-MM-DD HH:mm
          if (timeDiffDay > 30 || lineChartData.length > 50) {
            return '';
          } else if (showXAxis) {
            return dayjs(value).format(showXAxis);
          } else if (timeDiffHour < 30) {
            return dayjs(value).format('DD HH:mm');
          } else {
            return dayjs(value).format('MM-DD');
          }
        },
      },
    },
    yAxis: {
      type: 'value',
      splitLine: {
        lineStyle: {
          color: '#34363b', //线条颜色
        },
      },
      axisLabel: {
        textStyle: {
          color: 'rgba(255, 255, 255, 0.45)',
        },
      },
    },
    series: [
      {
        data: renderSeries(lineChartData),
        type: 'line',
        areaStyle: {
          color: '#74A0FA',
          opacity: 0.25,
        },
      },
    ],
  };

  return (
    <ProCardLineDark style={{ height: '378px', marginTop: '20px' }} ghost>
      <ReactECharts option={options} notMerge={true} style={{ width: '100%', height: '100%' }} />
    </ProCardLineDark>
  );
};

export default LineChartTrend;
