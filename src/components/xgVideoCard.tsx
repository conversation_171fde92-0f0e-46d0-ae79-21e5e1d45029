import { cn } from '@/lib/utils';
import { openNewWindow } from '@/utils/client';
import { CaretRightOutlined, PlayCircleOutlined } from '@ant-design/icons';
import { Button, Image, Popover, Spin, Tag, Modal } from 'antd';
import { <PERSON><PERSON><PERSON> } from 'lucide-react';
import { lazy, Suspense, useCallback, useMemo, useRef, useState } from 'react';
import Player from 'xgplayer';
import 'xgplayer/dist/index.min.css';

type XGVideoCardProps = {
  videoUrl?: string;
  coverUrl?: string;
  width: number;
  height: number;
  videoWidth?: number;
  videoHeight?: number;
  noShowTag?: boolean;
  playableVideoUrl?: string;
  isLive?: boolean;
  type?: 'm3u8' | 'flv' | 'mp4';
  showBottomTag?: boolean;
};

const XGPlayer = lazy(() => import('./xgplayer'));

const XGVideoCard = (props: XGVideoCardProps) => {
  const {
    videoUrl,
    coverUrl,
    videoWidth,
    videoHeight,
    width,
    height,
    noShowTag,
    playableVideoUrl,
    isLive = false,
    type = 'mp4',
    showBottomTag,
  } = props;

  const videoPlayerRef = useRef<Player | null>(null);
  const [open, setOpen] = useState(false);

  const onVideoReady = useCallback((player: Player) => {
    videoPlayerRef.current = player;

    const cfg = localStorage.getItem('video-card-preview-volume');
    if (cfg) {
      try {
        const { volume, muted } = JSON.parse(cfg);
        player.volume = volume;
        player.muted = muted;
      } catch (err) {
        console.log(err);
      }
    }
  }, []);

  const options = useMemo(() => {
    if (!playableVideoUrl) {
      return null;
    }
    return {
      poster: coverUrl, // 封面地址
      width: videoWidth,
      height: videoHeight,
      url: playableVideoUrl,
      videoAttributes: {
        crossOrigin: 'anonymous',
      },
    };
  }, [videoUrl, coverUrl, videoWidth, videoHeight, playableVideoUrl]);

  if (!playableVideoUrl && videoUrl) {
    return (
      <>
        {coverUrl && (
          <Popover
            content="点击播放"
            trigger="hover"
            placement="right"
            onOpenChange={(open) => {
              setOpen(open);
            }}
          >
            <Image
              onClick={() => {
                openNewWindow(videoUrl);
              }}
              src={coverUrl}
              width={width}
              height={height}
              referrerPolicy="no-referrer"
              style={{ objectFit: 'cover', borderRadius: '4px' }}
              preview={false}
            />
          </Popover>
        )}
        {!coverUrl && (
          <Button
            type="dashed"
            onClick={() => {
              openNewWindow(videoUrl);
            }}
          >
            <PlayCircleOutlined />
            播放
          </Button>
        )}
      </>
    );
  }

  return (
    <>
      <Modal
        open={open}
        centered
        width="auto"
        destroyOnHidden
        afterClose={() => {
          videoPlayerRef.current?.pause();
        }}
        onCancel={() => {
          if (videoPlayerRef.current && !open) {
            videoPlayerRef.current?.pause();
            const volume = videoPlayerRef.current.volume;
            const muted = videoPlayerRef.current.muted;
            localStorage.setItem('video-card-preview-volume', JSON.stringify({ volume, muted }));
          }
          setOpen(false);
        }}
        footer={null}
      >
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            marginTop: 30,
            marginBottom: 15,
          }}
        >
          {open && (
            <Suspense fallback={<Spin />}>
              <XGPlayer
                playerOptions={options}
                isLive={isLive}
                type={type}
                onPlayerReady={onVideoReady}
              />
            </Suspense>
          )}
        </div>
      </Modal>

      {coverUrl && (
        <div
          style={{ position: 'relative' }}
          onClick={() => {
            setOpen(true);
            if (videoPlayerRef.current) {
              if (videoPlayerRef.current.paused) {
                videoPlayerRef.current.play();
              } else {
                videoPlayerRef.current.pause();
              }
            }
          }}
        >
          <img
            src={coverUrl}
            width={width}
            height={height}
            style={{
              objectFit: 'cover',
              borderRadius: 4,
              border: showBottomTag && isLive ? '1px solid #F82074' : 'none',
            }}
            referrerPolicy="no-referrer"
          />
          {noShowTag ? null : (
            <Tag
              style={{ position: 'absolute', left: 2, bottom: 2, opacity: 0.78, borderRadius: 1 }}
              color="blue-inverse"
            >
              已保存
            </Tag>
          )}
          {showBottomTag && (
            <div
              className={cn(
                'absolute -bottom-1 left-1/2 z-[1000] flex h-[20px] w-[68px] -translate-x-1/2 transform items-center justify-center rounded px-2 py-1 text-xs',
                {
                  'bg-[#F82074] text-white': isLive,
                  'bg-[#EFF3FB] text-[#5D5F66]': !isLive,
                },
              )}
            >
              {isLive && <Kanban className="mr-1 w-2 rotate-180" />}
              {isLive ? '直播中' : '直播结束'}
            </div>
          )}
          {!!videoUrl && (
            <Button
              type="text"
              size="large"
              shape="circle"
              style={{
                position: 'absolute',
                left: '50%',
                top: '50%',
                color: '#fff',
                transform: 'translate(-50%, -50%)',
                backgroundColor: 'rgba(0,0,0,.5)',
              }}
              icon={<CaretRightOutlined />}
            />
          )}
        </div>
      )}
      {!coverUrl && (
        <Button
          type="dashed"
          onClick={() => {
            setOpen(true);
            videoPlayerRef.current?.play();
            // if (playableVideoUrl) {
            //   openNewWindow(playableVideoUrl);
            // }
          }}
        >
          <PlayCircleOutlined />
          播放
        </Button>
      )}
    </>
  );
};

export default XGVideoCard;
