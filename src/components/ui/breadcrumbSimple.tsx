import React from 'react';
import {
  B<PERSON><PERSON>rumb,
  BreadcrumbList,
  BreadcrumbItem,
  BreadcrumbSeparator,
  BreadcrumbPage,
} from './breadcrumb';

type BreandCrumbSimpleProps = {
  breadcrumbs: string[];
};

const BreadCrumbSimple = (props: BreandCrumbSimpleProps) => {
  const { breadcrumbs } = props;

  return (
    <Breadcrumb className="mb-4">
      <BreadcrumbList>
        {breadcrumbs.map((breadcrumb, index) => (
          <React.Fragment key={index}>
            {index < breadcrumbs.length - 1 ? (
              <BreadcrumbItem>
                <Breadcrumb>{breadcrumb}</Breadcrumb>
              </BreadcrumbItem>
            ) : (
              <BreadcrumbItem>
                <BreadcrumbPage>{breadcrumb}</BreadcrumbPage>
              </BreadcrumbItem>
            )}

            {index < breadcrumbs.length - 1 && <BreadcrumbSeparator />}
          </React.Fragment>
        ))}
      </BreadcrumbList>
    </Breadcrumb>
  );
};

export default BreadCrumbSimple;
