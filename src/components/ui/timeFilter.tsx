import { Tabs, Ta<PERSON>List, TabsTrigger } from './tabs';
import { getTimeByType } from '@/utils/time';
import { DatePicker, Space } from 'antd';
import { RangePickerProps } from 'antd/es/date-picker';
import dayjs, { Dayjs } from 'dayjs';
import weekOfYear from 'dayjs/plugin/weekOfYear';
import { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
dayjs.extend(weekOfYear);

type TimeFilterProps = {
  value?: (string | undefined)[];
  onChange?: (value: (string | undefined)[]) => void;
  showToday?: boolean;
  style?: React.CSSProperties;
  rangeDisable?: (current: Dayjs) => boolean;
};

const { RangePicker } = DatePicker;

const TimeFilter = forwardRef((props: TimeFilterProps, ref) => {
  const { value, onChange, showToday, style, rangeDisable } = props;
  const [radioValue, setRadioValue] = useState<string | undefined>(showToday ? 'today' : 'day');
  const [showRangeTime, setShowRangeTime] = useState<(string | undefined)[]>(
    showToday ? getTimeByType('today') : getTimeByType('day'),
  );

  useEffect(() => {
    if (value) {
      setShowRangeTime(value);

      // 检查 value 是否与任何标签选项匹配
      const tabOptions = ['today', 'day', 'week', 'month'];

      // 如果当前已经选中了某个标签，优先检查当前选中的标签是否匹配
      if (radioValue) {
        const currentTabTimeRange = getTimeByType(radioValue);
        if (value[0] === currentTabTimeRange[0] && value[1] === currentTabTimeRange[1]) {
          return; // 如果当前选中的标签匹配，就保持不变
        }
      }

      // 如果当前标签不匹配，再检查其他标签
      const matchedTab = tabOptions.find((tab) => {
        const timeRange = getTimeByType(tab);
        return value[0] === timeRange[0] && value[1] === timeRange[1];
      });

      // 如果没有匹配项，设置 radioValue 为 undefined
      setRadioValue(matchedTab);
    }
  }, [value, radioValue]);

  const onChangeTabs = (value: string) => {
    setRadioValue(value);
    const range = getTimeByType(value);
    onChange?.(range);
    setShowRangeTime(range);
  };

  const onChangeRange = (date: RangePickerProps['value'], dateString: string[]) => {
    setRadioValue(undefined);
    if (!value) {
      setShowRangeTime(dateString);
    }
    onChange?.(dateString);
  };

  const disabledDate = (current: Dayjs) => {
    if (rangeDisable) {
      return rangeDisable(current);
    } else if (showToday) {
      return current && current > dayjs();
    } else {
      return current && current > dayjs().subtract(1, 'day');
    }
  };

  useImperativeHandle(ref, () => ({
    value: radioValue,
  }));

  const displayRangeTime = value || showRangeTime;

  return (
    <Space style={style}>
      <Tabs value={radioValue || ''} onValueChange={onChangeTabs}>
        <TabsList value={radioValue || ''}>
          {showToday && (
            <TabsTrigger value="today">
              今天{radioValue === 'today' ? ` ${dayjs().format('YYYY-MM-DD')}` : null}
            </TabsTrigger>
          )}
          <TabsTrigger value="day">
            昨日
            {radioValue === 'day' ? ` ${dayjs().subtract(1, 'day').format('YYYY-MM-DD')}` : null}
          </TabsTrigger>
          <TabsTrigger value="week">
            自然周
            {radioValue === 'week'
              ? dayjs().day() === 1
                ? `${dayjs().subtract(1, 'week').year()}年 - 第${dayjs()
                    .subtract(1, 'week')
                    .week()}周`
                : `${dayjs().year()}年 - 第${dayjs().week()}周`
              : null}
          </TabsTrigger>
          <TabsTrigger value="month">
            自然月
            {radioValue === 'month'
              ? dayjs().date() === 1
                ? `${dayjs().subtract(1, 'month').format('YYYY-MM')}`
                : `${dayjs().format('YYYY-MM')}`
              : null}
          </TabsTrigger>
        </TabsList>
      </Tabs>
      <RangePicker
        onChange={onChangeRange}
        disabledDate={disabledDate}
        allowClear={false}
        value={
          displayRangeTime[0] && displayRangeTime[1]
            ? [dayjs(displayRangeTime[0]), dayjs(displayRangeTime[1])]
            : undefined
        }
      />
    </Space>
  );
});

export default TimeFilter;
