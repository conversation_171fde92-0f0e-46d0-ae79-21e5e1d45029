import { CardTrend } from '@/services/data-card';
import { secondToHours } from '@/utils/time';
import ReactECharts from 'echarts-for-react';
import { useEffect, useState } from 'react';
import { echartAreaStyleGradient } from '@/utils/commonStyle';

type DataCardDetailProps = {
  data?: CardTrend;
  isTimeType?: boolean;
  isPercentage?: boolean;
};

// 在 Popover 显示的时候首次渲染获取不到宽高所以显示的是默认的 100px 鼠标 hover 第二次才正确
// https://blog.csdn.net/Machel_/article/details/140470013
const DataCardDetail = (props: DataCardDetailProps) => {
  const [show, setShow] = useState(false);
  const { data, isTimeType, isPercentage } = props;

  const formatData = data?.trendList?.map((item) => {
    if (isTimeType) {
      return secondToHours(Number(item.count));
    } else if (isPercentage) {
      return Number(item.count).toFixed(2); // 后端已经乘100了
    } else {
      return item.count;
    }
  });

  useEffect(() => {
    setTimeout(() => {
      setShow(true);
    }, 0);
  }, []);

  const options = {
    tooltip: {
      trigger: 'axis',
      appendToBody: true,
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985',
          precision: 0, // 省略小数点
        },
      },
    },
    // dataZoom: [
    //   {
    //     id: 'dataZoomX',
    //     type: 'inside',
    //     xAxisIndex: [0],
    //     filterMode: 'filter',
    //     startValue: 0, // 设定初始显示的范围
    //     endValue: 6, // 设定初始显示的范围
    //     zoomOnMouseWheel: false, // 滚轮是否触发缩放
    //     moveOnMouseMove: true, // 鼠标滚轮触发滚动
    //     moveOnMouseWheel: true, // 鼠标移动能触发数据窗口缩放
    //   },
    // ],
    grid: {
      top: '3%',
      left: 1,
      right: '2%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: data?.trendList?.map((item) => item.date),
      axisLabel: {
        interval: 'auto',
      },
      axisTick: {
        interval: 'auto',
      },
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        show: true,
        formatter: function (value: string) {
          if (isPercentage) {
            return value + '%'; // 当 isPercentage 为 true 时，显示百分比
          }
          return value; // 否则显示原始数值
        },
      },
    },
    series: [
      {
        data: formatData,
        type: 'line',
        stack: 'Total',
        symbol: 'none',
        smooth: true,
        areaStyle: echartAreaStyleGradient,
        emphasis: {
          focus: 'series',
        },
      },
    ],
    graphic: {
      type: 'text', // 类型：文本
      left: 'center',
      top: 'middle',
      silent: true, // 不响应事件
      invisible: data?.trendList && data?.trendList?.length > 0, // 有数据就隐藏
      style: {
        fill: '#9d9d9d',
        fontWeight: 'bold',
        text: '暂无数据',
        fontFamily: 'Microsoft YaHei',
        fontSize: '1.5625rem',
      },
    },
  };

  return (
    <div style={{ width: '390px', height: '320px' }}>
      {show && <ReactECharts option={options} notMerge={true} />}
    </div>
  );
};

export default DataCardDetail;
