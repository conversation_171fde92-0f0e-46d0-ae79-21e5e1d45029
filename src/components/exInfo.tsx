import { GetQualityExDetail } from '@/services/quality';
import { useRequest } from '@umijs/max';
import { Timeline, Image } from 'antd';
import styled from 'styled-components';
import FallBackImg from '@/assets/fallback-img.png';
import { groupBy } from 'lodash-es';

const QualityContent = styled.div`
  .highlight {
    color: red;
  }
`;

type ExInfoDetailProps = {
  projectId?: string;
  platform?: number;
  targetId?: number | string;
  targetType?: 1 | 2;
  monitoringNameId?: number;
};

const ExInfoDetail = (props: ExInfoDetailProps) => {
  const { projectId, platform, targetId, targetType, monitoringNameId } = props;

  const { data: exInfoDetail } = useRequest(
    () =>
      GetQualityExDetail({
        projectId,
        platform,
        targetId,
        targetType,
        monitoringNameId,
      }),
    {
      refreshDeps: [projectId, platform, targetId],
    },
  );

  // exInfoDetail可能同时存在图片和文字形态的数据
  const grouped = groupBy(exInfoDetail?.exInfoList, (item) => {
    if (item.qualityRuleType === 4 || item.qualityRuleType === 3) return 'imageType';
    if (item.qualityRuleType === 1 || item.qualityRuleType === 2) return 'textType';
  });

  const exInfoImageType = grouped.imageType || [];
  const exInfoTextType = grouped.textType || [];

  const renderQualityContent = (content: string, mark?: string) => {
    if (!mark) return content;
    const searchRegex = new RegExp(mark, 'gi');
    const showContent = content.replace(searchRegex, '<span class="highlight">$&</span>');
    return showContent;
  };

  const timeLineRender = exInfoTextType?.map((item) => {
    return {
      children: (
        <>
          {item.content && (
            <QualityContent>
              <span style={{ color: '#95979C' }}>{item.exTime}</span>
              <div
                dangerouslySetInnerHTML={{
                  __html: renderQualityContent(item.content, item.mark),
                }}
              />
            </QualityContent>
          )}
        </>
      ),
    };
  });

  return (
    <div className="flex max-h-[350px] w-[500px] flex-col gap-5 overflow-y-auto">
      <div>
        <span className="mr-2 break-words text-sm font-normal leading-5 text-[#989898]">账号:</span>
        <span className="break-words text-sm font-normal leading-5 text-[#0e1015]">
          {exInfoDetail?.nickname}
        </span>
      </div>
      <div>
        <span className="mr-2 break-words text-sm font-normal leading-5 text-[#989898]">
          {targetType === 1 ? '直播' : '作品'}标题:
        </span>
        <span className="break-words text-sm font-normal leading-5 text-[#0e1015]">
          {exInfoDetail?.title}
        </span>
      </div>
      <div style={{ marginTop: '10px' }}>
        {exInfoImageType.length > 0 && (
          <div style={{ marginBottom: '20px' }}>
            <Image.PreviewGroup>
              {exInfoImageType?.map((item) => (
                <Image
                  key={item.picUrl}
                  width={150}
                  height={200}
                  style={{ borderRadius: '4px', objectFit: 'cover' }}
                  src={item.picUrl}
                  fallback={FallBackImg}
                />
              ))}
            </Image.PreviewGroup>
          </div>
        )}
        {exInfoTextType.length > 0 && <Timeline items={timeLineRender} />}
      </div>
    </div>
  );
};

export default ExInfoDetail;
