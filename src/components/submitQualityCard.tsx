import { useSubmitLiveFrameExInfoMutation } from '@/hooks/request/use-live-frame';
import { useSubmitSubtitleExInfoMutation } from '@/hooks/request/use-live-subtitle';
import { QualityTargetType } from '@/services/constants';
import { MonitorItem } from '@/services/quality';
import { ProCardDarkGhost } from '@/utils/commonStyle';
import { EventEmitter } from 'ahooks/lib/useEventEmitter';
import type { RadioChangeEvent } from 'antd';
import { App, Button, Radio, Space } from 'antd';
import { countBy } from 'lodash-es';
import { nanoid } from 'nanoid';
import { useMemo, useState } from 'react';

type SubmitQualityCardProps = {
  style?: React.CSSProperties;
  triggerType: 'subtitle' | 'frame';
  selectItems: any[];
  setSelectItems: (items: any[]) => void;
  data: MonitorItem[] | undefined;
  platform: string | undefined;
  targetId: string | undefined;
  targetType: QualityTargetType;
  projectId?: string;
  postEvent$?: EventEmitter<void>;
};

const generateGroups = (data: any[]) => {
  const sortData = data.sort((a, b) => a.index - b.index);
  let groupId = '';
  const result = [];

  for (let i = 0; i < sortData.length; i++) {
    if (i === 0 || sortData[i].index !== sortData[i - 1].index + 1) {
      groupId = nanoid();
    }

    result.push({ ...sortData[i], groupId });
  }

  return result;
};

const extractTextFromHtml = (htmlContent: string): string => {
  if (!htmlContent) return '';
  // 创建一个临时 div 元素
  const doc = new DOMParser().parseFromString(htmlContent, 'text/html');
  // 获取纯文本内容
  return doc.body.textContent || '';
};

export const SubmitQualityCard = (props: SubmitQualityCardProps) => {
  const { message } = App.useApp();

  const {
    style,
    triggerType,
    data,
    selectItems,
    setSelectItems,
    platform,
    targetId,
    targetType,
    postEvent$,
  } = props;

  const [value, setValue] = useState('');

  const onChange = (e: RadioChangeEvent) => {
    setValue(e.target.value);
  };

  const showData = data?.filter((item) => item.isDisabled === 0);
  const submitSubtitleExInfoMutation = useSubmitSubtitleExInfoMutation();
  const submitLiveFrameExInfoMutation = useSubmitLiveFrameExInfoMutation();

  const frameGroupData = useMemo(() => {
    if (triggerType === 'frame') {
      const groupData = generateGroups(selectItems);
      const count = countBy(groupData, 'groupId');
      return {
        items: groupData,
        count: Object.keys(count).length,
      };
    }
    return {
      items: [],
      count: 0,
    };
  }, [triggerType, selectItems]);

  async function handleClick() {
    if (!value) {
      message.error('请选择违规行为标记');
      return;
    }

    if (triggerType === 'frame') {
      const submitData = frameGroupData.items.map((item: any) => {
        return {
          ...item,
          startTime: item.startTime,
          endTime: item.endTime,
          targetId,
          targetType,
          platform,
          url: item.targetUrl,
          picUrl: item.picUrl,
          groupId: item.groupId,
          //	1:标题文字 2:语音 3:封面 4:视频画面
          ruleType: 4,
          monitoringWordId: value,
        };
      });
      submitLiveFrameExInfoMutation.mutate({
        roomId: targetId,
        platform,
        submitData,
        projectId: props.projectId,
      });
    } else if (triggerType === 'subtitle') {
      const submitData = selectItems.map((item) => {
        return {
          ...item,
          startTime: item.startTime,
          endTime: item.endTime,
          targetId,
          targetType,
          platform,
          content: extractTextFromHtml(
            targetType === QualityTargetType.LIVE ? item.subtitle : item.content,
          ),
          ruleType: 2,
          monitoringWordId: value,
          groupId: nanoid(),
        };
      });
      submitSubtitleExInfoMutation.mutate({
        roomId: targetId,
        platform,
        submitData,
        projectId: props.projectId,
      });
      postEvent$?.emit();
    }

    message.success('提交成功');
    setSelectItems([]);
  }

  return (
    <ProCardDarkGhost title="选择违规行为标记" bordered style={{ width: '500px', ...style }}>
      <div>
        当前选中{triggerType === 'subtitle' ? '字幕' : '连续画面片段'}：
        {triggerType === 'subtitle' ? selectItems.length : frameGroupData.count}
      </div>

      <Radio.Group
        style={{ marginTop: '10px', minHeight: '50px' }}
        value={value}
        onChange={onChange}
      >
        {showData?.map((item) => (
          <Radio key={item.id} value={item.id}>
            {item.name}
          </Radio>
        ))}
      </Radio.Group>
      <Space style={{ float: 'right', marginTop: '10px' }}>
        <Button
          onClick={() => {
            setSelectItems([]);
          }}
        >
          取消
        </Button>
        <Button type="primary" onClick={handleClick}>
          确认
        </Button>
      </Space>
    </ProCardDarkGhost>
  );
};
