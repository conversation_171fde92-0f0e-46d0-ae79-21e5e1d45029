import { ProjectTeamFieldItem } from '@/services/typings';
import FieldSelect from './fieldSelect';
import { BizType } from '@/utils/common';

// 生成带有用户自定义列头的列和所有列数组, fieldIdArr收集这些自定义维度的id
export async function renderCustomColumns(
  fieldList?: ProjectTeamFieldItem[] | null,
  isTeamSetting?: boolean,
) {
  const fieldIdArr: string[] = [];
  let customColumns = [];

  if (!fieldList) {
    return { customColumns: [], fieldIdArr };
  }

  const defaultColumns = (item: ProjectTeamFieldItem) => {
    return {
      dataIndex: item.id,
      hideInTable: !item.showFlag,
      hideInSearch: !item.showFlag || item.bizType !== BizType.ORDINARY,
      align: 'left',
      fixed: isTeamSetting && item.bizType === 1 ? 'left' : undefined, // 团队列表这个表格特殊
      ellipsis: isTeamSetting && item.bizType === 1 ? 'true' : undefined,
      formItemProps: {
        label: null,
      },
      minWidth: item.fieldName.length * 25,
      renderFormItem: () => {
        return <FieldSelect fieldId={item.id} name={item.fieldName} />;
      },
    };
  };

  // 过滤 bizType: 2 且 depth 递增的字段
  const bizType2Fields = fieldList?.filter((item) => item.bizType === 2 && item.depth > 0);

  // 过滤 bizType: 1 且 depth: 0 的字段
  const bizType1Depth0Fields = fieldList?.filter((item) => item.bizType === 1 && item.depth === 0);

  // 千变万化的表格列
  const fieldsToInclude = fieldList?.filter((item) => {
    if (isTeamSetting) {
      return (item.bizType === 0 && item.depth === 0) || (item.bizType === 1 && item.depth === 0);
    } else {
      return item.bizType === 0 && item.depth === 0;
    }
  });

  // 创建团队信息列
  const teamInfoColumn = {
    dataIndex: 'teamInfo',
    title: '团队信息',
    minWidth: 150,

    // @ts-ignore
    render: (_, record) => {
      return (
        <div style={{ minWidth: 150 }}>
          {bizType2Fields
            .map((item) => item.id)
            .map((id) => record[id])
            .join('-')}
          {'-'}
          {bizType1Depth0Fields.map((item) => record[item.id])}
        </div>
      );
    },
    align: 'left',
    hideInSearch: true,
  };

  customColumns = await Promise.allSettled(
    fieldsToInclude?.map(async (item: ProjectTeamFieldItem) => {
      fieldIdArr.push(String(item.id));
      // 自定义维度字段都可以通过接口请求到Select的内容
      const column = defaultColumns(item);
      return {
        ...column,
        title: item.fieldName,
      };
    }),
  );

  const filterFulFilledValue = customColumns
    .filter((result) => result.status === 'fulfilled')
    // @ts-ignore
    .map((result) => result.value);

  return { customColumns: [...filterFulFilledValue, teamInfoColumn], fieldIdArr };
}
