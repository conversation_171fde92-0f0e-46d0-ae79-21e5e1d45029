import { DateType } from '@/services/business';
import { getTimeByTypeBusiness } from '@/utils/time';
import { DatePicker, Radio, RadioChangeEvent, Space } from 'antd';
import { RangePickerProps } from 'antd/es/date-picker';
import dayjs from 'dayjs';

const { RangePicker } = DatePicker;

interface TimeFilterCustomTypeTextProps {
  radioValue: DateType;
  setRadioValue: React.Dispatch<React.SetStateAction<DateType>>;
  rangeTime: (string | undefined)[];
  setRangeTime: React.Dispatch<React.SetStateAction<string[]>>;
  style?: React.CSSProperties;
  dateOptions: [DateType, string][];
  radioGroupClassName?: string;
}

// 与其他时间筛选器区别是时间按钮文本从组件外传入
const TimeFilterCustomTypeText = (props: TimeFilterCustomTypeTextProps) => {
  const {
    radioValue,
    setRadioValue,
    rangeTime,
    setRangeTime,
    style,
    dateOptions,
    radioGroupClassName,
  } = props;

  const onChange = (e: RadioChangeEvent) => {
    const value = e.target.value;
    setRadioValue?.(value);
    const range = getTimeByTypeBusiness(value);
    setRangeTime(range);
    setRangeTime(range);
  };

  const onChangeRange = (date: RangePickerProps['value'], dateString: string[]) => {
    setRangeTime(dateString);
    setRadioValue(DateType.Custom);
  };

  return (
    <Space style={style}>
      <div className={radioGroupClassName}>
        <Radio.Group value={radioValue} onChange={onChange} style={{ whiteSpace: 'nowrap' }}>
          {dateOptions.map(([type, text]) => {
            return (
              <Radio.Button value={type} key={type}>
                {text}
              </Radio.Button>
            );
          })}
        </Radio.Group>
      </div>
      <RangePicker
        onChange={onChangeRange}
        disabledDate={(currentDate) => currentDate > dayjs().endOf('day')}
        allowClear={false}
        defaultValue={[dayjs(rangeTime[0]), dayjs(rangeTime[1])]}
        value={[dayjs(rangeTime[0]), dayjs(rangeTime[1])]}
      />
    </Space>
  );
};

export default TimeFilterCustomTypeText;
