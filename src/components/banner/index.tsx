import bannerbody from '@/assets/NewMedia/bannerbody.png';
import bannerbody1 from '@/assets/NewMedia/bannerbody1.png';
import bannerPart1 from '@/assets/NewMedia/bannerpart1.png';
import bannerPart2 from '@/assets/NewMedia/bannerpart2.png';
import { motion } from 'framer-motion';
import styles from './index.module.less';

const animationProps = {
  initial: { opacity: 0, y: 30 },
  animate: { opacity: 1, y: 0 },
  transition: { duration: 0.3 },
};

export default function NewMediaBanner() {
  return (
    <motion.div className={styles.banner} {...animationProps}>
      <motion.img
        src={bannerbody}
        alt="bannerbody"
        animate={{
          y: [10, -10, 10], // 上下浮动，可根据需要调整值
          x: [10, -10, 10], // 左右浮动，可根据需要调整值
        }}
        transition={{
          duration: 5, // 动画持续时间，单位为秒
          ease: 'easeInOut', // 缓动函数
          repeat: Infinity, // 无限循环动画
          repeatType: 'reverse', // 动画循环类型，这里设置为反向
        }}
        className={styles.bannerBody}
      />
      <motion.img
        src={bannerbody1}
        alt="bannerbody1"
        animate={{
          y: [10, -10, 10], // 上下浮动，可根据需要调整值
          x: [10, -10, 10], // 左右浮动，可根据需要调整值
        }}
        transition={{
          duration: 5, // 动画持续时间，单位为秒
          ease: 'easeInOut', // 缓动函数
          repeat: Infinity, // 无限循环动画
          repeatType: 'reverse', // 动画循环类型，这里设置为反向
        }}
        className={styles.bannerBody1}
      />
      <img src={bannerPart1} className={styles.bannerPart1} alt="bannerPart1" />
      <img src={bannerPart2} className={styles.bannerPart2} alt="bannerPart2" />
    </motion.div>
  );
}
