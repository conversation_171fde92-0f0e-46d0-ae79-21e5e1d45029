import { Button, Progress, ButtonProps } from 'antd';
import { forwardRef } from 'react';
import { FolderDown } from 'lucide-react';

interface ExportButtonProps extends ButtonProps {
  exportFn: () => Promise<void>;
  loading?: boolean;
  percent?: number;
  text?: string;
}

const ExportButton = forwardRef<HTMLButtonElement, ExportButtonProps>((props, ref) => {
  const { exportFn, loading, percent, text = '导出', ...restProps } = props;
  return (
    <Button ref={ref} onClick={exportFn} loading={loading} {...restProps}>
      <FolderDown size={16} strokeWidth={1.5} />
      <span>{text}</span>
      {loading && (
        <Progress percent={percent} type="circle" size={15} style={{ marginLeft: '5px' }} />
      )}
    </Button>
  );
});

export default ExportButton;
