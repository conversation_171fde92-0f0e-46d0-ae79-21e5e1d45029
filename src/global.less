@font-face {
  font-family: 'HarmonyOS_Sans_Regular';
  src: url('./assets/fonts/HarmonyOS_Sans_Regular.ttf');
}

@font-face {
  font-family: 'HarmonyOS_Sans_Regular_ltalic';
  src: url('./assets/fonts/HarmonyOS_Sans_Regular_Italic.ttf');
}

@font-face {
  font-family: 'HarmonyOS_Sans_SC_Bold';
  src: url('./assets/fonts/HarmonyOS_Sans_SC_Bold.ttf');
}

@font-face {
  font-family: 'HarmonyOS_Sans_SC_Black';
  src: url('./assets/fonts/HarmonyOS_Sans_SC_Black.ttf');
}

html,
body,
#root {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans',
    sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
}

.colorWeak {
  filter: invert(80%);
}

.ant-layout {
  min-height: 100vh;
}

.ant-pro-sider.ant-layout-sider.ant-pro-sider-fixed {
  left: unset;
}

canvas {
  display: block;
}

body {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

ul,
ol {
  list-style: none;
}

@media (max-width: 768px) {
  .ant-table {
    width: 100%;
    overflow-x: auto;

    &-thead > tr,
    &-tbody > tr {
      > th,
      > td {
        white-space: pre;

        > span {
          display: block;
        }
      }
    }
  }
}

.single-line {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.ant-image-preview-operations-wrapper.quality-imgepreview {
  .ant-image-preview-close {
    color: black;
    background: white;
  }

  .ant-image-preview-switch-left {
    left: 500px;
    color: black;
    background: white;
  }

  .ant-image-preview-switch-right {
    right: 500px;
    color: black;
    background: white;
  }
}

#dify-chatbot-bubble-button {
  background-color: #1c64f2 !important;
}

#dify-chatbot-bubble-window {
  width: 24rem !important;
  height: 40rem !important;
}
