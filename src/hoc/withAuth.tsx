import Exception from '@/components/exception';
import useProjectFeature from '@/hooks/fg/useProjectFeature';
import useProjectId from '@/hooks/useProjectId';
import { FunctionCode } from '@/services/system';
import { useNavigate, useParams } from '@umijs/max';
import { Spin } from 'antd';

export const withAuth = (Component: React.ComponentType, featureId: FunctionCode) => () => {
  const navigation = useNavigate();
  const projectId = useProjectId();
  const { projectKey, industryType = '1' } = useParams();
  const { enable, isLoading } = useProjectFeature({ projectId, featureId });

  if (isLoading) {
    return (
      <Spin
        style={{
          width: '100%',
          height: '100%',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }}
        spinning
      />
    );
  } else if (enable) {
    return <Component />;
  } else if (projectKey) {
    return (
      <Exception
        type={403}
        homePage={`/project/${projectKey}/${industryType}/overview/data-dashboard`}
      />
    );
  } else {
    return navigation('/');
  }
};
