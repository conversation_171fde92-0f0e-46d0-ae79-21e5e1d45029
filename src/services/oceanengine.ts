import { ApiResult, PageBasicParams, PagintaionData } from './common';
import { request } from '@umijs/max';

export type GetAuthorizationUrlParams = {
  /**
   * 应用身份类型
   * 1 :广告主类型
   * 2 :代理商类型
   */
  identityType: number;
  /**
   * 项目 id
   */
  projectId?: string;
};

export function GetAuthorizationUrl(params: GetAuthorizationUrlParams) {
  return request<ApiResult<{ authorizationUrl: string; taskId: string }>>(
    '/new-media-api/oceanengine/authorization/url',
    {
      method: 'GET',
      params,
    },
  );
}

export function GetAuthorization(data: {
  /**
   * 授权码
   */
  authCode: string;
  /**
   * 回调 state 参数
   */
  state: string;
}) {
  return request<ApiResult<boolean>>(`/new-media-api/oceanengine/authorization`, {
    method: 'POST',
    data,
  });
}

export type OceanengineAdvertiserParams = {
  /**
   * 广告主名称
   */
  advertiserName?: string;
  /**
   * 授权日期-结束范围
   */
  authorizationDateTimeEnd?: string;
  /**
   * 授权日期-开始范围
   */
  authorizationDateTimeStart?: string;
  /**
   * 身份类型
   */
  identityType?: number;
  /**
   * 项目 Id
   */
  projectId: string;
} & PageBasicParams;

export type OceanengineAdvertiserResult = {
  /**
   * 广告主 id
   */
  advertiserId?: number;
  /**
   * 广告主名称
   */
  advertiserName?: string;
  /**
   * 授权日期
   */
  authorizationDateTime?: string;
  /**
   * 身份类型
   */
  identityType?: number;
  /**
   * 操作人
   */
  operatorName?: string;
  /**
   * 状态
   */
  status?: number;
};

export function GetOceanengineAdvertiserPage(params: OceanengineAdvertiserParams) {
  return request<ApiResult<PagintaionData<OceanengineAdvertiserResult>>>(
    '/new-media-api/oceanengine/advertiser/page',
    {
      method: 'GET',
      params,
    },
  );
}

export type ExportOceanengineAdvertiserParams = {
  /**
   * 广告主名称
   */
  advertiserName?: string;
  /**
   * 授权日期-结束范围
   */
  authorizationDateTimeEnd?: string;
  /**
   * 授权日期-开始范围
   */
  authorizationDateTimeStart?: string;
  /**
   * 身份类型
   */
  identityType?: number;
  /**
   * 项目 Id
   */
  projectId: string;
};

export function ExportOceanengineAdvertiserPage(params: any) {
  return request<ApiResult<string>>('/new-media-api/oceanengine/advertiser/export', {
    method: 'GET',
    params,
  });
}

export function UpdateOceanengineAdvertiserStatus(data: {
  /**
   * 广告主 id
   */
  advertiserId: number;
  /**
   * 项目 id
   */
  projectId?: string;
  /**
   * 状态
   */
  status: number;
}) {
  return request<ApiResult<boolean>>(`/new-media-api/oceanengine/advertiser/status`, {
    method: 'POST',
    data,
  });
}

export enum TaskStatus {
  /**
   * 待授权
   */
  Pending = 0,
  /**
   * 进行中
   */
  InProgress = 1,
  /**
   * 成功
   */
  Success = 2,
  /**
   * 失败
   */
  Failed = 3,
  /**
   * 失效
   */
  Invalid = 4,
  /**
   * 异常
   */
  NotExist = -1,
}

export function GetOceanengineAuthorizationTaskPage(params: {
  /**
   * 任务 Id
   */
  taskId: string;
}) {
  return request<ApiResult<{ status?: TaskStatus }>>(
    '/new-media-api/oceanengine/authorization/task',
    {
      method: 'GET',
      params,
    },
  );
}
