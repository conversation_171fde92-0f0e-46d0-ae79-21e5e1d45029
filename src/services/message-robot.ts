import { request } from '@umijs/max';
import { ApiResult } from './common';

type ConcludingRemark = {
  /** 结束语内容 */
  content: string;
  /** 状态 0-关闭 1-开启 */
  status: number;
};

type ContactWay = {
  /** 获取联系方式发问内容 */
  askContent: string;
  /** 获取联系方式响应内容 */
  responseContent: string;
  /** 状态 0-关闭 1-开启 */
  status: number;
};

export type RobotTimeConfig = {
  /** 星期一 */
  mon?: number[];
  /** 星期二 */
  tue?: number[];
  /** 星期三 */
  wed?: number[];
  /** 星期四 */
  thu?: number[];
  /** 星期五 */
  fri?: number[];
  /** 星期六 */
  sat?: number[];
  /** 星期天 */
  sun?: number[];
};

export type KnowledgeTableCofig = {
  /** 引导文案 */
  guideTitle?: string;
  /** 引导问题 */
  questionList?: KnowledgeItem[];
};

type DouyinPrivateMessageLeadsCustomerUpdateDTO = {
  /** 项目 id */
  projectId?: string;
  /** 更新类型  */
  updateType: keyof IntentionTableConfig;
  /** 获取意向用户配置 */
  leadsCustomerConfig?: Partial<IntentionTableConfig>;
};

type RetainCustomerConfig = {
  /** 挽留配置 */
  retainConfig: RetainConfig[];
  /** 夜间免打扰状态 0-关闭 1-开启 */
  nightNotDisturbStatus: number;
};

type DouyinPrivateMessageRetainCustomerUpdateDTO = {
  /** 项目 id */
  projectId?: string;
  /** 挽留客户配置 */
  douyinPrivateMessageRetainCustomerConfig: RetainCustomerConfig;
};

type DouyinPrivateMessageRobotActiveGuideConfigUpdateDTO = {
  /** 项目 id */
  projectId?: string;
  /** 开关状态 0 关闭 1 开启 */
  status: number;
  /** 主动引导问题配置 */
  activeGuideConfig?: KnowledgeTableCofig;
};

export type RobotConfig = {
  /** 主键 */
  id?: number;
  /** 总开关状态 0 关闭 1 开启 */
  status?: number;
  /** 主动引导开关状态 0 关闭 1 开启 */
  activeGuideStatus?: number;
  /** 挽留客户功能开关状态 0 关闭 1 开启 */
  retainCustomerStatus?: number;
  /** 获取意向用户功能开关状态 0 关闭 1 开启 */
  leadsCustomerStatus?: number;
  /** 开启时间配置 */
  effectiveTimeConfig?: RobotTimeConfig;
  /** 主动引导问题配置 */
  activeGuideConfig?: KnowledgeTableCofig;
  /** 挽留客户配置 */
  retainCustomerConfig?: RetainCustomerConfig;
  /** 获取意向用户配置 */
  leadsCustomerConfig?: IntentionTableConfig;
  /** 是否启用所有团队 0 否 1 是 */
  enableAllTeam: 0 | 1;
};

export type IntentionTableConfig = {
  /** 开场白配置 */
  openingTalk: OpeningTalk;
  /** 意向车系配置 */
  intentionCarSeries: IntentionCarSeries;
  /** 意向城市配置 */
  intentionCity: IntentionCity;
  /** 试驾意愿配置 */
  driveWillingness: DriveWillingness;
  /** 联系方式配置 */
  contactWay: ContactWay;
  /** 获取称呼配置 */
  salutation: Salutation;
  /** 购车时间配置 */
  purchaseTime: PurchaseTime;
  /** 结束语配置 */
  concludingRemark: ConcludingRemark;
};

type DriveWillingness = {
  /** 试驾意愿发问内容 */
  askContent: string;
  /** 试驾意愿响应内容 */
  responseContent: string;
  /** 引导卡片配置 */
  guideCardConfig: SubConfig[];
  /** 状态 0-关闭 1-开启 */
  status: number;
};

export type SubConfig = {
  /** 问题 */
  question: string;
  /** 排序字段 */
  sort?: number;
};

export type KnowledgeItem = {
  /** 问题 */
  question: string;
  /** 答案 */
  answer: string;
  /** 排序字段 */
  sort: number;
  /** 留资后回复 */
  leadsAnswer: string;
};

type IntentionCarSeries = {
  /** 意向车系发问内容 */
  askContent: string;
  /** 意向车系响应内容 */
  responseContent: string;
  /** 意向车系引导卡片配置 */
  guideCardConfig: SubConfig[];
  /** 状态 0-关闭 1-开启 */
  status: number;
};

type IntentionCity = {
  /** 发问内容 */
  askContent: string;
  /** 响应内容 */
  responseContent: string;
  /** 状态 0-关闭 1-开启 */
  status: number;
};

export type OpeningTalk = {
  /** 开场白内容 */
  content: string;
  /** 状态 0-关闭 1-开启 */
  status: number;
};

type PurchaseTime = {
  /** 购车时间发问内容 */
  askContent: string;
  /** 购车时间响应内容 */
  responseContent: string;
  /** 购车时间引导卡片配置 */
  guideCardConfig: SubConfig[];
  /** 状态 0-关闭 1-开启 */
  status: number;
};

export type RetainConfig = {
  /** 沉默时间-秒 */
  silenceTime: number;
  /** 发送话术 */
  content: string;
};

export type Salutation = {
  /** 获取联系方式发问内容 */
  askContent: string;
  /** 卡片背景图片 */
  imageUrl: string;
  /** 卡片标题 */
  title: string;
  /** 组件集合 1-姓名 2-手机号 3-城市 */
  componentList: number[];
  /** 获取称呼响应内容 */
  responseContent: string;
  /** 获取称呼状态 0-关闭 1-开启 */
  status: number;
};

/** 获取智能客服配置 GET /douyin/private-message/robot/config */
export async function GetDouyinPrivateMessageRobotConfig(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: {
    projectId?: string;
  },
) {
  return request<ApiResult<RobotConfig>>(`/new-media-api/douyin/private-message/robot/config`, {
    method: 'GET',
    params,
  });
}

/** 更新智能客服主动引导配置 POST /douyin/private-message/robot/config/active-guide */
export async function PostActiveGuide(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  body: DouyinPrivateMessageRobotActiveGuideConfigUpdateDTO,
) {
  return request<ApiResult<boolean>>(
    `/new-media-api/douyin/private-message/robot/config/active-guide`,
    {
      method: 'POST',
      data: body,
    },
  );
}

/** 更新智能客服主动引导配置开关 POST /douyin/private-message/robot/config/active-guide/switch-status */
export async function PostActiveGuideSwitch(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  body: {
    /** 项目 id */
    projectId?: string;
    /** 状态 0-关闭 1-开启 */
    status: number;
  },
) {
  return request<ApiResult<boolean>>(
    `/new-media-api/douyin/private-message/robot/config/active-guide/switch-status`,
    {
      method: 'POST',
      data: body,
    },
  );
}

/** 更新智能客服接待时段配置 POST /douyin/private-message/robot/config/effective-time */
export async function PostEffectiveTime(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  body: {
    /** 项目 id */
    projectId?: string;
    /** 配置参数 */
    effectiveTimeConfig: RobotTimeConfig;
  },
) {
  return request<ApiResult<boolean>>(
    `/new-media-api/douyin/private-message/robot/config/effective-time`,
    {
      method: 'POST',
      data: body,
    },
  );
}

/** 获取智能客服开启门店 GET /douyin/private-message/robot/config/enable-team */
export async function GetEnableTeam(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: {
    /** 项目 id */
    projectId?: string;
  },
) {
  return request<ApiResult<string[]>>(
    `/new-media-api/douyin/private-message/robot/config/enable-team`,
    {
      method: 'GET',
      params,
    },
  );
}

/** 更新获取意向用户配置 POST /douyin/private-message/robot/config/leads-customer */
export async function PostLeadsCustomer(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  body: DouyinPrivateMessageLeadsCustomerUpdateDTO,
) {
  return request<ApiResult<boolean>>(
    `/new-media-api/douyin/private-message/robot/config/leads-customer`,
    {
      method: 'POST',
      data: body,
    },
  );
}

/** 更新获取意向用户配置开关 POST /douyin/private-message/robot/config/leads-customer/switch-status */
export async function PostLeadsCustomerSwitch(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  body: {
    /** 项目 id */
    projectId?: string;
    /** 状态 0-关闭 1-开启 */
    status: number;
  },
) {
  return request<ApiResult<boolean>>(
    `/new-media-api/douyin/private-message/robot/config/leads-customer/switch-status`,
    {
      method: 'POST',
      data: body,
    },
  );
}

/** 更新挽留客户配置 POST /douyin/private-message/robot/config/retain-customer */
export async function PostRetainCustomer(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  body: DouyinPrivateMessageRetainCustomerUpdateDTO,
) {
  return request<ApiResult<boolean>>(
    `/new-media-api/douyin/private-message/robot/config/retain-customer`,
    {
      method: 'POST',
      data: body,
    },
  );
}

export async function PostRetainCustomerSwitch(body: {
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  /** 项目 id */
  projectId?: string;
  /** 状态 0-关闭 1-开启 */
  status: number;
}) {
  return request<ApiResult<boolean>>(
    `/new-media-api/douyin/private-message/robot/config/retain-customer/switch-status`,
    {
      method: 'POST',
      data: body,
    },
  );
}

/** 更新智能客服开关状态 POST /douyin/private-message/robot/config/switch-status */
export async function PostSwitchStatus(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  body: {
    /** 项目 id */
    projectId?: string;
    /** 状态 0-关闭 1-开启 */
    status: number;
  },
) {
  return request<ApiResult<boolean>>(
    `/new-media-api/douyin/private-message/robot/config/switch-status`,
    {
      method: 'POST',
      data: body,
    },
  );
}

/** 更新智能客服可用门店 POST /douyin/private-message/robot/config/enable-team/update */
export async function PostEnableTeamUpdate(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  body: {
    /** 项目 id */
    projectId?: string;
    /** 开启类型 1-全部门店 0-指定门店 */
    enableType: number;
    /** 可用门店 */
    teamCodeList?: string[];
  },
) {
  return request<ApiResult<boolean>>(
    `/new-media-api/douyin/private-message/robot/config/enable-team/update`,
    {
      method: 'POST',
      data: body,
    },
  );
}

/** 智能客服体验 POST /douyin/private-message/robot/experience */
export async function PostExperience(body: {
  /** 项目 id */
  projectId?: string;
  /** 发问内容 */
  content: string;
  /**
   * 会话 id
   */
  conversationId?: string;
}) {
  return request<
    ApiResult<{
      contentList: string[];
      conversationId: string;
    }>
  >(`/new-media-api/douyin/private-message/robot/experience`, {
    method: 'POST',
    data: body,
  });
}
