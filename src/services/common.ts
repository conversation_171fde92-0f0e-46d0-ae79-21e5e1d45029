export interface ApiResult<TData = unknown> {
  code: number;
  data?: TData;
  msg?: string;
}

export interface PagintaionData<T> {
  total: number; // 总条数
  items?: T[]; // 数据 有些接口用items 有些接口用records
  records?: T[];
}

export interface PageBasicParams {
  isAsc?: boolean;
  orderBy?: string;
  orderType?: string;
  page?: number;
  size?: number;
  pos?: number;
}

export enum BlueVipFlag {
  NoVip = 0,
  Vip = 1,
}

export interface CommonTag {
  id: number;
  name: string;
}
