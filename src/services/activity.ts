import { request } from '@umijs/max';
import { ApiResult, PageBasicParams, PagintaionData } from './common';
import { PlatForm } from '@/utils/platform';

export type ActivityCreateOrUpdateParams = {
  /**
   * 账号主键 id 集合
   */
  accountPrimaryIds: number[];
  /**
   * 活动描述
   */
  activityDesc?: string;
  /**
   * 活动结束时间
   */
  activityEndTime: string;
  /**
   * 活动 id
   */
  activityId?: number;
  /**
   * 活动名称
   */
  activityName: string;
  /**
   * 活动开始时间
   */
  activityStartTime: string;
  /**
   * 直播激励规则
   */
  liveRules?: Rules[];
  /**
   * 其他激励规则
   */
  otherRules?: Rules[];
  /**
   * 参与平台
   */
  platformList?: PlatForm[];
  /**
   * 短视频激励规则
   */
  postRules?: Rules[];
  activityUserList?: ActivityUserItem[];
  /**
   * 项目 id
   */
  projectId: number;
};

export type Rules = {
  point: string;
  ruleStr: string;
  type: number;
  accountType: number | string;
  calcType: number | string;
};

export type ActivityUpdateRes = {
  code?: number;
  data?: boolean;
  msg?: string;
  time?: string;
  traceId?: string;
};

export type ActivityCopyParams = {
  /**
   * 活动结束时间
   */
  activityEndTime: string;
  /**
   * 活动id
   */
  activityId: number;
  /**
   * 活动名称
   */
  activityName: string;
  /**
   * 活动开始时间
   */
  activityStartTime: string;
};

export type ActivityRankParams = {
  /**
   * 活动ID
   */
  activityId?: string | number;
  /**
   * 查询日期
   */
  calcDate?: string;
  /**
   * 动态维度字段
   */
  fieldList?: string[];
  /**
   * 团队编码集合
   */
  teamCodeList?: string[];
  /**
   * 聚合类型 1: 大区 2: 省份 3: 城市 4: 团队
   */
  type: number;
};

export type ActivityRankParamsByPage = ActivityRankParams & PageBasicParams;

export type RankDetail = {
  /**
   * 账号类型 1-不限类型 2-企业号 3-员工号 4-普通号
   */
  accountType: number;
  /**
   * 得分
   */
  point: string;
  /**
   * 规则类型
   */
  ruleKey: string;
  /**
   * 具体值
   */
  value: string;
};

export type ActivityRankPageItem = {
  /**
   * 查询对象
   */
  name?: string;
  /**
   * 积分
   */
  point?: string;
  /**
   * 排名
   */
  rank?: number;
  /**
   * 排名详情
   */
  rankDetail?: RankDetail[];
  /**
   * 聚合类型 1: 大区 2: 省份 3: 城市 4: 团队
   */
  type: number;
};

export type ActivityPageParams = {
  /**
   * 活动结束时间
   */
  activityEndTime?: string;
  /**
   * 活动名称
   */
  activityName?: string;
  /**
   * 活动开始时间
   */
  activityStartTime?: string;
  /**
   * 活动状态 1未开始 2进行中 3已结束 4重跑中
   */
  activityStatus?: number;
  projectId?: string;
} & PageBasicParams;

export type ActivityPageItem = {
  activityDesc?: string;
  activityEndTime?: string;
  activityName?: string;
  activityStartTime?: string;
  activityStatus: number;
  createTime?: string;
  id: number;
  platformList?: PlatForm[];
  serialNumber?: number;
};

export type ActivityUserItem = {
  id: string;
  avatar: string;
  platform: number;
  nickname: string;
  blueVipFlag: number;
  showAccountId: string;
  isJoin?: number;
};

export type GetActivityRecordRes = {
  activityId?: number;
  createTime?: string;
  id?: number;
  produceDate?: string;
  produceTime?: string;
  updateTime?: string;
};

export type ActivityDetailRes = ActivityCreateOrUpdateParams;

export type RuleTypeList = {
  /**
   * 账号类型 1-不限类型 2-企业号 3-员工号
   */
  accountType: number;
  /**
   * 规则 key
   */
  ruleKey: string;
  calcType: number;
  type: number;
};

export type ExportRuleBody = { accountType: number; ruleKey: string; title: string };

export function ActivityUpdate(params: ActivityCreateOrUpdateParams) {
  return request<ApiResult<ActivityUpdateRes>>('/new-media-api/activity/update', {
    method: 'POST',
    data: params,
  });
}

export function ActivityDelete(params: { activityId: string | number }) {
  return request<ApiResult<any>>('/new-media-api/activity/delete', {
    method: 'POST',
    params,
  });
}

export function ActivityCreate(params: ActivityCreateOrUpdateParams) {
  return request<ApiResult>('/new-media-api/activity/create', {
    method: 'POST',
    data: params,
  });
}

export function ActivityCopy(params: ActivityCopyParams) {
  return request<ApiResult>('/new-media-api/activity/copy', {
    method: 'POST',
    data: params,
  });
}

export function GetActivityRankPage(params: ActivityRankParamsByPage) {
  return request<ApiResult<PagintaionData<ActivityRankPageItem>>>(
    '/new-media-api/activity/rank/page',
    {
      method: 'GET',
      params,
      skipErrorHandler: true,
    },
  );
}

export function ActivityRankExport(params: ActivityRankParams, data: ExportRuleBody[]) {
  return request('/new-media-api/activity/rank/export', {
    method: 'POST',
    params,
    data,
    responseType: 'blob',
  });
}

export function GetActivityPage(params: ActivityPageParams) {
  return request<ApiResult<PagintaionData<ActivityPageItem>>>('/new-media-api/activity/page', {
    method: 'GET',
    params,
  });
}

export function GetActivityDetail(params: { activityId?: number | string }) {
  return request<ApiResult<ActivityDetailRes>>('/new-media-api/activity/detail', {
    method: 'GET',
    params,
  });
}

export function GetActivityRankDetail(params: { activityId?: number | string }) {
  return request<ApiResult<PagintaionData<ActivityRankPageItem>>>(
    '/new-media-api/activity/rank/detail',
    {
      method: 'GET',
      params,
      skipErrorHandler: true,
    },
  );
}

export function GetActivityUserList(params: any) {
  return request<ApiResult<ActivityUserItem[]>>('/new-media-api/activity/user/list', {
    method: 'GET',
    params,
  });
}

export function GetActivityUserJoinList(params: { activityId?: number | string }) {
  return request<ApiResult<ActivityUserItem[]>>('/new-media-api/activity/user/join/list', {
    method: 'GET',
    params,
  });
}

export function ActivityRankDetailExport(
  params: { activityId?: number | string },
  data: ExportRuleBody[],
) {
  return request('/new-media-api/activity/rank/detail/export', {
    method: 'POST',
    params,
    data,
    responseType: 'blob',
  });
}

export function GetActivityRecord(params: any) {
  return request<ApiResult<GetActivityRecordRes[]>>('/new-media-api/activity/produce/record/list', {
    method: 'GET',
    params,
  });
}

export function GetRuleList(params: { activityId?: string }) {
  return request<ApiResult<RuleTypeList[]>>('/new-media-api/activity/rule/list', {
    method: 'GET',
    params,
  });
}
