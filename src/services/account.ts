import { request } from '@umijs/max';
import { Api<PERSON><PERSON>ult, CommonTag, PageBasicParams, PagintaionData } from './common';
import { PlatForm } from '@/utils/platform';

export interface SpiderSyncUser {
  id: number;
  accountId: string;
  platform: number;
  remark: string;
  syncFrequency: number;
  postSaveStatus: number;
  liveReplayStatus: number;
  postQualityStatus: number;
  liveQualityStatus: number;
  qualityRuleProjectId?: number;
  nextSyncTime: string;
  lastSyncTime: string;
  lastSyncProfileStatus: number;
  lastSyncPostStatus: number;
  isDisable: number;
  createTime: string;
  accountUser?: AccountUser;
  tags?: CommonTag[];
}

export interface AccountUser {
  id: number;
  platform: PlatForm;
  accountId: string;
  nickname: string;
  avatar: string;
  uid: string;
  uniqueId: string;
  blueVipFlag: number;
  blueVipReason: string;
  signature: string;
  postCount: number;
  postCountStr: string;
  followingCount: number;
  followingCountStr: string;
  followerCount: number;
  followerCountStr: string;
  totalFavorited: number;
  totalFavoritedStr: string;
  ipLocation: string;
  shareUrl: string;
  shareQrcodeUrl: string;
  roomId?: string;
  syncInfo?: SpiderSyncUser;
  liveInfo?: AccountLiveItem;
  recentLiveNum: number;
  tags?: CommonTag[];
  liveUid: string;
}

export function QueryAccountUsersForPagination(params: any) {
  return request<ApiResult<any>>(`/new-media-api/account/query_users`, {
    method: 'POST',
    data: params,
  });
}

export function GetAccountUserInfo(params: { accountId: string; platform: number }) {
  return request<ApiResult<any>>(`/new-media-api/account/get_user_info`, {
    method: 'GET',
    params,
  });
}

export function GetAccountUserDailyStats(params: any) {
  return request<ApiResult<any>>(`/new-media-api/account/get_account_daily_stats`, {
    method: 'POST',
    data: params,
  });
}

export type AccountPostBody = {
  /**
   * 用户id
   */
  accountId?: string;
  /**
   * 作品创建结束时间
   */
  endTime: string;
  /**
   * 昵称
   */
  nickname?: string;
  /**
   * 平台
   */
  platform?: number;
  /**
   * 作品 id
   */
  postId?: string;
  /**
   * 是否有质检
   */
  qualityFlag?: number;
  /**
   * 作品创建开始时间
   */
  startTime: string;
  /**
   * 标签id
   */
  tagIds?: number[];
};

export type AccountPostItem = {
  accountId?: string;
  admireCount?: number;
  allowShare?: number;
  /**
   * api 获取状态 0-未获取 1-已获取
   */
  apiStatus?: number;
  /**
   * 清除标识 0-正常 1-已清除
   */
  cleanFlag?: number;
  /**
   * 清除时间
   */
  cleanTime?: string;
  collectCount?: number;
  collectCountStr?: string;
  commentCount?: number;
  commentCountStr?: string;
  coverTransferUrl?: string;
  coverUrl?: string;
  description?: string;
  diggCount?: number;
  diggCountStr?: string;
  duration?: number;
  id?: number;
  inReviewing?: number;
  isDelete?: number;
  isLongVideo?: number;
  isProhibited?: number;
  isTop?: number;
  pictureTransferUrls?: string[];
  pictureUrls?: string[];
  pictureUrlsPixels?: {
    /**
     * 高度
     */
    height?: string;
    /**
     * 宽度
     */
    width?: string;
  }[];
  platform?: number;
  playCount?: number;
  postCreateTime?: string;
  postId?: string;
  postType?: number;
  /**
   * 关联的项目-平台账号
   */
  projectId?: number;
  /**
   * 关联的项目行业类型
   */
  projectIndustryType?: number;
  /**
   * 质检标记 0:没有质检记录 1: 存在质检记录
   */
  qualityFlag?: number;
  shareCount?: number;
  shareCountStr?: string;
  shareLinkDesc?: string;
  shareUrl?: string;
  status?: number;
  title?: string;
  userInfo?: AccountUser;
  videoDataSize?: number;
  videoHeight?: number;
  videoTransferUrl?: string;
  videoUrl?: string;
  videoWidth?: number;
  [property: string]: any;
};

export function QueryAccountPostsForPagination(params: PageBasicParams, data: AccountPostBody) {
  return request<ApiResult<PagintaionData<AccountPostItem>>>(
    `/new-media-api/account_post/query_posts`,
    {
      method: 'POST',
      params,
      data,
    },
  );
}

export type AccountLiveBody = {
  /**
   * 用户id
   */
  accountId?: string;
  isAsc?: boolean;
  /**
   * 昵称
   */
  nickname?: string;
  /**
   * 排序字段
   */
  orderBy?: string;
  /**
   * 排序类型
   */
  orderType?: string;
  /**
   * 当前页数
   */
  page: number;
  /**
   * 平台
   */
  platform?: number;
  pos?: number;
  /**
   * 质检状态
   */
  qualityStatus?: number;
  /**
   * 直播状态
   */
  roomStatus?: number;
  /**
   * 每页条数
   */
  size: number;
  /**
   * 标签id
   */
  tagIds?: number[];
};

export type AccountLiveItem = {
  accountId?: string;
  coverTransferUrl?: string;
  coverUrl?: string;
  flvPullUrl?: string;
  id?: number;
  likeCount?: number;
  liveDuration?: number;
  platform?: number;
  /**
   * 关联的项目-平台账号
   */
  projectId?: number;
  /**
   * 关联的项目行业类型
   */
  projectIndustryType?: number;
  qualityStatus?: number;
  replayFileHeight?: number;
  replayFileNum?: number;
  replayFileUrl?: string;
  replayFileWidth?: number;
  replayStatus?: number;
  roomId?: string;
  roomStatus?: number;
  /**
   * 直播间标题
   */
  roomTitle?: string;
  roomUserCount?: number;
  startTime?: string;
  stopTime?: string;
  totalUserCount?: number;
  totalUserCountStr?: string;
  userInfo?: AccountUser;
  webRid?: string;
};

export function QueryAccountLivesForPagination(params: PageBasicParams, data: AccountLiveBody) {
  return request<ApiResult<PagintaionData<AccountLiveItem>>>(
    `/new-media-api/account_live/query_lives`,
    {
      method: 'POST',
      params,
      data,
    },
  );
}

export function GetAccountStatSum(params: {
  platform: number;
  fromDate: string;
  toDate: string;
  tagIds?: number[];
}) {
  return request<ApiResult<any>>(`/new-media-api/account_stat/get_account_stat_sum`, {
    method: 'POST',
    data: params,
  });
}

export function GetPostStatSum(params: {
  platform: number;
  fromDate: string;
  toDate: string;
  tagIds?: number[];
}) {
  return request<ApiResult<any>>(`/new-media-api/account_stat/get_post_stat_sum`, {
    method: 'POST',
    data: params,
  });
}
