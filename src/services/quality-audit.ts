import { request } from '@umijs/max';
import { ApiResult, PagintaionData } from './common';

type getAnalysisCategoryOverviewParams = {
  /** 项目 id */
  projectId: string;
  /** 违规命中时间-开始时间 */
  startDate: string;
  /** 违规命中时间-结束时间 */
  endDate: string;
  /** 分类类型
  1 :品牌
  2 :平台
  3 :主播形象 */
  categoryType?: number;
};

type getAnalysisMonitoringWordOverviewParams = {
  /** 项目 id */
  projectId: string;
  /** 开始时间 */
  startDate: string;
  /** 结束时间 */
  endDate: string;
  /** 分类类型
  1 :品牌
  2 :平台
  3 :主播形象 */
  categoryType?: number;
  /** 质检词库主键/主播形象编码 */
  monitoringWordId: string;
};

type getAnalysisOverviewParams = {
  /** 项目 id */
  projectId: string;
  /** 违规命中时间-开始时间 */
  startDate: string;
  /** 违规命中时间-结束时间 */
  endDate: string;
};

type getAnalysisPageParams = {
  /** 项目 id */
  projectId: string;
  /** 开始时间 */
  startDate: string;
  /** 结束时间 */
  endDate: string;
  /** 分类类型
  1 :品牌
  2 :平台
  3 :主播形象 */
  categoryType?: number;
  /** 质检词库主键/主播形象编码 */
  monitoringWordId: string;
  /** 质检词 */
  monitoringWord?: string;
  /** 类型
  1 :直播
  2 :作品 */
  targetType?: number;
  /** 质检场景类型
  1 :标题文字
  2 :语音
  3 :封面
  4 :画面 */
  qualityRuleType?: number;
};

type getAuditCategoryOverviewParams = {
  /** 项目 id */
  projectId: string;
  /** 开始时间 */
  startDate: string;
  /** 结束时间 */
  endDate: string;
  /** 分类类型
  1 :品牌
  2 :平台
  3 :主播形象 */
  categoryType?: number;
};

type getAuditMonitoringWordOverviewParams = {
  /** 项目 id */
  projectId: string;
  /** 开始时间 */
  startDate: string;
  /** 结束时间 */
  endDate: string;
  /** 分类类型
  1 :品牌
  2 :平台
  3 :主播形象 */
  categoryType?: number;
  /** 质检词库主键/主播形象编码 */
  monitoringWordId: string;
};

type getAuditOperationDetailParams = {
  /** 项目 id */
  projectId?: string;
  /** 审核操作记录主键 id */
  id: number;
};

type getAuditOperationPageParams = {
  /** 结束时间 */
  endTime?: string;
  /** 排序字段 */
  orderBy?: string;
  /** 排序类型 */
  orderType?: string;
  /** 当前页数 */
  page: number;
  /** 项目 id */
  projectId: string;
  /** 每页条数 */
  size: number;
  /** 开始时间 */
  startTime?: string;
  /** 类型 1: 确认违规 2: 撤销违规 */
  type: number;
};

type getAuditOverviewParams = {
  /** 项目 id */
  projectId: string;
  /** 开始时间 */
  startDate: string;
  /** 结束时间 */
  endDate: string;
};

type getAuditPageParams = {
  /** 项目 id */
  projectId: string;
  /** 开始时间 */
  startDate: string;
  /** 结束时间 */
  endDate: string;
  /** 分类类型
  1 :品牌
  2 :平台
  3 :主播形象 */
  categoryType?: number;
  /** 质检词库主键/主播形象编码 */
  monitoringWordId: string;
  /** 质检词 */
  monitoringWord?: string;
  /** 类型
  1 :直播
  2 :作品 */
  targetTypeList?: number[];
  /** 质检场景类型
  1 :标题文字
  2 :语音
  3 :封面
  4 :画面 */
  qualityRuleTypeList?: number[];
};

type QualityAuditAnalysisCategoryOverviewItem = {
  /** 审核数量 */
  auditCount?: number;
  /** 误判率 */
  errorRate?: string;
  /** 质检类型名称 */
  label: string;
  /** 质检词库主键/主播形象编码 */
  monitoringWordId: string;
  /** 撤销违规数量 */
  revokeCount?: number;
  /** 质检类型 code */
  wordCode: string;
};

export type QualityAuditAnalysisCategoryOverviewVO = {
  /** 质检词库分类概览列表 */
  categoryOverviewItemList?: QualityAuditAnalysisCategoryOverviewItem[];
  /** 总撤销违规数量 */
  totalRevokeCount?: number;
  /** 总审核数量 */
  totalAuditCount?: number;
  /** 总误判率 */
  errorRate?: string;
};

type QualityAuditAnalysisMonitoringWordOverviewVO = {
  /** 质检词 */
  label?: string;
  /** 撤销违规数量 */
  revokeCount?: number;
  /** 审核数量 */
  auditCount?: number;
  /** 误判率 */
  errorRate?: string;
};

type QualityAuditAnalysisOverviewVO = {
  /** 总撤销违规数量 */
  totalRevokeCount?: number;
  /** 总审核数量 */
  totalAuditCount?: number;
  /** 总误判率 */
  errorRate?: string;
  /** 平台违规撤销违规数量 */
  platformViolationRevokeCount?: number;
  /** 平台违规审核数量 */
  platformViolationAuditCount?: number;
  /** 平台违规误判率 */
  platformErrorRate?: string;
  /** 品牌违规撤销违规数量 */
  brandViolationRevokeCount?: number;
  /** 品牌违规审核数量 */
  brandViolationAuditCount?: number;
  /** 品牌违规误判率 */
  brandErrorRate?: string;
  /** 主播形象撤销违规数量 */
  anchorImageViolationRevokeCount?: number;
  /** 主播形象审核数量 */
  anchorImageViolationAuditCount?: number;
  /** 主播形象误判率 */
  anchorImageErrorRate?: string;
};

export type QualityAuditAnalysisPageVO = {
  /** 审核理由 */
  auditReason?: string;
  /** 审核时间 */
  auditTime?: string;
  /** 清除标识 0-正常 1-已清除 */
  cleanFlag?: number;
  /** 质检文本内容 */
  content: string;
  /** 违规时间 */
  exCreateTime?: string;
  /** 审核记录主键 id */
  id?: number;
  /** 最新操作人 */
  latestOperator?: string;
  /** 命中标记 */
  mark?: string;
  /** 质检图片地址 */
  picUrl?: string;
  /** 质检规则类型 */
  qualityRuleType?: number;
  /** 命中的相对时间 */
  startTime?: number;
  /** 直播间 id/作品 id */
  targetId?: string;
  /** 类型 */
  targetType?: number;
  /** 直播: m3u8地址 / 短视频：cos 地址 */
  url?: string;
};

type QualityAuditCategoryOverviewVO = {
  /** 质检词库主键/主播形象编码 */
  monitoringWordId: string;
  /** 质检类型 code */
  wordCode: string;
  /** 标签名 */
  label: string;
  /** 数量 */
  count: number;
};

type QualityAuditHandleDTO = {
  /** 项目 id */
  projectId: string;
  /** 质检审核记录主键集合 */
  ids: string[];
  /** 操作类型 1: 确认违规 2: 撤销违规 */
  handleType: number;
  /** 质检审核理由标签主键 */
  tagId?: number;
  /** 分类类型 1: 品牌违规 2: 平台违规 3: 主播形象违规 */
  categoryType?: number;
  /** 内容类型 1: 文字 2: 图片 */
  contentType: number;
};

type QualityAuditMonitoringWordOverviewVO = {
  /** 标签名 */
  label?: string;
  /** 数量 */
  count?: number;
};

export type QualityAuditOperationDetailVO = {
  /** 审核记录主键 id */
  id?: number;
  /** 直播间 id/作品 id */
  targetId?: string;
  /** 类型 */
  targetType?: 1 | 2;
  /** 质检规则类型 */
  qualityRuleType?: 1 | 2 | 3 | 4;
  /** 质检文本内容 */
  content: string;
  /** 命中标记 */
  mark?: string;
  /** 质检图片地址 */
  picUrl?: string;
  /** 命中的相对时间 */
  startTime?: number;
  /** 审核理由 */
  auditReason?: string;
  /** 违规时间 */
  exCreateTime?: string;
  /** 审核时间 */
  auditTime?: string;
  /** 最新操作人 */
  latestOperator?: string;
};

type QualityAuditOperationRecordPageVO = {
  /** 审核操作记录主键 id */
  id?: number;
  /** 分类类型 1: 品牌违规 2: 平台违规 3: 主播形象违规 */
  categoryType?: 1 | 2 | 3;
  /** 类型 1: 确认违规 2: 撤销违规 */
  type?: number;
  /** 审核记录数量 */
  auditIdsSize?: number;
  /** 状态 0: 正常 1: 已回滚 */
  status?: number;
  /** 操作人 */
  operator?: string;
  /** 操作时间 */
  operationTime?: string;
  /** 内容类型 1: 文字 2: 图片 */
  contentType?: number;
};

type QualityAuditOperationRollbackDTO = {
  /** 项目 id */
  projectId?: string;
  /** 主键 id */
  id: number;
};

type QualityAuditOverviewVO = {
  /** 平台违规总数 */
  platformViolationCount?: number;
  /** 平台违规已审核条数 */
  platformViolationAuditCount?: number;
  /** 平台违规待审核条数 */
  platformViolationUnAuditCount?: number;
  /** 品牌违规总数 */
  brandViolationCount?: number;
  /** 品牌违规已审核条数 */
  brandViolationAuditCount?: number;
  /** 品牌违规待审核条数 */
  brandViolationUnAuditCount?: number;
  /** 主播形象违规条数 */
  anchorImageViolationCount?: number;
  /** 主播形象违规已审核条数 */
  anchorImageViolationAuditCount?: number;
  /** 主播形象违规待审核条数 */
  anchorImageViolationUnAuditCount?: number;
};

export type QualityAuditPageVO = {
  /** 审核记录主键 id */
  id?: number;
  /** 直播间 id/作品 id */
  targetId?: string;
  /** 类型 1 直播  2 作品 */
  targetType?: 1 | 2;
  /** 质检规则类型 */
  qualityRuleType?: 1 | 2 | 3 | 4;
  /** 质检文本内容 */
  content: string;
  /** 命中标记 */
  mark?: string;
  /** 质检图片地址 */
  picUrl?: string;
  /** 命中的相对时间 */
  startTime?: number;
  /** 直播: m3u8地址 / 短视频：cos 地址 */
  url?: string;
  /** 清除标识 0-正常 1-已清除 */
  cleanFlag?: number;
};

export type QualityAuditRevokeDTO = {
  /** 分类类型 */
  categoryType: number;
  /** 质检审核记录主键 */
  ids: number[];
  /** 项目 id */
  projectId: string;
};

/** 查询质检审核统计分析分类总览 查询质检审核统计分析分类总览
查询质检审核统计分析分类总览 GET /quality-audit/analysis/category/overview */
export async function GetAnalysisCategoryOverview(params: getAnalysisCategoryOverviewParams) {
  return request<ApiResult<QualityAuditAnalysisCategoryOverviewVO>>(
    `/new-media-api/quality-audit/analysis/category/overview`,
    {
      method: 'GET',
      params: {
        ...params,
      },
    },
  );
}

/** 查询质检审核统计分析质检词库总览 查询质检审核统计分析质检词库总览
查询质检审核统计分析质检词库总览 GET /quality-audit/analysis/monitoring-word/overview */
export async function GetAnalysisMonitoringWordOverview(
  params: getAnalysisMonitoringWordOverviewParams,
) {
  return request<ApiResult<QualityAuditAnalysisMonitoringWordOverviewVO[]>>(
    `/new-media-api/quality-audit/analysis/monitoring-word/overview`,
    {
      method: 'GET',
      params: {
        ...params,
      },
    },
  );
}

/** 查询质检审核统计分析总览 查询质检审核统计分析总览
查询质检审核统计分析总览 GET /quality-audit/analysis/overview */
export async function GetAnalysisOverview(params: getAnalysisOverviewParams) {
  return request<ApiResult<QualityAuditAnalysisOverviewVO>>(
    `/new-media-api/quality-audit/analysis/overview`,
    {
      method: 'GET',
      params: {
        ...params,
      },
    },
  );
}

/** 分页查询质检审核统计分析 分页查询质检审核统计分析
分页查询质检审核统计分析 GET /quality-audit/analysis/page */
export async function GetAnalysisPage(params: getAnalysisPageParams) {
  return request<ApiResult<PagintaionData<QualityAuditAnalysisPageVO>>>(
    `/new-media-api/quality-audit/analysis/page`,
    {
      method: 'GET',
      params: {
        ...params,
      },
    },
  );
}

/** 查询质检审核分类总览 查询质检审核分类总览
查询质检审核分类总览 GET /quality-audit/audit/category/overview */
export async function GetAuditCategoryOverview(params: getAuditCategoryOverviewParams) {
  return request<ApiResult<QualityAuditCategoryOverviewVO[]>>(
    `/new-media-api/quality-audit/audit/category/overview`,
    {
      method: 'GET',
      params: {
        ...params,
      },
    },
  );
}

/** 质检审核操作 质检审核操作
质检审核操作 POST /quality-audit/audit/handle */
export async function PostAuditHandle(body: QualityAuditHandleDTO) {
  return request<ApiResult<boolean>>(`/new-media-api/quality-audit/audit/handle`, {
    method: 'POST',
    data: body,
  });
}

/** 查询质检审核词总览 查询质检审核词总览
查询质检审核词总览 GET /quality-audit/audit/monitoring-word/overview */
export async function GetAuditMonitoringWordOverview(params: getAuditMonitoringWordOverviewParams) {
  return request<ApiResult<QualityAuditMonitoringWordOverviewVO[]>>(
    `/new-media-api/quality-audit/audit/monitoring-word/overview`,
    {
      method: 'GET',
      params: {
        ...params,
      },
    },
  );
}

/** 质检审核记录详细查询 GET /quality-audit/audit/operation/detail */
export async function GetAuditOperationDetail(params: getAuditOperationDetailParams) {
  return request<ApiResult<QualityAuditOperationDetailVO[]>>(
    `/new-media-api/quality-audit/audit/operation/detail`,
    {
      method: 'GET',
      params: {
        ...params,
      },
    },
  );
}

/** 质检审核记录分页查询 质检审核记录分页查询
质检审核记录分页查询 GET /quality-audit/audit/operation/page */
export async function GetAuditOperationPage(params: getAuditOperationPageParams) {
  return request<ApiResult<PagintaionData<QualityAuditOperationRecordPageVO>>>(
    `/new-media-api/quality-audit/audit/operation/page`,
    {
      method: 'GET',
      params: {
        ...params,
      },
    },
  );
}

/** 质检审核操作回滚 质检审核操作回滚
质检审核操作回滚 POST /quality-audit/audit/operation/rollback */
export async function PostAuditOperationRollback(body: QualityAuditOperationRollbackDTO) {
  return request<ApiResult<boolean>>(`/new-media-api/quality-audit/audit/operation/rollback`, {
    method: 'POST',
    data: body,
  });
}

/** 查询审核数量卡片总览 查询审核数量卡片总览
查询审核数量卡片总览 GET /quality-audit/audit/overview */
export async function GetAuditOverview(params: getAuditOverviewParams) {
  return request<ApiResult<QualityAuditOverviewVO>>(`/new-media-api/quality-audit/audit/overview`, {
    method: 'GET',
    params: {
      ...params,
    },
  });
}

/** 质检审核分页获取 质检审核分页获取
质检审核分页获取 GET /quality-audit/audit/page */
export async function GetAuditPage(params: getAuditPageParams) {
  return request<ApiResult<PagintaionData<QualityAuditPageVO>>>(
    `/new-media-api/quality-audit/audit/page`,
    {
      method: 'GET',
      params: {
        ...params,
      },
    },
  );
}

export async function PostAuditRevoke(body: QualityAuditRevokeDTO) {
  return request<ApiResult<boolean>>(`/new-media-api/quality-audit/audit/revoke`, {
    method: 'POST',
    data: body,
  });
}
