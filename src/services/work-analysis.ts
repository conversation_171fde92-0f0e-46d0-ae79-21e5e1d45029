import { request } from '@umijs/max';
import { ApiResult, PagintaionData } from './common';
import { IndustryType } from '@/utils/const';

/**
 * @summary 查询视频类型
 */
export function getVideoTypeList(params: AccountPostAnalysisVideoTypeQO) {
  return request<ApiResult<AccountPostAnalysisVideoTypeVO[]>>(
    '/new-media-api/account-post-analysis/video-type',
    {
      method: 'GET',
      params,
    },
  );
}

/**
 * @summary 标签作品数据
 */

export function getTagCompareData(params: AccountPostAnalysisTagCompareQO) {
  return request<ApiResult<AccountPostAnalysisTagCompareVO>>(
    '/new-media-api/account-post-analysis/tag-compare',
    {
      method: 'GET',
      params,
    },
  );
}

/**
 * @summary 素材分析
 */

export function getWorkAnalysisList(params: AccountPostAnalysisAnalysisQO) {
  return request<ApiResult<PagintaionData<WorksAnalysisVO>>>(
    '/new-media-api/account-post-analysis/page',
    {
      method: 'GET',
      params,
    },
  );
}

/**
 * @summary 内容公式
 */
export function getFormulaList(params: AccountPostAnalysisTagCompareQO) {
  return request<ApiResult<WorksContentFormulaVO[]>>(
    '/new-media-api/account-post-analysis/content-formula',
    {
      method: 'GET',
      params,
    },
  );
}

/**
 * @summary 根据视频类型查询品牌信息
 * @tag 作品分析端
 */

export function getBrandList(params: AccountPostAnalysisTagCompareQO) {
  return request<ApiResult<BrandInfoVO[]>>('/new-media-api/account-post-analysis/brand-info', {
    method: 'GET',
    params,
  });
}

/**
 * @summary 查询画面类型
 * @tag 作品分析端
 */
export function getScreenTypeList(params: AccountPostAnalysisTagCompareQO) {
  return request<ApiResult<string[]>>('/new-media-api/account-post-analysis/screen-type', {
    method: 'GET',
    params,
  });
}

export interface PaginationParam {
  /**
   * @format int32
   */
  size: number;
  /**
   * @format int32
   */
  page: number;
  orderBy?: string;
  orderType?: string;
  isAsc?: boolean;
  /**
   * @format int32
   */
  pos?: number;
}

/**
 * @description 作品类型查询参数
 */
export interface AccountPostAnalysisVideoTypeQO {
  /**
   * @description 开始时间
   */
  startTime: string;
  /**
   * @description 结束时间
   */
  endTime: string;
  /** 0作品，1互动 */
  sortType: number;
  industryType?: IndustryType;
  tagGroupId?: number;
}

export interface AccountPostAnalysisVideoTypeVO {
  videoType: string;
  videoCount?: number;
  brandCount?: number;
  collectCount?: number;
  commentCount?: number;
  diggCount?: number;
  shareCount?: number;
  ratio?: number;
}

export interface AccountPostAnalysisTagCompareQO {
  /**
   * @description 开始时间
   * @format date
   */
  startTime: string;
  /**
   * @description 结束时间
   * @format date
   */
  endTime: string;
  /**
   * @description 视频类型
   */
  videoType: string;
  /**
   * @description 矩阵标签id
   * @format int32
   */
  tagId?: string;
  /** 行业类型 */
  industryType?: IndustryType;
  /** 标签组id */
  tagGroupId?: number;
}

/**
 * @description 标签信息
 */
export interface TagVO {
  /**
   * @description 标签id
   * @format int32
   */
  tagId: number;
  /**
   * @description 互动量
   * @format int32
   */
  interactionCount?: number;
  /**
   * @format int32
   */
  worksCount?: number;
}

/**
 * @description 标签作品每日汇总
 */
export interface AccountPostAnalysisDailySumVO {
  /**
   * @description 日期
   * @format date
   */
  date?: string;
  /**
   * @description 互动量
   * @format int32
   */
  interactionCount?: number;
  /**
   * @format int32
   */
  worksCount?: number;
}

export interface AccountPostAnalysisTagCompareVO {
  /**
   * @description 总互动量
   * @format int32
   */
  totalInteraction?: number;
  /**
   * @description 总点赞量
   * @format int32
   */
  totalDigg?: number;
  /**
   * @description 总评论量
   * @format int32
   */
  totalComment?: number;
  /**
   * @description 总分享量
   * @format int32
   */
  totalShare?: number;
  /**
   * @description 总收藏量
   * @format int32
   */
  totalCollect?: number;
  /**
   * @description 互动量中位数
   * @format int32
   */
  medianInteraction?: number;
  /**
   * @description 点赞量中位数
   * @format int32
   */
  medianDigg?: number;
  /**
   * @description 评论量中位数
   * @format int32
   */
  medianComment?: number;
  /**
   * @description 分享量中位数
   * @format int32
   */
  medianShare?: number;
  /**
   * @description 收藏量中位数
   * @format int32
   */
  medianCollect?: number;
  /**
   * @description 总作品数
   * @format int32
   */
  totalWorks?: number;
  /**
   * @description 标签信息
   */
  tags?: Array<TagVO>;

  worksDailySumVOList?: Array<AccountPostAnalysisDailySumVO>;
}

export interface AccountPostAnalysisAnalysisQO {
  orderBy?: string;
  orderType?: string;
  /**
   * @format int32
   */
  size: number;
  /**
   * @format int32
   */
  page: number;
  /**
   * @description 开始时间
   * @format date
   */
  startTime: string;
  /**
   * @description 结束时间
   * @format date
   */
  endTime: string;
  /**
   * @description 视频类型
   */
  videoType: string;
  /**
   * @description 矩阵标签id
   * @format int32
   */
  tagId?: string;
  /**
   * @description 画面类型
   */
  screenType?: Array<string>;
  /**
   * @description 标题
   */
  title?: string;
  /** 行业类型 */
  industryType?: IndustryType;
  /** 标签组id */
  tagGroupId?: number;
}
export interface WorksAnalysisVO {
  /**
   * @description 排名
   * @format int32
   */
  ranking?: number;
  /**
   * @description 标题
   */
  title?: string;
  /**
   * @description 收藏数
   * @format int32
   */
  collectCount?: number;
  /**
   * @description 评论数
   * @format int32
   */
  commentCount?: number;
  /**
   * @description 点赞量
   * @format int32
   */
  diggCount?: number;
  /**
   * @description 分享量
   * @format int32
   */
  shareCount?: number;
  /**
   * @description 互动量
   * @format int32
   */
  interactCount?: number;
  /**
   * @description 封面URL
   */
  coverUrl?: string;
  /**
   * @description 视频URL
   */
  videoUrl?: string;
  /**
   * @description 脚本公式
   */
  scriptFormula?: string;
  /**
   * @description 文字脚本
   */
  subtitles?: string;
  /**
   * @description 画面类型
   */
  screenTypes?: Array<string>;
}

/**
 * @description 脚本内容信息
 */
export interface ScriptContentInfo {
  /**
   * @description 脚本内容
   */
  scriptContent?: string;
  /**
   * @description 内容占比
   * @format double
   */
  contentRatio?: number;
}

export interface WorksContentFormulaVO {
  /**
   * @description 脚本公式
   */
  scriptFormula?: string;
  /**
   * @description 脚本使用数量
   * @format int32
   */
  scriptCount?: number;
  /**
   * @description 开头脚本
   */
  startScripts?: Array<{
    /**
     * @description 脚本内容
     */
    startScript?: string;
    /**
     * @description 内容占比
     * @format double
     */
    startScriptRatio?: number;
  }>;
  /**
   * @description 脚本内容信息
   */
  scriptContentInfo?: Array<ScriptContentInfo>;
  /**
   * @description 推荐镜头数
   */
  recommendCount?: string;
}

export interface BrandInfoVO {
  brandId: string;
  brandName: string;
}
