import { request } from '@umijs/max';
import { ApiResult, PageBasicParams, PagintaionData } from './common';
import { PlatForm } from '@/utils/platform';

/** 账号删除 POST /spider-account/delete */
export async function deleteSpiderAccount(body: string[], options?: { [key: string]: any }) {
  return request<boolean>('/new-media-api/spider-account/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 分页查询账号相关信息 GET /spider-account/page */
export async function getSpiderAccounts(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: PageBasicParams & SpiderAccountQO,
  options?: { [key: string]: any },
) {
  return request<ApiResult<PagintaionData<SpiderAccountPageVO>>>(
    '/new-media-api/spider-account/page',
    {
      method: 'GET',
      params: {
        ...params,
      },
      ...(options || {}),
    },
  );
}

/** 抓取频次修改 POST /spider-account/update-status */
export async function updateCaptureStatus(
  body: {
    /** 帐户id */
    accountIds: string[];
    /** 捕获频次 */
    frequency: number;
  },
  options?: { [key: string]: any },
) {
  return request<boolean>('/new-media-api/spider-account/update-status', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

export async function getSpiderLog(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: PageBasicParams & SpiderAccountPostLogQO,
  options?: { [key: string]: any },
) {
  return request<ApiResult<PagintaionData<SpiderLogPageVO>>>('/new-media-api/spider-log/page', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 获取授权二维码 GET /spider-account/qr-code */
export async function createQrCode(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: {
    /** 平台：1抖音，5视频号，6小红书 */
    platform: number;
    /** 是否是机构视频号 */
    isAgency?: boolean;
    /** 项目id */
    projectId?: string | null;
  },
  options?: { [key: string]: any },
) {
  return request<ApiResult<string>>('/new-media-api/spider-account/qr-code', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

export enum AuthStatus {
  '二维码获取中' = '0',
  '待扫码' = '1',
  '授权成功' = '2',
  '二维码过期' = '3',
  '授权关闭' = '4',
  '需要验证码' = '5',
  '正在请求验证码' = '6',
  '正在验证验证码' = '7',
  '授权失败' = '',
}

/** 查询授权状态 GET /spider-account/query-status */
export async function queryAuthStatus(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: { uuid: string },
  options?: { [key: string]: any },
) {
  return request<
    ApiResult<{
      /** uuid */
      uuid: string;
      /**
       * 状态，0获取二维码中 1待扫码，2授权成功，3已过期 4授权关闭 5需要输入验证码
       * 6发送/spider-account/get-code接口后的状态 7发送/spider-account/code确认验证码后的状态
       * */
      status: AuthStatus;
      /** 二维码base64 */
      qrCode?: string;
      /** 手机号 */
      phoneNumber: string;
    }>
  >('/new-media-api/spider-account/query-status', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 停止查询授权状态 GET /spider-account/query-status */
export async function closeAuth(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: { uuid: string },
  options?: { [key: string]: any },
) {
  return request<ApiResult<boolean>>('/new-media-api/spider-account/authorisation-close', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 代理ip删除 POST /spider-proxy/delete */
export async function deleteUsingIP(
  body: {
    /** 项目id */
    projectId: string;
    /** ids */
    ids?: string[];
  },
  options?: { [key: string]: any },
) {
  return request<boolean>('/new-media-api/spider-proxy/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 下载代理信息模板 下载代理信息模板 GET /spider-proxy/download-template */
export async function downloadProxyTemplate(options?: { [key: string]: any }) {
  return request<any>('/new-media-api/spider-proxy/download-template', {
    method: 'GET',
    ...(options || {}),
    responseType: 'blob',
  });
}

/** 导出代理信息 导出代理信息 GET /spider-proxy/export */
export async function exportProxy(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: { projectId: string },
  options?: { [key: string]: any },
) {
  return request<SpiderProxyVO[]>('/new-media-api/spider-proxy/export', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
    responseType: 'blob',
  });
}

/** 分页查询代理信息 分页查询代理信息 GET /spider-proxy/page */
export async function getProxyList(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: { projectId: string } & PageBasicParams,
  options?: { [key: string]: any },
) {
  return request<ApiResult<PagintaionData<SpiderProxyVO>>>('/new-media-api/spider-proxy/page', {
    method: 'GET',
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** 代理ip信息修改 POST /spider-proxy/update */
export async function updateProxy(body: SpiderProxyDTO, options?: { [key: string]: any }) {
  return request<boolean>('/new-media-api/spider-proxy/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 代理ip信息修改 POST /spider-account/update */
export async function updateAccountProxy(
  body: ProxyAllocationDTO,
  options?: { [key: string]: any },
) {
  return request<boolean>('/new-media-api/spider-account/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}

/** 发送授权短信验证码 POST /spider-account/code */
export async function sendCaptcha(body: { uuid: string; code: string }) {
  return request<boolean>('/new-media-api/spider-account/code', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
  });
}

/** 获取验证码 POST /spider-account/get-code */
export async function requestCaptcha(body: { uuid: string }) {
  return request<boolean>('/new-media-api/spider-account/get-code', {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
    params: body,
  });
}

interface ProxyAllocationDTO {
  /** 分配规则: 0 自动分配，1 自定义 */
  rule: number;
  /** 项目id */
  projectId: string;
  /** 账号信息 */
  accountInfos: {
    /** 账号id */
    accountId?: string;
    /** 城市 */
    city?: string;
    /** 省份 */
    province?: string;
  }[];
  /** 代理id */
  proxyId: string;
}

export interface SpiderProxyDTO {
  /** id */
  id: string;
  /** 项目id */
  projectId: string;
  /** 代理 */
  proxy?: string;
  /** 类型 */
  type?: number;
  /** 市 */
  city?: string;
  /** 省 */
  province?: string;
  /** 到期时间 */
  expiresTime?: string;
}

export interface SpiderProxyVO {
  /** id */
  id: string;
  /** 代理IP */
  proxy: string;
  /** 地址 */
  city: string;
  province: string;
  /** 类型,1独享，2共享吧 */
  type: number;
  /** 分配账号数量 */
  accountCount: number;
  /** 到期时间 */
  expiresTime: string;
}

export interface SpiderAccountPageVO {
  /** 主键 id */
  id: string;
  /** 账号名称 */
  nickname?: string;
  /** 账号id */
  accountId: string;
  /** 平台：1抖音，5视频号，6小红书 */
  platform?: PlatForm;
  /** 账号状态,1:已授权 2:已失效 */
  authorizationtStatus?: number;
  /** 抓取状态，0 失败, 1 正常 */
  captureStatus?: number;
  /** 团队维度信息集合 */
  teamFields?: TeamField[];
  /** 团队编码 */
  teamCode?: string;
  /** 频次 0 禁止，1 每小时，2 每天 */
  frequency?: number;
  /** 更新时间 */
  updateTime?: string;
  /** 最后捕获时间 */
  lastCaptureTime?: string;
  /** 授权时间 */
  authorizationtTime?: string;
  /** IP */
  proxy: string;
  /** 所在地 */
  city: string;
  /** 是否是视频号机构号 */
  agencyFlag: number;
}

interface TeamField {
  /** 维度id */
  fieldValueId?: number;
  /** 维度名 */
  fieldName?: string;
  /** 维度值 */
  fieldValue?: string;
  /** 维度类型 0: 自定义 1：默认 */
  type?: number;
  /** 维度 id */
  fieldId: string;
  /** 是否展示 0: 不展示 1: 展示 */
  showFlag?: number;
  /** 0一般1团队名字2层级关系 */
  bizType?: number;
}

interface SpiderAccountQO {
  /** 项目 id */
  projectId: string;
  /** 平台：1抖音，5视频号，6小红书 */
  platform?: number;
  /** 团队列表 */
  teamCodeList?: string[];
}

interface SpiderAccountPostLogQO {
  /** 项目 id */
  projectId: number;
  /** 平台：1抖音，5视频号，6小红书 */
  platform?: number;
  /** 账号名称 */
  nickname?: string;
  /** 账号id */
  accountId?: string;
}

export interface SpiderLogPageVO {
  /** 平台1抖音，5视频号，6小红书 */
  platform?: number;
  /** 账号id */
  accountId?: string;
  /** 账号名称 */
  nickname?: string;
  /** 任务类型:0数据采集，1作品发布吧 */
  type?: number;
  /** 任务状态码，1：任务执行中，2：任务执行成功 */
  status?: number;
  /** 结果状态码，1：成功，0：失败 */
  resultStatus?: number;
  /** 任务开始时间 */
  taskStartTime?: string;
  /** 是否是视频号机构号 */
  agencyFlag: number;
}
