import { request } from '@umijs/max';
import { ApiResult } from './common';

export function QueryDouyinAuthLink(params: { projectAccountId: number }) {
  return request<ApiResult<string>>('/new-media-api/douyin-api/oauth/code/get', {
    method: 'POST',
    data: params,
  });
}

export function ReportDouyinAuthCode(params: { code: string; state?: string }) {
  return request<ApiResult>('/new-media-api/douyin-api/oauth/code/check', {
    method: 'POST',
    data: params,
  });
}
