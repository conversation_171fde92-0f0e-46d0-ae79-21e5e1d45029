import { request } from '@umijs/max';
import { ApiResult } from './common';
import { IndustryType } from '@/utils/const';

/** 查询视频类型/车型相关数据 查询视频类型/车型相关数据 GET /project-post-analysis/base-info */
export async function getDataTypes(params: Omit<ProjectAccountPostAnalysisQO, 'name'>) {
  return request<ApiResult<ProjectAccountPostAnalysisVO[]>>(
    '/new-media-api/project-post-analysis/base-info',
    {
      method: 'POST',
      data: {
        ...params,
      },
    },
  );
}

/** 查询车型-商品 GET /project-post-analysis/car-info */
export async function getCarInfo(params: TimeRangeWithIndustryType) {
  return request<ApiResult<string[]>>('/new-media-api/project-post-analysis/product-info', {
    method: 'GET',
    params: {
      ...params,
    },
  });
}

/** 获取作品相关信息 GET /project-post-analysis/post-info */
export async function getDataTypeInfo(params: ProjectAccountPostAnalysisQO) {
  return request<ApiResult<ProjectAccountPostAnalysisPostInfoVO>>(
    '/new-media-api/project-post-analysis/post-info',
    {
      method: 'POST',
      data: {
        ...params,
      },
    },
  );
}

/** 查询画面类型 GET /project-post-analysis/screen-type */
export async function getScreenType(params: TimeRangeWithIndustryType) {
  return request<ApiResult<string[]>>('/new-media-api/project-post-analysis/screen-type', {
    method: 'GET',
    params: {
      ...params,
    },
  });
}

/** 查询视频类型 GET /project-post-analysis/video-type */
export async function getVideoType(params: TimeRangeWithIndustryType) {
  return request<ApiResult<string[]>>('/new-media-api/project-post-analysis/video-type', {
    method: 'GET',
    params: {
      ...params,
    },
  });
}

interface ProjectAccountPostAnalysisQO {
  projectId: string;
  /** 开始时间 */
  startTime: string;
  /** 结束时间 */
  endTime: string;
  /** 0:按照车型对比，1:按照视频类型对比 */
  type: number;
  /** 车型名称/视频类型名称 */
  name: string;
  /** 团队编码列表 */
  teamCodeList?: string[];
  /** 行业类型 */
  industryType?: IndustryType;
}

export interface ProjectAccountPostAnalysisVO {
  /** 车型名称/视频类型名称 */
  name?: string;
  /** 作品数量 */
  videoCount?: number;
  /** 互动量占比 */
  videoRatio?: number;
  /** 互动量 */
  interCount?: number;
  /** 互动量占比 */
  interRatio?: number;
}

interface ProjectAccountPostAnalysisPostInfoVO {
  /** 总互动量 */
  totalInteraction?: number;
  /** 总点赞量 */
  totalDigg?: number;
  /** 总评论量 */
  totalComment?: number;
  /** 总分享量 */
  totalShare?: number;
  /** 总收藏量 */
  totalCollect?: number;
  /** 互动量中位数 */
  medianInteraction?: number;
  /** 点赞量中位数 */
  medianDigg?: number;
  /** 评论量中位数 */
  medianComment?: number;
  /** 分享量中位数 */
  medianShare?: number;
  /** 收藏量中位数 */
  medianCollect?: number;
  /** 互动量品牌中位数 */
  medianBrandInteraction?: number;
  /** 点赞量品牌中位数 */
  medianBrandDigg?: number;
  /** 评论量品牌中位数 */
  medianBrandComment?: number;
  /** 分享量品牌中位数 */
  medianBrandShare?: number;
  /** 收藏量品牌中位数 */
  medianBrandCollect?: number;
  /** 总作品数 */
  totalWorks?: number;
  dailySums?: ProjectAccountPostAnalysisDailySum[];
  distributionInfos?: DistributionInfo[];
}

interface ProjectAccountPostAnalysisDailySum {
  /** 日期 */
  date?: string;
  /** 互动量 */
  interactionCount?: number;
  worksCount?: number;
}

interface DistributionInfo {
  /** 车型名称/视频类型名称 */
  name?: string;
  /** 互动量 */
  interactionCount?: number;
  worksCount?: number;
}

interface TimeRangeParams {
  projectId: string;
  /** 开始时间 */
  startTime?: string;
  /** 结束时间 */
  endTime?: string;
}

interface TimeRangeWithIndustryType extends TimeRangeParams {
  industryType?: IndustryType;
}
