import { request } from '@umijs/max';
import { ApiResult } from './common';

export type IndustryDashboard = {
  industryName: string;
  industryIcon: string;
  tagId: number;
  accountCount: number;
  accountCountRate: string;
  followerCount: number;
  followerGrowthCount: number;
  followerGrowthCountRate: string;
  followerCountRate: string;
  postInteractionCount: number;
  postInteractionCountRate: string;
  postPublishCount: number;
  postPublishCountRate: string;
  liveCount: number;
  liveCountRate: string;
  liveViewTime: number;
  liveViewTimeRate: string;
  liveDiggCount: number;
  liveDiggCountRate: string;
  rank: number;
  isMe: number;
};

export type IndustryPost = {
  industryName: string;
  industryIcon: string;
  accountCount: number;
  postInteractionCount: string;
  postPublishCount: string;
  postInteractionCountRate?: string;
  postPublishCountRate?: string;
  tagId?: number;
  isMe?: number;
};

export type IndustryLive = {
  industryName: string;
  industryIcon: string;
  accountCount: number;
  liveCount: string;
  liveViewTime: string;
  liveDiggCount: string;
  liveCountRate?: string;
  liveViewTimeRate?: string;
  liveDiggCountRate?: string;
  tagId?: number;
  isMe?: number;
};

export type ApiResultTime<TData = unknown> = {
  code: number;
  data: TData;
  msg?: string;
  time: string;
};

export type IndustryPostTopData = {
  industryName: string;
  industryIcon: string;
  tagId: number;
  title: string;
  url: string;
  avatar: string;
  nickname: string;
  postInteractionCount: number;
  publishTime: string;
  duration: number;
  postDiggCount: number;
  postCommentCount: number;
  postShareCount: number;
  rank: number;
  cover: string;
};

export type IndustryLiveTopData = {
  industryName: string;
  industryIcon: string;
  tagId: number;
  avatar: string;
  nickname: string;
  title: string;
  replayUrls: any[];
  liveDiggCount: number;
  liveViewTime: number;
  rank: number;
  cover: string;
};

export function GetIndustryDashboard(params: any) {
  return request<ApiResultTime<IndustryDashboard[]>>(
    `/new-media-api/project-account-industry/dashboard`,
    {
      method: 'GET',
      params,
    },
  );
}

export function ExportIndustryDashboard(params: any) {
  return request('/new-media-api/project-account-industry/dashboard/export', {
    method: 'GET',
    params,
  });
}

export function GetIndustryPost(params: any) {
  return request<ApiResult<IndustryPost[]>>(`/new-media-api/project-account-industry/post`, {
    method: 'GET',
    params,
  });
}

export function GetIndustryPostTop(params: any) {
  return request<ApiResult<IndustryPostTopData[]>>(
    '/new-media-api/project-account-industry/post/top',
    {
      method: 'GET',
      params,
    },
  );
}

export function ExportIndustryPostTop(params: any) {
  return request('/new-media-api/project-account-industry/post/top/export', {
    method: 'GET',
    params,
  });
}

export function GetIndustryLive(params: any) {
  return request<ApiResult<IndustryLive[]>>('/new-media-api/project-account-industry/live', {
    method: 'GET',
    params,
  });
}

export function GetIndustryLiveTop(params: any) {
  return request<ApiResult<IndustryLiveTopData[]>>(
    '/new-media-api/project-account-industry/live/top',
    {
      method: 'GET',
      params,
    },
  );
}

export function ExportIndustryLiveTop(params: any) {
  return request('/new-media-api/project-account-industry/live/top/export', {
    method: 'GET',
    params,
  });
}
