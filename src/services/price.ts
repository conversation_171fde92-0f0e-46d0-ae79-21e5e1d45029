import { request } from '@umijs/max';
import { ApiResult, PagintaionData } from './common';
import { TeamFieldList } from './setting';

export type carList = {
  /**
   * 车型
   */
  carType: string;
  /**
   * 车型中文名
   */
  carTypeCn: string;
  /**
   * 最高报价
   */
  maxQuotation: number;
  /**
   * 最低报价
   */
  minQuotation: number;
};

export type LivePriceItem = {
  /**
   * 汽车报价集合
   */
  carQuotationList: carList[];
  /**
   * 直播结束时间
   */
  liveEndTime: string;
  /**
   * 直播开始时间
   */
  liveStartTime: string;
  /**
   * 报价类型 1-裸车价格 2-落地价格
   */
  quotationType: number;
  /**
   * 直播间 id
   */
  roomId: string;
  /**
   * 直播间名称
   */
  roomTitle: string;
  /**
   * 团队编码
   */
  teamCode: string;
  /**
   * 动态维度
   */
  teamFieldList: TeamFieldList[];
  /**
   * 经销商门店
   */
  teamName: string;
};

export type PaginationParam = {
  isAsc?: boolean;
  /**
   * 排序字段
   */
  orderBy?: string;
  /**
   * 排序类型
   */
  orderType?: string;
  /**
   * 当前页数
   */
  page: number;
  pos?: number;
  /**
   * 每页条数
   */
  size: number;
};

export type LivePriceRecordData = {
  startDate?: string;
  endDate?: string;
  /**
   * 车型
   */
  carType?: string;
  /**
   * 动态维度字段
   */
  fieldList?: TeamFieldList[];
  /**
   * 直播结束时间
   */
  liveEndDate?: Date;
  /**
   * 直播开始时间
   */
  liveStartDate?: Date;
  /**
   * 项目 id
   */
  projectId?: number;
  /**
   * 报价类型 1-裸车价格 2-落地价格
   */
  quotationType?: number;
  /**
   * 直播间 id
   */
  roomId?: string;
  /**
   * 直播间名称
   */
  roomTitle?: string;
  /**
   * 团队编码集合
   */
  teamCodeList?: string[];
};

export function LivePriceRecordPage(params: PaginationParam, data: LivePriceRecordData) {
  return request<ApiResult<PagintaionData<LivePriceItem>>>(
    '/new-media-api/vw/live/quotation/record/page',
    {
      method: 'POST',
      params,
      data,
    },
  );
}

export function ExportLivePriceRecordPage(data: any) {
  return request('/new-media-api/vw/live/quotation/record/export', {
    method: 'POST',
    data,
  });
}

export type GetCarTypeRes = {
  /**
   * 附加属性
   */
  attributes: { [key: string]: any };
  /**
   * 是否禁用
   */
  disabled: boolean;
  /**
   * 显示的数据
   */
  name: string;
  /**
   * 是否被选中
   */
  selected: boolean;
  /**
   * 分组标识
   */
  type: string;
  /**
   * 选中获取的属性
   */
  value: string;
};

export function GetCarType(params: { projectId?: string; startDate?: string; endDate?: string }) {
  return request<ApiResult<GetCarTypeRes[]>>('/new-media-api/vw/car/type/select', {
    method: 'GET',
    params,
  });
}

export type CarQuotationItem = {
  carType?: string;
  carTypeCn?: string;
  icon?: string;
  maxQuotation?: number;
  minQuotation?: number;
  carVersion?: string;
};

export function QueryCarQuotation(params: { projectId?: string; roomId?: string }) {
  return request<ApiResult<CarQuotationItem[]>>(
    '/new-media-api/vw/live/quotation/record/query-car-quotation',
    {
      method: 'GET',
      params,
    },
  );
}
