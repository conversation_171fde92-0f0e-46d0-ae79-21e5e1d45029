import { ApiResult, PageBasicParams, PagintaionData } from '@/services/common';
import { request } from '@umijs/max';

type AddKnowledgeDTO = {
  /** 项目 id */
  projectId?: string;
  /** 问题类型 */
  problemTypeId: number;
  /** 用户问题 */
  problemContent: string;
  /** 回复答案 */
  answerContent: string;
  /** 分组 id */
  groupId: number;
};

type DeleteKnowledgeDTO = {
  /** 项目 id */
  projectId?: string;
  /** 知识库知识主键 id */
  ids: number[];
};

type CreateKnowledgeGroup = {
  /** 项目 id */
  projectId?: string;
  /** 分组名称 */
  groupName: string;
};

type DeleteKnowledgeGroupDTO = {
  /** 项目 id */
  projectId?: string;
  /** 主键 id */
  id: number;
};

type DouyinPrivateMessageKnowledgeStoreGroupUpdateDTO = {
  /** 主键 id */
  id: number;
  /** 项目 id */
  projectId?: string;
  /** 分组名称 */
  groupName: string;
};

export type KnowledgeGroupItem = {
  /** 主键 */
  id: number;
  /** 分组名称 */
  groupName: string;
};

type DouyinPrivateMessageKnowledgeStoreSwitchStatusDTO = {
  /** 项目 id */
  projectId?: string;
  /** 主键 id */
  id?: number;
  /** 状态 0-关闭 1-开启 */
  status?: number;
};

type UpdateKnowledgeDTO = {
  /** 主键 id */
  id: number;
  /** 项目 id */
  projectId?: string;
  /** 问题类型 */
  problemTypeId: number;
  /** 用户问题 */
  problemContent: string;
  /** 回复答案 */
  answerContent: string;
  /** 分组 id */
  groupId: number;
};

export type KnowledgeBaseItem = {
  /** 主键 */
  id?: number;
  /** 问题类型名称 */
  problemTypeName?: string;
  /** 问题内容 */
  problemContent?: string;
  /** 回答内容 */
  answerContent?: string;
  /** 分组 id */
  groupId?: number;
  /** 是否默认问题 0 否 1 是 */
  defaultFlag?: number;
  /** 启用状态 0 禁用 1 启用 */
  status?: number;
  /** 操作人 */
  operator?: string;
  /** 创建时间 */
  createTime?: string;
  /** 问题类型 */
  problemTypeId?: number;
};

type DouyinPrivateMessageProblemType = {
  /** 主键 */
  id?: number;
  /** 行业主键 */
  projectIndustryId?: number;
  /** 问题类型名称 */
  problemTypeName?: string;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
};

type CreateProblemType = {
  /** 主键 */
  id?: number;
  /** 分组 id */
  knowledgeBaseGroupId: number;
  /** 问题类型名称 */
  problemTypeName: string;
  /** 项目 id */
  projectId?: string;
};

type UpdateProblemType = {
  /**
   * 主键
   */
  id: number;
  /**
   * 分组 id
   */
  knowledgeBaseGroupId: number;
  /**
   * 问题类型名称
   */
  problemTypeName: string;
  /**
   * 项目 id
   */
  projectId: string;
};

export type ProblemTypeItem = {
  /** 显示的数据 显示的数据 */
  name: string;
  /** 选中获取的属性 选中获取的属性 */
  value: number;
  /** 是否被选中 是否被选中 */
  selected?: boolean;
  /** 是否禁用 是否禁用 */
  disabled?: boolean;
  /** 分组标识 分组标识 */
  type?: string;
  /** 附加属性 附加属性 */
  attributes?: string;
};

/** 新增知识库知识 POST /douyin/private-message/knowledge-base/add */
export async function AddKnowledge(body: AddKnowledgeDTO) {
  return request<ApiResult<boolean>>(`/new-media-api/douyin/private-message/knowledge-base/add`, {
    method: 'POST',
    data: body,
  });
}

/** 检查是否初始化 GET /douyin/private-message/knowledge-base/check-init */
export async function GetCheckInit(params: { projectId?: string }) {
  return request<ApiResult<boolean>>(
    `/new-media-api/douyin/private-message/knowledge-base/check-init`,
    {
      method: 'GET',
      params: {
        ...params,
      },
    },
  );
}

/** 删除知识库知识 POST /douyin/private-message/knowledge-base/delete */
export async function DeleteKnowledge(body: DeleteKnowledgeDTO) {
  return request<ApiResult<boolean>>(
    `/new-media-api/douyin/private-message/knowledge-base/delete`,
    {
      method: 'POST',
      data: body,
    },
  );
}

/** 下载模板 GET /douyin/private-message/knowledge-base/download-template */
export async function DownloadTemplate() {
  return request<any>(`/new-media-api/douyin/private-message/knowledge-base/download-template`, {
    method: 'GET',
    responseType: 'blob',
  });
}

/** 创建分组 POST /douyin/private-message/knowledge-base/group/create */
export async function CreateKnowledgeGroup(body: CreateKnowledgeGroup) {
  return request<ApiResult<boolean>>(
    `/new-media-api/douyin/private-message/knowledge-base/group/create`,
    {
      method: 'POST',
      data: body,
    },
  );
}

/** 删除分组 POST /douyin/private-message/knowledge-base/group/delete */
export async function DeleteKnowledgeGroup(body: DeleteKnowledgeGroupDTO) {
  return request<ApiResult<boolean>>(
    `/new-media-api/douyin/private-message/knowledge-base/group/delete`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
    },
  );
}

/** 查询分组列表 GET /douyin/private-message/knowledge-base/group/list */
export async function GetKnowledgeGroupList(params: { projectId?: string }) {
  return request<ApiResult<KnowledgeGroupItem[]>>(
    `/new-media-api/douyin/private-message/knowledge-base/group/list`,
    {
      method: 'GET',
      params: {
        ...params,
      },
    },
  );
}

/** 更新分组 POST /douyin/private-message/knowledge-base/group/update */
export async function UpdateKnowledgeGroup(body: DouyinPrivateMessageKnowledgeStoreGroupUpdateDTO) {
  return request<ApiResult<boolean>>(
    `/new-media-api/douyin/private-message/knowledge-base/group/update`,
    {
      method: 'POST',
      data: body,
    },
  );
}

/** 初始化知识库 POST /douyin/private-message/knowledge-base/init-knowledge */
export async function PostInitKnowledge(params: { projectId?: string }) {
  return request<ApiResult<boolean>>(
    `/new-media-api/douyin/private-message/knowledge-base/init-knowledge`,
    {
      method: 'POST',
      params,
    },
  );
}

/** 分页查询知识库列表 GET /douyin/private-message/knowledge-base/page */
export async function GetKnowledgeBasePage(
  params: PageBasicParams & {
    /** 项目 id */
    projectId: string;
    /** 分组 id */
    groupId: number;
    /** 问题 */
    problemContent?: string;
    /** 问题类型 */
    problemTypeId?: number;
  },
) {
  return request<ApiResult<PagintaionData<KnowledgeBaseItem>>>(
    `/new-media-api/douyin/private-message/knowledge-base/page`,
    {
      method: 'GET',
      params,
    },
  );
}

/** 更新知识库知识状态 POST /douyin/private-message/knowledge-base/switch-status */
export async function PostSwitchStatus(body: DouyinPrivateMessageKnowledgeStoreSwitchStatusDTO) {
  return request<ApiResult<boolean>>(
    `/new-media-api/douyin/private-message/knowledge-base/switch-status`,
    {
      method: 'POST',
      data: body,
    },
  );
}

/** 更新知识库知识 POST /douyin/private-message/knowledge-base/update */
export async function UpdateKnowledge(body: UpdateKnowledgeDTO) {
  return request<ApiResult<boolean>>(
    `/new-media-api/douyin/private-message/knowledge-base/update`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
    },
  );
}

/** 新增问题分类 POST /douyin/private-message/problem-type/create */
export async function CreateProblemType(body: CreateProblemType) {
  return request<ApiResult<boolean>>(`/new-media-api/douyin/private-message/problem-type/create`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
  });
}

/** 删除问题分类 POST /douyin/private-message/problem-type/delete */
export async function DeleteProblemType(
  params: { projectId?: string; knowledgeBaseGroupId: number },
  body: number[],
) {
  return request<ApiResult<boolean>>(`/new-media-api/douyin/private-message/problem-type/delete`, {
    method: 'POST',
    data: body,
    params,
  });
}

/** 获取集合 GET /douyin/private-message/problem-type/list */
export async function GetProblemTypeList(params: { projectId?: string }) {
  return request<ApiResult<DouyinPrivateMessageProblemType[]>>(
    `/new-media-api/douyin/private-message/problem-type/list`,
    {
      method: 'GET',
      params: { ...params },
    },
  );
}

/** 获取下拉数据 GET /douyin/private-message/problem-type/select-data */
export async function GetProblemTypeSelectData(params: {
  projectId?: string;
  knowledgeBaseGroupId: number;
}) {
  return request<ApiResult<ProblemTypeItem[]>>(
    `/new-media-api/douyin/private-message/problem-type/select-data`,
    {
      method: 'GET',
      params: {
        ...params,
      },
    },
  );
}

/** 更新问题分类 POST /douyin/private-message/problem-type/update */
export async function UpdateProblemType(body: UpdateProblemType) {
  return request<ApiResult<boolean>>(`/new-media-api/douyin/private-message/problem-type/update`, {
    method: 'POST',
    data: body,
  });
}
