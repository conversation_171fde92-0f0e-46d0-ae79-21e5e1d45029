import { request } from '@umijs/max';
import { FieldList } from './business';
import { ApiResult } from './common';
import { PlatForm } from '@/utils/platform';
import { TrendRes } from './quality';

export type QualityDailyItem = {
  /**
   * 监控词
   */
  monitoringWord?: string;
  /**
   * 月违规次数
   */
  monthViolationCount?: number;
  /**
   * 账号昵称
   */
  nickname?: string;
  /**
   * 显示的账号 id
   */
  showAccountId?: string;
  /**
   * 团队名称
   */
  teamName?: string;
  /**
   * 违规次数
   */
  violationCount?: number;
  /**
   * 周违规次数
   */
  weekViolationCount?: number;
};

export type QualityPostDailyRes = {
  items?: QualityDailyItem[];
  total?: number;
};

export type QualityLiveDailyRes = {
  items?: QualityDailyItem[];
  total?: number;
};

export type ActivityDailyItem = {
  /**
   * 活动昵称
   */
  activityName?: string;
  /**
   * 60分以下的账号数
   */
  sixtyPointDownAccountCount?: number;
  /**
   * 60分以下的团队数
   */
  sixtyPointDownTeamCount?: number;
  /**
   * 60分以上的账号数（含 60分）
   */
  sixtyPointUpAccountCount?: number;
  /**
   * 60分以上团队数（含 60分）
   */
  sixtyPointUpTeamCount?: number;
};

export type ChatDailyItem = {
  /**
   * 会话人数
   */
  chatRound?: number;
  /**
   * 本月会话人数
   */
  monthChatRoundCount?: number;
  /**
   * 本月提醒次数
   */
  monthNotifyCount?: number;
  /**
   * 本月接收消息数
   */
  monthReceiveMsgCount?: number;
  /**
   * 本月回复消息数
   */
  monthReplayMsgCount?: number;
  /**
   * 账号昵称
   */
  nickname?: string;
  /**
   * 提醒次数
   */
  notifyCount?: number;
  /**
   * 接收消息数
   */
  receiveMsgCount?: number;
  /**
   * 回复消息数
   */
  replayMsgCount?: number;
  /**
   * 展示的账号 id
   */
  showAccountId?: string;
  /**
   * 团队名称
   */
  teamName?: string;
  /**
   * 本周会话人数
   */
  weekChatRoundCount?: number;
  /**
   * 本周提醒次数
   */
  weekNotifyCount?: number;
  /**
   * 本周接收消息数
   */
  weekReceiveMsgCount?: number;
  /**
   * 本周回复消息数
   */
  weekReplayMsgCount?: number;
};

export type ChatDailyRes = {
  items?: ChatDailyItem[];
  total?: number;
};

export function ExportQualityAccount(data: any) {
  return request('/new-media-api/quality/ex/violation/aggregation/account/export', {
    method: 'POST',
    data,
  });
}

export function ExportQualityTeam(data: any) {
  return request('/new-media-api/quality/ex/violation/aggregation/team/export', {
    method: 'POST',
    data,
  });
}

export function ExportChatTeam(data: any) {
  return request('/new-media-api/chat/violation/aggregation/team/export', {
    method: 'POST',
    data,
  });
}

export function ExportChatAccount(data: any) {
  return request('/new-media-api/chat/violation/aggregation/account/export', {
    method: 'POST',
    data,
  });
}

export type QualityExViolationTeamRes = {
  items?: QualityExViiolationTeamItem[];
  total?: number;
};

export type QualityExViiolationTeamItem = {
  /**
   * 直播违规次数
   */
  liveViolationCount: string;
  /**
   * 作品违规次数
   */
  postViolationCount: string;
  /**
   * 团队编码
   */
  teamCode: string;
  /**
   * 门店名称
   */
  teamName: string;
  /**
   * 违规总次数
   */
  violationCount: string;
  /**
   * 监控词
   */
  violationMonitoringWord: string;
  /**
   * 质检词名称-逗号分割
   */
  monitoringNameStr: string;
  /**
   * 违规等级
   */
  liveAfkLevel: number;
  /**
   * 轻微空播挂播
   */
  liveMinorAfkCount: string;
  /**
   * 严重空播挂播
   */
  liveSeriousAfkCount: string;
  /**
   * 主播形象违规-逗号分割
   */
  liveAnchorImageViolationStr: string;
};

export type QualityExViolationData = {
  /**
   * 结束日期
   */
  endDate: string;
  /**
   * 动态维度字段
   */
  fieldList?: FieldList[];
  /**
   * 搜索内容
   */
  keyword?: string;
  /**
   * 空播等级
   */
  liveAfkLevel?: number;
  /**
   * 平台
   */
  platform: number;
  /**
   * 项目 id
   */
  projectId: number;
  /**
   * 质检类别
   */
  qualityCategoryTypeList?: string[];
  /**
   * 开始日期
   */
  startDate: string;
  /**
   * 团队编码集合
   */
  teamCodeList?: string[];
};

export function QualityExViolationTeam(params: any, data: QualityExViolationData) {
  return request<ApiResult<QualityExViolationTeamRes>>(
    '/new-media-api/quality/ex/violation/aggregation/team',
    {
      method: 'POST',
      data,
      params,
    },
  );
}

export type QualityExViolationAccountRes = {
  items?: QualityExViolationAccountItem[];
  total?: number;
};

export type QualityExViolationAccountItem = {
  /**
   * 直播违规次数
   */
  liveViolationCount: string;
  /**
   * 违规账号昵称
   */
  nickname: string;
  /**
   * 作品违规次数
   */
  postViolationCount: string;
  /**
   * 违规账号 id
   */
  showAccountId: string;
  /**
   * 违规总次数
   */
  violationCount: string;
  /**
   * 监控词
   */
  violationMonitoringWord: string;
  /**
   * 团队编码
   */
  teamCode: string;
  /**
   * 门店名称
   */
  teamName: string;
  /**
   * 质检词名称-逗号分割
   */
  monitoringNameStr: string;
  /**
   * 违规等级
   */
  liveAfkLevel: number;
  /**
   * 轻微空播挂播
   */
  liveMinorAfkCount: string;
  /**
   * 严重空播挂播
   */
  liveSeriousAfkCount: string;
  /**
   * 主播形象违规-逗号分割
   */
  liveAnchorImageViolationStr: string;
};

export function QualityExViolationAccount(params: any, data: any) {
  return request<ApiResult<QualityExViolationAccountRes>>(
    '/new-media-api/quality/ex/violation/aggregation/account',
    {
      method: 'POST',
      data,
      params,
    },
  );
}

export type ChatTrendParams = {
  /**
   * 日期类型 0-昨日 1-周 2-月 3-自定义
   */
  dateType: number;
  /**
   * 平台
   */
  platform: PlatForm;
  /**
   * 项目 id
   */
  projectId?: string;
  /**
   * 类型 1-账号 2-团队
   */
  type: number;
};

export function ChatTrend(params: ChatTrendParams) {
  return request<ApiResult<TrendRes>>('/new-media-api/chat/violation/trend', {
    method: 'GET',
    params,
  });
}

export type ChatViolationAccountRes = {
  items: ChatViolationAccountItem[];
  total: string;
};

export type ChatViolationAccountItem = {
  /**
   * 会话人数
   */
  chatRound?: number;
  /**
   * 违规账号昵称
   */
  nickname?: string;
  /**
   * 提醒次数
   */
  notifyCount?: number;
  /**
   * 接收消息数
   */
  receiveMsgCount?: number;
  /**
   * 回复消息数
   */
  replayMsgCount?: number;
  /**
   * 违规账号 id
   */
  showAccountId?: string;
  /**
   * 团队编码
   */
  teamCode: string;
  /**
   * 门店名称
   */
  teamName: string;
};

export function ChatViolationAccount(params: any, data: any) {
  return request<ApiResult<ChatViolationAccountRes>>(
    '/new-media-api/chat/violation/aggregation/account',
    {
      method: 'POST',
      data,
      params,
    },
  );
}

export type ChatViolationTeamRes = {
  items?: ChatViolationTeamItem[];
  total?: number;
};

export type ChatViolationTeamItem = {
  /**
   * 会话人数
   */
  chatRound?: number;
  /**
   * 提醒次数
   */
  notifyCount?: number;
  /**
   * 接收消息数
   */
  receiveMsgCount?: number;
  /**
   * 回复消息数
   */
  replayMsgCount?: number;
  /**
   * 团队编码
   */
  teamCode: string;
  /**
   * 门店名称
   */
  teamName: string;
};

export type ChatViolationTeamData = {
  /**
   * 结束日期
   */
  endDate: string;
  /**
   * 动态维度字段
   */
  fieldList?: FieldList[];
  /**
   * 搜索内容
   */
  keyword?: string;
  /**
   * 空播等级
   */
  liveAfkLevel?: number;
  /**
   * 平台
   */
  platform: number;
  /**
   * 项目 id
   */
  projectId: number;
  /**
   * 质检类别
   */
  qualityCategoryTypeList?: string[];
  /**
   * 开始日期
   */
  startDate: string;
  /**
   * 团队编码集合
   */
  teamCodeList?: string[];
};

export function ChatViolationTeam(params: any, data: ChatViolationTeamData) {
  return request<ApiResult<ChatViolationTeamRes>>(
    '/new-media-api/chat/violation/aggregation/team',
    {
      method: 'POST',
      data,
      params,
    },
  );
}

// 导出激励活动日报统计
export function ExportActivityDaily(params: any) {
  return request('/new-media-api/activity/daily/stat/export', {
    method: 'GET',
    params,
    responseType: 'blob',
  });
}

// 查询激励活动日报统计
export function GetActivityDaily(params: any) {
  return request<ApiResult<ActivityDailyItem[]>>('/new-media-api/activity/daily/stat', {
    method: 'GET',
    params,
  });
}

// 导出私聊日报统计
export function ExportChatDaily(params: any) {
  return request('/new-media-api/chat/daily/stat/export', {
    method: 'GET',
    params,
    responseType: 'blob',
  });
}

// 分页查询私聊日报统计
export function GetChatDailyPage(params: any) {
  return request<ApiResult<ChatDailyRes>>('/new-media-api/chat/daily/stat', {
    method: 'GET',
    params,
  });
}

export type SummaryCount = {
  accountViolationCount?: number;
  accountViolationCountMom?: string;
  teamViolationCount?: number;
  teamViolationCountMom?: string;
  /**
   * 违规率
   */
  violationRate?: string;
};

export type QualityExCount = {
  /**
   * 时间类型
   * 1 :昨日/近一天
   * 2 :上周/近一周
   * 3 :上月/近一月
   * 4 :自定义
   * 5 :近 90天
   * 6 :自然周
   * 7 :自然月
   */
  dateType: number;
  /**
   * 结束日期
   */
  endDate?: string;
  /**
   * 平台
   */
  platform: PlatForm;
  /**
   * 项目 id
   */
  projectId?: string;
  /**
   * 开始日期
   */
  startDate?: string;
};

export function GetQualityExCount(params: QualityExCount) {
  return request<ApiResult<SummaryCount>>('/new-media-api/quality/ex/violation/count', {
    method: 'GET',
    params,
  });
}

export type ChatCount = {
  /**
   * 日期类型 0-昨日 1-周 2-月 3-自定义
   */
  dateType: number;
  /**
   * 结束日期
   */
  endDate?: string;
  /**
   * 平台
   */
  platform: PlatForm;
  /**
   * 项目 id
   */
  projectId?: string;
  /**
   * 开始日期
   */
  startDate?: string;
};

export function GetChatCount(params: ChatCount) {
  return request<ApiResult<SummaryCount>>('/new-media-api/chat/violation/count', {
    method: 'GET',
    params,
  });
}
