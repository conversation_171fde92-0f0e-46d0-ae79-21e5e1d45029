import { request } from '@umijs/max';
import { ApiResult, PageBasicParams, PagintaionData } from './common';
import { PortalUser } from './typings';

export interface IRegister {
  name: string;
  account: string;
  password: string;
}

export async function Register(params: IRegister) {
  return request<ApiResult<void>>('/new-media-api/auth/register', {
    method: 'POST',
    data: params,
  });
}

export interface ILogin {
  account: string;
  password: string;
  entryType: 1 | 2;
}

export async function Login(params: ILogin) {
  return request<ApiResult<void>>('/new-media-api/auth/login', {
    method: 'POST',
    data: {
      ...params,
      entryType: 1,
    },
  });
}

export async function Logout() {
  return request<ApiResult<void>>('/new-media-api/auth/logout', {
    method: 'POST',
  });
}

export async function GetCurrentUser() {
  return request<ApiResult<PortalUser>>('/new-media-api/portal-user/profile', {
    method: 'GET',
  });
}

export function QueryPortalUsersForPagination(params: PageBasicParams & { projectId?: string }) {
  return request<ApiResult<PagintaionData<PortalUser>>>('/new-media-api/portal-user/query-users', {
    method: 'GET',
    params,
  });
}

export function CreatePortalUser(params: any) {
  return request<ApiResult<any>>('/new-media-api/portal-user/create', {
    method: 'POST',
    data: params,
  });
}

export function DeletePortalUser(params: any) {
  return request<ApiResult<any>>('/new-media-api/portal-user/delete', {
    method: 'POST',
    data: params,
  });
}

export function UpdatePortalUser(params: any) {
  return request<ApiResult<any>>('/new-media-api/portal-user/update', {
    method: 'POST',
    data: params,
  });
}

// 批量生成外部账号
export function BatchCreateExternalUser(params: any) {
  return request<ApiResult<any>>('/new-media-api/portal-user/batch-create-external', {
    method: 'POST',
    data: params,
  });
}

// 生成外部账号
export function CreateExternalUser(params: {
  projectId: string | undefined;
  fieldId: number | string;
  dataScope: any;
}) {
  return request<ApiResult<any>>('/new-media-api/portal-user/create-external', {
    method: 'POST',
    data: params,
  });
}

export async function ExportExternalUser(params: { projectId: string | undefined }) {
  return request('/new-media-api/portal-user/export-external', {
    method: 'GET',
    params,
    responseType: 'blob',
  });
}

export function EditUserPassword(params: any) {
  return request<ApiResult<any>>('/new-media-api/portal-user/edit-password', {
    method: 'POST',
    data: params,
  });
}

export function ResetUserPassword(params: any) {
  return request<ApiResult<any>>('/new-media-api/portal-user/reset-password', {
    method: 'POST',
    data: params,
  });
}
