import { request } from '@umijs/max';
import { ApiResult } from './common';
import { PlatForm } from '@/utils/platform';

export enum DateType {
  Today = 0,
  Yesterday = 1,
  LastWeek = 2,
  LastMonth = 3,
  Custom = 4,
  Last90Days = 5,
  NaturalWeek = 6,
  NaturalMonth = 7,
}

export type FieldList = {
  /**
   * 维度字段 id
   */
  fieldId?: number;
  /**
   * 维度字段值
   */
  value?: string;
};

export type BusinessBody = {
  /**
   * 时间类型
   */
  dateType?: DateType;
  /**
   * 结束日志
   */
  endDate?: string;
  /**
   * 项目 id
   */
  projectId?: string;
  /**
   * 开始日期
   */
  startDate?: string;
  /**
   * 标签主键集合
   */
  tagIdList?: (number | string)[];
  /**
   * 团队编码集合
   */
  teamCodeList?: string[];
  /**
   * 动态维度字段
   */
  fieldList?: FieldList[];
};

export enum ModuleType {
  Post = 1,
  Live = 2,
  Message = 3,
  Fans = 4,
  Clue = 5,
}

export type BusinessResult = {
  /**
   * 主播形象违规数量
   */
  anchorImageViolationCount?: number;
  /**
   * 线索数
   */
  clueCount?: number;
  /**
   * 抖音私信渠道线索转化率
   */
  douyinChatClueConversionRate?: string;
  /**
   * 抖音私信渠道线索数
   */
  douyinChatClueCount?: number;
  /**
   * 抖音私信接收数
   */
  douyinChatReceiveCount?: number;
  /**
   * 抖音私信3分钟回复数
   */
  douyinChatThreeMinReplyCount?: number;
  /**
   * 抖音私信人数
   */
  douyinChatUserCount?: number;
  /**
   * 直播空播场次
   */
  liveAfkCount?: number;
  /**
   * 直播行为违规场次
   */
  liveBehaviorCount?: number;
  /**
   * 直播渠道线索转化率
   */
  liveClueConversionRate?: string;
  /**
   * 直播渠道线索数
   */
  liveClueCount?: number;
  /**
   * 直播场次
   */
  liveCount?: number;
  /**
   * 直播点赞数
   */
  liveDiggCount?: number;
  /**
   * 直播弹幕数
   */
  liveDmCount?: number;
  /**
   * 直播时长
   */
  liveDuration?: number;
  /**
   * 直播渠道新增粉丝数
   */
  liveNewFollowerCount?: number;
  /**
   * 直播敏感词违规场次
   */
  liveSensitiveCount?: number;
  /**
   * 直播观看人数
   */
  liveViewCount?: number;
  /**
   * 直播观看人次
   */
  liveViewTime?: number;
  /**
   * 环比主播形象违规数量
   */
  momAnchorImageViolation?: number;
  /**
   * 环比率-主播形象违规
   */
  momAnchorImageViolationRate?: string;
  /**
   * 环比-线索数
   */
  momClueCount?: number;
  /**
   * 环比率-线索数率
   */
  momClueCountRate?: string;
  /**
   * 环比率-抖音私信渠道线索转化率
   */
  momDouyinChatClueConversionRate?: string;
  /**
   * 环比抖音私信渠道线索数
   */
  momDouyinChatClueCount?: number;
  /**
   * 环比率-抖音私信渠道线索数率
   */
  momDouyinChatClueCountRate?: string;
  /**
   * 环比抖音私信接收数
   */
  momDouyinChatReceiveCount?: number;
  /**
   * 环比率-抖音私信接收数
   */
  momDouyinChatReceiveCountRate?: string;
  /**
   * 环比抖音私信 3 分钟回复数
   */
  momDouyinChatThreeMinReplyCount?: number;
  /**
   * 环比率-抖音私信3分钟回复数率
   */
  momDouyinChatThreeMinReplyCountRate?: string;
  /**
   * 环比抖音私信人数
   */
  momDouyinChatUserCount?: number;
  /**
   * 环比率-抖音私信人数
   */
  momDouyinChatUserCountRate?: string;
  /**
   * 环比直播空播场次
   */
  momLiveAfkCount?: number;
  /**
   * 环比率-直播空播场次
   */
  momLiveAfkCountRate?: string;
  /**
   * 环比直播行为违规场次
   */
  momLiveBehaviorCount?: number;
  /**
   * 环比率-直播行为违规场次
   */
  momLiveBehaviorCountRate?: string;
  /**
   * 环比率-直播渠道线索转化率
   */
  momLiveClueConversionRate?: string;
  /**
   * 环比直播渠道线索数
   */
  momLiveClueCount?: number;
  /**
   * 环比率-直播渠道线索数率
   */
  momLiveClueCountRate?: string;
  /**
   * 环比直播场次
   */
  momLiveCount?: number;
  /**
   * 环比率-直播场次
   */
  momLiveCountRate?: string;
  /**
   * 环比直播点赞数
   */
  momLiveDiggCount?: number;
  /**
   * 环比率-直播点赞数
   */
  momLiveDiggCountRate?: string;
  /**
   * 直播弹幕数
   */
  momLiveDmCount?: number;
  /**
   * 环比率-直播弹幕数
   */
  momLiveDmCountRate?: string;
  /**
   * 环比直播时长
   */
  momLiveDuration?: number;
  /**
   * 环比率-直播时长
   */
  momLiveDurationRate?: string;
  /**
   * 环比直播渠道新增粉丝数
   */
  momLiveNewFollowerCount?: number;
  /**
   * 环比率-直播渠道新增粉丝数率
   */
  momLiveNewFollowerCountRate?: string;
  /**
   * 环比直播敏感词违规场次
   */
  momLiveSensitiveCount?: number;
  /**
   * 环比率-直播敏感词违规场次
   */
  momLiveSensitiveCountRate?: string;
  /**
   * 环比直播观看人数
   */
  momLiveViewCount?: number;
  /**
   * 环比率-直播观看人数
   */
  momLiveViewCountRate?: string;
  /**
   * 环比直播观看人次
   */
  momLiveViewTime?: number;
  /**
   * 环比率-直播观看人次
   */
  momLiveViewTimeRate?: string;
  /**
   * 环比新增粉丝数
   */
  momNewFollowerCount?: number;
  /**
   * 环比率-新增粉丝数率
   */
  momNewFollowerCountRate?: string;
  /**
   * 环比作品新增发布数
   */
  momNewPostCount?: number;
  /**
   * 环比率-作品新增发布数
   */
  momNewPostCountRate?: string;
  /**
   * 环比其他渠道线索数
   */
  momOtherClueCount?: number;
  /**
   * 环比率-其他渠道线索数率
   */
  momOtherClueCountRate?: string;
  /**
   * 环比其他渠道新增粉丝数
   */
  momOtherNewFollowerCount?: number;
  /**
   * 环比率-其他渠道新增粉丝数率
   */
  momOtherNewFollowerCountRate?: string;
  /**
   * 环比率-作品渠道线索转化率
   */
  momPostClueConversionRate?: string;
  /**
   * 环比作品渠道线索数
   */
  momPostClueCount?: number;
  /**
   * 环比率-作品渠道线索数率
   */
  momPostClueCountRate?: string;
  /**
   * 环比作品评论数
   */
  momPostCommentCount?: number;
  /**
   * 环比率-作品评论数
   */
  momPostCommentCountRate?: string;
  /**
   * 环比作品点赞数
   */
  momPostDiggCount?: number;
  /**
   * 环比率-作品点赞数
   */
  momPostDiggCountRate?: string;
  /**
   * 环比作品互动数
   */
  momPostInteractionCount?: number;
  /**
   * 环比率-作品互动数
   */
  momPostInteractionCountRate?: string;
  /**
   * 环比作品播放数
   */
  momPostPlayCount?: number;
  /**
   * 环比率-作品播放数
   */
  momPostPlayCountRate?: string;
  /**
   * 环比作品分享数
   */
  momPostShareCount?: number;
  /**
   * 环比率-作品分享数
   */
  momPostShareCountRate?: string;
  /**
   * 新增粉丝数
   */
  newFollowerCount?: number;
  /**
   * 作品新增发布数
   */
  newPostCount?: number;
  /**
   * 其他渠道线索数
   */
  otherClueCount?: number;
  /**
   * 其他渠道新增粉丝数
   */
  otherNewFollowerCount?: number;
  /**
   * 作品渠道线索转化率
   */
  postClueConversionRate?: string;
  /**
   * 作品渠道线索数
   */
  postClueCount?: number;
  /**
   * 作品评论数
   */
  postCommentCount?: number;
  /**
   * 作品点赞数
   */
  postDiggCount?: number;
  /**
   * 作品互动数
   */
  postInteractionCount?: number;
  /**
   * 作品播放数
   */
  postPlayCount?: number;
  /**
   * 作品分享数
   */
  postShareCount?: number;
};

export function BusinessOverview(data: BusinessBody & { platform: PlatForm }) {
  return request<ApiResult<BusinessResult>>('/new-media-api/project/account/business/overview', {
    method: 'POST',
    data,
  });
}

export type BusinessTrendResult = {
  /**
   * 名称
   */
  name: string;
  /**
   * 值
   */
  value: number[];
};

export function BusinessTrend(data: BusinessBody & { moduleType: ModuleType; platform: PlatForm }) {
  return request<ApiResult<BusinessTrendResult[]>>(
    '/new-media-api/project/account/business/trend',
    {
      method: 'POST',
      data,
    },
  );
}

export enum BusinessDataType {
  /**
   * 作品发布数
   */
  WorksPublished = 10001,

  /**
   * 作品互动总数
   */
  WorksInteractions = 10002,

  /**
   * 作品新增播放量
   */
  WorksViewsIncrement = 10003,

  /**
   * 作品新增点赞数
   */
  WorksLikesIncrement = 10004,

  /**
   * 作品新增评论数
   */
  WorksCommentsIncrement = 10005,

  /**
   * 作品新增分享数
   */
  WorksSharesIncrement = 10006,

  /**
   * 直播场次
   */
  LiveSessions = 20001,

  /**
   * 直播时长
   */
  LiveDuration = 20002,

  /**
   * 直播观看人数
   */
  LiveViewers = 20003,

  /**
   * 直播观看人次
   */
  LiveViewerTimes = 20004,

  /**
   * 直播弹幕数
   */
  LiveDanmuCount = 20005,

  /**
   * 直播点赞数
   */
  LiveLikes = 20006,

  /**
   * 空挂播场次
   */
  IdleLiveSessions = 20007,

  /**
   * 敏感词违规场次
   */
  SensitivityViolations = 20008,

  /**
   * 行为违规场次
   */
  BehavioralViolations = 20009,

  /**
   * 主播形象违规
   */
  AnchorImageViolation = 20010,

  /**
   * 粉丝总量
   */
  // FollowersTotal = 30001,

  /**
   * 新增粉丝数
   */
  FollowersIncrement = 30002,

  /**
   * 店均总粉
   */
  // AverageFollowersTotal = 30003,

  /**
   * 店均增粉
   */
  AverageFollowersIncrement = 30004,

  /**
   * 私信人数
   */
  MessagesSent = 40001,

  /**
   * 接收消息数
   */
  MessagesReceived = 40002,

  /**
   * 发送消息数
   */
  MessagesSentTotal = 40003,

  /**
   * 3min内回复数
   */
  RepliesIn3Min = 40004,

  /**
   * 3min未回复数
   */
  RepliesNotIn3Min = 40005,

  /**
   * 店均私信人数
   */
  AverageMessagesSent = 40006,

  /**
   * 线索量
   */
  LeadsGenerated = 50001,

  /**
   * 直播线索数
   */
  LeadsFromLive = 50002,

  /**
   * 作品线索数
   */
  LeadsFromWorks = 50003,

  /**
   * 私信线索总量
   */
  LeadsMessages = 50004,

  /**
   * 其他线索数
   */
  LeadsFromOther = 50005,

  /**
   * 店均线索总量
   */
  LeadsAverage = 50006,
}

export const BusinessChineseName: Record<BusinessDataType, string> = {
  [BusinessDataType.WorksPublished]: '作品发布数',
  [BusinessDataType.WorksInteractions]: '作品互动总数',
  [BusinessDataType.WorksViewsIncrement]: '作品新增播放量',
  [BusinessDataType.WorksLikesIncrement]: '作品新增点赞数',
  [BusinessDataType.WorksCommentsIncrement]: '作品新增评论数',
  [BusinessDataType.WorksSharesIncrement]: '作品新增分享数',
  [BusinessDataType.LiveSessions]: '直播场次',
  [BusinessDataType.LiveDuration]: '直播时长',
  [BusinessDataType.LiveViewers]: '直播观看人数',
  [BusinessDataType.LiveViewerTimes]: '直播观看人次',
  [BusinessDataType.LiveDanmuCount]: '直播弹幕数',
  [BusinessDataType.LiveLikes]: '直播点赞数',
  [BusinessDataType.IdleLiveSessions]: '空挂播场次',
  [BusinessDataType.SensitivityViolations]: '敏感词违规场次',
  [BusinessDataType.BehavioralViolations]: '行为违规场次',
  [BusinessDataType.AnchorImageViolation]: '主播形象违规',
  [BusinessDataType.FollowersIncrement]: '新增粉丝数',
  // [BusinessDataType.FollowersTotal]: '粉丝总量',
  // [BusinessDataType.AverageFollowersTotal]: '店均总粉',
  [BusinessDataType.AverageFollowersIncrement]: '店均增粉',
  [BusinessDataType.MessagesSent]: '私信人数',
  [BusinessDataType.MessagesReceived]: '接收消息数',
  [BusinessDataType.MessagesSentTotal]: '发送消息数',
  [BusinessDataType.RepliesIn3Min]: '3min内回复数',
  [BusinessDataType.RepliesNotIn3Min]: '3min未回复数',
  [BusinessDataType.AverageMessagesSent]: '店均私信人数',
  [BusinessDataType.LeadsGenerated]: '线索量',
  [BusinessDataType.LeadsFromLive]: '直播线索数',
  [BusinessDataType.LeadsFromWorks]: '作品线索数',
  [BusinessDataType.LeadsMessages]: '私信线索总量',
  [BusinessDataType.LeadsFromOther]: '其他线索数',
  [BusinessDataType.LeadsAverage]: '店均线索总量',
};

export type ContributionResult = {
  /**
   * 名称
   */
  name?: string;
  /**
   * 占比
   */
  percent?: string;
  /**
   * 值
   */
  value?: string;
};

export function BusinessContributionOverview(
  data: BusinessBody & {
    /**
     * 1-综合指标 2-单指标
     */
    type: number;
    fieldId?: number;
    moduleType?: ModuleType;
    dataIndexType?: BusinessDataType;
    platform: PlatForm;
  },
) {
  return request<ApiResult<ContributionResult[]>>(
    '/new-media-api/project/account/business/contribution/overview',
    {
      method: 'POST',
      data,
    },
  );
}

export type ContributionListResult = {
  /**
   * 维度 id
   */
  fieldId?: number;
  /**
   * 聚合维度
   */
  name?: string;
  /**
   * 占比
   */
  percent?: string;
  /**
   * 排名
   */
  rank?: number;
  /**
   * 维度值
   */
  value?: number;
};

export function BusinessContributionList(
  data: BusinessBody & {
    /**
     * 1-综合指标 2-单指标
     */
    type: number;
    fieldId?: number;
    moduleType?: ModuleType;
    dataIndexType?: BusinessDataType;
    platform: PlatForm;
  },
) {
  return request<ApiResult<ContributionListResult[]>>(
    '/new-media-api/project/account/business/contribution/list',
    {
      method: 'POST',
      data,
    },
  );
}

export type BusinessTop = {
  /**
   * 维度 id
   */
  fieldId: number;
  /**
   * 环比率
   */
  momRate: string;
  /**
   * 聚合维度
   */
  name: string;
  /**
   * 排名
   */
  rank: number;
  /**
   * 值
   */
  value: number;
};

export type ContributionTopResult = {
  /**
   * 降幅 top 榜
   */
  topDownList?: BusinessTop[];
  /**
   * 涨幅 top 榜
   */
  topUpList?: BusinessTop[];
};

export function BusinessContributionTop(
  data: BusinessBody & {
    /**
     * 1-综合指标 2-单指标
     */
    type: number;
    fieldId?: number;
    moduleType?: ModuleType;
    dataIndexType?: BusinessDataType;
    platform: PlatForm;
  },
) {
  return request<ApiResult<ContributionTopResult>>(
    '/new-media-api/project/account/business/contribution/top',
    {
      method: 'POST',
      data,
    },
  );
}

export type DataIndexDetail = {
  /**
   * 数据指标类型
   */
  dataIndexType: BusinessDataType;
  /**
   * 环比率
   */
  momRate: string;
  /**
   * 值
   */
  value: string;
};

export type BusinessDetailResult = {
  /**
   * 表单数据
   */
  dataIndexDetailList?: DataIndexDetail[];
  /**
   * 动态维度
   */
  name?: string;
  /**
   * 排名
   */
  rank?: number;
};

export function BusinessDetail(
  data: BusinessBody & {
    /**
     * 1-综合指标 2-单指标
     */
    type: number;
    fieldId?: number;
    moduleType?: ModuleType;
    dataIndexType?: BusinessDataType;
    fieldList?: {
      fieldId?: number;
      value?: string;
    }[];
    platform: PlatForm;
  },
) {
  return request<ApiResult<BusinessDetailResult[]>>(
    '/new-media-api/project/account/business/detail',
    {
      method: 'POST',
      data,
    },
  );
}

export function BusinessDetailExport(
  data: BusinessBody & {
    /**
     * 1-综合指标 2-单指标
     */
    type: number;
    fieldId?: number;
    moduleType?: ModuleType;
    dataIndexType?: BusinessDataType;
    fieldList?: {
      fieldId?: number;
      value?: string;
    }[];
    platform: PlatForm;
  },
) {
  return request<ApiResult<string>>('/new-media-api/project/account/business/detail/export', {
    method: 'POST',
    data,
  });
}

export function GetDataUpdateTime(params: { projectId?: string }) {
  return request<ApiResult<any>>('/new-media-api/project/account/business/production/date', {
    method: 'GET',
    params,
  });
}

export type BusinessTipsResult = {
  /**
   * 聚合名称列表
   */
  aggregationNameList: string[];
  /**
   * 关注类型 1-直播 2-作品 3-私信
   */
  focusType: number;
  /**
   * 环比类型 1-环比上升 2-环比下降
   */
  momType: number;
  /**
   * tip 类型 1-线索总量 2-涨粉总量
   */
  tipType: number;
};

export function BusinessTips(data: BusinessBody & { platform: PlatForm }) {
  return request<ApiResult<BusinessTipsResult[]>>('/new-media-api/project/account/business/tips', {
    method: 'POST',
    data,
  });
}
