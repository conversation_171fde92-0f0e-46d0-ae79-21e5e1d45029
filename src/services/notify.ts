import { request } from '@umijs/max';
import { ReactNode } from 'react';
import { ApiResult, PageBasicParams } from './common';
import { PlatForm } from '@/utils/platform';

export type QualityNotifyItem = {
  /**
   * 违规音频
   */
  audioUrl?: string;
  /**
   * 违规内容
   */
  content?: string;
  /**
   * 发送失败原因
   */
  failReason?: string;
  /**
   * 空播挂播等级
   */
  liveAfkLevel?: number;
  /**
   * 质检监控词
   */
  mark?: string;
  /**
   * 账号昵称
   */
  nickname?: string;
  /**
   * 提醒时间
   */
  notifyTime?: string;
  /**
   * 违规图片
   */
  picUrl?: string;
  /**
   * 质检场景
   */
  qualityRuleType: number;
  /**
   * 违规标记时间
   */
  qualityTime?: string;
  /**
   * 发送状态 1: 成功 2: 失败
   */
  sendStatus?: number;
  /**
   * 目标 id
   */
  targetId?: string;
  /**
   * 违规类型
   */
  targetType?: number;
  /**
   * 团队名称
   */
  teamName?: string;
  /**
   * 标题
   */
  title?: string;
  /**
   * 质检通知类型 1: 普通违规 2：空播挂播违规
   */
  type: number;
  /**
   * 质检类型
   */
  violationNameStr?: string;
};

export type GetQualityNotifyRes = {
  items: QualityNotifyItem[];
  total: number;
};

export type ChatNotifyItem = {
  /**
   * 账号 id
   */
  accountId: string;
  /**
   * 私信用户昵称
   */
  chatUserNickname: string;
  /**
   * 私信内容
   */
  content: string;
  /**
   * 失败原因
   */
  failReason: string;
  /**
   * 私信关键词
   */
  keyword: string;
  /**
   * 来源账号昵称
   */
  nickname: string;
  /**
   * 提醒原因
   */
  notifyChatType: number;
  /**
   * 提醒时间
   */
  notifyTime: string;
  /**
   * 私信时间
   */
  sendAt: string;
  /**
   * 用户 openId
   */
  senderOpenId: string;
  /**
   * 提醒结果
   */
  sendStatus: number;
  /**
   * 团队名称
   */
  teamName: string;
};

export type GetChatNotifyRes = {
  items: ChatNotifyItem[];
  total: number;
};

export type GetNotifyOverviewRes = {
  notifyCount?: number;
  notifyFailCount?: number;
  notifySuccessCount?: number;
};

export type NotifySwitch = {
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 主键
   */
  id: number;
  /**
   * 推送频率 1-实时 2-每小时 3-每天
   */
  intervalType?: number;
  /**
   * 项目id
   */
  projectId: number;
  /**
   * 开关状态 0-关闭 1-开启
   */
  status?: number;
  /**
   * 开关 code
   */
  switchCode?: string;
  /**
   * 更新人
   */
  updatedBy?: string;
  /**
   * 更新时间
   */
  updateTime?: string;
  /**
   * 业务模块
   */
  bizModel?: string;
  /**
   * 提醒对象
   */
  remindTarget?: string;
};

export type RobotBindRelationItem = {
  /**
   * 添加机器人时间
   */
  bindTime?: Date;
  /**
   * 创建时间
   */
  createTime?: Date;
  /**
   * 好友状态 1-已添加 2-未添加 3-已删除
   */
  friendStatus?: number;
  /**
   * 团队识别码
   */
  teamIdentificationCode?: string;
  /**
   * 被提醒微信号
   */
  teamWechatId?: string;
  /**
   * 机器人微信号
   */
  wechatRobotId?: string;
  /**
   * 机器人微信昵称
   */
  wechatRobotName?: string;
  id: string;
};

export type GetRobotBindRelationRes = {
  items?: RobotBindRelationItem[];
  total?: number;
};

export type NotifyRobotListItem = {
  /**
   * 绑定类型 1-飞书 2-钉钉 3-企业微信 4-微信 5-其他
   */
  bindType: number;
  /**
   * 维度id
   */
  fieldId: string;
  /**
   * 维度值
   */
  fieldValue: string;
  /**
   * 主键
   */
  id: number;
  /**
   * 项目id
   */
  projectId: number;
  /**
   * 机器人名称
   */
  robotName: string;
  /**
   * 团队编码
   */
  teamCode: string;
  /**
   * 类型 1: 团队 2: 大区 3: 总部
   */
  type: number;
  /**
   * webhook 地址
   */
  webhook: string;
  /**
   * 作用类型
   */
  actionType: number;
};

export type QualityNotify = {
  /**
   * 空播等级
   */
  liveAfkLevel?: string[];
  /**
   * 通知发送结束时间
   */
  notifyEndDate?: string;
  /**
   * 通知发送开始时间
   */
  notifyStartDate?: string;
  /**
   * 平台
   */
  platform: PlatForm;
  /**
   * 项目id
   */
  projectId?: string;
  /**
   * 质检类别
   */
  qualityCategoryTypeList?: string[];
};

export function GetQualityNotify(params: QualityNotify & PageBasicParams) {
  return request<ApiResult<GetQualityNotifyRes>>('/new-media-api/notify/record/quality/page', {
    method: 'GET',
    params,
  });
}

export function GetChatNotify(params: QualityNotify & PageBasicParams) {
  return request<ApiResult<GetChatNotifyRes>>('/new-media-api/notify/record/chat/page', {
    method: 'GET',
    params,
  });
}

export type NotifyOverview = {
  /**
   * 结束日期
   */
  endDate?: string;
  /**
   * 平台
   */
  platform: PlatForm;
  /**
   * 项目id
   */
  projectId?: string;
  /**
   * 开始日期
   */
  startDate?: string;
  /**
   * 1-质检实时提醒 2-私信实时提醒
   */
  type: number;
};

export function GetNotifyOverview(params: NotifyOverview) {
  return request<ApiResult<GetNotifyOverviewRes>>('/new-media-api/notify/record/overview', {
    method: 'GET',
    params,
  });
}

export function ImportBindRobot(params: { projectId?: string }, data: any) {
  return request<ApiResult<any>>('/new-media-api/notify/robot/wechat/bind/import', {
    method: 'POST',
    params,
    data,
  });
}

export function DownloadNotifyRobotTemplate() {
  return request('/new-media-api/notify/robot/download-wechat-template', {
    method: 'GET',
    responseType: 'blob',
  });
}

type CreateRobotParams = {
  /**
   * 绑定类型
   */
  bindType?: number;
  /**
   * 维度 id
   */
  fieldId?: string;
  /**
   * 维度值
   */
  fieldValue?: ReactNode;
  /**
   * 项目 id
   */
  projectId?: string;
  /**
   * 机器人名称
   */
  robotName: string;
  /**
   * 团队编码
   */
  teamCode?: string;
  /**
   * 类型 1: 团队 2: 大区 3: 总部
   */
  type: number;
  /**
   * 机器人webhook
   */
  webhook: string;
  /**
   * 作用类型 1-实时提醒 2-日报
   */
  actionType: number;
};

export function CreateRobot(data: CreateRobotParams) {
  return request<ApiResult<any>>('/new-media-api/notify/robot/create', {
    method: 'POST',
    data,
  });
}

type CopyRobotParams = CreateRobotParams & {
  /**
   * 复制的机器人对象id
   */
  copyNotifyRobotId: number;
};

export function CopyRobot(data: CopyRobotParams) {
  return request<ApiResult<any>>('/new-media-api/notify/robot/copy', {
    method: 'POST',
    data,
  });
}

export function GetRobotBindRelation(params: any) {
  return request<ApiResult<GetRobotBindRelationRes>>(
    '/new-media-api/notify/robot/wechat/bind/relation',
    {
      method: 'GET',
      params,
    },
  );
}

export function DeleteBindRobot(data: any) {
  return request<ApiResult<any>>('/new-media-api/notify/robot/wechat/bind/delete', {
    method: 'POST',
    data,
  });
}

export function ExportQualityRecord(params: any) {
  return request('/new-media-api/notify/record/quality/export', {
    method: 'GET',
    params,
  });
}

export function ExportChatRecord(params: any) {
  return request('/new-media-api/notify/record/chat/export', {
    method: 'GET',
    params,
  });
}

export function GetNotifyRobotList(params: { projectId?: string; type: number }) {
  return request<ApiResult<NotifyRobotListItem[]>>('/new-media-api/notify/robot/list', {
    method: 'GET',
    params,
  });
}

export function DeleteNotifyRobot(params: { notifyRobotId: number }) {
  return request<ApiResult<any>>('/new-media-api/notify/robot/delete', {
    method: 'POST',
    params,
  });
}

export function UpdateNotifyRobot(data: any) {
  return request<ApiResult<any>>('/new-media-api/notify/robot/update', {
    method: 'POST',
    data,
  });
}

// 提醒信息部分的编辑更新
export function UpdateNotifyRobotBind(data: any) {
  return request<ApiResult<any>>('/new-media-api/notify/robot/wechat/bind/update', {
    method: 'POST',
    data,
  });
}

export type CheckNotifyRobotBindRes = {
  /**
   * 好友状态 1-已添加 2-未添加 3-已删除
   */
  friendStatus?: number;
  /**
   * 添加类型 1-新增 2-切换
   */
  importStatus?: number;
  /**
   * 团队识别码
   */
  teamIdentificationCode?: string;
  /**
   * 微信号
   */
  teamWechatId?: string;
};

export function CheckNotifyRobotBind(params: any) {
  return request<ApiResult<CheckNotifyRobotBindRes>>(
    '/new-media-api/notify/robot/wechat/bind/relation-check',
    {
      method: 'GET',
      params,
    },
  );
}

export type GetNotifyRobotBindStatusRes = {
  /**
   * 已添加微信机器人数量
   */
  addCount?: number;
  /**
   * 已绑定微信机器人数量
   */
  bindCount?: number;
  /**
   * 未添加微信机器人数量
   */
  unAddCount?: number;
  /**
   * 未绑定微信机器人数量
   */
  unBindCount?: number;
};

export function GetNotifyRobotBindStatus(params: { projectId?: string }) {
  return request<ApiResult<GetNotifyRobotBindStatusRes>>(
    '/new-media-api/notify/robot/wechat/bind/status',
    {
      method: 'GET',
      params,
    },
  );
}

export function ExportNotifyRobotBind(params: any) {
  return request('/new-media-api/notify/robot/wechat/bind/relation/export', {
    method: 'GET',
    params,
    responseType: 'blob',
  });
}

export type NotifyConfigList = {
  /**
   * 绑定类型 1-飞书 2-钉钉 3-企业微信 4-微信 5-其他
   */
  bindType: number;
  /**
   * 通知机器人id
   */
  id: number;
  /**
   * 通知类型 1:个微 2:群组
   */
  notifyType: number;
  /**
   * 对象类型 1: 默认 2: 自定义
   */
  objectType: number;
  /**
   * 名称
   */
  robotName: string;
  /**
   * webhook地址
   */
  webhook: string;
  /**
   * 作用类型
   */
  actionType: number;
};

export function GetNotifyConfigList(params: {
  /**
   * 作用类型 1-实时提醒 2-日报
   */
  actionType: number;
  /**
   * 项目 id
   */
  projectId?: string;
}) {
  return request<ApiResult<NotifyConfigList[]>>('/new-media-api/notify-config/list/object', {
    method: 'GET',
    params,
  });
}

export type NotifyConfigData = {
  /**
   * 配置项
   */
  configItemList?: ConfigItem[];
  /**
   * 通知类型 1:个微 2:群组
   */
  notifyType?: number;
  /**
   * 项目id
   */
  projectId?: number;
  /**
   * 推送结束时间
   */
  pushEndTime?: string;
  /**
   * 推送开始时间
   */
  pushStartTime?: string;
};

export type ConfigItem = {
  /**
   * 通知编码
   */
  code?: string | number;
  /**
   * 推送频率 1-实时 2-每小时 3-每天
   */
  intervalType?: number;
  /**
   * 是否禁用 0-正常 1-禁用
   */
  isDisable?: number;
  /**
   * 推送系数
   */
  pushCoefficient?: number;
};

export function GetNotifyConfigQuery(params: {
  /**
   * 作用类型 1-实时提醒 2-日报
   */
  actionType: number;
  /**
   * 通知机器人主键id
   */
  id?: string;
  /**
   * 通知类型 1:个微 2:群组
   */
  notifyType: number;
  /**
   * 对象类型 1: 默认 2: 自定义
   */
  objectType: number;
  /**
   * 项目id
   */
  projectId?: string;
}) {
  return request<ApiResult<NotifyConfigData>>('/new-media-api/notify-config/query-config', {
    method: 'GET',
    params,
  });
}

type UpdateNotifyConfigParams = {
  /**
   * 作用类型 1-实时提醒 2-日报
   */
  actionType: number;
  /**
   * 配置项
   */
  configItemList: ConfigItem[];
  /**
   * 机器人主键id
   */
  id?: number | string;
  /**
   * 通知类型 1:个微 2:群组
   */
  notifyType: number;
  /**
   * 对象类型 1: 默认 2: 自定义
   */
  objectType: number;
  /**
   * 项目id
   */
  projectId?: string;
};

export function UpdateNotifyConfig(data: UpdateNotifyConfigParams) {
  return request<ApiResult<any>>('/new-media-api/notify-config/update-config', {
    method: 'POST',
    data,
  });
}

export type UpdateNotifyPushTime = {
  /**
   * 机器人主键id
   */
  id?: number | string;
  /**
   * 通知类型 1:个微 2:群组
   */
  notifyType: number;
  /**
   * 对象类型 1: 默认 2: 自定义
   */
  objectType: number;
  /**
   * 项目id
   */
  projectId?: string;
  /**
   * 推送结束时间
   */
  pushEndTime: string;
  /**
   * 推送开始时间
   */
  pushStartTime: string;
  /**
   * 作用类型 1-实时提醒 2-日报
   */
  actionType: number;
};

export function UpdateNotifyPushTime(data: UpdateNotifyPushTime) {
  return request<ApiResult<any>>('/new-media-api/notify-config/update/push-time', {
    method: 'POST',
    data,
  });
}
