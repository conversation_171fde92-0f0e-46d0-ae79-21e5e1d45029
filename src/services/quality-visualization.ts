import { request } from '@umijs/max';
import { ApiResult } from './common';
import { PlatForm } from '@/utils/platform';
import { PaginationParam } from './price';

export type QualityOverview = {
  /**
   * 聚合数量
   */
  aggregationCount?: number;
  /**
   * 违规聚合数量
   */
  violationAggregationCount?: number;
  /**
   * 违规聚合数量环比
   */
  violationAggregationCountMom?: string;
};

type QualityBody = {
  /**
   * 聚合类型 1-场次/条 2-门店数 3-账号数
   */
  aggregationType?: number;
  /**
   * 时间类型 1-近一天 2-近一周 3-近一月 4-自定义
   */
  dateType: number;
  /**
   * 动态维度字段
   */
  fieldList?: {
    fieldId?: number;
    value?: string;
  }[];
  /**
   * 项目 id
   */
  projectId?: string;
  /**
   * 开始日期
   */
  startDate?: string;
  /**
   * 结束日期
   */
  endDate?: string;
  /**
   * 平台
   */
  platform: PlatForm;
};

export function GetQualityLiveOverview(data: QualityBody) {
  return request<ApiResult<QualityOverview>>('/new-media-api/quality/live/bi/overview', {
    method: 'POST',
    data,
  });
}

export type QualityOverviewTrend = {
  /**
   * 数量
   */
  count?: number;
  /**
   * 日期
   */
  date?: string;
};

export function GetQualityLiveOverviewTrend(data: QualityBody) {
  return request<ApiResult<QualityOverviewTrend[]>>(
    '/new-media-api/quality/live/bi/overview/trend',
    {
      method: 'POST',
      data,
    },
  );
}

export type QualityOverviewAggregation = {
  /**
   * 数量
   */
  count?: number;
  /**
   * 高频监控词-逗号分割
   */
  highFrequencyMonitoringWordStr?: string;
  /**
   * 轻微空播数量
   */
  minorAfkLiveCount?: number;
  /**
   * 质检类型-逗号分割
   */
  monitoringWordNameStr?: string;
  /**
   * 聚合维度名称
   */
  name?: string;
  /**
   * 严重空播数量
   */
  seriousAfkLiveCount?: number;
  /**
   * 违规数量
   */
  violationCount?: number;
  /**
   * 违规数量环比
   */
  violationMom?: string;
};

type AggregationData = QualityBody & { fieldId: number };

export function GetQualityLiveOverviewAggregation(params: PaginationParam, data: AggregationData) {
  return request<ApiResult<QualityOverviewAggregation[]>>(
    '/new-media-api/quality/live/bi/overview/aggregation',
    {
      method: 'POST',
      params,
      data,
    },
  );
}

export type MonitorWrodTrendRes = {
  /**
   * 数量集合
   */
  countList?: number[];
  /**
   * 分类名称
   */
  name?: string;
};

export function GetQualityLiveMonitorWrodTrend(data: QualityBody) {
  return request<ApiResult<MonitorWrodTrendRes[]>>(
    '/new-media-api/quality/live/bi/monitoring-word-name/trend',
    {
      method: 'POST',
      data,
    },
  );
}

export type MonitorWrodCountRes = {
  /**
   * 分类 id
   */
  monitoringWordId?: number;
  /**
   * 分类名称
   */
  name?: string;
  /**
   * 分类数量
   */
  count?: number;
};

export function GetQualityLiveMonitorWrodCount(data: QualityBody) {
  return request<ApiResult<MonitorWrodCountRes[]>>(
    '/new-media-api/quality/live/bi/monitoring-word-name/count',
    {
      method: 'POST',
      data,
    },
  );
}

export type QualityLiveAfkOverview = {
  /**
   * 全部疑似空播数量
   */
  afkCount?: number;
  /**
   * 全部疑似空播占比
   */
  afkCountRate?: string;
  /**
   * 轻微疑似空播数量
   */
  minorAfkCount?: number;
  /**
   * 轻微疑似空播占比
   */
  minorAfkCountRate?: string;
  /**
   * 严重疑似空播数量
   */
  seriousAfkCount?: number;
  /**
   * 严重疑似空播占比
   */
  seriousAfkCountRate?: string;
};

export function GetQualityLiveAfkOverview(data: QualityBody) {
  return request<ApiResult<QualityLiveAfkOverview>>(
    '/new-media-api/quality/live/bi/afk-live/overview',
    {
      method: 'POST',
      data,
    },
  );
}

export type QualityLiveAfkTrendRes = {
  /**
   * 轻微疑似空播数量趋势列表
   */
  minorAfkCountList?: number[];
  /**
   * 严重疑似空播数量趋势列表
   */
  seriousAfkCountList?: number[];
};

export function GetQualityLiveAfkTrend(data: QualityBody) {
  return request<ApiResult<QualityLiveAfkTrendRes>>(
    '/new-media-api/quality/live/bi/afk-live/trend',
    {
      method: 'POST',
      data,
    },
  );
}

type AfkAggregationData = QualityBody & { fieldId?: number; afkLevel: number | null };

export type AggregationRes = {
  count?: number;
  name?: string;
};

export function GetQualityLiveAfkAggregation(data: AfkAggregationData) {
  return request<ApiResult<AggregationRes[]>>(
    '/new-media-api/quality/live/bi/afk-live/overview/aggregation',
    {
      method: 'POST',
      data,
    },
  );
}

type MonitorWrodAggregationData = QualityBody & { fieldId?: number; monitoringWordId?: number };

export function GetQualityLiveMonitorWordAggregation(data: MonitorWrodAggregationData) {
  return request<ApiResult<AggregationRes[]>>(
    '/new-media-api/quality/live/bi/monitoring-word/overview/aggregation',
    {
      method: 'POST',
      data,
    },
  );
}

export type MonitorOverviewRes = {
  /**
   * 数量
   */
  count: number;
  /**
   * 违规词
   */
  monitoringWord: string;
};

export function GetQualityLiveMonitorOverview(data: MonitorWrodAggregationData) {
  return request<ApiResult<MonitorOverviewRes[]>>(
    '/new-media-api/quality/live/bi/monitoring-word/overview',
    {
      method: 'POST',
      data,
    },
  );
}

export function ExportQualityLiveOverviewAggregation(data: any) {
  return request<ApiResult<string>>('/new-media-api/quality/live/bi/overview/aggregation/export', {
    method: 'POST',
    data,
  });
}

export function GetQualityPostOverview(data: QualityBody) {
  return request<ApiResult<QualityOverview>>('/new-media-api/quality/post/bi/overview', {
    method: 'POST',
    data,
  });
}

export function GetQualityPostOverviewTrend(data: QualityBody) {
  return request<ApiResult<QualityOverviewTrend[]>>(
    '/new-media-api/quality/post/bi/overview/trend',
    {
      method: 'POST',
      data,
    },
  );
}

export function GetQualityPostMonitorWrodTrend(data: QualityBody) {
  return request<ApiResult<MonitorWrodTrendRes[]>>(
    '/new-media-api/quality/post/bi/monitoring-word-name/trend',
    {
      method: 'POST',
      data,
    },
  );
}

export function GetQualityPostMonitorWrodCount(data: QualityBody) {
  return request<ApiResult<MonitorWrodCountRes[]>>(
    '/new-media-api/quality/post/bi/monitoring-word-name/count',
    {
      method: 'POST',
      data,
    },
  );
}

export function GetQualityPostMonitorWordAggregation(data: MonitorWrodAggregationData) {
  return request<ApiResult<AggregationRes[]>>(
    '/new-media-api/quality/post/bi/monitoring-word/overview/aggregation',
    {
      method: 'POST',
      data,
    },
  );
}

export function GetQualityPostOverviewAggregation(params: PaginationParam, data: AggregationData) {
  return request<ApiResult<QualityOverviewAggregation[]>>(
    '/new-media-api/quality/post/bi/overview/aggregation',
    {
      method: 'POST',
      data,
      params,
    },
  );
}

export function ExportQualityPostOverviewAggregation(data: any) {
  return request<ApiResult<string>>('/new-media-api/quality/post/bi/overview/aggregation/export', {
    method: 'POST',
    data,
  });
}

export function GetQualityPostMonitorOverview(data: MonitorWrodAggregationData) {
  return request<ApiResult<MonitorOverviewRes[]>>(
    '/new-media-api/quality/post/bi/monitoring-word/overview',
    {
      method: 'POST',
      data,
    },
  );
}
