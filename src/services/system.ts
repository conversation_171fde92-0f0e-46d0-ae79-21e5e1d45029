import { request } from '@umijs/max';
import { ApiResult } from './common';
import { ProjectDataItem } from './project';

export type RoleData = {
  id: string;
  name: string;
  code: string;
  type: 0 | 1;
  remarks: string;
  createBy: null;
  updateBy: null;
  createTime: string;
  updateTime: null;
  userCount: number;
};

export async function GetRoleSelect() {
  return request<ApiResult<any>>('/new-media-api/system/role/select', {
    method: 'GET',
  });
}

export async function GetRoleList() {
  return request<ApiResult<RoleData[]>>('/new-media-api/system/role/list', {
    method: 'GET',
  });
}

export async function GetRolePermission(params: { roleCode: string }) {
  return request<ApiResult<string[]>>('/new-media-api/system/role/permission/code', {
    method: 'GET',
    params,
  });
}

export async function CreateRole(params: any) {
  return request<ApiResult>('/new-media-api/system/role/save', {
    method: 'POST',
    data: params,
  });
}

export async function DeleteRole(params: any) {
  return request<ApiResult>(`/new-media-api/system/role/delete/${params}`, {
    method: 'POST',
  });
}

export async function GrantRole(params: any) {
  return request<ApiResult>('/new-media-api/system/role/grant', {
    method: 'POST',
    data: params,
  });
}

export type Config = {
  'behavior-violate': string;
  [property: string]: any;
};

export enum FunctionCode {
  /** 质检字幕车型报价高亮 */
  QualityCheckSubtitleHighlight = '100001',

  /** 空挂播识别 */
  EmptyBroadcastRecognition = '100002',

  /** 主播形象识别 */
  AnchorImageRecognition = '100003',

  /** 直播OCR识别 */
  LiveOCRRecognition = '100004',

  /** 弹幕分析 */
  BarrageAnalysis = '100005',

  /** 直播内容分析 */
  LiveAnalysis = '100006',

  /** 作品分析 */
  ProjectWorksAnalysis = '100007',

  /** 质检审核 */
  QualityCheck = '100008',

  /** 团队总览 */
  TeamOverview = '100009',

  /** 矩阵分发 */
  Distribute = '100010',

  /** AI留资 */
  AIClue = '210001',

  /** AI助播 */
  AIAssistant = '210002',

  /** AI短视频 */
  AIShortVideo = '210003',

  /** 私信管理 */
  PrivateMessage = '310001',
}

export type SystemFunctionResult = {
  /**
   * 功能编码
   */
  functionCode?: FunctionCode;
  /**
   * 功能名称
   */
  functionName?: string;
  /**
   * 是否禁用 0-正常 1-禁用
   */
  isDisabled?: number;
  /**
   * 项目id
   */
  projectId?: number;
};

export function GetSystemFunction(params: { projectId?: string | null }) {
  return request<ApiResult<SystemFunctionResult[]>>('/new-media-api/system/function/project/list', {
    method: 'GET',
    params,
  });
}

export function GetSystemFunctionDaily(params: { payload?: string | null }) {
  return request<ApiResult<SystemFunctionResult[]>>(
    '/new-media-api/system/function/project/list/daily',
    {
      method: 'GET',
      params,
    },
  );
}

export function UpdateSystemFunction(data: {
  /**
   * 项目id
   */
  projectId?: string;
  /**
   * 功能编码
   */
  functionCode: FunctionCode;
  /**
   * 是否禁用 0-正常 1-禁用
   */
  isDisabled: number;
}) {
  return request<ApiResult<SystemFunctionResult[]>>(
    '/new-media-api/system/function/project/update',
    {
      method: 'POST',
      data,
    },
  );
}

export function GetProjectListByFunctionCode(params: { functionCode: FunctionCode }) {
  return request<ApiResult<ProjectDataItem[]>>('/new-media-api/system/function/project/list/code', {
    method: 'GET',
    params,
  });
}
