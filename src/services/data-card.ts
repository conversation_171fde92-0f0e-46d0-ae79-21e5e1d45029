import { request } from '@umijs/max';
import { ApiResult } from './common';

export type DataCardTrend = {
  /**
   * 结束日期
   */
  endDate?: string;
  /**
   * 平台
   */
  platform: number;
  /**
   * 项目 id
   */
  projectId?: string;
  /**
   * 开始日期
   */
  startDate?: string;
  /**
   * 趋势类型
   * 101 :账号总数
   * 102 :账号粉丝总数
   * 103 :账号获赞总数
   * 104 :账号作品总数
   * 201 :作品总数
   * 202 :作品播放总数
   * 203 :作品点赞总数
   * 204 :作品评论总数
   * 205 :作品收藏数
   * 206 :作品分享总数
   * 301 :直播场次总数
   * 302 :直播观看人数
   * 303 :直播观看人次
   * 304 :直播时长
   * 305 :直播点赞总数
   * 306 :直播弹幕数
   * 307 :直播粉丝增量
   * 308 :大于等于 25分钟直播场次数
   * 309 :大于等于 25分钟直播时长
   * 310 :场均直播时长
   * 311 :互动次数
   * 312 :场均观看人数
   * 313 :场均观看次数
   * 314 :场均互动次数
   * 315 :互动率
   * 316 :直播间线索数
   * 317 :场均直播线索量
   * 318 :留资率
   * 319 :投放消耗
   * 320 :投流 cpl
   * 321 :大于等于 60分钟直播场次数
   * 322 :大于等于 60 分钟直播时长
   * 401 :作品质检-门店总数
   * 402 :作品质检-作品总数
   * 403 :作品质检-违规作品总数
   * 404 :作品质检-作品违规率
   * 501 :直播质检-直播场次总数
   * 502 :直播质检-直播违规场次
   * 503 :直播质检-直播违规率
   * 504 :直播质检-空播时长
   * 505 :直播质检-空播率
   * 601 :抖音客服-机器人会话量
   * 602 :抖音客服-机器人回复消息数
   * 603 :抖音客服-机器人参与留资数据
   * 604 :抖音客服-机器人参与留资率
   * 605 :抖音客服-人工客服会话总数
   * 606 :抖音客服-人工客服咨询人数
   * 607 :抖音客服-人工客服消息数
   * 608 :抖音客服-人工客服留资数
   * 701 :千川-整体消耗
   * 702 :千川-整体成交金额
   * 703 :千川-整体成交订单数
   * 704 :千川-整体支付ROI
   * 705 :千川-整体成交订单成本
   * 706 :千川-用户实际支付金额
   * 707 :千川-整体成交智能优惠券金额
   */
  type: number;
};

export type CardTrend = {
  /**
   * 总数
   */
  count: string;
  /**
   * 环比率
   */
  momRate: number;
  /**
   * 趋势
   */
  trendList: {
    /**
     * 日期
     */
    date: string;
    /**
     * 数量
     */
    count: string;
  }[];
};

export function GetDataCardTrend(params: DataCardTrend) {
  return request<ApiResult<CardTrend>>('/new-media-api/data/card-trend/query', {
    method: 'GET',
    params,
  });
}
