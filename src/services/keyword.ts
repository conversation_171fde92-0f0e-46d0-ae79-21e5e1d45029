import { request } from '@umijs/max';
import { ApiResult } from './common';

export interface Tags {
  name?: string;
  words?: string[];
}
interface Keywords {
  id?: number;
  projectId?: string;
  tags?: Tags[];
  status?: number;
}

interface KeywordsQO {
  projectId?: string;
  tags?: Tags[];
}

export async function addBulletKeyword(body: KeywordsQO) {
  return request<ApiResult<Keywords>>('/new-media-api/keywords/save', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
  });
}

export async function getKeywordList(params: { projectId?: string }) {
  return request<ApiResult<Keywords>>('/new-media-api/keywords/list', {
    method: 'GET',
    params: {
      ...params,
    },
  });
}
