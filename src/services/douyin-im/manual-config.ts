import { request } from '@umijs/max';
import { ApiResult } from '../common';
import { KnowledgeItem } from '../message-robot';

type DouyinImManualConfigUpdateDTO = {
  /** 项目id */
  projectId?: string;
  /** 生效时间 格式 ['mm:ss', 'mm:ss'] */
  effectTime?: string[];
  /** 接待不及时智能客服介入 0-不启用 1-启用 */
  robotInterventionStatus?: number;
  /** 接待不及时智能客服介入时间 单位-分钟 */
  robotInterventionTime?: number;
  /** 来源账号分配规则状态 0-不启用 1-启用 */
  sourceAccountAssignRuleStatus?: number;
  /** 分流规则 1-接待量不饱和的客服优先 2-轮流分配 */
  shuntRule?: number;
  /** 接待客服列表 */
  receptionUserList?: Pick<DouyinImManualTeam, 'sysUserId' | 'receptionLimit'>[];
};

export type DouyinImManualConfig = {
  /** 项目id */
  projectId?: number;
  /** 生效时间 格式 ['mm:ss', 'mm:ss'] */
  effectTime?: string[];
  /** 接待不及时智能客服介入 0-不启用 1-启用 */
  robotInterventionStatus?: number;
  /** 接待不及时智能客服介入时间 单位-分钟 */
  robotInterventionTime?: number;
  /** 来源账号分配规则状态 0-不启用 1-启用 */
  sourceAccountAssignRuleStatus?: number;
  /** 分流规则 1-接待量不饱和的客服优先 2-轮流分配 */
  shuntRule?: number;
  /** 接待客服列表 */
  receptionUserList?: DouyinImManualTeam[];
};

export type DouyinImManualTeam = {
  /** 系统用户 id */
  sysUserId?: number;
  /** 账号昵称 */
  name?: string;
  /** 邮箱 */
  email?: string;
  /** 接待上限 */
  receptionLimit?: number;
  /**
   * 用户类型 0-邮箱 1-飞书 2-外部用户
   */
  userType?: number;
};

type DouyinPrivateMessageGuideCardConfigDTO = {
  /** 项目id */
  projectId?: string;
  /**
   * 主动引导配置
   */
  activeGuideConfig: {
    /** 引导文案 */
    guideTitle: string;
    /** 引导问题 */
    questionList: KnowledgeItem[];
  };
};

type DouyinManualConfigActiveGuide = {
  /**
   * 主动引导开关状态 0 关闭 1 开启
   */
  activeGuideStatus?: number;
  /**
   * 引导文案
   */
  guideTitle: string;
  /**
   * 引导问题
   */
  questionList: KnowledgeItem[];
};

/** 查询主动引导配置 GET /douyin/im/manual/config/active-guide */
export async function ActiveGuideQuery(params: { projectId?: string }) {
  return request<ApiResult<DouyinManualConfigActiveGuide>>(
    `/new-media-api/douyin/im/manual/config/active-guide`,
    {
      method: 'GET',
      params,
    },
  );
}

/** 更新主动引导配置 POST /douyin/im/manual/config/active-guide/update */
export async function ActiveGuideUpdate(body: DouyinPrivateMessageGuideCardConfigDTO) {
  return request<ApiResult<boolean>>(`/new-media-api/douyin/im/manual/config/active-guide/update`, {
    method: 'POST',
    data: body,
  });
}

/** 查询配置 GET /douyin/im/manual/config/query */
export async function ManualConfigQuery(params: { projectId?: string }) {
  return request<ApiResult<DouyinImManualConfig>>(`/new-media-api/douyin/im/manual/config/query`, {
    method: 'GET',
    params,
  });
}

/** 更新配置 POST /douyin/im/manual/config/update */
export async function ManualConfigUpdate(body: DouyinImManualConfigUpdateDTO) {
  return request<ApiResult<boolean>>(`/new-media-api/douyin/im/manual/config/update`, {
    method: 'POST',
    data: body,
  });
}

export async function ActiveGuideSwitch(body: { projectId?: string; status: number }) {
  return request<ApiResult<boolean>>(
    `/new-media-api/douyin/im/manual/config/active-guide/switch-status`,
    {
      method: 'POST',
      data: body,
    },
  );
}
