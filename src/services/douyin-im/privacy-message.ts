import { request } from '@umijs/max';
import { ApiResult } from '../common';

type Tag = {
  /**
   * 组 id
   */
  groupId?: number;
  id?: number;
  /**
   * 标签名字
   */
  name?: string;
  /**
   * 顺序
   */
  pos?: number;
};

type DouyinPrivateMessageCustomer = {
  /**
   * 地址
   */
  address?: string;
  /**
   * 年龄
   */
  age?: number;
  /**
   * 生日
   */
  birthday?: string;
  /**
   * 会话id
   */
  conversationId?: string;
  /**
   * 客户抖音 open id
   */
  customerOpenId?: string;
  /**
   * 客户抖音头像地址
   */
  douyinAvatar?: string;
  /**
   * 客户抖音昵称
   */
  douyinNickname?: string;
  /**
   * 性别 1-男性 2-女性 0-未知
   */
  gender?: number;
  /**
   * 计划名称
   */
  imEnterAdName?: string;
  /**
   * 广告主 id
   */
  imEnterAdvId?: number;
  /**
   * 广告主名称
   */
  imEnterAdvName?: string;
  /**
   * 创意id
   */
  imEnterCreativeId?: number;
  /**
   * 场景类型
   */
  imEnterSceneType?: number;
  /**
   * 私信页进入来源类型
   */
  imEnterUserType?: number;
  /**
   * 意向车型
   */
  intentionCarModel?: string;
  /**
   * 留资卡片-客户名称
   */
  name?: string;
  /**
   * 客户手机号
   */
  phone?: string;
  /**
   * 项目id
   */
  projectId?: number;
  /**
   * 留资卡片-客户城市
   */
  region?: string;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 用户标签
   */
  tags?: Tag[];
  /**
   * 微信
   */
  wechat?: string;
};

/** 查询会话客户信息 POST /douyin/private-message/customer/query */
export async function PrivacyMessageCustomerQuery(params: {
  /**
   * 会话 id
   */
  conversationId: string;
  /**
   * 项目 id
   */
  projectId: string;
}) {
  return request<ApiResult<DouyinPrivateMessageCustomer>>(
    `/new-media-api/douyin/private-message/customer/query`,
    {
      method: 'GET',
      params,
    },
  );
}

type CustomerUpdateBody = {
  /**
   * 地址
   */
  address?: string;
  /**
   * 年龄
   */
  age?: number;
  /**
   * 生日
   */
  birthday?: string;
  /**
   * 会话id
   */
  conversationId: string;
  /**
   * 性别 1-男性 2-女性 0-未知
   */
  gender: number;
  /**
   * 意向车型
   */
  intentionCarModel?: string;
  /**
   * 客户名称
   */
  name?: string;
  /**
   * 留资卡片-客户手机号
   */
  phone?: string;
  /**
   * 项目id
   */
  projectId: string;
  /**
   * 客户城市
   */
  region?: string;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 微信
   */
  wechat?: string;
};

/** 更新会话客户信息 POST /douyin/private-message/customer/update */
export function PrivacyMessageCustomerUpdate(data: CustomerUpdateBody) {
  return request<ApiResult<boolean>>(`/new-media-api/douyin/private-message/customer/update`, {
    method: 'POST',
    data,
  });
}
