import { request } from '@umijs/max';
import { <PERSON>piResult, PageBasicParams, PagintaionData } from '../common';

type DouyinImManualProjectAccountUserVO = {
  /** 昵称 */
  nickname: string;
  /** 內显 id */
  accountId: string;
  /** 团队编码 */
  teamCode: string;
  /** 抖音 uid */
  uid: string;
  /** 外显 id */
  showAccountId: string;
  /** 头像 */
  avatar: string;
  /** 蓝 V 标识 0: 否 1:是 */
  blueVipFlag: number;
  /** 平台 */
  platform: number;
  /** 是否被选中 true: 选中，false: 未选中 */
  selected: boolean;
};

type DouyinImManualProjectTeamVO = {
  /** 团队编码 */
  teamCode?: string;
  /** 团队昵称 */
  teamName?: string;
};

export type DouyinImManualSourceAccount = {
  id?: number | string;
  /** 系统用户id */
  sysUserId?: number;
  /** 系统用户昵称 */
  sysUserNickname?: string;
  /** 抖音账号open_id */
  openId?: string;
  /** 內显 id */
  accountId?: string;
  /** 抖音账号昵称 */
  nickname?: string;
  /** 抖音账号头像 */
  avatar?: string;
  /** 外显 id */
  showAccountId?: string;
  /** 团队编码 */
  teamCode?: string;
  /** 团队名称 */
  teamName?: string;
};

/** 删除 POST /douyin/im/manual/source-account-rule/delete */
export async function ManualSourceAccountRuleDelete(params: { projectId: number }, body: number[]) {
  return request<ApiResult<number>>(`/new-media-api/douyin/im/manual/source-account-rule/delete`, {
    method: 'POST',
    params,
    data: body,
  });
}

/** 分页查询 GET /douyin/im/manual/source-account-rule/page */
export async function ManualSourceAccountRulePage(params: PageBasicParams & { projectId: string }) {
  return request<ApiResult<PagintaionData<DouyinImManualSourceAccount>>>(
    `/new-media-api/douyin/im/manual/source-account-rule/page`,
    {
      method: 'GET',
      params,
    },
  );
}

/** 查询 GET /douyin/im/manual/source-account-rule/list */
export async function ManualSourceAccountRuleList(params: { projectId?: string }) {
  return request<ApiResult<DouyinImManualSourceAccount[]>>(
    `/new-media-api/douyin/im/manual/source-account-rule/list`,
    {
      method: 'GET',
      params,
    },
  );
}

/** 保存 POST /douyin/im/manual/source-account-rule/save */
export async function ManualSourceAccountRuleSave(body: {
  /** 项目 id */
  projectId?: string;
  /**
   * 源账号规则集合
   */
  sourceAccountRuleList: {
    /**
     * 平台账号內显 id集合
     */
    accountIdList: string[];
    /**
     * 系统用户 id
     */
    sysUserId: number;
  }[];
}) {
  return request<ApiResult<boolean>>(`/new-media-api/douyin/im/manual/source-account-rule/save`, {
    method: 'POST',
    data: body,
  });
}

/** 分页查询可选的团队 GET /douyin/im/manual/source-account-rule/team/page */
export async function ManualSourceAccountRuleTeamPage(
  params: PageBasicParams & {
    projectId: number;
    keyword?: string;
  },
) {
  return request<ApiResult<DouyinImManualProjectTeamVO[]>>(
    `/new-media-api/douyin/im/manual/source-account-rule/team/page`,
    {
      method: 'GET',
      params,
    },
  );
}

/** 查询可选团队集合 GET /douyin/im/manual/source-account-rule/team/list */
export async function ManualSourceAccountRuleTeamList(params: { projectId?: string }) {
  return request<ApiResult<DouyinImManualProjectTeamVO[]>>(
    `/new-media-api/douyin/im/manual/source-account-rule/team/list`,
    {
      method: 'GET',
      params,
    },
  );
}

/** 分页查询团队下的抖音账号 GET /douyin/im/manual/source-account-rule/team/project-account-user/page */
export async function ManualSourceAccountRuleTeamProjectAccountUserPage(params: {
  /** 项目 id */
  projectId: number;
  /** 团队编码 */
  teamCode: string;
}) {
  return request<ApiResult<PagintaionData<DouyinImManualProjectAccountUserVO>>>(
    `/new-media-api/douyin/im/manual/source-account-rule/team/project-account-user/page`,
    {
      method: 'GET',
      params,
    },
  );
}

/** 查询团队下的抖音账号集合 GET /douyin/im/manual/source-account-rule/team/project-account-user/list */
export async function ManualSourceAccountRuleTeamProjectAccountUserList(params: {
  /** 项目 id */
  projectId?: string;
  /** 团队编码 */
  teamCode: string;
}) {
  return request<ApiResult<DouyinImManualProjectAccountUserVO[]>>(
    `/new-media-api/douyin/im/manual/source-account-rule/team/project-account-user/list`,
    {
      method: 'GET',
      params,
    },
  );
}

/** 更新 POST /douyin/im/manual/source-account-rule/update */
export async function ManualSourceAccountRuleUpdate(body: {
  /** 项目 id */
  projectId: number;
  /** 系统用户 id */
  sysUserId: number;
  /** 抖音账号內显 id */
  accountIdList: string[];
}) {
  return request<ApiResult<boolean>>(`/new-media-api/douyin/im/manual/source-account-rule/update`, {
    method: 'POST',
    data: body,
  });
}
