import { request } from '@umijs/max';
import { ApiResult, PageBasicParams, PagintaionData } from '../common';

export type DouyinImManualUserInfoDTO = {
  /**
   * 头像
   */
  avatar?: string;
  /**
   * 抖音昵称
   */
  nickname?: string;
  /**
   * openId
   */
  openId?: string;
};

export enum MessageType {
  Emoji = 'emoji',
  Image = 'image',
  Other = 'other',
  PhoneCollectionWithOneClickCard = 'phone_collection_with_one_click_card',
  PremiumOnlineAppointmentCard = 'premium_online_appointment_card',
  RetainConsultCard = 'retain_consult_card',
  Text = 'text',
  UserLocalImage = 'user_local_image',
  UserLocalVideo = 'user_local_video',
  Video = 'video',
}

export type LastMessageOverview = {
  /**
   * 内容
   */
  content?: string;
  /**
   * 消息创建时间
   */
  messageCreateTime?: string;
  /**
   * 消息类型
   */
  messageType?: MessageType;
  /**
   * 发送者openId
   */
  senderOpenId?: string;
};

export type DouyinImManualConversation = {
  /**
   * 会话活动时间戳
   */
  activeTimestamp?: string;
  /**
   * 会话 id
   */
  conversationId?: string;
  /**
   * 客户
   */
  customerInfo?: DouyinImManualUserInfoDTO;
  /**
   * 最近一条消息概要
   */
  lastMessageOverview?: LastMessageOverview;
  /**
   * 客服/商家
   */
  merchantInfo?: DouyinImManualUserInfoDTO;
  /**
   * 留资状态 0-否 1-是
   */
  retentionStatus?: number;
  /**
   * 会话是否星标 0-否 1-是
   */
  starStatus?: number;
  /**
   * 未读条数
   */
  unReadCount?: number;
};

export type IMConversation = {
  /**
   * 分页游标
   */
  cursor?: number;
  /**
   * 数据列表
   */
  data?: DouyinImManualConversation[];
  /**
   * 是否还有更多数据
   */
  hasMore?: boolean;
};

export type ConversationPageParams = {
  /**
   * 分页游标
   */
  cursor?: number;
  /**
   * 分页大小
   */
  limit: number;
  /**
   * 项目 id
   */
  projectId?: string;
  /**
   * 用户/客户类型
   * 1 :自然用户
   * 2 :投流用户
   */
  customerType?: number;
  /**
   * 留资状态 0-否 1-是
   */
  retentionStatus?: number;
};

/** 会话活动时间更新 POST /douyin/im/manual/conversation/active-time/update */
export async function UpdateConversationActiveTime(data: {
  projectId: number;
  conversationId: string;
}) {
  return request<ApiResult<boolean>>(
    '/new-media-api/douyin/im/manual/conversation/active-time/update',
    {
      method: 'POST',
      data,
    },
  );
}

/** 分页获取会话列表 GET /douyin/im/manual/conversation/page */
export async function GetConversationPage(params: ConversationPageParams) {
  return request<ApiResult<IMConversation>>('/new-media-api/douyin/im/manual/conversation/page', {
    method: 'GET',
    params,
  });
}

/** 搜索会话列表 GET /douyin/im/manual/conversation/search */
export async function SearchConversation(
  params: PageBasicParams & {
    /**
     * 用户/客户昵称
     */
    customerNickname?: string;
    /**
     * 用户/客户类型
     * 1 :自然用户
     * 2 :投流用户
     */
    customerType?: number;
    /**
     * 项目 id
     */
    projectId: number;
    /**
     * 留资状态 0-否 1-是
     */
    retentionStatus?: number;
  },
) {
  return request<ApiResult<PagintaionData<DouyinImManualConversation>>>(
    '/new-media-api/douyin/im/manual/conversation/search',
    {
      method: 'GET',
      params,
    },
  );
}

/** 设置会话星标状态 POST /douyin/im/manual/conversation/star-status/update */
export async function UpdateConversationStarStatus(data: {
  /**
   * 会话id
   */
  conversationId: string;
  /**
   * 项目id
   */
  projectId: string;
  /**
   * 星标状态 0-未星标 1-星标
   */
  starStatus: number;
}) {
  return request<ApiResult<boolean>>(
    '/new-media-api/douyin/im/manual/conversation/star-status/update',
    {
      method: 'POST',
      data,
    },
  );
}
