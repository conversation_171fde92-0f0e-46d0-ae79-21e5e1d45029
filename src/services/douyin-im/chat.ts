import { request } from '@umijs/max';
import { ApiResult, PageBasicParams, PagintaionData } from '../common';
import { MessageType } from './conversation';

export type DouyinImChatPageParams = {
  /** 项目 id */
  projectId: string;
  /** 会话 id */
  conversationId: string;
} & PageBasicParams;

export enum SendType {
  TEXT = 1,
  IMAGE = 2,
  RETAIN_CONSULT_CARD = 3, // 留资咨询卡片
}

export type DouyinImChatSendDTO = {
  /** 项目 id */
  projectId?: string;
  /** 发送类型 */
  sendType: SendType;
  /** 仅当发送类型为图片/留资卡片时必传 */
  imageUrl?: string;
  /** 仅当发送类型为文本/留资卡片时必传 */
  text?: string;
  /** 留资卡片标题 仅当发送类型为留资卡片时必传 */
  title?: string;
  /** 会话 id */
  conversationId: string;
  /** 留资组件集合 1-姓名 2-手机号 3-城市 仅当发送类型为留资卡片时必传 */
  componentList?: number[];
};

export type DouyinImChatSendResult = {
  /** 系统消息 id，消息失败时无数据 */
  serverMessageId?: string;
};

export type ChatMessage = {
  /**
   * 私信内容
   */
  content?: string;
  /**
   * 会话 id
   */
  conversationId?: string;
  /**
   * 消息创建时间
   */
  messageCreateTime?: string;
  /**
   * 消息事件 0: 发送 1: 接收
   */
  messageEvent?: number;
  /**
   * 消息类型
   */
  messageType?: MessageType;
  /**
   * 系统消息 id
   */
  serverMessageId?: string;
  /**
   * 发送来源类型 1-人工客服 2-机器人客服 3-自然
   */
  sendSourceType?: number;
};

/** 分页加载聊天记录 GET /douyin/im/manual/chat/page */
export async function GetChatPage(params: DouyinImChatPageParams) {
  return request<ApiResult<PagintaionData<ChatMessage>>>(
    '/new-media-api/douyin/im/manual/chat/page',
    {
      method: 'GET',
      params,
    },
  );
}

export type ChatDataStatistics = {
  /**
   * 留资数
   */
  leadsCount?: number;
  /**
   * 接待中（还未留资）
   */
  receptionCount?: number;
  /**
   * 总接待数（留资+未留资）
   */
  totalReceptionCount?: number;
};

/** 获取聊天数据统计 GET /douyin/im/manual/chat/data-statistics */
export async function GetChatDataStatistics(params: { projectId: string }) {
  return request<ApiResult<ChatDataStatistics>>(
    '/new-media-api/douyin/im/manual/chat/data-statistics',
    {
      method: 'GET',
      params,
    },
  );
}

type ChatValid = {
  /**
   * 当 sendStatus 为 false 时的提示信息
   */
  message?: string;
  /**
   * 是否能够发送消息 true:可以 false:不可以
   */
  sendStatus?: boolean;
};

/** 获取有效会话 GET /douyin/im/manual/chat/valid */
export async function GetChatValid(params: {
  /**
   * 会话 id
   */
  conversationId: string;
  /**
   * 项目 id
   */
  projectId: string;
}) {
  return request<ApiResult<ChatValid>>('/new-media-api/douyin/im/manual/chat/valid', {
    method: 'GET',
    params,
  });
}

/** 发送消息 POST /douyin/im/manual/chat/send */
export async function PostChatSend(data: DouyinImChatSendDTO) {
  return request<ApiResult<DouyinImChatSendResult>>('/new-media-api/douyin/im/manual/chat/send', {
    method: 'POST',
    data,
  });
}

/** 获取在线状态 GET /douyin/im/manual/chat/online-status */
export async function GetChatOnlineStatus(params: {
  /**
   * 项目 id
   */
  projectId: string;
}) {
  return request<
    ApiResult<{
      /**
       * 在线状态 0-离线 1-在线
       */
      onlineStatus: number;
    }>
  >('/new-media-api/douyin/im/manual/chat/online-status', {
    method: 'GET',
    params,
  });
}

/** 更新在线状态 POST /douyin/im/manual/chat/online-status/update */
export async function PostChatOnlineStatusUpdate(data: {
  /**
   * 在线状态 0-离线 1-在线
   */
  onlineStatus: number;
  /**
   * 项目 id
   */
  projectId: string;
}) {
  return request<ApiResult<boolean>>('/new-media-api/douyin/im/manual/chat/online-status/update', {
    method: 'POST',
    data,
  });
}

/** 设置消息已读 POST /douyin/im/manual/chat/message/read */
export async function PostChatMessageRead(data: {
  /** 会话 id */
  conversationId: string;
  /**
   * 项目 id
   */
  projectId: string;
}) {
  return request<ApiResult<boolean>>('/new-media-api/douyin/im/manual/chat/message/read', {
    method: 'POST',
    data,
  });
}

/** 获取推荐回复 POST /douyin/im/manual/chat/reply/recommend */
export async function PostChatReplyRecommend(data: {
  /**
   * 会话 id
   */
  conversationId: string;
  /**
   * 项目 id
   */
  projectId: string;
  /**
   * 消息 id
   */
  serverMessageId: string;
}) {
  return request<
    ApiResult<{
      /**
       * 引用知识库内容
       */
      referenceContent?: string[];
      /**
       * 回复推荐内容
       */
      replyRecommendContent?: string;
    }>
  >('/new-media-api/douyin/im/manual/chat/reply/recommend', {
    method: 'POST',
    data,
  });
}
