import { request } from '@umijs/max';
import { ApiResult, PageBasicParams, PagintaionData } from '../common';

/** 删除分组 POST /douyin/im/quick-reply/group/delete */
export async function QuickReplyGroupDelete(params: {
  /** 主键 */
  id: number;
}) {
  return request<ApiResult<number>>(`/new-media-api/douyin/im/quick-reply/group/delete`, {
    method: 'POST',
    params,
  });
}

export type QuickReplyGroupData = {
  id: number;
  /** 分组名称 */
  groupName: string;
};

/** 查询集合 GET /douyin/im/quick-reply/group/list */
export async function QuickReplyGroupList() {
  return request<ApiResult<QuickReplyGroupData[]>>(
    `/new-media-api/douyin/im/quick-reply/group/list`,
    {
      method: 'GET',
    },
  );
}

/** 保存分组 POST /douyin/im/quick-reply/group/save */
export async function QuickReplyGroupSave(params: {
  /**
   * 分组名称
   */
  groupName: string;
}) {
  return request<ApiResult<boolean>>(`/new-media-api/douyin/im/quick-reply/group/save`, {
    method: 'POST',
    params,
  });
}

/** 更新分组 POST /douyin/im/quick-reply/group/update */
export async function QuickReplyGroupUpdate(params: {
  /** 主键 */
  id: number;
  /** 分组名称 */
  groupName: string;
}) {
  return request<ApiResult<boolean>>(`/new-media-api/douyin/im/quick-reply/group/update`, {
    method: 'POST',
    params,
  });
}

export async function QuickReplyDelete(data: number[]) {
  return request<ApiResult<number>>(`/new-media-api/douyin/im/quick-reply/delete`, {
    method: 'POST',
    data,
  });
}

export type ReplyData = {
  /**
   * 回复内容
   */
  content: string;
  /**
   * 分组id
   */
  groupId: number;
  id: number;
};

/** 分页查询快捷回复列表 GET /douyin/im/quick-reply/page */
export async function QuickReplyData(
  params: PageBasicParams & {
    /** 分组 id */
    groupId: number;
  },
) {
  return request<ApiResult<PagintaionData<ReplyData>>>(
    `/new-media-api/douyin/im/quick-reply/page`,
    {
      method: 'GET',
      params,
    },
  );
}

/** 保存快捷回复 POST /douyin/im/quick-reply/save */
export async function QuickReplySave(params: {
  /** 分组 id */
  groupId: number;
  /** 回复内容 */
  content: string;
}) {
  return request<ApiResult<boolean>>(`/new-media-api/douyin/im/quick-reply/save`, {
    method: 'POST',
    params,
  });
}

/** 更新快捷回复 POST /douyin/im/quick-reply/update */
export async function QuickReplyUpdate(params: {
  /** 主键 */
  id: number;
  /** 分组 id */
  groupId: number;
  /** 回复内容 */
  content: string;
}) {
  return request<ApiResult<boolean>>(`/new-media-api/douyin/im/quick-reply/update`, {
    method: 'POST',
    params,
  });
}
