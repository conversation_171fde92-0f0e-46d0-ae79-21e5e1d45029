import { request } from '@umijs/max';
import { ApiResult, PageBasicParams, PagintaionData } from '../common';
import { DouyinImManualTeam } from './manual-config';

export type DouyinImManualSysUserVO = {
  /** 自增id */
  id?: number;
  /** 邮箱 */
  email?: string;
  /** 用户名 */
  name?: string;
  /** 是否被选中 true: 选中，false: 未选中 */
  selected?: boolean;
};

/** 删除团队 POST /douyin/im/manual/team/delete */
export async function ManualTeamDelete(body: {
  /** 项目 id */
  projectId?: string;
  /** 系统用户 id */
  sysUserId: number;
}) {
  return request<ApiResult<number>>(`/new-media-api/douyin/im/manual/team/delete`, {
    method: 'POST',
    data: body,
  });
}

/** 查询团队 GET /douyin/im/manual/team/list */
export async function ManualTeamList(params: { projectId?: string }) {
  return request<ApiResult<DouyinImManualTeam[]>>(`/new-media-api/douyin/im/manual/team/list`, {
    method: 'GET',
    params,
  });
}

/** 保存团队 POST /douyin/im/manual/team/save */
export async function ManualTeamSave(body: {
  /** 项目 id */
  projectId?: string;
  /** 系统用户id */
  sysUserIdList: number[];
  /** 接待上限 */
  receptionLimit?: number;
}) {
  return request<ApiResult<boolean>>(`/new-media-api/douyin/im/manual/team/save`, {
    method: 'POST',
    data: body,
  });
}

/** 分页查询可选的系统用户 GET /douyin/im/manual/team/sys-user/page */
export async function ManualTeamSysUserPage(
  params: PageBasicParams & {
    /** 项目 id */
    projectId: string;
    /** 关键词 */
    keyword?: string;
  },
) {
  return request<ApiResult<PagintaionData<DouyinImManualSysUserVO>>>(
    `/new-media-api/douyin/im/manual/team/sys-user/page`,
    {
      method: 'GET',
      params,
    },
  );
}

export type DouyinImManualTeamVO = {
  /**
   * 邮箱
   */
  email?: string;
  /**
   * 账号昵称
   */
  name?: string;
  /**
   * 接待上限
   */
  receptionLimit?: number;
  /**
   * 系统用户 id
   */
  sysUserId?: number;
  /**
   * 用户类型
   */
  userType?: number;
};

export type SelectDataDouyinImManualTeamVO = {
  /**
   * 附加属性
   */
  attributes?: DouyinImManualTeamVO;
  /**
   * 是否禁用
   */
  disabled?: boolean;
  /**
   * 显示的数据
   */
  name?: string;
  /**
   * 是否被选中
   */
  selected?: boolean;
  /**
   * 分组标识
   */
  type?: string;
  /**
   * 选中获取的属性
   */
  value?: { [key: string]: any };
};

export async function ManualTeamSelect(params: {
  /** 项目 id */
  projectId?: string;
}) {
  return request<ApiResult<SelectDataDouyinImManualTeamVO[]>>(
    `/new-media-api/douyin/im/manual/team/select`,
    {
      method: 'GET',
      params,
    },
  );
}
