import { request } from '@umijs/max';
import { ApiResult } from './common';

export enum TaskStatus {
  UNFINISHED = 1,
  FINISHED = 2,
  FAILED = 3,
}

export type PollingExportRes = {
  /**
   * 文件地址
   */
  fileUrl: string;
  /**
   * 进度
   */
  progress: number;
  /**
   * 任务状态 1-未完成 2-已完成 3-失败
   */
  taskStatus: TaskStatus;
};

export function PollingExport(params: { taskId: string }) {
  return request<ApiResult<PollingExportRes>>('/new-media-api/export-task/query', {
    method: 'GET',
    params,
  });
}
