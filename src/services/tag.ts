import { TagGroupCreateParam, TagGroupEditParam } from '@/components/tagsList/createTagModal';
import { request } from '@umijs/max';
import { ReactText } from 'react';
import { ApiResult } from './common';
import { CommonTagGroup, TagGroupWithIndustry } from './typings';

export enum TagsType {
  /** 爬虫标签 */
  spiderTag = 1,
  /** 自定义标签 || 线索客户标签 */
  customTag = 2,
  /** 平台账号标签 */
  accountTag = 3,
  /** 质检审核理由标签 */
  qualityReviewTag = 4,
}

export function GetTagGroups(type: TagsType, params?: { projectId?: string }) {
  return request<ApiResult<CommonTagGroup[]>>(`/new-media-api/tag/get_tag_groups/${type}`, {
    method: 'GET',
    params: params || {},
  });
}

export function CreateTagGroup(type: number, params: TagGroupCreateParam) {
  return request<ApiResult<void>>(`/new-media-api/tag/create_tag_group/${type}`, {
    method: 'POST',
    data: params,
  });
}

export function EditTagGroup(id: number, params: TagGroupEditParam) {
  return request<ApiResult<void>>(`/new-media-api/tag/edit_tag_group/${id}`, {
    method: 'POST',
    data: params,
  });
}

export function DeleteTagGroup(id: number) {
  return request<ApiResult<void>>(`/new-media-api/tag/delete_tag_group/${id}`, {
    method: 'POST',
  });
}

export function UpdateObjectTags(params: { type: number; objectId: string; tagIds: number[] }) {
  return request<ApiResult<void>>(`/new-media-api/tag/update_object_tags`, {
    method: 'POST',
    data: params,
  });
}

export function BatchAddObjectTags(params: {
  type: TagsType;
  objectIds: string[];
  tagIds: number[];
}) {
  return request<ApiResult<void>>(`/new-media-api/tag/batch_add_object_tags`, {
    method: 'POST',
    data: params,
  });
}

export function OrderTagsGroup(params: { tagGroupIds: ReactText[] }) {
  return request<ApiResult<void>>(`/new-media-api/tag/order_tag_group`, {
    method: 'POST',
    data: params,
  });
}

export function BatchEditObjectTags(params: {
  type: TagsType;
  objectIds: string[];
  tagIds: number[];
}) {
  return request<ApiResult<void>>(`/new-media-api/tag/batch-edit-object_tags`, {
    method: 'POST',
    data: params,
  });
}

// 获取行业标签组
export function getIndustryTagGroups() {
  return request<ApiResult<TagGroupWithIndustry[]>>(`/new-media-api/tag/get_industry_tag_groups`, {
    method: 'GET',
  });
}
