import { request } from '@umijs/max';
import { ApiResult } from './common';

type DifyMenuDTOCreate = {
  /** 主键 */
  id: number;
  /** 父级菜单id */
  parentId: number;
  /** 名称 */
  name: string;
  /** app 类型 1-工作流 2-聊天助手 */
  appType?: number;
  /** 类型 1-目录 2-配置项 */
  menuType: number;
  /** 模块code */
  code?: string;
};

type DifyMenuDTOUpdate = {
  /** 主键 */
  id: number;
  /** 父级菜单id */
  parentId: number;
  /** 名称 */
  name: string;
  /** app 类型 1-工作流 2-聊天助手 */
  appType?: number;
  /** 类型 1-目录 2-配置项 */
  menuType: number;
  /** 模块code */
  code?: string;
};

type DifyMenuTreeDTO = {
  /** 节点ID */
  id?: number;
  /** 父节点ID */
  parentId?: number;
  /** 名称 */
  name?: string;
  /** app 类型 1-工作流 2-聊天助手 */
  appType?: number;
  /** 类型 1-目录 2-配置项 */
  menuType?: number;
  /** 模块code */
  code?: string;
  /** 子节点集合 */
  children?: DifyMenuTreeDTO[];
};

export type ProjectDifyConfig = {
  /**
   * 访问密钥
   */
  apiKey: string;
  /**
   * app 类型 1-工作流 2-聊天助手
   */
  appType?: number;
  /**
   * 模块code
   */
  code?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 主键
   */
  id?: number;
  /**
   * 知识库 id
   */
  knowledgeId?: string;
  /**
   * 模块 name
   */
  name?: string;
  /**
   * 操作人
   */
  operator?: string;
  /**
   * 项目id
   */
  projectId?: number;
  /**
   * 最上级菜单 name
   */
  topMenuName?: string;
  /**
   * 更新时间
   */
  updateTime?: string;
};

type ProjectDifyConfigDeleteDTO = {
  /** 项目 id */
  projectId: number;
  /** 配置编码 */
  code: string;
};

type ProjectDifyConfigSaveDTO = {
  /** 项目 id */
  projectId: number;
  /** 配置编码 */
  code: string;
  /** 访问密钥 */
  apiKey: string;
  /** 知识库 id */
  knowledgeId?: string;
};

type ProjectDifyConfigUpdateDTO = {
  /** 项目 id */
  projectId: number;
  /** 配置编码 */
  code: 'private-message-assistant' | 'private-message-intention-judgment';
  /** 访问密钥 */
  apiKey: string;
  /** 知识库 id */
  knowledgeId?: string;
};

/** 删除配置 POST /project/dify-config/delete */
export async function PostDifyConfigDelete(data: ProjectDifyConfigDeleteDTO) {
  return request<ApiResult<boolean>>(`/new-media-api/project/dify-config/delete`, {
    method: 'POST',
    data,
  });
}

/** 获取集合 GET /project/dify-config/list */
export async function GetDifyConfigList(params: { projectId?: string }) {
  return request<ApiResult<ProjectDifyConfig[]>>(`/new-media-api/project/dify-config/list`, {
    method: 'GET',
    params,
  });
}

/** 保存配置 POST /project/dify-config/save */
export async function PostDifyConfigSave(data: ProjectDifyConfigSaveDTO) {
  return request<ApiResult<boolean>>(`/new-media-api/project/dify-config/save`, {
    method: 'POST',
    data,
  });
}

/** 更新配置 POST /project/dify-config/update */
export async function PostDifyConfigUpdate(data: ProjectDifyConfigUpdateDTO) {
  return request<ApiResult<boolean>>(`/new-media-api/project/dify-config/update`, {
    method: 'POST',
    data,
  });
}

/** 删除菜单项 POST /dify/menu/delete */
export async function PostDifyMenuDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: { menuId: number },
) {
  return request<ApiResult<boolean>>(`/new-media-api/dify/menu/delete`, {
    method: 'POST',
    params,
  });
}

/** 保存菜单项 POST /dify/menu/save */
export async function PostDifyMenuSave(data: DifyMenuDTOCreate) {
  return request<ApiResult<boolean>>(`/new-media-api/dify/menu/save`, {
    method: 'POST',
    data,
  });
}

/** 获取菜单树 GET /dify/menu/tree */
export async function GetDifyMenuTree() {
  return request<ApiResult<DifyMenuTreeDTO[]>>(`/new-media-api/dify/menu/tree`, {
    method: 'GET',
  });
}

/** 更新菜单项 POST /dify/menu/update */
export async function PostDifyMenuUpdate(data: DifyMenuDTOUpdate) {
  return request<ApiResult<boolean>>(`/new-media-api/dify/menu/update`, {
    method: 'POST',
    data,
  });
}
