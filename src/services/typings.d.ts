import { IndustryType } from '@/utils/const';

export interface PortalUser {
  id: number;
  email: string;
  name: string;
  type: number;
  roleCodes: any[];
  permissionCodes: string[];
  projectIds: any[];
  avatar?: string;
}

export interface AccountUserDailyStat {
  date: string;
  platform: number;
  accountId: string;
  postCount: number;
  followingCount: number;
  followerCount: number;
  totalFavorited: number;
  userInfo: AccountUser;
  accountNum?: number;
}

export interface PostDailySum {
  date: string;
  accountNum: number;
  postNum: number;
  admireCount: number;
  collectCount: number;
  commentCount: number;
  diggCount: number;
  playCount: number;
  shareCount: number;
}

export interface CommonTagGroup {
  id: number;
  name: string;
  type: number;
  tags: TagType[];
  industryType?: IndustryType;
}

export interface TagIDAccount {
  accountId: string;
  nickname: string;
  platform: number;
}

export interface TagSelectData {
  name: string;
  value: number;
}

export interface ProjectTeamFieldItem {
  bizType: number;
  createTime: string;
  deleteFlag: number;
  depth: number;
  fieldName: string;
  id: number;
  projectId: number;
  showFlag: number;
  sort: number;
  type: number;
  updateTime: string;
}

export interface SimpleTeamFieldItem {
  fieldId: number;
  value: string;
}

export interface LiveInfo {
  accountId: string;
  nickname: string;
  platform: number;
  teamCode: string;
  province: string;
  city?: string;
  watchCount?: number;
  dmCount?: number;
  diggCount?: number;
  livePullUrl: string;
  roomId: string;
  avatar: string;
  fullName?: string;
}

export interface LiveQuotationRecordGroupData {
  appearanceStatus: string[];
  carModel: string[];
  liveBackground: string[];
}

export interface TagGroupWithIndustry {
  id: number; // 标签组id
  type: number; // 标签类型
  name: string; // 组名称
  projectId: number;
  pos: number;
  industryType: IndustryType; // 行业类型
}
