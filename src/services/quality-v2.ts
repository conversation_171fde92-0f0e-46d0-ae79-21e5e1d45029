import { request } from '@umijs/max';
import { ApiResult } from './common';
import { PlatForm } from '@/utils/platform';
import { SummaryCount } from './daily';
import { TrendRes } from './quality';

export function QualityExV2Trend(params: {
  dateType?: number;
  projectId?: string;
  /**
   * 查询类型 1-直播 2-作品
   */
  queryType: number;
  /**
   * 类型 1-账号 2-团队
   */
  type: number;
  /**
   * 平台
   */
  platform: PlatForm;
}) {
  return request<ApiResult<TrendRes>>('/new-media-api/quality/ex/v2/violation/trend', {
    method: 'GET',
    params,
  });
}

export function GetQualityExV2Count(params: {
  dateType?: number;
  projectId?: string;
  /**
   * 查询类型 1-直播 2-作品
   */
  queryType: number;
  /**
   * 结束日期
   */
  endDate?: string;
  /**
   * 开始日期
   */
  startDate?: string;
  /**
   * 平台
   */
  platform: PlatForm;
}) {
  return request<ApiResult<SummaryCount>>('/new-media-api/quality/ex/v2/violation/count', {
    method: 'GET',
    params,
  });
}

export type ViolationDataType = {
  /**
   * 时间类型 1-近一天 6-自然周 7-自然月 4-自定义
   */
  dateType: number;
  /**
   * 结束日期
   */
  endDate?: string;
  /**
   * 聚合维度主键
   */
  fieldId?: number;
  /**
   * 动态维度字段
   */
  fieldList?: {
    /**
     * 维度字段 id
     */
    fieldId?: number;
    /**
     * 维度字段值
     */
    value?: string;
  }[];
  /**
   * 项目 id
   */
  projectId?: string;
  /**
   * 开始日期
   */
  startDate?: string;
  /**
   * 团队编码集合
   */
  teamCodeList?: string[];
  /**
   * 平台
   */
  platform: PlatForm;
};

export type QualityLiveViolationAggregationDailyV2VO = {
  /**
   * 聚合名称
   */
  aggregationName?: string;
  /**
   * 主播形象违规场次
   */
  anchorImageViolationCount?: number;
  /**
   * 品牌违规场次
   */
  brandViolationCount?: number;
  /**
   * 空播场次
   */
  liveAfkCount?: number;
  /**
   * 总直播场次
   */
  liveCount?: number;
  /**
   * 总违规直播场次
   */
  liveViolationCount?: number;
  /**
   * 监控词
   */
  monitoringWordStr?: string;
  /**
   * 平台违规场次
   */
  platformViolationCount?: number;
  /**
   * 违规占比
   */
  violationPercent?: string;
  /**
   * 违规率
   */
  violationRate?: string;
  /**
   * 违规类型
   */
  violationTypeNameStr?: string;
};

export function QualityExV2LiveViolationAggregation(data: ViolationDataType) {
  return request<ApiResult<QualityLiveViolationAggregationDailyV2VO[]>>(
    '/new-media-api/quality/ex/v2/live/violation/aggregation/daily',
    {
      method: 'POST',
      data,
    },
  );
}

export type QualityLiveViolationAccountDailyV2VO = {
  /**
   * 主播形象违规场次
   */
  anchorImageViolationCount?: number;
  /**
   * 品牌违规场次
   */
  brandViolationCount?: number;
  /**
   * 空播场次
   */
  liveAfkCount?: number;
  /**
   * 总直播场次
   */
  liveCount?: number;
  /**
   * 总违规直播场次
   */
  liveViolationCount?: number;
  /**
   * 监控词
   */
  monitoringWordStr?: string;
  /**
   * 账号昵称
   */
  nickname?: string;
  /**
   * 平台违规场次
   */
  platformViolationCount?: number;
  /**
   * 展示账号id
   */
  showAccountId?: string;
  /**
   * 团队昵称
   */
  teamName?: string;
  /**
   * 违规占比
   */
  violationPercent?: string;
  /**
   * 违规率
   */
  violationRate?: string;
  /**
   * 违规类型
   */
  violationTypeNameStr?: string;
};

export function QualityExV2LiveViolationAccount(data: ViolationDataType) {
  return request<ApiResult<QualityLiveViolationAccountDailyV2VO[]>>(
    '/new-media-api/quality/ex/v2/live/violation/account/daily',
    {
      method: 'POST',
      data,
    },
  );
}

export function ExportQualityEXV2LiveViolationAggregation(data: any) {
  return request('/new-media-api/quality/ex/v2/live/violation/aggregation/daily/export', {
    method: 'POST',
    data,
  });
}

export function ExportQualityEXV2LiveViolationAccount(data: any) {
  return request('/new-media-api/quality/ex/v2/live/violation/account/daily/export', {
    method: 'POST',
    data,
  });
}

export type QualityPostViolationAggregationDailyV2VO = {
  /**
   * 聚合名称
   */
  aggregationName?: string;
  /**
   * 品牌违规场次
   */
  brandViolationCount?: number;
  /**
   * 监控词
   */
  monitoringWordStr?: string;
  /**
   * 平台违规场次
   */
  platformViolationCount?: number;
  /**
   * 总作品数
   */
  postCount?: number;
  /**
   * 总违规作品数
   */
  postViolationCount?: number;
  /**
   * 违规占比
   */
  violationPercent?: string;
  /**
   * 违规率
   */
  violationRate?: string;
  /**
   * 违规类型
   */
  violationTypeNameStr?: string;
};

export function QualityExV2PostViolationAggregation(data: ViolationDataType) {
  return request<ApiResult<QualityPostViolationAggregationDailyV2VO[]>>(
    '/new-media-api/quality/ex/v2/post/violation/aggregation/daily',
    {
      method: 'POST',
      data,
    },
  );
}

export type QualityPostViolationAccountDailyV2VO = {
  /**
   * 品牌违规场次
   */
  brandViolationCount?: number;
  /**
   * 监控词
   */
  monitoringWordStr?: string;
  /**
   * 账号昵称
   */
  nickname?: string;
  /**
   * 平台违规场次
   */
  platformViolationCount?: number;
  /**
   * 总作品数
   */
  postCount?: number;
  /**
   * 总违规作品数
   */
  postViolationCount?: number;
  /**
   * 展示账号id
   */
  showAccountId?: string;
  /**
   * 团队昵称
   */
  teamName?: string;
  /**
   * 违规占比
   */
  violationPercent?: string;
  /**
   * 违规率
   */
  violationRate?: string;
  /**
   * 违规类型
   */
  violationTypeNameStr?: string;
};

export function QualityExV2PostViolationAccount(data: ViolationDataType) {
  return request<ApiResult<QualityPostViolationAccountDailyV2VO[]>>(
    '/new-media-api/quality/ex/v2/post/violation/account/daily',
    {
      method: 'POST',
      data,
    },
  );
}

export function ExportQualityEXV2PostViolationAggregation(data: any) {
  return request('/new-media-api/quality/ex/v2/post/violation/aggregation/daily/export', {
    method: 'POST',
    data,
  });
}

export function ExportQualityEXV2PostViolationAccount(data: any) {
  return request('/new-media-api/quality/ex/v2/post/violation/account/daily/export', {
    method: 'POST',
    data,
  });
}
