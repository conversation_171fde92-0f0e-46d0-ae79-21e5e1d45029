import { request } from '@umijs/max';
import { ApiResult, PageBasicParams, PagintaionData } from './common';
import { PlatForm } from '@/utils/platform';
import { IndustryType } from '@/utils/const';

import { TeamFieldList } from './setting';
import { LiveInfo, ProjectTeamFieldItem, SimpleTeamFieldItem } from './typings';

export type ProjectDataItem = {
  id: string;
  name: string;
  tagId: number;
  iconUrl: string;
  description: string;
  projectKey: string;
  projectUserCount: number;
  competitionCount: number;
  teamFieldCount: number;
  industryType: IndustryType;
};

export type TeamSettingItem = {
  /**
   * 账号数量
   */
  accountCount: number;
  /**
   * 线索总数
   */
  clueCount: number;
  /**
   * 粉丝总数
   */
  followerCount: number;
  /**
   * 粉丝增量
   */
  followerGrowth: number;
  /**
   * 直播空播时长-单位秒
   */
  liveAfkDuration: number;
  /**
   * 直播空播率
   */
  liveAfkRate: string;
  /**
   * 直播线索数
   */
  liveClueCount: number;
  /**
   * 直播评论量
   */
  liveCommentCount: number;
  /**
   * 直播场次
   */
  liveCount: number;
  /**
   * 直播点赞次数
   */
  liveDiggCount: number;
  /**
   * 直播时长
   */
  liveDuration: number;
  /**
   * 直播粉丝增量
   */
  liveFollowerGrowth: number;
  /**
   * 轻微空播场次
   */
  liveMinorAfkCount: number;
  /**
   * 严重空播场次
   */
  liveSeriousAfkCount: number;
  /**
   * 直播观看人数
   */
  liveViewCount: number;
  /**
   * 直播观看人次
   */
  liveViewTime: number;
  /**
   * 短视频线索数
   */
  postClueCount: number;
  /**
   * 作品收藏总数
   */
  postCollectCount: number;
  /**
   * 作品评论总数
   */
  postCommentCount: number;
  /**
   * 作品总数
   */
  postCount: number;
  /**
   * 作品点赞总数
   */
  postDiggCount: number;
  /**
   * 作品粉丝增量
   */
  postFollowerGrowthCount: number;
  /**
   * 作品新增播放数
   */
  postNewPlayCount: number;
  /**
   * 作品播放总数
   */
  postPlayCount: number;
  /**
   * 作品发布数
   */
  postPublishCount: number;
  /**
   * 作品分享总数
   */
  postShareCount: number;
  /**
   * 门店编码
   */
  teamCode: string;
  /**
   * 检测时长
   */
  liveAfkCheckDuration: string;
  /**
   * 空播挂播等级 1-严重 2-轻微 3-正常
   */
  afkLevel: number;
  /**
   * 其他字段
   */
  teamFieldList: TeamFieldList[];
};

export type TeamSettingData = {
  total: number;
  items: TeamSettingItem[];
};

export type ProjectInfoList = {
  /**
   * 竞品数
   */
  competitionCount?: number;
  /**
   * 描述
   */
  description?: string;
  /**
   * 项目图标
   */
  iconUrl?: string;
  /**
   * 自增id
   */
  id?: number;
  /**
   * 高光时刻直播观看人次生成阈值
   */
  liveHighlightViewThreshold?: number;
  /**
   * 名称
   */
  name?: string;
  /**
   * 标识
   */
  projectKey?: string;
  /**
   * 项目成员数
   */
  projectUserCount?: number;
  /**
   * 直播回放/作品视频等存储生命周期
   */
  storageLife?: number;
  /**
   * 标签id
   */
  tagId?: number;
  /**
   * 团队信息数
   */
  teamFieldCount?: number;
};

export function QueryProjectList(params: any) {
  return request<ApiResult<PagintaionData<ProjectInfoList>>>('/new-media-api/project/page', {
    method: 'GET',
    params,
  });
}

export function CreateProject(params: any) {
  return request<ApiResult<any>>('/new-media-api/project/create', {
    method: 'POST',
    data: params,
  });
}

export function DeleteProject(params: number[]) {
  return request<ApiResult<any>>('/new-media-api/project/delete', {
    method: 'POST',
    data: params,
  });
}

export type CompetitionTagItem = {
  competitionName?: string;
  icon?: string;
  tagId?: number;
};

export type UpdateProjectData = {
  competitionTagList?: CompetitionTagItem[];
  description?: string;
  iconUrl: string;
  liveHighlightViewThreshold?: number;
  name: string;
  projectId: string;
  projectKey: string;
  storageLife?: number;
  tagId: number;
  industryId: number;
};

export function UpdateProject(data: UpdateProjectData) {
  return request<ApiResult<boolean>>('/new-media-api/project/update', {
    method: 'POST',
    data,
  });
}

export function AuthorizeUser(params: any) {
  return request<ApiResult<any>>('/new-media-api/project_user/authorize', {
    method: 'POST',
    data: params,
  });
}

export type ProjectInfo = {
  competitionCount?: number;
  description?: string;
  iconUrl?: string;
  id?: number;
  /**
   * 高光时刻直播观看人次生成阈值
   */
  liveHighlightViewThreshold?: number;
  name?: string;
  /**
   * 多平台列表
   */
  platformList?: number[];
  projectKey?: string;
  projectUserCount?: number;
  /**
   * 直播回放/作品视频等存储生命周期
   */
  storageLife?: number;
  tagId?: number;
  teamFieldCount?: number;
  industryType: IndustryType;
};

export function GetProjectInfo(params: { projectId?: string }) {
  return request<ApiResult<ProjectInfo>>('/new-media-api/project/getInfo', {
    method: 'GET',
    params,
  });
}

export function GetProjectInfoByPayload(params: { payload: string }) {
  return request<ApiResult<ProjectInfo>>('/new-media-api/project/getInfoByPayload', {
    method: 'GET',
    params,
  });
}

export function QueryProjectCompetitionTag(params: any) {
  return request<ApiResult<any>>('/new-media-api/project_competition_tag/page', {
    method: 'GET',
    params,
  });
}

export function DeleteProjectCompetition(params: any) {
  return request<ApiResult<any>>('/new-media-api/project_competition_tag/delete', {
    method: 'POST',
    data: params,
  });
}

export function GetAllProject() {
  return request<ApiResult<ProjectDataItem[]>>('/new-media-api/project/list', {
    method: 'GET',
  });
}

export function QueryTeamListFields(params: { projectId?: string }) {
  return request<ApiResult<ProjectTeamFieldItem[]>>('/new-media-api/project_team/list_field', {
    method: 'GET',
    params,
  });
}

export function QueryLiveMapList(params: {
  projectId: string;
  platform?: number;
  fieldList?: SimpleTeamFieldItem[];
  teamCodeList?: string[];
}) {
  return request<ApiResult<{ liveInfoList: LiveInfo[]; storeCount: number }>>(
    '/new-media-api/quality/live_map/list',
    {
      method: 'GET',
      params: {
        ...params,
      },
    },
  );
}

export interface GrantAccountItem {
  /**
   * 抖音账号 id
   */
  accountId?: string;
  /**
   * 头像
   */
  avatar?: string;
  /**
   * 抖音小程序绑定状态 0 未绑定 1 已绑定
   * 抖音小程序绑定状态
   */
  douyinMiniProgramBindStatus?: number;
  /**
   * 授权状态: 0未授权1已授权2已过期
   */
  grantStatus?: number;
  /**
   * 授权时间
   */
  grantTime?: string;
  /**
   * 托管式账号授权状态
   */
  hostingGrantStatus?: number;
  /**
   * 托管方式授权时间
   */
  hostingGrantTime?: string;
  /**
   * 授权账号主键
   */
  id: number | string;
  /**
   * 对应ai助播中的 liveUid
   */
  liveUid?: string;
  /**
   * 账号名称
   */
  nickname?: string;
  /**
   * 抖音号 小红书号
   */
  showAccountId?: string;
  /**
   * 平台
   */
  platform?: PlatForm;
}

export interface GrantTeamInfo {
  teamName: string;
  accounts: GrantAccountItem[];
}

export function QueryGrantTeamInfo(params: { teamCode: string }) {
  return request<ApiResult<GrantTeamInfo>>('/new-media-api/project_team/get-grant-team-info', {
    method: 'POST',
    params: {
      ...params,
    },
  });
}

export type TeamSettingParams = {
  /**
   * 结束时间
   */
  endTime: string;
  /**
   * 动态维度字段
   */
  fieldList?: TeamFieldList[];
  /**
   * 空播时长-单位秒
   */
  liveAfkDuration?: number;
  /**
   * 空播率
   */
  liveAfkRate?: string;
  /**
   * 直播时长 (秒)
   */
  liveDuration?: number;
  /**
   * 平台
   */
  platform?: number;
  pos?: number;
  /**
   * 短视频播放数
   */
  postPlayCount?: number;
  /**
   * 项目 id
   */
  projectId: number;
  /**
   * 开始时间
   */
  startTime: string;
  /**
   * 团队编码列表
   */
  teamCodeList?: string[];
  /**
   * 账号标签
   */
  tagIds?: string;
} & PageBasicParams;

export function TeamSettingPage(params: TeamSettingParams) {
  return request<ApiResult<TeamSettingData>>('/new-media-api/project/team-list/page', {
    method: 'GET',
    params,
  });
}

export function ExportTeamList(params: any) {
  return request('/new-media-api/project/team-list/export', {
    method: 'GET',
    params,
  });
}

export function ImportProjectTeam(formData: any, projectId?: string) {
  return request<ApiResult<{ successFlag: boolean; failFileUrl: string }>>(
    `/new-media-api/project_team/import_team?projectId=${projectId}`,
    {
      method: 'POST',
      data: formData,
    },
  );
}
