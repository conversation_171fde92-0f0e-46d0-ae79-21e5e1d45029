import { request } from '@umijs/max';
import { ApiResult, PageBasicParams, PagintaionData } from './common';
import { PlatForm } from '@/utils/platform';
import { LiveQuotationRecordGroupData } from './typings';

export type TagType = {
  id: number;
  groupId: number;
  name: string;
  pos: number;
};
export interface AccountBasicItem {
  /**
   * 账号 id
   */
  accountId: string;
  /**
   * 账号头像
   */
  avatar: string;
  /**
   * 是否是蓝 v 是 ：1 否：:0
   */
  blueVipFlag: number;
  /**
   * 线索总数
   */
  clueCount: number;
  /**
   * 获赞总数
   */
  favoritedCount: number;
  /**
   * 粉丝总数
   */
  followerCount: number;
  /**
   * 粉丝增量
   */
  followerGrowthCount: number;
  /**
   * 主键 id
   */
  id: number;
  /**
   * 直播空播时长-单位秒
   */
  liveAfkDuration: number;
  /**
   * 直播空播率
   */
  liveAfkRate: string;
  /**
   * 直播线索数
   */
  liveClueCount: number;
  /**
   * 直播评论次数
   */
  liveCommentCount: number;
  /**
   * 直播场次
   */
  liveCount: number;
  /**
   * 直播点赞次数
   */
  liveDiggCount: number;
  /**
   * 直播时长 单位秒
   */
  liveDuration: number;
  /**
   * 直播粉丝增量
   */
  liveFollowerGrowthCount: number;
  /**
   * 轻微空播场次
   */
  liveMinorAfkCount: number;
  /**
   * 严重空播场次
   */
  liveSeriousAfkCount: number;
  /**
   * 直播观看人数
   */
  liveViewCount: number;
  /**
   * 直播观看人次
   */
  liveViewTime: number;
  /**
   * 账号昵称
   */
  nickname: string;
  /**
   * 平台
   */
  platform: number;
  /**
   * 平台昵称
   */
  platformName: string;
  /**
   * 短视频线索数
   */
  postClueCount: number;
  /**
   * 作品收藏数
   */
  postCollectCount: number;
  /**
   * 作品评论数
   */
  postCommentCount: number;
  /**
   * 作品总数
   */
  postCount: number;
  /**
   * 作品粉丝增量
   */
  postFollowerGrowthCount: number;
  /**
   * 作品新增播放量
   */
  postNewPlayCount: number;
  /**
   * 作品播放量
   */
  postPlayCount: number;
  /**
   * 作品发布数
   */
  postPublishCount: number;
  /**
   * 作品分享数
   */
  postShareCount: number;
  /**
   * 更新时间
   */
  reportTime: Date;
  /**
   * 前端展示的账号 id
   */
  showAccountId: string;
  /**
   * 账号状态
   */
  status: number;
  /**
   * 托管方式授权状态
   */
  hostingGrantStatus?: number;
  /**
   * 标签组
   */
  tags: TagType[];
  /**
   * 标签组 字符串
   */
  tagsStr: string;
  /**
   * 团队编码
   */
  teamCode: string;
  /**
   * 检测时长
   */
  liveAfkCheckDuration: string;
  teamFieldList: TeamFieldList[];
}

export interface TeamFieldList {
  name: string;
  value: string;
  fieldId: string;
  showFlag?: number;
  sort: number;
  depth: number;
  bizType: number;
}

export interface PostBasicItem {
  /**
   * 账号 id，账号 id
   */
  accountId?: string;
  /**
   * 账号头像，账号头像
   */
  avatar?: string;
  /**
   * 均播时长 单位秒，均播时长 单位秒
   */
  averagePlayDuration?: number;
  /**
   * 是否是蓝 v 是 ：1 否：:0，是否是蓝 v 是 ：1 否：:0
   */
  blueVipFlag?: number;
  /**
   * 清除标识 0-正常 1-已清除
   */
  cleanFlag?: number;
  /**
   * 清除时间
   */
  cleanTime?: string;
  /**
   * 收藏数，收藏数
   */
  collectCount?: number;
  /**
   * 评论数，评论数
   */
  commentCount?: number;
  /**
   * 完播率，完播率
   */
  completionPlayRate?: number;
  /**
   * 封面图，封面图
   */
  cover?: string;
  /**
   * 点赞数，点赞数
   */
  diggCount?: number;
  /**
   * 作品时长 单位秒，作品时长 单位秒
   */
  duration?: number;
  /**
   * 粉丝增量，粉丝增量
   */
  followerGrowth?: number;
  /**
   * 互动量，互动量
   */
  interactiveCount?: number;
  /**
   * 账号昵称，账号信息
   */
  nickname?: string;
  /**
   * 平台，平台
   */
  platform?: number;
  /**
   * 平台昵称，平台昵称
   */
  platformName?: string;
  /**
   * 播放量，播放量
   */
  playCount?: number;
  /**
   * 作品 id，作品 id
   */
  postId?: string;
  /**
   * 发布时间，发布时间
   */
  publishTime?: string;
  /**
   * 质检标记 0：未质检 1：已质检
   */
  qualityFlag?: number;
  /**
   * 更新时间，更新时间
   */
  reportTime?: string;
  /**
   * 分享数，分享数
   */
  shareCount?: number;
  /**
   * 作品链接，抖音主页链接
   */
  shareLink?: string;
  /**
   * 作品分享url，作品分享url
   */
  shareUrl?: string;
  /**
   * 前端展示的账号 id，前端展示的账号 id
   */
  showAccountId?: string;
  /**
   * 用户标签集合，用户标签集合
   */
  tags: TagType[];
  /**
   * 标签组 字符串，标签组 字符串
   */
  tagsStr?: string;
  /**
   * 动态维度
   */
  teamFieldList?: TeamFieldList[];
  /**
   * 作品标题，作品标题
   */
  title?: string;
  /**
   * 作品超链接，作品超链接
   */
  url?: string;
  /** 脚本公式 */
  scriptFormula?: string;
  /** 文字脚本 */
  subtitles?: string;
  /** 画面类型 */
  screenTypes?: string[];
  /** 视频类型 */
  videoType?: string;
  /** 车型 */
  productInfo?: string;
  /**
   * 作品话题
   */
  topic?: string;
  /** 是否是乾坤圈发布 */
  systemReleaseFlag: number;
}

export interface LiveBasicItem {
  /**
   * 账号 id，账号 id
   */
  accountId?: string;
  /**
   * 投流CPL
   */
  adCpl?: string;
  /**
   * 广告流量关注数
   */
  adFlowCategoryFollowerCount?: number;
  /**
   * 广告流量观看次数
   */
  adFlowCategoryWatchCount?: number;
  /**
   * 广告流量观看时长(分)
   */
  adFlowCategoryWatchDuration?: string;
  /**
   * 广告流量观看人数
   */
  adFlowCategoryWatchUcount?: number;
  /**
   * 投放消耗
   */
  adSpent?: string;
  /**
   * 广告主 Id
   */
  advertiserId?: string;
  /**
   * 空播挂播等级 1-严重 2-轻微 3-正常
   */
  afkLevel?: number;
  /**
   * 全场景线索人数
   */
  allSceneLeadsUcount?: number;
  /**
   * 出镜情况
   */
  appearanceStatus?: string;
  /**
   * 账号头像，账号头像
   */
  avatar?: string;
  /**
   * 平均在线人数
   */
  avgConcurrentUsers?: number;
  /**
   * 平均观看时长，人均停留时长
   */
  avgViewDuration?: number;
  /**
   * 是否是蓝 v 是 ：1 否：:0，是否是蓝 v 是 ：1 否：:0
   */
  blueVipFlag?: number;
  /**
   * 讲解车型
   */
  carModel?: string[];
  /**
   * 清除标识 0-正常 1-已清除，清除标识 0-正常 1-已清除
   */
  cleanFlag?: number;
  /**
   * 清除时间，清除时间
   */
  cleanTime?: string;
  /**
   * 评论数，评论数
   */
  commentCount?: number;
  /**
   * 评论人数
   */
  commentUcount?: number;
  /**
   * 转化组件是否挂载 0-否 1-是
   */
  componentStatus?: number;
  /**
   * 封面图，封面图
   */
  cover?: string;
  /**
   * 点赞数，点赞数
   */
  diggCount?: number;
  /**
   * 点赞人数
   */
  diggUcount?: number;
  /**
   * 曝光次数
   */
  exposureCount?: number;
  /**
   * 曝光进入率
   */
  exposureEntryRate?: string;
  /**
   * 曝光人数
   */
  exposureUcount?: number;
  /**
   * 加入粉丝团数
   */
  fansClubCount?: number;
  /**
   * 粉丝增量，关注人数
   */
  followerGrowthCount?: number;
  /**
   * 互动次数
   */
  interactionCount?: number;
  /**
   * 互动率
   */
  interactionRate?: string;
  /**
   * 互动人数
   */
  interactionUcount?: number;
  /**
   * 是否拉流地址 0-否 1-是
   */
  isPullUrl?: number;
  /**
   * 留资率
   */
  leadsRate?: string;
  /**
   * 直播空播检测时长-单位秒
   */
  liveAfkCheckDuration?: number;
  /**
   * 直播空播时长-单位秒
   */
  liveAfkDuration?: number;
  /**
   * 直播空播率
   */
  liveAfkRate?: string;
  /**
   * 直播背景
   */
  liveBackground?: string;
  /**
   * 小风车曝光次数
   */
  liveCardIconComponentClickCount?: number;
  /**
   * 小风车点击次数（不含小雪花）
   */
  liveCardIconComponentShowCount?: number;
  /**
   * 直播时长，直播时长
   */
  liveDuration?: number;
  /**
   * 直播时长 小时，直播时长 小时
   */
  liveDurationHourStr?: string;
  /**
   * 直播结束时间，直播结束时间
   */
  liveEndTime?: string;
  /**
   * 直播间线索数
   */
  liveFormSubmitCount?: number;
  /**
   * 打赏金额
   */
  liveGiftMoney?: string;
  /**
   * 直播回放，直播回放
   */
  liveReplay?: string[];
  /**
   * 直播开始时间，直播开始时间
   */
  liveStartTime?: string;
  /**
   * 自然流量关注数
   */
  naturalFlowCategoryFollowerCount?: number;
  /**
   * 自然流量观看次数
   */
  naturalFlowCategoryWatchCount?: number;
  /**
   * 自然流量观看时长(分)
   */
  naturalFlowCategoryWatchDuration?: string;
  /**
   * 自然流量观看人数
   */
  naturalFlowCategoryWatchUcount?: number;
  /**
   * 账号昵称，账号信息
   */
  nickname?: string;
  /**
   * 直播时长是大于等于25分钟 0-否 1-是
   */
  over25minLiveDurationStatus?: number;
  /**
   * 直播时长是大于等于60分钟 0-否 1-是
   */
  over60minLiveDurationStatus?: number;
  /**
   * 峰值在线人数
   */
  peakConcurrentUsers?: number;
  /**
   * 平台，平台
   */
  platform?: number;
  /**
   * 平台昵称，平台昵称
   */
  platformName?: string;
  /**
   * 质检标记 0：未质检 1：已质检，质检标记 0：未质检 1：已质检
   */
  qualityFlag?: number;
  /**
   * 更新时间，更新时间
   */
  reportTime?: string;
  /**
   * 直播间进入私信数
   */
  roomEnterPrivateMsgCount?: number;
  /**
   * 房间 id，房间 id
   */
  roomId?: string;
  /**
   * 分享数
   */
  shareCount?: number;
  /**
   * 分享人数
   */
  shareUcount?: number;
  /**
   * 前端展示的账号 id，前端展示的账号 id
   */
  showAccountId?: string;
  /**
   * 账号标签集合，账号标签集合
   */
  tags?: TagType[];
  /**
   * 标签组 字符串，标签组 字符串
   */
  tagsStr?: string;
  /**
   * 动态维度
   */
  teamFieldList?: TeamFieldList[];
  /**
   * 直播标题，作品标题
   */
  title?: string;
  /**
   * uid
   */
  uid?: string;
  /**
   * 视频url（mp4）
   */
  videoUrl?: string;
  /**
   * 观看人数，观看人数
   */
  viewCount?: number;
  /**
   * 观看人次，观看人次
   */
  viewTime?: number;
}

export type LiveBasicItemEcommerce = {
  /**
   * 账号 id
   */
  accountId?: string;
  /**
   * 账号头像
   */
  avatar?: string;
  /**
   * 是否是蓝 v 是 ：1 否：:0
   */
  blueVipFlag?: number;
  /**
   * 清除标识 0-正常 1-已清除
   */
  cleanFlag?: number;
  /**
   * 清除时间
   */
  cleanTime?: string;
  /**
   * 评论数
   */
  commentCount?: number;
  /**
   * 封面图
   */
  cover?: string;
  /**
   * 点赞数
   */
  diggCount?: number;
  /**
   * 是否拉流地址 0-否 1-是
   */
  isPullUrl?: number;
  /**
   * 直播时长
   */
  liveDuration?: number;
  /**
   * 直播结束时间
   */
  liveEndTime?: string;
  /**
   * 直播回放
   */
  liveReplay?: string[];
  /**
   * 直播开始时间
   */
  liveStartTime?: string;
  /**
   * 账号信息
   */
  nickname?: string;
  /**
   * 直播时长是大于等于25分钟 0-否 1-是
   */
  over25minLiveDurationStatus?: number;
  /**
   * 平台
   */
  platform?: number;
  /**
   * 平台昵称
   */
  platformName?: string;
  /**
   * 房间 id
   */
  roomId?: string;
  /**
   * 前端展示的账号 id
   */
  showAccountId?: string;
  /**
   * 千川整体消耗
   */
  statCost?: number;
  /**
   * 账号标签集合
   */
  tags?: TagType[];
  /**
   * 标签组 字符串
   */
  tagsStr?: string;
  /**
   * 动态维度
   */
  teamFieldList?: TeamFieldList[];
  /**
   * 作品标题
   */
  title?: string;
  /**
   * 千川整体成交订单成本
   */
  totalCostPerPayOrder?: number;
  /**
   * 千川整体成交订单数
   */
  totalPayOrderCount?: number;
  /**
   * 千川整体成交智能优惠券金额
   */
  totalPayOrderCouponAmount?: number;
  /**
   * 用户实际支付金额
   */
  totalPayOrderGmv?: number;
  /**
   * 千川整体成交金额
   */
  totalPayOrderGmvIncludeCoupon?: number;
  /**
   * 千川整体支付 ROI（整体成交金额/整体消耗）
   */
  totalPayOrderRoi?: number;
  /**
   * 抖音uid
   */
  uid?: string;
  /**
   * 观看人次
   */
  viewTime?: number;
  /**
   * 视频url（mp4）
   */
  videoUrl?: string;
};

export interface LivePlatformItem {
  commentCount: number;
  diggCount: number;
  followerGrowth: number;
  liveDuration: number;
  liveTime: number;
  platformName: string;
  viewCount: number;
  viewTime: number;
}

export interface PostPlatformItem {
  key?: number;
  collectCount: number;
  commentCount: number;
  diggCount: number;
  followerGrowthCount: number;
  platformName: string;
  playCount: number;
  publishCount: number;
  shareCount: number;
}

export interface TimeRangeParams {
  startTime: string;
  endTime: string;
  projectId: string | undefined;
}

export interface IProjectAccount extends PageBasicParams {
  blueVipFlag?: 0 | 1;
  nickname?: string;
  platform?: PlatForm;
  projectId?: string;
  status?: number;
}

export interface IProjectPost extends PageBasicParams {
  averagePlayDuration?: number;
  blueVipFlag?: 0 | 1;
  duration?: number;
  nickname?: string;
  platform?: PlatForm;
  projectId?: string;
  publishTime?: string;
  title?: string;
  /** 画面类型 */
  screenTypes?: string[];
  /** 视频类型 */
  videoType?: string;
  /** 车型 */
  productInfo?: string;
  // 查看已分析作品
  isAnalysis?: boolean;
}

export interface IProjectLive extends PageBasicParams {
  blueVipFlag?: 0 | 1;
  nickname?: string;
  platform?: number;
  projectId?: string;
  title?: string;
  liveDuration?: string;
  liveEndTime?: string;
  liveReplyStatus?: string;
  liveStartTime?: string;
  liveStatus?: string;
  isAnalysis: boolean;
  carModel?: string;
  liveBackground?: string;
  appearanceStatus?: string;
}

export interface IPieData {
  name: string;
  value: number;
}

export function AccountSettingPage(params: IProjectAccount) {
  return request<ApiResult<AccountBasicItem>>('/new-media-api/project_account_user/page', {
    method: 'GET',
    params,
  });
}

export function PostSettingPage(params: IProjectPost) {
  return request<ApiResult<PagintaionData<PostBasicItem>>>(
    '/new-media-api/project_account_post/page',
    {
      method: 'GET',
      params,
    },
  );
}

export function LiveSettingPage(params: IProjectLive) {
  return request<ApiResult<PagintaionData<LiveBasicItem>>>(
    '/new-media-api/project_account_live/page',
    {
      method: 'GET',
      params,
    },
  );
}

export function LiveSettingPageEcommerce(params: IProjectLive) {
  return request<ApiResult<PagintaionData<LiveBasicItemEcommerce>>>(
    '/new-media-api/project_account_live/page/ecommerce',
    {
      method: 'GET',
      params,
    },
  );
}

export function LiveSettingPageEcommerceHightLightExport(params: IProjectLive) {
  return request<ApiResult<string>>(
    '/new-media-api/project_account_live/ecommerce/highlight/export',
    {
      method: 'GET',
      params,
    },
  );
}

export function ExportAccount(params: any) {
  return request('/new-media-api/project_account_user/export', {
    method: 'GET',
    params,
  });
}

export function ExportPost(params: any) {
  return request('/new-media-api/project_account_post/export', {
    method: 'GET',
    params,
  });
}

export function ExportLive(params: any) {
  return request('/new-media-api/project_account_live/export', {
    method: 'GET',
    params,
  });
}

export function ExportEcommerceLive(params: any) {
  return request('/new-media-api/project_account_live/export/ecommerce', {
    method: 'GET',
    params,
  });
}

export type LineChartData = {
  /**
   * 名称
   */
  name: string;
  /**
   * 每个日期的数据
   */
  trendData: { value: number; lastValue: number }[];
};

export function AccountUserDelete(params: (string | number)[]) {
  return request<ApiResult>('/new-media-api/project_account_user/delete_account', {
    method: 'POST',
    data: params,
  });
}

export function AccountUserUpdate(params: {
  accountUserId: string | number;
  projectId: string | undefined;
  teamCode: string;
}) {
  return request<ApiResult>('/new-media-api/project_account_user/update_account_info', {
    method: 'POST',
    data: params,
  });
}

export function AccountUserChangeTeam(params: { projectId: string | undefined }) {
  return request<ApiResult<any>>('/new-media-api/project_account_user/get_account_edit_team', {
    method: 'GET',
    params,
  });
}

export function GetFollowerMap(params: any) {
  return request<ApiResult<any>>('/new-media-api/project_account_user/get_follower_map', {
    method: 'GET',
    params,
  });
}

export function getLiveQuotationRecordGroup(params: {
  projectId: string;
  roomTitle?: string;
  teamCodeList?: string[];
  fieldList?: { fieldId: string; value: string }[];
  roomId?: string;
  quotationType?: 1 | 2;
  liveStartDate?: string;
  liveEndDate?: string;
  carType?: string;
  carVersion?: string;
  contentType?: string;
}) {
  return request<ApiResult<LiveQuotationRecordGroupData>>(
    '/new-media-api/vw/live/quotation/record/group',
    {
      method: 'POST',
      data: params,
    },
  );
}

export async function DouyinLiveExternalTemplate() {
  return request<any>(`/new-media-api/douyin/live/external/download-template`, {
    method: 'GET',
    responseType: 'blob',
  });
}
