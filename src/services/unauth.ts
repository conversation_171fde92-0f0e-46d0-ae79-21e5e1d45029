// 无需登录鉴权的接口
import { request } from '@umijs/max';
import { FieldList } from './business';
import { ApiResult, PageBasicParams } from './common';
import { PlatForm } from '@/utils/platform';
import {
  ChatViolationAccountRes,
  ChatViolationTeamRes,
  QualityExViolationAccountRes,
  QualityExViolationTeamRes,
  SummaryCount,
} from './daily';
import { QualityTypeSelectItem, TrendRes } from './quality';
import { TreeData } from './team';

export type QualityExViolationData = {
  /**
   * 结束日期
   */
  endDate: string;
  /**
   * 动态维度字段
   */
  fieldList?: FieldList[];
  /**
   * 搜索内容
   */
  keyword?: string;
  /**
   * 空播等级
   */
  liveAfkLevel?: number;
  /**
   * 平台
   */
  platform: PlatForm;
  /**
   * 项目 id
   */
  projectId: number;
  /**
   * 质检类别
   */
  qualityCategoryTypeList?: string[];
  /**
   * 开始日期
   */
  startDate: string;
  /**
   * 团队编码集合
   */
  teamCodeList?: string[];
};

export function QualityExViolationTeamDaily(
  params: { payload?: string | null } & PageBasicParams,
  data: QualityExViolationData,
) {
  return request<ApiResult<QualityExViolationTeamRes>>(
    '/new-media-api/quality/ex/violation/aggregation/team/daily',
    {
      method: 'POST',
      data,
      params,
    },
  );
}

export function ExportQualityTeamDaily(data: any, params: any) {
  return request('/new-media-api/quality/ex/violation/aggregation/team/export/daily', {
    method: 'POST',
    data,
    params,
  });
}

export function QualityExViolationAccountDaily(params: any, data: any) {
  return request<ApiResult<QualityExViolationAccountRes>>(
    '/new-media-api/quality/ex/violation/aggregation/account/daily',
    {
      method: 'POST',
      data,
      params,
    },
  );
}

export function ExportQualityAccountDaily(data: any, params: any) {
  return request('/new-media-api/quality/ex/violation/aggregation/account/export/daily', {
    method: 'POST',
    data,
    params,
  });
}

export function QualityExTrendDaily(params: {
  payload?: string | null;
  dateType?: number;
  projectId?: string | number;
  type: number; //类型 1-账号 2-团队
  platform: PlatForm;
}) {
  return request<ApiResult<TrendRes>>('/new-media-api/quality/ex/violation/trend/daily', {
    method: 'GET',
    params,
  });
}

export type ExCountDaily = {
  /**
   * 时间类型
   * 1 :昨日/近一天
   * 2 :上周/近一周
   * 3 :上月/近一月
   * 4 :自定义
   * 5 :近 90天
   * 6 :自然周
   * 7 :自然月
   */
  dateType: number;
  /**
   * 结束日期
   */
  endDate?: string;
  /**
   * 加密参数
   */
  payload?: string | null;
  /**
   * 平台
   */
  platform: PlatForm;
  /**
   * 项目 id
   */
  projectId?: number;
  /**
   * 开始日期
   */
  startDate?: string;
};

export function GetQualityExCountDaily(params: ExCountDaily) {
  return request<ApiResult<SummaryCount>>('/new-media-api/quality/ex/violation/count/daily', {
    method: 'GET',
    params,
  });
}

export function GetTreeDataDaily(params: any) {
  return request<ApiResult<TreeData>>('/new-media-api/project_team/get-team-depth/daily', {
    method: 'GET',
    params,
  });
}

export function ChatViolationTeamDaily(params: any, data: any) {
  return request<ApiResult<ChatViolationTeamRes>>(
    '/new-media-api/chat/violation/aggregation/team/daily',
    {
      method: 'POST',
      data,
      params,
    },
  );
}

export function ExportChatTeamDaily(data: any, params: any) {
  return request('/new-media-api/chat/violation/aggregation/team/export/daily', {
    method: 'POST',
    data,
    params,
  });
}

export function ChatViolationAccountDaily(params: any, data: any) {
  return request<ApiResult<ChatViolationAccountRes>>(
    '/new-media-api/chat/violation/aggregation/account/daily',
    {
      method: 'POST',
      data,
      params,
    },
  );
}

export function ExportChatAccountDaily(data: any, params: any) {
  return request('/new-media-api/chat/violation/aggregation/account/export/daily', {
    method: 'POST',
    data,
    params,
  });
}

export function ChatTrendDaily(params: {
  payload?: string | null;
  dateType?: number;
  projectId?: string | number;
  type: number; //类型 1-账号 2-团队
  platform: PlatForm;
}) {
  return request<ApiResult<TrendRes>>('/new-media-api/chat/violation/trend/daily', {
    method: 'GET',
    params,
  });
}

export function GetChatCountDaily(params: ExCountDaily) {
  return request<ApiResult<SummaryCount>>('/new-media-api/chat/violation/count/daily', {
    method: 'GET',
    params,
  });
}

export function GetQualityTypeSelectDaily(params: { payload?: string | null }) {
  return request<ApiResult<QualityTypeSelectItem[]>>(
    '/new-media-api/quality/monitoring-word/select/daily',
    {
      method: 'GET',
      params,
    },
  );
}
