import { request } from '@umijs/max';
import { ApiR<PERSON>ult, PagintaionData } from './common';
import { PlatForm } from '@/utils/platform';
import { QualityTargetType } from './constants';
import { TagType, TeamFieldList } from './setting';

type TrendParams = {
  startTime?: string;
  endTime?: string;
  type: string;
  platform?: string;
};

type PostTrendParams = {
  postId?: string;
} & TrendParams;

type LiveTrendParams = {
  roomId?: string;
} & TrendParams;

export type spriteInfoListItem = {
  targetUrl: string;
  spriteUrls: any[];
};

type ResQualitySprite = {
  targetId: string;
  targetType: number;
  platform: number;
  frameInfo: {
    width: number;
    height: number;
    frameNum: number;
  };
  spriteInfoList: spriteInfoListItem[];
};

export type WordCloudData = {
  word: string;
  count: number;
};

export type ResQualityRule = {
  qualityRuleList: QualityRuleList[];
  words: string[];
};

export type ResQualityRuleWithProject = {
  projectId: number;
  projectName: string;
  qualityRuleList: QualityRuleList[];
  words: string[];
};

type QualityRuleList = {
  id: number;
  name: string;
  ruleType: number;
  type: number;
  status: number;
  createTime: string;
  updateTime: string;
};

type ResSubtitle = {
  id: string;
  postId: string;
  subtitle: string;
  createTime: string;
  updateTime: string;
  exInfo: any;
};

export type PostQualityItem = {
  /**
   * 是否是蓝 v 是 ：1 否：:0
   */
  blueVipFlag: number;
  /**
   * 账号头像
   */
  avatar: string;
  /**
   * 清除标识 0-正常 1-已清除
   */
  cleanFlag?: number;
  /**
   * 清除时间
   */
  cleanTime?: string;
  /**
   * 收藏次数
   */
  collectCount: number;
  /**
   * 评论次数
   */
  commentCount: number;
  /**
   * 完播率
   */
  completionPlayRate: number;
  /**
   * 封面
   */
  cover: string;
  /**
   * 点赞次数
   */
  diggCount: number;
  /**
   * 作品时长 单位：秒
   */
  duration: number;
  /**
   * 粉丝总数
   */
  followerCount: number;
  /**
   * 粉丝增量
   */
  followerGrowth: number;
  /**
   * 质检词名称-逗号分割
   */
  monitoringNameStr: string;
  /**
   * 违规质检分类信息集合
   */
  monitoringNameInfoList?: {
    /**
     * 质检分类名称
     */
    monitoringName?: string;
    /**
     * 分类 主键id
     */
    monitoringNameId?: number;
  }[];
  /**
   * 账号昵称
   */
  nickname: string;
  /**
   * 平台 id
   */
  platform: number;
  /**
   * 平台名字
   */
  platformName: string;
  /**
   * 播放数
   */
  playCount: number;
  /**
   * 作品 id
   */
  postId: string;
  /**
   * 作品链接
   */
  postUrl: string;
  /**
   * 项目 id
   */
  projectId: string;
  /**
   * 发布时间
   */
  publishTime: string;
  /**
   * 质检结果状态 0: 正常 1: 异常
   */
  qualityResultStatus: number;
  /**
   * 质检状态 0: 分析中 1: 已结束
   */
  qualityStatus: number;
  /**
   * 分享数
   */
  shareCount: number;
  /**
   * 作品源地址
   */
  shareLink: string;
  /**
   * 账号标签集合
   */
  tags: TagType[];
  /**
   * 标签组 字符串
   */
  tagsStr: string;
  /**
   * 团队编码
   */
  teamCode: string;
  /**
   * 动态维度
   */
  teamFieldList: TeamFieldList[];
  /**
   * 作品标题
   */
  title: string;
  /**
   * 作品话题
   */
  topic: string;
  /**
   * 外显 id
   */
  showAccountId?: string;
  /**
   * 作品源地址
   */
  url?: string;
};

export type LiveQualityItem = {
  /**
   * 账号 id
   */
  accountId?: string;
  /**
   * 空播挂播等级 1-严重 2-轻微 3-正常
   */
  afkLevel?: number;
  /**
   * 主播照片
   */
  anchorImage?: string;
  /**
   * 主播照片识别状态
   */
  anchorImageStatus?: number;
  /**
   * 主播形象违规信息集合
   */
  anchorImageViolationInfoList?: {
    /**
     * 主播形象违规类型
     */
    anchorImageViolationType?: number;
    /**
     * 主播形象违规类型名称
     */
    anchorImageViolationTypeName?: string;
  }[];
  /**
   * 主播形象违规类型 字符串
   */
  anchorImageViolationTypeStr?: string;
  /**
   * 账号头像
   */
  avatar?: string;
  /**
   * 点赞次数
   */
  diggCount?: number;
  /**
   * 折扣误导状态 0: 正常 1: 异常
   */
  discountMisleadStatus?: number;
  /**
   * 弹幕条数
   */
  dmCount?: number;
  /**
   * 直播时长 单位：秒
   */
  duration?: number;
  /**
   * 清除标识 0-正常 1-已清除
   */
  cleanFlag?: number;
  /**
   * 清除时间
   */
  cleanTime?: string;
  /**
   * 粉丝总数
   */
  followerCount?: number;
  /**
   * 直播空播检测时长
   */
  liveAfkCheckDuration?: number;
  /**
   * 直播空播时长-单位秒
   */
  liveAfkDuration?: number;
  /**
   * 直播空播率
   */
  liveAfkRate?: string;
  /**
   * 结束时间
   */
  liveEndTime?: string;
  /**
   * 直播拉流地址
   */
  livePullUrl?: string;
  /**
   * 开播时间
   */
  liveStartTime?: string;
  /**
   * 直播状态 0: 直播中 1: 已结束
   */
  liveStatus?: number;
  /**
   * 违规质检分类信息集合
   */
  monitoringNameInfoList?: {
    /**
     * 质检分类名称
     */
    monitoringName?: string;
    /**
     * 分类 主键id
     */
    monitoringNameId?: number;
  }[];
  /**
   * 质检词名称-逗号分割
   */
  monitoringNameStr?: string;
  /**
   * 账号昵称
   */
  nickname?: string;
  /**
   * 平台 id
   */
  platform?: number;
  /**
   * 平台名字
   */
  platformName?: string;
  /**
   * 人气峰值
   */
  popularityPeakNum?: number;
  /**
   * 项目 id
   */
  projectId?: string;
  /**
   * 质检结果状态 0: 正常 1: 异常
   */
  qualityResultStatus?: number;
  /**
   * 质检状态 0: 分析中 1: 已结束
   */
  qualityStatus?: number;
  /**
   * 主键 id
   */
  replayId?: number;
  /**
   * 回放地址 m3u8格式
   */
  replayUrls?: string[];
  /**
   * 房间 id
   */
  roomId?: string;
  /**
   * 房间标题
   */
  roomTitle?: string;
  /**
   * 账号标签集合
   */
  tags?: TagType[];
  /**
   * 标签组 字符串，标签组 字符串
   */
  tagsStr?: string;
  /**
   * 团队编码
   */
  teamCode?: string;
  /**
   * 动态维度
   */
  teamFieldList?: TeamFieldList[];
  /**
   * 平台 uid
   */
  uid?: string;
  /**
   * 累计观看人数
   */
  viewCount?: number;
  /**
   * 累计观看人次
   */
  viewTime?: number;
  /**
   * 封面
   */
  cover?: string;
  /**
   * 是否是蓝 v 是 ：1 否：:0
   */
  blueVipFlag: number;
  /**
   * 外显 id
   */
  showAccountId?: string;
  /**
   * 录制状态
   */
  recordStatus?: number;
  /**
   * 是否是拉流地址 0-否 1-是
   */
  isPullUrl?: number;
  /**
   * 视频地址
   */
  videoUrl?: string;
};

export type LiveQualityEcommerceItem = {
  /**
   * 账号 id
   */
  accountId?: string;
  /**
   * 空播挂播等级
   */
  afkLevel?: number;
  /**
   * 账号头像
   */
  avatar?: string;
  /**
   * 是否是蓝v，企业用户 0: 否 1:是
   */
  blueVipFlag?: number;
  /**
   * 清除标识 0-正常 1-已清除
   */
  cleanFlag?: number;
  /**
   * 清除时间
   */
  cleanTime?: string;
  /**
   * 封面
   */
  cover?: string;
  /**
   * 点赞次数
   */
  diggCount?: number;
  /**
   * 弹幕条数
   */
  dmCount?: number;
  /**
   * 直播时长 单位：秒
   */
  duration?: number;
  /**
   * 粉丝总数
   */
  followerCount?: number;
  /**
   * 是否拉流地址 0-否 1-是
   */
  isPullUrl?: number;
  /**
   * 直播空播检测时长
   */
  liveAfkCheckDuration?: number;
  /**
   * 直播空播时长-单位秒
   */
  liveAfkDuration?: number;
  /**
   * 直播空播率
   */
  liveAfkRate?: string;
  /**
   * 结束时间
   */
  liveEndTime?: string;
  /**
   * 直播拉流地址
   */
  livePullUrl?: string;
  /**
   * 开播时间
   */
  liveStartTime?: string;
  /**
   * 直播状态 0: 直播中 1: 已结束
   */
  liveStatus?: number;
  /**
   * 账号昵称
   */
  nickname?: string;
  /**
   * 平台 id
   */
  platform?: number;
  /**
   * 平台名字
   */
  platformName?: string;
  /**
   * 人气峰值
   */
  popularityPeakNum?: number;
  /**
   * 项目 id
   */
  projectId?: string;
  /**
   * 质检状态 0: 分析中 1: 已结束
   */
  qualityStatus?: number;
  /**
   * 录制状态
   */
  recordStatus?: number;
  /**
   * 主键 id
   * 录制表主键 id
   */
  replayId?: number;
  /**
   * 回放地址 m3u8格式
   */
  replayUrls?: string[];
  /**
   * 房间 id
   */
  roomId?: string;
  /**
   * 房间标题
   */
  roomTitle?: string;
  /**
   * 外显 id
   */
  showAccountId?: string;
  /**
   * 千川整体消耗
   */
  statCost?: number;
  /**
   * 团队编码
   */
  teamCode?: string;
  /**
   * 千川整体成交订单数
   */
  totalPayOrderCount?: number;
  /**
   * 千川整体成交金额
   */
  totalPayOrderGmvIncludeCoupon?: number;
  /**
   * 平台 uid
   */
  uid?: string;
  /**
   * 视频url（mp4）
   */
  videoUrl?: string;
  /**
   * 累计观看人数
   */
  viewCount?: number;
  /**
   * 累计观看人次
   */
  viewTime?: number;
};

export type QualityItem = {
  /**
   * 音频地址
   */
  audioUrl: string;
  /**
   * 创建时间
   */
  createTime: string;
  /**
   * 创建者
   */
  creator: string;
  /**
   * 备注
   */
  description: string;
  /**
   * 标记结束时间 单位秒
   */
  endTime: number;
  /**
   * 拓展信息
   */
  extra: string;
  /**
   * 分组 id
   */
  groupId: number;
  /**
   * 主键
   */
  id: number;
  /**
   * 命中标记
   */
  mark?: string;
  /**
   * 质检词库主键
   */
  monitoringWordId: number;
  /**
   * 质检词库分类名称
   */
  monitoringWordName: string;
  /**
   * 图片地址
   */
  picUrl: string[];
  /**
   * 平台
   */
  platform: number;
  /**
   * 1: 标题文字 2:语音 3:封面 4:视频画面
   */
  qualityRuleType?: number;
  /**
   * 标记开始时间 单位秒
   */
  startTime: number;
  /**
   * 目标主键 id
   */
  targetId: string;
  /**
   * 目标类型 1: 直播 2: 作品
   */
  targetType: number;
  /**
   * 质检类型 类型 1：系统 2：人工
   */
  type: number;
  /**
   * 违规类型 1-疑似违规 2-违规
   */
  violationType: number;
};

export type LiveSubtitleItem = {
  id: string;
  roomId: string;
  platform: number;
  subtitle: string;
  startTime: string;
  endTime: string;
  status: any;
  createTime: string;
  updateTime: string;
  startDurationTime: string;
  endDurationTime: string;
  exInfo: ExInfo;
};
export interface ExInfo {
  id: string;
  targetId: string;
  targetType: number;
  platform: number;
  groupId: string;
  type: number;
  qualityRuleType: number;
  violationType: number;
  monitoringWordId: string;
  monitoringWordName: string;
  description: string;
  mark: string;
  picUrl: string;
  audioUrl: string;
  extra: string;
  startTime: string;
  endTime: string;
  creator: string;
  createTime: string;
}

export type LiveScreenItem = {
  roomId: string;
  platform: number;
  url: string;
  createTime: string;
  exInfo: ExInfo | null;
  index?: number;
};

export function GetPostQuality(params: {
  postId: string | undefined;
  platform: string | undefined;
}) {
  return request<ApiResult<PostQualityItem>>('/new-media-api/quality/post/query', {
    method: 'GET',
    params,
  });
}

export function GetLiveQuality(params: {
  roomId: string | undefined;
  platform: string | undefined;
}) {
  return request<ApiResult<LiveQualityItem>>('/new-media-api/quality/live/query', {
    method: 'GET',
    params,
  });
}

export function GetLiveQualityEcommerce(params: {
  roomId: string | undefined;
  platform: string | undefined;
}) {
  return request<ApiResult<LiveQualityEcommerceItem>>(
    '/new-media-api/quality/live/ecommerce/query',
    {
      method: 'GET',
      params,
    },
  );
}

export function GetPostTrend(params: PostTrendParams) {
  return request<ApiResult>('/new-media-api/quality/post/trend', {
    method: 'GET',
    params,
  });
}

export type TimeLineChartData = {
  time: string;
  trendData: { value: number; lastValue: number };
};

export function GetLiveTrend(params: LiveTrendParams) {
  return request<ApiResult<TimeLineChartData[]>>('/new-media-api/quality/live/trend', {
    method: 'GET',
    params,
  });
}

export function GetQualityExList(params: {
  endTime?: number;
  platform?: string;
  startTime?: number;
  targetId?: string;
  targetType: QualityTargetType;
}) {
  return request<ApiResult<QualityItem[]>>('/new-media-api/quality/ex/list', {
    method: 'GET',
    params,
  });
}

export function GetQualitySprite(params: {
  targetId?: string;
  platform?: string;
  targetType: number;
}) {
  return request<ApiResult<ResQualitySprite>>('/new-media-api/quality/sprite/query', {
    method: 'GET',
    params,
  });
}

// 作品词云
export function GetPostWordCloud(params: { postId?: string; platform?: string }) {
  return request<ApiResult<WordCloudData[]>>('/new-media-api/quality/post/comment_word_cloud', {
    method: 'GET',
    params,
  });
}

// 直播弹幕词云
export function GetLiveWordCloud(params: { roomId?: string; platform?: string }) {
  return request<ApiResult<WordCloudData[]>>('/new-media-api/quality/live/dm_word_cloud', {
    method: 'GET',
    params,
  });
}

// 作品字幕
export function GetPostSubtitle(params: any) {
  return request<ApiResult<ResSubtitle>>('/new-media-api/quality/post/subtitle', {
    method: 'GET',
    params,
  });
}

// 直播字幕
export function getLiveSubtitleAll(params: any) {
  return request<ApiResult<LiveSubtitleItem[]>>('/new-media-api/quality/live/subtitle/list', {
    method: 'GET',
    params,
  });
}
// 作品评论
export function GetPostComment(params: any) {
  return request<ApiResult<{ items: any[]; total: number }>>(
    '/new-media-api/quality/post/comment/page',
    {
      method: 'GET',
      params,
    },
  );
}

// 直播评论
export function GetLiveComment(params: any) {
  return request<ApiResult<{ items: any[]; total: number }>>(
    '/new-media-api/quality/live/comment/dm/page',
    {
      method: 'GET',
      params,
    },
  );
}
export interface Daum {
  id: string;
  nickname: string;
  content: string;
  time: string;
  startTime: string;
  suspectFlag: number;
}

export function getLiveCommentAll(params: any) {
  return request<ApiResult<Daum[]>>('/new-media-api/quality/live/comment/dm/list', {
    method: 'GET',
    params,
  });
}
export function PostQualityPage(params: any) {
  return request<ApiResult<PagintaionData<PostQualityItem>>>('/new-media-api/quality/post/page', {
    method: 'GET',
    params,
  });
}

export function LiveQualityPage(params: any) {
  return request<ApiResult<PagintaionData<LiveQualityItem>>>('/new-media-api/quality/live/page', {
    method: 'GET',
    params,
  });
}

export type QualityLiveHighLight = {
  /**
   * 清除标识 0-正常 1-已清除
   */
  cleanFlag?: number;
  /**
   * 清除时间
   */
  cleanTime?: string;
  /**
   * 封面地址，封面地址
   */
  cover?: string;
  /**
   * 结束时间，结束时间
   */
  endTime: string;
  /**
   * 在线人数，在线人数
   */
  onlineNum?: number;
  /**
   * 开始时间，开始时间
   */
  startTime: string;
  /**
   * 片段视频地址，片段视频地址
   */
  videoUrl?: string;
};

export function GetLiveHighLight(params: {
  roomId: string | undefined;
  platform: string | undefined;
}) {
  return request<ApiResult<QualityLiveHighLight[]>>(
    '/new-media-api/quality/live/high_light/query',
    {
      method: 'GET',
      params,
    },
  );
}

export type QualityLiveEcommerceHighLight = {
  /**
   * 弹幕列表
   */
  dmList?: string[];
  /**
   * 高光片段截图URL列表
   */
  highlightScreenshots?: string[];
  /**
   * 高光片段话术（从字幕数据提取）
   * 高光片段话术
   */
  highlightSubtitle?: string;
  /**
   * 高光片段链接
   */
  highlightVideoUrl?: string;
  /**
   * 直播时长（分钟）
   * 直播时长(分钟)
   */
  liveDurationOfMinutes?: number;
  /**
   * 直播结束时间
   */
  liveEndTime?: string;
  /**
   * 直播开始时间
   */
  liveStartTime?: string;
  /**
   * 高光片段峰值成交金额
   */
  peakGmv?: number;
  /**
   * 高光时间（峰值发生的具体时间点）
   * 高光时间
   */
  peakTime?: string;
  /**
   * 直播间ID
   */
  roomId?: string;
  /**
   * 直播间标题
   */
  roomTitle?: string;
};

export function GetLiveEcommerceHighLight(params: {
  roomId: string | undefined;
  platform: string | undefined;
}) {
  return request<ApiResult<QualityLiveEcommerceHighLight>>(
    '/new-media-api/quality/live/ecommerce/highlight',
    {
      method: 'GET',
      params,
    },
  );
}

export function ExportLiveEcommerceHighLight(params: {
  roomId: string | undefined;
  platform: string | undefined;
}) {
  return request('/new-media-api/quality/live/ecommerce/highlight/export', {
    method: 'GET',
    params,
  });
}

export function SaveManual(params: any) {
  return request<ApiResult>('/new-media-api/quality/ex/manual/save', {
    method: 'POST',
    data: params,
  });
}

export function liveScreenAll(params: any) {
  return request<ApiResult<LiveScreenItem[]>>('/new-media-api/quality/live/screen_shot/list', {
    method: 'GET',
    params,
  });
}

export function DeleteManual(params: any) {
  return request<ApiResult>('/new-media-api/quality/ex/delete', {
    method: 'POST',
    data: params,
  });
}

export type TrendList = {
  /**
   * 数量
   */
  count?: number;
  /**
   * 日期
   */
  date?: string;
  /**
   * 环比
   */
  mom?: string;
};

export type TrendRes = {
  /**
   * 总量
   */
  totalCount?: number;
  /**
   * 范围趋势
   */
  trendList?: TrendList[];
};

export function QualityExTrend(params: {
  dateType?: number;
  projectId?: string;
  type: number; //类型 1-账号 2-团队
  platform: PlatForm;
}) {
  return request<ApiResult<TrendRes>>('/new-media-api/quality/ex/violation/trend', {
    method: 'GET',
    params,
  });
}

export enum SceneTypeEnum {
  TEXT = 1,
  BEHAVIOR = 2,
  COMMENT = 3,
}

export type MonitorItem = {
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 创建人
   */
  creator?: string;
  /**
   * 说明
   */
  description?: string;
  /**
   * 主键 id
   */
  id: number;
  /**
   * 是否禁用 0-正常 1-禁用
   */
  isDisabled?: number;
  /**
   * 修改人
   */
  modifier?: string;
  /**
   * 分类
   */
  name: string;
  /**
   * 项目id
   */
  projectId?: number;
  /**
   * 场景类型 1-文字标识 2-行为标识 3-弹幕标识
   */
  sceneType: SceneTypeEnum;
  /**
   * 敏感词库
   */
  sensitiveWord?: string[];
  /**
   * 疑似词库
   */
  suspectedWord?: string[];
  /**
   * 1-默认 2-自定义
   */
  type?: number;
  /**
   * 更新时间
   */
  updateTime?: string;
  /**
   * 规范类型
   */
  standardType?: number;
};

export function QualityMonitorWordList(params: { projectId?: string; sceneType?: SceneTypeEnum }) {
  return request<ApiResult<MonitorItem[]>>('/new-media-api/quality/monitoring-word/list', {
    method: 'GET',
    params,
  });
}

export function UpdateQualityMonitorSwitch(data: {
  /**
   * 主键id
   */
  id: number | string;
  /**
   * 是否禁用 0-正常 1-禁用
   */
  isDisabled: number;
  /**
   * 项目id
   */
  projectId?: string;
}) {
  return request('/new-media-api/quality/monitoring-word/update/status', {
    method: 'POST',
    data,
  });
}

export type MonitorData = {
  /**
   * 说明
   */
  description?: string;
  /**
   * 违规词-逗号分割
   */
  suspectedWord?: string;
  /**
   * 分类
   */
  name: string;
  /**
   * 项目id
   */
  projectId?: string;
  /**
   * 场景类型 1-文字标识 2-行为标识 3-弹幕标识
   */
  sceneType: number;
  /**
   * 敏感词-逗号分割
   */
  sensitiveWord?: string;
  /**
   * 规范类型
   */
  standardType?: number;
};

export type UpdateMonitorData = {
  /**
   * 主键 id
   */
  id?: number | string;
} & MonitorData;

export function QualityMonitorWordSave(data: MonitorData) {
  return request('/new-media-api/quality/monitoring-word/save', {
    method: 'POST',
    data,
  });
}

export function QualityMonitorWordUpdate(data: UpdateMonitorData) {
  return request('/new-media-api/quality/monitoring-word/update', {
    method: 'POST',
    data,
  });
}

export function QualityMonitorWordDelete(params: { id: any }) {
  return request('/new-media-api/quality/monitoring-word/delete', {
    method: 'POST',
    params,
  });
}

export type QualityRuleData = {
  /**
   * 创建时间
   */
  createTime: string;
  /**
   * 主键
   */
  id: string;
  /**
   * 规则名称
   */
  name: string;
  /**
   * 规则类型 1:标题文字 2:语音 3:封面 4:视频画面
   */
  ruleType: number;
  /**
   * 状态 0:禁用 1:启用
   */
  status: number;
  /**
   * 更新时间
   */
  updateTime: string;
};

export function QualityRuleList(params: { projectId?: string }) {
  return request<ApiResult<QualityRuleData[]>>('/new-media-api/quality/rule/list', {
    method: 'GET',
    params,
  });
}

export function QualityRuleUpdate(data: { id: string; projectId?: string; status: number }) {
  return request('/new-media-api/quality/rule/update', {
    method: 'POST',
    data,
  });
}

export function ExportLiveQuality(params: any) {
  return request('/new-media-api/quality/live/export', {
    method: 'GET',
    params,
  });
}

export function ExportPostQuality(params: any) {
  return request('/new-media-api/quality/post/export', {
    method: 'GET',
    params,
  });
}

export type QualityTypeSelectItem = {
  /**
   * 附加属性
   */
  attributes?: { [key: string]: any };
  /**
   * 是否禁用
   */
  disabled: boolean;
  /**
   * 显示的数据
   */
  name: string;
  /**
   * 是否被选中
   */
  selected: boolean;
  /**
   * 分组标识
   */
  type: string;
  /**
   * 选中获取的属性
   */
  value: string;
};

export function GetQualityTypeSelect(params: { projectId?: string }) {
  return request<ApiResult<QualityTypeSelectItem[]>>(
    '/new-media-api/quality/monitoring-word/select',
    {
      method: 'GET',
      params,
    },
  );
}

export function getAggregation(params: {
  projectId: number;
  carType: string;
  aggregationField: string;
  liveStartDate: string;
  liveEndDate: string;
}) {
  return request<
    ApiResult<
      {
        title: string;
        data: IAggregation[];
      }[]
    >
  >('/new-media-api/vw/live/quotation/record/aggregation/v2', {
    method: 'POST',
    data: params,
  });
}

export function aggregationExport(data: {
  projectId: number;
  carType: string;
  aggregationField: string;
  liveStartDate: string;
  liveEndDate: string;
}) {
  return request('/new-media-api/vw/live/quotation/record/aggregation/export', {
    method: 'POST',
    data,
  });
}

export interface IAggregation {
  fieldValue: string;
  guidePrice: number;
  maxPrice: MaxPrice;
  minPrice: MinPrice;
  averagePrice: AveragePrice;
  quotationScope: number[];
  median: Median;
}

export interface MaxPrice {
  price: number;
  historyPrice: number;
  percent: number;
}

export interface MinPrice {
  price: number;
  historyPrice: number;
  percent: number;
}

export interface AveragePrice {
  price: number;
  historyPrice: number;
  percent: number;
}

export interface Median {
  price: number;
  historyPrice: number;
  percent: number;
}

export type LiveDiscountList = {
  /**
   * 创建时间
   */
  createTime: string;
  /**
   * 说明
   */
  description: string;
  /**
   * 主键
   */
  id: number;
  /**
   * 是否禁用 0-否 1-是
   */
  isDisabled: number;
  /**
   * 项目id
   */
  projectId: number;
  /**
   * 敏感词
   */
  sensitiveWord: string[];
  /**
   * 更新时间
   */
  updateTime: string;
};

export type LiveDiscountSwitchList = {
  /**
   * 创建时间
   */
  createTime: string;
  /**
   * 主键
   */
  id: number;
  /**
   * 是否禁用 0-否 1-是
   */
  isDisabled: number;
  /**
   * 场景名称
   */
  name: string;
  /**
   * 项目id
   */
  projectId: number;
  /**
   * 场景类型 1: 标题文字 2: 语音 3: 封面 4: 视频画面
   */
  type: number;
  /**
   * 更新时间
   */
  updateTime: string;
};

export type QualityLiveAnchorImage = {
  /**
   * 创建时间
   */
  createTime: string;
  /**
   * 说明
   */
  description: string;
  /**
   * 主键
   */
  id: number;
  /**
   * 是否禁用 0-否 1-是
   */
  isDisabled: number;
  /**
   * 开关名字
   */
  name: string;
  /**
   * 项目id
   */
  projectId: number;
  /**
   * 类型 1-着装违规 2-发型凌乱 3-妆容浓厚 4-眼妆浓厚 5-指甲过长 6-配饰繁琐
   */
  type: number;
  /**
   * 更新时间
   */
  updateTime: string;
  /**
   * 规范类型
   */
  standardType?: number;
};

export function GetAnchorImageList(params: { projectId?: string }) {
  return request<ApiResult<QualityLiveAnchorImage[]>>(
    '/new-media-api/quality/live/anchor-image/switch/list',
    {
      method: 'GET',
      params,
    },
  );
}

export function UpdateAnchorImageSwitch(data: {
  projectId?: string;
  /**
   * 主键id
   */
  id: number;
  /**
   * 是否禁用 0-正常 1-禁用
   */
  isDisabled: number;
}) {
  return request('/new-media-api/quality/live/anchor-image/switch/update/status', {
    method: 'POST',
    data,
  });
}

export function UpdateAnchorImageDescription(data: {
  projectId?: string;
  /**
   * 说明
   */
  description?: string;
  /**
   * 主键 id
   */
  id: number;
  /**
   * 规范类型
   */
  standardType?: number;
}) {
  return request('/new-media-api/quality/live/anchor-image/switch/update', {
    method: 'POST',
    data,
  });
}

export function BatchUpdateAnchorImageSwitch(data: {
  projectId?: string;
  /**
   * 是否禁用 0-正常 1-禁用
   */
  isDisabled: number;
}) {
  return request('/new-media-api/quality/live/anchor-image/switch/update/status/batch', {
    method: 'POST',
    data,
  });
}

export function BatchUpdateMonitorWordStatus(data: {
  /**
   * 是否禁用 0-正常 1-禁用
   */
  isDisabled: number;
  /**
   * 项目id
   */
  projectId?: string;
  /**
   * 场景类型 1-文字标识 2-行为标识 3-弹幕标识
   */
  sceneType: number;
}) {
  return request('/new-media-api/quality/monitoring-word/update/status/batch', {
    method: 'POST',
    data,
  });
}

export function GetAnchorImageViolationSelect(params: { projectId?: string }) {
  return request<ApiResult<QualityTypeSelectItem[]>>(
    '/new-media-api/quality/live/anchor-image/violation/type/selectData',
    {
      method: 'GET',
      params,
    },
  );
}

export function GetAnchorImageViolationSelectDaily(params: { payload?: string | null }) {
  return request<ApiResult<QualityTypeSelectItem[]>>(
    '/new-media-api/quality/live/anchor-image/violation/type/selectData/daily',
    {
      method: 'GET',
      params,
    },
  );
}

export type AnchorImageViolationDetailInfo = {
  /**
   * 违规时间
   */
  exTime?: string;
  /**
   * 违规名称
   */
  name?: string;
  /**
   * 账号昵称
   */
  nickname?: string;
  /**
   * 违规图片
   */
  picUrl?: string[];
  /**
   * 标题
   */
  title?: string;
  /**
   * 违规类型
   */
  type?: number;
};

export function GetAnchorImageViolationDetailInfo(params: {
  /**
   * 平台
   */
  platform?: number;
  /**
   * 项目 id
   */
  projectId?: string;
  /**
   * 房间 id
   */
  roomId?: string;
  /**
   * 违规类型
   * 1 :着装违规
   * 2 :发型凌乱
   * 3 :妆容浓厚
   * 4 :眼妆浓厚
   * 5 :指甲过长
   * 6 :配饰繁琐
   */
  type?: number;
}) {
  return request<ApiResult<AnchorImageViolationDetailInfo>>(
    '/new-media-api/quality/live/anchor-image/violation/detail',
    {
      method: 'GET',
      params,
    },
  );
}

export type QualityExDetail = {
  /**
   * 违规内容
   */
  exInfoList?: {
    /**
     * 内容
     */
    content?: string;
    /**
     * 异常时间
     */
    exTime?: string;
    /**
     * 分组 id
     */
    groupId?: number;
    /**
     * 标记内容
     */
    mark?: string;
    /**
     * 图片
     */
    picUrl?: string;
    /**
     * 质检规则类型 1: 标题文字 2:语音 3:封面 4:视频画面
     */
    qualityRuleType?: number;
  }[];
  /**
   * 质检分类名称
   */
  monitoringName?: string;
  /**
   * 账号昵称
   */
  nickname?: string;
  /**
   * 标题
   */
  title?: string;
};

export function GetQualityExDetail(params: {
  /**
   * 监控分类主键
   */
  monitoringNameId?: number;
  /**
   * 平台
   */
  platform?: number;
  /**
   * 目标 id
   */
  targetId?: string | number;
  /**
   * 目标类型 1：直播 2：作品
   */
  targetType?: number;
  /**
   * 项目 id
   */
  projectId?: string;
}) {
  return request<ApiResult<QualityExDetail>>('/new-media-api/quality/ex/detail', {
    method: 'GET',
    params,
  });
}

export type AnchorImageViolationItem = {
  /**
   * 违规名称
   */
  name: string;
  /**
   * 图片地址
   */
  picUrl: string;
  /**
   * 相对时间
   */
  startTime: number;
  /**
   * 违规类型
   */
  type: number;
};

export function GetAnchorImageViolationList(params: {
  /**
   * 平台
   */
  platform?: string;
  /**
   * 项目 id
   */
  projectId?: string;
  /**
   * 房间 id
   */
  roomId?: string;
  /**
   * 违规类型
   * 1 :着装违规
   * 2 :发型凌乱
   * 3 :妆容浓厚
   * 4 :眼妆浓厚
   * 5 :指甲过长
   * 6 :配饰繁琐
   */
  type?: number;
}) {
  return request<ApiResult<AnchorImageViolationItem[]>>(
    '/new-media-api/quality/live/anchor-image/violation/list',
    {
      method: 'GET',
      params,
    },
  );
}

export type QualityLiveDiscountItem = {
  /**
   * 内容
   */
  content: string;
  /**
   * 标记
   */
  mark: string;
  /**
   * 图片地址
   */
  picUrl: string;
  /**
   * 相对时间
   */
  startTime: number;
  /**
   * 类型 1: 标题 2:语音 3:封面 4:画面
   */
  type: number;
};
