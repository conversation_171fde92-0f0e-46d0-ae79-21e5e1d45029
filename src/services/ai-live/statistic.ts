import { request } from '@umijs/max';
import { ApiResult, PageBasicParams, PagintaionData } from '../common';

export interface IStatisticResponse {
  carVersion: CarVersion[];
  content: Content[];
  liveRoom: LiveRoom[];
  anchor: Anchor[];
  roomTotal: number;
}

export interface CarVersion {
  key: string;
  value: string;
}

export interface Content {
  key: string;
  value: string;
}

export interface LiveRoom {
  key: string;
  value: string;
}

export interface Anchor {
  key: string;
  value: string;
}

export function getStatistic(params: any) {
  return request<ApiResult<IStatisticResponse>>(
    '/new-media-api/vw/live/quotation/record/statistic',
    {
      method: 'POST',
      data: params,
    },
  );
}

export interface IStatisticVersionResponse {
  carVersion: string;
  cnt: number;
  contents: Content[];
}

export interface Content {
  contentType: string;
  cnt: string;
}

export function getStatisticVersion(params: any) {
  return request<ApiResult<IStatisticVersionResponse[]>>(
    '/new-media-api/vw/live/quotation/record/statistic/version',
    {
      method: 'POST',
      data: params,
    },
  );
}
export type StatisticContentParams = {
  projectId: number;
  roomTitle: string;
  teamCodeList: any[];
  fieldList: FieldList[];
  roomId: string;
  quotationType: number;
  liveStartDate: string;
  liveEndDate: string;
  carType: string;
  carVersion: string;
  contentType: string;
};

export interface FieldList {
  fieldId: number;
  value: string;
}

export function getStatisticContent(
  params: PageBasicParams,
  data: Partial<StatisticContentParams>,
) {
  return request<ApiResult<PagintaionData<StatisticContentItem>>>(
    '/new-media-api/vw/live/quotation/record/content/analytics',
    {
      method: 'POST',
      data,
      params,
    },
  );
}

export interface StatisticContentItem {
  roomId: string;
  contentType: string;
  content: string;
  contentSummary: string;
  teamName: string;
  teamCode: string;
  roomTitle: string;
  teamFieldList: TeamFieldList[];
  startTime: string;
  endTime: string;
  accountName: string;
}

export interface TeamFieldList {
  name: string;
  value: string;
  fieldId: string;
  showFlag: number;
  bizType: number;
  sort: number;
  depth: number;
}

export function getStatisticContentDetail(params: { roomId: string; platform: string }) {
  return request<ApiResult<Daum[]>>(
    '/new-media-api/vw/live/quotation/record/statistic/version/detail',
    {
      method: 'GET',
      params,
    },
  );
}

export interface Daum {
  id: string;
  roomId: string;
  num: any;
  carVersion: string;
  contentType: string;
  content: string;
  contentSummary: string;
  roomTitle: string;
  accountName: string;
  teamCode: string;
  startTime: any;
  createTime: string;
  updateTime: string;
}
