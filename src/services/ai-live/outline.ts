import { request } from '@umijs/max';

export interface PagintaionData<T> {
  total: number; // 总条数
  items: T[];
  page: number;
}

export function getOutlineList(params: any) {
  return request<PagintaionData<LiveOutline>>('/liveroom-api/outline/list', {
    method: 'GET',
    params,
  });
}

export function deleteOutline(params: { carId: number; critic: string }) {
  return request('/liveroom-api/outline/del', {
    method: 'POST',
    data: params,
  });
}

interface IPostCaption {
  carId: number;
  outlineId?: number;
  captionId?: number;
  text?: string;
  captionOrder?: number[];
}

export function saveCaption(params: IPostCaption) {
  return request('/liveroom-api/caption/save', {
    method: 'POST',
    data: params,
  });
}

export function saveOutline(data: ISaveOutline) {
  return request('/liveroom-api/outline/save', {
    method: 'POST',
    data,
  });
}

export function getCarList(params: any): Promise<CarItem[]> {
  return request('/liveroom-api/car/list', {
    method: 'GET',
    params,
  });
}
export function saveCar(data: { type: string; projectId: string }) {
  return request('/liveroom-api/car/save', {
    method: 'POST',
    data,
  });
}

export interface CarItem {
  carId: number;
  carName: string;
  critic: string[];
}
export interface ISaveOutline {
  outline: Outline[];
  carId: number;
  critic: string;
}

export interface Outline {
  caption: Caption[];
  outlineName: string;
}

export interface Caption {
  subtitle: string;
  text: string;
}
export interface LiveOutline {
  carType: string;
  carId: string;
  critics: Critic[];
}

export interface Critic {
  carId: number;
  create_time: string;
  critic: string;
  id: number;
  outline: Outline[];
  update_time: string;
}

export interface Outline {
  caption: Caption[];
  outlineId: number;
  outlineName: string;
  word_count: number;
}

export interface Caption {
  captionId?: number;
  subtitle: string;
  text: string;
}
