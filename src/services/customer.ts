import { request } from '@umijs/max';
import { ApiResult, CommonTag, PageBasicParams } from './common';
import { Tags } from './keyword';
import { TeamFieldList } from './setting';
import { SimpleTeamFieldItem } from './typings';

export interface CustomerSearchQO {
  projectId?: number;
  /** 1 评论 2 弹幕 3 私信 */
  interactiveType?: number;
  name?: string;
  /** 1 抖音 4 视频号 6 小红书 */
  platform?: number;
  type?: number;
  tagIds?: number[];
  firstCommentTimeBeg?: string;
  firstCommentTimeEnd?: string;
  latestCommentTimeBeg?: string;
  latestCommentTimeEnd?: string;
  team?: string;
  teamCode?: string;
  district?: string;
  province?: string;
  city?: string;
  id?: number;
  isAsc?: boolean;
  pos?: number;
}

export interface CustomerSearchQOParams {
  /** 每页条数 */
  size: number;
  /** 当前页数 */
  page: number;
  /** 排序字段 */
  orderBy?: string;
  /** 排序类型 */
  orderType?: string;
}
interface PaginationResultCustomer {
  total?: string;
  items?: Customer[];
}

export interface Customer {
  id: number;
  projectId?: number;
  interactiveType?: number;
  name?: string;
  avatar?: string;
  platform?: number;
  platformUniqueId?: string;
  type?: number;
  firstCommentTime?: string;
  latestCommentTime?: string;
  commentsNum?: number;
  interactiveNum?: number;
  keywordsNum?: number;
  keywordTags?: Tags[];
  accountUserId: number;
  accountUserName?: string;
  createTime?: string;
  updateTime?: string;
  tags: CommonTag[];
  teamFieldList?: TeamFieldList[];
  replyRate?: number;
  teamCodeList?: string[];
}

export async function getCustomerList(
  params: CustomerSearchQOParams,
  data: CustomerSearchQO,
  options?: { [key: string]: any },
) {
  return request<ApiResult<PaginationResultCustomer>>('/new-media-api/customer/search', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    params,
    data,
    ...(options || {}),
  });
}

interface LinkInfo {
  url?: string;
  cover?: string;
  title?: string;
}
interface AccountInfo {
  id?: string;
  name?: string;
  avatar?: string;
}

export interface ChatHistoryVO {
  id?: number;
  interactiveType?: number;
  platform?: number;
  sender?: string;
  senderInfo?: AccountInfo;
  receiver?: string;
  receiverInfo?: AccountInfo;
  content?: {
    conversation_short_id?: string;
    server_message_id?: string;
    conversation_type?: number;
    create_time?: string;
    message_type?: string;
    text?: string;
    user_infos?: any[];
    source?: string;
    resource_url?: string;
    item_id?: string;
  };
  sendAt?: string;
  linkInfo?: LinkInfo;
  tags?: Tags[];
}

export interface ChatSearchQO {
  /** 每页条数 */
  size: number;
  /** 当前页数 */
  page: number;
  /** 排序字段 */
  orderBy?: string;
  /** 排序类型 */
  orderType?: string;
  projectId?: number;
  interactiveType?: number;
  begDate?: string;
  endDate?: string;
  content?: string;
  sender?: string;
  accountUserId?: string;
  isAsc?: boolean;
  pos?: number;
}
interface PaginationResultChatHistoryVO {
  total?: string;
  items?: ChatHistoryVO[];
}
export async function getChatHistoryRequest(body: ChatSearchQO) {
  return request<ApiResult<PaginationResultChatHistoryVO>>('/new-media-api/chat/search', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
  });
}

export async function exportCustomerList(body: any) {
  return request('/new-media-api/customer/export', {
    method: 'POST',
    data: body,
  });
}

interface TrendDataDateDTO {
  /** 数量 */
  count?: number;
  /** 日期 */
  date?: string;
}

interface FollowerMapDTO {
  /** 省份名字 */
  province?: string;
  /** 数量 */
  count?: number;
}

export interface ProjectAccountUser {
  id?: string;
  projectId?: string;
  teamCode?: string;
  platform?: number;
  accountId?: string;
  nickname?: string;
  uid?: string;
  blueVipFlag?: number;
  avatar?: string;
  signature?: string;
  postCount?: number;
  followingCount?: number;
  followerCount?: number;
  totalFavorited?: number;
  followerGrowthTrendThirty?: TrendDataDateDTO[];
  favoritedGrowthTrendThirty?: TrendDataDateDTO[];
  followerMap?: FollowerMapDTO[];
  status?: number;
  deleteFlag?: number;
  reportTime?: string;
  createTime?: string;
  updateTime?: string;
}

export interface AgentSearchQO {
  fieldList?: SimpleTeamFieldItem[];
  projectId?: number;
  nickname?: string;
}

export interface AgentVO {
  accountUserId?: string;
  nickname?: string;
  // accountUsers?: ProjectAccountUser[];
  avatar?: string;
}

interface AgentResultVO {
  total?: string;
  items?: AgentVO[];
}
/**展示所有经销商 */
export async function getAgentList(
  params: PageBasicParams,
  data: AgentSearchQO,
  options?: { [key: string]: any },
) {
  return request<ApiResult<AgentResultVO>>('/new-media-api/customer/inbox', {
    method: 'POST',
    params,
    data,
    ...(options || {}),
  });
}

interface ConversationSearchQO {
  size: number;
  page: number;
  orderBy?: string;
  orderType?: string;
  accountUserId: string;
  platformUniqueId?: string;
  name?: string;
  pos?: number;
  isAsc?: boolean;
}
export interface PaginationResultConversationVO {
  total?: string;
  items?: ConversationVO[];
}
export interface ConversationVO {
  sender: string;
  name: string;
  avatar: string;
  lateMessage: string;
  latestSendAt: string;
}
/** 展示每个经销商下的对话框 */
export async function getConversationList(
  body: ConversationSearchQO,
  options?: { [key: string]: any },
) {
  return request<ApiResult<PaginationResultConversationVO>>('/new-media-api/chat/conversation', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
    ...(options || {}),
  });
}
