import { request } from '@umijs/max';
import { FieldList } from './business';
import { ApiResult, PageBasicParams, PagintaionData } from './common';

export type HumanCustomerDataParams = {
  /** 项目 id */
  projectId: number;
  /** 开始日期 */
  startDate: string;
  /** 结束日期 */
  endDate: string;
  /** 平台 */
  platform: number;
  /** 聚合维度主键 */
  fieldId?: number;
  /** 动态维度字段 */
  fieldList?: FieldList[];
  /** 标签id 集合 */
  tagIds?: number[];
  /** 团队编码集合 */
  teamCodeList?: string[];
};

export type HumanCustomerDataVO = {
  /** 聚合维度名词 */
  aggregationName?: string;
  /** 会话数 */
  conversationCount?: number;
  /** 消息数 */
  messageCount?: number;
  /** 咨询人数 */
  consultUvCount?: number;
  /** 留资数 */
  leadsCount?: number;
};

export type CustomerServiceAllDataParams = {
  /** 项目 id */
  projectId?: string;
  /** 开始日期 */
  startDate: string;
  /** 结束日期 */
  endDate: string;
  /** 平台 */
  platform: number;
  /** 动态维度字段 */
  fieldList?: FieldList[];
  /** 标签id 集合 */
  tagIds?: number[];
  /** 团队编码集合 */
  teamCodeList?: string[];
  /** 账号名称 */
  sourceAccountNickname?: string;
};

export type AICustomDataParams = {
  /** 项目 id */
  projectId?: string;
  /** 开始日期 */
  startDate: string;
  /** 结束日期 */
  endDate: string;
  /** 平台 */
  platform: number;
  /** 聚合维度主键 */
  fieldId?: number;
  /** 动态维度字段 */
  fieldList?: FieldList[];
  /** 标签id 集合 */
  tagIds?: number[];
  /** 团队编码集合 */
  teamCodeList?: string[];
  /** 账号名称 */
  sourceAccountNickname?: string;
};

export type AICustomResponseData = {
  /** 聚合维度名词 */
  aggregationName?: string;
  /** 会话数 */
  conversationCount?: number;
  /** 回复消息数 */
  replyCount?: number;
  /** 参与留资数 */
  leadsParticipateCount?: number;
  /** 参与留资率 */
  leadsParticipateRate?: string;
};

export type CustomerServiceAllDataVO = {
  /** 数据时间 */
  dateTime?: string;
  /** 会话总数 */
  conversationCount?: number;
  /** 咨询客户数 */
  consultCustomerCount?: number;
  /** 留资数 */
  leadsCount?: number;
};

/** 查询全部数据 POST /douyin/im/customer-service/data-statistics/all */
export async function CustomerServiceAllData(body: CustomerServiceAllDataParams) {
  return request<ApiResult<CustomerServiceAllDataVO[]>>(
    `/new-media-api/douyin/im/customer-service/data-statistics/all`,
    {
      method: 'POST',
      data: body,
    },
  );
}

/** 查询人工客服数据 POST /douyin/im/customer-service/data-statistics/manual */
export async function HumanCustomerData(body: HumanCustomerDataParams) {
  return request<ApiResult<HumanCustomerDataVO[]>>(
    `/new-media-api/douyin/im/customer-service/data-statistics/manual`,
    {
      method: 'POST',
      data: body,
    },
  );
}

/** 导出人工客服数据 POST /douyin/im/customer-service/data-statistics/manual/export */
export async function ExportHumanCustomData(body: HumanCustomerDataParams) {
  return request<ApiResult<string>>(
    `/new-media-api/douyin/im/customer-service/data-statistics/manual/export`,
    {
      method: 'POST',
      data: body,
    },
  );
}

/** 查询机器人数据 POST /douyin/im/customer-service/data-statistics/robot */
export async function AICustomData(body: AICustomDataParams) {
  return request<ApiResult<AICustomResponseData[]>>(
    `/new-media-api/douyin/im/customer-service/data-statistics/robot`,
    {
      method: 'POST',
      data: body,
    },
  );
}

/** 导出机器人数据 POST /douyin/im/customer-service/data-statistics/robot/export */
export async function ExportAICustomData(body: AICustomDataParams) {
  return request<ApiResult<string>>(
    `/new-media-api/douyin/im/customer-service/data-statistics/robot/export`,
    {
      method: 'POST',
      data: body,
    },
  );
}

export type ConversationDetailTableData = {
  /**
   * 会话ID
   */
  conversationId?: string;
  /**
   * 用户头像
   */
  customerAvatar?: string;
  /**
   * 用户昵称
   */
  customerNickname?: string;
  /**
   * 用户openId
   */
  customerOpenId?: string;
  /**
   * 是否留资 0：否 1：是
   */
  leadsFlag?: number;
  /**
   * 是否智能助手接待 0：否 1：是
   */
  robotFlag?: number;
  /**
   * 客服昵称
   */
  serviceNickname?: string;
  /**
   * 来源抖音昵称
   */
  sourceAccountDouyinNickname?: string;
  /**
   * 来源抖音号
   */
  sourceAccountDouyinShowAccountId?: string;
};

export type ConversationDetailBody = {
  /**
   * 用户昵称
   */
  customerNickname?: string;
  /**
   * 会话创建结束日期
   */
  endDate: string;
  /**
   * 动态维度字段
   */
  fieldList?: FieldList[];
  /**
   * 消息来源
   */
  imEnterUserType?: number;
  /**
   * 是否留资 0-否 1-是
   */
  leadsFlag?: number;
  /**
   * 项目 id
   */
  projectId?: string;
  /**
   * 客服系统 ID 集合
   */
  serviceSysUserIdList?: string[];
  /**
   * 来源账号內显 ID 集合
   */
  sourceAccountIdList?: string[];
  /**
   * 会话创建开始日期
   */
  startDate: string;
  /**
   * 标签id 集合
   */
  tagIds?: number[];
  /**
   * 团队编码集合
   */
  teamCodeList?: string[];
};

/** 查询会话详情 POST /douyin/im/customer-service/data-statistics/conversation/page */
export async function ConversationDetailData(
  params: PageBasicParams,
  data: ConversationDetailBody,
) {
  return request<ApiResult<PagintaionData<ConversationDetailTableData>>>(
    `/new-media-api/douyin/im/customer-service/data-statistics/conversation/page`,
    {
      method: 'POST',
      params,
      data,
    },
  );
}

export async function ExportConversationDetail(body: ConversationDetailBody) {
  return request<ApiResult<string>>(
    `/new-media-api/douyin/im/customer-service/data-statistics/conversation/export`,
    {
      method: 'POST',
      data: body,
    },
  );
}

export type GetConversationSourceAccountData = {
  /**
   * 附加属性
   */
  attributes?: DouyinImManualProjectAccountUserVO;
  /**
   * 是否禁用
   */
  disabled?: boolean;
  /**
   * 显示的数据
   */
  name?: string;
  /**
   * 是否被选中
   */
  selected?: boolean;
  /**
   * 分组标识
   */
  type?: string;
  /**
   * 选中获取的属性
   */
  value?: { [key: string]: any };
};

export type DouyinImManualProjectAccountUserVO = {
  /**
   * 內显 id
   */
  accountId?: string;
  /**
   * 头像
   */
  avatar?: string;
  /**
   * 蓝 V 标识 0: 否 1:是
   */
  blueVipFlag?: number;
  /**
   * 昵称
   */
  nickname?: string;
  /**
   * 平台
   */
  platform?: number;
  /**
   * 外显 id
   */
  showAccountId?: string;
  /**
   * 团队编码
   */
  teamCode?: string;
  /**
   * 抖音 uid
   */
  uid?: string;
};

export function GetConversationSourceAccount(params?: { projectId?: string }) {
  return request<ApiResult<GetConversationSourceAccountData[]>>(
    `/new-media-api/douyin/im/customer-service/data-statistics/conversation/source-account/select`,
    {
      method: 'GET',
      params,
    },
  );
}
