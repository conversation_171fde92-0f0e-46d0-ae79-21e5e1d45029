import { request } from '@umijs/max';
import { ApiResult, PageBasicParams, PagintaionData } from './common';
import { PlatForm } from '@/utils/platform';

export interface TeamField {
  fieldId: string;
  fieldName: string;
  fieldValue: string;
  fieldValueId: string;
  type?: number;
  showFlag?: number;
  bizType: number;
}

export interface TeamInfoItem {
  /**
   * Bilibili账号数量
   */
  bilibiliAccountCount?: number;
  /**
   * 懂车帝账号数量，懂车帝账号数量
   */
  dongchediAccountCount?: number;
  /**
   * 抖音账号数量，抖音账号数量
   */
  douyinAccountCount?: number;
  /**
   * 主键 id，主键 id
   */
  id?: any;
  /**
   * 快手账号数量，快手账号数量
   */
  kuaishowAccountCount?: number;
  /**
   * 汽车之家账号数量
   */
  qczzAccountCount?: number;
  /**
   * 关联号账号状态，关联号账号状态 1:正常 2:部分失效 3:全部失效 4: 无关联账号
   */
  status?: number;
  /**
   * 团队编码，团队编码
   */
  teamCode: string;
  /**
   * 团队维度信息集合，团队维度信息集合
   */
  teamFields: TeamField[];
  /**
   * tiktok账号数量
   */
  tiktokAccountCount?: number;
  /**
   * 更新时间，更新时间
   */
  updateTime?: string;
  /**
   * Weibo账号数量
   */
  weiboAccountCount?: number;
  /**
   * 微信公众号账号数量
   */
  wxPublicAccountCount?: number;
  /**
   * 视频号账号数量，视频号账号数量
   */
  wxVideoAccountCount?: number;
  /**
   * 小红书账号数量，小红书账号数量
   */
  xiaoHongShuAccountCount?: number;
  /**
   * YICHE账号数量
   */
  yicheAccountCount?: number;
}

export interface FiledTableItem {
  id: string;
  projectId?: string | undefined;
  fieldName: string | undefined;
  type: number;
  showFlag: number;
  sort: number;
  bizType?: number;
  depth?: number;
  biz?: Pick<FiledTableItem, 'depth' | 'bizType'>;
}

export interface ProjectIdQueryPage {
  page: number;
  size: number;
  projectId: string | undefined;
}

export type FieldValueItem = {
  name: string;
  value: string;
  selected: boolean;
  disabled: boolean;
  type: string;
  attributes: string;
};

export type ProjectTeamFieldValueDTOList = {
  id: number;
  teamId: number;
  teamCode: string;
  fieldId: number;
  fieldValue: string;
  bizType: number;
  depth: number;
  createTime: string;
  updateTime: string;
};

export type DepthList = {
  teamCode: string;
  projectTeamFieldValueDTOList: ProjectTeamFieldValueDTOList[];
};

export type User = {
  id: string;
  userName: string;
  defaultPassword: string;
  fieldId: string;
  dataScope: string;
  createTime: Date;
};

export type DepthListWithUser = DepthList & {
  portalUserExternalList: User[];
};

export type TreeDataWithUser = {
  maxDepth: number;
  depthList: DepthListWithUser[];
};

export type TreeData = {
  maxDepth: number;
  depthList: DepthList[];
};

export interface PortalUserDistributor {
  teamFieldList: ITeamFieldList[];
  id: string;
  teamCode: string;
  distributorAccountId: string;
  distributorDefaultPassword: string;
  sendStatus: number;
  existStatus?: number;
}

export interface ITeamFieldList {
  name: string;
  value: string;
  fieldId: string;
  showFlag: number;
  bizType: number;
  sort: number;
  depth: number;
}

export type TeamInfo = {
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 授权时间
   */
  grantDateTime?: string;
  /**
   * 托管方式授权状态
   */
  hostingGrantStatus?: number;
  /**
   * 托管方式授权时间
   */
  hostingGrantTime?: string;
  /**
   * 账号昵称
   */
  nickname?: string;
  /**
   * 平台
   */
  platform?: PlatForm;
  /**
   * 展示的账号 id
   */
  showAccountId?: string;
  /**
   * 授权状态
   */
  status?: number;
};

export function GetProjectTeamInfo(params: ProjectIdQueryPage) {
  return request<ApiResult<PagintaionData<TeamInfoItem>>>('/new-media-api/project_team/page_team', {
    method: 'GET',
    params,
  });
}

export function GetPortalUserDistributor(params: ProjectIdQueryPage) {
  return request<ApiResult<PortalUserDistributor>>('/new-media-api/portal-user/distributor/page', {
    method: 'GET',
    params,
  });
}

export function DistributorResetPassword(data: {
  userId: string;
  newPassword: string;
  confirmPassword: string;
}) {
  return request<ApiResult<PortalUserDistributor>>('/new-media-api/portal-user/reset-password', {
    method: 'POST',
    data,
  });
}

export function DistributorPasswordCreate(data: { projectId: string; teamCodeList: string[] }) {
  return request<ApiResult<PortalUserDistributor>>(
    '/new-media-api/portal-user/distributor/batch-create',
    {
      method: 'POST',
      data,
    },
  );
}
export function DistributorSendAccount(data: { projectId: string; teamCodeList: string[] }) {
  return request<ApiResult<[]>>('/new-media-api/portal-user/distributor/send-account', {
    method: 'POST',
    data,
  });
}

export function DistributorExport(data: {
  projectId: number;
  distributorAccountId?: string;
  teamCode?: string[];
  sendStatus?: number;
}) {
  return request('/new-media-api/portal-user/distributor/export', {
    method: 'POST',
    data,
  });
}

export function GetPageField(params: ProjectIdQueryPage) {
  return request<ApiResult<PagintaionData<FiledTableItem>>>(
    '/new-media-api/project_team/page_field',
    {
      method: 'GET',
      params,
    },
  );
}

export function UpdateField(params: FiledTableItem[]) {
  return request<ApiResult<any>>('/new-media-api/project_team/update_field', {
    method: 'POST',
    data: params,
  });
}

export function AddField(params: FiledTableItem) {
  return request<ApiResult<any>>('/new-media-api/project_team/add_field', {
    method: 'POST',
    data: params,
  });
}

export function DeleteField(params: (string | undefined)[]) {
  return request<ApiResult<any>>('/new-media-api/project_team/delete_field', {
    method: 'POST',
    data: params,
  });
}

export function DownLoadTemplate(params: { projectId: string | undefined }) {
  return request('/new-media-api/project_team/download_import_template', {
    method: 'GET',
    params,
    responseType: 'blob',
  });
}

export function ExportTeam(params: any) {
  return request('/new-media-api/project_team/export_team', {
    method: 'GET',
    params,
    responseType: 'blob',
  });
}

export function BatchDeleteTeam(params: string[] | React.Key[]) {
  return request<ApiResult<any>>('/new-media-api/project_team/batch_delete_team', {
    method: 'POST',
    data: params,
  });
}

export function TeamFieldList(params: any) {
  return request<ApiResult<TeamField[]>>('/new-media-api/project_team/list_team_field', {
    method: 'GET',
    params,
  });
}

export function UpdateTeamFieldValueV2(
  params: {
    projectId?: string;
    teamId: string;
  },
  data: {
    /**
     * 维度字段ID
     */
    fieldId: string;
    /**
     * 字段值
     */
    fieldValue?: string;
  }[],
) {
  return request<ApiResult<boolean>>('/new-media-api/project_team/update_team_field_value/v2', {
    method: 'POST',
    params,
    data,
  });
}

export function GetTeamInfo(params: { teamId: string }) {
  return request<ApiResult<TeamInfo[]>>('/new-media-api/project_team/get_team_info', {
    method: 'GET',
    params,
  });
}

export function GetFieldValue(params: { fieldId?: string | number; projectId?: string }) {
  return request<ApiResult<FieldValueItem[]>>('/new-media-api/project_team/list_field_value', {
    method: 'GET',
    params,
  });
}

export function GetTreeData(params: { projectId?: string }) {
  return request<ApiResult<TreeData>>('/new-media-api/project_team/get-team-depth', {
    method: 'GET',
    params,
  });
}

// 生产树的接口且包含了成员信息
export function GetExternalUserTreeData(params: { projectId?: string }) {
  return request<ApiResult<TreeDataWithUser>>(
    '/new-media-api/project_team/get-team-depth-external',
    {
      method: 'GET',
      params,
    },
  );
}

export type TeamDepthSelect = {
  /**
   * 附加属性
   */
  attributes?: { [key: string]: any };
  /**
   * 是否禁用
   */
  disabled?: boolean;
  /**
   * 显示的数据
   */
  name?: string;
  /**
   * 是否被选中
   */
  selected?: boolean;
  /**
   * 分组标识
   */
  type?: string;
  /**
   * 选中获取的属性
   */
  value?: string;
};

export function GetTeamDepthSelect(params: { projectId?: string }) {
  return request<ApiResult<TeamDepthSelect[]>>(
    '/new-media-api/project_team/get-team-depth-select',
    {
      method: 'GET',
      params,
    },
  );
}

export type ProjectTeamOverviewFieldVO = {
  /** 字段描述*/
  description?: string;
  /** 字段名称*/
  fieldName?: string;
  /** 主键*/
  id: string;
  /** 团队id*/
  projectId?: number;
  /** 是否展示 0-不展示 1-展示*/
  showFlag?: number;
  /** 排序*/
  sort?: number;
};

export function GetTeamOverviewData(params: { projectId?: string }) {
  return request<ApiResult<ProjectTeamOverviewFieldVO[]>>(
    '/new-media-api/project_team/overview/page-field',
    {
      method: 'GET',
      params,
    },
  );
}

export function UpdateTeamOverviewData(params: {
  projectId?: string;
  teamOverviewFieldInfoList: {
    showFlag?: number;
    id: string;
    sort?: number;
  }[];
}) {
  return request<ApiResult<boolean>>('/new-media-api/project_team/overview/update_field', {
    method: 'POST',
    data: params,
  });
}

export function GetTeamOverviewPage(
  params: PageBasicParams & {
    projectId?: string;
    startTime: string;
    endTime: string;
    platform?: PlatForm;
  },
) {
  return request<ApiResult<PagintaionData<ProjectTeamOverviewAggregationListVO>>>(
    '/new-media-api/project_team/overview/page',
    {
      method: 'GET',
      params,
    },
  );
}

/* ProjectTeamOverviewAggregationListV*/
export type ProjectTeamOverviewAggregationListVO = {
  /** 直播线索成本*/
  clueCost?: number;
  /** 线索总数*/
  clueCount?: number;
  /** 评论人数*/
  commentCount?: number;
  /** 完播率*/
  completionPlayRate?: number;
  /** 点赞人数*/
  diggCount?: number;
  /** 直播间曝光人数*/
  exposureUcount?: number;
  /** 粉丝净增长*/
  followerGrowth?: number;
  /** 互动数（点赞数+评论数+分享数）*/
  interactionCount?: number;
  /** 作品线索成本*/
  itemClueCost?: number;
  /** 作品线索数*/
  itemClueCount?: number;
  /** 作品广告消耗*/
  itemCost?: number;
  /** 投流作品数*/
  itemFlowCount?: number;
  /** 引导私信线索量*/
  itemGuideMsgClueCount?: number;
  /** 小风车和讲解卡点击人数*/
  liveClueBusinessCardClickCount?: number;
  /** 直播线索数*/
  liveClueCount?: number;
  /** 直播场次*/
  liveCount?: number;
  /** 投流直播场次*/
  liveFlowCount?: number;
  /** 直播广告消耗*/
  liveStatCost?: number;
  /** 直播进入人数*/
  liveWatchUv?: number;
  /** 3mins回复率*/
  messageReply3MinRatio?: number;
  /** 评论数*/
  postCommentCount?: number;
  /** 作品发布总数*/
  postCount?: number;
  /** 点赞数*/
  postDiggCount?: number;
  /** 作品互动数（点赞数+评论数+分享数）*/
  postInteractionCount?: number;
  /** 作品平均播放数（播放数/作品发布总数）*/
  postPlayAvg?: number;
  /** 作品新增播放数*/
  postPlayCount?: number;
  /** 分享数*/
  postShareCount?: number;
  /** 时间*/
  selectTime?: string;
  /** 服务分*/
  serviceScore?: number;
  /** 分享人数*/
  shareCount?: number;
  /** 单条线索成本*/
  singleClueCost?: number;
  /** 其他字段*/
  teamFieldList?: TeamFieldValueDTO[];
  /** 总消耗*/
  totalCost?: number;
};

/* TeamFieldValueDTO，动态维*/
export type TeamFieldValueDTO = {
  /** 业务类型 0: 普通 1: 名称  2: 层级*/
  bizType?: number;
  /** 层级深度*/
  depth?: number;
  /** 维度 id*/
  fieldId?: number;
  /** 名称*/
  name?: string;
  /** 是否展示 0: 不展示 1: 展示*/
  showFlag?: number;
  /** 排序字段*/
  sort?: number;
  /** 值*/
  value?: string;
};
