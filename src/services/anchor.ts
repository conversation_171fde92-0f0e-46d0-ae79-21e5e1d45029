import { PlatForm } from '@/utils/platform';
import { request } from '@umijs/max';
import { ApiResult, PageBasicParams, PagintaionData } from './common';

export type AnchorVO = {
  id?: number;
  name?: string;
  avatarUrl?: string;
  createTime?: string;
};

export type AnchorCreateUpdateDTO = {
  id?: number;
  name: string;
  avatarUrl: string;
};

export type AnchorDeleteDTO = {
  id: number;
};

export type AnchorBasicParams = {
  /** 项目 Id */
  projectId?: string;
  /** 平台 */
  platform: PlatForm;
};

export type AnchorPageParams = AnchorBasicParams & PageBasicParams;

// 新增主播
export async function CreateAnchor(params: AnchorBasicParams, body: AnchorCreateUpdateDTO) {
  return request<ApiResult<void>>('/new-media-api/anchor/create', {
    method: 'POST',
    params,
    data: body,
  });
}

// 删除主播
export async function DeleteAnchor(params: AnchorBasicParams, body: AnchorDeleteDTO) {
  return request<ApiResult<void>>('/new-media-api/anchor/delete', {
    method: 'POST',
    params,
    data: body,
  });
}

// 查询主播列表
export async function GetAnchorList(params: AnchorBasicParams) {
  return request<ApiResult<AnchorVO[]>>('/new-media-api/anchor/list', {
    method: 'GET',
    params,
  });
}

// 分页查询主播列表
export async function GetAnchorPage(params: AnchorPageParams) {
  return request<ApiResult<PagintaionData<AnchorVO>>>('/new-media-api/anchor/page', {
    method: 'GET',
    params,
  });
}

// 更新主播信息
export async function UpdateAnchor(params: AnchorBasicParams, body: AnchorCreateUpdateDTO) {
  return request<ApiResult<void>>('/new-media-api/anchor/update', {
    method: 'POST',
    params,
    data: body,
  });
}

// 导出主播列表
export async function ExportAnchorPage(params: AnchorBasicParams) {
  return request<ApiResult<string>>('/new-media-api/anchor/export', {
    method: 'GET',
    params,
  });
}

export type AnchorScheduleItem = {
  /** 排班日程 ID */
  scheduleId?: string;
  /** 排班日期 */
  scheduleDate?: string;
  /** 排班开始时间 */
  startTime?: string;
  /** 排班结束时间 */
  endTime?: string;
  /** 主播名称 */
  anchor?: AnchorVO;
  /** 上播状态 0: 未设置 1: 已上播 2: 未开播 3: 未上播 */
  liveStatus?: 0 | 1 | 2 | 3;
  /** 描述 */
  description?: string;
};

type GetAnchorScheduleParams = {
  /** 开始日期 */
  startDate: string;
  /** 结束日期 */
  endDate: string;
  /** 账号內显 ID */
  accountId?: string;
  /** 项目 Id */
  projectId?: string;
  /** 平台 */
  platform: number;
};

export type AnchorScheduleBody = {
  /** 主键 ID */
  id?: string;
  /** 账号內显 ID */
  accountId: string;
  /** 主播 ID */
  anchorId: number;
  /** 排班开始时间 */
  startTime: string;
  /** 排班结束时间 */
  endTime: string;
  /** 备注 */
  description?: string;
};

export type ProjectAccountDouyin = {
  /**
   * 內显 id
   */
  accountId?: string;
  /**
   * 头像
   */
  avatar?: string;
  /**
   * 蓝 V 标识 0: 否 1:是
   */
  blueVipFlag?: number;
  /**
   * 昵称
   */
  nickname?: string;
  /**
   * 平台
   */
  platform?: number;
  /**
   * 外显 id
   */
  showAccountId?: string;
  /**
   * 团队编码
   */
  teamCode?: string;
  /**
   * 抖音 uid
   */
  uid?: string;
};

/** 查询主播排班 GET /anchor/schedule */
export async function GetAnchorSchedule(params: GetAnchorScheduleParams) {
  return request<ApiResult<AnchorScheduleItem[]>>(`/new-media-api/anchor/schedule`, {
    method: 'GET',
    params,
  });
}

/** 创建主播排班 POST /anchor/schedule/create */
export async function CreateAnchorSchedule(params: AnchorBasicParams, body: AnchorScheduleBody) {
  return request<ApiResult<boolean>>(`/new-media-api/anchor/schedule/create`, {
    method: 'POST',
    params,
    data: body,
  });
}

/** 删除主播排班 POST /anchor/schedule/delete */
export async function DeleteAnchorSchedule(
  params: AnchorBasicParams,
  body: {
    /** 排班日程 ID */
    id: number;
  },
) {
  return request<ApiResult<boolean>>(`/new-media-api/anchor/schedule/delete`, {
    method: 'POST',
    params,
    data: body,
  });
}

/** 下载排班模板 GET /anchor/schedule/download-template */
export async function DownloadAnchorScheduleTemplate() {
  return request<any>(`/new-media-api/anchor/schedule/download-template`, {
    method: 'GET',
    responseType: 'blob',
  });
}

/** 导出排班 GET /anchor/schedule/export */
export async function ExportAnchorSchedule(params: {
  /** 开始日期 */
  startDate?: string;
  /** 结束日期 */
  endDate?: string;
  /** 项目 Id */
  projectId?: string;
  /** 平台 */
  platform: number;
}) {
  return request<ApiResult<string>>(`/new-media-api/anchor/schedule/export`, {
    method: 'GET',
    params,
  });
}

/** 更新主播排班 POST /anchor/schedule/update */
export async function UpdateAnchorSchedule(params: AnchorBasicParams, body: AnchorScheduleBody) {
  return request<boolean>(`/new-media-api/anchor/schedule/update`, {
    method: 'POST',
    params,
    data: body,
  });
}

/** 查询平台账号概览集合 GET /project_account_user/overview/list */
export async function GetProjectAccountUserOverviewList(params: {
  /**
   * 內显账号ID
   */
  accountId?: string;
  /**
   * 账号昵称
   */
  nickname?: string;
  /**
   * 平台
   */
  platform: number;
  /**
   * 项目 Id
   */
  projectId?: string;
  /**
   * 外显账号ID
   */
  showAccountId?: string;
}) {
  return request<ApiResult<ProjectAccountDouyin[]>>(
    `/new-media-api/project_account_user/overview/list`,
    {
      method: 'GET',
      params,
    },
  );
}

/** 批量创建主播排班 POST /anchor/schedule/create/batch */
export async function BatchCreateAnchorSchedule(
  params: AnchorBasicParams,
  body: AnchorScheduleBody[],
) {
  return request<ApiResult<string>>(`/new-media-api/anchor/schedule/create/batch`, {
    method: 'POST',
    params,
    data: body,
  });
}

/** 批量删除主播排班 POST /anchor/schedule/delete/batch */
export async function BatchDeleteAnchorSchedule(params: AnchorBasicParams, body: number[]) {
  return request<ApiResult<string>>(`/new-media-api/anchor/schedule/delete/batch`, {
    method: 'POST',
    params,
    data: body,
  });
}

/** 清除排班 POST /anchor/schedule/clear */
export async function AnchorScheduleClear(
  params: AnchorBasicParams,
  body: {
    /**
     * 账号內显 ID
     */
    accountId: string;
    /**
     * 结束日期
     */
    endDate: string;
    /**
     * 开始日期
     */
    startDate: string;
  },
) {
  return request<ApiResult<boolean>>(`/new-media-api/anchor/schedule/clear`, {
    method: 'POST',
    params,
    data: body,
  });
}

export type BatchTimeRangeType = {
  startTime: string;
  endTime: string;
};

/** 查询批量排班草稿 GET /anchor/schedule/draft */
export async function GetAnchorBatchSetDraft(
  params: AnchorBasicParams & {
    accountId: string;
  },
) {
  return request<ApiResult<BatchTimeRangeType[]>>(`/new-media-api/anchor/schedule/draft`, {
    method: 'GET',
    params,
  });
}

/** 更新或创建批量排班草稿 POST /anchor/schedule/draft/upsert */
export async function UpsertAnchorBatchSetDraft(
  params: AnchorBasicParams,
  body: {
    /**
     * 账号內显 ID
     */
    accountId: string;
    /**
     * 排班草稿时间列表
     */
    draftTimeList: {
      /**
       * 结束时间
       */
      endTime: string;
      /**
       * 开始时间
       */
      startTime: string;
    }[];
  },
) {
  return request<ApiResult<boolean>>(`/new-media-api/anchor/schedule/draft/upsert`, {
    method: 'POST',
    params,
    data: body,
  });
}

export type AnchorDetailReport = {
  /** 直播间 ID */
  roomId?: string;
  /** 直播间标题 */
  roomTitle?: string;
  /** 外显账号 ID */
  showAccountId?: string;
  /** 直播时长 */
  liveDuration?: number;
  /** 曝光人数 */
  exposureUcount?: number;
  /** 曝光次数 */
  exposureCount?: number;
  /** 观看人数 */
  viewCount?: number;
  /** 观看次数 */
  viewTime?: number;
  /** 小风车点击次数 */
  componentClickCount?: number;
  /** 点赞次数 */
  diggCount?: number;
  /** 评论次数 */
  commentCount?: number;
  /** 分享次数 */
  shareCount?: number;
  /** 关注人数 */
  followCount?: number;
  /** 留资线索数 */
  leadsCount?: number;
};

type AnchorLeadsReportVO = {
  /** 主播id */
  anchorId?: number;
  /** 主播昵称 */
  anchorName?: string;
  /** 线索总数 */
  leadsCount?: number;
};

type AnchorLiveDurationReportVO = {
  /** 主播id */
  anchorId?: number;
  /** 主播昵称 */
  anchorName?: string;
  /** 直播时长-单位：秒 */
  liveDuration?: number;
};

export type AnchorOverviewDetailData = {
  /** 主播 ID */
  anchorId?: string;
  /** 主播昵称 */
  anchorName?: string;
  /** 主播头像 */
  anchorAvatarUrl?: string;
  /** 累计直播场次 */
  liveCount?: number;
  /** 累计直播时长-单位：秒 */
  liveDuration?: number;
  /** 曝光人数 */
  exposureUcount?: number;
  /** 曝光次数 */
  exposureCount?: number;
  /** 观看人数 */
  viewCount?: number;
  /** 观看次数 */
  viewTime?: number;
  /** 小风车点击次数 */
  componentClickCount?: number;
  /** 点赞次数 */
  diggCount?: number;
  /** 评论次数 */
  commentCount?: number;
  /** 分享次数 */
  shareCount?: number;
  /** 关注人数 */
  followCount?: number;
  /** 留资线索数 */
  leadsCount?: number;
  /** 外显 ID 集合 */
  showAccountIds?: string[];
};

export type AnchorOverviewReport = {
  /**
   * 关联账号数
   */
  accountCount?: number;
  /**
   * 主播头像
   */
  anchorAvatarUrl?: string;
  /**
   * 主播 ID
   */
  anchorId?: number;
  /**
   * 主播昵称
   */
  anchorName?: string;
  /**
   * 评论次数
   */
  commentCount?: number;
  /**
   * 小风车点击次数
   */
  componentClickCount?: number;
  /**
   * 点赞次数
   */
  diggCount?: number;
  /**
   * 曝光次数
   */
  exposureCount?: number;
  /**
   * 曝光人数
   */
  exposureUcount?: number;
  /**
   * 关注人数
   */
  followCount?: number;
  /**
   * 留资线索数
   */
  leadsCount?: number;
  /**
   * 累计直播场次
   */
  liveCount?: number;
  /**
   * 累计直播时长-单位：秒
   */
  liveDuration?: number;
  /**
   * 0-未开播 1-直播中
   */
  liveStatus?: number;
  /**
   * 上周期评论次数
   */
  momCommentCount?: number;
  /**
   * 评论次数环比
   */
  momCommentCountRate?: string;
  /**
   * 上周期小风车点击次数
   */
  momComponentClickCount?: number;
  /**
   * 小风车点击次数环比
   */
  momComponentClickCountRate?: string;
  /**
   * 上周期点赞次数
   */
  momDiggCount?: number;
  /**
   * 点赞次数环比
   */
  momDiggCountRate?: string;
  /**
   * 上周期曝光次数
   */
  momExposureCount?: number;
  /**
   * 曝光次数环比
   */
  momExposureCountRate?: string;
  /**
   * 上周期曝光人数
   */
  momExposureUcount?: number;
  /**
   * 曝光人数环比
   */
  momExposureUcountRate?: string;
  /**
   * 上周期关注人数
   */
  momFollowCount?: number;
  /**
   * 关注人数环比
   */
  momFollowCountRate?: string;
  /**
   * 上周期留资线索数
   */
  momLeadsCount?: number;
  /**
   * 留资线索数环比
   */
  momLeadsCountRate?: string;
  /**
   * 上周期累计直播场次
   */
  momLiveCount?: number;
  /**
   * 累计直播场次环比
   */
  momLiveCountRate?: string;
  /**
   * 上周期累计直播时长-单位：秒
   */
  momLiveDuration?: number;
  /**
   * 累计直播时长环比
   */
  momLiveDurationRate?: string;
  /**
   * 上周期分享次数
   */
  momShareCount?: number;
  /**
   * 分享次数环比
   */
  momShareCountRate?: string;
  /**
   * 上周期观看人数
   */
  momViewCount?: number;
  /**
   * 观看人数环比
   */
  momViewCountRate?: string;
  /**
   * 上周期观看次数
   */
  momViewTime?: number;
  /**
   * 观看次数环比
   */
  momViewTimeRate?: string;
  /**
   * 分享次数
   */
  shareCount?: number;
  /**
   * 观看人数
   */
  viewCount?: number;
  /**
   * 观看次数
   */
  viewTime?: number;
};

export type AnchorReportCompare = {
  /** 主播 ID */
  anchorId?: number;
  /** 主播昵称 */
  anchorName?: string;
  /** 主播头像 */
  anchorAvatarUrl?: string;
  /** 开播时长-单位：秒 */
  liveDuration?: number;
  /** 留资线索数 */
  leadsCount?: number;
  /** 观看人数 */
  viewCount?: number;
  /** 新增粉丝数 */
  followCount?: number;
};

type AnchorDashboardBasicParams = {
  /** 开始日期 */
  startDate: string;
  /** 结束日期 */
  endDate: string;
  /** 项目 Id */
  projectId?: string;
  /** 平台 */
  platform: PlatForm;
};

type GetAnchorCompareParams = {
  /** 主播主键 id 集合 */
  anchorIds: string[];
} & AnchorDashboardBasicParams;

type GetAnchorDetailPageParams = {
  /** 主播Id */
  anchorId: number;
} & PageBasicParams &
  AnchorDashboardBasicParams;

type GetAnchorLeadsParams = {
  /** 主播Id */
  anchorIds?: string[];
  /** 內显账号 ID */
  accountIds?: string[];
} & AnchorDashboardBasicParams;

type GetAnchorLiveDurationParams = {
  /** 主播Id */
  anchorIds?: string[];
  /** 內显账号 ID */
  accountIds?: string[];
} & AnchorDashboardBasicParams;

type GetAnchorOverviewExportParams = {
  /** 主播Id */
  anchorIds?: string[];
  /** 內显账号 ID */
  accountIds?: string[];
} & AnchorDashboardBasicParams;

type GetAnchorOverviewDetailParams = {
  /** 主播Id */
  anchorId: number;
} & AnchorDashboardBasicParams;

type GetAnchorOverviewPageParams = {
  /** 主播Id */
  anchorIds?: string[];
  /** 內显账号 ID */
  accountIds?: string[];
  /**
   * 角色 ID
   */
  roleId: number;
} & PageBasicParams &
  AnchorDashboardBasicParams;

export const enum AnchorTrendType {
  LiveCount = 1,
  ViewCount = 2,
  ViewTime = 3,
  ExposureUcount = 4,
  ExposureCount = 5,
  ComponentClickCount = 6,
  DiggCount = 7,
  CommentCount = 8,
  ShareCount = 9,
  FollowCount = 10,
  LeadsCount = 11,
}

type GetAnchorTrendParams = {
  /** 主播 id */
  anchorId: number;
  /** 趋势类型
  1 :直播场次
  2 :观看人数
  3 :观看次数
  4 :曝光人数
  5 :曝光次数
  6 :小风车点击次数
  7 :点赞次数
  8 :评论次数
  9 :分享次数
  10 :关注人数
  11 :留资线索数 */
  type: AnchorTrendType;
} & AnchorDashboardBasicParams;

export type TrendDate = {
  /** 数量 */
  count?: string;
  /** 日期 */
  date?: string;
};

type AnchorAccountParams = {
  /** 主播Id */
  anchorId: number;
} & AnchorDashboardBasicParams;

/** 查询主播对比数据 GET /anchor/report/compare */
export async function GetAnchorCompare(params: GetAnchorCompareParams) {
  return request<ApiResult<AnchorReportCompare[]>>(`/new-media-api/anchor/report/compare`, {
    method: 'GET',
    params,
  });
}

/** 查询主播明细数据 GET /anchor/report/detail/page */
export async function GetAnchorDetailPage(params: GetAnchorDetailPageParams) {
  return request<ApiResult<PagintaionData<AnchorDetailReport>>>(
    `/new-media-api/anchor/report/detail/page`,
    {
      method: 'GET',
      params,
    },
  );
}

/** 导出主播明细数据 GET /anchor/report/detail/export */
export async function ExportAnchorDetail(params: GetAnchorDetailPageParams) {
  return request<ApiResult<string>>(`/new-media-api/anchor/report/detail/export`, {
    method: 'GET',
    params,
  });
}

/** 查询线索贡献报表 GET /anchor/report/leads */
export async function GetAnchorLeads(params: GetAnchorLeadsParams) {
  return request<ApiResult<AnchorLeadsReportVO[]>>(`/new-media-api/anchor/report/leads`, {
    method: 'GET',
    params,
  });
}

/** 查询主播直播时长报表 GET /anchor/report/live-duration */
export async function GetAnchorLiveDuration(params: GetAnchorLiveDurationParams) {
  return request<ApiResult<AnchorLiveDurationReportVO[]>>(
    `/new-media-api/anchor/report/live-duration`,
    {
      method: 'GET',
      params,
    },
  );
}

/** 查询主播概览数据 GET /anchor/report/overview/detail */
export async function GetAnchorOverviewDetail(params: GetAnchorOverviewDetailParams) {
  return request<ApiResult<AnchorOverviewDetailData>>(
    `/new-media-api/anchor/report/overview/detail`,
    {
      method: 'GET',
      params,
    },
  );
}

/** 导出主播详细数据 GET /anchor/report/overview/export */
export async function GetAnchorOverviewExport(params: GetAnchorOverviewExportParams) {
  return request<ApiResult<string>>(`/new-media-api/anchor/report/overview/export`, {
    method: 'GET',
    params,
  });
}

/** 分页查询主播详细数据 GET /anchor/report/overview/page */
export async function GetAnchorOverviewPage(params: GetAnchorOverviewPageParams) {
  return request<ApiResult<PagintaionData<AnchorOverviewReport>>>(
    `/new-media-api/anchor/report/overview/page`,
    {
      method: 'GET',
      params,
    },
  );
}

/** 查询主播趋势数据 GET /anchor/report/trend */
export async function GetAnchorTrend(params: GetAnchorTrendParams) {
  return request<ApiResult<TrendDate[]>>(`/new-media-api/anchor/report/trend`, {
    method: 'GET',
    params,
  });
}

export type AnchorAccountReportVO = {
  /**
   * 內显账号 ID
   */
  accountId?: string;
  /**
   * 评论次数
   */
  commentCount?: number;
  /**
   * 小风车点击次数
   */
  componentClickCount?: number;
  /**
   * 点赞次数
   */
  diggCount?: number;
  /**
   * 曝光次数
   */
  exposureCount?: number;
  /**
   * 曝光人数
   */
  exposureUcount?: number;
  /**
   * 关注人数
   */
  followCount?: number;
  /**
   * 留资线索数
   */
  leadsCount?: number;
  /**
   * 直播时长-单位秒
   */
  liveDuration?: number;
  /**
   * 分享次数
   */
  shareCount?: number;
  /**
   * 外显账号 ID
   */
  showAccountId?: string;
  /**
   * 观看人数
   */
  viewCount?: number;
  /**
   * 观看次数
   */
  viewTime?: number;
};

/** 查询主播关联的账号数据 GET /anchor/report/account */
export async function GetAnchorAccount(params: AnchorAccountParams) {
  return request<ApiResult<AnchorAccountReportVO[]>>(`/new-media-api/anchor/report/account`, {
    method: 'GET',
    params,
  });
}
