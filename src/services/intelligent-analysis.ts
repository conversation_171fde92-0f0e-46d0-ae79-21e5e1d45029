import { PagintaionData, ApiResult } from './common';
import { request } from '@umijs/max';

export type BarrageEmotion = '积极弹幕' | '中性弹幕' | '消极弹幕';

export interface BarragDistribution {
  total: number;
  rate: number;
  emotion: BarrageEmotion;
}

export interface CarDistribution {
  carInfo: string;
  rate: number;
  total: number;
}

export interface BarrageTypeDistribution {
  barrageType: string;
  rate: number;
  total: number;
}

export interface BarrageAnalysisOverviewResult {
  barragDistribution: BarragDistribution[];
  carDistribution: CarDistribution[];
  barrageTypeDistribution: BarrageTypeDistribution[];
  barrageTotal: number | null;
  /** 环比增长率 */
  growthRate?: number;
}

export interface BarrageItem {
  barrage: string;
  count: number;
}

// 弹幕总览数据
export function GetBarrageAnalysisOverview(params: {
  projectId: string;
  startTime?: string;
  endTime?: string;
  teamCodeList?: string[];
  liveRoomId?: string;
}) {
  return request<ApiResult<BarrageAnalysisOverviewResult>>(
    '/new-media-api/barrage-analysis/overview',
    {
      method: 'POST',
      data: params,
    },
  );
}

interface BarrageAnalysisParams {
  projectId: string;
  startTime?: string;
  endTime?: string;
  teamCodeList?: string[];
  emotion?: BarrageEmotion;
  carInfo?: string;
  barrageType?: string;
  liveRoomId?: string;
  /** 弹幕内容模糊搜索关键字 */
  barrage?: string;
  isBarrageDetail?: boolean;
}
// 弹幕列表数据
export function GetBarragesPage(
  params: {
    size: number;
    page: number;
  } & BarrageAnalysisParams,
) {
  return request<ApiResult<PagintaionData<BarrageItem>>>('/new-media-api/barrage-analysis/page', {
    method: 'POST',
    params: {
      page: params.page,
      size: params.size,
    },
    data: {
      projectId: params.projectId,
      startTime: params.startTime,
      endTime: params.endTime,
      teamCodeList: params.teamCodeList,
      emotion: params.emotion,
      carInfo: params.carInfo,
      barrageType: params.barrageType,
      liveRoomId: params.liveRoomId,
      barrage: params.barrage,
    },
  });
}

// 弹幕车型数据
export function GetBarrageAnalysisCarInfo(params: {
  projectId: string;
  liveRoomId?: string;
  startTime?: string;
  endTime?: string;
  teamCodeList?: string[];
}) {
  return request<ApiResult<string[]>>('/new-media-api/barrage-analysis/car-info', {
    method: 'POST',
    data: params,
  });
}

// 弹幕类型数据
export function GetBarrageAnalysisType(params: {
  projectId: string;
  liveRoomId?: string;
  startTime?: string;
  endTime?: string;
  teamCodeList?: string[];
}) {
  return request<ApiResult<string[]>>('/new-media-api/barrage-analysis/type', {
    method: 'POST',
    data: params,
  });
}

export const exportBarrage = (params: BarrageAnalysisParams) => {
  return request<ApiResult<string>>('/new-media-api/barrage-analysis/export', {
    method: 'POST',
    data: params,
  });
};
