import { request } from '@umijs/max';
import { ApiResult } from './common';
import { PlatForm } from '@/utils/platform';
import { SpiderSyncUser } from './account';

export function QuerySpiderSyncUserForPagination(params: any) {
  return request<ApiResult<any>>(`/new-media-api/spider/query_sync_users`, {
    method: 'POST',
    data: params,
  });
}

export function EditSpiderSyncUser(id: number, params: any) {
  return request<ApiResult<void>>(`/new-media-api/spider/edit_sync_users/${id}`, {
    method: 'POST',
    data: params,
  });
}

export type BatchEditSpiderSyncUserParams = {
  ids: number[];
  postSaveStatus?: number;
  postQualityStatus?: number;
  liveReplayStatus?: number;
  liveQualityStatus?: number;
  qualityRuleProjectId?: number;
  syncFrequency?: number;
  nextSyncTime?: string;
  type?: number;
};

export function BatchEditSpiderSyncUser(params: BatchEditSpiderSyncUserParams) {
  return request<ApiResult<void>>(`/new-media-api/spider/batch_edit_sync_users`, {
    method: 'POST',
    data: params,
  });
}

export function CheckCreateSpiderSyncUser(params: { accountIds: string[]; platform: number }) {
  return request<ApiResult<SpiderSyncUser[]>>(`/new-media-api/spider/check_create_sync_users`, {
    method: 'POST',
    data: params,
  });
}

export type SpiderUserInfo = {
  /**
   * 账号 id
   */
  accountId?: string;
  /**
   * 是否存在
   */
  isExist?: boolean;
  /**
   * 个人主页链接
   */
  link: string;
  /**
   * 备注
   */
  remark: string;
};

export function CheckCreateSpiderSyncUserV2(params: {
  items: { link: string; remark: string }[];
  platform: number;
}) {
  return request<ApiResult<SpiderUserInfo[]>>(`/new-media-api/spider/check_create_sync_users/v2`, {
    method: 'POST',
    data: params,
  });
}

// 视频号使用 v1 接口 其他平台都使用 v2 的
export function BatchCreateSpiderSyncUser(data: {
  items: { accountId: string; remark: string }[];
  platform: PlatForm;
  syncFrequency: number;
  liveReplayStatus: number;
}) {
  return request<ApiResult<void>>(`/new-media-api/spider/batch_create_sync_users`, {
    method: 'POST',
    data,
  });
}

export function BatchCreateSpiderSyncUserV2(data: {
  items: { accountId: string; link: string; remark: string }[];
  platform: PlatForm;
  syncFrequency: number;
  liveReplayStatus: number;
}) {
  return request<ApiResult<void>>(`/new-media-api/spider/batch_create_sync_users/v2`, {
    method: 'POST',
    data,
  });
}

export function GetSpiderMonitorTemplate() {
  return request<any>(`/new-media-api/spider/monitor/download-template`, {
    method: 'GET',
    responseType: 'blob',
  });
}
