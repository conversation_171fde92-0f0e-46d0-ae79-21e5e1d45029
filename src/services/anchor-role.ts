import { request } from '@umijs/max';
import { ApiResult } from './common';
import { PlatForm } from '@/utils/platform';

export type AnchorRoleListParams = {
  /**
   * 平台
   */
  platform?: PlatForm;
  /**
   * 项目 Id
   */
  projectId?: string;
  /**
   * 角色代码（精确查询）
   */
  roleCode?: string;
  /**
   * 角色名称（模糊查询）
   */
  roleName?: string;
  /**
   * 状态（1：启用，0：禁用）
   */
  status?: number;
};

export type AnchorRole = {
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 角色ID
   */
  id?: number;
  /**
   * 项目ID
   */
  projectId?: number;
  /**
   * 角色代码（如：ANCHOR、ASSISTANT、OPTIMIZER等）
   */
  roleCode?: string;
  /**
   * 角色描述
   */
  roleDescription?: string;
  /**
   * 角色名称（如：主播、助播、优化师等）
   */
  roleName?: string;
  /**
   * 排序顺序
   */
  sortOrder?: number;
  /**
   * 状态（1：启用，0：禁用）
   */
  status?: boolean;
  /**
   * 更新时间
   */
  updateTime?: string;
};

export async function GetAnchorRoleList(params: AnchorRoleListParams) {
  return request<ApiResult<AnchorRole[]>>(`/new-media-api/anchor/role/list`, {
    method: 'GET',
    params,
  });
}
