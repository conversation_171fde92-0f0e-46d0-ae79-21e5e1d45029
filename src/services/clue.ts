import { request } from '@umijs/max';
import { ApiResult, PageBasicParams, PagintaionData } from './common';
import { PlatForm } from '@/utils/platform';

export enum ChannelSourceEnum {
  '字节-橙子建站' = 0,
  '其他渠道-外部导入' = 1,
  '抖音企业号' = 2,
  '巨量线索' = 5,
  '字节-云店' = 7,
  '字节-星图' = 8,
  '字节-获客宝' = 9,
  '字节-住小帮' = 10,
}

export enum ActionTypeEnum {
  '其他' = 0,
  '短视频' = 1,
  '直播' = 2,
  '企业主页' = 3,
  '私信' = 4,
  '订阅文章' = 5,
  '群聊' = 6,
  '独立店铺' = 7,
  '搜索服务卡' = 8,
}

export enum ClueTypeEnum {
  '表单提交' = 0,
  '在线咨询' = 1,
  '智能电话' = 2,
  '网页回呼' = 3,
  '卡券' = 4,
  '抽奖' = 5,
}

export interface ITopSource {
  referDyName: string;
  referDyId: string;
  userCount: number;
}
export type IQueryLeadsParams = {
  /**
   * 互动类型
   * 0 :其他
   * 1 :短视频
   * 2 :直播
   * 3 :企业主页
   * 4 :私信
   * 5 :订阅文章
   * 6 :群聊
   * 7 :独立店铺
   * 8 :搜索服务卡
   */
  actionType?: ActionTypeEnum[];
  /**
   * 线索渠道
   * 0 :字节-橙子建站
   * 1 :其他渠道-外部导入
   * 2 :抖音企业号
   * 5 :巨量线索
   * 7 :字节-云店
   * 8 :字节-星图
   * 9 :字节-获客宝
   * 10 :字节-住小帮
   */
  clueSource?: string[];
  /**
   * 线索类型
   * 0 :表单提交
   * 1 :在线咨询
   * 2 :智能电话
   * 3 :网页回呼
   * 4 :卡券
   * 5 :抽奖
   */
  clueType?: ClueTypeEnum[];
  /**
   * 留资结束日期
   */
  endDate: string;
  /**
   * 用户昵称
   */
  name?: string;
  /**
   * 留资平台
   */
  platform?: PlatForm;
  /**
   * 项目 id
   */
  projectId?: string;
  /**
   * 来源账号 id
   */
  referDyId?: string;
  /**
   * 来源账号名称
   */
  referDyName?: string;
  /**
   * 线索来源车企
   */
  source?: string;
  /**
   * 线索来源经销商
   */
  sourceCommerce?: string;
  /**
   * 留资开始日期
   */
  startDate: string;
  /**
   * 留资手机
   */
  telephone?: string;
} & PageBasicParams;

export type ClueInfo = {
  /**
   * 互动类型
   */
  actionType?: ActionTypeEnum;
  /**
   * 年龄
   */
  age?: number;
  /**
   * 城市
   */
  cityName?: string;
  /**
   * 线索 id
   */
  clueId?: string;
  /**
   * 线索渠道
   */
  clueSource?: ChannelSourceEnum;
  /**
   * 线索类型
   */
  clueType?: ClueTypeEnum;
  /**
   * 留资时间
   */
  createTime?: string;
  /**
   * 姓名
   */
  name?: string;
  /**
   * 留资平台
   */
  platform?: number;
  /**
   * 省份
   */
  provinceName?: string;
  /**
   * 来源经销商昵称
   */
  sourceCommerce?: string;
  /**
   * 电话
   */
  telephone?: string;
  /**
   * 留资微信号
   * 微信
   */
  weixin?: string;
};

export interface IDynamicsData {
  actionType: ActionTypeEnum;
  clueCreateTime: string;
  platform: string;
  referDyId: string;
  referDyName: string;
  telephone: string;
  clueType: number;
}

export async function QueryLeadsPage(params: IQueryLeadsParams) {
  return request<ApiResult<PagintaionData<ClueInfo>>>('/new-media-api/leads/douyin/query_leads', {
    method: 'GET',
    params,
  });
}

export type LeadsDouyinDetail = {
  /**
   * 来源抖音号
   */
  referDyId?: string;
  /**
   * 来源抖音昵称
   */
  referDyName?: string;
  /**
   * 互动类型
   */
  actionType?: ActionTypeEnum;
  /**
   * 年龄
   */
  age?: number;
  /**
   * 城市
   */
  cityName?: string;
  /**
   * 线索 id
   */
  clueId?: string;
  /**
   * 线索渠道
   */
  clueSource?: number;
  /**
   * 线索类型
   */
  clueType?: ClueTypeEnum;
  /**
   * 首次留资时间
   */
  firstLeadsTime?: string;
  /**
   * 姓名
   */
  name?: string;
  /**
   * 最新留资时间
   */
  newestLeadsTime?: string;
  /**
   * 留资平台
   */
  platform?: number;
  /**
   * 省份
   */
  provinceName?: string;
  /**
   * 来源经销商昵称
   */
  sourceCommerce?: string;
  /**
   * 电话
   */
  telephone?: string;
  /**
   * 留资微信号
   * 微信
   */
  weixin?: string;
};

export async function QueryLeadsDetail(params: { projectId?: string; clueId?: string }) {
  return request<ApiResult<LeadsDouyinDetail>>('/new-media-api/leads/douyin/query_detail', {
    method: 'GET',
    params,
  });
}

export type TopSource = {
  /**
   * 结束时间
   */
  endDate: string;
  /**
   * 项目 id
   */
  projectId?: string;
  /**
   * 开始时间
   */
  startDate: string;
};

export async function QueryTopSource(params: TopSource) {
  return request<ApiResult<ITopSource[]>>('/new-media-api/leads/douyin/top_sources', {
    method: 'GET',
    params,
  });
}
export async function QueryCustomerDynamics(params: { projectId: string; clueId: string }) {
  return request<ApiResult<IDynamicsData[]>>('/new-media-api/leads/douyin/customer_dynamic', {
    method: 'GET',
    params,
  });
}

export function ExportLeads(params: any) {
  return request('/new-media-api/leads/douyin/export_leads', {
    method: 'POST',
    params,
  });
}
