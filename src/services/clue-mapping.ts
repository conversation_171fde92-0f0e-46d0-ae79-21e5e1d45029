import { request } from '@umijs/max';
import { ApiResult, PageBasicParams, PagintaionData } from './common';

export type WeiLaiClueParams = {
  /**
   * 广告主 id
   */
  advertiserId?: string;
  /**
   * 广告主类型 1:短视频 2:直播
   */
  advertiserType?: number;
  /**
   * 创建结束时间
   */
  createTimeEnd?: string;
  /**
   * 创建开始时间
   */
  createTimeStart?: string;
  /**
   * 抖音号
   */
  referDyId?: string;
  /**
   * 线索编码
   */
  srcInterface?: string;
  /**
   * 更新结束时间
   */
  updateTimeEnd?: string;
  /**
   * 更新开始时间
   */
  updateTimeStart?: string;
} & PageBasicParams;

export type WeiLaiClueData = {
  /**
   * 客户来源 1:抖音本地通 2:抖音 3:奥迪官号 4:南区矩阵 5:西区矩阵
   */
  sourceType?: number;
  /**
   * 广告主id
   */
  advertiserId?: string;
  /**
   * 广告主类型 1:短视频 2:直播
   */
  advertiserType?: number;
  /**
   * 创建人
   */
  createBy?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 员工域账号
   */
  empDomain?: string;
  /**
   * 主键
   */
  id?: number;
  /**
   * 推荐员工域账号
   */
  koeEmpDomain?: string;
  /**
   * 抖音号
   */
  referDyId?: string;
  /**
   * 来源经销商编码
   */
  sourceCommerceKey?: string;
  /**
   * 线索编码
   */
  srcInterface?: string;
  /**
   * 更新人
   */
  updateBy?: string;
  /**
   * 更新时间
   */
  updateTime?: string;
};

export function WeiLaiClueQuery(params: WeiLaiClueParams) {
  return request<ApiResult<PagintaionData<WeiLaiClueData>>>(
    '/new-media-api/leads/weilai/queryPage',
    {
      method: 'GET',
      params,
    },
  );
}

export function WeiLaiDownloadTemplate() {
  return request<any>('/new-media-api/leads/weilai/download-template', {
    method: 'GET',
    responseType: 'blob',
  });
}

export function WeiLaiClueExport(params: {
  /**
   * 抖音号
   */
  referDyId?: string;
}) {
  return request<ApiResult<string>>('/new-media-api/leads/weilai/export', {
    method: 'GET',
    params,
  });
}

export type WeiLaiFormValues = {
  /**
   * 客户来源 1:抖音本地通 2:抖音 3:奥迪官号 4:南区矩阵 5:西区矩阵
   */
  sourceType: number;
  /**
   * 广告主id
   */
  advertiserId: string;
  /**
   * 广告主类型 1:短视频 2:直播
   */
  advertiserType: number;
  /**
   * 员工域账号
   */
  empDomain?: string;
  /**
   * 推荐员工域账号
   */
  koeEmpDomain?: string;
  /**
   * 抖音号
   */
  referDyId: string;
  /**
   * 来源经销商编码
   */
  sourceCommerceKey?: string;
  /**
   * 线索编码
   */
  srcInterface: string;
  /**
   * 账号类型 1-不限类型 2-企业号 3-员工号 4-普通号
   */
  accountType: number;
};

export function WeiLaiClueCreate(data: WeiLaiFormValues) {
  return request<ApiResult<boolean>>('/new-media-api/leads/weilai/create', {
    method: 'POST',
    data,
  });
}

export function WeiLaiClueUpdate(data: { id: number } & WeiLaiFormValues) {
  return request<ApiResult<boolean>>('/new-media-api/leads/weilai/update', {
    method: 'POST',
    data,
  });
}

export function WeiLaiClueDelete(data: number[]) {
  return request<ApiResult<boolean>>('/new-media-api/leads/weilai/delete', {
    method: 'POST',
    data,
  });
}

export function WeiLaiClueImport(params: { sourceType: number }, data: FormData) {
  return request<ApiResult<boolean>>('/new-media-api/leads/weilai/import', {
    method: 'POST',
    data,
    params,
  });
}

export type XiaoPengClueParams = {
  /**
   * 创建结束时间
   */
  createTimeEnd?: string;
  /**
   * 创建开始时间
   */
  createTimeStart?: string;
  /**
   * 抖音号
   */
  referDyId?: string;
  /**
   * 经销商编码
   */
  sourceCommerceKey?: string;
  /**
   * 线索编码
   */
  srcInterface?: string;
  /**
   * 更新结束时间
   */
  updateTimeEnd?: string;
  /**
   * 更新开始时间
   */
  updateTimeStart?: string;
} & PageBasicParams;

export type XiaoPengClueData = {
  /**
   * 创建人
   */
  createBy?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 主键
   */
  id?: number;
  /**
   * 抖音号
   */
  referDyId?: string;
  /**
   * 抖音昵称
   */
  referDyName?: string;
  /**
   * 经销商昵称
   */
  sourceCommerce?: string;
  /**
   * 经销商编码
   */
  sourceCommerceKey?: string;
  /**
   * 线索编码
   */
  srcInterface?: string;
  /**
   * 线索昵称
   */
  srcInterfaceName?: string;
  /**
   * 修改人
   */
  updateBy?: string;
  /**
   * 更新时间
   */
  updateTime?: string;
};

export function XiaoPengClueQuery(params: XiaoPengClueParams) {
  return request<ApiResult<PagintaionData<XiaoPengClueData>>>(
    '/new-media-api/leads/xiaopeng/queryPage',
    {
      method: 'GET',
      params,
    },
  );
}

export function XiaoPengClueDelete(data: number[]) {
  return request<ApiResult<boolean>>('/new-media-api/leads/xiaopeng/delete', {
    method: 'POST',
    data,
  });
}

export function XiaoPengDownloadTemplate() {
  return request<any>('/new-media-api/leads/xiaopeng/download-template', {
    method: 'GET',
    responseType: 'blob',
  });
}

export function XiaoPengClueExport(params: {
  /**
   * 抖音号
   */
  referDyId?: string;
}) {
  return request<any>('/new-media-api/leads/xiaopeng/export', {
    method: 'GET',
    params,
  });
}

export type XiaoPengFormValues = {
  /**
   * 抖音号
   */
  referDyId: string;
  /**
   * 抖音昵称
   */
  referDyName?: string;
  /**
   * 经销商昵称
   */
  sourceCommerce?: string;
  /**
   * 经销商编码
   */
  sourceCommerceKey?: string;
  /**
   * 线索编码
   */
  srcInterface: string;
  /**
   * 线索昵称
   */
  srcInterfaceName?: string;
};

export function XiaoPengClueCreate(data: XiaoPengFormValues) {
  return request<ApiResult<boolean>>('/new-media-api/leads/xiaopeng/create', {
    method: 'POST',
    data,
  });
}

export function XiaoPengClueUpdate(data: { id: number } & XiaoPengFormValues) {
  return request<ApiResult<boolean>>('/new-media-api/leads/xiaopeng/update', {
    method: 'POST',
    data,
  });
}

export function XiaoPengClueImport(params: any, data: FormData) {
  return request<ApiResult<boolean>>('/new-media-api/leads/xiaopeng/import', {
    method: 'POST',
    data,
    params,
  });
}

type AODIExportParams = {
  /** 抖音号 */
  referDyId?: string;
  /** 创建开始时间 */
  createTimeStart?: string;
  /** 创建结束时间 */
  createTimeEnd?: string;
  /** 更新开始时间 */
  updateTimeStart?: string;
  /** 更新结束时间 */
  updateTimeEnd?: string;
};

type AODIClueParams = {
  /** 每页条数 */
  size: number;
  /** 当前页数 */
  page: number;
  /** 排序字段 */
  orderBy?: string;
  /** 排序类型 */
  orderType?: string;
  /** 抖音号 */
  referDyId?: string;
  /** 创建开始时间 */
  createTimeStart?: string;
  /** 创建结束时间 */
  createTimeEnd?: string;
  /** 更新开始时间 */
  updateTimeStart?: string;
  /** 更新结束时间 */
  updateTimeEnd?: string;
} & PageBasicParams;

export type AODIClueData = {
  /** 主键 id */
  id?: number;
  /** 抖音号 */
  referDyId?: string;
  /** 抖音昵称 */
  referDyName?: string;
  /** 创建人 */
  createBy?: string;
  /** 更新人 */
  updateBy?: string;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 客户来源 1:抖音本地通 2:抖音 3:奥迪官号 4:南区矩阵 5:西区矩阵 */
  sourceType?: number;
};

/** 删除过滤映射 删除过滤映射
删除过滤映射 POST /leads/aodi/filter-store/delete */
export async function AODIClueDelete(body: number[]) {
  return request<ApiResult<boolean>>(`/new-media-api/leads/aodi/filter-store/delete`, {
    method: 'POST',
    data: body,
  });
}

/** 下载导入模板 下载导入模板
下载导入模板 GET /leads/aodi/filter-store/download-template */
export async function AODIDownloadTemplate() {
  return request<any>(`/new-media-api/leads/aodi/filter-store/download-template`, {
    method: 'GET',
    responseType: 'blob',
  });
}

/** 导出数据 导出数据
导出数据 GET /leads/aodi/filter-store/export */
export async function AODIClueExport(params: AODIExportParams) {
  return request<ApiResult<string>>(`/new-media-api/leads/aodi/filter-store/export`, {
    method: 'GET',
    params: {
      ...params,
    },
  });
}

/** 导入数据 导入数据
导入数据 POST /leads/aodi/filter-store/import */
export async function AODIClueImport(params: any, data: FormData) {
  return request<ApiResult<boolean>>(`/new-media-api/leads/aodi/filter-store/import`, {
    method: 'POST',
    data,
    params,
  });
}

/** 分页查询 分页查询
分页查询 GET /leads/aodi/filter-store/page */
export async function AODIClueQuery(params: AODIClueParams) {
  return request<ApiResult<PagintaionData<AODIClueData>>>(
    `/new-media-api/leads/aodi/filter-store/queryPage`,
    {
      method: 'GET',
      params: {
        ...params,
      },
    },
  );
}

export type AODIFormValues = {
  /**
   * 抖音号集合
   * 抖音号
   */
  referDyId: string;
  /**
   * 抖音昵称
   */
  referDyName?: string;
  /**
   * 客户来源 1:抖音本地通 2:抖音 3:奥迪官号 4:南区矩阵 5:西区矩阵
   */
  sourceType?: number;
};

export async function AODIClueCreate(data: AODIFormValues) {
  return request<ApiResult<boolean>>('/new-media-api/leads/aodi/filter-store/create', {
    method: 'POST',
    data,
  });
}

/** 更新过滤映射 更新过滤映射
更新过滤映射 POST /leads/aodi/filter-store/update */
export async function AODIClueUpdate(body: {
  /** 主键 id */
  id: number;
  /** 抖音昵称 */
  referDyName?: string;
  /** 抖音号 */
  referDyId: string;
}) {
  return request<ApiResult<boolean>>(`/new-media-api/leads/aodi/filter-store/update`, {
    method: 'POST',
    data: body,
  });
}

export type LeDaoClueParams = {
  /**
   * 广告主 id
   */
  advertiserId?: string;
  /**
   * 广告主类型 1:短视频 2:直播
   */
  advertiserType?: number;
  /**
   * 创建结束时间
   */
  createTimeEnd?: string;
  /**
   * 创建开始时间
   */
  createTimeStart?: string;
  /**
   * 抖音号
   */
  referDyId?: string;
  /**
   * 线索编码
   */
  srcInterface?: string;
  /**
   * 更新结束时间
   */
  updateTimeEnd?: string;
  /**
   * 更新开始时间
   */
  updateTimeStart?: string;
} & PageBasicParams;

export type LeDaoClueData = {
  /**
   * 客户来源 1:抖音本地通 2:抖音 3:奥迪官号 4:南区矩阵 5:西区矩阵
   */
  sourceType?: number;
  /**
   * 广告主id
   */
  advertiserId?: string;
  /**
   * 广告主类型 1:短视频 2:直播
   */
  advertiserType?: number;
  /**
   * 创建人
   */
  createBy?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 员工域账号
   */
  empDomain?: string;
  /**
   * 主键
   */
  id?: number;
  /**
   * 推荐员工域账号
   */
  koeEmpDomain?: string;
  /**
   * 抖音号
   */
  referDyId?: string;
  /**
   * 来源经销商编码
   */
  sourceCommerceKey?: string;
  /**
   * 线索编码
   */
  srcInterface?: string;
  /**
   * 更新人
   */
  updateBy?: string;
  /**
   * 更新时间
   */
  updateTime?: string;
};

export function LeDaoClueQuery(params: LeDaoClueParams) {
  return request<ApiResult<PagintaionData<LeDaoClueData>>>('/new-media-api/leads/ledao/page', {
    method: 'GET',
    params,
  });
}

export function LeDaoDownloadTemplate() {
  return request<any>('/new-media-api/leads/ledao/download-template', {
    method: 'GET',
    responseType: 'blob',
  });
}

export function LeDaoClueExport(params: {
  /**
   * 抖音号
   */
  referDyId?: string;
}) {
  return request<ApiResult<string>>('/new-media-api/leads/ledao/export', {
    method: 'GET',
    params,
  });
}

export type LeDaoFormValues = {
  /**
   * 客户来源 1:抖音本地通 2:抖音 3:奥迪官号 4:南区矩阵 5:西区矩阵
   */
  sourceType: number;
  /**
   * 广告主id
   */
  advertiserId: string;
  /**
   * 广告主类型 1:短视频 2:直播
   */
  advertiserType: number;
  /**
   * 员工域账号
   */
  empDomain?: string;
  /**
   * 推荐员工域账号
   */
  koeEmpDomain?: string;
  /**
   * 抖音号
   */
  referDyId: string;
  /**
   * 来源经销商编码
   */
  sourceCommerceKey?: string;
  /**
   * 线索编码
   */
  srcInterface: string;
  /**
   * 账号类型 1-不限类型 2-企业号 3-员工号 4-普通号
   */
  accountType: number;
};

export function LeDaoClueCreate(data: LeDaoFormValues) {
  return request<ApiResult<boolean>>('/new-media-api/leads/ledao/create', {
    method: 'POST',
    data,
  });
}

export function LeDaoClueUpdate(data: { id: number } & LeDaoFormValues) {
  return request<ApiResult<boolean>>('/new-media-api/leads/ledao/update', {
    method: 'POST',
    data,
  });
}

export function LeDaoClueDelete(data: number[]) {
  return request<ApiResult<boolean>>('/new-media-api/leads/ledao/delete', {
    method: 'POST',
    data,
  });
}

export function LeDaoClueImport(params: { sourceType: number }, data: FormData) {
  return request<ApiResult<boolean>>('/new-media-api/leads/ledao/import', {
    method: 'POST',
    data,
    params,
  });
}
