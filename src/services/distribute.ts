import { request } from '@umijs/max';

import { ApiResult, PageBasicParams, PagintaionData } from './common';
import { RcFile } from 'antd/es/upload';

/** 分发任务 POST /distribute */
export async function distributePost(body: PostDistributeDTO) {
  return request<ApiResult<boolean>>(`/new-media-api/distribute`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
  });
}

/** 删除分发任务 POST /distribute/delete */
export async function deleteDistribute(params: {
  /** 项目id */
  projectId: string;
  /** 分发配置id */
  distributionId: string;
}) {
  return request<ApiResult<boolean>>(`/new-media-api/distribute/delete`, {
    method: 'POST',
    params: {
      ...params,
    },
  });
}

/** 根据id查看配置信息 GET /distribute/get-distribution-info */
export async function getByDistributionInfoById(params: {
  /** 项目id */
  projectId: string;
  /** 分发配置id */
  distributionId: string;
}) {
  return request<ApiResult<string>>(`/new-media-api/distribute/get-distribution-info`, {
    method: 'GET',
    params,
  });
}

/** 查看最近编辑配置任务 GET /distribute/get-recent-distribution-info */
export async function getLatestDistribution(params: {
  /** 项目id */
  projectId: string;
}) {
  return request<ApiResult<PostDistributeDTO>>(
    `/new-media-api/distribute/get-recent-distribution-info`,
    {
      method: 'GET',
      params,
    },
  );
}
export interface TitleParams {
  /** 标题案例 */
  title: string;
  /** 生成标题数量 */
  count: number;
  projectId: string;
}
/** ai生成标题 GET /distribute/get-titles */
export async function getTitlesByDify(params: TitleParams) {
  return request<ApiResult<string[]>>(`/new-media-api/distribute/get-titles`, {
    method: 'POST',
    timeout: 60000,
    data: {
      ...params,
    },
  });
}

// /** 导入标题信息 导入标题信息 POST /distribute/import-title */
export async function importTitle(body: { file: RcFile }) {
  return request<ApiResult<string[]>>(`/new-media-api/distribute/import`, {
    method: 'POST',
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    data: body,
  });
}

// /** 导入视频信息 导入视频信息 POST /distribute/import-video */
export async function importVideo(body: { file: RcFile }) {
  return request<ApiResult<string[]>>(`/new-media-api/distribute/import`, {
    method: 'POST',
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    data: body,
  });
}

/** 新建分发任务 POST /distribute/insert */
export async function createDistribute(body: PostDistributeDTO) {
  return request<ApiResult<string>>(`/new-media-api/distribute/insert`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
  });
}

/** 分页查询分发配置信息 GET /distribute/page */
export async function getDistributeList(
  params: {
    /** 项目id */
    projectId: number;
    /** 状态 */
    status?: number;
    /** 开始时间 */
    startTime?: string;
    /** 结束时间 */
    endTime?: string;
  } & PageBasicParams,
) {
  return request<ApiResult<PagintaionData<SpiderAccountPostDistributeVO>>>(
    `/new-media-api/distribute/page`,
    {
      method: 'GET',
      params: {
        ...params,
      },
    },
  );
}

/** 作品分发详情分页 */
export async function getDistributeDetailList(
  params: {
    /** 项目id */
    projectId: string;
    /** 分发id */
    distributionId: string;
  } & PageBasicParams,
) {
  return request<ApiResult<PagintaionData<DistributeVideoItem>>>(
    `/new-media-api/spider-log/distribute-page`,
    {
      method: 'GET',
      params: {
        ...params,
      },
    },
  );
}

/** 更新分发任务 POST /distribute/update */
export async function updateDistribute(body: PostDistributeDTO) {
  return request<ApiResult<boolean>>(`/new-media-api/distribute/update`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
  });
}

/** 分页查询分发账号信息 POST /distribute/account-page */
export async function getDistributeAccountPage(
  body: {
    /** 项目id */
    projectId: string;
    /** 平台 */
    platform: number;
    /** 1:自动分发,2:任务派发 */
    type: number;
    /** 账号名称 */
    nickName?: string;
    /** 团队列表 */
    teamCodeList?: string[];
  } & PageBasicParams,
) {
  return request<ApiResult<PagintaionData<PostDistributeAccountPageVO>>>(
    `/new-media-api/distribute/account-page?page=${body.page}&size=${body.size}`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: body,
    },
  );
}

interface PostDistributeDTO {
  /** 分发主键id */
  id?: string;
  /** 项目id */
  projectId?: string;
  /** 分布配置 */
  config?: string;
}

export interface SpiderAccountPostDistributeVO {
  /** 分发主键id */
  id: string;
  /** 发布视频数 */
  videoCount?: number;
  /** 状态 0待发布 1发布中 2发布成功 3发布失败 4未发布  */
  status: 0 | 1 | 2 | 3 | 4;
  /** 任务开始时间 */
  taskStartTime?: string;
  /** 任务完成（结束）时间 */
  taskEndTime?: string;
  /** 账号数量 */
  accountCount?: number;
  /**	1:自动分发,2:任务派发 */
  type: number;
  /** 平台1抖音，5视频号， 6小红书 */
  platform: number;
  /** 头像 */
  avatars: string[];
}

export interface DistributeVideoItem {
  /** id */
  id: string;
  /** 视频链接 */
  videoUrl: string;
  /** 封面 */
  coverUrl: string;
  /** 平台1抖音，5视频号， 6小红书 */
  platform: number;
  /** 账号名称 */
  nickname: string;
  /** 任务类型 0数据采集 1视频分发 2任务派发 */
  type: number;
  /** 任务状态码 0待执行 1任务执行中 2任务成功 3任务失败 */
  status: 0 | 1 | 2 | 3;
  /** 任务开始时间 */
  taskStartTime: string;
  /** 任务结束时间 */
  taskEndTime: string;
  /** 蓝v  0：否 1：是*/
  blueVipFlag: number;
  /** 头像 */
  avatar: string;
  teamFields: TeamField[];
}

interface TeamField {
  /** 维度id */
  fieldValueId?: number;
  /** 维度名 */
  fieldName?: string;
  /** 维度值 */
  fieldValue?: string;
  /** 维度类型 0: 自定义 1：默认 */
  type?: number;
  /** 维度 id */
  fieldId: string;
  /** 是否展示 0: 不展示 1: 展示 */
  showFlag?: number;
  /** 0一般1团队名字2层级关系 */
  bizType?: number;
}

export interface PostDistributeAccountPageVO {
  /** 团队id */
  id?: number;
  /** 账号名称 */
  nickname?: string;
  /** 头像 */
  avatar?: string;
  /** 账号id */
  accountId?: string;
  /** 平台：1抖音，4视频号，6小红书 */
  platform?: number;
  /** 团队维度信息集合 */
  teamFields?: TeamField[];
  shortName?: string;
  district?: string;
  city?: string;
  province?: string;
}
