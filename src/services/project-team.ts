import { request } from '@umijs/max';
import { ApiResult } from './common';
import { PlatForm } from '@/utils/platform';

export type ProjectTeamAccountItem = {
  /**
   * 抖音账号 id
   */
  accountId?: string;
  /**
   * 创建时间
   */
  createTime?: string;
  /**
   * 抖音 api授权账号时间
   */
  douyinApiGrantTime?: string;
  /**
   * 托管方式授权状态
   */
  hostingGrantStatus?: number;
  /**
   * 托管方式授权时间
   */
  hostingGrantTime?: string;
  /**
   * 账号名称
   */
  nickname?: string;
  /**
   * 抖音号
   */
  showAccountId?: string;
  /**
   * 授权状态
   */
  status?: number;
  /**
   * 团队识别码
   */
  teamIdentificationCode?: string;
};

export type GetProjectTeamAccountPageRes = {
  items?: ProjectTeamAccountItem[];
  total?: number;
};

export function GetProjectTeamAccountPage(params: { projectId?: string; platform?: PlatForm }) {
  return request<ApiResult<GetProjectTeamAccountPageRes>>(
    '/new-media-api/project-team-account/page',
    {
      method: 'GET',
      params,
    },
  );
}

export function ExportProjectTeamAccount(params: { projectId?: string; platform?: PlatForm }) {
  return request('/new-media-api/project-team-account/export', {
    method: 'GET',
    params,
    responseType: 'blob',
  });
}

export function DownloadProjectTeamAccountTemplate(params: { platform?: PlatForm }) {
  return request('/new-media-api/project-team-account/download-template', {
    method: 'GET',
    params,
    responseType: 'blob',
  });
}

export function ImportProjectTeamAccount(formData: any, projectId?: string, platform?: PlatForm) {
  return request<ApiResult<{ successFlag: boolean; failFileUrl: string }>>(
    `/new-media-api/project-team-account/import?projectId=${projectId}&platform=${platform}`,
    {
      method: 'POST',
      data: formData,
    },
  );
}

export function ImportProjectTeamAccountV2(formData: any, projectId?: string, platform?: PlatForm) {
  return request<ApiResult<{ successFlag: boolean; failFileUrl: string }>>(
    `/new-media-api/project-team-account/import/v2?projectId=${projectId}&platform=${platform}`,
    {
      method: 'POST',
      data: formData,
    },
  );
}
