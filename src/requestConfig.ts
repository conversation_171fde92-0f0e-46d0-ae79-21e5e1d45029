﻿import { loginPath } from '@/utils/const';
import { checkFreePage } from '@/utils/index';
import type { RequestConfig, RequestOptions } from '@umijs/max';
import { history } from '@umijs/max';
import { message } from 'antd';
import { omitBy } from 'lodash-es';
import qs from 'qs';
import { PlatForm } from './utils/platform';

// 与后端约定的响应数据格式
interface ResponseStructure {
  code?: number;
  data: any;
  msg: string;
}

// 特殊要求：platform 参数为 ALL 时，不传该参数
function clearEmptyParam(config: any) {
  if (config.params?.platform === PlatForm.ALL) {
    config.params.platform = undefined;
  }
  config.params = omitBy(config.params, (x) => x === undefined || x === null || x === '');
}

/**
 * @name 错误处理
 * pro 自带的错误处理， 可以在这里做自己的改动
 * @doc https://umijs.org/docs/max/request#配置
 */
export const requestConfig: RequestConfig = {
  errorConfig: {
    // 错误接收及处理
    errorHandler: (error: any, opts: any) => {
      if (opts?.skipErrorHandler) throw error;
      // 我们的 errorThrower 抛出的错误。
      if (error.response) {
        // Axios 的错误
        // 请求成功发出且服务器也响应了状态码，但状态代码超出了 2xx 的范围
        const statusCode = error.response.status;
        if (statusCode !== 0) {
          message.error(`服务器出错: ${statusCode} ${error.response?.data?.msg}`);
          if ((statusCode === 401 || statusCode === 402) && !checkFreePage()) {
            history.replace(
              loginPath +
                '?redirect=' +
                encodeURIComponent(window.location.pathname + window.location.search),
            );
          }
        }
      } else if (error.request) {
        // 请求已经成功发起，但没有收到响应
        // \`error.request\` 在浏览器中是 XMLHttpRequest 的实例，
        // 而在node.js中是 http.ClientRequest 的实例
        message.error('没有响应! 请重试。');
      } else {
        // 发送请求时出了点问题
        message.error('请求出错！请重试。');
      }
    },
  },

  // 请求拦截器
  requestInterceptors: [
    (config: RequestOptions) => {
      // 拦截请求配置，进行个性化处理。
      // const url = config?.url?.concat('?token = 123');
      // return { ...config, url };
      clearEmptyParam(config);
      if (config.method === 'get' && config.params) {
        config.paramsSerializer = (params) => {
          return qs.stringify(params, { allowDots: true });
        };
        return config;
      } else {
        return config;
      }
    },
  ],

  // 响应拦截器
  responseInterceptors: [
    (response) => {
      // @ts-ignore
      const { skipErrorHandler } = response.config;
      if (skipErrorHandler) return response;
      // 拦截响应数据，进行个性化处理
      const { code, msg } = response.data as unknown as ResponseStructure;
      if (response.data instanceof Blob) {
        // 如果响应数据类型为blob，直接返回响应
        return response;
      }
      if (code && code !== 0) {
        if (!(code === 401 && checkFreePage())) {
          message.error(msg);
        }

        if ((code === 401 || code === 402) && !checkFreePage()) {
          history.replace(
            loginPath +
              '?redirect=' +
              encodeURIComponent(window.location.pathname + window.location.search),
          );
        }
      }

      return response;
    },
  ],
};
