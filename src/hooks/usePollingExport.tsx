import { PollingExport, TaskStatus } from '@/services/polling';
import { useRequest } from 'ahooks';
import { message } from 'antd';
import dayjs from 'dayjs';
import { saveAs } from 'file-saver';
import { useState } from 'react';

export const usePollingExport = (fileName: string = dayjs().format('YYYYMMDD')) => {
  const [percent, setPercent] = useState<number>(0);
  const [pollingLoading, setPollingLoading] = useState<boolean>(false);

  const { run: pollingExport, cancel } = useRequest(PollingExport, {
    manual: true,
    pollingInterval: 2000,
    onSuccess: (res) => {
      const taskStatus = res.data?.taskStatus;
      if (taskStatus === TaskStatus.FINISHED) {
        setPollingLoading(false);
        // @ts-ignore
        fetch(res.data.fileUrl)
          .then((response) => response.blob())
          .then((blob) => {
            setPollingLoading(false);
            setPercent(0);
            saveAs(blob, `${fileName}.xls`);
          })
          .catch(() => {
            message.error('导出失败');
          });
        cancel();
      } else if (taskStatus === TaskStatus.UNFINISHED) {
        setPollingLoading(true);
        res.data && setPercent(res.data.progress);
      } else if (taskStatus === TaskStatus.FAILED) {
        setPollingLoading(false);
        setPercent(0);
        cancel();
      }
    },
    onError: () => {
      setPollingLoading(false);
      setPercent(0);
      message.error('导出失败');
    },
  });

  return { pollingExport, percent, pollingLoading };
};
