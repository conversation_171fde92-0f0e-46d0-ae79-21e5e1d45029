import { FunctionCode, GetSystemFunction, GetSystemFunctionDaily } from '@/services/system';
import { useRequest } from '@umijs/max';

function useLiveAfkFG(params?: string | null, unAuth?: boolean) {
  const { data: systemFunction } = useRequest(
    unAuth
      ? () => GetSystemFunctionDaily({ payload: params })
      : () => GetSystemFunction({ projectId: params }),
    {
      ready: !!params,
    },
  );

  const afkFunctionState = systemFunction?.find(
    (item) => item.functionCode === FunctionCode.EmptyBroadcastRecognition,
  )?.isDisabled;

  return !afkFunctionState;
}

export default useLiveAfkFG;
