import { GetAnchorImageList } from '@/services/quality';
import { useRequest } from '@umijs/max';

function useAnchorImageFG(projectId?: string) {
  const { data: anchorImageData } = useRequest(() => GetAnchorImageList({ projectId }), {
    refreshDeps: [projectId],
    ready: !!projectId,
  });

  const anchorImageState = anchorImageData?.some((item) => item.isDisabled === 0);

  return anchorImageState;
}

export default useAnchorImageFG;
