import useProjectId from '../useProjectId';
import { FunctionCode, GetSystemFunction, SystemFunctionResult } from '@/services/system';
import { useMemo } from 'react';
import { useRequest } from 'ahooks';

/** 开关控制一些路由routerKey, 对于要通过 FG 控制的模块在 routes 文件添加对应的 key */
export const SWITCHER_DICT = {
  [FunctionCode.BarrageAnalysis]: {
    id: FunctionCode.BarrageAnalysis,
    name: '弹幕分析',
    routeKey: 'barrageAnalysis',
  },
  [FunctionCode.LiveAnalysis]: {
    id: FunctionCode.LiveAnalysis,
    name: '直播分析',
    routeKey: 'liveAnalysis',
  },
  [FunctionCode.ProjectWorksAnalysis]: {
    id: FunctionCode.ProjectWorksAnalysis,
    name: '作品分析',
    routeKey: 'projectWorksAnalysis',
  },
  [FunctionCode.QualityCheck]: {
    id: FunctionCode.QualityCheck,
    name: '质检审核',
    routeKey: '',
  },
  [FunctionCode.PrivateMessage]: {
    id: FunctionCode.PrivateMessage,
    name: '私信管理',
    routeKey: 'message',
  },
  [FunctionCode.TeamOverview]: {
    id: FunctionCode.TeamOverview,
    name: '团队总览',
    routeKey: 'teamOverview',
  },
  [FunctionCode.Distribute]: {
    id: FunctionCode.Distribute,
    name: '矩阵分发',
    routeKey: 'distribute',
  },
};

/**
 * 判断/system/function/project/list接口下项目的某个功能开关是否启用
 */
const useProjectFeature = ({
  projectId: propsProjectId,
  featureId,
}: {
  projectId?: string;
  featureId?: FunctionCode;
}) => {
  const queryStringProjectId = useProjectId();
  const projectId = propsProjectId || queryStringProjectId;
  const cacheKey = `project-feature-${projectId}`;
  const {
    data: functionData,
    loading,
    refresh,
  } = useRequest(
    () => {
      if (!projectId) return Promise.reject();
      return GetSystemFunction({ projectId });
    },
    {
      cacheKey,
      refreshDeps: [projectId],
    },
  );

  const data = useMemo(() => {
    const featureEnableDict = functionData?.data?.reduce(
      (acc, cur) => {
        acc[cur.functionCode as FunctionCode] = !cur.isDisabled;
        return acc;
      },
      {} as Record<FunctionCode, boolean>,
    );

    const featureDict = functionData?.data?.reduce(
      (acc, cur) => {
        acc[cur.functionCode as FunctionCode] = cur;
        return acc;
      },
      {} as Record<FunctionCode, SystemFunctionResult>,
    );

    return {
      featureDict,
      featureEnableDict,
    };
  }, [functionData?.data]);

  return {
    /** 功能的字典 */
    featureDict: data.featureDict,
    /** 功能id对应的功能是否启用 */
    featureEnableDict: data.featureEnableDict,
    /** 传入的featureId对应的功能是否打开 */
    enable: featureId ? data.featureEnableDict?.[featureId] : true,
    /** 本次查询的cacheKey 在功能开关开启/关闭后应该clearCache清除 */
    cacheKey,
    refresh,
    isLoading: loading,
  };
};

export default useProjectFeature;
