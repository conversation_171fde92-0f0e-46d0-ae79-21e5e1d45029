import { QualityMonitorWordList, SceneTypeEnum } from '@/services/quality';
import { useRequest } from '@umijs/max';

function useBehaviorViolateFG(projectId?: string) {
  const { data: behaviorList } = useRequest(
    () => QualityMonitorWordList({ projectId, sceneType: SceneTypeEnum.BEHAVIOR }),
    {
      ready: !!projectId,
    },
  );

  const behaviorState = behaviorList?.some((item) => item.isDisabled === 0);

  return behaviorState;
}

export default useBehaviorViolateFG;
