import { useMemo } from 'react';
import routes from '../../config/routes';

import { ANTD_ICON_MAPS } from '@/utils/const';
import useProjectId from './useProjectId';

import useProjectFeature, { SWITCHER_DICT } from './fg/useProjectFeature';
import { FunctionCode } from '@/services/system';
import { useParams } from '@umijs/max';

export interface RouteItem {
  path: string;
  component: string;
  layout: boolean;
  name?: string;
  access?: undefined;
  redirect?: undefined;
  icon?: keyof typeof ANTD_ICON_MAPS;
  routes?: RouteItem[];
  hideInMenu?: boolean;
  [key: string]: any;
  parentKeys?: string[];
}

export interface ProjectRoutes {
  path: string;
  name?: string;
  icon?: React.ReactElement;
  routes?: ProjectRoutes[];
  hideInMenu?: boolean;
  access?: string;
  [key: string]: any;
  parentKeys?: string[];
}

let projectIdParams: string | undefined;

// 当跟路由组下的子路由都隐藏时隐藏
const hideRouteWhenAllChildHide = (routes: ProjectRoutes[]) => {
  return routes.map((item) => {
    const childRoutes = item.routes;

    if (childRoutes && Array.isArray(childRoutes)) {
      const allChildHide = childRoutes.every((childRoute) => childRoute.hideInMenu);

      if (allChildHide) {
        return {
          ...item,
          hideInMenu: true,
        };
      }
    }

    return {
      ...item,
    };
  });
};

// 处理路由 映射icon组件
const loopRoutes = (
  routes: RouteItem[],
  hideControl: Record<string, boolean>,
  industryType?: string,
): ProjectRoutes[] => {
  if (!routes) {
    return [];
  }
  return routes.map((item) => {
    const { path, name, icon, routes, access, key, parentKeys } = item;
    const isPriceInspection = name === '报价质检';
    const isBusiness = name === '商务政策管理';
    const isClue = name === '线索管理';
    const isAodi = projectIdParams === '60'; // 奥迪项目
    const isYiqi = projectIdParams === '22'; // 一起播项目

    // 合并现有的 hideInMenu 设置，如果已定义则保留原值，否则默认为 false
    const existingHideInMenu = typeof item.hideInMenu !== 'undefined' ? item.hideInMenu : false;

    // 根据特定条件判断是否需要隐藏菜单项
    const shouldHide =
      existingHideInMenu || // 使用现有的 hideInMenu 设置
      (isPriceInspection && !isYiqi && !isAodi) || // 报价质检在一起播与奥迪项目现实
      (isBusiness && isAodi) || // 奥迪项目隐藏商务政策管理
      (isClue && isAodi) || // 奥迪项目隐藏线索管理
      (key !== undefined && key in hideControl && hideControl[key] === true) ||
      (key === 'clue' && industryType !== '1'); // 线索管理只在汽车行业显示

    return {
      path,
      name,
      icon: icon ? ANTD_ICON_MAPS[icon] : undefined,
      routes:
        routes && Array.isArray(routes) ? loopRoutes(routes, hideControl, industryType) : undefined,
      // 隐藏的路由key权限访问也隐藏
      access,
      // TODO 要根据接口来开启模块
      hideInMenu: shouldHide,
      key,
      parentKeys,
    };
  });
};

function useProjectRoutes() {
  const projectId = useProjectId();
  const { industryType } = useParams();

  const { featureEnableDict } = useProjectFeature({ projectId });

  if (!projectIdParams) {
    projectIdParams = projectId;
  }

  const projectRoutes = useMemo(() => {
    const rawProjectRoutes = routes.find((item) => item.path.indexOf('/project/') === 0);
    const hideRoutes: Record<string, boolean> = {};

    for (const [key, item] of Object.entries(SWITCHER_DICT)) {
      // 默认不隐藏 等接口回来之后按照接口返回的结果来展示
      hideRoutes[item.routeKey as string] =
        // @ts-ignore
        featureEnableDict?.[key as FunctionCode] === undefined
          ? false
          : !featureEnableDict?.[key as FunctionCode];
    }

    let useRoutes =
      rawProjectRoutes?.routes && rawProjectRoutes?.routes.length
        ? loopRoutes(
            rawProjectRoutes.routes as RouteItem[],
            {
              ...hideRoutes,
            },
            industryType,
          )
        : undefined;

    if (useRoutes) {
      useRoutes = hideRouteWhenAllChildHide(useRoutes);
    }

    return {
      name: rawProjectRoutes?.name,
      path: rawProjectRoutes?.path || '/project/404',
      routes: useRoutes,
      access: rawProjectRoutes?.access,
    };
  }, [featureEnableDict]);

  return projectRoutes;
}

export default useProjectRoutes;
