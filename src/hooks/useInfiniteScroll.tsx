import { selectPlatformAtom } from '@/pages/ProjectHome/atom';
import { useParams, useSearchParams } from '@umijs/max';
import { CheckListValue } from 'antd-mobile/es/components/check-list';
import { useAtomValue } from 'jotai';
import { useEffect, useRef, useState } from 'react';
import InfiniteScroll from 'react-infinite-scroll-component';
import styled from 'styled-components';

const ShowTip = styled.div`
  height: 3rem;
  display: flex;
  align-items: center;
`;

type UseInfiniteScrollType<T> = {
  rangeDate?: string[];
  fieldValueStr?: string;
  keyword?: string;
  children: (data: T[]) => React.ReactNode;
  fetchFn: (params: any, data: any) => Promise<any>;
  liveAfkLevel?: CheckListValue | null;
  monitorType?: CheckListValue[];
};

const HidScrollBarInfiniteScroll = styled(InfiniteScroll as any)`
  &::-webkit-scrollbar {
    display: none; /* Safari 和 Chrome */
  }
  -ms-overflow-style: none; /* IE 和 Edge */
  scrollbar-width: none; /* Firefox */
`;

const UseInfiniteScroll = <T,>({
  fetchFn,
  children,
  rangeDate,
  fieldValueStr,
  keyword,
  liveAfkLevel,
  monitorType,
}: UseInfiniteScrollType<T>) => {
  const [startDate, endDate] = rangeDate || [undefined, undefined];

  // 滚动加载相关
  const [data, setData] = useState<T[]>([]);
  const pageRef = useRef(1);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const { projectId } = useParams();
  const [searchParams] = useSearchParams();
  const payload = searchParams.get('payload');
  const platform = useAtomValue(selectPlatformAtom);

  const fetchData = async () => {
    if (rangeDate?.length === 0 || !rangeDate) return;
    const page = pageRef.current;
    const queryParams: any = {
      startDate,
      endDate,
      projectId: payload ? -1 : projectId,
      liveAfkLevel,
      qualityCategoryTypeList: monitorType,
      platform,
    };
    if (fieldValueStr) {
      const [value, fieldId] = fieldValueStr.split('-');
      queryParams.fieldList = [{ fieldId, value }];
    }
    if (keyword) {
      queryParams.keyword = keyword;
    }
    let res;
    if (payload) {
      res = await fetchFn({ payload, page, size: 10 }, queryParams);
    } else {
      res = await fetchFn({ projectId, page, size: 10 }, queryParams);
    }
    const data = res.data.items;
    const total = res.data.total;
    setTotal(Number(total || 0));
    if (data && data.length > 0) {
      setData((preData) => [...preData, ...data]);
      pageRef.current += 1;
    } else {
      setData([]);
      pageRef.current = 1;
    }
    setLoading(false);
  };

  const loadMoreData = () => {
    if (loading) return;
    setLoading(true);
    fetchData();
  };

  useEffect(() => {
    pageRef.current = 1;
    setData([]);
    setLoading(true);
    fetchData();
  }, [rangeDate, fieldValueStr, keyword, liveAfkLevel, monitorType, platform]);

  return (
    <HidScrollBarInfiniteScroll
      dataLength={data.length}
      hasMore={data.length < total}
      next={loadMoreData}
      loader={<ShowTip>加载中...</ShowTip>}
      endMessage={<ShowTip>{loading ? '加载中...' : '已展示全部'}</ShowTip>}
      scrollableTarget="scrollableDiv"
      // 不设置这个height可能会出现无法触发next的情况
      // https://stackoverflow.com/questions/70680133/next-prop-not-working-in-react-infinite-scroll-component/74509225#74509225
      height={`calc(100vh - 2rem - 2.75rem - 2.25rem - 2rem)`}
    >
      {children(data)}
    </HidScrollBarInfiniteScroll>
  );
};

export default UseInfiniteScroll;
