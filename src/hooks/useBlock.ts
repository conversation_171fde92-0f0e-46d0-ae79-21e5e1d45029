import React from 'react';
import { history } from 'umi';

type Blocker = Parameters<typeof history.block>[0];

export function useBlocker(blocker: Blocker, when = true): void {
  React.useEffect(() => {
    if (!when) return;

    const unblock = history.block((tx) => {
      const autoUnblockingTx = {
        ...tx,
        retry() {
          // Automatically unblock the transition so it can play all the way
          // through before retrying it. TODO: Figure out how to re-enable
          // this block if the transition is cancelled for some reason.
          unblock();
          tx.retry();
        },
      };

      blocker(autoUnblockingTx);
    });

    return unblock;
  }, [navigator, blocker, when]);
}

// export function usePrompt(message: string, when = true) {
//   const blocker: Blocker = React.useCallback(
//     (tx) => {
//       if (window.confirm(message)) tx.retry();
//     },
//     [message],
//   );

//   useBlocker(blocker, when);
// }
