import { QueryTeamListFields } from '@/services/project';
import { ProjectTeamFieldItem } from '@/services/typings';
import { useRequest } from 'ahooks';
import { useRef } from 'react';

const useGetTeamFieldList = (projectId: string | undefined) => {
  const teamFieldListRef = useRef<ProjectTeamFieldItem[] | undefined | null>([]);
  useRequest(
    () => {
      if (!projectId) {
        return Promise.resolve(null);
      }
      return QueryTeamListFields({ projectId });
    },
    {
      onSuccess: (result) => {
        teamFieldListRef.current = result?.data || [];
      },
      refreshDeps: [projectId],
      ready: !!projectId,
    },
  );
  return teamFieldListRef;
};

export default useGetTeamFieldList;
