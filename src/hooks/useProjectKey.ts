import { useModel } from '@umijs/max';

/**
 * 通过 projectId 获取对应的 projectKey
 * @param projectId 项目ID
 * @returns projectKey 项目标识
 */
function useProjectKey(projectId?: string | null) {
  const { initialState } = useModel('@@initialState');
  const { projectData } = initialState || {};

  const projectKey = projectId
    ? // eslint-disable-next-line @typescript-eslint/no-unused-vars
      Object.entries(projectData || {}).find(([_, value]) => value === projectId)?.[0]
    : undefined;

  return projectKey;
}

export default useProjectKey;
