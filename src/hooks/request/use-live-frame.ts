import { ApiResult } from '@/services/common';
import { liveScreenAll, LiveScreenItem, MonitorItem, SaveManual } from '@/services/quality';
import { useMutation, useQuery, useQueryClient } from '@umijs/max';
import { IQueryParam } from './use-live-subtitle';

export const useLiveFrame = ({ roomId, platform }: IQueryParam) => {
  const { data, error, isFetching } = useQuery({
    queryFn: () => liveScreenAll({ roomId, platform }),
    queryKey: [`live-screen`, roomId, platform],
    enabled: !!roomId && !!platform,
  });
  return { data: data?.data, error, loading: isFetching };
};

export function useSubmitLiveFrameExInfoMutation() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: ({ submitData }: IQueryParam & { submitData: any; projectId?: string }) =>
      SaveManual(submitData),
    // When mutate is called:
    onMutate: async ({
      roomId,
      platform,
      submitData,
      projectId,
    }: IQueryParam & { submitData: any; projectId?: string }) => {
      console.log('submitData: ', submitData);
      // Cancel any outgoing refetch
      // (so they don't overwrite our optimistic update)
      await queryClient.cancelQueries({ queryKey: ['live-screen', roomId, platform] });

      // Snapshot the previous value
      const previousSubtitles = queryClient.getQueryData<ApiResult<LiveScreenItem[]>>([
        'live-screen',
        roomId,
        platform,
      ]);

      // Optimistically update to the new value
      if (previousSubtitles) {
        const ids = submitData.map((item: any) => item.picUrl);

        const qualityMonitorWordList = queryClient.getQueryData<ApiResult<MonitorItem[]>>([
          'qualityMonitorWordList',
          projectId,
          2,
        ]);

        queryClient.setQueryData(['live-screen', roomId, platform], {
          ...previousSubtitles,
          data: previousSubtitles.data?.map((item) => {
            if (ids.includes(item.url)) {
              const submitItem = submitData.find(
                (submitItem: any) => submitItem.picUrl === item.url,
              );

              const exInfoItem = qualityMonitorWordList?.data?.find(
                (qualityItem) => qualityItem.id === submitItem.monitoringWordId,
              );

              return {
                ...item,
                exInfo: {
                  // TODO
                  monitoringWordName: exInfoItem?.name,
                },
              };
            }
            return item;
          }),
        });
      }
      queryClient.invalidateQueries({
        queryKey: [`qualityDetail`],
      });
      return { previousData: previousSubtitles };
    },
    // If the mutation fails,
    // use the context returned from onMutate to roll back
    onError: (err, variables, context) => {
      if (context?.previousData) {
        queryClient.setQueryData(
          [`live-screen`, variables.roomId, variables.platform],
          context.previousData,
        );
      }
    },
    // Always refetch after error or success:
    onSettled: (data, error, variables) => {
      queryClient.invalidateQueries({
        queryKey: [`live-screen`, variables.roomId, variables.platform],
      });
      queryClient.invalidateQueries({
        queryKey: [`qualityDetail`],
      });
    },
  });
}
