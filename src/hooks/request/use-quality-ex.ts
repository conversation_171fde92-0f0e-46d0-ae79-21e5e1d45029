import { ApiResult } from '@/services/common';
import { QualityTargetType } from '@/services/constants';
import { DeleteManual, LiveScreenItem, LiveSubtitleItem, QualityItem } from '@/services/quality';
import { useMutation, useParams, useQueryClient } from '@umijs/max';
import { App } from 'antd';

export function useDeleteExInfoMutation(targetType: QualityTargetType) {
  const queryClient = useQueryClient();
  const { roomId, platform } = useParams();
  const { message } = App.useApp();

  return useMutation({
    mutationFn: (qualityItem: QualityItem) => DeleteManual([qualityItem['groupId']]),
    onMutate: async (qualityItem: QualityItem) => {
      // TODO 作品的删除暂时就不走乐观更新了 发现会出现qualityDetail被setQueryData不正确的值 由于key都相同
      if (targetType === QualityTargetType.POST) return;
      const previousLiveFrame = queryClient.getQueryData<ApiResult<LiveScreenItem[]>>([
        'live-screen',
        roomId,
        platform,
      ]);

      const previousSubtitles = queryClient.getQueryData<ApiResult<LiveSubtitleItem[]>>([
        'live-subtitle',
        roomId,
        platform,
      ]);
      const previousQualityDetailData = queryClient.getQueryData<ApiResult<QualityItem[]>>([
        'qualityDetail',
        roomId,
        QualityTargetType.LIVE,
        platform,
      ]);

      const newQualityDetailData = previousQualityDetailData?.data?.filter(
        (item) => item.groupId !== qualityItem.groupId,
      );
      queryClient.setQueryData(['qualityDetail', roomId, QualityTargetType.LIVE, platform], {
        ...previousQualityDetailData,
        data: newQualityDetailData,
      });
      // Optimistically update to the new value
      if (previousLiveFrame && previousSubtitles && previousQualityDetailData) {
        if (qualityItem.qualityRuleType === 2) {
          // 口播
          const newSubtitleData = previousSubtitles.data?.map((item) => {
            if (
              Number(item.startTime) === Number(qualityItem.startTime) &&
              Number(item.endTime) === Number(qualityItem.endTime)
            ) {
              return {
                ...item,
                exInfo: null,
              };
            }
            return item;
          });

          queryClient.setQueryData(['live-subtitle', roomId, platform], {
            ...previousSubtitles,
            data: newSubtitleData,
          });
        } else if (qualityItem.qualityRuleType === 4) {
          // 画面
          const pics = qualityItem.picUrl;
          const newFrameData = previousLiveFrame.data?.map((item) => {
            if (pics.includes(item.url)) {
              return {
                ...item,
                exInfo: null,
              };
            }
            return item;
          });

          queryClient.setQueryData(['live-screen', roomId, platform], {
            ...previousLiveFrame,
            data: newFrameData,
          });
        }
      }

      return {
        previousData: {
          previousLiveFrame,
          previousSubtitles,
          previousQualityDetailData,
        },
      };
    },
    // If the mutation fails,
    // use the context returned from onMutate to roll back
    onError: (err, variables, context) => {
      if (context?.previousData) {
        queryClient.setQueryData(
          [`live-screen`, roomId, platform],
          context.previousData.previousLiveFrame,
        );
        queryClient.setQueryData(
          [`live-subtitle`, roomId, platform],
          context.previousData.previousSubtitles,
        );
        queryClient.setQueryData(
          [`qualityDetail`, roomId, QualityTargetType.LIVE, platform],
          context.previousData.previousQualityDetailData,
        );
      }
      message.error('删除失败');
    },
    onSuccess: () => {
      message.success('删除成功');
    },
    // Always refetch after error or success:
    onSettled: (data, error, variables) => {
      queryClient.invalidateQueries({
        queryKey: [`qualityDetail`, roomId, QualityTargetType.LIVE, platform],
      });
      if (variables.qualityRuleType === 2) {
        // 口播
        queryClient.invalidateQueries({
          queryKey: [`live-subtitle`, roomId, platform],
        });
      } else if (variables.qualityRuleType === 4) {
        // 画面
        queryClient.invalidateQueries({
          queryKey: [`live-screen`, roomId, platform],
        });
      }
    },
  });
}
