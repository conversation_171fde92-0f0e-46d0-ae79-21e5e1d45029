import {
  GetLiveQuality,
  GetLiveQualityEcommerce,
  LiveQualityItem,
  LiveQualityEcommerceItem,
} from '@/services/quality';
import { useQuery } from '@umijs/max';
import { ApiResult } from '@/services/common';

type QueryParam = {
  roomId?: string;
  platform?: string;
  industryType?: string;
};

// 函数重载：根据 industryType 返回不同的类型
export function useLiveDetail(params: QueryParam & { industryType: '1' }): {
  data: LiveQualityItem | undefined;
  error: any;
  loading: boolean;
};

export function useLiveDetail(params: QueryParam & { industryType: '2' }): {
  data: LiveQualityEcommerceItem | undefined;
  error: any;
  loading: boolean;
};

export function useLiveDetail(params: QueryParam): {
  data: LiveQualityItem | LiveQualityEcommerceItem | undefined;
  error: any;
  loading: boolean;
};

// 实际实现
export function useLiveDetail({ roomId, platform, industryType }: QueryParam) {
  const { data, error, isFetching } = useQuery({
    queryKey: ['live-detail', roomId, platform, industryType],
    queryFn: async (): Promise<
      ApiResult<LiveQualityItem | LiveQualityEcommerceItem> | undefined
    > => {
      if (industryType === '1') {
        return GetLiveQuality({ roomId, platform });
      } else if (industryType === '2') {
        return GetLiveQualityEcommerce({ roomId, platform });
      }
      return undefined;
    },
    enabled: !!roomId && !!platform,
  });

  return { data: data?.data, error, loading: isFetching };
}
