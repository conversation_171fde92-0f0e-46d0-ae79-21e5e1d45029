import { ApiResult } from '@/services/common';
import { getLiveSubtitleAll, LiveSubtitleItem, MonitorItem, SaveManual } from '@/services/quality';
import { useMutation, useQuery, useQueryClient } from '@umijs/max';

export interface IQueryParam {
  roomId?: string;
  platform?: string;
}

export function useLiveSubtitle({ roomId, platform }: IQueryParam) {
  const { data, error, isFetching } = useQuery({
    queryKey: ['live-subtitle', roomId, platform],
    queryFn: () => getLiveSubtitleAll({ roomId, platform }),
    enabled: !!roomId && !!platform,
  });

  return { data: data?.data, error, loading: isFetching };
}

export function useSubmitSubtitleExInfoMutation() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: ({ submitData }: IQueryParam & { submitData: any; projectId?: string }) =>
      SaveManual(submitData),
    // When mutate is called:
    onMutate: async ({
      roomId,
      platform,
      submitData,
      projectId,
    }: IQueryParam & { submitData: any; projectId?: string }) => {
      // Cancel any outgoing refetch
      // (so they don't overwrite our optimistic update)
      await queryClient.cancelQueries({ queryKey: ['live-subtitle', roomId, platform] });

      // Snapshot the previous value
      const previousSubtitles = queryClient.getQueryData<ApiResult<LiveSubtitleItem[]>>([
        'live-subtitle',
        roomId,
        platform,
      ]);

      // Optimistically update to the new value
      if (previousSubtitles) {
        const qualityMonitorWordList = queryClient.getQueryData<ApiResult<MonitorItem[]>>([
          'qualityMonitorWordList',
          projectId,
          1,
        ]);
        const ids = submitData.map((item: any) => item.id);

        queryClient.setQueryData(['live-subtitle', roomId, platform], {
          ...previousSubtitles,
          data: previousSubtitles.data?.map((item) => {
            if (ids.includes(item.id)) {
              const submitItem = submitData.find((submitItem: any) => submitItem.id === item.id);
              const exInfoItem = qualityMonitorWordList?.data?.find(
                (qualityItem) => qualityItem.id === submitItem.monitoringWordId,
              );
              console.log('exInfoItem: ', exInfoItem);
              return {
                ...item,
                exInfo: {
                  monitoringWordName: exInfoItem?.name,
                },
              };
            }
            return item;
          }),
        });
      }
      queryClient.invalidateQueries({
        queryKey: [`qualityDetail`],
      });
      return { previousData: previousSubtitles };
    },
    // If the mutation fails,
    // use the context returned from onMutate to roll back
    onError: (err, variables, context) => {
      if (context?.previousData) {
        queryClient.setQueryData(
          [`live-subtitle`, variables.roomId, variables.platform],
          context.previousData,
        );
      }
    },
    // Always refetch after error or success:
    onSettled: (data, error, variables) => {
      queryClient.invalidateQueries({
        queryKey: [`live-subtitle`, variables.roomId, variables.platform],
      });
      queryClient.invalidateQueries({
        queryKey: [`qualityDetail`],
      });
    },
  });
}
