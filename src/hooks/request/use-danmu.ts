import { getLiveCommentAll } from '@/services/quality';
import { replaceDouyinEmoji } from '@/utils/douyinEmoji';
import { useQuery } from '@umijs/max';
import { IQueryParam } from './use-live-subtitle';

export const useLiveDanmu = ({ roomId, platform }: IQueryParam) => {
  const { data, error, isFetching } = useQuery({
    queryFn: () => getLiveCommentAll({ roomId, platform }),
    queryKey: [`live-comment`, roomId, platform],
    enabled: !!roomId && !!platform,
  });

  const danmu = data?.data;
  const replaceDanmuContent = danmu?.map((item) => {
    const { content } = item;
    return {
      ...item,
      content: replaceDouyinEmoji(content),
    };
  });

  return { data: replaceDanmuContent, error, loading: isFetching };
};
