import { DetailInfo, updateLocalStorage } from '@/utils/quality';
import { useEffect } from 'react';

function useBeforeUnloadRecords(
  localStorageName: 'liveDetailRecords' | 'postDetailRecords',
  recordPrams: Partial<DetailInfo>,
) {
  useEffect(() => {
    const handleBeforeUnload = () => {
      updateLocalStorage(localStorageName, {
        ...recordPrams,
      });
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [localStorageName, recordPrams]);
}

export default useBeforeUnloadRecords;
