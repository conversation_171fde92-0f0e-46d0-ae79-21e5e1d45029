import { useModel, useParams } from '@umijs/max';

function useProjectId(): string | undefined {
  const { initialState } = useModel('@@initialState');
  const { projectData } = initialState || {};
  const { projectKey, projectId: queryStringProjectId } = useParams();
  const projectId = projectKey && projectData?.[projectKey];

  return projectId || queryStringProjectId;
}

export default useProjectId;
