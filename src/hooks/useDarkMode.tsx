import { useLocalStorageState } from 'ahooks';
import { useEffect } from 'react';
import { enable as enableDarkMode, disable as disableDarkMode } from 'darkreader';

export const DARK_READER_CONFIG = {
  brightness: 100,
  contrast: 95,
  sepia: 5,
  darkSchemeBackgroundColor: '#1a1b1e',
  lightSchemeBackgroundColor: '#ffffff',
  ignoreInlineStyle: true,
};

const DARK_READER_FIXES = {
  invert: [],
  css: `
      .ant-switch:not(.ant-switch-checked) {
        background-color: #4E4E4E !important;
      }

      .ant-popover-inner {
        background-color: #2a2a2e !important;
        box-shadow: rgba(0, 0, 0, 0.15) 0px 6px 16px 0px,
              rgba(0, 0, 0, 0.23) 0px 3px 6px -4px,
            rgba(0, 0, 0, 0.1) 0px 9px 28px 8px !important;
      }
    `,
  ignoreInlineStyle: [],
  ignoreImageAnalysis: [],
  disableStyleSheetsProxy: false,
};
const updateHtmlClass = (isDark: boolean) => {
  const html = document.documentElement;
  if (isDark) {
    html.classList.add('dark');
  } else {
    html.classList.remove('dark');
  }
};

export const applyDarkMode = async (isDark: boolean) => {
  if (isDark) {
    enableDarkMode(DARK_READER_CONFIG, DARK_READER_FIXES);
  } else {
    disableDarkMode();
  }
  updateHtmlClass(isDark);
};

export const useDarkMode = () => {
  const [isDarkMode, setIsDarkMode] = useLocalStorageState('darkMode', {
    defaultValue: false,
    listenStorageChange: true,
  });

  useEffect(() => {
    if (!isDarkMode) return;

    const applyTheme = async () => {
      await applyDarkMode(true);
    };

    applyTheme();
  }, [isDarkMode]);

  const toggleDarkMode = () => {
    setIsDarkMode((prevMode) => {
      const newMode = !prevMode;
      applyDarkMode(newMode);
      return newMode;
    });
  };

  return {
    isDarkMode: isDarkMode as boolean,
    toggleDarkMode,
  };
};
