import { useEffect, useState } from 'react';

import { fillParams, getUrlParams } from '@/utils/url';

const DefaultTabKeySearchName = 'activeKey';

/**
 *
 * @param initActiveKey 初始激活的tab-key
 * @param searchParamName 写入页面路径参数名称
 * @returns
 */
function useTabKeySearchParams(
  initActiveKey: string,
  searchParamName: string = DefaultTabKeySearchName,
): [activeKey: string | undefined, onTabChange: (key: string) => void] {
  const [activeKey, setActiveKey] = useState<string | undefined>();
  const onTabChange = (activeKey: string) => {
    setActiveKey(activeKey);

    window.history.replaceState(null, '', fillParams({ [searchParamName]: activeKey }));
  };

  useEffect(() => {
    const urlSearchActiveKey = getUrlParams(searchParamName);
    if (urlSearchActiveKey) {
      setActiveKey(urlSearchActiveKey);
      return;
    }

    setActiveKey(initActiveKey);
  }, []);

  return [activeKey, onTabChange];
}

export default useTabKeySearchParams;
