import { useEffect, useState } from 'react';
import { TeamInfoItem } from '@/services/team';
import { douyinAuthPath } from '@/utils/const';

function useDouyinAuthLink(teamInfos?: TeamInfoItem[], projectId?: string) {
  const [douyinAuthLinks, setDouyinAuthLinks] = useState<{ [teamCode: string]: string }>({});

  const getTeamDouyinLink = (teamCode: string, projectId?: string) => {
    const link: string =
      window.location.origin +
      douyinAuthPath +
      '?' +
      `teamCode=${teamCode}&beforeAuth=true&projectId=${projectId}`;

    return link;
  };

  useEffect(() => {
    if (teamInfos) {
      const links: { [teamCode: string]: string } = {};
      teamInfos.forEach((item) => {
        const link = getTeamDouyinLink(item.teamCode, projectId);

        links[item.teamCode] = link;
      });

      setDouyinAuthLinks(links);
    } else {
      setDouyinAuthLinks({});
    }
  }, [teamInfos]);

  return douyinAuthLinks;
}

export default useDouyinAuthLink;
