import {
  convertToMobileTree,
  TreeNodeMobile,
} from '@/pages/DailyDetail/component/convertToMobileTree';
import { GetTreeData } from '@/services/team';
import { useAsyncEffect } from 'ahooks';
import { memoize } from 'lodash-es';
import { useState } from 'react';

function useGetCascaderOptionsByProjectId(projectId?: string) {
  const [cascaderOptions, setCascaderOptions] = useState<TreeNodeMobile[]>([]);
  const memoizedConvertToTree = memoize(convertToMobileTree);

  useAsyncEffect(async () => {
    if (!projectId) return;
    const res = await GetTreeData({ projectId });
    const data = res.data;
    if (data) {
      const convertTreeData = memoizedConvertToTree(data);
      setCascaderOptions(convertTreeData);
    } else {
      setCascaderOptions([]);
    }
  }, []);

  return cascaderOptions;
}

export default useGetCascaderOptionsByProjectId;
