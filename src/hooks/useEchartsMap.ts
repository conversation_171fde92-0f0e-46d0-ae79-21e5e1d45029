import { message } from 'antd';
import axios from 'axios';
import * as echarts from 'echarts';
import { useEffect, useState } from 'react';

// 地图对应json链接
const mapNameJson = {
  china:
    'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/resources-json/279590a6-2c8d-4a25-9319-c2f26adc09a6.json',
};

// 已注册地图列表
const registerMapList = new Map();

function useEchartsMap(mapName: 'china') {
  const [isRegister, setIsRegister] = useState<boolean>(
    registerMapList.get(mapName) ? true : false,
  );

  useEffect(() => {
    if (!isRegister) {
      const mapJson = mapNameJson[mapName];

      if (mapJson) {
        axios
          .get(mapJson)
          .then((res) => {
            console.log('get-map');
            echarts.registerMap('china', res.data, {});
            registerMapList.set(mapName, true);
            setIsRegister(true);
          })
          .catch(() => {
            message.error('获取地图失败，请刷新重试');
          });
      }
    }
  }, [isRegister, mapName]);

  return isRegister;
}

export default useEchartsMap;
