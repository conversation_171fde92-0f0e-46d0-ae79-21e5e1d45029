import {
  convertToMobileTree,
  TreeNodeMobile,
} from '@/pages/DailyDetail/component/convertToMobileTree';
import { GetTreeDataDaily } from '@/services/unauth';
import { useAsyncEffect } from 'ahooks';
import { memoize } from 'lodash-es';
import { useState } from 'react';

function useGetCascaderOptions(payload: string | null) {
  const [cascaderOptions, setCascaderOptions] = useState<TreeNodeMobile[]>([]);
  const memoizedConvertToTree = memoize(convertToMobileTree);

  useAsyncEffect(async () => {
    if (!payload) return;
    const res = await GetTreeDataDaily({ payload });
    const data = res.data;
    if (data) {
      const convertTreeData = memoizedConvertToTree(data);
      setCascaderOptions(convertTreeData);
    } else {
      setCascaderOptions([]);
    }
  }, []);

  return cascaderOptions;
}

export default useGetCascaderOptions;
