import { PortalUser } from './services/typings';

/**
 * @see https://umijs.org/zh-CN/plugins/plugin-access
 * */

export enum PermissionCode {
  ManageRead = 'manage:read',
  ManageWrite = 'manage:write',
  MonitorRead = 'monitor:read',
  MonitorWrite = 'monitor:write',
  ProjectRead = 'project:read',
  ProjectWrite = 'project:write',
  MaterialRead = 'material:read',
  MaterialWrite = 'material:write',
  LeadsRead = 'leads:read',
  LeadsWrite = 'leads:write',
  AuditRead = 'audit:read',
  AuditWrite = 'audit:write',
  ManualCustomerServiceRead = 'manual-customer-service:read',
  ManualCustomerServiceWrite = 'manual-customer-service:write',
}

// 角色 普通用户 超级管理员 项目管理员 自定义角色
export enum RoleCode {
  DEFAULT_USER = 'default:user',
  SUPER_ADMIN = 'super:admin',
  PROJECT_ADMIN = 'project:admin',
}

enum AccountType {
  SystemAccount = 0,
  FeishuAccount = 1,
  ExternalAccount = 2,
}

export default function access(initialState: { currentUser?: PortalUser } | undefined) {
  const { currentUser } = initialState ?? {};
  const permissionCodes = currentUser?.permissionCodes;
  const roleCodes = currentUser?.roleCodes;
  const type = currentUser?.type;

  const hasPermission = (code: PermissionCode) => permissionCodes?.includes(code);

  return {
    canReadManage: hasPermission(PermissionCode.ManageRead),
    canWriteManage: hasPermission(PermissionCode.ManageWrite),
    canReadProject: hasPermission(PermissionCode.ProjectRead),
    canWriteProject: hasPermission(PermissionCode.ProjectWrite),
    canReadMonitor: hasPermission(PermissionCode.MonitorRead),
    canWriteMonitor: hasPermission(PermissionCode.MonitorWrite),
    canReadMaterial: hasPermission(PermissionCode.MaterialRead),
    canWriteMaterial: hasPermission(PermissionCode.MaterialWrite),
    canReadLeads: hasPermission(PermissionCode.LeadsRead),
    canWriteLeads: hasPermission(PermissionCode.LeadsWrite),
    canReadAudit: hasPermission(PermissionCode.AuditRead),
    canWriteAudit: hasPermission(PermissionCode.AuditWrite),
    canReadManualCustomerService: hasPermission(PermissionCode.ManualCustomerServiceRead),
    canWriteManualCustomerService: hasPermission(PermissionCode.ManualCustomerServiceWrite),
    isAdmin:
      roleCodes?.includes(RoleCode.SUPER_ADMIN) || roleCodes?.includes(RoleCode.PROJECT_ADMIN),
    isInsideAccount: type !== AccountType.ExternalAccount,
  };
}
