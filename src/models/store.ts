import { GetTagGroups, TagsType } from '@/services/tag';
import { message } from 'antd';
import { atom } from 'jotai';

/**
 * 全局共享的数据，避免重复获取，也方便更好的封装组件
 */

// 监控账号的标签列表
const spiderSyncUserTagsReloadFlag = atom(0);

export const spiderSyncUserTags = atom(async (get) => {
  get(spiderSyncUserTagsReloadFlag);
  const res = await GetTagGroups(TagsType.spiderTag);
  if (res.code !== 0) {
    message.error('查询标签失败');
  }

  return res.code === 0 ? res.data : [];
});

spiderSyncUserTags.debugLabel = 'fetchSpiderSyncUserTags';

export const spiderSyncUserTagsReloader = atom(null, async (get, set) => {
  set(spiderSyncUserTagsReloadFlag, new Date().getTime());
});
