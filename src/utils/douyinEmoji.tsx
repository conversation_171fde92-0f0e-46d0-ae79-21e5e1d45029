import React from 'react';

const douyinEmojiMap: Record<string, string> = {
  '[OK]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f44c.png',
  '[666]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/co9.png',
  '[18禁]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f51e.png',
  '[V5]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/clv.png',
  '[不看]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f648.png',
  '[不失礼貌的微笑]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/cop.png',
  '[互粉]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/co3.png',
  '[亲亲]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f61a.png',
  '[便便]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f4a9.png',
  '[偷笑]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f92d.png',
  '[做鬼脸]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f61c.png',
  '[再见]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f44b.png',
  '[凋谢]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f940.png',
  '[加好友]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/cm9.png',
  '[击掌]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f64c.png',
  '[勾引]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/cmt.png',
  '[去污粉]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/co8.png',
  '[发]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f005.png',
  '[发呆]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/cnf.png',
  '[发怒]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f621.png',
  '[可怜]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f97a.png',
  '[右哼哼]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f624-new.png',
  '[右边]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f449.png',
  '[吃瓜群众]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/cox.png',
  '[吐]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f92e.png',
  '[吐彩虹]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f308.png',
  '[吐血]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/cnj.png',
  '[听歌]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f3a7.png',
  '[呆无辜]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/cor.png',
  '[呲牙]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f601.png',
  '[吐舌]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/coq.png',
  '[咒骂]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f92c.png',
  '[哈欠]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f971.png',
  '[咖啡]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/2615.png',
  '[啤酒]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f37a.png',
  '[嘘]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f92b.png',
  '[嘴唇]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f444.png',
  '[嘿哈]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/cm8.png',
  '[困]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/cna.png',
  '[囧]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f644.png',
  '[坏笑]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f62c.png',
  '[大笑]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f604.png',
  '[大哭]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f62d.png',
  '[大金牙]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f9b7.png',
  '[太阳]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f31e.png',
  '[奋斗]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/cpc.png',
  '[奸笑]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f60f-new.png',
  '[委屈]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f641-new.png',
  '[如花]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/cp8.png',
  '[害羞]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f60a.png',
  '[小鼓掌]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/cnd.png',
  '[尬笑]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f605-new.png',
  '[尴尬]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f605.png',
  '[左哼哼]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f624.png',
  '[左上]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f446.png',
  '[左边]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f448.png',
  '[弱]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f44e.png',
  '[强]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/cp7.png',
  '[强壮]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f4aa.png',
  '[得意]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f60e.png',
  '[微笑]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f642.png',
  '[心碎]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f494.png',
  '[思考]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f914.png',
  '[快哭了]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f625.png',
  '[悠闲]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f6ac.png',
  '[惊喜]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f929.png',
  '[恐惧]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f631.png',
  '[惊恐]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f628.png',
  '[愉快]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f604-new.png',
  '[惊讶]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f632.png',
  '[感谢]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f64f-new.png',
  '[憨笑]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f600.png',
  '[打脸]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f915.png',
  '[我想静静]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f611.png',
  '[抓狂]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f62b.png',
  '[抠鼻]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/co1.png',
  '[抱拳]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f64f.png',
  '[拳头]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/270a.png',
  '[握手]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f91d.png',
  '[捂脸]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f926.png',
  '[摸头]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f60c.png',
  '[撇嘴]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f615.png',
  '[擦汗]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/cp3.png',
  '[敲打]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f915-new.png',
  '[晕]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f635.png',
  '[斜眼]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f612.png',
  '[月亮]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f31c.png',
  '[来看我]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/cn1.png',
  '[机智]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/cn0.png',
  '[比心]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f91e.png',
  '[泣不成声]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/cnc.png',
  '[泪奔]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/cnv.png',
  '[派对]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f389.png',
  '[流汗]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f613.png',
  '[流泪]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f622-new.png',
  '[灵机一动]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/cn2.png',
  '[炸弹]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f4a3.png',
  '[熊吉]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f43b.png',
  '[爱心]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/2764.png',
  '[猪头]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f437.png',
  '[玫瑰]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f339.png',
  '[生病]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f637.png',
  '[白眼]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/cot.png',
  '[疑问]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/cnb.png',
  '[皱眉]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/cp2.png',
  '[看]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f436.webp',
  '[睡]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f62a.png',
  '[石化]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f630.png',
  '[碰拳]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f91b.png',
  '[礼物]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f381.png',
  '[笑哭]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f602.png',
  '[红包]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f9e7.png',
  '[红脸]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f633.png',
  '[紫薇别走]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/coj.png',
  '[给力]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/clw.png',
  '[绝望的凝视]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f61e.png',
  '[翻白眼]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f644-new.png',
  '[绿帽子]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/coz.png',
  '[耶]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/270c-new.png',
  '[胜利]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/270c.png',
  '[胡瓜]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f952.png',
  '[舔屏]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/cof.png',
  '[色]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f60d.png',
  '[菜刀]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f52a.png',
  '[蛋糕]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f382.png',
  '[衰]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f622.png',
  '[西瓜]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f349.png',
  '[调皮]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f61b.png',
  '[赞]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f44d.png',
  '[送心]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f970.png',
  '[鄙视]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/cog.png',
  '[酷拽]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/cnq.png',
  '[钱]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f911.png',
  '[闭嘴]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f910.png',
  '[阴险]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f60f.png',
  '[震惊]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f92f.png',
  '[难过]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f641.png',
  '[飞吻]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f618.png',
  '[骷髅]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f480.png',
  '[鼓掌]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f44f.png',
  '[黑脸]': 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/emojiall/1f31a.png',
};

export function replaceDouyinEmoji(content: string) {
  return content.replace(/\[.*?\]/g, (match) => {
    const emojiUrl = douyinEmojiMap[match];
    if (emojiUrl) {
      return `<img src="${emojiUrl}" alt="${match}" width="10" height="10"/>`;
    }
    return match; // 未知表情符号保持原样
  });
}

export function parseDouyinEmojiToReactNode(content?: string): React.ReactNode[] {
  if (!content) return [];
  const parts: React.ReactNode[] = [];
  let lastIndex = 0;

  // 使用正则表达式匹配表情符号 [xxx]
  const regex = /\[.*?\]/g;
  let match;

  while ((match = regex.exec(content)) !== null) {
    // 添加表情符号前的文本
    if (match.index > lastIndex) {
      parts.push(content.substring(lastIndex, match.index));
    }

    const emoji = match[0];
    const emojiUrl = douyinEmojiMap[emoji];

    if (emojiUrl) {
      // 添加表情图片
      parts.push(
        <img
          key={`emoji-${match.index}`}
          src={emojiUrl}
          alt={emoji}
          className="mx-1 inline-block h-5 w-5 align-middle"
        />,
      );
    } else {
      // 如果没有找到对应的表情，保留原文本
      parts.push(emoji);
    }

    lastIndex = regex.lastIndex;
  }

  // 添加剩余的文本
  if (lastIndex < content.length) {
    parts.push(content.substring(lastIndex));
  }

  return parts;
}
