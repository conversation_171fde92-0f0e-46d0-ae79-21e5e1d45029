import { DateType } from '@/services/business';
import { DatePickerProps } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import duration from 'dayjs/plugin/duration';
import { XIAOPENG } from './constant';
dayjs.extend(duration);

export const disabledDate = (current: Dayjs) => {
  return current && current > dayjs().endOf('day');
};

// export const rangeTimeDisableMoreThanThreeMonths = (current: dayjs.Dayjs, dates: null[] | null) => {
//   if (!dates) {
//     return false; // 开始和结束日期都未选则所有日期都可选
//   }
//   // 如果优先选择开始日期，根据当前为日期表还是月份表，利用monent的diff计算可选日期和开始时间的差值，返回boolean类型
//   const tooLate = dates[0] && current.diff(dates[0], 'months') >= 3;
//   // 如果优先选择结束日期，同样根据当前为日期表还是月份表，利用monent的diff计算可选日期和开始时间的差值，返回boolean类型
//   // @ts-ignore
//   const tooEarly = dates[1] && dates[1] && dates[1].diff(current, 'month') >= 3;
//   return !!tooEarly || !!tooLate;
// };

// 激励活动限制创建时间不超过3个月
export const disableDateLimitThreeMouth: DatePickerProps['disabledDate'] = (current, { from }) => {
  if (from) {
    return Math.abs(current.diff(from, 'month')) >= 3;
  }
  return false;
};

export const getTimes = (recentDay: number, format = 'YYYY-MM-DD') => {
  const today = dayjs();
  const yesterday = today.subtract(1, 'day');
  const recentDayAgo = recentDay === 1 ? yesterday : yesterday.subtract(recentDay, 'day');
  const startTime = recentDayAgo.startOf('day').format(format);
  const endTime = yesterday.format(format);
  return [startTime, endTime];
};

export const formatSecond = (second: any, nullText = '--') => {
  if (second == '0.0' || !second) {
    return nullText;
  }
  const days = Math.floor(second / (24 * 3600));
  const hours = Math.floor((second % (24 * 3600)) / 3600);
  const minutes = Math.floor((second % 3600) / 60);
  const seconds = Math.round(second % 60);

  let formattedTime = '';

  if (days > 0) {
    formattedTime += `${days}天`;
    if (hours === 0 && minutes === 0 && seconds === 0) {
      return formattedTime.trim();
    }
  }

  if (hours > 0) {
    formattedTime += `${hours}小时`;
  }

  if (minutes > 0) {
    formattedTime += `${minutes}分`;
  }

  if (seconds > 0 || (days === 0 && hours === 0 && minutes === 0)) {
    formattedTime += `${seconds}秒`;
  }

  return formattedTime.trim();
};

export const getTimeByType = (type: string) => {
  const today = dayjs();
  let startTime, endTime;
  if (type === 'day') {
    startTime = today.subtract(1, 'day').startOf('day');
    endTime = today.subtract(1, 'day').endOf('day');
  } else if (type === 'week') {
    if (today.day() === 1) {
      startTime = today.subtract(1, 'week').startOf('week');
      endTime = today.subtract(1, 'week').endOf('week');
    } else {
      startTime = today.startOf('week');
      endTime = today.subtract(1, 'day');
    }
  } else if (type === 'month') {
    if (today.date() === 1) {
      startTime = today.subtract(1, 'month').startOf('month');
      endTime = today.subtract(1, 'month').endOf('month');
    } else {
      startTime = today.startOf('month');
      endTime = today.subtract(1, 'day');
    }
  } else if (type === 'today') {
    startTime = today.startOf('day');
    endTime = today.endOf('day');
  }

  if (startTime && endTime) {
    return [startTime.format('YYYY-MM-DD'), endTime.format('YYYY-MM-DD')];
  } else {
    return [];
  }
};

export const getTimeByTypeSimple = (type: number) => {
  const today = dayjs();
  const startTime = today.subtract(type, 'day').startOf('day');
  const endTime = today.subtract(1, 'day').endOf('day');
  return [startTime.format('YYYY-MM-DD'), endTime.format('YYYY-MM-DD')];
};

export const getDateType = (stringDateType?: 'day' | 'week' | 'month' | string | null) => {
  switch (stringDateType) {
    case 'day':
      return 1;
    case 'week':
      return 2;
    case 'month':
      return 3;
    case null:
      return 4;
    default:
      return 4;
  }
};

// 参数eg: [2024-03-19,2024-03-20]
export const renderXAxis = (recentDay: string[], formatString = 'YYYY-MM-DD') => {
  const dates = [];
  const startDate = dayjs(recentDay[0]);
  const endDate = dayjs(recentDay[1]);
  let currentDate = startDate;
  while (currentDate <= endDate) {
    const date = currentDate.format(formatString);
    dates.push(date);
    currentDate = currentDate.add(1, 'day');
  }
  return dates;
};

export const renderXAxisByDataType = (dataType: 'day' | 'week' | 'month' | null) => {
  const datas = [];
  switch (dataType) {
    case 'day': {
      const startDay = dayjs().subtract(7, 'day');
      const endDay = dayjs().subtract(1, 'day');
      for (
        let currentDate = startDay;
        currentDate <= endDay;
        currentDate = currentDate.add(1, 'day')
      ) {
        datas.push(currentDate.format('MM.DD'));
      }
      break;
    }
    case 'week': {
      let startWeek;
      let endWeek;
      // 每个周一特殊处理
      if (dayjs().day() === 1) {
        startWeek = dayjs().subtract(7, 'week');
        endWeek = dayjs().subtract(1, 'week');
      } else {
        startWeek = dayjs().subtract(6, 'week');
        endWeek = dayjs();
      }
      for (
        let currentWeek = startWeek;
        currentWeek.isBefore(endWeek) || currentWeek.isSame(endWeek, 'week');
        currentWeek = currentWeek.add(1, 'week')
      ) {
        datas.push(`第${currentWeek.week()}周`);
      }
      break;
    }
    case 'month': {
      let startMonth;
      let endMonth;
      if (dayjs().date() === 1) {
        startMonth = dayjs().subtract(1, 'month').subtract(6, 'month');
        endMonth = dayjs().subtract(1, 'month');
      } else {
        startMonth = dayjs().subtract(0, 'month').subtract(6, 'month');
        endMonth = dayjs().subtract(0, 'month');
      }
      for (
        let currentMonth = startMonth;
        currentMonth.isBefore(endMonth) || currentMonth.isSame(endMonth, 'month');
        currentMonth = currentMonth.add(1, 'month')
      ) {
        datas.push(currentMonth.format('YYYY年M月'));
      }
      break;
    }
  }
  return datas;
};

export const getTimeByTypeBusiness = (type: DateType, projectId?: string) => {
  const today = projectId === XIAOPENG ? dayjs().subtract(1, 'day') : dayjs();

  let startTime: string = '';
  const endTime: string = today.subtract(1, 'day').endOf('day').format('YYYY-MM-DD');

  switch (type) {
    case DateType.Today:
      return [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')];
    case DateType.Yesterday:
      startTime = today.subtract(1, 'day').startOf('day').format('YYYY-MM-DD');
      break;
    case DateType.LastWeek:
      startTime = today.subtract(7, 'day').startOf('day').format('YYYY-MM-DD');
      break;
    case DateType.LastMonth:
      startTime = today.subtract(30, 'day').startOf('day').format('YYYY-MM-DD');
      break;
    case DateType.Last90Days:
      startTime = today.subtract(90, 'day').startOf('day').format('YYYY-MM-DD');
      break;
    case DateType.NaturalWeek:
      if (today.day() === 1) {
        startTime = today.subtract(1, 'week').startOf('week').startOf('day').format('YYYY-MM-DD');
      } else {
        startTime = today.startOf('week').startOf('day').format('YYYY-MM-DD');
      }
      break;
    case DateType.NaturalMonth:
      if (today.date() === 1) {
        startTime = today.subtract(1, 'month').startOf('month').startOf('day').format('YYYY-MM-DD');
      } else {
        startTime = today.startOf('month').startOf('day').format('YYYY-MM-DD');
      }
      break;
    default:
      break;
  }

  return startTime ? [startTime, endTime] : [];
};

export function convertTimeStr(duration: number, withMilliseconds?: boolean): string {
  const milliseconds = Math.floor((duration * 100.0) % 100);
  const hour = Math.floor(duration / 3600);
  const minute = Math.floor((duration % 3600) / 60);
  const second = Math.floor(duration % 60);

  return `${hour < 10 ? '0' + hour : hour}:${minute < 10 ? '0' + minute : minute}:${
    second < 10 ? '0' + second : second
  }${withMilliseconds ? `.${milliseconds < 10 ? '0' + milliseconds : milliseconds}` : ''}`;
}

export function getDurationFromTimeStr(timeStr: string): number {
  let duration = 0;
  if (!timeStr) {
    return duration;
  }
  const str = timeStr;
  if (str.includes('.')) {
    duration += +str.substring(str.indexOf('.'));
  }
  const parts = str.split(':').reverse();
  if (parts[0] && +parts[0]) {
    duration += +parts[0];
  }
  if (parts[1] && +parts[1]) {
    duration += 60 * +parts[1];
  }
  if (parts[2] && +parts[2]) {
    duration += 3600 * +parts[2];
  }
  return duration;
}

// 秒转分钟
export const secondToHours = (second?: number) => {
  if (!second) return '0';
  const duration = dayjs.duration(second, 'seconds');
  return duration.asHours().toFixed(2);
};
