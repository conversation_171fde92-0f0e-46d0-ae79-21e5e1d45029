import IconDouYin from '@/assets/platformIcon/douyin.png';
import IconWXVideo from '@/assets/platformIcon/wxvideo.png';
import IconXiaoHongShu from '@/assets/platformIcon/xiaohongshu.png';
import IconBilibili from '@/assets/platformIcon/bilibili.png';
import IconKuaiShou from '@/assets/platformIcon/kuaishou.png';
import IconWeibo from '@/assets/platformIcon/weibo.png';
import IconYiChe from '@/assets/platformIcon/yiche.png';
import IconQiCheZhiJia from '@/assets/platformIcon/qichezhijia.png';
import IconDongCheDi from '@/assets/platformIcon/dongchedi.png';
import IconWXPublic from '@/assets/platformIcon/wxpublic.png';
// import IconTikTok from '@/assets/platformIcon/tiktok.png';

export enum PlatForm {
  ALL = 0,
  Douyin = 1,
  KuaiShou = 2,
  DongCheDi = 3,
  WXVideo = 4,
  // TikTok = 5,
  <PERSON><PERSON><PERSON><PERSON><PERSON> = 6,
  <PERSON>ilibili = 7,
  Weibo = 8,
  <PERSON><PERSON><PERSON> = 9,
  QiCheZhiJia = 10,
  WXPublic = 11,
}

export const AccountPlatformEnum: Partial<Record<PlatForm, { text: string }>> = {
  [PlatForm.ALL]: {
    text: '全部',
  },
  [PlatForm.Douyin]: {
    text: '抖音',
  },
  [PlatForm.KuaiShou]: {
    text: '快手',
  },
  [PlatForm.DongCheDi]: {
    text: '懂车帝',
  },
  [PlatForm.WXVideo]: {
    text: '视频号',
  },
  [PlatForm.XiaoHongShu]: {
    text: '小红书',
  },
  [PlatForm.Bilibili]: {
    text: 'B 站',
  },
  [PlatForm.Weibo]: {
    text: '微博',
  },
  [PlatForm.YiChe]: {
    text: '易车',
  },
  [PlatForm.QiCheZhiJia]: {
    text: '汽车之家',
  },
  [PlatForm.WXPublic]: {
    text: '微信公众号',
  },
};

export const AccountPlatformKeysEnum: Record<string, PlatForm> = {
  douyin: PlatForm.Douyin,
  kuaishou: PlatForm.KuaiShou,
  weixin: PlatForm.WXVideo,
  xiaohongshu: PlatForm.XiaoHongShu,
  bilibili: PlatForm.Bilibili,
  weibo: PlatForm.Weibo,
  yiche: PlatForm.YiChe,
  qichezhijia: PlatForm.QiCheZhiJia,
  // tiktok: PlatForm.TikTok,
  wxpublic: PlatForm.WXPublic,
};

export const AccountPlatformKeysMap: Record<number, string> = {
  [PlatForm.Douyin]: 'douyin',
  [PlatForm.KuaiShou]: 'kuaishou',
  [PlatForm.WXVideo]: 'weixin',
  [PlatForm.XiaoHongShu]: 'xiaohongshu',
  [PlatForm.Bilibili]: 'bilibili',
  [PlatForm.Weibo]: 'weibo',
  [PlatForm.YiChe]: 'yiche',
  [PlatForm.QiCheZhiJia]: 'qichezhijia',
  [PlatForm.WXPublic]: 'wxpublic',
};

export const LiveRecordSupportingPlatforms = [PlatForm.Douyin, PlatForm.WXVideo];

export const TextToPlatForm: Record<string, PlatForm> = {
  全部: PlatForm.ALL,
  抖音: PlatForm.Douyin,
  快手: PlatForm.KuaiShou,
  懂车帝: PlatForm.DongCheDi,
  视频号: PlatForm.WXVideo,
  小红书: PlatForm.XiaoHongShu,
  B站: PlatForm.Bilibili,
  微博: PlatForm.Weibo,
  易车: PlatForm.YiChe,
  汽车之家: PlatForm.QiCheZhiJia,
  微信公众号: PlatForm.WXPublic,
  // TikTok: PlatForm.TikTok,
};

export const PlatformSelectOptions = [
  { value: PlatForm.ALL, label: '全平台' },
  { value: PlatForm.Douyin, label: '抖音' },
  { value: PlatForm.XiaoHongShu, label: '小红书' },
  { value: PlatForm.KuaiShou, label: '快手' },
  { value: PlatForm.WXVideo, label: '视频号' },
  { value: PlatForm.Bilibili, label: 'B 站' },
  { value: PlatForm.DongCheDi, label: '懂车帝' },
  { value: PlatForm.Weibo, label: '微博' },
  { value: PlatForm.YiChe, label: '易车' },
  { value: PlatForm.QiCheZhiJia, label: '汽车之家' },
  { value: PlatForm.WXPublic, label: '微信公众号' },
  // { value: PlatForm.TikTok, label: 'TikTok' },
];

export const PLATFORM_LIST = [
  { label: '抖音', value: PlatForm.Douyin, icon: IconDouYin },
  { label: '视频号', value: PlatForm.WXVideo, icon: IconWXVideo },
  { label: '小红书', value: PlatForm.XiaoHongShu, icon: IconXiaoHongShu },
  { label: 'Bilibili', value: PlatForm.Bilibili, icon: IconBilibili },
  { label: '快手', value: PlatForm.KuaiShou, icon: IconKuaiShou },
  { label: '微博', value: PlatForm.Weibo, icon: IconWeibo },
  { label: '懂车帝', value: PlatForm.DongCheDi, icon: IconDongCheDi },
  { label: '易车', value: PlatForm.YiChe, icon: IconYiChe },
  { label: '汽车之家', value: PlatForm.QiCheZhiJia, icon: IconQiCheZhiJia },
  { label: '微信公众号', value: PlatForm.WXPublic, icon: IconWXPublic },
  // { label: 'TikTok', value: PlatForm.TikTok, icon: IconTikTok },
];

export const PLATFORM_ICONS: Partial<Record<PlatForm, string>> = {
  [PlatForm.Douyin]: IconDouYin,
  [PlatForm.XiaoHongShu]: IconXiaoHongShu,
  [PlatForm.WXVideo]: IconWXVideo,
  [PlatForm.KuaiShou]: IconKuaiShou,
  [PlatForm.DongCheDi]: IconDongCheDi,
  // [PlatForm.TikTok]: IconTikTok,
  [PlatForm.Bilibili]: IconBilibili,
  [PlatForm.Weibo]: IconWeibo,
  [PlatForm.YiChe]: IconYiChe,
  [PlatForm.QiCheZhiJia]: IconQiCheZhiJia,
  [PlatForm.WXPublic]: IconWXPublic,
};

export const PLATFORM_NAMES: Partial<Record<PlatForm, string>> = {
  [PlatForm.Douyin]: '抖音',
  [PlatForm.XiaoHongShu]: '小红书',
  [PlatForm.WXVideo]: '视频号',
  [PlatForm.KuaiShou]: '快手',
  [PlatForm.DongCheDi]: '懂车帝',
  // [PlatForm.TikTok]: 'TikTok',
  [PlatForm.Bilibili]: 'B站',
  [PlatForm.Weibo]: '微博',
  [PlatForm.YiChe]: '易车',
  [PlatForm.QiCheZhiJia]: '汽车之家',
  [PlatForm.WXPublic]: '微信公众号',
};
