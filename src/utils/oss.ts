import { request } from '@umijs/max';
import COS from 'cos-js-sdk-v5';

const bucket = 'media-1307444343';
const region = 'ap-guangzhou';

function CosGetTempSecret() {
  return request('/new-media-api/cos/get_temp_secret', {
    method: 'POST',
  });
}

export const cos = new COS({
  getAuthorization: (options, callback) => {
    // 异步获取临时密钥
    CosGetTempSecret().then((res) => {
      if (res && res.code === 0) {
        const { data } = res;
        callback({
          TmpSecretId: data.tmpSecretId,
          TmpSecretKey: data.tmpSecretKey,
          XCosSecurityToken: data.sessionToken,
          StartTime: data.startTime,
          ExpiredTime: data.endTime,
        });
      }
    });
  },
});

export interface IBase64Image {
  key: string;
  base64Data: any;
}

export const uploadBase64Image = ({ key, base64Data }: IBase64Image): Promise<any> => {
  return new Promise((resolve, reject) => {
    cos.uploadFile(
      {
        Bucket: bucket,
        Region: region,
        Key: key,
        Body: base64Data /* 必须 */,
        SliceSize: 1024 * 1024 * 5 /* 触发分块上传的阈值，超过5MB使用分块上传，非必须 */,
        onTaskReady(taskId) {
          /* 非必须 */
          console.log(taskId);
        },
        onProgress(progressData) {
          /* 非必须 */
          console.log(JSON.stringify(progressData));
        },
        onFileFinish(err, data, options) {
          /* 非必须 */
          console.log(`${options.Key}上传${err ? '失败' : '完成'}`);
        },
      },
      function (err, data) {
        if (err) {
          return reject(err);
        }
        console.log(data);
        resolve(data);
      },
    );
  });
};

export const uploadFile = ({
  key,
  base64Data,
  onProgress,
  onFileFinish,
  onTaskReady,
}: IBase64Image & {
  onProgress?: (data: COS.ProgressInfo) => void;
  onFileFinish?: COS.onFileFinish;
  onTaskReady?: (TaskId: COS.TaskId) => void;
}): Promise<any> => {
  return new Promise((resolve, reject) => {
    cos.uploadFile(
      {
        Bucket: bucket,
        Region: region,
        Key: key,
        Body: base64Data /* 必须 */,
        SliceSize: 1024 * 1024 * 5 /* 触发分块上传的阈值，超过5MB使用分块上传，非必须 */,
        onTaskReady,
        onProgress,
        onFileFinish,
      },
      function (err, data) {
        if (err) {
          return reject(err);
        }
        console.log(data);
        resolve(data);
      },
    );
  });
};

type UploadImageParams = {
  file: File;
  key: string;
};

export const uploadImage = ({ file, key }: UploadImageParams): Promise<any> => {
  return new Promise((resolve, reject) => {
    cos.uploadFile(
      {
        Bucket: bucket,
        Region: region,
        Key: key,
        Body: file,
        SliceSize: 1024 * 1024 * 5,
      },
      function (err, data) {
        if (err) {
          return reject(err);
        }
        resolve(data);
      },
    );
  });
};
