import dayjs from 'dayjs';
import {
  disableDateLimitThreeMouth,
  getTimes,
  formatSecond,
  getTimeByType,
  getTimeByTypeSimple,
  getDateType,
  renderXAxis,
  renderXAxisByDataType,
  getTimeByTypeBusiness,
  convertTimeStr,
  getDurationFromTimeStr,
  secondToHours,
} from './time';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import { DateType } from '@/services/business';
import { XIAOPENG } from './constant';

dayjs.extend(utc);
dayjs.extend(timezone);

describe('disableDateLimitThreeMouth', () => {
  it('应该在from日期存在时禁用超过3个月的日期', () => {
    const from = dayjs('2023-01-01');
    const current = dayjs('2023-04-02');
    expect(disableDateLimitThreeMouth?.(current, { type: 'date', from })).toBe(true);
  });

  it('应该在from日期存在时允许3个月内的日期', () => {
    const from = dayjs('2023-01-01');
    const current = dayjs('2023-03-31');
    expect(disableDateLimitThreeMouth?.(current, { type: 'date', from })).toBe(false);
  });

  it('应该在from日期不存在时允许所有日期', () => {
    const current = dayjs('2023-04-02');
    expect(disableDateLimitThreeMouth?.(current, { type: 'date' })).toBe(false);
  });
});

describe('getTimes', () => {
  it('应返回最近一天=1的正确开始和结束时间', () => {
    const [startTime, endTime] = getTimes(1);
    const today = dayjs();
    const yesterday = today.subtract(1, 'day').format('YYYY-MM-DD');

    expect(startTime).toBe(yesterday);
    expect(endTime).toBe(yesterday);
  });

  it('应返回最近一天>1的正确开始和结束时间', () => {
    const recentDay = 3;
    const [startTime, endTime] = getTimes(recentDay);
    const today = dayjs();
    const yesterday = today.subtract(1, 'day');
    const recentDayAgo = yesterday.subtract(recentDay, 'day').format('YYYY-MM-DD');
    const expectedEndTime = yesterday.format('YYYY-MM-DD');

    expect(startTime).toBe(recentDayAgo);
    expect(endTime).toBe(expectedEndTime);
  });

  it('应处理自定义日期格式', () => {
    const recentDay = 2;
    const format = 'MM/DD/YYYY';
    const [startTime, endTime] = getTimes(recentDay, format);
    const today = dayjs();
    const yesterday = today.subtract(1, 'day');
    const recentDayAgo = yesterday.subtract(recentDay, 'day').format(format);
    const expectedEndTime = yesterday.format(format);

    expect(startTime).toBe(recentDayAgo);
    expect(endTime).toBe(expectedEndTime);
  });
});

describe('formatSecond', () => {
  it('应该返回正确格式化的时间字符串', () => {
    expect(formatSecond(3661)).toBe('1小时1分1秒');
    expect(formatSecond(86400)).toBe('1天');
    expect(formatSecond(3600)).toBe('1小时');
    expect(formatSecond(60)).toBe('1分');
    expect(formatSecond(30)).toBe('30秒');
  });

  it('应该处理边界情况', () => {
    expect(formatSecond(0)).toBe('--');
    expect(formatSecond('0.0')).toBe('--');
    expect(formatSecond(null)).toBe('--');
    expect(formatSecond(undefined)).toBe('--');
  });

  it('应该使用自定义的nullText', () => {
    expect(formatSecond(0, '无数据')).toBe('无数据');
  });
});

describe('getTimeByType', () => {
  beforeEach(() => {
    // 设置时区为中国时区
    dayjs.tz.setDefault('Asia/Shanghai');
    // 设置一周的开始为周一
    dayjs.locale('zh-cn');
    // 设置系统时间
    jest.useFakeTimers();
    jest.setSystemTime(new Date('2024-09-04T00:00:00Z'));
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  it('应该返回正确的昨天时间范围', () => {
    const result = getTimeByType('day');
    expect(result).toEqual(['2024-09-03', '2024-09-03']);
  });

  it('应该返回正确的本周时间范围', () => {
    const result = getTimeByType('week');
    expect(result).toEqual(['2024-09-02', '2024-09-03']);
  });

  it('应该返回正确的上周时间范围（当今天是周一时）', () => {
    jest.setSystemTime(new Date('2024-09-02T00:00:00Z')); // 设置为周一
    const result = getTimeByType('week');
    expect(result).toEqual(['2024-08-26', '2024-09-01']);
  });

  it('应该返回正确的本月时间范围', () => {
    const result = getTimeByType('month');
    expect(result).toEqual(['2024-09-01', '2024-09-03']);
  });

  it('应该返回正确的上月时间范围（当今天是1号时）', () => {
    jest.setSystemTime(new Date('2024-09-01T00:00:00Z')); // 设置为1号
    const result = getTimeByType('month');
    expect(result).toEqual(['2024-08-01', '2024-08-31']);
  });

  it('应该返回正确的今天时间范围', () => {
    const result = getTimeByType('today');
    expect(result).toEqual(['2024-09-04', '2024-09-04']);
  });

  it('应该返回空数组当类型不匹配时', () => {
    const result = getTimeByType('invalid');
    expect(result).toEqual([]);
  });
});

describe('getTimeByTypeSimple', () => {
  beforeEach(() => {
    jest.useFakeTimers();
    jest.setSystemTime(new Date('2024-09-04T00:00:00Z'));
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  it('应该返回正确的最近1天时间范围', () => {
    const result = getTimeByTypeSimple(1);
    expect(result).toEqual(['2024-09-03', '2024-09-03']);
  });

  it('应该返回正确的最近7天时间范围', () => {
    const result = getTimeByTypeSimple(7);
    expect(result).toEqual(['2024-08-28', '2024-09-03']);
  });

  it('应该返回正确的最近30天时间范围', () => {
    const result = getTimeByTypeSimple(30);
    expect(result).toEqual(['2024-08-05', '2024-09-03']);
  });

  it('应该处理跨月份的情况', () => {
    jest.setSystemTime(new Date('2024-10-01T00:00:00Z'));
    const result = getTimeByTypeSimple(7);
    expect(result).toEqual(['2024-09-24', '2024-09-30']);
  });

  it('应该处理跨年份的情况', () => {
    jest.setSystemTime(new Date('2025-01-01T00:00:00Z'));
    const result = getTimeByTypeSimple(7);
    expect(result).toEqual(['2024-12-25', '2024-12-31']);
  });
});

describe('getDateType', () => {
  it('应该返回正确的日期类型数字', () => {
    expect(getDateType('day')).toBe(1);
    expect(getDateType('week')).toBe(2);
    expect(getDateType('month')).toBe(3);
    expect(getDateType(null)).toBe(4);
  });

  it('应该对未知类型返回默认值4', () => {
    expect(getDateType('unknown')).toBe(4);
    expect(getDateType('')).toBe(4);
    expect(getDateType(undefined)).toBe(4);
  });
});

describe('renderXAxis', () => {
  it('应该正确渲染连续日期范围', () => {
    const result = renderXAxis(['2024-03-19', '2024-03-21']);
    expect(result).toEqual(['2024-03-19', '2024-03-20', '2024-03-21']);
  });

  it('应该处理单日期范围', () => {
    const result = renderXAxis(['2024-03-19', '2024-03-19']);
    expect(result).toEqual(['2024-03-19']);
  });

  it('应该使用自定义格式字符串', () => {
    const result = renderXAxis(['2024-03-19', '2024-03-21'], 'MM/DD');
    expect(result).toEqual(['03/19', '03/20', '03/21']);
  });

  it('应该处理跨月份的日期范围', () => {
    const result = renderXAxis(['2024-03-30', '2024-04-02']);
    expect(result).toEqual(['2024-03-30', '2024-03-31', '2024-04-01', '2024-04-02']);
  });

  it('应该处理跨年份的日期范围', () => {
    const result = renderXAxis(['2024-12-30', '2025-01-02']);
    expect(result).toEqual(['2024-12-30', '2024-12-31', '2025-01-01', '2025-01-02']);
  });
});

describe('renderXAxisByDataType', () => {
  beforeEach(() => {
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  it('应该正确渲染日期类型为day的X轴', () => {
    jest.setSystemTime(new Date('2024-03-20'));
    const result = renderXAxisByDataType('day');
    expect(result).toEqual(['03.13', '03.14', '03.15', '03.16', '03.17', '03.18', '03.19']);
  });

  it('应该正确渲染日期类型为week的X轴', () => {
    jest.setSystemTime(new Date('2024-03-20'));
    const result = renderXAxisByDataType('week');
    expect(result).toEqual(['第6周', '第7周', '第8周', '第9周', '第10周', '第11周', '第12周']);
  });

  it('应该正确处理周一的特殊情况', () => {
    jest.setSystemTime(new Date('2024-03-18'));
    const result = renderXAxisByDataType('week');
    expect(result).toEqual(['第5周', '第6周', '第7周', '第8周', '第9周', '第10周', '第11周']);
  });

  it('应该正确渲染日期类型为month的X轴', () => {
    jest.setSystemTime(new Date('2024-03-20'));
    const result = renderXAxisByDataType('month');
    expect(result).toEqual([
      '2023年9月',
      '2023年10月',
      '2023年11月',
      '2023年12月',
      '2024年1月',
      '2024年2月',
      '2024年3月',
    ]);
  });

  it('应该正确处理月初的特殊情况', () => {
    jest.setSystemTime(new Date('2024-03-01'));
    const result = renderXAxisByDataType('month');
    expect(result).toEqual([
      '2023年8月',
      '2023年9月',
      '2023年10月',
      '2023年11月',
      '2023年12月',
      '2024年1月',
      '2024年2月',
    ]);
  });

  it('应该返回空数组当日期类型为null', () => {
    const result = renderXAxisByDataType(null);
    expect(result).toEqual([]);
  });
});

describe('getTimeByTypeBusiness', () => {
  beforeEach(() => {
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  it('应该正确处理Yesterday类型', () => {
    jest.setSystemTime(new Date('2024-03-20'));
    const result = getTimeByTypeBusiness(DateType.Yesterday);
    expect(result).toEqual(['2024-03-19', '2024-03-19']);
  });

  it('应该正确处理LastWeek类型', () => {
    jest.setSystemTime(new Date('2024-03-20'));
    const result = getTimeByTypeBusiness(DateType.LastWeek);
    expect(result).toEqual(['2024-03-13', '2024-03-19']);
  });

  it('应该正确处理LastMonth类型', () => {
    jest.setSystemTime(new Date('2024-03-20'));
    const result = getTimeByTypeBusiness(DateType.LastMonth);
    expect(result).toEqual(['2024-02-19', '2024-03-19']);
  });

  it('应该正确处理Last90Days类型', () => {
    jest.setSystemTime(new Date('2024-03-20'));
    const result = getTimeByTypeBusiness(DateType.Last90Days);
    expect(result).toEqual(['2023-12-21', '2024-03-19']);
  });

  it('应该正确处理NaturalWeek类型', () => {
    jest.setSystemTime(new Date('2024-03-20'));
    const result = getTimeByTypeBusiness(DateType.NaturalWeek);
    expect(result).toEqual(['2024-03-18', '2024-03-19']);
  });

  it('应该正确处理NaturalMonth类型', () => {
    jest.setSystemTime(new Date('2024-03-20'));
    const result = getTimeByTypeBusiness(DateType.NaturalMonth);
    expect(result).toEqual(['2024-03-01', '2024-03-19']);
  });

  it('应该正确处理小鹏项目的特殊情况', () => {
    jest.setSystemTime(new Date('2024-03-20'));
    const result = getTimeByTypeBusiness(DateType.Yesterday, XIAOPENG);
    expect(result).toEqual(['2024-03-18', '2024-03-18']);
  });

  it('应该返回空数组当类型无效时', () => {
    const result = getTimeByTypeBusiness('InvalidType' as any);
    expect(result).toEqual([]);
  });
});

describe('convertTimeStr', () => {
  it('应该正确转换时间字符串（不包含毫秒）', () => {
    expect(convertTimeStr(3661)).toBe('01:01:01');
    expect(convertTimeStr(7200)).toBe('02:00:00');
    expect(convertTimeStr(45296)).toBe('12:34:56');
  });

  it('应该正确转换时间字符串（包含毫秒）', () => {
    expect(convertTimeStr(3661.75, true)).toBe('01:01:01.75');
    expect(convertTimeStr(7200.09, true)).toBe('02:00:00.09');
    expect(convertTimeStr(45296.5, true)).toBe('12:34:56.50');
  });

  it('应该正确处理零值', () => {
    expect(convertTimeStr(0)).toBe('00:00:00');
    expect(convertTimeStr(0, true)).toBe('00:00:00.00');
  });

  it('应该正确处理大于24小时的时间', () => {
    expect(convertTimeStr(90000)).toBe('25:00:00');
    expect(convertTimeStr(90000.5, true)).toBe('25:00:00.50');
  });
});

describe('getDurationFromTimeStr', () => {
  it('应该正确解析包含小时、分钟和秒的时间字符串', () => {
    expect(getDurationFromTimeStr('01:30:45')).toBe(5445);
  });

  it('应该正确解析包含分钟和秒的时间字符串', () => {
    expect(getDurationFromTimeStr('05:30')).toBe(330);
  });

  it('应该正确解析只包含秒的时间字符串', () => {
    expect(getDurationFromTimeStr('45')).toBe(45);
  });

  it('应该返回0当输入为空字符串时', () => {
    expect(getDurationFromTimeStr('')).toBe(0);
  });

  it('应该正确处理非标准格式的时间字符串', () => {
    expect(getDurationFromTimeStr('1:2:3')).toBe(3723);
  });
});

describe('secondToHours', () => {
  it('应正确将秒数转换为小时', () => {
    // 测试 3600 秒（1 小时）
    expect(secondToHours(3600)).toBe('1.00');
  });

  it('如果没有传入秒数，应返回 0', () => {
    // 测试没有传递任何参数
    expect(secondToHours()).toBe('0');
  });

  it('应将非零秒数转换为小时，并保留两位小数', () => {
    // 测试 4500 秒（1小时 15分钟）
    expect(secondToHours(4500)).toBe('1.25');
  });

  it('应正确处理 1 秒的边界情况', () => {
    // 测试 1 秒（极小的时间）
    expect(secondToHours(1)).toBe('0.00');
  });

  it('应正确处理大于 1 天的秒数', () => {
    // 测试 100000 秒（大约 27.78 小时）
    expect(secondToHours(100000)).toBe('27.78');
  });
});
