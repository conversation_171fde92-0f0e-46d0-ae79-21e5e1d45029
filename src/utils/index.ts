import { douyinAuthPath, loginPath, registerPath } from '@/utils/const';
import { ActionType } from '@ant-design/pro-components';
import { SortOrder } from 'antd/lib/table/interface';
import { omit, pick } from 'lodash-es';

export interface ProTableRequestAdapterOptions {
  dataFieldName?: 'items' | 'records'; // 数据列表字段名
  totalFieldName?: string; // 数据总条数字段名
}
export const proTableRequestAdapter = async (
  params: any & {
    pageSize?: number;
    current?: number;
    keyword?: string;
  },
  sorter: Record<string, SortOrder>,
  filter: Record<string, React.ReactText[] | null>,
  requestFn: (arg: any) => any,
  refreshFingerFn?: (arg: any) => void,
  responseFn?: (res: { code: number; data: any; msg?: string }) => void,
  options?: ProTableRequestAdapterOptions, // 配置对象
  needExtraData?: boolean,
): Promise<any> => {
  // console.log({ params, sorter, filter });

  const formattedParams = {
    ...params,
    page: params.current,
    size: params.pageSize,
  };

  delete formattedParams.current;
  delete formattedParams.pageSize;

  if (sorter && Object.keys(sorter)) {
    Object.keys(sorter).forEach((key) => {
      formattedParams.orderBy = key;
      formattedParams.orderType = sorter[key] === 'ascend' ? 'asc' : 'desc';
    });
  }

  const resp = await requestFn(formattedParams);
  if (refreshFingerFn) {
    refreshFingerFn(Date.now());
  }
  if (responseFn) {
    responseFn(resp);
  }
  return {
    success: resp.code === 0,
    data: (options?.dataFieldName ? resp.data[options?.dataFieldName] : resp.data?.items) || [],
    total: options?.totalFieldName ? resp.data[options?.totalFieldName] : resp.data?.total,
    extraData: needExtraData ? resp.data : undefined,
  };
};

export const proTableRequestAdapterParamsAndData = async (
  params: any & {
    pageSize?: number;
    current?: number;
    keyword?: string;
  },
  sorter: Record<string, SortOrder>,
  requestFn: (params: any, data: any) => any,
  //需要放在params的参数 一般默认是把分页和排序放在params里面 可传参自定义
  paramsDefault = ['size', 'page', 'orderBy', 'orderType'],
): Promise<any> => {
  const formattedParams = {
    ...params,
    page: params.current,
    size: params.pageSize,
  };

  delete formattedParams.current;
  delete formattedParams.pageSize;

  if (sorter && Object.keys(sorter)) {
    Object.keys(sorter).forEach((key) => {
      formattedParams.orderBy = key;
      formattedParams.orderType = sorter[key] === 'ascend' ? 'asc' : 'desc';
    });
  }

  const filterParams = pick(formattedParams, paramsDefault);
  const filterBody = omit(formattedParams, paramsDefault);

  const resp = await requestFn(filterParams, filterBody);

  return {
    success: resp.code === 0,
    data: resp.data?.items,
    total: resp.data?.total,
  };
};

// 检查当前是否是免登录页面
export function checkFreePage(): boolean {
  if (window) {
    const pathname = window.location.pathname;
    return pathname === douyinAuthPath || pathname === loginPath || pathname === registerPath;
  }

  return false;
}

// 删除当前页最后一项时候跳回前一页
export const reloadTable = (
  tableRef: React.MutableRefObject<ActionType | undefined>,
  deleteNum: number,
) => {
  const cur = tableRef?.current;
  if (!cur || !cur.pageInfo) {
    return;
  }
  const {
    pageInfo: { current, total, pageSize },
  } = cur;
  // 总页数
  const pageNum = Math.ceil(total / pageSize);
  // 最后一页任务数
  const taskNum = total - (pageNum - 1) * pageSize;
  // 删除的是最后一页的任务，并且全部删除
  const lastPageIsEmpey = current === pageNum && deleteNum === taskNum && current !== 1;
  if (lastPageIsEmpey) {
    // 重置为上一页的页码并刷新
    cur.setPageInfo!({
      ...cur.pageInfo,
      current: current - 1,
      total: total - deleteNum,
    });
    cur.reload();
  } else {
    cur.reload();
  }
};

// 截取饼状图数据 最多展现11个其余归为其他
export const slicePieData = (
  data?: {
    count?: number;
    name?: string;
  }[],
  sliceNum = 11,
) => {
  if (!data) {
    return;
  }
  const sortedData = data.sort((a, b) => (b.count || 0) - (a.count || 0));
  const topItems = sortedData.slice(0, sliceNum);
  const otherCount = sortedData
    .slice(sliceNum)
    .reduce((sum, item) => sum + (Number(item.count) || 0), 0);
  const result = topItems;
  if (otherCount > 0) {
    result.push({ name: '其他', count: otherCount });
  }
  const res = result.map((item) => ({
    value: item.count,
    name: item.name,
  }));
  return res;
};
