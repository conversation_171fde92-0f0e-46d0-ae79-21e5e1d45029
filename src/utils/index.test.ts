import { reloadTable, slicePieData } from '.';

describe('reloadTable', () => {
  it('应该在删除最后一页所有项时跳转到上一页', () => {
    const mockSetPageInfo = jest.fn();
    const mockReload = jest.fn();
    const mockTableRef = {
      current: {
        pageInfo: {
          current: 3,
          total: 25,
          pageSize: 10,
        },
        setPageInfo: mockSetPageInfo,
        reload: mockReload,
      },
    };

    reloadTable(mockTableRef as any, 5);

    expect(mockSetPageInfo).toHaveBeenCalledWith({
      current: 2,
      total: 20,
      pageSize: 10,
    });
    expect(mockReload).toHaveBeenCalled();
  });

  it('不应该在非最后一页或删除部分项时改变页码', () => {
    const mockReload = jest.fn();
    const mockTableRef = {
      current: {
        pageInfo: {
          current: 2,
          total: 25,
          pageSize: 10,
        },
        reload: mockReload,
      },
    };

    reloadTable(mockTableRef as any, 3);

    expect(mockReload).toHaveBeenCalled();
  });

  it('当tableRef为空时不应执行任何操作', () => {
    const mockTableRef = { current: undefined };
    expect(() => reloadTable(mockTableRef as any, 1)).not.toThrow();
  });
});

describe('slicePieData', () => {
  it('应该正确处理空数据', () => {
    expect(slicePieData()).toBeUndefined();
  });

  it('应该正确处理少于 sliceNum 的数据', () => {
    const data = [
      { name: 'A', count: 10 },
      { name: 'B', count: 5 },
    ];
    const result = slicePieData(data);
    expect(result).toEqual([
      { name: 'A', value: 10 },
      { name: 'B', value: 5 },
    ]);
  });

  it('应该正确处理超过 sliceNum 的数据', () => {
    const data = [
      { name: 'A', count: 10 },
      { name: 'B', count: 8 },
      { name: 'C', count: 6 },
      { name: 'D', count: 4 },
      { name: 'E', count: 2 },
    ];
    const result = slicePieData(data, 3);
    expect(result).toEqual([
      { name: 'A', value: 10 },
      { name: 'B', value: 8 },
      { name: 'C', value: 6 },
      { name: '其他', value: 6 },
    ]);
  });

  it('应该正确处理包含 undefined 值的数据', () => {
    const data = [
      { name: 'A', count: 10 },
      { name: 'B', count: undefined },
      { name: 'C', count: 5 },
    ];
    const result = slicePieData(data);
    expect(result).toEqual([
      { name: 'A', value: 10 },
      { name: 'C', value: 5 },
      { name: 'B', value: undefined },
    ]);
  });
});
