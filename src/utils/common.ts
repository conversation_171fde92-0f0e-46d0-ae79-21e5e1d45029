import { GetAllProject, ProjectDataItem } from '@/services/project';
import { RuleObject } from 'antd/es/form';
import { StoreValue } from 'antd/es/form/interface';
import dayjs from 'dayjs';
import saveAs from 'file-saver';
import { isNull, omit } from 'lodash-es';
import { projectIdMap } from './const';

/**
 * @param params 表单的params
 * @param filedArr 存储自定义维度的数组
 * @returns
 */
export const transferParams = (params: any, filedArr: string[]) => {
  const fieldList = [];
  for (const key in params) {
    if (filedArr.includes(key)) {
      fieldList.push({
        fieldId: key,
        value: params[key],
      });
      delete params[key];
    }
  }
  return fieldList;
};

export const handleExport = async (exportFn: any, params: any) => {
  const omitPageParams = omit(params, ['pageSize', 'current']);
  const resBlob = await exportFn({
    ...omitPageParams,
  });
  saveAs(resBlob, `${dayjs().format('YYYYMMDDHHMM')}.xls`);
};

export const formatNum = (num: string | null | number = 0) => {
  if (isNull(num) || num === '') {
    return '--';
  }
  const absoluteNum = Math.abs(Number(num)); // 取绝对值
  if (absoluteNum > 10000) {
    const formattedNum = (absoluteNum / 10000).toFixed(2) + 'w';
    return Number(num) < 0 ? '-' + formattedNum : formattedNum; // 如果原始数值为负数，则添加负号
  } else {
    return num;
  }
};

export const STICKY_OFFSETHEADER = 64;

export enum BizType {
  ORDINARY = 0,
  FINALLYHIERACHY = 1, // 层级最后的叶子节点
  HIERACHY = 2,
}

export const initDataFilter = {
  videoFilter: false,
  liveFilter: false,
  postPlayCount: null,
  postDuration: undefined,
  liveDuration: null,
  postDurationFilter: false,
};

export enum ClassifyType {
  Account = 1,
  Team = 2,
}

const parseProjectListToResult = (projectList?: ProjectDataItem[]): { [key: string]: string } => {
  if (!projectList) {
    return {};
  }
  return projectList.reduce<{ [key: string]: string }>((acc, { projectKey, id }) => {
    acc[projectKey] = id;
    return acc;
  }, {});
};

export const fetchProjectInfo = async () => {
  try {
    // 直接从服务器获取
    const res = await GetAllProject();
    if (res.code === 0) {
      const projectData = res.data;
      localStorage.setItem('projectList', JSON.stringify(projectData));
      return parseProjectListToResult(projectData);
    } else {
      return projectIdMap;
    }
  } catch (error) {
    console.error(error);
    // 出错时返回兜底数据
    return projectIdMap;
  }
};

export const checkChineseComma = (_: RuleObject, value: StoreValue) => {
  if (value && value.includes('，')) {
    return Promise.reject(new Error('请不要输入中文逗号'));
  }
  return Promise.resolve();
};

export const isTestEnvironment = window.location.host.includes('dev');
