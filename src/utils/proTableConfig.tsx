import { ActionType, BaseQueryFilterProps } from '@ant-design/pro-components';
import { Button, TablePaginationConfig } from 'antd';
import { SettingOutlined } from '@ant-design/icons';
import { customPaginationRender } from '@/pages/ProjectHome/style';

export const proTableSearchConfig: BaseQueryFilterProps = {
  className: 'custom-table-search',
  labelWidth: 'auto',
  span: {
    xs: 24,
    sm: 12,
    md: 8,
    lg: 8,
    xl: 8,
    xxl: 4,
  },
  searchGutter: 10,
  defaultCollapsed: true,
};

export type OptionsType =
  | ((e: React.MouseEvent<HTMLSpanElement>, action?: ActionType) => void)
  | boolean;

export type OptionConfig = {
  density?: boolean;
  fullScreen?: OptionsType;
  reload?: OptionsType;
  setting?: boolean | SettingOptionType;
  search?: boolean; //  (OptionSearchProps & { name?: string }) | boolean;
  reloadIcon?: React.ReactNode;
  densityIcon?: React.ReactNode;
};

export type SettingOptionType = {
  draggable?: boolean;
  checkable?: boolean;
  showListItemOption?: boolean;
  checkedReset?: boolean;
  listsHeight?: number;
  extra?: React.ReactNode;
  children?: React.ReactNode;
  settingIcon?: React.ReactNode;
};

export const proTableOptionsConfig: OptionConfig = {
  density: false,
  fullScreen: false,
  reload: false,
  setting: {
    settingIcon: (
      <Button>
        <SettingOutlined />
      </Button>
    ),
  },
};

export const proTablePaginationConfig: TablePaginationConfig = {
  defaultPageSize: 10,
  showTotal: (total, range) => `${range[0]}-${range[1]} 共 ${total} 条`,
  itemRender: customPaginationRender,
  showSizeChanger: true,
};
