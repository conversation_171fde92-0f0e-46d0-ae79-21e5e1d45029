import { RuleObject } from 'antd/es/form';
import { checkChineseComma, formatNum } from './common';

describe('checkChineseComma', () => {
  it('应该在输入包含中文逗号时拒绝', async () => {
    await expect(checkChineseComma({} as RuleObject, '测试，测试')).rejects.toThrow(
      '请不要输入中文逗号',
    );
  });

  it('应该在输入不包含中文逗号时通过', async () => {
    await expect(checkChineseComma({} as RuleObject, '测试,测试')).resolves.toBeUndefined();
  });

  it('应该在输入为空时通过', async () => {
    await expect(checkChineseComma({} as RuleObject, '')).resolves.toBeUndefined();
  });

  it('应该在输入为 undefined 时通过', async () => {
    await expect(checkChineseComma({} as RuleObject, undefined)).resolves.toBeUndefined();
  });
});

describe('formatNum', () => {
  it('应该正确格式化大于10000的正数', () => {
    expect(formatNum(15000)).toBe('1.50w');
  });

  it('应该正确格式化大于10000的负数', () => {
    expect(formatNum(-20000)).toBe('-2.00w');
  });

  it('应该正确处理小于等于10000的数字', () => {
    expect(formatNum(5000)).toBe(5000);
  });

  it('应该正确处理0', () => {
    expect(formatNum(0)).toBe(0);
  });

  it('应该正确处理字符串数字', () => {
    expect(formatNum('12345')).toBe('1.23w');
  });

  it('应该正确处理null值', () => {
    expect(formatNum(null)).toBe('--');
  });

  it('应该使用默认值0当没有提供参数时', () => {
    expect(formatNum()).toBe(0);
  });

  it('应该正确处理空字符串', () => {
    expect(formatNum('')).toBe('--');
  });
});
