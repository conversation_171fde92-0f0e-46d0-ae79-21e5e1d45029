import { concatURIComponent, fillParams, getUrlParams } from './url';

describe('getUrlParams', () => {
  const mockLocation = {
    search: '',
  };

  beforeEach(() => {
    Object.defineProperty(window, 'location', {
      value: mockLocation,
      writable: true,
    });
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  test('返回所有参数对象', () => {
    Object.defineProperty(window, 'location', {
      value: { search: '?foo=bar&baz=qux' },
    });
    expect(getUrlParams()).toEqual({
      foo: 'bar',
      baz: 'qux',
    });
  });

  test('返回指定参数的值', () => {
    Object.defineProperty(window, 'location', {
      value: { search: '?foo=bar&baz=qux' },
    });
    expect(getUrlParams('foo')).toBe('bar');
  });

  test('URL无参数时返回空对象或空字符串', () => {
    Object.defineProperty(window, 'location', {
      value: { search: '' },
    });
    expect(getUrlParams()).toEqual({});
    expect(getUrlParams('foo')).toBe('');
  });

  test('正确解码特殊字符', () => {
    Object.defineProperty(window, 'location', {
      value: { search: '?foo=hello%20world' },
    });
    expect(getUrlParams('foo')).toBe('hello world');
  });
});

describe('fillParams', () => {
  test('应该正确注入新参数', () => {
    const url = 'https://example.com?a=1&b=2';
    const params = { c: '3', d: '4' };
    const result = fillParams(params, url);
    expect(result).toBe('https://example.com?a=1&b=2&c=3&d=4');
  });

  test('应该覆盖现有参数', () => {
    const url = 'https://example.com?a=1&b=2';
    const params = { b: '3', c: '4' };
    const result = fillParams(params, url);
    expect(result).toBe('https://example.com?a=1&b=3&c=4');
  });

  test('应该正确处理带有哈希的URL', () => {
    const url = 'https://example.com?a=1#section';
    const params = { b: '2' };
    const result = fillParams(params, url);
    expect(result).toBe('https://example.com?a=1&b=2#section');
  });

  test('应该排除指定的参数', () => {
    const url = 'https://example.com?a=1&b=2&c=3';
    const params = { b: '4', d: '5' };
    const withouts = ['c'];
    const result = fillParams(params, url, withouts);
    expect(result).toBe('https://example.com?a=1&b=4&d=5');
  });

  test('应该处理空参数对象', () => {
    const url = 'https://example.com?a=1&b=2';
    const params = {};
    const result = fillParams(params, url);
    expect(result).toBe('https://example.com?a=1&b=2');
  });
});

describe('concatURIComponent', () => {
  test('应该正确连接并编码多个字符串', () => {
    const result = concatURIComponent(['hello', 'world', 'test']);
    expect(result).toBe('/hello/world/test');
  });

  test('应该正确处理包含特殊字符的字符串', () => {
    const result = concatURIComponent(['hello', 'world?', 'test&']);
    expect(result).toBe('/hello/world%3F/test%26');
  });

  test('应该正确处理数字', () => {
    const result = concatURIComponent(['page', 1, 'item', 2]);
    expect(result).toBe('/page/1/item/2');
  });

  test('应该正确处理undefined值', () => {
    const result = concatURIComponent(['hello', undefined, 'world']);
    expect(result).toBe('/hello//world');
  });

  test('应该返回空字符串当输入为空数组时', () => {
    const result = concatURIComponent([]);
    expect(result).toBe('');
  });

  test('应该正确处理包含空格的字符串', () => {
    const result = concatURIComponent(['hello world', 'test space']);
    expect(result).toBe('/hello%20world/test%20space');
  });

  test('应该正确处理包含Unicode字符的字符串', () => {
    const result = concatURIComponent(['你好', '世界']);
    expect(result).toBe('/%E4%BD%A0%E5%A5%BD/%E4%B8%96%E7%95%8C');
  });
});
