import { ProColumns } from '@ant-design/pro-components';

export const getSumColumnsWidth = (columns: ProColumns[]) => {
  const sumWidth = columns.reduce((acc, item) => {
    if (item.hidden) {
      return acc;
    }
    const width = item.width;
    let widthValue = 0;
    if (typeof width === 'string') {
      // 如果是字符串 提取数值部分
      widthValue = parseInt(width, 10);
    } else if (typeof width === 'number') {
      // 如果是数字，直接使用
      widthValue = width;
    }
    return acc + widthValue;
  }, 0);
  return sumWidth;
};

// export const noWrapColumns = (columns: ProColumns[]) => {
//   return columns.map((item) => {
//     const fun = () => ({ style: { whiteSpace: 'nowrap' } });
//     // 为列的 header 和 cell 设置不换行属性
//     item.onHeaderCell = fun;
//     item.onCell = fun;

//     return item;
//   });
// };
