import { SvgIcon } from '@/components/SvgIcon';
import { ProCard } from '@ant-design/pro-components';
import { InputNumber, Radio, Row, Tabs } from 'antd';
import { styled } from 'styled-components';
import Rank1 from '@/assets/rank1.png';
import Rank2 from '@/assets/rank2.png';
import Rank3 from '@/assets/rank3.png';

export const CustomDateRadioGroup = styled(Radio.Group)`
  & {
    .ant-radio-button-wrapper {
      border: none;
    }
  }
`;

export const CustomDateRadioButton = styled(Radio.Button)`
  margin-right: 5px;
  color: #000;
  font-weight: 700;
  font-size: 12px;
  font-family: Helvetica;
  border: none;
  border-radius: 2px;
  padding-inline: 5px;

  &.ant-radio-button-wrapper-checked {
    color: #000 !important;
    background: rgba(30, 94, 255, 0.2) !important;
    border: none !important;
    border-radius: 2px !important;
    padding-inline: 5px;
  }

  &::before {
    width: 0 !important;
  }
`;

export const CustomDateRadioButtonDark = styled(Radio.Button)`
  margin-right: 5px;
  color: #fff;
  background: none;
  font-weight: 700;
  font-size: 12px;
  font-family: Helvetica;
  border: none;
  border-radius: 2px;
  padding-inline: 5px;

  &.ant-radio-button-wrapper-checked {
    height: 22px;
    color: #fff !important;
    line-height: 22px;
    background: #232e4d !important;
    border: none !important;
    border-radius: 2px !important;
    padding-inline: 5px;
  }

  &::before {
    width: 0 !important;
  }
`;

export const CustomHeaderTabsDark = styled(Tabs)`
  .ant-tabs-nav {
    padding: 10px 10px 10px 20px;
    background: #1b1d22;
    border-radius: 6px;

    &::before {
      border: none;
    }
  }

  .ant-tabs-nav-list {
    div {
      color: #929397;
    }

    .ant-tabs-tab:hover {
      color: #fff;
    }

    .ant-tabs-tab-active {
      .ant-tabs-tab-btn[aria-selected='true'] {
        color: #fff;
        font-size: 14px;
        font-family: OPPOSans;
        text-shadow: none;
      }
    }
    .ant-tabs-ink-bar {
      background: #fff;
    }
  }

  .ant-tabs-content-holder {
    ::-webkit-scrollbar {
      display: none; /* Safari 和 Chrome */
    }
    -ms-overflow-style: none; /* IE 和 Edge */
    scrollbar-width: none; /* Firefox */
  }
`;

export const ProCardDark = styled(ProCard)`
  background-color: #1b1d22 !important;
  padding: 20px;

  .ant-pro-card-title {
    color: #fff;
  }

  .ant-pro-card:first-child {
    padding-left: 0;
  }
`;

export const ProCardDarkGhost = styled(ProCard)`
  background-color: #1b1d22 !important;

  .ant-pro-card-title {
    color: #fff;
  }
`;

export const ScrollSider = styled.div`
  width: 400px;
  height: 100%;
  min-width: 400px;
  overflow-y: auto;
  &::-webkit-scrollbar {
    display: none;
  }
`;

export const HideScrollBarDiv = styled.div`
  width: 100%;
  overflow-y: auto;
  &::-webkit-scrollbar {
    display: none; /* Safari 和 Chrome */
  }
  -ms-overflow-style: none; /* IE 和 Edge */
  scrollbar-width: none; /* Firefox */
`;

export const echartDefaultColor = [
  '#7D95FF',
  '#5FEAE3',
  '#48D585',
  '#F9D765',
  '#F88C5B',
  '#EF7682',
  '#F17FFA',
  '#9F75FE',
  '#CBD5FF',
  '#BFF7F4',
  '#B6EECE',
  '#FDEFC1',
  '#FCD1BD',
  '#F9C8CD',
  '#F9CCFD',
  '#D9C8FF',
];

export const CustomInputNumber = styled(InputNumber)`
  .ant-input-number-handler-wrap {
    opacity: 1;
  }
`;

export const ShowRightSiderBtn = styled.div<{
  $top: number;
}>`
  cursor: move;
  position: absolute;
  width: 56px;
  height: 32px;
  right: 10px;
  top: ${(props) => props.$top}px;
  background-color: #282c35;
  border-radius: 20px 0 0 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #3d4350;

  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;

  div {
    color: #ffffff;
  }
`;

export const TagSpan = styled.span<{
  $bgColor: string;
  $textColor: string;
}>`
  box-sizing: border-box;
  font-size: 12px;
  padding: 3px 5px;
  background: ${(props) => props.$bgColor};
  color: ${(props) => props.$textColor};
  border-radius: 4px;
  font-weight: 400;
  border-color: transparent;
`;

export const InfoCard = styled.div`
  min-width: 222px;
  height: 112px;
  border: 1px solid rgba(5, 5, 5, 0.06);
  border-radius: 6px;
  padding: 10px 20px;
  max-width: 300px;
  .title {
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 5px;
  }

  .number {
    font-size: 24px;
  }

  .rate {
    font-size: 12px;
    .rate-title {
      margin-inline-end: 6px;
    }
    .rate-number {
      color: rgba(42, 46, 54, 0.65);
    }
  }
`;

export const renderMom = (data?: string | number, text?: string) => {
  if (!data) return <></>;
  if (Number(data) === 0) {
    return (
      <span style={{ fontSize: '12px', color: 'rgba(0,0,0,60%)', lineHeight: '12px' }}>
        {data}%
      </span>
    );
  }
  if (Number(data) > 0) {
    return (
      <span style={{ fontSize: '12px', color: '#f54848', lineHeight: '12px' }}>
        {text && `${text}增加`}
        <SvgIcon icon={'local:outline/rate-rise'} y={2} />
        {data}%
      </span>
    );
  } else {
    return (
      <span style={{ fontSize: '12px', color: '#30b824', lineHeight: '12px' }}>
        {text && `${text}减少`}
        <SvgIcon icon={'local:outline/rate-decline'} y={2} />
        {data}%
      </span>
    );
  }
};

export const pieColor = [
  '#3c76f4',
  '#7e9ff3',
  '#502ae6',
  '#c89cf8',
  '#ce4481',
  '#f4c09f',
  '#f8e9a0',
  '#e0f09c',
  '#beeeba',
  '#86ddd7',
];

export const pieColor2 = [
  '#5EE0D8',
  '#73A0FA',
  '#E33283',
  '#D299FF',
  '#5721F0',
  '#AFF0B5',
  '#FFBE99',
  '#1C76FD',
  '#DCF190',
  '#FCE996',
];

export const lineColor = [
  '#3c76f4',
  '#7e9ff3',
  '#502ae6',
  '#c89cf8',
  '#ce4481',
  '#f4c09f',
  '#f8e9a0',
  '#e0f09c',
  '#beeeba',
  '#86ddd7',
  '#d6ba5f',
  '#6cd99a',
  '#b47c4b',
  '#cb6081',
  '#b4a3c5',
  '#9e8b45',
  '#5aa7d3',
  '#678f4b',
  '#7a5798',
  '#d09489',
];

export const TableRankTop = styled.div`
  width: 18px;
  height: 18px;
  text-align: center;
  color: #fff;
  margin: 0 auto;
  line-height: 18px;
  border-radius: 4px;
  font-weight: 500;
`;

export const RankBgMap: Record<number, React.JSX.Element> = {
  1: <img width={16} src={Rank1} />,
  2: <img width={16} src={Rank2} />,
  3: <img width={16} src={Rank3} />,
};

export const HideScrollBarRow = styled(Row)`
  width: 100%;
  overflow-x: auto;
  overflow-y: hidden;
  &::-webkit-scrollbar {
    display: none; /* Safari 和 Chrome */
  }
  -ms-overflow-style: none; /* IE 和 Edge */
  scrollbar-width: none; /* Firefox */

  & {
    .ant-col:first-child {
      padding-left: 0 !important;
    }

    .ant-col:last-child {
      padding-right: 0 !important;
    }
  }
`;

export const echartAreaStyleGradient = {
  color: {
    type: 'linear',
    x: 0,
    y: 0,
    x2: 0,
    y2: 1,
    colorStops: [
      {
        offset: 0,
        color: 'rgba(22, 119, 255, 0.2)',
      },
      {
        offset: 1,
        color: 'rgba(22, 119, 255, 0)',
      },
    ],
  },
};
