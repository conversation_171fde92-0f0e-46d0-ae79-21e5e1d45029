// 质检记录行为 记好多啊。。。
export type DetailInfo = {
  headerActiveKey: string;
  rightSiderActiveKey: string;
  tabKey: string;
  showHitState: boolean;
  toggleRightSiderShow: boolean;
  framePriorityShowContent: string[];
  subtitlesPriorityShowContent: string[];
  danmuPriorityShowContent: string[];
};

export const findLocalStorageValue = <T extends string | boolean | string[]>(
  localStorageName: 'liveDetailRecords' | 'postDetailRecords',
  paramName: keyof DetailInfo,
) => {
  const localStorageDetail = localStorage.getItem(localStorageName);
  if (localStorageDetail) {
    const detail: DetailInfo = JSON.parse(localStorageDetail);
    return detail[paramName] as T;
  }
};

export const updateLocalStorage = (
  localStorageName: 'liveDetailRecords' | 'postDetailRecords',
  detailUpdate: Partial<DetailInfo>,
) => {
  const localStorageDetail = localStorage.getItem(localStorageName);
  const detail: DetailInfo = localStorageDetail ? JSON.parse(localStorageDetail) : {};
  Object.assign(detail, detailUpdate);
  localStorage.setItem(localStorageName, JSON.stringify(detail));
};
