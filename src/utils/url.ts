import qs from 'qs';

// url上拼参数
export function paramsFilter(
  url: string,
  obj: { [prop: string]: string | number | undefined },
): string {
  let result = '';
  let item;
  if (url.includes('?')) {
    for (item in obj) {
      if (obj[item] !== undefined && String(obj[item])) {
        result += `&${item}=${obj[item]}`;
      }
    }
  } else {
    result += '?';
    for (item in obj) {
      if (obj[item] !== undefined && String(obj[item])) {
        result += `${item}=${obj[item]}&`;
      }
    }
  }
  return url + result;
}

/**
 * 获取query参数
 * @param  {string} name 需要获取的参数key值
 * @return url参数中对应name的值 或者所有参数-值的对象
 */
export const getUrlParams = function <
  T extends string | undefined,
  R = T extends string ? string : { [name: string]: string },
>(name?: T, paramSearch?: string): R {
  // 如果 paramSearch 为空，则使用 window.location.search
  const search = paramSearch || window.location.search;

  const index = search.indexOf('?');
  if (index === -1) {
    return typeof name !== 'undefined' ? ('' as R) : ({} as R);
  }
  const params = search.slice(index + 1);

  // 使用 qs.parse 解析查询参数，并忽略前缀 ?
  const queries = qs.parse(params, { ignoreQueryPrefix: true });

  // 返回指定的参数或所有参数
  return name ? (queries[name] as R) : (queries as R);
};

/**
 * 给url注入参数，注入的参数会覆盖旧参数
 * @param {Object} params 要注入的参数
 * @param {string} [url] 要注入参数的url，为空时取当前页面url
 * @param {string[]} [withouts] url中需要排除的参数key数组
 * @returns {string} 注入参数后的url
 */
export const fillParams = function (
  params: { [name: string]: string | undefined },
  urlParam?: string,
  withouts?: string[],
): string {
  const url = urlParam || window.location.href;

  const urlPairs = url.split('#'),
    fullUrl = urlPairs[0],
    hashUrl = (urlPairs.length > 1 && '#' + urlPairs[1]) || '',
    baseUrl = fullUrl.split('?')[0],
    originParams = getUrlParams(undefined, fullUrl),
    paramsList = [];
  let re = '';

  for (const key in originParams) {
    if (params[key] === undefined && (withouts || []).indexOf(key) === -1) {
      paramsList.push(key + '=' + originParams[key]);
    }
  }

  for (const key1 in params) {
    if ((withouts || []).indexOf(key1) === -1) {
      if (params[key1] !== undefined) {
        paramsList.push(key1 + '=' + params[key1]);
      }
    }
  }

  re += baseUrl;
  re += (paramsList.length && '?' + paramsList.join('&')) || '';
  re += hashUrl;

  return re;
};

export const concatURIComponent = (str: (string | number | undefined)[]) => {
  return str.reduce((prev, cur) => {
    return prev + `/${encodeURIComponent(cur || '')}`;
  }, '') as string;
};
