import { message } from 'antd';
import FileSaver from 'file-saver';

/**
 * openNewWindow 打开一个新页面，如果在客户端内，是打开一个新窗口，如果是在浏览器内，是打开一个新页面tab
 * @param url
 * @returns
 */
export const openNewWindow = async (url: string, target?: string, features?: string) => {
  // @ts-ignore
  if (!window.__TAURI__) {
    window.open(url, target, features);
    return;
  }

  // @ts-ignore 。。。
  const WebviewWindow = window.__TAURI__?.window?.WebviewWindow;
  if (!WebviewWindow) {
    return;
  }
  const webview = new WebviewWindow('tmp', {
    url,
  });

  webview.once('tauri://created', async () => {
    // webview window successfully created
    await webview.maximize();
  });
  webview.once('tauri://error', (e: any) => {
    console.error('tauri://error', e);
    // an error happened creating the webview window
  });
};

const getUniqueFileName = (fileName: string, files: { path: string; name: string }[]): string => {
  const existNames = files.map((i) => i.name) || [];
  const parts = fileName.split('.');
  const ext = parts.pop();
  const name = parts.join('.');

  let checkName = '';
  let idx = 0;
  // eslint-disable-next-line no-constant-condition
  while (true) {
    checkName = idx === 0 ? `${name}.${ext}` : `${name} (${idx}).${ext}`;
    if (!existNames.includes(checkName)) {
      break;
    }

    idx += 1;
    continue;
  }

  return checkName;
};

/**
 * saveFile 保存下载一个文件
 * @param param0
 * @returns
 */
export const saveFile = async (fileData: string | Blob, filename?: string) => {
  let fileName = filename;

  // @ts-ignore
  if (!window.__TAURI__) {
    // if (typeof fileData === 'string') {
    //   location.href = fileData;
    // } else {
    FileSaver.saveAs(fileData, fileName);
    // }
    return;
  }

  if (!fileName && typeof fileData === 'string') {
    // 使用url中的文件名
    fileName = fileData.split('?')[0].split('/').pop();
  }

  if (!fileName) {
    return;
  }

  // @ts-ignore
  const { path, fs } = window.__TAURI__;
  const { downloadDir } = path;
  const { writeBinaryFile } = fs;

  const hide = message.loading('下载中');
  const unqFileName = getUniqueFileName(fileName, await fs.readDir(await downloadDir()));
  const filePath = await path.resolve(await downloadDir(), unqFileName);

  let blob: Blob;
  if (typeof fileData === 'string') {
    blob = await fetch(fileData).then((res) => res.blob());
  } else {
    blob = fileData;
  }

  const buffer = await blob.arrayBuffer();
  const uint8 = new Uint8Array(buffer);
  await writeBinaryFile(filePath, uint8);
  hide();
  message.success(`下载成功 ${filePath}`);
};
