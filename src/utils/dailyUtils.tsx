import {
  accountQualityColumns,
  chatAccountColumns,
  chatTeamColumns,
  teamQualityColumns,
} from '@/components/columnsConfig';
import { ClassifyType } from './common';

export const renderColumns = (type: string, classifyType: ClassifyType, afkState?: boolean) => {
  if (type === 'quality') {
    if (classifyType === ClassifyType.Team) {
      return teamQualityColumns(afkState);
    } else {
      return accountQualityColumns(afkState);
    }
  } else {
    if (classifyType === ClassifyType.Team) {
      return chatTeamColumns;
    } else {
      return chatAccountColumns;
    }
  }
};

export const scrollAntdMobileCalendar = () => {
  setTimeout(() => {
    const el = document.querySelector('.adm-calendar-picker-view-cell-selected-begin');
    setTimeout(() => {
      el?.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }, 100);
  }, 100);
};
