import { DetailInfo, findLocalStorageValue, updateLocalStorage } from './quality';

describe('findLocalStorageValue', () => {
  beforeEach(() => {
    localStorage.clear();
  });

  it('应该返回正确的值当localStorage中存在数据时', () => {
    const mockData: DetailInfo = {
      headerActiveKey: 'test',
      rightSiderActiveKey: 'test',
      tabKey: 'test',
      showHitState: true,
      toggleRightSiderShow: false,
      framePriorityShowContent: ['test'],
      subtitlesPriorityShowContent: ['test'],
      danmuPriorityShowContent: ['test'],
    };
    localStorage.setItem('liveDetailRecords', JSON.stringify(mockData));

    const result = findLocalStorageValue<string>('liveDetailRecords', 'headerActiveKey');
    expect(result).toBe('test');
  });

  it('应该返回undefined当localStorage中不存在数据时', () => {
    const result = findLocalStorageValue<string>('postDetailRecords', 'tabKey');
    expect(result).toBeUndefined();
  });

  it('应该正确处理不同类型的返回值', () => {
    const mockData: DetailInfo = {
      headerActiveKey: 'test',
      rightSiderActiveKey: 'test',
      tabKey: 'test',
      showHitState: true,
      toggleRightSiderShow: false,
      framePriorityShowContent: ['test1', 'test2'],
      subtitlesPriorityShowContent: ['test'],
      danmuPriorityShowContent: ['test'],
    };
    localStorage.setItem('postDetailRecords', JSON.stringify(mockData));

    const stringResult = findLocalStorageValue<string>('postDetailRecords', 'headerActiveKey');
    expect(stringResult).toBe('test');

    const booleanResult = findLocalStorageValue<boolean>('postDetailRecords', 'showHitState');
    expect(booleanResult).toBe(true);

    const arrayResult = findLocalStorageValue<string[]>(
      'postDetailRecords',
      'framePriorityShowContent',
    );
    expect(arrayResult).toEqual(['test1', 'test2']);
  });
});

describe('updateLocalStorage', () => {
  beforeEach(() => {
    localStorage.clear();
  });

  it('应该正确更新localStorage中的数据', () => {
    const initialData: DetailInfo = {
      headerActiveKey: 'initial',
      rightSiderActiveKey: 'initial',
      tabKey: 'initial',
      showHitState: false,
      toggleRightSiderShow: false,
      framePriorityShowContent: [],
      subtitlesPriorityShowContent: [],
      danmuPriorityShowContent: [],
    };
    localStorage.setItem('liveDetailRecords', JSON.stringify(initialData));

    const updateData: Partial<DetailInfo> = {
      headerActiveKey: 'updated',
      showHitState: true,
    };

    updateLocalStorage('liveDetailRecords', updateData);

    const updatedData = JSON.parse(localStorage.getItem('liveDetailRecords') || '{}');
    expect(updatedData.headerActiveKey).toBe('updated');
    expect(updatedData.showHitState).toBe(true);
    expect(updatedData.rightSiderActiveKey).toBe('initial');
  });

  it('应该创建新的记录如果localStorage中不存在数据', () => {
    const newData: Partial<DetailInfo> = {
      headerActiveKey: 'new',
      showHitState: true,
    };

    updateLocalStorage('postDetailRecords', newData);

    const createdData = JSON.parse(localStorage.getItem('postDetailRecords') || '{}');
    expect(createdData.headerActiveKey).toBe('new');
    expect(createdData.showHitState).toBe(true);
  });
});
