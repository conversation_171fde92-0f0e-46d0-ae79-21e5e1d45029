import {
  AlarmClockCheck,
  CalendarDays,
  ChartNoAxesColumn,
  ChartSpline,
  House,
  MessageSquareText,
  MonitorDot,
  Ratio,
  Settings,
  Sparkles,
  UserCheck,
  UsersRound,
  Waypoints,
} from 'lucide-react';

// 侧边栏 icons 映射
export const ANTD_ICON_MAPS = {
  home: <House size={12} />,
  overview: <ChartNoAxesColumn size={12} />,
  team: <UsersRound size={12} />,
  setting: <Settings size={12} />,
  monitor: <MonitorDot size={12} />,
  clue: <Waypoints size={12} />,
  business: <Sparkles size={12} />,
  remind: <AlarmClockCheck size={12} />,
  intelligence: <ChartSpline size={12} />,
  distribute: <Ratio size={12} />,
  message: <MessageSquareText size={12} />,
  authorization: <UserCheck size={12} />,
  anchor: <CalendarDays size={12} />,
};

// 抖音授权中间页
export const douyinAuthPath = '/douyin-auth';
// 日报页
export const dailyPath = '/daily';
// 日报详情页
export const dailyDetailPath = '/daily/detail';
export const loginPath = '/login';
export const registerPath = '/register';

// 操作系统
export const OS = (function () {
  const ua = navigator.userAgent,
    isWindowsPhone = /(?:Windows Phone)/.test(ua),
    isSymbian = /(?:SymbianOS)/.test(ua) || isWindowsPhone,
    isAndroid = /(?:Android)/.test(ua),
    isFireFox = /(?:Firefox)/.test(ua),
    isChrome = /(?:Chrome|CriOS)/.test(ua),
    isTablet =
      /(?:iPad|PlayBook)/.test(ua) ||
      (isAndroid && !/(?:Mobile)/.test(ua)) ||
      (isFireFox && /(?:Tablet)/.test(ua)),
    isPhone = /(?:iPhone)/.test(ua) && !isTablet,
    isPc = !isPhone && !isAndroid && !isSymbian;
  return {
    isTablet: isTablet,
    isPhone: isPhone,
    isAndroid: isAndroid,
    isPc: isPc,
    isChrome: isChrome,
  };
})();

export const projectIdMap = {
  AGB386: '19',
  PB138: '22',
  CQTTYYDS: '50',
  amalive: '51',
  chery: '53',
  xiaopeng2: '54',
  AGR0229: '55',
  ACGQCZJ0311: '57',
  XPCZ0120: '58',
  whgh0402: '59',
  YQAD0613: '60',
  dfepai: '61',
};

export const enum IndustryType {
  OTHER = 0,
  CAR = 1,
  E_COMMERCE = 2,
}

export const INDUSTRY_TAG_COLOR_MAP: { [key in IndustryType]: string } = {
  [IndustryType.OTHER]: 'gold',
  [IndustryType.CAR]: '#1E5EFF',
  [IndustryType.E_COMMERCE]: 'red',
};
