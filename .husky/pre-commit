#!/bin/sh
. "$(dirname "$0")/_/husky.sh"

npx --no-install lint-staged

# 获取src/utils目录下被修改的文件
modified_files=$(git diff --cached --name-only -- src/utils | grep -v '\.test\.ts$' || true)

if [ -n "$modified_files" ]; then
    # 遍历被修改的文件
    for file in $modified_files; do
        # 构建测试文件路径
        test_file="${file%.*}.test.ts"

        # 检查测试文件是否存在
        if [ -f "$test_file" ]; then
            echo "检测到 $file 被修改，正在运行对应的单元测试..."
            npm run test "$test_file"

            if [ $? -ne 0 ]; then
                echo "单元测试失败，请修复错误后再提交。"
                exit 1
            fi
            echo "$file 的单元测试通过。"
        else
            echo "警告：$file 没有对应的测试文件。"
        fi
    done
    echo "所有修改的 src/utils 文件的单元测试都已通过。"
fi
