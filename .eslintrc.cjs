module.exports = {
  env: {
    browser: true,
    es2021: true,
    node: true,
  },
  extends: [
    'plugin:case-police/recommended',
    'eslint:recommended',
    'plugin:@typescript-eslint/recommended',
    'plugin:react-hooks/recommended',
    'plugin:react/recommended',
    'plugin:import/recommended',
    'plugin:import/typescript',
    'prettier',
  ],
  globals: {
    page: true,
    REACT_APP_ENV: true,
  },
  overrides: [],
  parser: '@typescript-eslint/parser',
  parserOptions: {
    ecmaVersion: 'latest',
    sourceType: 'module',
  },
  plugins: ['react', '@typescript-eslint', 'prettier', 'simple-import-sort', 'unused-imports', 'import'],
  settings: {
    'import/resolver': {
      typescript: {
        alwaysTryTypes: true,
        project: './tsconfig.json',
      },
    },
  },
  rules: {
    'react/react-in-jsx-scope': 'off',
    'react/self-closing-comp': ['error'],
    'react/display-name': 'off',
    'react/no-unknown-property': ['error', { ignore: ['css'] }],
    // 'unused-imports/no-unused-imports': 'warn',
    '@typescript-eslint/ban-ts-comment': 'off',
    'prefer-const': 'warn',
    '@typescript-eslint/no-inferrable-types': 'off',
    '@typescript-eslint/no-var-requires': 'off',
    '@typescript-eslint/no-explicit-any': 'off',
    // 检查 Hooks 的使用规则
    'react-hooks/rules-of-hooks': 'error',
    // 检查依赖项的声明
    'react-hooks/exhaustive-deps': 'warn',
    'react/prop-types': [2, { ignore: ['className'] }],
    '@typescript-eslint/no-unused-vars': [
      'error',
      {
        varsIgnorePattern: '^_',
      },
    ],
    // 循环依赖检测
    'import/no-cycle': ['error', { maxDepth: 5 }],
    // 禁止自引用
    'import/no-self-import': 'error',
    // 确保导入的模块存在
    'import/no-unresolved': 'error',
  },
};
