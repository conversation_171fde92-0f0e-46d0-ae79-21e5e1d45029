server {
  listen 80;
  server_name localhost;
  # 开启 gzip
  gzip on;
  # 开启静态 gzip，对于已经压缩好的 .gz 文件直接使用，不再压缩
  gzip_static on;
  # 启用压缩的最小文件大小
  gzip_min_length 10k;
  # 是否在 http header 中添加 Vary: Accept-Encoding，建议开启
  gzip_vary on;

  location / {
    root /app/dist; # 打包的路径
    index index.html index.htm;
    try_files $uri $uri/ /index.html; # 防止重刷新返回404
    add_header Cache-Control 'no-cache, must-revalidate, proxy-revalidate, max-age=0';

    proxy_buffer_size 1024k;
    proxy_buffers 16 1024k;
    proxy_busy_buffers_size 2048k;
    proxy_temp_file_write_size 2048k;
  }

  location ~* \.(js|css)$ {
    root /app/dist;
    expires max;
  }

  location /liveroom-api/ {
    proxy_pass http://new-media-liveroom.new-media.svc:5000;
  }

  error_page 500 502 503 504 /50x.html;
  location = /50x.html {
    root /usr/share/nginx/html;
  }
}
