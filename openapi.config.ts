import { generateService } from '@umijs/openapi';

generateService({
  requestLibPath: "import { request } from '@umijs/max'",
  // 可以用 Apifox 导出部分接口生成本地 openapi 链接然后 pnpm openapi 执行生成
  // schemaPath: 'http://127.0.0.1:4523/export/openapi/7?version=3.1',
  // schemaPath: 'http://*************:7004/new-media-api/v3/api-docs',
  schemaPath: 'http://127.0.0.1:4523/export/openapi/2?version=3.1',
  serversPath: 'src/services/anchor',
  apiPrefix: "'/new-media-api'",
  dataFields: ['data'],
});
