// For format details, see https://aka.ms/devcontainer.json. For config options, see the
// README at: https://github.com/devcontainers/templates/tree/main/src/docker-existing-docker-compose
{
  "name": "Existing Docker Compose (Extend)",

  // Update the 'dockerComposeFile' list if you have more compose files or use different names.
  // The .devcontainer/docker-compose.yml file contains any overrides you need/want to make.
  "build": {
    "dockerfile": "Dockerfile"
  },
  "postCreateCommand": "bash .devcontainer/post-create.sh",
  // The 'service' property is the name of the service for the container that VS Code should
  // use. Update this value and .devcontainer/docker-compose.yml to the real service name.

  "mounts": [
    "type=bind,source=/Users/<USER>/Library/pnpm/store,target=/home/<USER>/.pnpm-store"
  ],

  // The optional 'workspaceFolder' property is the path VS Code should open by default when
  // connected. This is typically a file mount in .devcontainer/docker-compose.yml
  "workspaceFolder": "/workspaces/${localWorkspaceFolderBasename}",

  // Features to add to the dev container. More info: https://containers.dev/features.
  // "features": {},
  "forwardPorts": [8000],
  // Use 'portsAttributes' to set default properties for specific forwarded ports.
  // More info: https://containers.dev/implementors/json_reference/#port-attributes
  "portsAttributes": {
    "8000": {
      "label": "Website",
      "onAutoForward": "notify"
    }
  },

  // Use 'forwardPorts' to make a list of ports inside the container available locally.
  // "forwardPorts": [],

  // Uncomment the next line if you want start specific services in your Docker Compose config.
  // "runServices": [],

  // Uncomment the next line if you want to keep your containers running after VS Code shuts down.
  // "shutdownAction": "none",

  // Configure tool-specific properties.
  "customizations": {
    // Configure properties specific to VS Code.
    "vscode": {
      "settings": {},
      "extensions": [
        "streetsidesoftware.code-spell-checker",
        "phoenisx.cssvar",
        "wix.vscode-import-cost",
        "yzhang.markdown-all-in-one",
        "esbenp.prettier-vscode",
        "yoavbls.pretty-ts-errors",
        "robbowen.synthwave-vscode",
        "bradlc.vscode-tailwindcss",
        "gruntfuggly.todo-tree",
        "travisthetechie.write-good-linter"
      ]
    }
  },

  // Uncomment to connect as an existing user other than the container default. More info: https://aka.ms/dev-containers-non-root.
  "remoteUser": "node"
}
