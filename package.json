{"name": "ant-design-pro", "version": "6.0.0", "private": true, "description": "An out-of-box UI solution for enterprise applications", "scripts": {"analyze": "cross-env ANALYZE=1 max build", "build": "max build", "build:doctor": "cross-env RSDOCTOR=true max build", "deploy": "npm run build && npm run gh-pages", "dev": "npm run start:dev", "gh-pages": "gh-pages -d dist", "i18n-remove": "pro i18n-remove --locale=zh-CN --write", "postinstall": "max setup", "jest": "jest", "lint": "npm run lint:js && npm run lint:prettier && npm run tsc", "lint-staged": "lint-staged", "lint-staged:js": "eslint --ext .js,.jsx,.ts,.tsx ", "lint:fix": "eslint --fix --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src ", "lint:js": "eslint --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src", "lint:prettier": "prettier -c --write \"**/**.{js,jsx,tsx,ts,less,md,json}\" --end-of-line auto", "local": "npm run start:local", "openapi": "tsx openapi.config.ts", "prepare": "husky install", "prettier": "prettier -c --write \"**/**.{js,jsx,tsx,ts,less,md,json}\"", "preview": "npm run build && max preview --port 8000", "record": "cross-env NODE_ENV=development REACT_APP_ENV=test max record --scene=login", "serve": "umi-serve", "prestart": "rm -rf ./src/.umi && rm -rf node_modules/.cache", "start": "cross-env UMI_ENV=dev max dev", "start:dev": "cross-env REACT_APP_ENV=dev MOCK=none UMI_ENV=dev max dev", "start:local": "cross-env REACT_APP_ENV=local MOCK=none UMI_ENV=dev max dev", "start:no-mock": "cross-env MOCK=none UMI_ENV=dev max dev", "start:pre": "cross-env REACT_APP_ENV=pre UMI_ENV=dev max dev", "start:test": "cross-env REACT_APP_ENV=test MOCK=none UMI_ENV=dev max dev", "test": "cross-env  TS_NODE_TRANSPILE_ONLY=yes jest --passWithNoTests", "test:coverage": "npm run jest -- --coverage", "test:update": "npm run jest -- -u", "test:watch": "npm run jest -- --watch", "tsc": "tsc --noEmit", "preinstall": "npx only-allow pnpm"}, "lint-staged": {"**/*.{js,jsx,ts,tsx}": "npm run lint-staged:js", "**/*.{js,jsx,tsx,ts,less,md,json}": ["prettier --write"]}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"], "dependencies": {"@ant-design/icons": "^5.3.6", "@ant-design/pro-components": "^2.8.0", "@ant-design/use-emotion-css": "1.0.4", "@ant-design/x": "^1.0.4", "@dnd-kit/core": "^6.1.0", "@dnd-kit/modifiers": "^7.0.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@emoji-mart/react": "^1.1.1", "@openreplay/tracker": "^12.0.4", "@openreplay/tracker-assist": "^9.0.1", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@reactuses/core": "^5.0.23", "aegis-web-sdk": "^1.39.1", "ahooks": "^3.8.1", "antd": "^5.25.1", "antd-mobile": "^5.34.0", "antd-mobile-icons": "^0.3.0", "axios": "^1.6.8", "class-variance-authority": "^0.7.0", "classnames": "^2.3.2", "clsx": "^2.1.1", "copy-to-clipboard": "^3.3.3", "cos-js-sdk-v5": "^1.4.21", "cropperjs": "^1.6.2", "darkreader": "^4.9.96", "dayjs": "^1.11.10", "echarts": "5.4.3", "echarts-for-react": "^3.0.2", "element-china-area-data": "^6.1.0", "file-saver": "^2.0.5", "framer-motion": "^11.0.12", "jotai": "^2.1.0", "js-cookie": "^3.0.5", "jszip": "^3.10.1", "lodash-es": "^4.17.21", "lucide-react": "^0.451.0", "mark.js": "^8.11.1", "nanoid": "^5.0.7", "omit.js": "^2.0.2", "p-queue": "^8.0.1", "qs": "^6.11.2", "rc-resize-observer": "^1.3.1", "react": "^18.0.0", "react-cropper": "^2.3.3", "react-dom": "^18.0.0", "react-error-boundary": "^4.0.12", "react-infinite-scroll-component": "^6.1.0", "read-excel-file": "^5.7.1", "styled-components": "^6.1.0", "tailwind-merge": "^2.5.3", "tailwindcss-animate": "^1.0.7", "use-immer": "^0.9.0", "wordcloud": "^1.2.2", "xgplayer": "^3.0.18", "xgplayer-flv": "^3.0.11", "xgplayer-hls": "^3.0.18", "xgplayer-mp4": "3.0.11", "xlsx": "^0.18.5"}, "devDependencies": {"@ant-design/pro-cli": "^2.1.0", "@rsdoctor/webpack-plugin": "^1.1.2", "@testing-library/react": "^13.4.0", "@types/classnames": "^2.3.1", "@types/echarts": "^4.9.22", "@types/emoji-mart": "^3.0.14", "@types/express": "^4.17.14", "@types/file-saver": "^2.0.6", "@types/history": "^4.7.11", "@types/jest": "^29", "@types/js-cookie": "^3.0.6", "@types/lodash-es": "^4.17.12", "@types/mark.js": "^8.11.12", "@types/qs": "^6.9.17", "@types/react": "18.2.79", "@types/react-dom": "^18.2.25", "@types/react-helmet": "^6.1.5", "@types/wordcloud": "^1.2.2", "@types/xlsx": "^0.0.36", "@umijs/lint": "^4.0.34", "@umijs/max": "^4.0.33", "@umijs/openapi": "^1.13.0", "autoprefixer": "^10.4.20", "code-inspector-plugin": "^0.16.1", "compression-webpack-plugin": "^11.1.0", "cross-env": "^7", "eslint": "^8.0.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-case-police": "^0.6.1", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-simple-import-sort": "^10.0.0", "eslint-plugin-unused-imports": "^3.0.0", "express": "^4.18.2", "gh-pages": "^3.2.0", "husky": "^7.0.4", "jest": "^29", "jest-environment-jsdom": "^29.2.2", "lint-staged": "^10.0.0", "mockjs": "^1.1.0", "postcss": "^8.4.47", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.8", "swagger-ui-dist": "^4.14.2", "tailwindcss": "^3", "ts-node": "^10", "tsx": "^4.19.2", "typescript": "^4", "umi-presets-pro": "^2.0.0"}, "engines": {"node": ">=12.0.0", "pnpm": "8.15.9"}, "packageManager": "pnpm@8.15.9"}