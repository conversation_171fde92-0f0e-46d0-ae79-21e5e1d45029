# node镜像仅仅是用来打包文件
FROM node:18.12.0-alpine as builder

# 设置工作目录
WORKDIR /app

# 复制 package.json 和 pnpm-lock.yaml，安装依赖
COPY package.json pnpm-lock.yaml /app/
RUN npm config set registry http://registry.npmmirror.com && \
    npm install -g pnpm@10.13.1 && \
    pnpm install

# 复制源代码并构建
COPY . /app
RUN pnpm build

# 选择更小体积的基础镜像
FROM nginx:alpine

# 复制 Nginx 配置和构建后的静态文件
COPY docker/nginx.conf /etc/nginx/conf.d/default.conf
COPY --from=builder /app/dist /app/dist

# 暴露端口
EXPOSE 80

# 启动nginx，关闭守护式运行，否则容器启动后会立刻关闭
CMD ["nginx", "-g", "daemon off;"]
