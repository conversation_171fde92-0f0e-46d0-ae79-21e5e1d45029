// https://umijs.org/config/
import { defineConfig } from '@umijs/max';
import { codeInspectorPlugin } from 'code-inspector-plugin';
import fs from 'fs';
import { RsdoctorWebpackPlugin } from '@rsdoctor/webpack-plugin';
import CompressionPlugin from 'compression-webpack-plugin';
import defaultSettings from './defaultSettings';
import proxy from './proxy';
import routes from './routes';

const { REACT_APP_ENV = 'dev' } = process.env;

function getLocalSvgList(path: string) {
  if (!fs.existsSync(path)) {
    return [];
  }
  const items = fs.readdirSync(path);
  const result: string[] = [];
  items.forEach((item) => {
    const itemPath = `${path}/${item}`;
    // console.log(item, itemPath);
    const stat = fs.statSync(itemPath);
    if (stat.isDirectory()) {
      let children = getLocalSvgList(itemPath);
      if (children && children.length) {
        children?.forEach((leaveItem) => {
          result.push(`${item}/${leaveItem}`);
        });
      }
    } else {
      // 文件
      const matchRes = item.match(/(.*)\.svg$/)?.[1];
      if (matchRes) {
        result.push(`${matchRes}`);
      }
    }
  });

  return result;
}

export default defineConfig({
  chainWebpack(memo: any) {
    // 只在 RSDOCTOR 环境变量为 true 时启用插件
    if (process.env.RSDOCTOR === 'true') {
      memo.plugin('rsdoctor').use(RsdoctorWebpackPlugin, [
        {
          // 开启性能分析
          features: {
            analyze: {
              enable: true,
            },
          },
          // 输出产物目录
          output: {
            path: './rsdoctor',
          },
        },
      ]);
    }

    // 添加 gzip 压缩配置
    memo.plugin('compression-webpack-plugin').use(CompressionPlugin, [
      {
        test: /\.(js|css|html|svg)$/,
        algorithm: 'gzip',
        threshold: 10240, // 只有大小大于该值的资源会被处理，单位是 bytes，默认是 10kb
        minRatio: 0.8, // 只有压缩率小于这个值的资源才会被处理，默认值是 0.8
        deleteOriginalAssets: false, // 是否删除原资源，默认为 false
      },
    ]);

    memo.plugin('code-inspector-plugin').use(
      codeInspectorPlugin({
        bundler: 'webpack',
      }),
    );

    memo.optimization.splitChunks({
      // chunks、minSize、minChunks 将对所有缓存组生效
      chunks: 'async', // 对所有的chunk进行拆分
      minSize: 20000, // 拆分 chunk 的最小体积 20000 bytes
      minChunks: 2, // 需在两个模块中共享才进行拆分
      cacheGroups: {
        // 设置缓存组
        vendor: {
          // vendor组 存放node_modules下的chunk
          // name: 'vendor', // chunk 的名称 vendor 特意不配置 name
          test: /[\\/]node_modules[\\/]/i, // 匹配node_modules下所有的chunk
          priority: 10, // 优先级10 优先将node_modules下的chunk拆分到vendor组
          reuseExistingChunk: true, // 重用模块，而不是重新生成
          enforce: true, // 强制拆分
        },
        default: {
          // 默认组 非node_modules下的文件块 将执行default缓存组规则
          reuseExistingChunk: true, // 重用模块，而不是重新生成
          priority: -10, // 优先级 -10
          enforce: true, // 强制拆分
        },
        antd: {
          name: 'antd',
          test: /[\\/]node_modules[\\/]antd[\\/]/,
          priority: 20, // 优先级20
          minChunks: 1, // 改为1，只要使用就分割
          chunks: 'all', // 改为all，对所有类型的引入都进行分割
          reuseExistingChunk: true,
        },
        echarts: {
          name: 'echarts',
          test: /[\\/]node_modules[\\/]echarts[\\/]/,
          priority: 20,
          minChunks: 2,
          reuseExistingChunk: true,
        },
        antDesignIcons: {
          name: 'ant-design-icons',
          test: /[\\/]node_modules[\\/]@ant-design[\\/]icons[\\/]/,
          priority: 20, // 设置优先级
          minChunks: 2, // 至少被2个模块引用
          reuseExistingChunk: true,
        },
      },
    });
  },
  metas: [
    {
      'http-equiv': 'Content-Security-Policy',
      content: 'upgrade-insecure-requests',
    },
    {
      property: 'og:image',
      content:
        'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/%E5%9B%BE%E7%89%87%E7%B4%A0%E6%9D%90/logo.png',
    },
    {
      property: 'og:title',
      content: '乾坤圈-新媒体矩阵监控管家',
    },
    {
      property: 'og:description',
      content: '你的新媒体矩阵监控管家品牌全矩阵social账号的体系化经营洞察',
    },
  ],
  scripts: [
    {
      src: 'https://umami.xiaofeilun.cn/script.js',
      'data-website-id': 'c5cc9601-ed32-40b0-a209-0fb2cdca23a1',
      'data-domains': 'new-media.xiaofeilun.cn',
      defer: true,
      async: false,
    },
    `
    window.difyChatbotConfig = {
      token: '6zGvbLbQVCpGiaK1',
      baseUrl: 'https://dify.xiaofeilun.cn',
    };
    `,
    { src: 'https://dify.xiaofeilun.cn/embed.min.js', id: '6zGvbLbQVCpGiaK1', defer: true },
  ],

  esbuildMinifyIIFE: true,
  /**
   * @name 分包加载
   * @doc https://umijs.org/blog/code-splitting
   */
  codeSplitting: {
    jsStrategy: 'granularChunks',
  },
  /**
   * @name 开启 hash 模式
   * @description 让 build 之后的产物包含 hash 后缀。通常用于增量发布和避免浏览器加载缓存。
   * @doc https://umijs.org/docs/api/config#hash
   */
  hash: true,
  /**
   * @name 兼容性设置
   * @description 设置 ie11 不一定完美兼容，需要检查自己使用的所有依赖
   * @doc https://umijs.org/docs/api/config#targets
   */
  // targets: {
  //   ie: 11,
  // },
  /**
   * @name 路由的配置，不在路由中引入的文件不会编译
   * @description 只支持 path，component，routes，redirect，wrappers，title 的配置
   * @doc https://umijs.org/docs/guides/routes
   */
  // umi routes: https://umijs.org/docs/routing
  routes,
  /**
   * @name 主题的配置
   * @description 虽然叫主题，但是其实只是 less 的变量设置
   * @doc antd的主题设置 https://ant.design/docs/react/customize-theme-cn
   * @doc umi 的theme 配置 https://umijs.org/docs/api/config#theme
   */
  theme: {
    // 如果不想要 configProvide 动态设置主题需要把这个设置为 default
    // 只有设置为 variable， 才能使用 configProvide 动态设置主色调
    'root-entry-name': 'variable',
  },
  /**
   * @name moment 的国际化配置
   * @description 如果对国际化没有要求，打开之后能减少js的包大小
   * @doc https://umijs.org/docs/api/config#ignoremomentlocale
   */
  ignoreMomentLocale: true,
  /**
   * @name 代理配置
   * @description 可以让你的本地服务器代理到你的服务器上，这样你就可以访问服务器的数据了
   * @see 要注意以下 代理只能在本地开发时使用，build 之后就无法使用了。
   * @doc 代理介绍 https://umijs.org/docs/guides/proxy
   * @doc 代理配置 https://umijs.org/docs/api/config#proxy
   */
  proxy: proxy[REACT_APP_ENV as keyof typeof proxy],
  /**
   * @name 快速热更新配置
   * @description 一个不错的热更新组件，更新时可以保留 state
   */
  fastRefresh: true,
  //============== 以下都是max的插件配置 ===============
  /**
   * @name 数据流插件
   * @@doc https://umijs.org/docs/max/data-flow
   */
  model: {},
  /**
   * 一个全局的初始数据流，可以用它在插件之间共享数据
   * @description 可以用来存放一些全局的数据，比如用户信息，或者一些全局的状态，全局初始状态在整个 Umi 项目的最开始创建。
   * @doc https://umijs.org/docs/max/data-flow#%E5%85%A8%E5%B1%80%E5%88%9D%E5%A7%8B%E7%8A%B6%E6%80%81
   */
  initialState: {},
  /**
   * @name layout 插件
   * @doc https://umijs.org/docs/max/layout-menu
   */
  layout: {
    locale: true,
    ...defaultSettings,
  },
  /**
   * @name 国际化插件
   * @doc https://umijs.org/docs/max/i18n
   */
  locale: {
    // default zh-CN
    default: 'zh-CN',
    antd: true,
    // default true, when it is true, will use `navigator.language` overwrite default
    baseNavigator: true,
  },
  /**
   * @name antd 插件
   * @description 内置了 babel import 插件
   * @doc https://umijs.org/docs/max/antd#antd
   */
  antd: {},
  /**
   * @name 网络请求配置
   * @description 它基于 axios 和 ahooks 的 useRequest 提供了一套统一的网络请求和错误处理方案。
   * @doc https://umijs.org/docs/max/request
   */
  request: {},
  /**
   * @name 权限插件
   * @description 基于 initialState 的权限插件，必须先打开 initialState
   * @doc https://umijs.org/docs/max/access
   */
  access: {},
  //================ pro 插件配置 =================
  presets: ['umi-presets-pro'],
  /**
   * @name mfsu
   * @doc https://umijs.org/docs/api/config#mfsu
   */
  mfsu: {
    strategy: 'normal',
  },
  requestRecord: {},
  headScripts: [`if(!this.globalThis){this.globalThis = this;}`],
  crossorigin: true,
  icons: {
    include: getLocalSvgList('./src/icons').map((item) => `local:${item}`),
  },
  srcTranspiler: 'esbuild',
  reactQuery: {},
  tailwindcss: {},
  deadCode: {
    exclude: ['src/assets/**', 'src/icons/**'],
  },
  jsMinifier: 'terser',
  cssMinifier: 'cssnano',
});
