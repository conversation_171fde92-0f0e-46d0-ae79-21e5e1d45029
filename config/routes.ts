﻿/**
 * @name umi 的路由配置
 * @description 只支持 path,component,routes,redirect,wrappers,name,icon 的配置
 * @param path  path 只支持两种占位符配置，第一种是动态参数 :id 的形式，第二种是 * 通配符，通配符只能出现路由字符串的最后。
 * @param component 配置 location 和 path 匹配后用于渲染的 React 组件路径。可以是绝对路径，也可以是相对路径，如果是相对路径，会从 src/pages 开始找起。
 * @param routes 配置子路由，通常在需要为多个路径增加 layout 组件时使用。
 * @param redirect 配置路由跳转
 * @param wrappers 配置路由组件的包装组件，通过包装组件可以为当前的路由组件组合进更多的功能。 比如，可以用于路由级别的权限校验
 * @param name 配置路由的标题，默认读取国际化文件 menu.ts 中 menu.xxxx 的值，如配置 name 为 login，则读取 menu.ts 中 menu.login 的取值作为标题
 * @param icon 配置路由的图标，取值参考 https://ant.design/components/icon-cn， 注意去除风格后缀和大小写，如想要配置图标为 <StepBackwardOutlined /> 则取值应为 stepBackward 或 StepBackward，如想要配置图标为 <UserOutlined /> 则取值应为 user 或者 User
 * @doc https://umijs.org/docs/guides/routes
 */

export default [
  {
    path: '/register',
    component: 'Register',
    layout: false,
  },
  {
    path: '/login',
    component: 'Login',
    layout: false,
  },
  {
    path: '/douyin-auth',
    component: 'DouyinAuth',
    layout: false,
  },
  {
    path: 'post/detail/:postId/:platform/:industryType',
    component: 'PostDetail',
    layout: false,
  },
  {
    path: 'live/detail/:roomId/:platform/:industryType',
    component: 'LiveDetail',
    layout: false,
  },
  {
    path: 'account/detail/:platformKey/:accountId',
    component: 'AccountDetail',
    layout: false,
  },
  {
    path: '/daily',
    component: 'Daily',
    layout: false,
  },
  {
    // 新的通用日报V2版
    path: '/daily/:projectId',
    component: 'DailyDefault',
    layout: false,
  },
  {
    path: '/daily/detail',
    component: 'DailyDetail',
    layout: false,
  },
  {
    path: '/daily/detail/:projectId',
    component: 'DailyDetailDefault',
    layout: false,
  },
  {
    path: '/oceanengine/authorization-page',
    component: 'AuthorizationMiddlePage',
    layout: false,
  },
  {
    path: '/monitor',
    name: '监控中心',
    access: 'canReadMonitor',
    routes: [
      {
        path: '/monitor',
        redirect: '/monitor/account',
      },
      {
        path: '/monitor/account',
        name: '平台管理',
        routes: [
          {
            path: '/monitor/account',
            redirect: '/monitor/account/user-list',
          },
          {
            path: '/monitor/account/user-list',
            name: '平台账号列表',
            component: 'AccountList',
          },
          {
            path: '/monitor/account/user-list/detail/:platformKey/:accountId',
            component: 'AccountDetail',
          },
          {
            path: '/monitor/account/post-list',
            name: '平台作品列表',
            component: 'PostList',
          },
          {
            path: '/monitor/account/post-list/detail/:platformKey/:accountId',
            component: 'AccountDetail',
          },
          {
            path: '/monitor/account/live-list',
            name: '平台直播列表',
            component: 'LiveList',
          },
          {
            path: '/monitor/account/live-list/detail/:platformKey/:accountId',
            component: 'AccountDetail',
          },
        ],
      },
      {
        path: '/monitor/stat',
        name: '分析看板',
        routes: [
          {
            path: '/monitor/stat',
            redirect: '/spider/stat/account',
          },
          {
            path: '/monitor/stat/account',
            name: '账号数据',
            component: 'AccountStat',
          },
          {
            path: '/monitor/stat/post',
            name: '作品数据',
            component: 'PostStat',
          },
        ],
      },
      {
        path: '/monitor/config',
        name: '配置管理',
        routes: [
          {
            path: '/monitor/config/tags',
            name: '平台账号标签',
            component: 'TagList',
          },
        ],
      },
      {
        path: '/monitor/spider',
        name: '监控管理',
        routes: [
          {
            path: '/monitor/spider',
            redirect: '/monitor/douyin',
          },
          {
            path: '/monitor/spider/list/:platformKey/create',
            component: 'SpiderList/create',
          },
          {
            path: '/monitor/spider/list/:platformKey/weixin-create', // 视频号较为特殊单独写了
            component: 'SpiderList/weixinCreate',
          },
          {
            path: '/monitor/spider/list/douyin',
            name: '抖音监控用户',
            component: 'SpiderList/douyin',
          },
          {
            path: '/monitor/spider/list/kuaishou',
            name: '快手监控用户',
            component: 'SpiderList/kuaishou',
          },
          {
            path: '/monitor/spider/list/dongchedi',
            name: '懂车帝监控用户',
            component: 'SpiderList/dongchedi',
          },
          {
            path: '/monitor/spider/list/weixin',
            name: '视频号监控用户',
            component: 'SpiderList/weixin',
          },
          {
            path: '/monitor/spider/list/xiaohongshu',
            name: '小红书监控用户',
            component: 'SpiderList/xiaohongshu',
          },
          {
            path: '/monitor/spider/list/bilibili',
            name: 'B站监控用户',
            component: 'SpiderList/bilibili',
          },
          {
            path: '/monitor/spider/list/weibo',
            name: '微博监控用户',
            component: 'SpiderList/weibo',
          },
          {
            path: '/monitor/spider/list/yiche',
            name: '易车监控用户',
            component: 'SpiderList/yiche',
          },
          {
            path: '/monitor/spider/list/qichezhijia',
            name: '汽车之家监控用户',
            component: 'SpiderList/qichezhijia',
          },
          {
            path: '/monitor/spider/list/wxpublic',
            name: '微信公众号监控用户',
            component: 'SpiderList/wxpublic',
          },
        ],
      },
    ],
  },
  {
    path: '/project-list',
    name: '矩阵项目',
    component: 'ProjectList',
  },
  {
    path: '/setting-project-list',
    name: '项目管理',
    component: 'PortalProjectList',
    access: 'canReadManage',
    routes: [
      {
        path: '/setting-project-list/:projectId/:tagId/edit',
        component: 'PortalProjectEdit',
      },
    ],
  },
  {
    path: '/setting',
    name: '系统管理',
    access: 'canReadManage',
    routes: [
      {
        path: '/setting',
        redirect: '/setting/user-list',
      },
      {
        path: '/setting/user-list',
        name: '成员管理',
        component: 'PortalUserList',
      },
      {
        path: '/setting/role-list',
        name: '角色管理',
        component: 'PortalRoleList',
      },
      {
        path: '/setting/project-list/:projectId/:tagId/edit',
        component: 'PortalProjectEdit',
      },
    ],
  },
  {
    path: 'https://new-media.xiaofeilun.cn/clip-portal',
    name: '素材中心',
    access: 'canWriteMaterial',
  },
  {
    path: '/work-analysis',
    name: '作品分析',
    component: 'WorkAnalysis',
    access: 'canWriteMaterial',
  },
  {
    path: '/clue-mapping',
    name: '线索映射',
    component: 'ClueMapping',
    access: 'canWriteLeads',
  },
  {
    path: '/quality-review',
    name: '质检审核',
    component: 'QualityReview',
    access: 'canWriteAudit',
  },
  {
    path: '/customer-system',
    name: '人工客服',
    component: 'CustomerSystem',
    access: 'canReadManualCustomerService',
  },
  // /project/:projectKey/下面组件都套在ProjectHome组件上 具体渲染哪个组件定义在ProjectHome里面  icon映射在utils/const
  {
    path: '/project/:projectKey/:industryType',
    layout: false,
    component: 'ProjectHome',
    routes: [
      {
        path: '/project/:projectKey/:industryType/home',
        redirect: '/project/:projectKey/:industryType/overview/data-dashboard',
      },
      {
        path: '/project/:projectKey/:industryType',
        redirect: '/project/:projectKey/:industryType/overview/data-dashboard',
      },
      {
        name: '总览',
        path: '/project/:projectKey/:industryType/overview',
        icon: 'home',
        routes: [
          // {
          //   path: '/project/:projectKey/:industryType/overview/panel',
          //   name: '总面板',
          //   component: 'ProjectHome',
          // },
          {
            path: '/project/:projectKey/:industryType/overview/data-dashboard',
            name: '数据大盘',
            component: 'ProjectHome/DataDashboard',
          },
          {
            path: '/project/:projectKey/:industryType/overview/live-map',
            name: '实时直播地图',
            component: 'ProjectHome/LiveMap',
          },
        ],
      },
      {
        name: '行业管理',
        path: '/project/:projectKey/:industryType/management',
        icon: 'overview',
        routes: [
          {
            path: '/project/:projectKey/:industryType/management/hot-selling-overview',
            name: '行业爆款拆解',
            component: 'WorkAnalysis',
          },
          {
            path: '/project/:projectKey/:industryType/management/overview',
            name: '行业总榜',
            component: 'ProjectHome/Overview/overview',
          },
          {
            path: '/project/:projectKey/:industryType/management/post-overview',
            name: '行业作品榜',
            component: 'ProjectHome/Overview/postOverview',
          },
          {
            path: '/project/:projectKey/:industryType/management/live-overview',
            name: '行业直播榜',
            component: 'ProjectHome/Overview/liveOverview',
          },
        ],
      },
      {
        name: '团队管理',
        path: '/project/:projectKey/:industryType/team',
        icon: 'team',
        routes: [
          {
            path: '/project/:projectKey/:industryType/team',
            redirect: '/project/:projectKey/:industryType/team/index',
          },
          {
            path: '/project/:projectKey/:industryType/team/index',
            name: '团队管理',
            component: 'ProjectHome/TeamManage',
          },
          {
            name: '团队总览',
            path: '/project/:projectKey/:industryType/team/overview',
            component: 'ProjectHome/TeamOverview',
            key: 'teamOverview',
          },
        ],
      },
      {
        name: '平台管理',
        path: '/project/:projectKey/:industryType/platform',
        icon: 'setting',
        routes: [
          {
            path: '/project/:projectKey/:industryType/platform/team-setting',
            name: '团队列表',
            component: 'ProjectHome/TeamSetting',
          },
          {
            path: '/project/:projectKey/:industryType/platform/account-setting',
            name: '账号列表',
            component: 'ProjectHome/AccountSetting',
          },
          {
            path: '/project/:projectKey/:industryType/platform/account-detail',
            component: 'AccountDetail',
          },
          {
            path: '/project/:projectKey/:industryType/platform/product-setting',
            name: '作品列表',
            component: 'ProjectHome/ProductSetting',
          },
          {
            path: '/project/:projectKey/:industryType/platform/live-setting',
            name: '直播列表',
            component: 'ProjectHome/LiveSetting',
          },
          {
            path: '/project/:projectKey/:industryType/platform/account-tags',
            name: '账号标签',
            component: 'ProjectHome/AccountTagsList',
            access: 'isAdmin',
          },
        ],
      },
      {
        name: '智能分析',
        path: '/project/:projectKey/:industryType/intelligent-analysis',
        icon: 'intelligence',
        routes: [
          {
            path: '/project/:projectKey/:industryType/intelligent-analysis/live-analysis',
            name: '直播分析',
            component: 'ProjectHome/LiveAnalysis',
            key: 'liveAnalysis',
          },
          {
            path: '/project/:projectKey/:industryType/intelligent-analysis/barrage',
            name: '弹幕分析',
            component: 'ProjectHome/BarrageAnalysis',
            key: 'barrageAnalysis',
          },
          {
            path: '/project/:projectKey/:industryType/intelligent-analysis/project-work',
            name: '作品分析',
            component: 'ProjectHome/ProjectWorkAnalysis',
            key: 'projectWorksAnalysis',
          },
        ],
      },
      {
        name: '质检管理',
        path: '/project/:projectKey/:industryType/inspection',
        icon: 'monitor',
        routes: [
          {
            path: '/project/:projectKey/:industryType/inspection/post-inspection',
            name: '作品质检',
            component: 'ProjectHome/PostQuality',
          },
          {
            path: '/project/:projectKey/:industryType/inspection/live-inspection',
            name: '直播质检',
            component: 'ProjectHome/LiveQuality',
          },
          {
            // 报价质检只有一汽项目有 src/hooks/useProjectRoutes.ts#loopRoutes
            path: '/project/:projectKey/:industryType/inspection/price-inspection',
            name: '报价质检',
            component: 'ProjectHome/PriceQuality',
          },
          {
            path: '/project/:projectKey/:industryType/inspection/remind-dashboard',
            name: '实时提醒',
            component: 'ProjectHome/Remind/dashboard',
          },
          {
            path: '/project/:projectKey/:industryType/inspection/remind-daily',
            name: '每日日报',
            component: 'ProjectHome/Remind/daily',
          },
        ],
      },
      {
        name: '线索管理',
        path: '/project/:projectKey/:industryType/clue',
        icon: 'clue',
        access: 'isAdmin',
        key: 'clue',
        routes: [
          {
            path: '/project/:projectKey/:industryType/clue/clue-docking',
            name: '线索对接',
            component: 'ProjectHome/ClueDocking',
          },
          {
            path: '/project/:projectKey/:industryType/clue/potential-user',
            name: '高潜用户',
            component: 'ProjectHome/PotentialUser',
          },
          {
            path: '/project/:projectKey/:industryType/clue/tags',
            name: '用户标签',
            component: 'ProjectHome/UserTagsList',
          },
          {
            path: '/project/:projectKey/:industryType/clue/:projectId/:accountUserId/:platformUniqueId/:name/session-list',
            name: '对话流',
            component: 'ProjectHome/PotentialUser/sessionsModal',
            hideInMenu: true,
          },
        ],
      },
      {
        name: '商务政策管理',
        path: '/project/:projectKey/:industryType/business',
        icon: 'business',
        routes: [
          {
            path: '/project/:projectKey/:industryType/business/incentive',
            name: '激励活动',
            component: 'ProjectHome/Business/incentive',
            key: 'incentive',
          },
          {
            path: '/project/:projectKey/:industryType/business/create-incentive',
            component: 'ProjectHome/Business/incentiveCreate',
            name: '创建激励活动',
            hideInMenu: true,
            parentKeys: ['incentive'],
          },
          {
            path: '/project/:projectKey/:industryType/business/:incentiveId/edit-incentive',
            component: 'ProjectHome/Business/incentiveEdit',
            name: '编辑激励活动',
            hideInMenu: true,
            parentKeys: ['incentive'],
          },
          {
            path: '/project/:projectKey/:industryType/business/:incentiveId/rank-incentive',
            component: 'ProjectHome/Business/incentiveRank',
            name: '激励活动排名',
            hideInMenu: true,
            parentKeys: ['incentive'],
          },
        ],
      },
      {
        name: '矩阵分发',
        path: '/project/:projectKey/:industryType/distribute',
        icon: 'distribute',
        key: 'distribute',
        routes: [
          {
            path: '/project/:projectKey/:industryType/distribute/log',
            name: '发布日志',
            component: 'ProjectHome/Distribute/distributeTable/index',
          },
          {
            path: '/project/:projectKey/:industryType/distribute',
            name: '分发',
            hideInMenu: true,
            component: 'ProjectHome/Distribute/index',
          },
        ],
      },
      {
        name: '私信管理',
        path: '/project/:projectKey/:industryType/message',
        icon: 'message',
        key: 'message',
        routes: [
          {
            path: '/project/:projectKey/:industryType/message/knowledge-base',
            name: '知识库',
            component: 'ProjectHome/Message/knowledgeBase',
          },
          {
            path: '/project/:projectKey/:industryType/message/customer-service',
            name: '客服中心',
            component: 'ProjectHome/Message/customerService',
          },
          {
            path: '/project/:projectKey/:industryType/message/customer-service-data',
            name: '客服数据',
            component: 'ProjectHome/Message/customerServiceData',
          },
        ],
      },
      {
        name: '授权管理',
        path: '/project/:projectKey/:industryType/authorization',
        icon: 'authorization',
        key: 'authorization',
        routes: [
          {
            path: '/project/:projectKey/:industryType/authorization/account-authorization',
            name: '账号授权',
            component: 'ProjectHome/Authorization/authorizationPage',
          },
          {
            path: '/project/:projectKey/:industryType/authorization/authorization-history',
            name: '授权历史',
            component: 'ProjectHome/Authorization/authorizationHistory',
          },
        ],
      },
      {
        name: '主播管理',
        path: '/project/:projectKey/:industryType/anchor-management',
        icon: 'anchor',
        key: 'anchor',
        routes: [
          {
            path: '/project/:projectKey/:industryType/anchor-management/list',
            name: '主播列表',
            component: 'ProjectHome/AnchorManager/anchorList',
          },
          {
            path: '/project/:projectKey/:industryType/anchor-management/schedule',
            name: '主播排班',
            component: 'ProjectHome/AnchorManager/anchorSchedule',
          },
          {
            path: '/project/:projectKey/:industryType/anchor-management/dashboard',
            name: '主播数据概览',
            component: 'ProjectHome/AnchorManager/anchorDashboard',
          },
        ],
      },
    ],
  },
  {
    path: '/',
    redirect: '/project-list',
  },
  {
    path: '*',
    layout: false,
    component: './404',
  },
];
