/**
 * @name 代理的配置
 * @see 在生产环境 代理是无法生效的，所以这里没有生产环境的配置
 * -------------------------------
 * The agent cannot take effect in the production environment
 * so there is no configuration of the production environment
 * For details, please see
 * https://pro.ant.design/docs/deploy
 *
 * @doc https://umijs.org/docs/guides/proxy
 */
export default {
  local: {
    '/new-media-api/': {
      target: 'http://localhost:7004/',
      changeOrigin: true,
      pathRewrite: { '^': '' },
    },
  },
  dev: {
    '/mock/': {
      target: 'http://127.0.0.1:4523/m1/3418656-0-default/',
      changeOrigin: true,
      pathRewrite: { '^/mock/': '' },
    },
    '/new-media-api/': {
      // 数据库已区分测试和生产两个环境 需要看到线上数据的话切换target到线上但不要有增删改操作
      // target: 'http://************:7004/',
      // target: 'https://new-media.xiaofeilun.cn/', // 线上地址
      target: 'https://new-media-dev.xiaofeilun.cn/',
      // target: 'http://************:7004/',
      changeOrigin: true,
      pathRewrite: { '^': '' },
      // SSE 本地开发环境需要处理跨域
      onProxyRes: (proxyRes: any, req: any, res: any) => {
        if (req.headers.accept === 'text/event-stream') {
          res.writeHead(res.statusCode, {
            'Content-Type': 'text/event-stream',
            'Cache-Control': 'no-transform',
            Connection: 'keep-alive',
            'X-Accel-Buffering': 'no',
            'Access-Control-Allow-Origin': '*',
          });
        }
      },
    },
    // AI 助播
    '/liveroom-api/': {
      target: 'https://ailive.xiaofeilun.cn',
      changeOrigin: true,
      pathRewrite: { '^': '' },
    },
  },
};
