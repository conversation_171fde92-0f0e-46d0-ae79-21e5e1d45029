import { Settings as LayoutSettings } from '@ant-design/pro-components';

/**
 * @name
 */
const Settings: LayoutSettings & {
  pwa?: boolean;
  logo?: string;
} = {
  navTheme: 'light',
  colorPrimary: '#1890ff', // 拂晓蓝
  layout: 'mix',
  fixedHeader: true,
  fixSiderbar: true,
  splitMenus: true,
  colorWeak: false,
  title: '乾坤圈-新媒体矩阵监控管家',
  pwa: false,
  logo: 'https://media-1307444343.cos.ap-guangzhou.myqcloud.com/%E5%9B%BE%E7%89%87%E7%B4%A0%E6%9D%90/logo.png',
  iconfontUrl: '',
  siderMenuType: 'group',
  menu: {
    defaultOpenAll: true,
    locale: false,
  },
  // bread
};

export default Settings;
