# new-media-manager

本项目基于 [Ant Design Pro](https://pro.ant.design)

# 开发环境

- develop 为测试环境分支 地址: https://new-media-dev.xiaofeilun.cn/project-list
- main 为正式环境分支 地址: https://new-media.xiaofeilun.cn/project-list

# 开发流程

由于存在多需求并行开发的情况, 所以开发流程如下:

- 从 main 分支切出开发分支,开发完成提测合入 develop,触发 develop 分支的 CI 更新测试环境
- 测试过程中的 bugfix 在自己的需求分支修复后 MR 或 cherry pick commit 到 develop 再更新测试环境
- 最后测试完成将需求分支合入 main,触发 main 分支的 CI 更新正式环境
- 当有 package.json 与 pnpm-lock.yaml 更新的时候 触发 CI 环境变量添加一下 DEPENDENCE_CHANGE=true 来更新一下 docker的 nodemodule 镜像

main 分支不允许任何人 push 代码,只允许 MR 形式合入

## 开始

Install `node_modules`:

```bash
pnpm install
```

```bash
pnpm dev
```

### Build

```bash
pnpm run build
```

### 其他

- 如果遇到启动不起来项目 可能是 umi 的缓存影响的 删除 node_modules 与 src/.umi 重新运行即可
- 项目中需要使用 svg icon 在 antd icon 与 https://lucide.dev/icons/ 未找到的 复制设计稿件中的 svg 添加到 src/icons/outline 中, 然后在项目中使用 SvgIcon 组件使用

```bash
eg:  <SvgIcon icon="local:outline/upload" />
```

- ProTable 的样式覆写添加类名与覆写过得公共样式

```bash
tableClassName="custom-table"

pagination={{
    itemRender: customPaginationRender,
}}
```

- 通用的导出按钮组件在 src/components/exportButton.tsx
- 熟悉项目可以先逛一下其他模块找找有没有已经写好的组件 按 Options+shift 快捷键跳转到对应的代码内 方便复用避免熵增
- 覆盖 antd 的公共样式在 src/utils/commonStyle.tsx 文件中找找
- 通用 util 在文件 src/utils 里找找
- 后台开关影响模块显示隐藏控制收归于 src/hooks/fg 相关阅读: https://mp.weixin.qq.com/s/evDXmXtMhsUX_lnXRLHOSQ
- fg 控制某些模块隐藏显示的话在src/hooks/fg/useProjectFeature.ts:7添加要隐藏的模块,然后 router 中写好对应模块的 key 来控制根据接口显示隐藏, 还需要使用 withAuth 这个 HOC 来禁止通过路由访问 src/hoc/withAuth.tsx
- 在新媒体设计优化建议的会议的结论后引入项目使用 tailwind 与 shadcn/ui ,已经配置好 shadcn/ui 的 components.json 可以使用 CLI 安装组件, 全局主题色设置为了蓝色, 如果需要使用 antd 的样式优先级优先的话请使用 StyleProvider 包裹起来参考链接 https://github.com/ant-design/ant-design/issues/38794
- src/pages/ProjectHome/TeamManage/components/dealer.tsx 这个文件用 Iframe 免登录打开经销商端。后端 set-cookie 设置不到 localhost 所以更到 dev 来验证
- Antd ProTable 的通用配置在 src/utils/proTableConfig.tsx:3
- 本项目在根目录添加了.cursorrules 文件 如果不需要的话请设置关闭
- 项目暗色模式使用https://www.npmjs.com/package/darkreader 的颜色反转不用特意写, 只需要静态资源图片类的尽量使用透明底的图, 备注: 使用 styled-components 写的样式会出现反转颜色覆盖不到的情况，所以尽量使用 tailwind 来写, 或者把 styled-components 颜色相关的抽离到行内样式来写 (主要是搜索不到 darkreader 如何解决 cssinjs run-time 情况如何解决的办法 所以只能做下这个约定)
- 本项目使用 echarts 的配色请使用 src/utils/commonStyle.tsx:145 中的echartDefaultColor

### 一些解释

系统中的每日日报有三个形态: 有侧边栏的在系统内,推送出去的每日日报(无侧边栏),移动端形态(包括横竖屏) 需求的迭代的过程如下

1. 第一版: 是甲方先要免登录的日报点进去就可以看,所以日报链接是这个形式 /daily?payload=xxxxxx path: '/daily', payload 是一串加密的字符串信息然后端将项目 id 还有一些他需要的信息存进在 payload 然后请求 src/services/unauth.ts 文件下这些免登录访问的接口参数为这个地址栏上的 payload。
2. 第二版: 后面甲方又要改成需要登录的形式了,但是不想对接口什么的做太大的修改就还是保留原来的 payload 的形式不变,但是添加了登录退出,有了 cookie 了,后端就来读 cookie 判断看的是什么角色返回对应权限的数据。
3. 第三版: 第三版就是接入新的甲方了,新甲方提了一些需求产品修改了原日报的形态内容,但小鹏的又不能改,所以新增了 path: '/daily/:projectId',这个 V2 的版本，组件什么的都直接复制出来之前的定制化修改了,这一版是需要登录的 所以接口都换成 projectId 为参数的了,因为怕改到原逻辑, 所以组件就也不做什么封装了。然后旧的链接与新的链接都存在不能下掉因为可能某天甲方要翻回去 N 个月前发出去的日报还需要看得到。注: 所以有日报相关的修改记得 check 下对应路由下存在的两套类似的代码需要都改,也有可能两套变化的越来越不一样了,那时候就该庆幸还好没缝在一起,所以定制化需求越多就越不要封装 UI 层封装逻辑层就好
